{"fileNames": ["../../../../node_modules/typescript/lib/lib.es5.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.d.ts", "../../../../node_modules/typescript/lib/lib.es2016.d.ts", "../../../../node_modules/typescript/lib/lib.es2017.d.ts", "../../../../node_modules/typescript/lib/lib.es2018.d.ts", "../../../../node_modules/typescript/lib/lib.dom.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.core.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../../../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../../../node_modules/typescript/lib/lib.es2016.intl.d.ts", "../../../../node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "../../../../node_modules/typescript/lib/lib.es2017.date.d.ts", "../../../../node_modules/typescript/lib/lib.es2017.object.d.ts", "../../../../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../../../node_modules/typescript/lib/lib.es2017.string.d.ts", "../../../../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../../../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../../../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../../../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../../../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../../../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../../../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../../../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../../../node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../../../node_modules/typescript/lib/lib.es2021.string.d.ts", "../../../../node_modules/typescript/lib/lib.esnext.intl.d.ts", "../../../../node_modules/typescript/lib/lib.decorators.d.ts", "../../../../node_modules/typescript/lib/lib.decorators.legacy.d.ts", "../../../../node_modules/tslib/tslib.d.ts", "../../../../node_modules/@angular/core/graph.d.d.ts", "../../../../node_modules/@angular/core/event_dispatcher.d.d.ts", "../../../../node_modules/@angular/core/chrome_dev_tools_performance.d.d.ts", "../../../../node_modules/rxjs/internal/subscription.d.ts", "../../../../node_modules/rxjs/internal/types.d.ts", "../../../../node_modules/rxjs/internal/subscriber.d.ts", "../../../../node_modules/rxjs/internal/operator.d.ts", "../../../../node_modules/rxjs/internal/observable/iif.d.ts", "../../../../node_modules/rxjs/internal/observable/throwerror.d.ts", "../../../../node_modules/rxjs/internal/observable.d.ts", "../../../../node_modules/rxjs/internal/subject.d.ts", "../../../../node_modules/rxjs/internal/observable/connectableobservable.d.ts", "../../../../node_modules/rxjs/internal/operators/groupby.d.ts", "../../../../node_modules/rxjs/internal/symbol/observable.d.ts", "../../../../node_modules/rxjs/internal/behaviorsubject.d.ts", "../../../../node_modules/rxjs/internal/replaysubject.d.ts", "../../../../node_modules/rxjs/internal/asyncsubject.d.ts", "../../../../node_modules/rxjs/internal/scheduler.d.ts", "../../../../node_modules/rxjs/internal/scheduler/action.d.ts", "../../../../node_modules/rxjs/internal/scheduler/asyncscheduler.d.ts", "../../../../node_modules/rxjs/internal/scheduler/asyncaction.d.ts", "../../../../node_modules/rxjs/internal/scheduler/asapscheduler.d.ts", "../../../../node_modules/rxjs/internal/scheduler/asap.d.ts", "../../../../node_modules/rxjs/internal/scheduler/async.d.ts", "../../../../node_modules/rxjs/internal/scheduler/queuescheduler.d.ts", "../../../../node_modules/rxjs/internal/scheduler/queue.d.ts", "../../../../node_modules/rxjs/internal/scheduler/animationframescheduler.d.ts", "../../../../node_modules/rxjs/internal/scheduler/animationframe.d.ts", "../../../../node_modules/rxjs/internal/scheduler/virtualtimescheduler.d.ts", "../../../../node_modules/rxjs/internal/notification.d.ts", "../../../../node_modules/rxjs/internal/util/pipe.d.ts", "../../../../node_modules/rxjs/internal/util/noop.d.ts", "../../../../node_modules/rxjs/internal/util/identity.d.ts", "../../../../node_modules/rxjs/internal/util/isobservable.d.ts", "../../../../node_modules/rxjs/internal/util/argumentoutofrangeerror.d.ts", "../../../../node_modules/rxjs/internal/util/emptyerror.d.ts", "../../../../node_modules/rxjs/internal/util/objectunsubscribederror.d.ts", "../../../../node_modules/rxjs/internal/util/unsubscriptionerror.d.ts", "../../../../node_modules/rxjs/internal/util/timeouterror.d.ts", "../../../../node_modules/rxjs/internal/observable/bindcallback.d.ts", "../../../../node_modules/rxjs/internal/observable/bindnodecallback.d.ts", "../../../../node_modules/rxjs/internal/innersubscriber.d.ts", "../../../../node_modules/rxjs/internal/outersubscriber.d.ts", "../../../../node_modules/rxjs/internal/observable/combinelatest.d.ts", "../../../../node_modules/rxjs/internal/observable/concat.d.ts", "../../../../node_modules/rxjs/internal/observable/defer.d.ts", "../../../../node_modules/rxjs/internal/observable/empty.d.ts", "../../../../node_modules/rxjs/internal/observable/forkjoin.d.ts", "../../../../node_modules/rxjs/internal/observable/from.d.ts", "../../../../node_modules/rxjs/internal/observable/fromevent.d.ts", "../../../../node_modules/rxjs/internal/observable/fromeventpattern.d.ts", "../../../../node_modules/rxjs/internal/observable/generate.d.ts", "../../../../node_modules/rxjs/internal/observable/interval.d.ts", "../../../../node_modules/rxjs/internal/observable/merge.d.ts", "../../../../node_modules/rxjs/internal/observable/never.d.ts", "../../../../node_modules/rxjs/internal/observable/of.d.ts", "../../../../node_modules/rxjs/internal/observable/onerrorresumenext.d.ts", "../../../../node_modules/rxjs/internal/observable/pairs.d.ts", "../../../../node_modules/rxjs/internal/observable/partition.d.ts", "../../../../node_modules/rxjs/internal/observable/race.d.ts", "../../../../node_modules/rxjs/internal/observable/range.d.ts", "../../../../node_modules/rxjs/internal/observable/timer.d.ts", "../../../../node_modules/rxjs/internal/observable/using.d.ts", "../../../../node_modules/rxjs/internal/observable/zip.d.ts", "../../../../node_modules/rxjs/internal/scheduled/scheduled.d.ts", "../../../../node_modules/rxjs/internal/config.d.ts", "../../../../node_modules/rxjs/index.d.ts", "../../../../node_modules/@angular/core/signal.d.d.ts", "../../../../node_modules/@angular/core/primitives/di/index.d.ts", "../../../../node_modules/@angular/core/discovery.d.d.ts", "../../../../node_modules/@angular/core/api.d.d.ts", "../../../../node_modules/@angular/core/weak_ref.d.d.ts", "../../../../node_modules/@angular/core/index.d.ts", "../../../../node_modules/@angular/platform-browser-dynamic/index.d.ts", "../../../../node_modules/@angular/common/platform_location.d.d.ts", "../../../../node_modules/@angular/common/common_module.d.d.ts", "../../../../node_modules/@angular/common/xhr.d.d.ts", "../../../../node_modules/@angular/common/index.d.ts", "../../../../node_modules/@angular/common/module.d.d.ts", "../../../../node_modules/@angular/common/http/index.d.ts", "../../../../node_modules/@angular/platform-browser/browser.d.d.ts", "../../../../node_modules/@angular/animations/animation_player.d.d.ts", "../../../../node_modules/@angular/animations/animation_driver.d.d.ts", "../../../../node_modules/@angular/animations/browser/index.d.ts", "../../../../node_modules/@angular/platform-browser/animations/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/swui-constants.service.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/settings/app-settings.d.ts", "../../../../node_modules/moment/ts3.1-typings/moment.d.ts", "../../../../node_modules/moment-timezone/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/settings/settings.service.d.ts", "../../../../node_modules/@angular/platform-browser/index.d.ts", "../../../../node_modules/@angular/router/router_module.d.d.ts", "../../../../node_modules/@angular/router/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-hub-config/sw-hub-config.service.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-browser-title/sw-browser-title.service.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-hub-auth/sw-hub-auth.service.d.ts", "../../../../node_modules/@auth0/angular-jwt/lib/jwthelper.service.d.ts", "../../../../node_modules/@auth0/angular-jwt/lib/jwt.interceptor.d.ts", "../../../../node_modules/@auth0/angular-jwt/lib/jwtoptions.token.d.ts", "../../../../node_modules/@auth0/angular-jwt/lib/angular-jwt.module.d.ts", "../../../../node_modules/@auth0/angular-jwt/index.d.ts", "../../../../node_modules/@auth0/angular-jwt/auth0-angular-jwt.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-hub-auth/sw-hub-auth.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-hub-init/sw-hub-init.model.d.ts", "../../../../node_modules/@ngx-translate/core/dist/lib/missing-translation-handler.d.ts", "../../../../node_modules/@ngx-translate/core/dist/lib/translate.compiler.d.ts", "../../../../node_modules/@ngx-translate/core/dist/lib/translate.loader.d.ts", "../../../../node_modules/@ngx-translate/core/dist/lib/translate.parser.d.ts", "../../../../node_modules/@ngx-translate/core/dist/lib/translate.store.d.ts", "../../../../node_modules/@ngx-translate/core/dist/lib/translate.service.d.ts", "../../../../node_modules/@ngx-translate/core/dist/lib/translate.pipe.d.ts", "../../../../node_modules/@ngx-translate/core/dist/lib/translate.directive.d.ts", "../../../../node_modules/@ngx-translate/core/dist/public-api.d.ts", "../../../../node_modules/@ngx-translate/core/dist/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-sidebar/swui-sidebar.service.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-hub-entity/sw-hub-entity.model.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-hub-entity/sw-hub-entity-data-source.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-hub-entity/sw-hub-entity.service.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-hub-init/sw-hub-init.service.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-hub-auth/sw-hub-auth.guard.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-hub-auth/sw-hub-auth.token.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-hub-auth/permissions.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-hub-config/sw-hub-config-init.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-menu/swui-menu.interface.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-sidebar/swui-sidebar.component.d.ts", "../../../../node_modules/@angular/cdk/bidi-module.d.d.ts", "../../../../node_modules/@angular/cdk/bidi/index.d.ts", "../../../../node_modules/@angular/material/common-module.d.d.ts", "../../../../node_modules/@angular/material/palette.d.d.ts", "../../../../node_modules/@angular/material/icon-module.d.d.ts", "../../../../node_modules/@angular/material/icon-registry.d.d.ts", "../../../../node_modules/@angular/material/icon/index.d.ts", "../../../../node_modules/@angular/forms/index.d.ts", "../../../../node_modules/@angular/material/error-options.d.d.ts", "../../../../node_modules/@angular/material/line.d.d.ts", "../../../../node_modules/@angular/cdk/platform.d.d.ts", "../../../../node_modules/@angular/cdk/platform/index.d.ts", "../../../../node_modules/@angular/material/ripple.d.d.ts", "../../../../node_modules/@angular/material/ripple-module.d.d.ts", "../../../../node_modules/@angular/material/pseudo-checkbox-module.d.d.ts", "../../../../node_modules/@angular/cdk/list-key-manager.d.d.ts", "../../../../node_modules/@angular/cdk/activedescendant-key-manager.d.d.ts", "../../../../node_modules/@angular/cdk/focus-monitor.d.d.ts", "../../../../node_modules/@angular/cdk/focus-key-manager.d.d.ts", "../../../../node_modules/@angular/cdk/tree-key-manager-strategy.d.d.ts", "../../../../node_modules/@angular/cdk/number-property.d.d.ts", "../../../../node_modules/@angular/cdk/observers/index.d.ts", "../../../../node_modules/@angular/cdk/a11y-module.d.d.ts", "../../../../node_modules/@angular/cdk/a11y/index.d.ts", "../../../../node_modules/@angular/material/option.d.d.ts", "../../../../node_modules/@angular/material/option-module.d.d.ts", "../../../../node_modules/@angular/material/option-parent.d.d.ts", "../../../../node_modules/@angular/material/ripple-loader.d.d.ts", "../../../../node_modules/@angular/material/date-adapter.d.d.ts", "../../../../node_modules/@angular/material/core/index.d.ts", "../../../../node_modules/@angular/cdk/coercion/index.d.ts", "../../../../node_modules/@angular/material/list-option-types.d.d.ts", "../../../../node_modules/@angular/material/divider/index.d.ts", "../../../../node_modules/@angular/cdk/data-source.d.d.ts", "../../../../node_modules/@angular/cdk/view-repeater.d.d.ts", "../../../../node_modules/@angular/cdk/selection-model.d.d.ts", "../../../../node_modules/@angular/cdk/unique-selection-dispatcher.d.d.ts", "../../../../node_modules/@angular/cdk/collections/index.d.ts", "../../../../node_modules/@angular/material/list/index.d.ts", "../../../../node_modules/@angular/cdk/tree/index.d.ts", "../../../../node_modules/@angular/material/tree/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-menu/swui-menu.component.d.ts", "../../../../node_modules/@angular/cdk/portal-directives.d.d.ts", "../../../../node_modules/@angular/cdk/scrolling-module.d.d.ts", "../../../../node_modules/@angular/cdk/scrolling/index.d.ts", "../../../../node_modules/@angular/cdk/style-loader.d.d.ts", "../../../../node_modules/@angular/cdk/overlay-module.d.d.ts", "../../../../node_modules/@angular/cdk/overlay/index.d.ts", "../../../../node_modules/@angular/material/menu/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-menu/swui-menu.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-sidebar/swui-sidebar.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-hub-init/sw-hub-init.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-hub-init/sw-hub-init.token.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/columns-management/columns-management.model.d.ts", "../../../../node_modules/dexie/dist/dexie.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-dexie/dexie-types.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-dexie/sw-dexie.service.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-dexie/sw-dexie-columns-provider.service.d.ts", "../../../../node_modules/@skywind-group/lib-swui/services/sw-dexie/sw-dexie.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/pipes/swui-currency-symbol/swui-currency-symbol.pipe.d.ts", "../../../../node_modules/@skywind-group/lib-swui/pipes/swui-currency-symbol/swui-currency-symbol.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-checkbox/swui-checkbox.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-checkbox/swui-checkbox.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-autoselect/option.model.d.ts", "../../../../node_modules/@angular/material/autocomplete/index.d.ts", "../../../../node_modules/@angular/material/form-field-control.d.d.ts", "../../../../node_modules/@angular/material/form-field.d.d.ts", "../../../../node_modules/@angular/material/form-field-module.d.d.ts", "../../../../node_modules/@angular/cdk/text-field/index.d.ts", "../../../../node_modules/@angular/material/input/index.d.ts", "../../../../node_modules/@angular/material/form-field/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/common/swui-mat-form-field-control.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-autoselect/swui-autoselect.component.d.ts", "../../../../node_modules/@angular/material/button/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-autoselect/swui-autoselect.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-is-control-invalid/swui-is-control-invalid.service.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-control-messages/swui-control-messages.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-control-messages/swui-control-messages.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-control-messages/swui-control-messages.token.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-control-messages/public-api.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-chips-autocomplete/swui-chips-autocomplete.component.d.ts", "../../../../node_modules/@angular/material/chips/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-chips-autocomplete/swui-chips-autocomplete.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-timepicker/swui-timepicker.interface.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-date-range/swui-date-range.model.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-datetimepicker/swui-datetimepicker.interface.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-datetimepicker/swui-datetimepicker.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-calendar/swui-calendar.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-calendar/swui-calendar.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-timepicker/swui-timepicker.component.d.ts", "../../../../node_modules/@angular/material/select-module.d.d.ts", "../../../../node_modules/@angular/material/select/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-timepicker/swui-timepicker.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-datetimepicker/swui-datetimepicker.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-date-time-range/custom-period/custom-period.interface.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-date-time-range/swui-date-time-range.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-select-table/swui-select-table.interface.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/dynamic-form.model.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-top-filter/swui-schema-top-filter.model.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-top-filter/top-filter-data.service.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-date-time-range/custom-period/custom-period.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-date-time-range/custom-period/custom-period.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-date-time-range/swui-date-time-range.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-breadcrumbs/swui-breadcrumbs.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-breadcrumbs/swui-breadcrumbs.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-input/swui-input.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-input/swui-input.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-input-sequence-map/swui-input-sequence-map.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-input-sequence-map/swui-input-sequence-map.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-time-duration/swui-time-duration.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-time-duration/swui-time-duration.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-start-time/swui-start-time.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-start-time/swui-start-time.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/footer-widget/total/total.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/footer-widget/footer-widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/pipes/truncate.interface.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/calc/calc.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/calc-async/calc-async.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/click/click.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/currency/currency.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/game-labels/game-labels.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/icon/icon.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/icon-popover/icon-popover.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/image/image.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/inactivity/inactivity.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/link/link.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/list/list.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/percent/percent.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/status/status.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/string/string.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/timestamp/timestamp.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/games-labels/games-labels.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/percent-editable/percent-editable.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/td-widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/swui-grid.model.d.ts", "../../../../node_modules/@angular/material/tooltip-module.d.d.ts", "../../../../node_modules/@angular/material/paginator.d.d.ts", "../../../../node_modules/@angular/material/paginator/index.d.ts", "../../../../node_modules/@angular/material/sort-direction.d.d.ts", "../../../../node_modules/@angular/material/sort.d.d.ts", "../../../../node_modules/@angular/material/sort/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/services/grid-data.service.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/swui-grid-url-handler.service.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/swui-grid.datasource.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/registry/registry.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/boolean/boolean.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/colorful-labels/colorful-labels.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/jackpot/jackpot.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/number/number.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/user/user.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/pipes/formatted-number.pipe.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/pipes/formatted-money.pipe.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/pipes/truncate.pipe.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/pipes/grid-pipes.module.d.ts", "../../../../node_modules/@angular/material/tooltip/index.d.ts", "../../../../node_modules/@angular/material/progress-bar/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/td-widget/td-widgets.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/footer-widget/string/string.widget.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/footer-widget/footer-widgets.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/row-actions/row-actions.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/row-actions/row-actions.module.d.ts", "../../../../node_modules/@angular/cdk/portal/index.d.ts", "../../../../node_modules/@angular/cdk/dialog/index.d.ts", "../../../../node_modules/@angular/material/dialog.d.d.ts", "../../../../node_modules/@angular/material/dialog/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/bulk-actions/bulk-actions.model.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/bulk-actions/bulk-actions.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-select/swui-select.interface.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/columns-management/columns-management.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-menu-select/swui-menu-select.component.d.ts", "../../../../node_modules/@angular/material/checkbox/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-menu-select/swui-menu-select.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/columns-management/columns-management.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/swui-grid.config.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/swui-grid.component.d.ts", "../../../../node_modules/@angular/cdk/table/index.d.ts", "../../../../node_modules/@angular/material/table/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/widget-chooser/widget-chooser.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/widget-chooser/widget-chooser.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/default-widget/default-widget.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/default-widget/default-widget.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/bulk-actions/dialogs/confirm-dialog.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/bulk-actions/dialogs/less-dialog.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/bulk-actions/dialogs/no-data-dialog.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/bulk-actions/bulk-actions.module.d.ts", "../../../../node_modules/@angular/material/progress-spinner.d.d.ts", "../../../../node_modules/@angular/material/progress-spinner/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-progress-container/swui-progress-container.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-progress-container/swui-progress-container.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/swui-grid.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/registry/default-registry.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/registry/default-list.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-grid/services/local-data.service.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/mat-dynamic-form.service.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-top-filter/swui-schema-top-filter.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/mat-dynamic-form.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/control-items/control-items.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/control-input/control-input.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/input-outlet/input-outlet.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/input-text/input-text.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/input-select/input-select.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/input-image/input-image.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/input-color/input-color.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/input-switch/input-switch.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/input-password/input-password.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/input-date-range/input-date-range.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/input-search/input-search.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/input-numeric-range/input-numeric-range.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/button-action/button-action.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/input-number/input-number.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/input-date/input-date.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/input-video-url/input-video-url.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/input-select-table/input-select-table.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-switchery/switchery.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-switchery/switchery.module.d.ts", "../../../../node_modules/ngx-color-picker/lib/formats.d.ts", "../../../../node_modules/ngx-color-picker/lib/helpers.d.ts", "../../../../node_modules/ngx-color-picker/lib/color-picker.service.d.ts", "../../../../node_modules/ngx-color-picker/lib/color-picker.component.d.ts", "../../../../node_modules/ngx-color-picker/lib/color-picker.directive.d.ts", "../../../../node_modules/ngx-color-picker/lib/ng-dev-mode.d.ts", "../../../../node_modules/ngx-color-picker/lib/color-picker.module.d.ts", "../../../../node_modules/ngx-color-picker/public-api.d.ts", "../../../../node_modules/ngx-color-picker/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/directives/selectonclick/select-on-click.directive.d.ts", "../../../../node_modules/@skywind-group/lib-swui/directives/selectonclick/select-on-click.module.d.ts", "../../../../node_modules/@angular/material/slide-toggle/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-date-time-chooser/swui-date-time-chooser.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-mat-calendar/swui-mat-calendar.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-mat-calendar/swui-mat-calendar.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-date-time-chooser/swui-date-time-chooser.module.d.ts", "../../../../node_modules/@angular/material/tabs/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-date-picker/swui-date-picker-config.model.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-date-range/swui-date-range.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-date-range/swui-date-range.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-select/swui-select.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-select/swui-select.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-search/swui-search.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-search/swui-search.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-numeric-range-menu/swui-numeric-range-menu.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-numeric-range-menu/swui-numeric-range-menu.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-date-picker/swui-date-picker.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-date-picker/swui-date-picker.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-select-table/swui-select-table.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-select-table/swui-select-table.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/mat-dynamic-form.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-schema-top-filter/swui-schema-top-filter.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-numeric-range/swui-numeric-range.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-numeric-range/swui-numeric-range.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-games-select-manager/game-category-item.model.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-games-select-manager/game-select-item/game-select-item-types.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-games-select-manager/game.model.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-games-select-manager/game-select-item/game-select-item.model.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-games-select-manager/games-select-manager.service.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-games-select-manager/games-select-manager.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-games-select-manager/available-games/available-games.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-games-select-manager/available-labels/available-labels.component.d.ts", "../../../../node_modules/@angular/cdk/drag-drop/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-games-select-manager/selected-items/selected-items.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-games-select-manager/game-select-columns/extra-column-chooser.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-games-select-manager/game-select-columns/default-column.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-games-select-manager/game-select-columns/coin-value-column.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-games-select-manager/game-select-columns/game-coeff-column.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-games-select-manager/filtered.pipe.d.ts", "../../../../node_modules/@angular/cdk/accordion/index.d.ts", "../../../../node_modules/@angular/material/expansion/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-games-select-manager/games-select-manager.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-page-panel/swui-page-panel.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-page-panel/action-confirm-dialog/action-confirm-dialog.component.d.ts", "../../../../node_modules/@angular/material/card/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-page-panel/swui-page-panel.module.d.ts", "../../../../node_modules/@angular/material/snack-bar/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-notifications/swui-notifications.service.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-notifications/swui-snackbar/swui-snackbar.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-notifications/swui-notifications.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-multiselect/swui-multiselect.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-multiselect/swui-multiselect.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-top-menu/swui-top-menu.component.d.ts", "../../../../node_modules/@angular/material/toolbar/index.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-top-menu/swui-top-menu.model.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-top-menu/hub-selector/hub-selector.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-top-menu/hub-selector/hub-selector.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-top-menu/user-menu/user-menu.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-top-menu/language-selector/language-selector.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-top-menu/language-selector/language-selector.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-top-menu/settings-dialog/swui-settings-dialog.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-top-menu/settings-dialog/swui-settings-dialog.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-top-menu/user-menu/user-menu.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-top-menu/entity-picker/entity-picker.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-top-menu/entity-picker/entity-picker.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-top-menu/swui-top-menu.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-dynamic-form/mat-dynamic-form-widget.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-translations-manager/swui-translations-manager.service.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-translations-manager/swui-translations-manager.component.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-translations-manager/swui-translations-manager.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-is-control-invalid/swui-is-control-invalid.directive.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-is-control-invalid/swui-is-control-invalid.pipe.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-is-control-invalid/swui-is-control-invalid.module.d.ts", "../../../../node_modules/@skywind-group/lib-swui/swui-is-control-invalid/public-api.d.ts", "../../../../node_modules/@skywind-group/lib-swui/common/date-validation.util.d.ts", "../../../../node_modules/@skywind-group/lib-swui/public-api.d.ts", "../../../../node_modules/@skywind-group/lib-swui/index.d.ts", "../../../../node_modules/ngx-toastr/toastr/toast.directive.d.ts", "../../../../node_modules/ngx-toastr/portal/portal.d.ts", "../../../../node_modules/ngx-toastr/overlay/overlay-ref.d.ts", "../../../../node_modules/ngx-toastr/toastr/toast-ref.d.ts", "../../../../node_modules/ngx-toastr/toastr/toastr-config.d.ts", "../../../../node_modules/ngx-toastr/overlay/overlay.d.ts", "../../../../node_modules/ngx-toastr/toastr/toastr.service.d.ts", "../../../../node_modules/ngx-toastr/toastr/toast.component.d.ts", "../../../../node_modules/ngx-toastr/toastr/toastr.module.d.ts", "../../../../node_modules/ngx-toastr/toastr/toast.provider.d.ts", "../../../../node_modules/ngx-toastr/toastr/toast-noanimation.component.d.ts", "../../../../node_modules/ngx-toastr/overlay/overlay-container.d.ts", "../../../../node_modules/ngx-toastr/public_api.d.ts", "../../../../node_modules/ngx-toastr/index.d.ts", "../../../../node_modules/rxjs/internal/operators/audit.d.ts", "../../../../node_modules/rxjs/internal/operators/audittime.d.ts", "../../../../node_modules/rxjs/internal/operators/buffer.d.ts", "../../../../node_modules/rxjs/internal/operators/buffercount.d.ts", "../../../../node_modules/rxjs/internal/operators/buffertime.d.ts", "../../../../node_modules/rxjs/internal/operators/buffertoggle.d.ts", "../../../../node_modules/rxjs/internal/operators/bufferwhen.d.ts", "../../../../node_modules/rxjs/internal/operators/catcherror.d.ts", "../../../../node_modules/rxjs/internal/operators/combineall.d.ts", "../../../../node_modules/rxjs/internal/operators/combinelatest.d.ts", "../../../../node_modules/rxjs/internal/operators/concat.d.ts", "../../../../node_modules/rxjs/internal/operators/concatall.d.ts", "../../../../node_modules/rxjs/internal/operators/concatmap.d.ts", "../../../../node_modules/rxjs/internal/operators/concatmapto.d.ts", "../../../../node_modules/rxjs/internal/operators/count.d.ts", "../../../../node_modules/rxjs/internal/operators/debounce.d.ts", "../../../../node_modules/rxjs/internal/operators/debouncetime.d.ts", "../../../../node_modules/rxjs/internal/operators/defaultifempty.d.ts", "../../../../node_modules/rxjs/internal/operators/delay.d.ts", "../../../../node_modules/rxjs/internal/operators/delaywhen.d.ts", "../../../../node_modules/rxjs/internal/operators/dematerialize.d.ts", "../../../../node_modules/rxjs/internal/innersubscribe.d.ts", "../../../../node_modules/rxjs/internal/operators/distinct.d.ts", "../../../../node_modules/rxjs/internal/operators/distinctuntilchanged.d.ts", "../../../../node_modules/rxjs/internal/operators/distinctuntilkeychanged.d.ts", "../../../../node_modules/rxjs/internal/operators/elementat.d.ts", "../../../../node_modules/rxjs/internal/operators/endwith.d.ts", "../../../../node_modules/rxjs/internal/operators/every.d.ts", "../../../../node_modules/rxjs/internal/operators/exhaust.d.ts", "../../../../node_modules/rxjs/internal/operators/exhaustmap.d.ts", "../../../../node_modules/rxjs/internal/operators/expand.d.ts", "../../../../node_modules/rxjs/internal/operators/filter.d.ts", "../../../../node_modules/rxjs/internal/operators/finalize.d.ts", "../../../../node_modules/rxjs/internal/operators/find.d.ts", "../../../../node_modules/rxjs/internal/operators/findindex.d.ts", "../../../../node_modules/rxjs/internal/operators/first.d.ts", "../../../../node_modules/rxjs/internal/operators/ignoreelements.d.ts", "../../../../node_modules/rxjs/internal/operators/isempty.d.ts", "../../../../node_modules/rxjs/internal/operators/last.d.ts", "../../../../node_modules/rxjs/internal/operators/map.d.ts", "../../../../node_modules/rxjs/internal/operators/mapto.d.ts", "../../../../node_modules/rxjs/internal/operators/materialize.d.ts", "../../../../node_modules/rxjs/internal/operators/max.d.ts", "../../../../node_modules/rxjs/internal/operators/merge.d.ts", "../../../../node_modules/rxjs/internal/operators/mergeall.d.ts", "../../../../node_modules/rxjs/internal/operators/mergemap.d.ts", "../../../../node_modules/rxjs/internal/operators/mergemapto.d.ts", "../../../../node_modules/rxjs/internal/operators/mergescan.d.ts", "../../../../node_modules/rxjs/internal/operators/min.d.ts", "../../../../node_modules/rxjs/internal/operators/multicast.d.ts", "../../../../node_modules/rxjs/internal/operators/observeon.d.ts", "../../../../node_modules/rxjs/internal/operators/onerrorresumenext.d.ts", "../../../../node_modules/rxjs/internal/operators/pairwise.d.ts", "../../../../node_modules/rxjs/internal/operators/partition.d.ts", "../../../../node_modules/rxjs/internal/operators/pluck.d.ts", "../../../../node_modules/rxjs/internal/operators/publish.d.ts", "../../../../node_modules/rxjs/internal/operators/publishbehavior.d.ts", "../../../../node_modules/rxjs/internal/operators/publishlast.d.ts", "../../../../node_modules/rxjs/internal/operators/publishreplay.d.ts", "../../../../node_modules/rxjs/internal/operators/race.d.ts", "../../../../node_modules/rxjs/internal/operators/reduce.d.ts", "../../../../node_modules/rxjs/internal/operators/repeat.d.ts", "../../../../node_modules/rxjs/internal/operators/repeatwhen.d.ts", "../../../../node_modules/rxjs/internal/operators/retry.d.ts", "../../../../node_modules/rxjs/internal/operators/retrywhen.d.ts", "../../../../node_modules/rxjs/internal/operators/refcount.d.ts", "../../../../node_modules/rxjs/internal/operators/sample.d.ts", "../../../../node_modules/rxjs/internal/operators/sampletime.d.ts", "../../../../node_modules/rxjs/internal/operators/scan.d.ts", "../../../../node_modules/rxjs/internal/operators/sequenceequal.d.ts", "../../../../node_modules/rxjs/internal/operators/share.d.ts", "../../../../node_modules/rxjs/internal/operators/sharereplay.d.ts", "../../../../node_modules/rxjs/internal/operators/single.d.ts", "../../../../node_modules/rxjs/internal/operators/skip.d.ts", "../../../../node_modules/rxjs/internal/operators/skiplast.d.ts", "../../../../node_modules/rxjs/internal/operators/skipuntil.d.ts", "../../../../node_modules/rxjs/internal/operators/skipwhile.d.ts", "../../../../node_modules/rxjs/internal/operators/startwith.d.ts", "../../../../node_modules/rxjs/internal/operators/subscribeon.d.ts", "../../../../node_modules/rxjs/internal/operators/switchall.d.ts", "../../../../node_modules/rxjs/internal/operators/switchmap.d.ts", "../../../../node_modules/rxjs/internal/operators/switchmapto.d.ts", "../../../../node_modules/rxjs/internal/operators/take.d.ts", "../../../../node_modules/rxjs/internal/operators/takelast.d.ts", "../../../../node_modules/rxjs/internal/operators/takeuntil.d.ts", "../../../../node_modules/rxjs/internal/operators/takewhile.d.ts", "../../../../node_modules/rxjs/internal/operators/tap.d.ts", "../../../../node_modules/rxjs/internal/operators/throttle.d.ts", "../../../../node_modules/rxjs/internal/operators/throttletime.d.ts", "../../../../node_modules/rxjs/internal/operators/throwifempty.d.ts", "../../../../node_modules/rxjs/internal/operators/timeinterval.d.ts", "../../../../node_modules/rxjs/internal/operators/timeout.d.ts", "../../../../node_modules/rxjs/internal/operators/timeoutwith.d.ts", "../../../../node_modules/rxjs/internal/operators/timestamp.d.ts", "../../../../node_modules/rxjs/internal/operators/toarray.d.ts", "../../../../node_modules/rxjs/internal/operators/window.d.ts", "../../../../node_modules/rxjs/internal/operators/windowcount.d.ts", "../../../../node_modules/rxjs/internal/operators/windowtime.d.ts", "../../../../node_modules/rxjs/internal/operators/windowtoggle.d.ts", "../../../../node_modules/rxjs/internal/operators/windowwhen.d.ts", "../../../../node_modules/rxjs/internal/operators/withlatestfrom.d.ts", "../../../../node_modules/rxjs/internal/operators/zip.d.ts", "../../../../node_modules/rxjs/internal/operators/zipall.d.ts", "../../../../node_modules/rxjs/operators/index.d.ts", "../../../../src/environments/base.ts", "../../../../src/environments/main.ts", "../../../../src/environments/environment.ts", "../../../../src/app/common/models/select-option.model.ts", "../../../../src/app/common/typings/finished-option.ts", "../../../../src/app/common/typings/notification_message.ts", "../../../../src/app/common/typings/ui.button.ts", "../../../../src/app/common/typings/base.ts", "../../../../src/app/common/typings/country.ts", "../../../../src/app/common/typings/currency.ts", "../../../../src/app/common/typings/language.ts", "../../../../src/app/common/typings/balance.ts", "../../../../src/app/common/typings/agent.ts", "../../../../src/app/common/typings/audit.ts", "../../../../src/app/common/typings/deposit.ts", "../../../../src/app/common/typings/game.ts", "../../../../src/app/common/typings/gamecategory.ts", "../../../../src/app/common/typings/withdrawal.ts", "../../../../src/app/common/typings/jurisdiction.ts", "../../../../src/app/common/typings/entity.ts", "../../../../src/app/common/typings/server-notification.ts", "../../../../src/app/common/typings/player.ts", "../../../../src/app/common/typings/payment.ts", "../../../../src/app/pages/users/components/roles/role.model.ts", "../../../../src/app/common/typings/user.ts", "../../../../src/app/common/typings/app-settings.ts", "../../../../src/app/common/typings/schema.field.match.ts", "../../../../src/app/common/typings/reports/game_history_round.ts", "../../../../src/app/common/typings/reports/game_history_spin.ts", "../../../../src/app/common/typings/reports/currency.ts", "../../../../src/app/common/typings/reports/player.ts", "../../../../src/app/common/typings/reports/financial.ts", "../../../../src/app/common/typings/grid.filter.ordering.ts", "../../../../src/app/common/typings/grid.filter.pages.ts", "../../../../src/app/common/typings/grid.filter.values.ts", "../../../../src/app/common/typings/grid.filter.ts", "../../../../src/app/common/typings/index.ts", "../../../../src/app/pages/games-management/game-provider.model.ts", "../../../../src/app/app.constants.ts", "../../../../src/app/common/services/bathemespinner/bathemespinner.service.ts", "../../../../src/app/app.component.ts", "../../../../node_modules/@angular/material/sidenav/index.d.ts", "../../../../node_modules/ngx-bootstrap/component-loader/bs-component-ref.class.d.ts", "../../../../node_modules/ngx-bootstrap/positioning/models/index.d.ts", "../../../../node_modules/ngx-bootstrap/positioning/ng-positioning.d.ts", "../../../../node_modules/ngx-bootstrap/positioning/positioning.service.d.ts", "../../../../node_modules/ngx-bootstrap/positioning/utils/checkmargin.d.ts", "../../../../node_modules/ngx-bootstrap/positioning/public_api.d.ts", "../../../../node_modules/ngx-bootstrap/positioning/index.d.ts", "../../../../node_modules/ngx-bootstrap/component-loader/listen-options.model.d.ts", "../../../../node_modules/ngx-bootstrap/component-loader/component-loader.class.d.ts", "../../../../node_modules/ngx-bootstrap/component-loader/component-loader.factory.d.ts", "../../../../node_modules/ngx-bootstrap/component-loader/content-ref.class.d.ts", "../../../../node_modules/ngx-bootstrap/component-loader/public_api.d.ts", "../../../../node_modules/ngx-bootstrap/component-loader/index.d.ts", "../../../../node_modules/ngx-bootstrap/dropdown/bs-dropdown.config.d.ts", "../../../../node_modules/ngx-bootstrap/dropdown/bs-dropdown-menu.directive.d.ts", "../../../../node_modules/ngx-bootstrap/dropdown/bs-dropdown.state.d.ts", "../../../../node_modules/@angular/animations/index.d.ts", "../../../../node_modules/ngx-bootstrap/dropdown/bs-dropdown.directive.d.ts", "../../../../node_modules/ngx-bootstrap/dropdown/bs-dropdown-toggle.directive.d.ts", "../../../../node_modules/ngx-bootstrap/dropdown/bs-dropdown-container.component.d.ts", "../../../../node_modules/ngx-bootstrap/dropdown/bs-dropdown.module.d.ts", "../../../../node_modules/ngx-bootstrap/dropdown/public_api.d.ts", "../../../../node_modules/ngx-bootstrap/dropdown/index.d.ts", "../../../../node_modules/ngx-bootstrap/tabs/ng-transclude.directive.d.ts", "../../../../node_modules/ngx-bootstrap/tabs/tabset.config.d.ts", "../../../../node_modules/ngx-bootstrap/tabs/tabset.component.d.ts", "../../../../node_modules/ngx-bootstrap/tabs/tab.directive.d.ts", "../../../../node_modules/ngx-bootstrap/tabs/tab-heading.directive.d.ts", "../../../../node_modules/ngx-bootstrap/tabs/tabs.module.d.ts", "../../../../node_modules/ngx-bootstrap/tabs/public_api.d.ts", "../../../../node_modules/ngx-bootstrap/tabs/index.d.ts", "../../../../src/app/common/base/base-service.ts", "../../../../src/app/common/models/schema.model.ts", "../../../../src/app/common/base/base-component.ts", "../../../../src/app/common/base/index.ts", "../../../../src/app/common/typings/deployment-group.ts", "../../../../src/app/common/models/entity-settings.model.ts", "../../../../src/app/common/models/entity.model.ts", "../../../../src/app/common/models/whitelist-ip.model.ts", "../../../../src/app/common/pipes/formatted-money/formatted-money.pipe.ts", "../../../../src/app/common/core/currecy-transform.ts", "../../../../src/app/common/services/entity.service.ts", "../../../../src/app/common/typings/server-config.ts", "../../../../src/app/common/components/livechat/live-chat.component.ts", "../../../../src/app/common/directives/baifallowed/baifallowed.directive.ts", "../../../../src/app/common/directives/baifallowed/baifallowed.module.ts", "../../../../src/app/common/components/livechat/live-chat.module.ts", "../../../../src/app/common/services/calendar.service.ts", "../../../../src/app/common/lib/files.ts", "../../../../node_modules/@types/lodash/common/common.d.ts", "../../../../node_modules/@types/lodash/common/array.d.ts", "../../../../node_modules/@types/lodash/common/collection.d.ts", "../../../../node_modules/@types/lodash/common/date.d.ts", "../../../../node_modules/@types/lodash/common/function.d.ts", "../../../../node_modules/@types/lodash/common/lang.d.ts", "../../../../node_modules/@types/lodash/common/math.d.ts", "../../../../node_modules/@types/lodash/common/number.d.ts", "../../../../node_modules/@types/lodash/common/object.d.ts", "../../../../node_modules/@types/lodash/common/seq.d.ts", "../../../../node_modules/@types/lodash/common/string.d.ts", "../../../../node_modules/@types/lodash/common/util.d.ts", "../../../../node_modules/@types/lodash/index.d.ts", "../../../../src/app/common/services/csv.service.ts", "../../../../src/app/common/models/menu-item.model.ts", "../../../../src/app/common/services/menu-sidebar.service.ts", "../../../../src/app/common/services/country.service.ts", "../../../../src/app/common/services/currency.service.ts", "../../../../src/app/common/services/game-provider.service.ts", "../../../../src/app/common/services/languages.service.ts", "../../../../src/app/common/services/resolvers/brief.resolver.ts", "../../../../src/app/common/services/resolvers/currencies.resolver.ts", "../../../../src/app/common/services/resolvers/short-structure.resolver.ts", "../../../../src/app/pages/pages.animation.ts", "../../../../src/app/pages/pages.menu.ts", "../../../../src/app/pages/pages.component.ts", "../../../../src/app/pages/empty/empty.component.ts", "../../../../src/app/pages/empty/empty-routing.module.ts", "../../../../src/app/pages/empty/empty.module.ts", "../../../../src/app/pages/support/components/faq/faq.component.ts", "../../../../src/app/pages/support/components/faq/index.ts", "../../../../src/app/pages/support/components/support-tickets/support-tickets.component.ts", "../../../../src/app/pages/support/components/support-tickets/index.ts", "../../../../src/app/pages/support/components/system-notifications/system-notifications.component.ts", "../../../../src/app/pages/support/components/system-notifications/index.ts", "../../../../src/app/pages/support/support.component.ts", "../../../../src/app/pages/support/support.routing.ts", "../../../../src/app/pages/support/support.module.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/global-finder/global-finder.component.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/mat-business-structure-search.service.ts", "../../../../src/app/pages/business-management/business-management.component.ts", "../../../../src/app/common/services/entity-settings.service.ts", "../../../../src/app/common/models/game.model.ts", "../../../../src/app/common/services/game.service.ts", "../../../../src/app/common/services/merchant-types.service.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/structure-entity.model.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/business-structure.service.ts", "../../../../src/app/common/services/jurisdiction.service.ts", "../../../../node_modules/is-cidr/index.d.ts", "../../../../src/app/common/services/validation.service.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/mat-entity-edit-dialog/form.component.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/mat-entity-edit-dialog/mat-entity-edit-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-users/users.schema.ts", "../../../../src/app/pages/users/user.model.ts", "../../../../src/app/common/components/bo-confirmation/bo-confirmation.component.ts", "../../../../src/app/common/services/role.service.ts", "../../../../src/app/common/typings/permission.ts", "../../../../src/app/common/services/user.service.ts", "../../../../src/app/common/components/mat-user-editor/user-editor.service.ts", "../../../../src/app/common/components/mat-user-editor/user-form.component.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/manage-balance/form.component.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/mat-regional-dialog/mat-regional-item/mat-regional-item.component.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/mat-regional-dialog/mat-regional-dialog.component.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/setup-hint-dialog/setup-hint-dialog.component.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/status-confirm/status-confirm.component.ts", "../../../../src/app/common/models/site.model.ts", "../../../../src/app/common/services/site.service.ts", "../../../../src/app/pages/business-management/components/entities/setup-entity.service.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/tab-2fa.component.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/search-by-domain-modal/search-by-domain-modal.component.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/mat-business-structure.component.ts", "../../../../src/app/common/components/control-messages/control-messages.component.ts", "../../../../src/app/common/components/control-messages/control-messages.module.ts", "../../../../src/app/common/models/domain.model.ts", "../../../../src/app/pages/domains-management/domains-management.service.ts", "../../../../src/app/pages/business-management/components/entity-page-panel/entity-page-panel.component.ts", "../../../../src/app/pages/business-management/components/entity-page-panel/entity-page-panel.module.ts", "../../../../src/app/common/services/resolvers/availableproviders.resolver.ts", "../../../../src/app/common/services/resolvers/entity-settings.resolver.ts", "../../../../src/app/common/services/resolvers/structure.resolver.ts", "../../../../src/app/pages/business-management/components/entities/resolvers/setup-entity-details.resolver.ts", "../../../../src/app/pages/business-management/components/entities/resolvers/setup-entity-parent.resolver.ts", "../../../../src/app/pages/business-management/entity-state.service.ts", "../../../../src/app/pages/business-management/components/entities/setup-entity.component.ts", "../../../../src/app/pages/business-management/components/entities/setup-entity.guard.ts", "../../../../src/app/common/components/hints/hints.component.ts", "../../../../src/app/common/pipes/trusturl/trusturl.pipe.ts", "../../../../src/app/theme/theme.constants.ts", "../../../../src/app/theme/theme.configprovider.ts", "../../../../src/app/theme/theme.config.ts", "../../../../src/app/theme/index.ts", "../../../../src/app/common/pipes/baprofilepicture/baprofilepicture.pipe.ts", "../../../../src/app/common/pipes/baapppicture/baapppicture.pipe.ts", "../../../../src/app/common/pipes/bakameleonpicture/bakameleonpicture.pipe.ts", "../../../../src/app/common/pipes/swcolored/swcolored.pipe.ts", "../../../../src/app/common/pipes/highlight/highlight.pipe.ts", "../../../../src/app/common/pipes/nl2br/nl2br.pipe.ts", "../../../../src/app/common/pipes/formatted-number/formatted-number.pipe.ts", "../../../../src/app/common/pipes/objectkeys/object-keys.pipe.ts", "../../../../src/app/common/pipes/swbytes/bytes.pipe.ts", "../../../../src/app/common/pipes/truncate/truncate.interface.ts", "../../../../src/app/common/pipes/truncate/truncate.pipe.ts", "../../../../src/app/common/pipes/sanitise/sanitise.pipe.ts", "../../../../src/app/common/pipes/pipes.module.ts", "../../../../src/app/common/components/hints/hints.module.ts", "../../../../src/app/common/directives/trim-input-value/trim-input-value.component.ts", "../../../../src/app/common/directives/trim-input-value/trim-input-value.module.ts", "../../../../src/app/common/services/deployment-group.service.ts", "../../../../src/app/common/services/resolvers/countries.resolver.ts", "../../../../src/app/common/services/resolvers/entity-balances.resolver.ts", "../../../../src/app/common/services/resolvers/languages.resolver.ts", "../../../../src/app/common/components/touchspin/touchspin.component.ts", "../../../../src/app/common/components/touchspin/touchspin.module.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/manage-balance/manage-balance.component.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/manage-balance/manage-balance.module.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/mat-country-dialog/mat-country-dialog.component.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/mat-country-dialog/mat-country-dialog.module.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/mat-currency-dialog/mat-currency-dialog.component.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/mat-currency-dialog/mat-currency-dialog.module.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/mat-jurisdictions-dialog/jurisdiction-item.model.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/mat-jurisdictions-dialog/mat-jurisdictions-dialog.component.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/mat-jurisdictions-dialog/mat-jurisdictions-dialog.module.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/mat-language-dialog/mat-language-dialog.component.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/mat-language-dialog/mat-language-dialog.module.ts", "../../../../src/app/pages/business-management/components/entities/dialogs/remove-confirm-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/dialogs/entity-setup-dialogs.module.ts", "../../../../src/app/pages/business-management/components/entities/resolvers/setup-entity-deployment-groups.resolver.ts", "../../../../src/app/pages/business-management/components/entities/resolvers/setup-entity-jurisdictions.resolver.ts", "../../../../src/app/pages/business-management/components/entities/tab-regional/entity-countries.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-regional/entity-allowed-countries.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-regional/entity-restricted-countries.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-regional/entity-currencies.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-regional/entity-deployment-groups.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-regional/entity-jurisdictions.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-regional/entity-languages.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-regional/tab-regional.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-regional/tab-regional.routing.ts", "../../../../src/app/pages/business-management/components/entities/tab-regional/entity-blocked-countries.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-regional/tab-regional.module.ts", "../../../../node_modules/@angular/cdk/layout/index.d.ts", "../../../../node_modules/@angular/material/radio/index.d.ts", "../../../../src/app/common/components/mat-user-editor/user-editor-dialog.component.ts", "../../../../src/app/common/components/bo-confirmation/bo-confirmation.module.ts", "../../../../src/app/common/components/mat-user-editor/user-editor.module.ts", "../../../../src/app/common/services/user-actions.service.ts", "../../../../src/app/pages/business-management/components/entities/tab-users/dialogs/delete-user-dialog.component.ts", "../../../../src/app/pages/auth/two-factor/two-factor.model.ts", "../../../../src/app/pages/business-management/components/entities/tab-users/dialogs/twofa-reset-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-users/dialogs/unblock-user-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-users/dialogs/tab-users-dialogs.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-users/entity-users.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-users/tab-users.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-users/tab-users.routing.ts", "../../../../src/app/pages/business-management/components/entities/tab-users/tab-users.module.ts", "../../../../src/app/common/components/vertical-tabs/vertical-tabs-body/vertical-tabs-body.component.ts", "../../../../src/app/common/components/vertical-tabs/vertical-tabs-label/vertical-tabs-label.component.ts", "../../../../src/app/common/components/vertical-tabs/vertical-tabs-item/vertical-tabs-item.component.ts", "../../../../src/app/common/components/vertical-tabs/vertical-tabs.component.ts", "../../../../src/app/common/components/vertical-tabs/vertical-tabs.module.ts", "../../../../src/app/common/directives/disable-if-not-allowed/disable-if-not-allowed.directive.ts", "../../../../src/app/common/directives/disable-if-not-allowed/disable-if-not-allowed-disable.module.ts", "../../../../src/app/pages/gamehistory/components/game-notify/game-notify.component.ts", "../../../../src/app/pages/gamehistory/components/game-notify/game-notify.module.ts", "../../../../src/app/common/typings/entity-notifications.ts", "../../../../src/app/pages/business-management/components/entities/tab-notifications/entity-notification-currencies-list/entity-notification-currencies-list.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-notifications/entity-notification-currencies-list/entity-notification-currencies-list.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-notifications/entity-notification-emails-list/entity-notification-emails-list.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-notifications/entity-notification-emails-list/entity-notification-emails-list.module.ts", "../../../../node_modules/@angular/material/button-toggle.d.d.ts", "../../../../node_modules/@angular/material/button-toggle/index.d.ts", "../../../../src/app/pages/business-management/components/entities/tab-notifications/entity-notification-report-period/entity-notification-report-period.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-notifications/entity-notification-report-period/entity-notification-report-period.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-notifications/entity-notification.component.ts", "../../../../node_modules/rxjs-compat/observable.d.ts", "../../../../node_modules/rxjs/observable.d.ts", "../../../../src/app/pages/business-management/components/entities/tab-notifications/hasunsaveddataguard/unsaveddataguard.ts", "../../../../src/app/pages/business-management/components/entities/tab-notifications/tab-notifications.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-notifications/tab-notifications.routing.ts", "../../../../src/app/pages/business-management/components/entities/tab-notifications/tab-notifications.module.ts", "../../../../src/app/common/typings/label.ts", "../../../../src/app/common/services/labels.service.ts", "../../../../src/app/common/components/bo-labels-group/bo-labels-group.component.ts", "../../../../src/app/common/components/bo-labels-group/bo-labels-group.pipe.ts", "../../../../src/app/common/components/bo-labels-group/bo-labels-group.module.ts", "../../../../src/app/common/components/download-csv/download-csv.component.ts", "../../../../src/app/common/components/download-csv/download-csv.module.ts", "../../../../src/app/common/components/swwizard/wizard-step.directive.ts", "../../../../src/app/common/components/swwizard/wizard.component.ts", "../../../../src/app/common/components/swwizard/wizard.module.ts", "../../../../src/app/common/models/game-group.model.ts", "../../../../src/app/common/services/game-group-filters.service.ts", "../../../../src/app/common/models/proxy.model.ts", "../../../../src/app/common/services/proxy.service.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/dialogs/add-specific-games-alert-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/dialogs/game-force-remove-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/dialogs/game-settings-to-run.component.ts", "../../../../node_modules/@angular/cdk/stepper/index.d.ts", "../../../../node_modules/@angular/material/stepper/index.d.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/games-setup-stepper/games-setup-stepper.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/manage-games.service.ts", "../../../../src/app/pages/games-management/games-managment.constants.ts", "../../../../src/app/pages/lobby/theme.constants.ts", "../../../../src/app/pages/lobby/theme.model.ts", "../../../../src/app/pages/lobby/lobby.model.ts", "../../../../src/app/pages/games-management/games-create/translations/translations.component.ts", "../../../../src/app/common/lib/form.submitted.ts", "../../../../src/app/pages/games-management/games-create/games-create.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/general-games-info/games.schema.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/manage-games.schema.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/manage-games/manage-games-grid/manage-games-grid.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/manage-games/manage-games-preview/manage-games-preview.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/manage-games/setup-game.model.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/manage-games/setup-game-progress.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/manage-games/manage-games-setup/manage-games-setup.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/dialogs/manage-games-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/dialogs/manage-jackpots-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/games-refresh.service.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/entity-games.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/games-setup-stepper/games-setup-step.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/games-setup-stepper/games-setup-stepper.module.ts", "../../../../src/app/common/typings/game-limits.ts", "../../../../src/app/common/services/game-group.service.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/general-games-info/general-games-info.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/general-games-info/general-games-info.module.ts", "../../../../src/app/common/services/jackpot.service.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/jp-games-info/jp-games-info.schema.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/jp-games-info/modals/view-details.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/jp-games-info/jp-games-info.component.ts", "../../../../node_modules/@types/clipboard/index.d.ts", "../../../../src/app/common/components/clipboard/clipboard.directive.ts", "../../../../src/app/common/components/clipboard/clipboard.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/jp-games-info/modals/view-details.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/jp-games-info/jp-games-info.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/manage-games/setup-game-form.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/manage-games/setup-game-info/setup-game-info.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/manage-games/manage-games.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/tab-games.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/tab-games.routing.ts", "../../../../src/app/pages/business-management/components/entities/tab-games/tab-games.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/entity-domain-auth/entity-domain-auth.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/entity-domain-auth/entity-domain-auth.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/entity-ip-whitelist/ip-whitelist.schema.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/entity-ip-whitelist/dialogs/remove-confirm-dialog.component.ts", "../../../../src/app/pages/users/components/activitylog/activitylog.service.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/entity-ip-whitelist/entity-ip-whitelist.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/entity-ip-whitelist/entity-ip-whitelist.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/entity-sites-available/dialogs/edit-site-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/entity-sites-available/dialogs/remove-confirm-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/entity-sites-available/sites.schema.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/entity-sites-available/entity-sites-available.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/entity-sites-available/whitelist-levels.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/entity-sites-available/entity-sites-available.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/entity-twofa/entity-twofa.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/entity-twofa/twofa-email-templates/twofa-email-templates.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/entity-twofa/twofa-sms-templates/twofa-sms-templates.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/entity-twofa/entity-twofa.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/tab-2fa.routing.ts", "../../../../src/app/pages/business-management/components/entities/tab-2fa/tab-2fa.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-domains/entity-domains/entity-domains.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-domains/entity-maintenance/entity-maintenance.component.ts", "../../../../src/app/pages/domains-management/domains-pool/domains-pool.service.ts", "../../../../src/app/pages/business-management/components/entities/tab-domains/manage-domains/allowed-child-domains/entity-allowed-child-domains.component.ts", "../../../../src/app/pages/domains-management/entity-domain.service.ts", "../../../../src/app/pages/business-management/components/entities/tab-domains/manage-domains/select-domain-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-domains/manage-domains/domain-item/domain-item.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-domains/manage-domains/entity-domain-pool.service.ts", "../../../../src/app/pages/business-management/components/entities/tab-domains/manage-domains/select-pool-dialog/select-pool-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-domains/manage-domains/pool-item/pool-item.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-domains/manage-domains/static-tags/entity-static-domain-tags.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-domains/manage-domains/manage-domains.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-domains/manage-merchant-params/select-proxy-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-domains/manage-merchant-params/merchant-params.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-domains/manage-merchant-params/manage-merchant-params.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-domains/tab-domains.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-domains/tab-domains.routing.ts", "../../../../src/app/pages/business-management/components/entities/tab-domains/tab-domains.module.ts", "../../../../src/app/common/models/player.model.ts", "../../../../src/app/pages/business-management/components/entities/tab-players/entity-player-blocklist.service.ts", "../../../../src/app/pages/business-management/components/entities/tab-players/entity-player-blocklist.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-players/tab-players.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-players/tab-players.routing.ts", "../../../../src/app/pages/business-management/components/entities/tab-players/tab-players.module.ts", "../../../../node_modules/ngx-bootstrap/tooltip/tooltip.config.d.ts", "../../../../node_modules/ngx-bootstrap/utils/trigger.class.d.ts", "../../../../node_modules/ngx-bootstrap/utils/triggers.d.ts", "../../../../node_modules/ngx-bootstrap/utils/theme-provider.d.ts", "../../../../node_modules/ngx-bootstrap/utils/linked-list.class.d.ts", "../../../../node_modules/ngx-bootstrap/utils/decorators.d.ts", "../../../../node_modules/ngx-bootstrap/utils/utils.class.d.ts", "../../../../node_modules/ngx-bootstrap/utils/facade/browser.d.ts", "../../../../node_modules/ngx-bootstrap/utils/warn-once.d.ts", "../../../../node_modules/ngx-bootstrap/utils/public_api.d.ts", "../../../../node_modules/ngx-bootstrap/utils/index.d.ts", "../../../../node_modules/ngx-bootstrap/tooltip/tooltip-container.component.d.ts", "../../../../node_modules/ngx-bootstrap/tooltip/tooltip.directive.d.ts", "../../../../node_modules/ngx-bootstrap/tooltip/tooltip.module.d.ts", "../../../../node_modules/ngx-bootstrap/tooltip/public_api.d.ts", "../../../../node_modules/ngx-bootstrap/tooltip/index.d.ts", "../../../../src/app/common/components/mat-currency-rates-setup/mat-currency-rates-setup.component.ts", "../../../../src/app/common/components/mat-currency-rates-setup/currency-rate-item/mat-currency-rate-item.component.ts", "../../../../src/app/common/components/mat-currency-rates-setup/mat-currency-rates-setup.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-engagement/payment-settings/payment-settings.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-engagement/tab-engagement.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-engagement/tab-engagement.routing.ts", "../../../../src/app/pages/business-management/components/entities/tab-engagement/tab-engagement.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-additional/entity-additional-options/entity-additional-options.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-additional/entity-game-logout/entity-game-logout.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-additional/entity-payment-retry/entity-payment-retry.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-additional/tab-additional.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-additional/tab-additional.routing.ts", "../../../../src/app/pages/business-management/components/entities/tab-additional/entity-merchant-params/entity-merchant-params.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-additional/tab-additional.module.ts", "../../../../src/app/common/services/game-limits.service.ts", "../../../../src/app/common/typings/stake-all-range.ts", "../../../../src/app/common/services/stake-ranges.service.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-limits/tab-game-limits.constants.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-limits/forms/clone-limits.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-limits/forms/create-limits.component.ts", "../../../../src/app/common/models/currency.model.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-limits/limits.schema.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-limits/game-limits.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-limits/tab-game-limits.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-limits/tab-game-limits.routing.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-limits/tab-game-limits.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-group/game-group-delete-dialog/game-group-delete-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-group/game-group-dialog/game-group-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-group/schema.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-group/game-group.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-group/tab-game-group.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-group/tab-game-group.routing.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-group/tab-game-group.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-group-filters/game-group-filters-delete-dialog/game-group-filters-delete-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-group-filters/game-group-filters-form.constants.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-group-filters/game-group-filters-dialog/game-group-filters-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-group-filters/schema.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-group-filters/game-group-filters.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-group-filters/tab-game-group-filters.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-group-filters/tab-game-group-filters.routing.ts", "../../../../src/app/pages/business-management/components/entities/tab-game-group-filters/tab-game-group-filters.module.ts", "../../../../src/app/common/typings/rtp-reducer.ts", "../../../../src/app/common/services/rtp-reducer.service.ts", "../../../../src/app/pages/business-management/components/entities/tab-rtp-reducer/rtp-reduce-modal/tagged-items/tagged-items.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-rtp-reducer/rtp-reduce-modal/rtp-reduce-modal.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-rtp-reducer/rtp-reduce-modal/tagged-items/tagged-items.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-rtp-reducer/rtp-reduce-modal/games-form/form-service.model.ts", "../../../../src/app/pages/business-management/components/entities/tab-rtp-reducer/rtp-reduce-modal/games-form/games-form.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-rtp-reducer/rtp-reduce-modal/games-form/games-form.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-rtp-reducer/rtp-reduce-modal/rtp-reduce-modal.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-rtp-reducer/schema.ts", "../../../../src/app/pages/business-management/components/entities/tab-rtp-reducer/tab-rtp-reducer.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-rtp-reducer/tab-rtp-reducer.routing.ts", "../../../../src/app/pages/business-management/components/entities/tab-rtp-reducer/tab-rtp-reducer.module.ts", "../../../../src/app/pages/business-management/components/entities/tab-email-templates/tab-email-templates.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-email-templates/tab-email-templates.routing.ts", "../../../../src/app/pages/business-management/components/entities/tab-email-templates/tab-email-templates.module.ts", "../../../../src/app/common/typings/test-player.ts", "../../../../src/app/common/services/test-players.service.ts", "../../../../src/app/pages/business-management/components/entities/tab-test-players/tab-test-players.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-test-players/tab-test-players.routing.ts", "../../../../src/app/pages/business-management/components/entities/tab-test-players/test-players-dialog/test-players-dialog.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-test-players/schema.ts", "../../../../src/app/pages/business-management/components/entities/tab-test-players/test-players-merchant.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-test-players/test-players.component.ts", "../../../../src/app/pages/business-management/components/entities/tab-test-players/tab-test-players.module.ts", "../../../../src/app/pages/business-management/components/move-entity/move-entity.component.ts", "../../../../src/app/pages/business-management/components/move-entity/move-entity.routing.ts", "../../../../src/app/pages/business-management/components/move-entity/move-entity.module.ts", "../../../../src/app/pages/business-management/components/cascade-games/cascade-games.component.ts", "../../../../src/app/pages/business-management/components/cascade-games/cascade-games.routing.ts", "../../../../src/app/pages/business-management/components/cascade-games/cascade-games.module.ts", "../../../../src/app/pages/business-management/components/entities/entities-routing.module.ts", "../../../../src/app/pages/business-management/components/entities/entity-breadcrumbs/entity-breadcrumbs.model.ts", "../../../../src/app/pages/business-management/components/entities/entity-breadcrumbs/entity-breadcrumbs-item/entity-breadcrumbs-item.component.ts", "../../../../src/app/pages/business-management/components/entities/entity-breadcrumbs/entity-breadcrumbs-item/entity-breadcrumbs-item.module.ts", "../../../../src/app/pages/business-management/components/entities/entity-breadcrumbs/entity-breadcrumbs.component.ts", "../../../../src/app/pages/business-management/components/entities/entity-breadcrumbs/entity-breadcrumbs.module.ts", "../../../../src/app/pages/business-management/components/entities/entities.module.ts", "../../../../node_modules/ng-table-virtual-scroll/lib/fixed-size-table-virtual-scroll-strategy.d.ts", "../../../../node_modules/ng-table-virtual-scroll/lib/table-item-size.directive.d.ts", "../../../../node_modules/ng-table-virtual-scroll/lib/table-virtual-scroll.module.d.ts", "../../../../node_modules/ng-table-virtual-scroll/lib/table-data-source.d.ts", "../../../../node_modules/ng-table-virtual-scroll/public-api.d.ts", "../../../../node_modules/ng-table-virtual-scroll/index.d.ts", "../../../../src/app/common/services/resolvers/dynamic-domains.resolver.ts", "../../../../src/app/common/services/resolvers/dynamic-entitydomain.resolver.ts", "../../../../src/app/common/services/resolvers/static-domains.resolver.ts", "../../../../src/app/common/services/resolvers/static-entity-domain-resolver.service.ts", "../../../../src/app/pages/business-management/components/entity-bulk-actions/action-edit/action-edit.component.ts", "../../../../src/app/pages/business-management/components/entity-bulk-actions/structure.ts", "../../../../src/app/pages/business-management/components/entity-bulk-actions/switch-domain/switch-domain.component.ts", "../../../../src/app/pages/business-management/components/entity-bulk-actions/entity-bulk-actions.component.ts", "../../../../src/app/pages/business-management/components/entity-bulk-actions/entity-bulk-actions.routing.ts", "../../../../src/app/pages/business-management/components/entity-bulk-actions/entity-linker/entity-linker.component.ts", "../../../../src/app/pages/business-management/components/entity-bulk-actions/switch-domain/domains-list/domains-list.component.ts", "../../../../src/app/pages/business-management/components/entity-bulk-actions/entity-bulk-actions.module.ts", "../../../../src/app/common/typings/bi-report-domain.ts", "../../../../src/app/pages/business-management/components/bi-reports-switch/bi-reports-switch.service.ts", "../../../../src/app/pages/business-management/components/bi-reports-switch/bi-reports-switch.component.ts", "../../../../src/app/pages/business-management/components/bi-reports-switch/bi-reports-switch.routing.ts", "../../../../src/app/pages/business-management/components/bi-reports-switch/bi-reports-switch.module.ts", "../../../../src/app/pages/business-management/business-management.routing.ts", "../../../../src/app/common/services/entity-labels.service.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/entity-labels-dialog/entity-labels-dialog.component.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/entity-labels-dialog/entity-labels-dialog.module.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/mat-entity-edit-dialog/mat-entity-edit-dialog.module.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/mat-regional-dialog/mat-regional-item/mat-regional-item.module.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/mat-regional-dialog/mat-regional-dialog.module.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/setup-hint-dialog/setup-hint-dialog.module.ts", "../../../../node_modules/cfb/types/index.d.ts", "../../../../node_modules/ssf/types/index.d.ts", "../../../../node_modules/xlsx/types/index.d.ts", "../../../../src/app/common/services/excel.service.ts", "../../../../src/app/common/typings/flat-report.ts", "../../../../src/app/common/services/flat-reports.service.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/show-limits-modal/show-limits-modal.component.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/show-limits-modal/show-limits-modal.module.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/dialogs/status-confirm/status-confirm.module.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/entity-item/entity-item.component.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/search-by-domain-modal/search-by-domain-modal.module.ts", "../../../../src/app/pages/business-management/components/mat-business-structure/mat-business-structure.module.ts", "../../../../src/app/pages/business-management/business-management.module.ts", "../../../../src/app/pages/comingsoon/comingsoon.component.ts", "../../../../src/app/pages/comingsoon/comingsoon.routing.ts", "../../../../src/app/pages/comingsoon/comingsoon.module.ts", "../../../../src/app/common/models/customer-balance.model.ts", "../../../../src/app/common/services/player.service.ts", "../../../../src/app/common/services/reports/gamehistory.service.ts", "../../../../src/app/common/services/payments.service.ts", "../../../../src/app/pages/payments/components/deposits/deposits.service.ts", "../../../../src/app/pages/payments/components/deposits/schema.ts", "../../../../src/app/pages/payments/components/deposits/deposits.component.ts", "../../../../src/app/pages/payments/components/deposits/deposits.module.ts", "../../../../src/app/pages/payments/components/payment-groups/payment-groups.component.ts", "../../../../src/app/pages/payments/components/payment-groups/index.ts", "../../../../src/app/common/services/transfers.service.ts", "../../../../src/app/common/services/entity-data-source.service.ts", "../../../../src/app/pages/payments/components/transfers/base-payments.model.ts", "../../../../src/app/pages/payments/components/transfers/schema.ts", "../../../../src/app/pages/payments/components/transfers/transfers.component.ts", "../../../../src/app/pages/payments/components/transfers/transfers.module.ts", "../../../../src/app/pages/payments/components/withdrawals/schema.ts", "../../../../src/app/pages/payments/components/withdrawals/withdrawals.service.ts", "../../../../src/app/pages/payments/components/withdrawals/withdrawals.component.ts", "../../../../src/app/pages/payments/components/withdrawals/withdrawals.module.ts", "../../../../src/app/pages/payments/payments.component.ts", "../../../../src/app/pages/payments/components/transfers/index.ts", "../../../../src/app/pages/payments/payments.routing.ts", "../../../../src/app/pages/payments/payments.module.ts", "../../../../node_modules/highcharts/globals.d.ts", "../../../../node_modules/highcharts/highcharts.d.ts", "../../../../src/app/common/components/swhighcharts/highcharts.component.ts", "../../../../src/app/common/services/charts.service.ts", "../../../../src/app/common/components/swhighcharts/highcharts.module.ts", "../../../../src/app/common/components/swhighcharts/helpers.ts", "../../../../src/app/pages/dashboard/chart-schemas/example.chart-schema.ts", "../../../../src/app/pages/dashboard/chart-schemas/payments.chart-schema.ts", "../../../../src/app/pages/dashboard/chart-schemas/average-payments.chart-schema.ts", "../../../../src/app/pages/dashboard/chart-schemas/gcr.chart-schema.ts", "../../../../src/app/pages/dashboard/chart-schemas/index.ts", "../../../../src/app/pages/dashboard/dashboard.component.ts", "../../../../src/app/pages/dashboard/dashboard.routing.ts", "../../../../src/app/pages/dashboard/dashboard.module.ts", "../../../../src/app/common/components/bacard/bacard.component.ts", "../../../../src/app/common/components/bacard/bgmetrics.ts", "../../../../src/app/common/components/bacard/bacardblurhelper.service.ts", "../../../../src/app/common/components/bacard/bacardblur.directive.ts", "../../../../src/app/common/components/bacard/bacard.module.ts", "../../../../src/app/common/services/resolvers/game-groups.resolver.ts", "../../../../src/app/common/services/resolvers/gamesshortinfo.resolver.ts", "../../../../src/app/common/services/resolvers/config.resolver.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/notice/notice-settings.ts", "../../../../src/app/common/typings/responsible-gaming.ts", "../../../../src/app/common/models/responsible-gaming.model.ts", "../../../../src/app/common/services/responsible-gaming.service.ts", "../../../../src/app/pages/business-management/components/entities/resolvers/player-info.resolver.ts", "../../../../node_modules/ngx-clipboard/lib/interface.d.ts", "../../../../node_modules/ngx-clipboard/lib/ngx-clipboard.service.d.ts", "../../../../node_modules/ngx-clipboard/lib/ngx-clipboard.directive.d.ts", "../../../../node_modules/ngx-clipboard/lib/ngx-clipboard.module.d.ts", "../../../../node_modules/ngx-clipboard/lib/ngx-clipboard-if-supported.directive.d.ts", "../../../../node_modules/ngx-clipboard/public_api.d.ts", "../../../../node_modules/ngx-clipboard/ngx-clipboard.d.ts", "../../../../src/app/common/services/reports/gamehistory.spin.service.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/modals/iframe-view-modal/iframe-view-modal.component.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/modals/iframe-view-modal/iframe-view-modal.module.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/base-history/base-history.component.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/modals/round-info-view-modal.component.ts", "../../../../src/app/pages/gamehistory/components/game-history-broken/game-history-broken.service.ts", "../../../../src/app/common/typings/reports/game_history.ts", "../../../../src/app/pages/gamehistory/components/game-history-broken/schema.ts", "../../../../src/app/pages/gamehistory/components/game-history-broken/game-history-broken.component.ts", "../../../../src/app/pages/gamehistory/components/game-history-broken/game-history-broken.module.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/spin-bns/spin-bns.component.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/spin-details/sw_live_roulette_transform/interfaces.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/spin-details/sw_live_roulette_transform/transformers.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/spin-details/sw_live_roulette_transform/transform.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/spin-details/sw-history.plugin.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/spin-details/spin-details.component.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/spin-jackpot/spin-jackpot.component.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/spin-list/spin-list.component.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/spin-list/spin-list-actions/spin-list-actions.component.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/spin-list/spin-list-actions/spin-list-actions.module.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/spin-list/spin-list.module.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/spin-ph-tournament/spin-ph-tournament.component.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/spin-prize-drop/spin-prize-drop.component.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/spin-shared-jackpot-prize/spin-shared-jackpot-prize.component.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/spin-srt-canvas/spin-srt-canvas.component.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/round/missing-translation-pool.handler.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/round/round-info.component.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/round/round-info-module.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/modals/round-info-view-modal.module.ts", "../../../../src/app/pages/gamehistory/components/game-history-general/game-history-general.service.ts", "../../../../src/app/pages/gamehistory/components/game-history-general/schema.ts", "../../../../src/app/pages/gamehistory/components/game-history-general/game-history-general.component.ts", "../../../../src/app/pages/gamehistory/components/game-history-general/game-history-general.module.ts", "../../../../src/app/pages/player/components/customer-page/customer-report/customer-report.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-report/customer-report.module.ts", "../../../../src/app/global.state.ts", "../../../../src/app/pages/player/components/customer-page/customer-view-control/customer-view-control.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-view-control/customer-view-control.module.ts", "../../../../src/app/pages/gamehistory/components/game-history-external/game-history-external.service.ts", "../../../../src/app/pages/player/components/customer-page/customer-game-history/abstract-customer-game-history-service/abstractcustomergamehistoryservice.ts", "../../../../src/app/pages/player/components/customer-page/customer-game-history/customer-external-game-history/customer-external-game-history.service.ts", "../../../../src/app/pages/player/components/customer-page/customer-game-history/customer-external-game-history/schema.ts", "../../../../src/app/pages/player/components/customer-page/customer-game-history/customer-external-game-history/customer-external-game-history.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-game-history/customer-external-game-history/customer-external-game-history.module.ts", "../../../../src/app/pages/player/components/customer-page/customer-game-history/customer-game-history.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-game-history/customer-general-game-history/customer-general-game-history.service.ts", "../../../../src/app/pages/player/components/customer-page/customer-game-history/customer-general-game-history/schema.ts", "../../../../src/app/pages/player/components/customer-page/customer-game-history/customer-general-game-history/customer-general-game-history.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-game-history/customer-general-game-history/customer-general-game-history.module.ts", "../../../../src/app/pages/player/components/customer-page/customer-game-history/customer-unfinished-game-history/customer-unfinished-game-history.service.ts", "../../../../src/app/pages/player/components/customer-page/customer-game-history/customer-unfinished-game-history/schema.ts", "../../../../src/app/pages/player/components/customer-page/customer-game-history/customer-unfinished-game-history/customer-unfinished-game-history.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-game-history/customer-unfinished-game-history/customer-unfinished-game-history.module.ts", "../../../../src/app/pages/player/components/customer-page/customer-game-history/customer-game-history.module.ts", "../../../../src/app/common/components/calendar/calendar.component.ts", "../../../../src/app/common/pipes/leading-zero/leading-zero.pipe.ts", "../../../../node_modules/text-mask-core/dist/textmaskcore.d.ts", "../../../../node_modules/angular2-text-mask/dist/angular2textmask.d.ts", "../../../../node_modules/ng-click-outside/lib_commonjs/click-outside.directive.d.ts", "../../../../node_modules/ng-click-outside/lib_commonjs/click-outside.module.d.ts", "../../../../node_modules/ng-click-outside/lib_commonjs/index.d.ts", "../../../../src/app/common/components/calendar/calendar.module.ts", "../../../../src/app/pages/player/components/customer-page/customer-general/customer-general.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-general/customer-general.module.ts", "../../../../src/app/pages/player/components/customer-page/customer-balance/customer-balance-dialog/customer-balance-dialog.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-balance/customer-balance.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-balance/customer-balance.module.ts", "../../../../src/app/pages/player/components/customer-page/customer-info-widget/customer-info-widget.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-info-widget/customer-info-widget.module.ts", "../../../../src/app/pages/player/components/customer-page/responsible-gaming-status-resolver.service.ts", "../../../../src/app/pages/player/components/customer-page/customer-page.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-payments/customer-payments-deposits/customer-payments-deposits.service.ts", "../../../../src/app/pages/player/components/customer-page/customer-payments/customer-payments-deposits/schema.ts", "../../../../src/app/pages/player/components/customer-page/customer-payments/customer-payments-deposits/customer-payments-deposits.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-payments/customer-payments-deposits/customer-payments-deposits.module.ts", "../../../../src/app/pages/player/components/customer-page/customer-payments/customer-payments-withdrawals/customer-payments-withdrawals.service.ts", "../../../../src/app/pages/player/components/customer-page/customer-payments/customer-payments-withdrawals/schema.ts", "../../../../src/app/pages/player/components/customer-page/customer-payments/customer-payments-withdrawals/customer-payments-withdrawals.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-payments/customer-payments-withdrawals/customer-payments-withdrawals.module.ts", "../../../../src/app/pages/player/components/customer-page/customer-payments/customer-payments.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-payments/customer-payments.module.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/customer-resp-gaming.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/notice/notice.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/notice/notice.module.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/timeframe.model.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/rg-deposit-limit/rg-deposit-limit-dialog/rg-deposit-limit-dialog.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/rg-deposit-limit/rg-deposit-limit-casino/rg-deposit-limit-casino.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/rg-deposit-limit/rg-deposit-limit.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/rg-loss-limit/rg-loss-limit-dialog/rg-loss-limit-dialog.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/rg-loss-limit/rg-loss-limit-casino/rg-loss-limit-casino.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/rg-loss-limit/rg-loss-limit.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/rg-reality-check/rg-reality-check-dialog/rg-reality-check-dialog.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/rg-reality-check/rg-reality-check-casino/rg-reality-check-casino.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/rg-reality-check/rg-reality-check.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/rg-self-exclusion/rg-self-exclusion-dialog/rg-self-exclusion-dialog.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/rg-self-exclusion/rg-self-exclusion-casino/rg-self-exclusion-casino.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/rg-self-exclusion/rg-self-exclusion.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/rg-time-out/rg-time-out-dialog/rg-time-out-dialog.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/rg-time-out/rg-time-out-casino/rg-time-out-casino.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/rg-time-out/rg-time-out.component.ts", "../../../../src/app/pages/player/components/customer-page/customer-resp-gaming/customer-resp-gaming.module.ts", "../../../../src/app/pages/player/components/customer-page/customer-page.module.ts", "../../../../src/app/pages/player/components/list-page/player-list-page.component.ts", "../../../../src/app/pages/player/components/list-page/index.ts", "../../../../src/app/common/models/simple-promo.model.ts", "../../../../src/app/common/services/simple-promo.service.ts", "../../../../src/app/pages/player/components/mat-player-form/player-form.component.ts", "../../../../src/app/pages/player/components/mat-player-form/player-form.module.ts", "../../../../src/app/common/models/promotion.model.ts", "../../../../src/app/pages/player/components/list/dialogs/apply-simple-freebet-dialog.component.ts", "../../../../src/app/pages/player/components/list/dialogs/create-player-dialog.component.ts", "../../../../src/app/pages/player/components/list/dialogs/customer-list-dialogs.module.ts", "../../../../src/app/pages/player/schema.ts", "../../../../src/app/pages/player/components/list/player-list.component.ts", "../../../../src/app/pages/player/components/list/player-list.module.ts", "../../../../src/app/pages/player/player.component.ts", "../../../../src/app/pages/player/player.routing.ts", "../../../../src/app/pages/player/player.module.ts", "../../../../src/app/pages/games-categories-management/game-category.model.ts", "../../../../src/app/common/lib/handle-http-error.ts", "../../../../src/app/common/services/game-categories.service.ts", "../../../../src/app/common/services/lobby-build.service.ts", "../../../../src/app/common/services/lobby.service.ts", "../../../../src/app/pages/lobby/lobbies.component.ts", "../../../../src/app/pages/lobby/lobbies-list/lobbies-list-delete-dialog/lobbies-list-delete-dialog.component.ts", "../../../../src/app/pages/lobby/lobbies-list/lobbies-list-item/lobbies-list-item.component.ts", "../../../../src/app/pages/lobby/lobbies-list/lobbies-list-item/lobbies-list-item.module.ts", "../../../../src/app/pages/lobby/lobbies-list/lobbies-list.component.ts", "../../../../src/app/pages/lobby/lobbies-list/lobbies-list.module.ts", "../../../../src/app/common/services/lobby-widgets.service.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-form.component.ts", "../../../../node_modules/ng2-dragula/drakewithmodels.d.ts", "../../../../node_modules/ng2-dragula/dragulaoptions.d.ts", "../../../../node_modules/ng2-dragula/group.d.ts", "../../../../node_modules/ng2-dragula/drakefactory.d.ts", "../../../../node_modules/ng2-dragula/components/dragula.service.d.ts", "../../../../node_modules/ng2-dragula/components/dragula.directive.d.ts", "../../../../node_modules/ng2-dragula/components/dragula.module.d.ts", "../../../../node_modules/ng2-dragula/eventtypes.d.ts", "../../../../node_modules/ng2-dragula/mockdrake.d.ts", "../../../../node_modules/ng2-dragula/public_api.d.ts", "../../../../node_modules/ng2-dragula/index.d.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-list/lobby-menu-items-list.component.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-list/lobby-menu-items-list.module.ts", "../../../../src/app/common/services/cdn.service.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-ribbons/lobby-menu-items-ribbons-modal/lobby-menu-items-ribbons-modal.component.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-ribbons/lobby-menu-items-ribbons.component.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-ribbons/lobby-menu-items-ribbons.module.ts", "../../../../node_modules/slugify/slugify.d.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-general/lobby-menu-items-general.component.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-general/lobby-menu-items-info/lobby-menu-items-info.component.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-general/lobby-menu-items-general.module.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-options/lobby-menu-items-options.component.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-preview/lobby-menu-item-preview.service.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-preview/lobby-menu-items-preview-form/lobby-menu-items-preview-form.component.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-preview/lobby-menu-items-preview.component.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-setup.component.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-widget/lobby-menu-items-widget.component.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-widgets/placement/placement-position/placement-position.component.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-widgets/placement/placement.component.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-widgets/widget-options-dialog/widget-options-dialog.component.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-widgets/lobby-menu-items-widgets.component.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-widgets/placement/placement-position/placement-position.module.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-widgets/widget-options-dialog/widget-options-dialog.module.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items-setup/lobby-menu-items-setup.module.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items.component.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-menu-items/lobby-menu-items.module.ts", "../../../../src/app/common/components/swconditions/conditions-element.component.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-settings/lobby-settings.component.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-settings/lobby-settings.module.ts", "../../../../src/app/pages/lobby/lobby-form/template-settings/template-settings.component.ts", "../../../../src/app/pages/lobby/lobby-form/template-settings/template-settings.module.ts", "../../../../src/app/pages/lobby/lobby-form/templates-list/templates-list.component.ts", "../../../../src/app/pages/lobby/lobby-form/templates-list/templates-list.module.ts", "../../../../src/app/pages/lobby/lobby-form/lobby-form.module.ts", "../../../../src/app/pages/lobby/lobby-create/lobby-create.component.ts", "../../../../src/app/pages/lobby/lobby-create/lobby-create.module.ts", "../../../../src/app/pages/lobby/lobby-item.resolver.ts", "../../../../src/app/pages/lobby/lobby-edit/lobby-edit.component.ts", "../../../../src/app/pages/lobby/lobby-edit/lobby-edit.module.ts", "../../../../src/app/pages/lobby/lobbies.routing.ts", "../../../../src/app/pages/lobby/lobbies.module.ts", "../../../../src/app/common/services/resolvers/available-game-providers.resolver.ts", "../../../../src/app/common/services/resolvers/merchant-brief.resolver.ts", "../../../../src/app/common/components/editable/editable.component.ts", "../../../../src/app/common/components/editable/editable.module.ts", "../../../../src/app/pages/games-management/games-create/limits/limits.component.ts", "../../../../src/app/pages/games-management/games-create/limits/limits.module.ts", "../../../../src/app/pages/games-management/games-create/translations/translation-info/translation-info.component.ts", "../../../../src/app/pages/games-management/games-create/translations/translations.module.ts", "../../../../src/app/pages/games-management/games-create/images/screenshots/screenshots.component.ts", "../../../../src/app/pages/games-management/games-create/images/images.component.ts", "../../../../src/app/pages/games-management/games-create/images/image/image.component.ts", "../../../../src/app/pages/games-management/games-create/images/image/image.module.ts", "../../../../src/app/pages/games-management/games-create/images/screenshots/screenshots.module.ts", "../../../../src/app/pages/games-management/games-create/images/images.module.ts", "../../../../src/app/pages/games-management/games-create/games-create.module.ts", "../../../../src/app/pages/games-management/schema.ts", "../../../../src/app/pages/games-management/games-list/modals/clone-game/clone-game.component.ts", "../../../../src/app/pages/games-management/games-list/modals/set-jackpot/set-jackpot.component.ts", "../../../../src/app/pages/games-management/games-list/games-list.component.ts", "../../../../src/app/pages/games-management/games-list/games-list.module.ts", "../../../../src/app/pages/games-management/games-management.component.ts", "../../../../src/app/common/services/resolvers/game.resolver.ts", "../../../../src/app/common/services/resolvers/providers.resolver.ts", "../../../../src/app/common/services/schema-definitions.service.ts", "../../../../src/app/common/typings/schema-definition.ts", "../../../../src/app/common/services/resolvers/schema-definitions.resolver.ts", "../../../../src/app/pages/games-management/games-management.routing.ts", "../../../../src/app/pages/games-management/games-management.module.ts", "../../../../src/app/pages/gamehistory/components/game-history-external/modals/round-info-view-modal.component.ts", "../../../../src/app/pages/gamehistory/components/game-history-external/schema.ts", "../../../../src/app/pages/gamehistory/components/game-history-external/game-history-external.component.ts", "../../../../src/app/pages/gamehistory/components/game-history-external/game-history-external.module.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/internal-game-history.component.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/internal-game-history.routing.ts", "../../../../src/app/pages/gamehistory/components/internal-game-history/internal-game-history.module.ts", "../../../../src/app/pages/gamehistory/game-history-section.component.ts", "../../../../src/app/pages/games-categories-management/game-category-details/games-select-manager/game-select-item.model.ts", "../../../../src/app/pages/games-categories-management/game-category-details/game-category-preview/game-category-preview-modal.component.ts", "../../../../src/app/pages/games-categories-management/game-category-details/game-category-details.component.ts", "../../../../src/app/pages/games-categories-management/games-categories-list/games-categories-list.component.ts", "../../../../src/app/pages/games-categories-management/games-categories-management.component.ts", "../../../../src/app/common/services/resolvers/game-labels.resolver.ts", "../../../../src/app/pages/games-categories-management/games-categories-management.routing.ts", "../../../../src/app/pages/games-categories-management/games-categories-list/games-categories-list.module.ts", "../../../../src/app/pages/games-categories-management/game-category-details/applied-games-list/applied-games-list.component.ts", "../../../../src/app/pages/games-categories-management/game-category-details/applied-games-list/applied-games-list.module.ts", "../../../../src/app/pages/games-categories-management/game-category-details/game-categories-translation-manager/game-categories-translation-manager.component.ts", "../../../../src/app/pages/games-categories-management/game-category-details/game-categories-translation-manager/game-categories-info/game-categories-info.component.ts", "../../../../src/app/pages/games-categories-management/game-category-details/game-categories-translation-manager/game-categories-translation-manager.module.ts", "../../../../src/app/pages/games-categories-management/game-category-details/game-category-preview/game-category-preview.module.ts", "../../../../src/app/pages/games-categories-management/game-category-details/game-category-details.module.ts", "../../../../src/app/pages/games-categories-management/games-categories-management.module.ts", "../../../../src/app/common/services/id.service.ts", "../../../../src/app/pages/id-transcode/id-transcode.component.ts", "../../../../src/app/pages/id-transcode/id-transcode.routing.ts", "../../../../src/app/pages/id-transcode/id-transcode.module.ts", "../../../../src/app/pages/gamehistory/game-history-section.routing.ts", "../../../../src/app/pages/gamehistory/game-history-section.module.ts", "../../../../src/app/pages/game-store/game-store-category/game-store-category-item/game-store-category-item.component.ts", "../../../../src/app/pages/game-store/game-store-category/game-store-category-item/game-store-category-item.module.ts", "../../../../src/app/pages/game-store/game-store-category/game-store-category.component.ts", "../../../../src/app/pages/game-store/game-store-category/game-store-category.module.ts", "../../../../src/app/pages/game-store/game-store-category-details/game-store-category-details.component.ts", "../../../../src/app/pages/game-store/game-store-category-details/game-store-category-details.module.ts", "../../../../src/app/pages/game-store/game-store-game-details/game-store-game-details-dialog/game-store-game-details-dialog.component.ts", "../../../../node_modules/ngx-bootstrap/modal/bs-modal-ref.service.d.ts", "../../../../node_modules/ngx-bootstrap/modal/modal-backdrop.options.d.ts", "../../../../node_modules/ngx-bootstrap/modal/models/index.d.ts", "../../../../node_modules/ngx-bootstrap/modal/modal-options.class.d.ts", "../../../../node_modules/ngx-bootstrap/modal/modal-backdrop.component.d.ts", "../../../../node_modules/ngx-bootstrap/modal/bs-modal.service.d.ts", "../../../../node_modules/ngx-bootstrap/modal/modal-container.component.d.ts", "../../../../node_modules/ngx-bootstrap/modal/modal.directive.d.ts", "../../../../node_modules/ngx-bootstrap/focus-trap/boolean-property.d.ts", "../../../../node_modules/ngx-bootstrap/focus-trap/platform.d.ts", "../../../../node_modules/ngx-bootstrap/focus-trap/interactivity-checker.d.ts", "../../../../node_modules/ngx-bootstrap/focus-trap/focus-trap.d.ts", "../../../../node_modules/ngx-bootstrap/focus-trap/focus-trap.module.d.ts", "../../../../node_modules/ngx-bootstrap/focus-trap/public_api.d.ts", "../../../../node_modules/ngx-bootstrap/focus-trap/index.d.ts", "../../../../node_modules/ngx-bootstrap/modal/modal.module.d.ts", "../../../../node_modules/ngx-bootstrap/modal/public_api.d.ts", "../../../../node_modules/ngx-bootstrap/modal/index.d.ts", "../../../../src/app/pages/game-store/game-store-game-details/game-store-game-details.component.ts", "../../../../src/app/pages/game-store/game-store-game-details/game-store-game-details.module.ts", "../../../../src/app/pages/game-store/game-store.component.ts", "../../../../src/app/pages/game-store/game-store.routing.ts", "../../../../src/app/pages/game-store/game-store.module.ts", "../../../../src/app/pages/reports/components/audit-log/audit-log.component.ts", "../../../../src/app/common/services/reports/currency.service.ts", "../../../../src/app/pages/reports/components/player/schema.ts", "../../../../src/app/pages/reports/components/currency/schema.ts", "../../../../src/app/pages/reports/components/currency/currency.component.ts", "../../../../src/app/pages/reports/components/currency/report-currency.module.ts", "../../../../src/app/common/services/reports/financial.service.ts", "../../../../src/app/pages/reports/components/financial/schema.ts", "../../../../src/app/pages/reports/components/financial/financial.component.ts", "../../../../src/app/pages/reports/components/financial/report-financial.module.ts", "../../../../src/app/pages/reports/components/index/index.component.ts", "../../../../src/app/common/services/bi.service.ts", "../../../../src/app/pages/reports/components/kpi/kpi.component.ts", "../../../../src/app/pages/reports/components/kpi/index.ts", "../../../../src/app/common/services/reports/report-player.service.ts", "../../../../src/app/pages/reports/components/player/report-player.component.ts", "../../../../src/app/pages/reports/components/player/report-player.module.ts", "../../../../src/app/common/typings/reports/responsible-gaming.ts", "../../../../src/app/common/services/reports/report-rg.service.ts", "../../../../src/app/pages/reports/components/report-rg/report-rg.model.ts", "../../../../src/app/pages/reports/components/report-rg/schema.ts", "../../../../src/app/pages/reports/components/report-rg/report-rg.component.ts", "../../../../src/app/pages/reports/components/report-rg/report-rg.module.ts", "../../../../src/app/pages/reports/reports.component.ts", "../../../../src/app/pages/reports/components/currency/index.ts", "../../../../src/app/pages/reports/reports.routing.ts", "../../../../src/app/pages/reports/reports-section.module.ts", "../../../../node_modules/@angular/material/grid-list/index.d.ts", "../../../../src/app/common/typings/integration.ts", "../../../../src/app/common/services/integration.service.ts", "../../../../src/app/pages/integrations/integration-test-brand/integration-test-brand.component.ts", "../../../../src/app/pages/integrations/integration-test-brand/integration-test-brand.module.ts", "../../../../src/app/pages/integrations/integration-test-history/integration-test-history.component.ts", "../../../../src/app/pages/integrations/integration-test-history/integration-test-history.module.ts", "../../../../src/app/pages/integrations/integration-test-merchant/integration-test-merchant.component.ts", "../../../../src/app/pages/integrations/integration-test-merchant/integration-test-merchant.module.ts", "../../../../src/app/pages/integrations/integrations.component.ts", "../../../../src/app/pages/integrations/integrations.routing.ts", "../../../../src/app/pages/integrations/integrations.module.ts", "../../../../src/app/pages/cashier/cashier.component.ts", "../../../../src/app/pages/cashier/player-info/player-info.component.ts", "../../../../src/app/pages/cashier/cashier.routing.ts", "../../../../src/app/pages/cashier/cashier.module.ts", "../../../../node_modules/@angular/material/datepicker/index.d.ts", "../../../../src/app/pages/domains-management/confirmation-dialog/confirmation-dialog.component.ts", "../../../../src/app/pages/domains-management/domains/schema.ts", "../../../../src/app/pages/domains-management/domains/dynamic/schema.ts", "../../../../src/app/pages/domains-management/domains/static/schema.ts", "../../../../src/app/common/typings/game-server.ts", "../../../../src/app/pages/game-server/game-server.service.ts", "../../../../src/app/pages/domains-management/domains/domains-item-dialog/domains-item-dialog.component.ts", "../../../../src/app/pages/domains-management/domains/domains.component.ts", "../../../../src/app/pages/domains-management/domains/dynamic/domains-dynamic.component.ts", "../../../../src/app/pages/domains-management/domains-pool/dialog/domains-pool-dialog.component.ts", "../../../../src/app/pages/domains-management/domains-management.component.ts", "../../../../src/app/pages/domains-management/domains/static/domains-static.component.ts", "../../../../src/app/pages/domains-management/domains-pool/schema.ts", "../../../../src/app/pages/domains-management/domains-pool/domains-pool.component.ts", "../../../../src/app/pages/domains-management/domains-management.module.ts", "../../../../src/app/pages/proxy-management/proxy-item-dialog/proxy-item-dialog.component.ts", "../../../../src/app/pages/proxy-management/proxy-confiramtion-dialog/proxy-confiramtion-dialog.component.ts", "../../../../src/app/pages/proxy-management/proxy-management.service.ts", "../../../../src/app/pages/proxy-management/schema.ts", "../../../../src/app/pages/proxy-management/proxy-management.component.ts", "../../../../src/app/pages/proxy-management/proxy-management.module.ts", "../../../../src/app/common/typings/grc-config.ts", "../../../../src/app/common/services/grc.service.ts", "../../../../src/app/pages/grc/grc-errors.constants.ts", "../../../../src/app/pages/grc/components/grc-list/forms/grc-email-form/grc-email-form.component.ts", "../../../../src/app/pages/grc/components/grc-list/forms/grc-email-get-form/grc-email-get-form.component.ts", "../../../../src/app/pages/grc/components/grc-list/grc-email-end/grc-email-end.component.ts", "../../../../src/app/pages/grc/components/grc-list/grc-email-start/grc-email-start.component.ts", "../../../../src/app/pages/grc/components/grc-list/modals/add-grc-modal.component.ts", "../../../../src/app/pages/grc/components/grc-list/modals/edit-definition-modal.component.ts", "../../../../src/app/pages/grc/components/grc-list/modals/grc-email-modal.component.ts", "../../../../src/app/pages/grc/components/grc-list/schema.ts", "../../../../src/app/pages/grc/components/grc-list/grc-list.component.ts", "../../../../src/app/pages/grc/components/grc-list/grc-list.module.ts", "../../../../src/app/pages/grc/grc.component.ts", "../../../../src/app/pages/grc/grc.routing.ts", "../../../../src/app/pages/grc/grc.module.ts", "../../../../src/app/pages/game-server/modals/game-server-modal.component.ts", "../../../../src/app/pages/game-server/schema.ts", "../../../../src/app/pages/game-server/game-server.component.ts", "../../../../src/app/pages/game-server/game-server.routing.ts", "../../../../src/app/pages/game-server/game-server.module.ts", "../../../../src/app/pages/jurisdictions/jurisdictionmodal/jurisdiction-modal.component.ts", "../../../../src/app/pages/jurisdictions/jurisdictionmodal/jurisdiction-modal.module.ts", "../../../../src/app/pages/jurisdictions/schema.ts", "../../../../src/app/pages/jurisdictions/jurisdictions.component.ts", "../../../../src/app/pages/jurisdictions/jurisdictions-routing.module.ts", "../../../../src/app/pages/jurisdictions/jurisdictions.module.ts", "../../../../src/app/pages/labels-management/schema.ts", "../../../../src/app/pages/labels-management/label-create-modal/label-create-modal.component.ts", "../../../../src/app/pages/labels-management/label-create-modal/label-create-modal.module.ts", "../../../../src/app/pages/labels-management/label-group-create-modal/label-group-create-modal.component.ts", "../../../../src/app/pages/labels-management/label-group-create-modal/label-group-create-modal.module.ts", "../../../../src/app/pages/labels-management/labels-management.component.ts", "../../../../src/app/pages/labels-management/labels-management-routing.module.ts", "../../../../src/app/pages/labels-management/labels-management.module.ts", "../../../../src/app/common/services/audits.service.ts", "../../../../src/app/common/services/resolvers/current-user.resolver.ts", "../../../../src/app/pages/users/components/activitylog/modals/view-history.component.ts", "../../../../src/app/pages/users/components/activitylog/activitylog.constants.ts", "../../../../src/app/pages/users/components/activitylog/schema.ts", "../../../../src/app/pages/users/components/activitylog/activitylog.component.ts", "../../../../src/app/pages/users/components/activitylog/modals/view-history.module.ts", "../../../../src/app/pages/users/components/activitylog/activitylog.module.ts", "../../../../src/app/pages/users/schema.ts", "../../../../src/app/pages/users/components/list/users-list.component.ts", "../../../../src/app/pages/users/components/list/user-list.module.ts", "../../../../src/app/pages/users/users.component.ts", "../../../../src/app/pages/users/components/roles/dialogs/confirm-delete-dialog.component.ts", "../../../../src/app/pages/users/components/roles/dialogs/roles-dialog.component.ts", "../../../../src/app/pages/users/components/roles/roles.schema.ts", "../../../../src/app/pages/users/components/roles/roles.component.ts", "../../../../src/app/pages/users/components/roles/roles.routing.ts", "../../../../src/app/pages/users/components/roles/roles.module.ts", "../../../../src/app/pages/users/users.routing.ts", "../../../../src/app/pages/users/users.module.ts", "../../../../src/app/common/services/server-notification.service.ts", "../../../../src/app/pages/server-notifications/server-notifications.component.ts", "../../../../src/app/pages/server-notifications/server-notifications.routing.ts", "../../../../src/app/pages/server-notifications/server-notifications.module.ts", "../../../../src/app/common/typings/limit-level.ts", "../../../../src/app/common/services/limit-levels.service.ts", "../../../../src/app/common/services/new-limits.service.ts", "../../../../src/app/common/services/resolvers/games-without-limits.resolver.ts", "../../../../src/app/pages/limits/limits.component.ts", "../../../../src/app/pages/limits/limits.routing.ts", "../../../../src/app/pages/limits/live-limits/forms/add-level-modal.component.ts", "../../../../src/app/pages/limits/live-limits/forms/clone-live-limits.component.ts", "../../../../src/app/pages/limits/live-limits/forms/edit-live-limits.component.ts", "../../../../src/app/pages/limits/live-limits/forms/rename-level-modal.component.ts", "../../../../src/app/pages/limits/live-limits/live-limits.component.ts", "../../../../src/app/pages/limits/live-limits/live-limits.module.ts", "../../../../src/app/pages/limits/limits.module.ts", "../../../../src/app/pages/pages.routing.ts", "../../../../src/app/pages/pages.module.ts", "../../../../src/app/pages/integrations/integration-test-result/integration-test-result.component.ts", "../../../../src/app/pages/integrations/integration-test-result/integration-test-result.routing.ts", "../../../../src/app/pages/integrations/integration-test-result/integration-test-result.module.ts", "../../../../src/app/gitbook/gitbook.component.ts", "../../../../src/app/gitbook/gitbook.guard.ts", "../../../../src/app/gitbook/gitbook.module.ts", "../../../../src/app/app.routing.ts", "../../../../src/app/common/components/schema/grid/widgets/td/action-list/action-list.widget.ts", "../../../../src/app/common/components/schema/grid/widgets/td/action-list/action-list.widget.module.ts", "../../../../src/app/common/components/schema/grid/widgets/td/game-finish/game-finish.widget.ts", "../../../../src/app/common/components/schema/grid/widgets/td/game-finish/game-finish.widget.module.ts", "../../../../src/app/common/components/schema/grid/widgets/td/radio-button/radio-button.widget.ts", "../../../../src/app/common/components/schema/grid/widgets/td/radio-button/radio-button.widget.module.ts", "../../../../src/app/common/components/schema/grid/widgets/td/issue/issue.widget.component.ts", "../../../../src/app/common/components/schema/grid/widgets/td/issue/issue.widget.module.ts", "../../../../src/app/common/components/schema/grid/widgets/td/broken-game-finish/broken-game-finish.widget.ts", "../../../../src/app/common/components/schema/grid/widgets/td/broken-game-finish/broken-game-finish.widget.module.ts", "../../../../src/app/app.module.ts", "../../../../src/main.ts", "../../../../node_modules/zone.js/lib/zone-impl.d.ts", "../../../../node_modules/zone.js/lib/zone.d.ts", "../../../../node_modules/zone.js/lib/zone.api.extensions.d.ts", "../../../../node_modules/zone.js/lib/zone.configurations.api.d.ts", "../../../../node_modules/zone.js/zone.d.ts", "../../../../src/polyfills.ts", "../../../../src/app/common/components/calendar/calendar.typings.d.ts", "../../../../node_modules/@types/node/globals.d.ts", "../../../../node_modules/@types/node/async_hooks.d.ts", "../../../../node_modules/@types/node/buffer.d.ts", "../../../../node_modules/@types/node/child_process.d.ts", "../../../../node_modules/@types/node/cluster.d.ts", "../../../../node_modules/@types/node/console.d.ts", "../../../../node_modules/@types/node/constants.d.ts", "../../../../node_modules/@types/node/crypto.d.ts", "../../../../node_modules/@types/node/dgram.d.ts", "../../../../node_modules/@types/node/dns.d.ts", "../../../../node_modules/@types/node/domain.d.ts", "../../../../node_modules/@types/node/events.d.ts", "../../../../node_modules/@types/node/fs.d.ts", "../../../../node_modules/@types/node/fs/promises.d.ts", "../../../../node_modules/@types/node/http.d.ts", "../../../../node_modules/@types/node/http2.d.ts", "../../../../node_modules/@types/node/https.d.ts", "../../../../node_modules/@types/node/inspector.d.ts", "../../../../node_modules/@types/node/module.d.ts", "../../../../node_modules/@types/node/net.d.ts", "../../../../node_modules/@types/node/os.d.ts", "../../../../node_modules/@types/node/path.d.ts", "../../../../node_modules/@types/node/perf_hooks.d.ts", "../../../../node_modules/@types/node/process.d.ts", "../../../../node_modules/@types/node/punycode.d.ts", "../../../../node_modules/@types/node/querystring.d.ts", "../../../../node_modules/@types/node/readline.d.ts", "../../../../node_modules/@types/node/repl.d.ts", "../../../../node_modules/@types/node/stream.d.ts", "../../../../node_modules/@types/node/string_decoder.d.ts", "../../../../node_modules/@types/node/timers.d.ts", "../../../../node_modules/@types/node/tls.d.ts", "../../../../node_modules/@types/node/trace_events.d.ts", "../../../../node_modules/@types/node/tty.d.ts", "../../../../node_modules/@types/node/url.d.ts", "../../../../node_modules/@types/node/util.d.ts", "../../../../node_modules/@types/node/v8.d.ts", "../../../../node_modules/@types/node/vm.d.ts", "../../../../node_modules/@types/node/worker_threads.d.ts", "../../../../node_modules/@types/node/zlib.d.ts", "../../../../node_modules/@types/node/ts3.4/base.d.ts", "../../../../node_modules/@types/node/globals.global.d.ts", "../../../../node_modules/@types/node/wasi.d.ts", "../../../../node_modules/@types/node/ts3.6/base.d.ts", "../../../../node_modules/@types/node/assert.d.ts", "../../../../node_modules/@types/node/base.d.ts", "../../../../node_modules/@types/node/index.d.ts"], "fileIdsList": [[109, 118], [109, 118, 119], [109, 179, 183], [103, 109, 177, 178, 179, 180, 181, 182, 183, 184], [103, 109, 198], [177], [109], [109, 162], [109, 182], [103, 109, 195, 196, 197, 198], [103], [103, 109, 114, 162, 172, 179, 182, 183, 184, 195, 204, 205, 206, 207, 208, 323], [103, 109, 162, 182, 195, 205, 206], [177, 179], [103, 109], [103, 109, 182], [103, 109, 114, 162, 172, 204, 205, 206, 207], [103, 109, 114, 162, 172, 182, 195, 204, 205, 206, 207, 208], [109, 172], [109, 204], [103, 109, 162, 182, 195], [103, 109, 162, 182, 195, 205], [103, 109, 162, 169, 177, 179, 180], [103, 109, 162, 182, 195, 196, 205], [103, 109, 181, 195, 197], [103, 109, 111], [103, 109, 113, 115], [103, 109, 111, 112, 113], [39], [37, 38], [37, 38, 39, 103, 104, 105], [37, 38, 39, 103, 104, 105, 106, 107, 108], [37], [103, 109, 163, 164, 165, 169, 173, 174, 175, 176, 185, 186, 187, 206, 209], [109, 163, 169], [109, 163, 164, 169, 173, 174, 175, 841], [109, 163, 164, 165, 173, 174, 175, 185, 189], [109, 163, 164], [109, 163, 164, 165, 169, 185], [103, 109, 163, 164, 169, 170, 173, 174, 175, 185, 227], [109, 163], [103, 109, 163, 164, 165, 169, 170, 171, 173, 174, 175, 176, 185, 186, 187, 188, 189, 190], [103, 109, 163, 164, 165, 169, 170, 173, 174, 175, 185, 189, 190, 206, 209, 227, 235, 323], [103, 109, 163, 185, 209, 323, 324], [103, 109, 163, 164, 185, 206, 209, 323, 324, 325], [109, 163, 164, 192], [109, 169], [103, 109, 163, 164, 185, 323, 426], [103, 109, 169], [109, 164, 183, 228], [109, 165, 169, 192, 227], [103, 109, 163, 164, 165, 169, 183, 192, 227, 228, 229], [109, 163, 164, 171, 192], [109, 164, 165], [103, 109, 116, 127], [103, 109, 116, 127, 163, 164, 165, 166, 167], [103, 109, 163, 164, 165, 169, 170, 173, 183, 192, 227, 228, 229, 230], [109, 164], [109, 163, 164, 165, 169, 173, 174, 175, 176, 183, 192, 193, 194, 199], [103, 109, 163, 164, 173, 174, 175, 185, 206, 209], [109, 164, 175, 176, 186], [103, 109, 185], [103, 109, 165, 228], [103, 109, 163, 164, 165, 169, 170, 173, 174, 175, 176, 183, 185, 186, 187, 189, 192, 199, 206, 209, 227, 228, 229, 235, 252, 297, 298], [109, 163, 164, 165], [109, 165], [109, 163, 164, 165, 347], [109, 163, 164, 165, 169, 173, 174, 175, 185], [109, 164, 174], [109, 173], [103, 109, 164, 169, 170, 185, 186, 187, 199, 206, 209, 227, 228, 229], [103, 109, 163, 164, 165, 169, 170, 173, 174, 175, 176, 183, 185, 186, 187, 192, 199, 206, 209, 227, 228, 229, 252], [103, 109, 163, 164, 185, 192, 206], [103, 109, 163, 164, 165, 173, 174, 175, 185, 189, 209, 235, 323], [103, 109, 300], [103, 109, 163, 164, 300, 301], [103, 109, 163, 164, 165, 166, 169, 170, 173, 174, 175, 185, 323, 869], [103, 109, 163, 164, 165, 169, 192, 199, 227, 228, 298, 300, 301, 337], [103, 109, 163, 164, 165, 173, 174, 185, 323], [103, 109, 163, 164, 185, 192, 206, 209], [103, 109, 163, 164, 185, 192, 206, 209, 297], [103, 109, 163, 164, 199, 201], [109, 114, 117, 120], [109, 114], [109, 114, 116, 117], [103, 109, 114, 127, 128], [103, 109, 114], [137], [133, 134, 135, 136], [109, 116], [103, 116, 133], [116], [149], [109, 146], [103, 109, 146], [103, 109, 141, 142, 143, 144, 145], [109, 141, 142, 143, 144, 145, 146, 147, 148], [103, 109, 169, 185, 191, 232], [109, 386], [462], [109, 114, 221], [122, 123, 126, 130, 131, 132, 139, 140, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 203, 211, 212, 213, 214, 215, 218, 219, 220, 221, 222, 223, 224, 234, 236, 241, 242, 244, 246, 248, 249, 250, 251, 254, 255, 257, 259, 260, 261, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 281, 282, 284, 286, 287, 289, 290, 291, 292, 293, 294, 295, 296, 303, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 318, 319, 320, 321, 322, 327, 328, 329, 330, 331, 333, 334, 335, 336, 349, 351, 352, 353, 354, 355, 356, 357, 375, 376, 386, 387, 389, 390, 391, 392, 394, 395, 396, 397, 398, 403, 404, 407, 408, 409, 410, 412, 413, 414, 415, 416, 428, 429, 430, 432, 434, 436, 437, 438, 439, 441, 452, 453, 454, 455, 456, 460, 461], [103, 109, 123, 125], [109, 127, 129, 130], [103, 109, 215, 218], [103, 109, 132, 216, 217], [103, 109, 129, 130, 132, 140, 155], [109, 114, 132, 138], [109, 139], [130], [103, 152], [103, 109, 129, 152, 153], [123, 139], [103, 109, 114, 116, 139, 140, 150, 212], [103, 109, 123, 126, 129, 130, 132, 140, 150, 151, 154], [109, 140], [109, 169, 185, 191, 225, 226, 231, 233], [109, 114, 169, 226, 231, 234, 235], [109, 129], [109, 114, 129, 168, 200, 235, 265], [109, 124, 169], [109, 114, 168, 235, 249], [109, 114, 223], [103, 109, 169, 185, 191, 225, 226, 231, 233], [109, 114, 168, 169, 226, 231, 235, 242, 243], [238, 239, 240], [109, 169, 237], [109, 114, 150, 238], [109, 238], [124, 245], [109, 169, 185, 191, 210, 231, 233, 394], [109, 114, 150, 169, 191, 210, 231, 235, 392, 403], [109, 169, 185, 191, 210, 231, 233, 246, 393, 394], [109, 114, 150, 169, 191, 210, 231, 235, 392, 393, 395], [109, 169, 245], [109, 114, 169, 254, 389, 391], [109, 210, 256, 261], [124], [109, 114, 150, 191, 210, 235, 262], [109, 124, 169, 185, 191, 233, 245, 247, 248, 256], [109, 114, 150, 168, 169, 191, 231, 232, 235, 255, 257, 263], [109, 124, 169, 185, 191, 210, 231, 233, 247], [124, 245, 246], [109, 114, 169, 194, 210, 231, 235, 248, 250, 254], [109, 259], [109, 169, 259], [103, 109, 169, 247, 258], [109, 169, 247, 259, 355], [109, 127, 169, 259], [103, 109, 169, 259], [169, 259], [109, 114, 150, 168, 169, 231, 232, 235, 239, 253, 259, 316, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 376, 385, 387, 388, 396, 398, 400, 402, 404, 406], [109, 414, 415], [109, 412, 414, 415], [109, 422], [109, 414], [412, 413], [103, 109, 411, 414, 415], [109, 114, 150, 168, 169, 200, 206, 231, 235, 243, 316, 332, 393, 416, 417, 418, 419, 420, 421, 423, 424, 425, 427], [103, 109, 411, 414], [109, 412, 414, 415, 419], [109, 114, 168, 169, 232, 235, 253, 269], [109, 169, 191, 232], [109, 114, 169, 231, 232, 267], [237, 457, 458, 459], [109, 114, 457, 458], [109, 124, 125, 169, 329], [109, 114, 169, 232, 253, 390], [109, 169, 329], [109, 114, 150, 168, 169, 191, 231, 235, 253, 331, 332], [109, 129, 160, 201, 202], [109, 114, 129, 150, 168, 191, 200, 202, 203, 210], [109, 150, 169, 185, 191, 210, 231, 233, 329], [109, 114, 150, 169, 210, 231, 333, 437], [109, 114, 168, 235, 433, 435], [109, 433, 435], [109, 433, 434], [109, 169, 185, 191, 210, 231, 233], [109, 114, 150, 168, 169, 210, 231, 232, 235, 401], [109, 114, 121, 168, 169, 231, 232, 235, 239, 409], [109, 326, 429], [109, 114, 129, 191, 326], [109, 114, 129, 150, 168, 210, 235, 326, 429, 430, 431], [109, 114, 348, 349], [109, 326, 327], [323, 326], [109, 114, 150, 168, 210, 235, 316, 326, 328, 343, 344, 345], [109, 210, 215, 296, 329], [109, 114, 150, 210, 235, 316, 330, 333], [109, 306], [109, 114, 341], [275], [109, 114, 275, 315, 319], [109, 312, 313, 314], [109, 277], [306], [109, 306, 335], [109, 296, 305], [109, 114, 150, 168, 210, 235, 316, 321], [103, 116], [103, 109, 116, 303], [109, 129, 261, 299, 302], [103, 109, 126, 154, 199, 215, 218, 261, 296, 299, 302, 303, 304, 305, 306, 321], [103, 154, 199, 261, 299, 302, 303, 304], [109, 260, 276, 295, 306], [109, 114, 150, 168, 235, 299, 302, 316, 318, 320, 322, 332, 334, 335, 336, 338, 340, 342, 346, 348, 350], [103, 109, 306], [109, 127, 277, 306], [109, 277, 306], [109, 126, 306], [109, 127, 306], [109, 306, 308], [109, 291], [278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 307], [109, 114, 129, 150, 168, 210, 235, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 307, 308, 309, 310, 311, 315, 316, 317], [109, 125, 126, 306], [109, 296, 305, 306], [109, 114, 339], [103, 109, 126, 169, 210, 260, 261, 329, 355], [259], [109, 114, 150, 168, 210, 235, 316, 333, 356, 407], [103, 109, 260], [109, 114, 168, 169, 231, 232, 399], [103, 109, 169, 206, 210, 258], [109, 114, 150, 168, 169, 191, 206, 231, 232, 235, 332, 350, 405], [103, 109, 150, 169, 185, 191, 206, 210, 233, 329], [109, 114, 150, 168, 169, 191, 206, 210, 231, 253, 332, 397], [109, 127, 151, 160], [109, 114, 161, 168, 191, 211], [109, 169, 185, 191, 233], [109, 114, 169, 231, 273], [109, 114, 375], [109, 114, 169, 231, 271], [109, 114, 169, 191, 232, 251, 253], [103, 109, 132, 152, 154, 155, 169, 210], [109, 114, 150, 168, 169, 191, 210, 231, 316, 450], [109, 130, 132, 140, 441], [109, 114, 150, 168, 191, 210, 442], [109, 140, 150], [109, 114, 150, 168, 191, 210, 445], [109, 125, 126, 169], [109, 114, 150, 169, 235, 253, 326, 398, 447], [109, 123, 130, 132, 140, 155], [109, 114, 150, 210, 439, 440, 443, 446, 449, 451], [109, 123, 130, 132, 326], [109, 114, 150, 168, 191, 194, 210, 316, 444, 446, 448], [109, 150, 169, 329, 393, 454], [109, 114, 150, 168, 169, 191, 210, 231, 232, 235, 393, 455], [673, 675, 676, 677, 678, 679, 680, 681, 682, 683, 684, 685], [673, 674, 676, 677, 678, 679, 680, 681, 682, 683, 684, 685], [674, 675, 676, 677, 678, 679, 680, 681, 682, 683, 684, 685], [673, 674, 675, 677, 678, 679, 680, 681, 682, 683, 684, 685], [673, 674, 675, 676, 678, 679, 680, 681, 682, 683, 684, 685], [673, 674, 675, 676, 677, 679, 680, 681, 682, 683, 684, 685], [673, 674, 675, 676, 677, 678, 680, 681, 682, 683, 684, 685], [673, 674, 675, 676, 677, 678, 679, 681, 682, 683, 684, 685], [673, 674, 675, 676, 677, 678, 679, 680, 682, 683, 684, 685], [673, 674, 675, 676, 677, 678, 679, 680, 681, 683, 684, 685], [673, 674, 675, 676, 677, 678, 679, 680, 681, 682, 684, 685], [673, 674, 675, 676, 677, 678, 679, 680, 681, 682, 683, 685], [673, 674, 675, 676, 677, 678, 679, 680, 681, 682, 683, 684], [1633, 1634], [1601, 1602, 1609, 1618], [1593, 1601, 1609], [1625], [1597, 1602, 1610], [1618], [1599, 1601, 1609], [1601], [1601, 1603, 1618, 1624], [1602], [1609, 1618, 1624], [1601, 1602, 1604, 1609, 1618, 1621, 1624], [1601, 1604, 1621, 1624], [1635], [1624], [1599, 1601, 1618], [1591], [1623], [1601, 1618], [1616, 1625, 1627], [1597, 1599, 1609, 1618], [1590, 1591, 1592, 1593, 1594, 1595, 1596, 1597, 1598, 1599, 1600, 1601, 1602, 1603, 1604, 1605, 1606, 1607, 1608, 1609, 1610, 1611, 1612, 1613, 1614, 1615, 1616, 1617, 1618, 1619, 1620, 1621, 1622, 1623, 1624, 1625, 1626, 1627, 1628, 1629], [1630, 1631, 1632], [1609], [1615], [1601, 1618, 1624, 1627], [109, 169, 1211], [1121], [109, 1213], [1213, 1214], [1054], [103, 109, 199, 206], [103, 199, 338], [103, 109, 337, 1050], [109, 1051], [1050, 1051, 1052, 1053], [109, 1290], [109, 1291], [103, 109, 1287, 1288, 1289], [1286, 1287], [1295], [1286, 1287, 1289, 1293], [1286, 1287, 1288, 1289, 1290, 1291, 1292, 1293, 1294], [109, 630, 631], [109, 630, 632], [635], [624, 632, 633, 634], [109, 639, 640], [109, 639], [109, 639, 641], [109, 636, 637, 639, 640], [109, 638, 641, 642, 643], [109, 636, 638], [645], [637, 638, 639, 641, 642, 643, 644], [109, 1410, 1412], [109, 114, 1413], [1415], [109, 1411], [1413, 1414], [109, 636, 1402, 1405, 1406], [1418], [109, 1405, 1407], [109, 1404], [109, 636, 1404, 1405, 1406], [109, 1406, 1408, 1409, 1416], [1402, 1403, 1404, 1405, 1406, 1407, 1408, 1409, 1417], [629], [109, 625], [103, 109, 625], [625, 626, 627, 628], [625], [653], [647, 648, 649, 650, 651, 652], [109, 650], [109, 649], [109, 114, 647, 649, 650, 651], [109, 648, 650], [969], [955, 966, 967, 968], [109, 955, 965], [109, 630, 636, 955], [109, 114, 966, 967], [964], [956, 957, 958, 959, 960, 961, 962, 963], [109, 956], [109, 1149], [109, 1148, 1149], [103, 1148], [1153], [1148, 1149, 1150, 1151, 1152], [384], [109, 377, 378, 379], [109, 378, 379], [109, 114, 378, 380, 381, 382], [109, 377, 380], [377, 378, 379, 380, 381, 383], [476], [109, 465], [109, 464, 466], [464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475], [109, 468, 470], [103, 466], [109, 468], [103, 109, 465, 467], [109, 468, 471], [103, 109, 127, 464, 467, 468, 469], [40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 59, 60, 62, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102], [40, 42, 47], [40, 42], [42, 79], [41, 46], [40, 41, 42, 43, 44, 45], [41, 42, 43, 46, 79], [40, 42, 46, 47], [46], [46, 86], [40, 41, 42, 46], [41, 42, 43, 46], [41, 42], [41], [41, 66], [41, 42, 46, 499], [41, 42, 43, 499], [40, 41, 42, 46, 47], [41, 42, 43], [41, 42, 43, 46, 47, 48], [41, 42, 43, 66], [41, 46, 48], [41, 565], [42, 78], [40, 41, 42, 47], [40, 41, 55], [40, 41, 54], [63], [56, 57], [58], [56], [40, 41, 55, 56], [40, 41, 54, 55, 57], [61], [40, 41, 56, 57], [40, 41, 42, 43, 46], [40, 41], [40, 46], [846], [49, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580], [1081, 1082], [1583], [1584, 1585, 1586], [36, 103, 109, 127, 129, 463, 581, 620, 621], [36, 103, 463, 584, 585, 586, 618, 619], [36, 109, 114, 116, 121, 463, 477, 621, 622, 665, 730, 1108, 1190, 1436, 1570, 1571, 1572, 1573, 1574, 1575, 1576, 1577, 1578, 1579, 1580], [36, 109, 129, 701, 1563, 1566, 1569], [36, 656], [36, 103, 116, 124, 463, 581, 618, 620], [36, 655, 657], [36, 109, 463], [36, 109, 114, 763, 1135, 1137, 1138], [36, 109, 763, 1136, 1137], [36, 103, 109, 1136], [36], [36, 109, 326], [36, 109, 114, 150, 235, 326, 727, 812], [36, 103, 109, 581, 618, 620, 853], [36, 109, 114, 150, 169, 646, 779, 853, 854, 855], [36, 109], [36, 103, 109, 124, 125, 169, 463, 618, 671], [36, 109, 114, 150, 169, 646, 779, 1209, 1210, 1212, 1215], [36, 109, 901], [36, 109, 114, 902], [36, 109, 169, 722], [36, 109, 114, 150, 722, 744], [36, 103, 109, 463, 581, 661], [36, 109, 114, 168, 235, 316, 348, 857], [36, 109, 169], [36, 109, 1339], [36, 103, 109, 463], [36, 109, 114, 150, 168, 235, 431, 758, 776], [36, 103, 109, 116, 129, 150, 463, 581, 618, 665, 666], [36, 109, 114, 667, 669], [36, 103, 109, 169, 581, 660], [36, 109, 114, 150, 168, 169, 231, 235, 253, 316, 745, 779, 970, 971, 972], [36, 103, 109, 150, 326, 463, 581, 620, 661, 665, 726, 730, 732], [36, 109, 114, 150, 169, 231, 232, 235, 253, 317, 326, 348, 431, 463, 731, 732, 745, 779, 812, 813, 814, 815], [36, 103, 109, 463, 581, 620, 726, 728, 730], [36, 103, 109, 169, 326, 463, 581, 585, 605, 620, 661, 665, 722, 725, 726, 727, 731], [36, 109, 114, 150, 168, 210, 235, 1571], [36, 109, 114, 150, 210, 669, 1579], [36, 109, 127, 463, 586], [36, 109, 114, 150, 210, 669, 1573], [36, 109, 114, 150, 1577], [36, 109, 813, 1575], [36, 124, 620, 1123], [36, 109, 1122], [36, 109, 114, 1123, 1124], [36, 109, 860], [36, 109, 859], [36, 109, 114, 859, 860], [36, 109, 114, 779, 784], [36, 109, 827, 828], [36, 103, 109, 581, 829], [36, 109, 114, 827, 828, 829, 830], [36, 618, 663], [36, 109, 668], [36, 109, 832], [36, 109, 114, 778], [36, 103, 169, 191, 463], [36, 103, 116, 150, 463, 581], [36, 585], [36, 618], [36, 169, 618], [36, 600, 601, 659, 660], [36, 597], [36, 589], [36, 1144], [36, 109, 763], [36, 109, 463, 664], [36, 109, 127], [36, 109, 114, 663, 759, 764, 765, 766, 767, 768, 769, 770, 771, 772, 774, 775], [36, 109, 773], [36, 103, 109, 116, 124, 463, 581, 618, 658], [36, 109, 129, 581], [36, 109, 116, 150, 463, 620], [36, 103, 109], [36, 103, 109, 116, 581], [36, 103, 109, 581, 1123], [36, 103, 109, 116, 463, 581, 658], [36, 103, 109, 116, 124, 129, 150, 463, 581, 672, 685], [36, 103, 109, 116, 463, 581, 618, 658, 664], [36, 103, 109, 116, 463, 581, 620, 659], [36, 103, 109, 463, 581, 618, 665], [36, 103, 109, 116, 463, 581, 658, 852], [36, 103, 109, 116, 463, 581, 658, 660], [36, 103, 109, 116, 463, 581, 585, 618, 620, 658, 661, 662, 664], [36, 109, 1083], [36, 103, 109, 116, 463, 581, 658, 1085], [36, 103, 109, 116, 150, 463, 581, 584, 618, 658, 1273, 1274], [36, 103, 109, 116, 463, 581, 620, 862], [36, 103, 109, 116, 463, 581, 658, 862, 893], [36, 103, 109, 116, 463, 581, 658, 893], [36, 103, 109, 116, 463, 581, 618, 619, 658], [36, 103, 109, 116, 303, 463, 581, 618, 658, 664, 686, 715], [36, 109, 116, 124, 463, 581, 618, 658, 847, 1490], [36, 103, 109, 116, 463, 581, 658, 847], [36, 103, 109, 116, 463, 581, 658, 1453], [36, 109, 116, 847], [36, 103, 109, 116, 463, 581, 600, 658], [36, 103, 109, 116, 463, 581, 658, 1549], [36, 103, 109, 116, 581, 876], [36, 103, 109, 116, 129, 259, 581], [36, 103, 109, 116, 124, 129, 463, 581, 584, 620, 876, 1276], [36, 103, 109, 463, 581, 661, 665, 687], [36, 103, 109, 116, 463, 620], [36, 103, 116, 129, 463, 581, 618, 658], [36, 103, 109, 116, 124, 150, 463, 581, 618, 620, 658, 664, 686, 1097], [36, 103, 109, 116, 124, 463, 581, 620, 655, 864], [36, 103, 109, 116, 124, 463, 581, 618, 620, 661, 664, 686], [36, 103, 109, 116, 124, 463, 618, 620, 664, 686], [36, 103, 109, 116, 124, 150, 463, 581, 618, 658, 716], [36, 103, 109, 116, 463, 581, 618, 620, 658, 664, 686], [36, 103, 109, 116, 124, 129, 463, 581, 618, 620, 661, 664, 686, 847], [36, 103, 109, 116, 124, 463, 581, 618, 620, 665, 686, 1274, 1442], [36, 103, 109, 116, 129, 463, 581, 619, 620], [36, 103, 109, 129, 581, 619, 691], [36, 103, 109, 129, 581, 665], [36, 103, 109, 116, 129, 581, 666], [36, 109, 129, 590, 689], [36, 109, 129, 591, 690], [36, 103, 109, 116, 129, 463, 581, 620], [36, 109, 129, 746, 747], [36, 109, 129, 746, 935], [36, 109, 129, 593, 665], [36, 103, 109, 129, 463, 620, 660, 714], [36, 103, 109, 129, 581, 894], [36, 109, 129, 852, 853], [36, 103, 109, 129, 581, 618, 716], [36, 109, 129, 618, 716], [36, 109, 129, 716], [36, 109, 129, 592, 692], [36, 103, 109, 129, 581, 618, 665], [36, 109, 129, 619, 691], [36, 109, 129, 1360, 1361], [36, 109, 129, 661, 665], [36, 109, 129, 581, 661, 665, 719], [36, 103, 109, 116, 124, 169, 463, 581, 620, 658, 722, 1143, 1144, 1145], [36, 103, 109, 116, 463, 581, 605, 620], [36, 103, 109, 116, 463, 581, 620, 1012], [36, 103, 109, 116, 463, 581, 620, 658], [36, 103, 109, 116, 124, 463, 620, 658], [36, 103, 109, 116, 124, 463, 581, 658, 1259], [36, 103, 109, 116, 463, 581, 589, 655, 738], [36, 103, 109, 116, 581, 658, 986], [36, 103, 109, 116, 463, 581, 620, 1028], [36, 103, 109, 116, 124, 463, 581, 618, 620, 658, 664, 686], [36, 103, 109, 463, 581, 620, 661, 726, 730], [36, 103, 109, 116, 124, 463, 581, 618, 620, 658, 661, 686, 729], [36, 109, 169, 721], [36, 589, 600], [36, 589, 597, 862], [36, 614, 615, 616], [36, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 601, 602, 603, 604, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617], [36, 661], [36, 589, 593], [36, 589, 605], [36, 109, 116, 129, 463, 581, 584], [36, 109, 114, 129, 1567, 1568], [36, 103, 109, 620], [36, 109, 712], [36, 109, 114, 646, 654, 689, 690, 692, 713, 755, 757, 1073, 1092], [36, 109, 129, 620, 693, 713, 743, 1049, 1067, 1072], [36, 109, 150, 169, 463, 581, 1068, 1069], [36, 109, 114, 150, 169, 235, 337, 338, 348, 431, 463, 813, 1069, 1070, 1071], [36, 109, 129, 620, 1070], [36, 103, 109, 116, 620, 1068], [36, 103, 109, 129, 150, 463, 581, 618, 716, 732, 740, 847], [36, 109, 114, 150, 235, 463, 749, 752, 777, 812, 1040, 1041], [36, 109, 129, 620, 693, 751, 752, 753, 754, 1040], [36, 109, 114, 150, 168, 235, 326, 797], [36, 109, 129, 463, 620, 693, 750, 751, 752, 753, 754, 756, 757, 811, 826, 851, 911, 930, 948, 954, 977, 984, 996, 1003, 1011, 1024, 1027, 1036, 1039, 1042], [36, 109, 114, 150, 169, 393, 463, 669, 691, 714, 745, 747, 749, 756, 1043, 1048], [36, 103, 109, 129, 169, 463, 581, 1044], [36, 109, 114, 150, 168, 169, 191, 210, 779, 1045], [36, 103, 109, 129, 463, 581, 661, 719, 1044], [36, 109, 114, 150, 168, 191, 210, 235, 316, 1044, 1046, 1047], [36, 109, 129, 618, 1098], [36, 103, 109, 129, 463, 581, 659, 780], [36, 109, 129, 618, 665], [36, 103, 109, 129, 463, 600, 620, 720], [36, 103, 109, 127, 129, 463, 581, 661, 665, 740, 755], [36, 103, 109, 129, 581, 755], [36, 103, 109, 619, 660, 661, 719], [36, 109, 150, 169, 463, 581, 661, 665, 722], [36, 109, 114, 150, 169, 231, 232, 235, 745, 912], [36, 109, 169, 326, 722], [36, 103, 109, 116, 124, 129, 150, 169, 326, 463, 581, 618, 661, 662, 665, 686, 722, 847, 914, 915, 916], [36, 109, 114, 129, 150, 168, 169, 200, 231, 232, 235, 326, 463, 745, 779, 858, 915, 917], [36, 463, 662], [36, 109, 169, 326, 738], [36, 109, 326, 738], [36, 103, 109, 150, 326, 463, 581, 660, 661, 727, 738, 739, 919, 920, 921], [36, 109, 114, 150, 168, 169, 200, 210, 231, 232, 235, 253, 316, 326, 332, 348, 463, 745, 779, 919, 920, 922, 923], [36, 463, 738], [36, 109, 581, 661, 738, 739], [36, 103, 109, 150, 169, 463, 581, 660, 661, 714], [36, 109, 114, 150, 169, 231, 235, 332, 388, 393, 427, 745, 779, 925, 926, 927], [36, 109, 169, 660], [36, 103, 109, 129, 463, 581, 660, 661, 714, 738, 739, 740], [36, 109, 114, 169, 739, 741, 913, 918, 924, 928, 929], [36, 109, 129, 741], [36, 109, 660, 661], [36, 109, 150, 169, 463, 581, 585, 618, 620, 661, 665], [36, 103, 109, 150, 169, 463, 581, 620, 661, 665, 717], [36, 109, 150, 169, 463, 581, 620, 660, 661, 714], [36, 109, 660, 661, 740], [36, 109, 114, 150, 169, 194, 231, 232, 235, 253, 388, 779, 785, 978, 979, 980, 981, 982, 983], [36, 109, 129, 981], [36, 109, 661, 746], [36, 109, 150, 169, 326, 463, 581, 660, 661, 714, 722, 727], [36, 109, 150, 169, 463, 581, 618, 660, 714, 746, 747], [36, 109, 326, 581, 661, 746, 935, 936], [36, 103, 109, 116, 463, 581, 620, 746, 933], [36, 109, 114, 150, 168, 169, 232, 235, 243, 253, 316, 317, 326, 348, 463, 714, 747, 933, 934, 935, 936, 937, 938, 939, 940, 941], [36, 103, 109, 326, 581, 661, 746, 933, 938, 939], [36, 103, 109, 169, 326, 463, 581, 746, 747], [36, 103, 109, 169, 326, 463, 581, 746], [36, 109, 150, 169, 463, 581, 618, 935], [36, 109, 114, 150, 168, 169, 232, 235, 316, 326, 463, 943, 944], [36, 103, 109, 169, 326, 581, 661, 864, 865, 943], [36, 103, 109, 169, 326, 463, 581, 864], [36, 109, 661, 740], [36, 109, 114, 150, 168, 169, 194, 231, 232, 235, 316, 388, 431, 463, 745, 815, 931, 932, 935, 942, 945, 946, 947], [36, 109, 129, 946], [36, 103, 109, 150, 169, 463, 581, 618, 660, 714, 740], [36, 109, 114, 150, 169, 231, 232, 463, 779, 1025, 1026], [36, 109, 129, 1025], [36, 103, 109, 150, 169, 463, 581, 660, 661, 685, 714, 732], [36, 109, 114, 150, 169, 231, 232, 235, 463, 813, 831, 973, 974, 975, 976], [36, 109, 129, 975], [36, 109, 150, 326, 463, 863], [36, 103, 109, 150, 169, 326, 463, 581, 585, 618, 661, 722, 813, 862, 863, 894, 991, 1005], [36, 732], [36, 103, 109, 326, 463, 581, 585, 618, 661, 716, 862, 863, 894, 1004, 1006, 1007], [36, 463, 663, 862], [36, 109, 129, 618, 661, 740], [36, 109, 114, 150, 168, 169, 231, 232, 235, 316, 326, 332, 463, 669, 694, 745, 776, 777, 812, 813, 863, 894, 1004, 1006, 1008, 1009, 1010], [36, 109, 129, 694, 1009], [36, 109, 169, 326, 862], [36, 109, 169, 326, 722, 732, 862], [36, 103, 109, 116, 150, 169, 326, 463, 581, 585, 620, 660, 661, 714, 862, 894, 997, 998, 999], [36, 463, 862], [36, 109, 114, 150, 168, 169, 231, 232, 235, 316, 326, 332, 463, 669, 745, 776, 779, 812, 894, 997, 998, 1000, 1001, 1002], [36, 109, 129, 1001], [36, 103, 109, 169, 326, 581, 585, 618, 722, 732, 894, 988], [36, 103, 109, 169, 326, 463, 581, 585, 618, 722, 732, 862, 894, 986, 988], [36, 103, 109, 116, 150, 326, 463, 581, 585, 618, 660, 661, 690, 716, 727, 862, 893, 894, 985, 986, 987, 989, 990, 991, 992], [36, 463, 893], [36, 109, 129, 618, 660, 661, 740], [36, 109, 114, 150, 168, 169, 231, 232, 235, 299, 326, 427, 431, 463, 694, 779, 812, 815, 894, 985, 987, 989, 990, 993, 994, 995], [36, 109, 129, 694, 994], [36, 109, 326, 618], [36, 109, 326, 618, 661], [36, 109, 169, 326], [36, 103, 109, 326, 581, 618, 661, 716, 862, 863, 866, 871, 872, 882, 883, 886], [36, 109, 463, 620, 661, 716, 889], [36, 103, 109, 581], [36, 109, 191, 870], [36, 109, 869, 870], [36, 109, 114, 869, 870, 871, 891], [36, 463, 618, 620, 716, 879], [36, 103, 109, 124, 150, 326, 463, 581, 618, 661, 690, 691, 692, 716, 727, 740, 852, 853, 862, 867, 868, 880, 887, 889, 894], [36, 109, 114, 150, 168, 235, 316, 463, 777, 858, 895], [36, 103, 109, 124, 326, 463, 581, 618, 661, 716, 889, 897, 898, 899], [36, 109, 114, 463, 858, 897, 900, 904], [36, 463, 618, 620, 880], [36, 109, 150, 169, 326, 463], [36, 109, 114, 150, 169, 235, 326, 431, 812, 899, 903], [36, 463, 618, 620, 879, 880], [36, 103, 109, 618], [36, 103, 109, 169, 463, 581, 618, 661, 716, 881], [36, 103, 109, 618, 872], [36, 109, 618, 661, 884, 885], [36, 109, 114, 150, 169, 231, 232, 235, 463, 745, 779, 785, 856, 882, 883, 885, 886, 906, 907], [36, 109, 169, 618, 661, 722], [36, 109, 618, 620], [36, 109, 884], [36, 109, 463, 661, 740], [36, 109, 114, 150, 168, 169, 231, 232, 235, 316, 326, 332, 348, 393, 463, 694, 777, 783, 812, 856, 858, 861, 863, 865, 866, 867, 868, 872, 887, 888, 890, 892, 896, 905, 908, 909, 910], [36, 109, 129, 909], [36, 103, 109, 169, 463, 581, 664, 836], [36, 109, 114, 150, 168, 169, 210, 231, 232, 235, 316, 463, 779, 837], [36, 103, 109, 169, 581, 722], [36, 109, 114, 150, 168, 169, 231, 232, 235, 316, 745, 779, 839], [36, 103, 109, 124, 169, 581], [36, 109, 114, 150, 168, 169, 232, 235, 332, 463, 842, 843], [36, 103, 109, 150, 169, 463, 581, 661, 665, 836], [36, 129, 847], [36, 103, 109, 150, 326, 581, 661, 665, 727, 740, 836, 847, 848], [36, 109, 114, 150, 168, 169, 194, 231, 232, 235, 316, 393, 431, 463, 745, 831, 833, 835, 838, 840, 844, 845, 848, 849, 850], [36, 109, 129, 848, 849], [36, 103, 109, 169, 463, 581, 661, 665, 950], [36, 103, 109, 116, 463, 581, 658, 949], [36, 109, 114, 150, 169, 231, 232, 235, 463, 745, 779, 951, 952, 953], [36, 109, 129, 952], [36, 109, 150, 326, 463, 581, 618, 661, 665, 689, 788, 801], [36, 103, 109, 150, 326, 463, 581, 618, 660, 661, 714, 740, 788, 801], [36, 103, 109, 169, 326, 338, 463, 581, 618, 660, 665, 718, 788, 797], [36, 103, 109, 150, 169, 326, 338, 463, 581, 618, 620, 661, 665, 690, 786, 790, 797], [36, 109, 338, 463, 581, 659, 661, 780], [36, 103, 109, 169, 326, 338, 463, 581, 600, 661, 720, 740, 793], [36, 103, 109, 150, 169, 326, 338, 463, 581, 618, 620, 661, 665, 795], [36, 109, 150, 326, 463, 581, 618, 660, 661, 689, 714, 788, 801], [36, 103, 109, 129, 169, 463, 581, 600, 618, 620, 659, 660, 661, 665, 714, 740], [36, 109, 114, 150, 168, 169, 231, 235, 253, 316, 338, 388, 463, 694, 720, 776, 777, 779, 780, 781, 782, 783, 787, 789, 791, 794, 796, 798, 799, 800, 802, 803, 804, 805, 806, 807, 808, 809, 810], [36, 109, 129, 694, 781, 783, 799, 800, 808], [36, 103, 109, 169, 581, 618, 1014, 1017], [36, 109, 114, 150, 169, 231, 232, 253, 388, 463, 716, 1016, 1018], [36, 103, 109, 169, 326, 581, 618, 1012, 1014], [36, 109, 114, 150, 168, 169, 231, 232, 235, 299, 302, 326, 338, 463, 745, 812, 813, 870, 1015, 1019], [36, 103, 109, 169, 206, 581], [36, 109, 114, 150, 168, 169, 206, 231, 232, 235, 243, 332, 779, 812, 1014], [36, 463, 1012], [36, 103, 109, 150, 326, 463, 581, 618, 661, 716, 727, 740, 1012, 1013, 1015, 1021], [36, 109, 114, 150, 169, 235, 388, 463, 815, 1013, 1020, 1022, 1023], [36, 109, 129, 1022], [36, 463], [36, 109, 114, 150, 168, 169, 231, 232, 235, 316, 326, 388, 431, 463, 745, 779, 812, 1029, 1030, 1031, 1032, 1034, 1035], [36, 109, 129, 1030], [36, 109, 124, 169, 326, 463, 722, 732, 1028], [36, 103, 109, 150, 326, 463, 581, 660, 661, 727, 1028, 1029, 1032, 1033], [36, 103, 109, 150, 169, 326, 463, 581, 660, 661, 714, 722, 727, 732], [36, 109, 326, 726], [36, 109, 114, 150, 169, 200, 235, 326, 332, 818, 820, 821], [36, 109, 326, 661, 726, 819], [36, 103, 109, 124, 326, 463, 581, 618], [36, 103, 109, 150, 326, 463, 581, 661, 686, 725, 726, 728, 730, 814, 817, 818, 820, 821], [36, 109, 129, 463, 620, 661, 740], [36, 109, 114, 150, 235, 463, 728, 730, 816, 817, 822, 823, 824, 825], [36, 109, 129, 693, 824], [36, 103, 109, 169, 326, 338, 581], [36, 103, 109, 124, 129, 150, 169, 199, 206, 302, 326, 338, 463, 581, 618, 661, 665, 686, 719, 727, 746, 935, 1055, 1060, 1061, 1062], [36, 109, 114, 150, 168, 169, 206, 231, 235, 253, 302, 316, 326, 332, 338, 348, 393, 431, 463, 745, 747, 752, 779, 813, 1055, 1056, 1057, 1058, 1059, 1060, 1062, 1063, 1064, 1065, 1066], [36, 109, 129, 620, 1056, 1057, 1058, 1059, 1063], [36, 618, 746], [36, 103, 109, 169, 338, 581, 746], [36, 109, 326, 746], [36, 109, 463, 661], [36, 109, 114, 463, 748], [36, 103, 109, 124, 581, 618, 661, 665, 686, 718], [36, 103, 109, 150, 169, 225, 326, 463, 581, 585, 661, 852, 853, 1074], [36, 109, 114, 150, 168, 169, 232, 235, 326, 463, 812, 853, 1074, 1075], [36, 103, 109, 169, 463, 581, 618, 665, 718, 719, 722], [36, 103, 109, 150, 326, 463, 581, 618, 661, 664, 665, 718], [36, 109, 114, 150, 169, 231, 232, 235, 253, 326, 348, 733, 745, 779, 785, 786], [36, 109, 326, 332, 338, 618, 661], [36, 109, 114, 150, 169, 231, 232, 235, 326, 332, 338, 779, 788], [36, 109, 326, 332, 338, 618, 661, 665], [36, 109, 114, 150, 169, 231, 232, 235, 326, 332, 338, 779, 790], [36, 103, 109, 169, 463, 581, 585, 600, 660, 661, 665, 717, 720, 722, 724], [36, 103, 109, 129, 326, 660, 714, 717, 718, 723], [36, 109, 114, 129, 150, 169, 231, 232, 235, 326, 332, 388, 431, 463, 720, 723, 724, 779, 812], [36, 600], [36, 109, 326, 332, 338, 600, 661, 792], [36, 109, 114, 150, 169, 231, 232, 235, 326, 332, 338, 779, 793], [36, 109, 326, 332, 338, 618, 661, 665, 718], [36, 109, 114, 150, 169, 231, 232, 235, 326, 332, 338, 779, 795], [36, 103, 109, 150, 169, 326, 463, 581, 600, 618, 661, 664, 665, 689, 690, 692, 720, 732, 733, 734], [36, 109, 114, 150, 169, 235, 326, 393, 735, 787, 1078], [36, 103, 109, 169, 332, 338, 581, 661], [36, 109, 114, 150, 169, 231, 232, 326, 332, 338, 348, 734], [36, 103, 109, 129, 326, 581, 718, 724], [36, 109, 114, 150, 235, 326, 736], [36, 103, 109, 150, 169, 326, 463, 581, 585, 618, 620, 660, 661, 714, 716, 718, 862, 894, 1084, 1085, 1086], [36, 109, 114, 150, 168, 169, 232, 235, 302, 326, 338, 463, 812, 894, 1084, 1086, 1087], [36, 109, 326, 661], [36, 109, 114, 150, 235, 326, 737], [36, 103, 109, 129, 316, 326, 463, 581, 618, 620, 661, 665, 689, 692, 718, 719, 734, 852, 1074, 1075, 1087], [36, 103, 109, 169, 463, 581], [36, 109, 711], [36, 103, 109, 129, 150, 206, 326, 463, 581, 600, 618, 660, 661, 665, 711, 712, 714, 716, 717, 718, 719, 724, 735, 736, 737, 742], [36, 109, 114, 129, 150, 168, 169, 194, 202, 206, 210, 231, 235, 243, 316, 317, 326, 332, 338, 348, 431, 463, 711, 714, 716, 717, 719, 743, 776, 779, 787, 789, 796, 812, 1074, 1076, 1077, 1079, 1080, 1088, 1089, 1090, 1091], [36, 103, 109, 129, 169, 326, 581, 585, 618, 661, 665, 722, 741], [36, 109, 114, 129, 150, 169, 231, 235, 326, 348, 431, 742, 745, 779], [36, 103, 109, 116, 129, 150, 169, 463, 581, 585, 661, 665, 732, 752], [36, 109, 114, 150, 169, 232, 235, 316, 338, 431, 463, 752, 776, 812, 1037, 1038], [36, 109, 129, 620, 693, 752, 1037], [36, 103, 109, 463, 581, 618, 620, 665, 756], [36, 103, 109, 129, 150, 169, 326, 463, 581, 618, 722, 732, 1097, 1098, 1265], [36, 109, 114, 129, 150, 169, 231, 232, 235, 463, 689, 690, 692, 812, 1098, 1464, 1465, 1466], [36, 109, 129, 620, 693, 1464], [36, 109, 124, 169, 463, 618], [36, 109, 1094, 1095], [36, 109, 129, 1094], [36, 1123, 1126], [36, 1127, 1128, 1129, 1130], [36, 109, 463, 1124, 1131], [36, 109, 114, 150, 169, 1125, 1132, 1133], [36, 109, 129, 620, 1132], [36, 103, 109, 150, 326, 393, 463, 581, 746, 747, 933, 1475, 1478], [36, 109, 114, 129, 150, 168, 169, 191, 231, 232, 235, 253, 316, 326, 332, 338, 393, 463, 747, 779, 812, 933, 935, 1468, 1469, 1474, 1475, 1477, 1478, 1479, 1480, 1482], [36, 103, 109, 116, 124, 463, 581, 620, 746], [36, 103, 109, 169, 326, 332, 338, 581, 746, 747], [36, 103, 109, 150, 326, 463, 581, 620, 746, 933, 1469, 1478, 1481], [36, 109, 169, 326, 746, 1472, 1474], [36, 103, 109, 150, 326, 463, 581, 746, 747, 1469, 1475], [36, 109, 463, 746, 747, 1471, 1476], [36, 463, 1470], [36, 109, 463, 746, 747, 1472, 1476], [36, 109, 129, 699], [36, 109, 114, 699, 700], [36, 103, 109, 116, 150, 326, 463, 581, 727, 1473, 1474, 1506, 1507], [36, 109, 150, 169, 235, 326, 463, 745, 812, 815, 1474, 1506, 1508, 1509], [36, 109, 129, 620, 1508], [36, 103, 109, 116, 463, 581, 620, 1473], [36, 109, 169, 326, 722, 1473], [36, 103, 109, 129, 581, 1273, 1275], [36, 109, 114, 129, 150, 431, 463, 1275, 1398, 1399], [36, 103, 109, 581, 618, 1299], [36, 109, 114, 129, 150, 243, 316, 431, 1299, 1395], [36, 109, 618, 1273], [36, 109, 114, 129, 150, 168, 235, 440, 716, 1299, 1396, 1397], [36, 103, 109, 114, 129, 326, 581, 618, 716, 1299, 1345, 1401, 1419], [36, 109, 114, 129, 150, 168, 169, 200, 235, 243, 316, 326, 393, 427, 431, 463, 776, 812, 1299, 1401, 1420], [36, 103, 109, 581, 1273, 1275], [36, 109, 114, 150, 431, 463, 1275, 1398, 1400, 1421, 1422, 1423], [36, 109, 129, 1399, 1420, 1422], [36, 103, 109, 124, 129, 150, 209, 326, 463, 581, 618, 660, 714, 716, 727, 1108, 1156, 1158, 1159, 1160, 1162], [36, 109, 114, 150, 168, 235, 316, 326, 348, 463, 716, 812, 815, 835, 858, 1157, 1160, 1163], [36, 103, 109, 116, 124, 463, 581, 618, 620, 660, 664, 686, 714, 716], [36, 463, 581, 620, 663, 664, 697, 991, 1161], [36, 103, 109, 127, 150, 326, 463, 581, 618, 620, 686, 716, 727, 1099, 1108, 1193, 1365, 1366], [36, 109, 114, 150, 168, 235, 316, 326, 463, 812, 858, 1365, 1367], [36, 103, 109, 116, 124, 463, 581, 618, 620, 664, 686, 716, 1161], [36, 109, 127, 326, 618], [36, 463, 581, 620, 663, 664, 697, 991], [36, 103, 109, 124, 129, 209, 326, 463, 581, 618, 660, 686, 714, 716, 1108, 1156, 1158, 1159, 1161, 1184, 1185], [36, 109, 114, 150, 316, 463, 716, 812, 835, 858, 1157, 1183, 1186], [36, 103, 109, 116, 124, 463, 581, 618, 620, 664, 686, 716, 847, 1161], [36, 109, 114, 150, 168, 431, 834], [36, 463, 620], [36, 103, 109, 129, 150, 463, 581, 585, 620], [36, 109, 114, 150, 169, 431, 463, 669, 714, 1164, 1187, 1369, 1370], [36, 109, 129, 463, 620, 693, 1163, 1186, 1338, 1369], [36, 103, 109, 116, 150, 326, 463, 581, 618, 664, 672, 686, 720, 1099, 1154, 1155], [36, 109, 114, 150, 168, 210, 235, 326, 348, 1156], [36, 103, 109, 326, 463, 581, 618, 672, 720, 1099, 1155, 1156], [36, 109, 114, 150, 168, 235, 326, 431, 812, 1155, 1159, 1182], [36, 150], [36, 109, 114, 150, 169, 231, 235, 326, 348, 716, 776, 812, 903, 1099, 1165, 1170, 1171, 1175, 1176, 1177, 1178, 1179, 1180, 1181], [36, 109, 618, 1155], [36, 109, 150, 463, 618, 620, 664, 1158, 1169], [36, 103, 124, 1168], [36, 1166, 1167], [36, 1166], [36, 109, 1170], [36, 103, 109, 116, 150, 192, 463, 581, 618, 664, 672, 720, 1099, 1154], [36, 109, 114, 150, 168, 210, 235, 903, 1173], [36, 103, 109, 124, 299, 302, 338, 463, 581, 618, 663, 664, 1155, 1158], [36, 109, 114, 150, 168, 169, 210, 231, 232, 235, 299, 302, 326, 338, 776, 779, 812, 903, 1172, 1174], [36, 109, 124], [36, 109, 114, 150, 169, 693, 720, 1141, 1142, 1337, 1338, 1364, 1368, 1371, 1372, 1393], [36, 109, 129, 463, 620, 693, 1142, 1338, 1367, 1371, 1388, 1392], [36, 109, 597], [36, 109, 114, 1381], [36, 103, 109, 169, 732], [36, 103, 109, 169, 463, 581, 618, 876], [36, 109, 114, 150, 168, 169, 231, 232, 235, 463, 776, 779, 812, 1383, 1384], [36, 103, 109, 129, 326, 463, 581, 618, 716, 727, 1273, 1275, 1374], [36, 109, 114, 150, 168, 235, 348, 393, 431, 463, 654, 815, 1275, 1375, 1382, 1385, 1386], [36, 103, 109, 326, 581, 618, 620, 1296, 1299, 1373], [36, 109, 114, 150, 235, 243, 326, 431, 812, 1296, 1299, 1374], [36, 618, 1273], [36, 598, 618], [36, 103, 109, 463, 581, 1108, 1273, 1275, 1296], [36, 109, 114, 168, 1275, 1296, 1376], [36, 103, 109, 129, 150, 169, 463, 581, 661, 1273, 1275, 1375, 1376], [36, 109, 114, 150, 169, 431, 463, 691, 777, 813, 853, 1275, 1359, 1377, 1378, 1379, 1380, 1387], [36, 109, 129, 693, 1359, 1377, 1378], [36, 103, 109, 114, 129, 150, 169, 191, 463, 581, 585, 618, 685, 715, 716, 722, 873, 877, 878], [36, 109, 114, 129, 150, 168, 169, 231, 232, 235, 427, 463, 779, 812, 879, 1340, 1342, 1344, 1350], [36, 109, 169, 873], [36, 109, 114, 150, 169, 231, 232, 463, 779, 1347], [36, 103, 109, 129, 169, 581, 618, 685, 722, 873, 1345], [36, 109, 114, 150, 393, 431, 1346, 1348, 1349], [36, 103, 109, 169, 581, 685, 873], [36, 109, 114, 150, 169, 231, 232, 463, 779, 812, 1345], [36, 109, 129, 169, 463, 585, 873], [36, 109, 114, 150, 169, 231, 232, 463, 779, 812, 1341], [36, 109, 169, 463], [36, 103, 109, 129, 169, 581, 618, 685, 876], [36, 109, 114, 150, 169, 231, 232, 463, 779, 812, 877, 1343], [36, 103, 109, 116, 129, 150, 326, 463, 581, 618, 619, 672, 691, 716, 1352, 1353, 1354], [36, 109, 114, 129, 150, 168, 169, 231, 232, 235, 316, 326, 463, 669, 691, 716, 745, 897, 1353, 1354, 1355], [36, 109, 169, 326, 618, 732, 873], [36, 103, 109, 150, 169, 192, 326, 463, 581, 618, 716, 897], [36, 109, 463, 1351, 1356, 1357, 1363], [36, 109, 129, 463, 620, 689, 691, 781, 879, 1357, 1358, 1359, 1360, 1362], [36, 463, 618, 879], [36, 109, 127, 169, 722, 732, 1490, 1492], [36, 109, 169, 732, 1490, 1492], [36, 109, 150, 463, 581, 1490, 1491], [36, 103, 109, 116, 129, 150, 326, 463, 581, 618, 727, 1490, 1491, 1497, 1498, 1499, 1500], [36, 109, 114, 150, 168, 169, 231, 232, 235, 316, 326, 393, 431, 463, 745, 779, 812, 815, 1216, 1491, 1493, 1494, 1495, 1496, 1497, 1498, 1499, 1501], [36, 103, 109, 124, 169, 247, 326, 463, 581, 585, 618, 722, 732, 991, 1492], [36, 109, 169, 326, 722, 732, 1492], [36, 109, 326, 1490], [36, 463, 664], [36, 109, 690, 694, 1502, 1503, 1504], [36, 109, 129, 620, 694, 1503], [36, 103, 109, 169, 581, 732, 1389], [36, 109, 114, 169, 231, 232, 348, 463, 779, 1389, 1390, 1391], [36, 109, 129, 1390], [36, 109, 114, 129, 150, 168, 812, 1455], [36, 109, 129, 299, 338, 1453], [36, 109, 114, 168, 235, 299, 338, 1457], [36, 103, 109, 116, 150, 169, 326, 463, 581, 661, 665, 716, 727, 1454], [36, 109, 114, 129, 150, 168, 169, 231, 232, 235, 316, 332, 348, 463, 716, 779, 812, 1459], [36, 103, 109, 127, 129, 427, 581, 672, 1453, 1454], [36, 109, 114, 150, 168, 194, 200, 231, 235, 316, 427, 812, 1454, 1564, 1565], [36, 109, 129, 463, 1564], [36, 103, 109, 124, 129, 463, 581, 661, 665, 1108, 1453, 1454], [36, 109, 114, 150, 235, 326, 348, 427, 463, 812, 815, 1452, 1454, 1456, 1458, 1460, 1461, 1462], [36, 109, 129, 620, 693, 1461], [36, 103, 109, 169, 326, 463, 581, 600, 618, 732], [36, 109, 114, 150, 169, 231, 232, 235, 253, 326, 463, 779, 812, 1511], [36, 109, 129, 620, 1514], [36, 109, 150, 326, 463, 581, 600, 618, 689, 720, 727, 1511, 1513], [36, 109, 114, 463, 1512, 1514, 1515], [36, 463, 600], [36, 103, 109, 169, 326, 581, 585, 852, 853, 1517], [36, 109, 114, 150, 169, 231, 232, 326, 463, 812, 853, 1518], [36, 103, 109, 169, 326, 585, 1517], [36, 109, 114, 150, 169, 231, 232, 326, 463, 812, 853, 1520], [36, 109, 129, 620, 1522], [36, 109, 150, 326, 463, 581, 727, 852, 853, 862, 1517, 1518, 1520], [36, 109, 114, 463, 777, 1519, 1521, 1522, 1523], [36, 463, 852], [36, 109, 150, 326, 431, 690, 693, 694, 695, 716, 1360, 1362, 1550, 1551, 1552, 1553, 1554, 1560], [36, 109, 129, 693, 694, 695, 1362, 1552, 1553], [36, 103, 109, 116, 169, 326, 463, 732, 1549, 1550], [36, 103, 109, 116, 150, 169, 326, 463, 581, 585, 618, 732, 862, 894, 1550], [36, 109, 169, 326, 463, 722, 732], [36, 103, 109, 116, 129, 150, 169, 326, 463, 581, 618, 665, 716, 727, 862, 894, 1360, 1549, 1550, 1551, 1555, 1556, 1557, 1558], [36, 109, 114, 150, 168, 169, 231, 232, 235, 316, 326, 332, 463, 693, 745, 779, 812, 813, 815, 894, 1555, 1556, 1557, 1558, 1559], [36, 109, 326, 876], [36, 103, 109, 129, 150, 463, 581, 584, 875, 876, 1277], [36, 109, 114, 129, 150, 168, 210, 235, 316, 317, 431, 1280], [36, 103, 109, 129, 150, 326, 463, 581, 876, 1277, 1279], [36, 109, 114, 129, 150, 235, 326, 348, 431, 463, 812, 1279, 1281, 1282], [36, 103, 109, 129, 463, 581, 1108], [36, 109, 114, 1275, 1276, 1277, 1278, 1284, 1335], [36, 109, 129, 463, 1278, 1283, 1331, 1334], [36, 103, 109, 129, 150, 463, 581, 876, 1277], [36, 109, 114, 129, 150, 235, 463, 1284, 1329, 1330], [36, 109, 129, 150, 463, 581, 876, 1277], [36, 109, 114, 129, 150, 235, 463, 1284, 1329, 1332, 1333], [36, 109, 129, 169, 393, 875, 876, 1277, 1284], [36, 109, 114, 129, 150, 168, 169, 231, 232, 235, 393, 431, 463, 654, 779, 812, 1285, 1321, 1324, 1326, 1328], [36, 103, 109, 463, 581, 876, 1273, 1275, 1284, 1296], [36, 109, 114, 150, 168, 210, 1296, 1297], [36, 109, 169, 326, 722, 732, 876], [36, 103, 109, 169, 326, 581, 727, 876, 1300], [36, 109, 114, 150, 168, 169, 210, 231, 232, 235, 326, 463, 646, 812, 970, 1300, 1301], [36, 103, 109, 169, 581, 876, 1303], [36, 109, 114, 150, 168, 169, 231, 232, 235, 463, 776, 779, 812, 1302, 1304, 1305], [36, 103, 109, 169, 732, 876], [36, 103, 109, 169, 581, 876, 1284], [36, 103, 109, 169, 581, 618, 722, 876, 1299, 1308], [36, 103, 109, 169, 463, 581, 618, 876, 1273, 1275, 1308], [36, 109, 393, 876, 1284, 1310], [36, 109, 114, 150, 168, 169, 210, 231, 232, 235, 253, 316, 348, 388, 393, 463, 812, 1299, 1302, 1306, 1307, 1308, 1309, 1310, 1311, 1312, 1314, 1316, 1317, 1318], [36, 103, 109, 169, 259, 581, 685, 732, 876, 1284], [36, 103, 109, 169, 326, 581, 732, 876, 1277, 1284, 1314, 1315], [36, 109, 169, 253, 722, 732, 876], [36, 109, 114, 168, 169, 231, 232, 235, 253, 463, 1313], [36, 103, 109, 169, 253, 581, 876, 1313], [36, 109, 169, 259, 326, 876, 1284], [36, 109, 114, 150, 169, 231, 232, 235, 326, 463, 1315], [36, 103, 109, 169, 463, 581, 876, 1275, 1284], [36, 109, 114, 431, 812, 1298, 1319, 1320], [36, 103, 109, 169, 259, 463, 581, 685, 716, 875, 876, 1322], [36, 109, 114, 150, 169, 463, 716, 1323], [36, 103, 109, 150, 169, 463, 581, 875, 876], [36, 109, 114, 150, 169, 235, 463, 776, 1325], [36, 109, 875], [36, 109, 114, 150, 168, 235, 316, 431, 1327], [36, 103, 109, 129, 581, 876, 1277], [36, 463, 584, 685, 875], [36, 463, 874], [36, 640], [36, 103, 109, 129, 463, 581, 618, 619, 620, 663, 687, 688, 689, 690, 691, 692, 696, 697], [36, 463, 687], [36, 109, 114, 129, 150, 169, 463, 621, 623, 646, 654, 670, 671, 686, 688, 689, 690, 691, 692, 693, 694, 695, 698, 1562], [36, 109, 129, 463, 693, 694, 695, 697, 698, 701, 710, 1093, 1096, 1120, 1134, 1272, 1336, 1364, 1394, 1424, 1451, 1463, 1467, 1483, 1489, 1505, 1510, 1516, 1524, 1544, 1548, 1561], [36, 109, 463, 618, 1101, 1102], [36, 109, 463, 1103], [36, 109, 116, 124, 129, 618, 1100], [36, 1105], [36, 586], [36, 1111], [36, 152, 463, 581, 620, 664, 991, 1109], [36, 103, 109, 463, 581, 618, 686, 1107, 1108, 1110], [36, 109, 114, 150, 316, 463, 777, 812, 858, 1107, 1111], [36, 109, 463, 618, 1113, 1114], [36, 109, 463, 1115], [36, 109, 114, 150, 463, 646, 654, 1098, 1099, 1104, 1106, 1112, 1116, 1117, 1119], [36, 109, 129, 620, 1103, 1106, 1115, 1118], [36, 109, 169, 326, 618, 664, 732], [36, 103, 109, 129, 150, 326, 463, 581, 618, 664, 1097, 1098, 1219], [36, 109, 114, 150, 168, 169, 231, 232, 235, 433, 463, 669, 745, 776, 812, 1219, 1220], [36, 116, 129, 463, 581, 618, 620], [36, 103, 109, 463, 581, 618, 1193, 1195, 1196], [36, 109, 114, 168, 235, 316, 463, 858, 1197], [36, 109, 116, 129, 618, 1194], [36, 463, 581, 620, 664, 991], [36, 103, 109, 169, 581, 585, 618, 620], [36, 109, 114, 150, 169, 232, 253, 393, 431, 1189, 1192, 1198, 1199, 1203, 1207], [36, 103, 109, 129, 209, 326, 463, 581, 618, 661, 665, 716, 1156, 1159, 1161, 1184, 1200, 1201], [36, 109, 114, 168, 235, 316, 463, 716, 858, 1157, 1202], [36, 103, 109, 129, 150, 209, 326, 463, 581, 618, 661, 665, 716, 727, 1156, 1159, 1160, 1161, 1204, 1205], [36, 109, 114, 150, 168, 210, 235, 316, 463, 669, 716, 815, 858, 1157, 1204, 1206], [36, 103, 109, 125, 129, 150, 169, 463, 581, 585, 618, 689, 722, 862, 1098], [36, 109, 114, 150, 168, 169, 194, 231, 232, 235, 253, 431, 463, 745, 779, 812, 894, 1216, 1217], [36, 103, 109, 124, 129, 150, 169, 463, 581, 618, 664, 722, 1098], [36, 109, 114, 150, 168, 169, 194, 200, 210, 231, 232, 235, 431, 669, 745, 779, 1221, 1222], [36, 109, 129, 463, 618, 1190, 1224], [36, 109, 114, 129, 150, 393, 463, 669, 714, 716, 1098, 1142, 1146, 1147, 1189, 1208, 1218, 1223, 1224, 1225, 1235, 1255], [36, 109, 463, 1226, 1227], [36, 109, 114, 150, 463, 1226, 1228], [36, 103, 109, 116, 618, 658], [36, 463, 620, 664], [36, 109, 463, 1230, 1231], [36, 109, 114, 150, 463, 1230, 1232], [36, 109, 129], [36, 109, 114, 150, 393, 431, 1192, 1229, 1233, 1234], [36, 109, 618], [36, 109, 114, 1104, 1116, 1164, 1187, 1188], [36, 109, 618, 1144, 1146], [36, 109, 114, 150, 169, 231, 232, 235, 253, 393, 431, 722, 745, 812, 813, 1236, 1238, 1240, 1241, 1242, 1243, 1244, 1245, 1246, 1247, 1248, 1249, 1250, 1251, 1252, 1253, 1254], [36, 109, 1143], [36, 109, 114, 150, 168, 235, 1237], [36, 103, 109, 150, 169, 326, 463, 581, 618, 1143, 1144, 1146, 1239, 1240], [36, 103, 109, 150, 169, 326, 463, 581, 618, 1143, 1144, 1146, 1239, 1243], [36, 103, 109, 150, 169, 326, 463, 581, 618, 1144, 1146, 1239, 1246], [36, 103, 109, 150, 169, 326, 463, 581, 618, 1143, 1144, 1146, 1239, 1249], [36, 103, 109, 150, 169, 326, 463, 581, 618, 1143, 1144, 1146, 1239, 1252], [36, 109, 463, 1190], [36, 109, 114, 150, 168, 191, 1191], [36, 109, 129, 581, 660, 714], [36, 1257], [36, 103, 109, 169, 326, 463, 581, 618, 1259, 1260, 1263], [36, 109, 114, 150, 169, 235, 253, 317, 326, 776, 812, 1260, 1262, 1264, 1265], [36, 103, 109, 129, 150, 326, 463, 581, 618, 620, 661, 666, 686, 1098, 1108, 1264, 1265, 1267], [36, 109, 114, 150, 168, 210, 235, 316, 326, 348, 463, 646, 669, 858, 1266, 1268], [36, 103, 109, 124, 169, 618, 689, 690, 692, 722], [36, 109, 114, 150, 168, 169, 231, 232, 235, 253, 463, 689, 690, 692, 722, 745, 812, 1261], [36, 109, 114, 150, 646, 654, 720, 1098, 1139, 1140, 1141, 1256, 1258, 1269, 1270, 1271], [36, 109, 129, 620, 693, 1140, 1141, 1142, 1147, 1224, 1225, 1258, 1270], [36, 152, 463, 581, 620, 664, 697], [36, 109, 326, 864, 1484], [36, 109, 169, 326, 864], [36, 103, 109, 150, 321, 326, 463, 581, 864, 1484, 1485, 1486, 1487], [36, 109, 114, 129, 150, 169, 231, 232, 235, 326, 463, 779, 812, 1484, 1485, 1488], [36, 103, 109, 116, 124, 463, 581, 620, 864], [36, 109, 129, 463, 618, 730], [36, 103, 109, 124, 129, 463, 581, 618, 686, 1108, 1426, 1428], [36, 1429], [36, 109, 114, 150, 316, 463, 777, 812, 835, 1429], [36, 124, 463, 581, 620, 663, 664, 991, 1427], [36, 103, 109, 463, 618, 686, 1108, 1431, 1432], [36, 109, 114, 150, 316, 463, 812, 1433], [36, 463, 581, 618, 620, 664, 991], [36, 1437], [36, 109, 127, 1436], [36, 103, 109, 124, 129, 463, 581, 618, 686, 1108, 1427, 1439], [36, 109, 114, 150, 316, 463, 777, 812, 835, 858, 1440], [36, 124, 463, 581, 620, 663, 664, 697, 991], [36, 103, 109, 129, 463, 581, 618, 661, 665, 686, 1442, 1443, 1445], [36, 109, 114, 150, 316, 463, 812, 1443, 1446], [36, 463, 1444], [36, 109, 114, 150, 463, 693, 716, 1139, 1141, 1338, 1425, 1430, 1434, 1435, 1438, 1441, 1447, 1448, 1450], [36, 109, 129, 620, 693, 1141, 1338, 1433, 1438, 1440, 1446, 1449], [36, 109, 618, 1545], [36, 109, 114, 1545, 1546, 1547], [36, 109, 129, 1546], [36, 702], [36, 704], [36, 706], [36, 109, 703, 705, 707, 708, 709], [36, 109, 129, 620, 703, 705, 707], [36, 103, 109, 326, 463, 581, 618, 686, 916, 1108, 1527, 1529], [36, 109, 114, 150, 168, 235, 316, 326, 463, 812, 858, 1530, 1531], [36, 103, 109, 116, 124, 463, 581, 618, 620, 686], [36, 109, 150, 169, 326, 463, 618], [36, 109, 114, 150, 169, 235, 326, 431, 812, 903, 1527], [36, 463, 1528], [36, 109, 114, 150, 168, 235, 316, 463, 669, 816, 817, 822, 858, 1534], [36, 103, 109, 129, 150, 326, 463, 581, 620, 661, 726, 730, 814, 817, 818, 820, 821, 1108, 1533], [36, 109, 169, 326, 463, 605, 729], [36, 103, 109, 150, 326, 463, 581, 605, 618, 728, 729, 730, 1537, 1538, 1539], [36, 109, 114, 150, 168, 169, 194, 231, 232, 235, 326, 332, 463, 728, 730, 745, 812, 1537, 1538, 1540, 1541], [36, 109, 129, 620, 1540], [36, 463, 726], [36, 605, 606], [36, 109, 114, 150, 169, 463, 728, 730, 903, 1525, 1526, 1532, 1535, 1536, 1543], [36, 109, 129, 620, 693, 1526, 1530, 1534, 1536, 1542], [36, 760, 761, 762], [36, 109, 760], [36, 583], [36, 582], [36, 109, 110, 584, 1581], [36, 1587]], "fileInfos": [{"version": "c430d44666289dae81f30fa7b2edebf186ecc91a2d4c71266ea6ae76388792e1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "080941d9f9ff9307f7e27a83bcd888b7c8270716c39af943532438932ec1d0b9", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fb0f136d372979348d59b3f5020b4cdb81b5504192b1cacff5d1fbba29378aa1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a680117f487a4d2f30ea46f1b4b7f58bef1480456e18ba53ee85c2746eeca012", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "995c005ab91a498455ea8dfb63aa9f83fa2ea793c3d8aa344be4a1678d06d399", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811c71eee4aa0ac5f7adf713323a5c41b0cf6c4e17367a34fbce379e12bbf0a4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9eb0ae9fa333acfc2dcbe6a7eaa2ab8be46b97ca259982c797c39167823235d5", "impliedFormat": 1}, {"version": "55f89a9db249f207b3d06b0e5d0fca7bd86e3fd3c9207b20b2ac5c1b11098d94", "impliedFormat": 99}, {"version": "a63fec4413775da6f6a2d7cb4887817ecd468d09ad601885764621fccd925435", "affectsGlobalScope": true, "impliedFormat": 99}, {"version": "27f3877a687b4a1daab6f1d44dc22e8dc7cc56c7a17f81e8270cff284bda11e1", "affectsGlobalScope": true, "impliedFormat": 99}, {"version": "6cb35d83d21a7e72bd00398c93302749bcd38349d0cc5e76ff3a90c6d1498a4d", "impliedFormat": 1}, {"version": "051451ceae7f29c8f17b810e6a6d8d270c67b401866f487cdc50c5c1a8b3f511", "impliedFormat": 1}, {"version": "2632057d8b983ee33295566088c080384d7d69a492bc60b008d6a6dfd3508d6b", "impliedFormat": 1}, {"version": "4bf71cf2a94492fc71e97800bdf2bcb0a9a0fa5fce921c8fe42c67060780cbfa", "impliedFormat": 1}, {"version": "0996ff06f64cb05b6dac158a6ada2e16f8c2ccd20f9ff6f3c3e871f1ba5fb6d9", "impliedFormat": 1}, {"version": "5c492d01a19fea5ebfff9d27e786bc533e5078909521ca17ae41236f16f9686a", "impliedFormat": 1}, {"version": "a6ee930b81c65ec79aca49025b797817dde6f2d2e9b0e0106f0844e18e2cc819", "impliedFormat": 1}, {"version": "84fce15473e993e6b656db9dd3c9196b80f545647458e6621675e840fd700d29", "impliedFormat": 1}, {"version": "7d5336ee766aa72dffb1cc2a515f61d18a4fb61b7a2757cbccfb7b286b783dfb", "impliedFormat": 1}, {"version": "63e96248ab63f6e7a86e31aa3e654ed6de1c3f99e3b668e04800df05874e8b77", "impliedFormat": 1}, {"version": "ed164267a8b206892d69768f51e6e7157ad0a6e89745fbd39f3e81c4700e9a9e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "06a20cc7d937074863861ea1159ac783ff97b13952b4b5d1811c7d8ab5c94776", "impliedFormat": 1}, {"version": "ab6de4af0e293eae73b67dad251af097d7bcc0b8b62de84e3674e831514cb056", "impliedFormat": 1}, {"version": "18cbd79079af97af66c9c07c61b481fce14a4e7282eca078c474b40c970ba1d0", "impliedFormat": 1}, {"version": "e7b45405689d87e745a217b648d3646fb47a6aaba9c8d775204de90c7ea9ff35", "impliedFormat": 1}, {"version": "669b754ec246dd7471e19b655b73bda6c2ca5bb7ccb1a4dff44a9ae45b6a716a", "impliedFormat": 1}, {"version": "bcfaca4a8ff50f57fd36df91fba5d34056883f213baff7192cbfc4d3805d2084", "impliedFormat": 1}, {"version": "76a564b360b267502219a89514953058494713ee0923a63b2024e542c18b40e5", "impliedFormat": 1}, {"version": "8f62cbd3afbd6a07bb8c934294b6bfbe437021b89e53a4da7de2648ecfc7af25", "impliedFormat": 1}, {"version": "a20629551ed7923f35f7556c4c15d0c8b2ebe7afaa68ceaab079a1707ba64be2", "impliedFormat": 1}, {"version": "d6de66600c97cd499526ddecea6e12166ab1c0e8d9bf36fb2339fd39c8b3372a", "impliedFormat": 1}, {"version": "8e7a5b8f867b99cc8763c0b024068fb58e09f7da2c4810c12833e1ca6eb11c4f", "impliedFormat": 1}, {"version": "a8932876de2e3138a5a27f9426b225a4d27f0ba0a1e2764ba20930b4c3faf4b9", "impliedFormat": 1}, {"version": "df877050b04c29b9f8409aa10278d586825f511f0841d1ec41b6554f8362092b", "impliedFormat": 1}, {"version": "027d600e00c5f5e1816c207854285d736f2f5fa28276e2829db746d5d6811ba1", "impliedFormat": 1}, {"version": "5443113a16ef378446e08d6500bb48b35de582426459abdb5c9704f5c7d327d9", "impliedFormat": 1}, {"version": "0fb581ecb53304a3c95bb930160b4fa610537470cce850371cbaad5a458ca0d9", "impliedFormat": 1}, {"version": "7da4e290c009d7967343a7f8c3f145a3d2c157c62483362183ba9f637a536489", "impliedFormat": 1}, {"version": "eb21ddc3a8136a12e69176531197def71dc28ffaf357b74d4bf83407bd845991", "impliedFormat": 1}, {"version": "914560d0c4c6aa947cfe7489fe970c94ba25383c414bbe0168b44fd20dbf0df4", "impliedFormat": 1}, {"version": "4fb3405055b54566dea2135845c3a776339e7e170d692401d97fd41ad9a20e5d", "impliedFormat": 1}, {"version": "8d607832a6ef0eac30657173441367dd76c96bf7800d77193428b922e060c3af", "impliedFormat": 1}, {"version": "20ff7207f0bb5cdde5fee8e83315ade7e5b8100cfa2087d20d39069a3d7d06f4", "impliedFormat": 1}, {"version": "7ca4c534eab7cff43d81327e369a23464bc37ef38ce5337ceff24a42c6c84eb2", "impliedFormat": 1}, {"version": "5252dec18a34078398be4e321dee884dc7f47930e5225262543a799b591b36d2", "impliedFormat": 1}, {"version": "23caed4dff98bd28157d2b798b43f1dfefe727f18641648c01ce4e0e929a1630", "impliedFormat": 1}, {"version": "f67e013d5374826596d7c23dbae1cdb14375a27cd72e16c5fb46a4b445059329", "impliedFormat": 1}, {"version": "ea3401b70e2302683bbf4c18b69ef2292b60f4d8f8e6d920413b81fb7bde0f65", "impliedFormat": 1}, {"version": "71afe26642c0fb86b9f8b1af4af5deb5181b43b6542a3ff2314871b53d04c749", "impliedFormat": 1}, {"version": "0d7f01634e6234d84cf0106508efdb8ae00e5ed126eff9606d37b031ac1de654", "impliedFormat": 1}, {"version": "f8d209086bad78af6bd7fef063c1ed449c815e6f8d36058115f222d9f788b848", "impliedFormat": 1}, {"version": "3ad003278d569d1953779e2f838f7798f02e793f6a1eceac8e0065f1a202669b", "impliedFormat": 1}, {"version": "fb2c5eceffcd918dbb86332afa0199f5e7b6cf6ee42809e930a827b28ef25afe", "impliedFormat": 1}, {"version": "f664aaff6a981eeca68f1ff2d9fd21b6664f47bf45f3ae19874df5a6683a8d8a", "impliedFormat": 1}, {"version": "ce066f85d73e09e9adbd0049bcf6471c7eefbfc2ec4b5692b5bcef1e36babd2a", "impliedFormat": 1}, {"version": "09d302513cacfbcc54b67088739bd8ac1c3c57917f83f510b2d1adcb99fd7d2a", "impliedFormat": 1}, {"version": "3faa54e978b92a6f726440c13fe3ab35993dc74d697c7709681dc1764a25219f", "impliedFormat": 1}, {"version": "2bd0489e968925eb0c4c0fb12ef090be5165c86bd088e1e803102c38d4a717d8", "impliedFormat": 1}, {"version": "88924207132b9ba339c1adb1ed3ea07e47b3149ff8a2e21a3ea1f91cee68589d", "impliedFormat": 1}, {"version": "b8800b93d8ab532f8915be73f8195b9d4ef06376d8a82e8cdc17c400553172d6", "impliedFormat": 1}, {"version": "d7d469703b78beba76d511957f8c8b534c3bbb02bea7ab4705c65ef573532fb8", "impliedFormat": 1}, {"version": "74c8c3057669c03264263d911d0f82e876cef50b05be21c54fef23c900de0420", "impliedFormat": 1}, {"version": "b303eda2ff2d582a9c3c5ecb708fb57355cdc25e8c8197a9f66d4d1bf09fda19", "impliedFormat": 1}, {"version": "4e5dc89fa22ff43da3dee1db97d5add0591ebaff9e4adef6c8b6f0b41f0f60f0", "impliedFormat": 1}, {"version": "ec4e82cb42a902fe83dc13153c7a260bee95684541f8d7ef26cb0629a2f4ca31", "impliedFormat": 1}, {"version": "5f36e24cd92b0ff3e2a243685a8a780c9413941c36739f04b428cc4e15de629d", "impliedFormat": 1}, {"version": "40a26494e6ab10a91851791169582ab77fed4fbd799518968177e7eefe08c7a9", "impliedFormat": 1}, {"version": "208e125b45bc561765a74f6f1019d88e44e94678769824cf93726e1bac457961", "impliedFormat": 1}, {"version": "b3985971de086ef3aa698ef19009a53527b72e65851b782dc188ac341a1e1390", "impliedFormat": 1}, {"version": "c81d421aabb6113cd98b9d4f11e9a03273b363b841f294b457f37c15d513151d", "impliedFormat": 1}, {"version": "30063e3a184ff31254bbafa782c78a2d6636943dfe59e1a34f451827fd7a68dc", "impliedFormat": 1}, {"version": "c05d4cae0bceed02c9d013360d3e65658297acb1b7a90252fe366f2bf4f9ccc9", "impliedFormat": 1}, {"version": "6f14b92848889abba03a474e0750f7350cc91fc190c107408ca48679a03975ae", "impliedFormat": 1}, {"version": "a588d0765b1d18bf00a498b75a83e095aef75a9300b6c1e91cbf39e408f2fe2f", "impliedFormat": 1}, {"version": "d479f8d992ddbd3e9b3a2cf2842e2d32ab94602a47a5748373e9007213ff6ac9", "impliedFormat": 99}, {"version": "675bf15f8ed9b0234064aa40a4ef91bf0006315accd500f0574b73a5e1c8bfa9", "impliedFormat": 99}, {"version": "5f6323c6dc8dd4f0391fd3b5335798ee0ad0021b1a16c83f91986d9b02d2eecb", "impliedFormat": 99}, {"version": "4f1e0e9277e01c7c20f6ca04e8f426c84b6099c21cbcae5eb13246b620e06ffd", "impliedFormat": 99}, {"version": "4d23cd689feba8a8dcb86b6fbf0cf4891c4df46aa047cac33f1feb032211fe38", "impliedFormat": 99}, {"version": "c71d0c914567374b0bec6bf82c44af03ceb3337f393be8b89cf6c5278e13ce13", "impliedFormat": 99}, {"version": "ae203240a83a1bfbdb05955a78328a4da6ad9d8826736cdfb25099f3715ba163", "impliedFormat": 99}, {"version": "f0e8974562a67e07cb58e790d73389013abaafcab0c2c9b3cb0814d86b6c3fde", "impliedFormat": 99}, {"version": "a7c0e1ff8d062a0c2c370c45b74ddfef09b59d3335b0a6c60e80e63ee55107b0", "impliedFormat": 99}, {"version": "4bd0b0c072c7e0c7c1327306ac9905c2e35899f9552e28ebbce49658c4204d19", "impliedFormat": 99}, {"version": "c61ebefc7d015cc4f19aa46a8aeb9ff9a37c18059c10381420a4a822ec8d6f3e", "impliedFormat": 99}, {"version": "d1827f88aa8d5a263ea0695d0b196e3fb279f350c32524bfd1aca6ec9b2f40dd", "impliedFormat": 99}, {"version": "30bc7114b52fc8c8cd1dfc2969de3ab959398db80561a099b9f1f013a7093309", "impliedFormat": 99}, {"version": "74b1a1048fbbf5b62d9a7f7975e28a39aec25594b21c309a3619580577c5f430", "impliedFormat": 99}, {"version": "1bb1a8e3aae8c1d13c2c7e32d94586af592b75d473330f47fa341b23b3d9c13f", "impliedFormat": 99}, {"version": "3315b681fb70c7dd6ce182ccbfc13955b10fe80f05b8f0c2e5241552776cd620", "impliedFormat": 99}, {"version": "545aa4bcfeb4e061cd36743d2613b1a3ae15a374fcd6c395a39b5f566b399984", "impliedFormat": 99}, {"version": "3b7db1f21c1a234295b6dd6fcfcb2ca01a5955ae383b6bced8da450901e2c7a4", "impliedFormat": 99}, {"version": "a0b7ac37153af7828feb6e49d4c6012c43a5ad2e82c2668b5c55156b5e928429", "impliedFormat": 1}, {"version": "0d8bf2e340b8a2ea900a0a11e770dbe5129f4f52761f63e9640ce92219e3c151", "impliedFormat": 1}, {"version": "f97edc4d3b4dd0b32766e9633086497565c37657fb591cc84a35864db4bc2fc5", "impliedFormat": 1}, {"version": "3ff27a264325767dd546133238d968f649d1376dc119e790fcd1a25661685de7", "impliedFormat": 1}, {"version": "31e238846304d1f07e5600fc80b01150d5dd695fd0285d1c3f2fb0958eb0d7e8", "impliedFormat": 1}, {"version": "29e4dd3ea6b1d8994c7d89b33c8577c5fe7c80019e1c889bb3f4c86943ce006f", "impliedFormat": 99}, {"version": "8e313ad0f321ed52fa0ec00f682c0a118d13682dc19f2cd61cb38b23a1c10964", "impliedFormat": 99}, {"version": "10fc7e431aca7ed1875d9e3c3a3e26e715ae1558ce4c8f9ac383d23d2a705994", "impliedFormat": 99}, {"version": "7d7891bc54d7d5728ee691925f96a884d0c327b0c38b762667c1dee8259c2d40", "impliedFormat": 1}, {"version": "164576d1d1a72dc859fe523b538f90b499e52737ce85b07b4ea67f1cf6b63ed1", "impliedFormat": 1}, {"version": "a28bfbe9d6dfeedf9ef19bba46b83e390c6c438f0f3ac017bbe3a6d12b337ebb", "impliedFormat": 1}, {"version": "aa086e5ed0124f9b8cddaedd00aef1c9abc564c5eacd3bacbf6df4313fb03f37", "impliedFormat": 1}, {"version": "4d07a1ac15874375c57d34d7e1b11709524a1a59053669028802affea1a27831", "impliedFormat": 1}, {"version": "732dd035f822065015fff23f8ddeeca02315c3f248588ef6fe9403c28d88e1ea", "impliedFormat": 1}, {"version": "1f798e4f20cc0ee2d5a9cdad207af0bf1a0a852bcaaa1febf51a2fe73085358b", "impliedFormat": 1}, {"version": "b9ed96fa75537ed0c1becf6f54a61a2972d8e31068f891b3586a200dfee4db34", "impliedFormat": 1}, {"version": "8ced3740a6cc03f9ff0c74d77b743747c6fe8554d5c08bc5d4ef6ea8c672b6cf", "impliedFormat": 1}, {"version": "96814c03b6cebc9e8d66156979edbe3b1dc387ab1ab2227bcef66712fe9930a5", "impliedFormat": 1}, {"version": "3fbb31eceaa17a683f37c4c89626a40ca6ff440a33bae505f1b80a27723e5825", "impliedFormat": 1}, {"version": "f1205a12015cf4cb5cdb028f4d95b2631a04adc777bb6ff4e0bb0cc17382a812", "impliedFormat": 1}, {"version": "df0d512cad2c566970d2297166904a484c6624ae6ff19838d64bd17370bf9618", "impliedFormat": 1}, {"version": "d93acca1dd0bb3525f6cf27c6f685885a58aa13df0f47b49142bd68dbb965d68", "impliedFormat": 1}, {"version": "808312fe55ac729346232cd9a24e7fa9d89212522a0776e410316655e111a2e1", "impliedFormat": 1}, {"version": "95ea59beb6eb564aa72608c33371f09292435dcb45e4ab434ebbf5692c5c2516", "impliedFormat": 1}, {"version": "dc58e600e4e2e6ca4afe4d8a3157f31c1fdc7345db2b9e0e6642bf7cf0885c89", "impliedFormat": 1}, {"version": "26da0bd9e55d9fe08facb47268dcb1b203d53d43c6d1ca3ad720a0c8a260210a", "impliedFormat": 1}, {"version": "ec05471256a3f74dec3838edb4fa648770741a11f6dc6f7df8ac09c6a00fea5a", "impliedFormat": 1}, {"version": "e5bb77e6403e68e84bd022cfaf593d6e62cb41b4cbf91b86df027af01240da57", "impliedFormat": 1}, {"version": "21523fd594d69096db00907bf5755c4408f4773c7c2ea3a6ed76becf7784c3ad", "impliedFormat": 1}, {"version": "5cc75781da20ae8711c4b756643f3a4fae25710eadf9ac7849dacd5a4d4e2749", "impliedFormat": 1}, {"version": "c25279f94b450c63c181e5cd30b5a69ead88f1bfc1d686b54a893dc13118b55b", "impliedFormat": 1}, {"version": "3330060c22d4960b05a3a07afd047f023153eddb131e3d4ba638c6c88e4e4606", "impliedFormat": 1}, {"version": "fc82d378b7dbdc09d0103c20d003f1a6e23ddb7ba82de11ea464bea8a9097a28", "impliedFormat": 1}, {"version": "2fad99f8c1c7087e2367aa676840114d40b05f67c8963c619c42dbbd81fe80b0", "impliedFormat": 1}, {"version": "7f8f054573dce4200dca23ece3dcc9562f6a2fc8de80023c403c9fc467dc98da", "impliedFormat": 1}, {"version": "39806bde0b89fda8a810ff5c035a9e780fbe4112aeb286dbdc608035262d1d74", "impliedFormat": 1}, {"version": "add573793fa1c87cc6dbcc945bf3cd43f79a474fc6a84afcc26c5f4c213a6c93", "impliedFormat": 1}, {"version": "82bee0f1fa9b99a824dd1c5d28312b455e2ccd0329f52c82e2d941c2a1b2001e", "impliedFormat": 1}, {"version": "be3aafeae697735fead6d389433c48c5fee60696ab6909216142c75237082544", "impliedFormat": 1}, {"version": "b23b706ad3eb1a88aec0e2c4cbc2feb3d06fd608709b3e41db305422153f10f0", "impliedFormat": 1}, {"version": "3a9323fd680c0d3d8d8fe14f8f951c1e1e1b762fea0a8de48d876f0d435e406d", "impliedFormat": 99}, {"version": "e28bd3811bc4d633b7686ac4cb1619a40d4a5f06e7a532d2f873362641d7c750", "impliedFormat": 99}, {"version": "85291a7a8ff3498595a58def3680cd2fd1d9a53f418e990ae452a31c36dc681c", "impliedFormat": 99}, {"version": "9b81bb792ae111bfe0e15bea0c137743ab5aca49ebb60575690e2dbc8c6142a9", "impliedFormat": 99}, {"version": "207e2211001d4b040b38a4c9180a973cf8951aef467d188e0510876bc2b638f4", "impliedFormat": 99}, {"version": "f476e85caaee5a61f9961fc8ddcc017edbe4c0a189b5f19fb0cf79785d350960", "impliedFormat": 99}, {"version": "e8f3817527fabf39af2cc9f818c9f2712e1bbbedc877165513260e945e14bea3", "impliedFormat": 99}, {"version": "60759b476b9fab2f6eed24316e294acbf2785dc09d2c0a7bc885c708d58d8eb0", "impliedFormat": 99}, {"version": "8b6557cf8ea0e54c5fd8d26cdb909adb0e56e606342e1b3bae08e35b3892d332", "impliedFormat": 99}, {"version": "1491945c5e5ce2642498fab535130bc6113a158302473648fed93bb659005c21", "impliedFormat": 99}, {"version": "91d9de4d3888ec1767f406307b333103aeb33469f81a8f8743ece26549f8f856", "impliedFormat": 99}, {"version": "86aeace38e1411ab3d8245c9fe8b47a6bbcfe67b1950952aaeba8d368792ea7f", "impliedFormat": 99}, {"version": "24938f42400a4d7a03909cd9fe47e08169c24f5080e40f94fdf5ad7865efdb64", "impliedFormat": 99}, {"version": "45c56bc0266d4b053a0bd6fed4e5dac795b25f4ffa163c2889f9430207ec7e61", "impliedFormat": 99}, {"version": "4c2b272e7621cab2e31cfd90b991faedb6c6093a7601c5cf706b1d382356a80f", "impliedFormat": 99}, {"version": "bae7d4bb0100dd7aa986242ad0c0d4fccd47ced046d8247619ec5addf1ccac46", "impliedFormat": 99}, {"version": "3723412824458f947ed83d54e5cf18bb0a3abd6a477ff27f790df5123911f4cc", "impliedFormat": 99}, {"version": "30b0c4166fa0e157bab29cce21816262809db7d9a2d7b550b0addd8770131cb2", "impliedFormat": 99}, {"version": "129eb3154d47765345b727dfad826dcf015b945518edc771fadd5b55fd79f8da", "impliedFormat": 99}, {"version": "68865626693619204e5c4939bd162e4d83793af5875f2ccaa040fa9d520245f3", "impliedFormat": 99}, {"version": "dc18d006527854eccef7394cfef826eb78bf2d98952401df0b94da286f7b11c6", "impliedFormat": 99}, {"version": "2ebc366546c04766343b893981c98cc98de25fd19780aa6e89a2b1aadae02d27", "impliedFormat": 99}, {"version": "634780c64447037b36520420630445cba29182f98e5fb30a3be171151a98bac5", "impliedFormat": 99}, {"version": "8ff732f7c6bbb546de0fc5fe67d9310cd0bf08bbc8b857f5ea9f8a7d1edd2091", "impliedFormat": 99}, {"version": "f040fc03fd40d432eaec6ba8ebdd39594a82543b9be06bf83582f48c3935e656", "impliedFormat": 99}, {"version": "ca1f8eb1cf10212b45073ad889ac2f487cc5e73fa034a98d1a2468bd768e2b66", "impliedFormat": 99}, {"version": "88cf614bf3ab45d110847ff24583aa93652cded3dba9ff9a28c052e7f856532f", "impliedFormat": 99}, {"version": "414cf5a4a3eb68a9de5d25cd65fab2dec3518d002d5e5ed283ab45dbb90213b1", "impliedFormat": 99}, {"version": "ecc9bb23831c259c81797184a9b04ac3d7ae57730af050c2bcd4ed1b17633222", "impliedFormat": 99}, {"version": "b2ef558e792dd42fa4648eec70f3cb16ea8f7c8112a781b116b35003619c4e88", "impliedFormat": 99}, {"version": "b4e5110fc351e685cdf5cca89d14968e3961c207e6a718672934a82907095e31", "impliedFormat": 99}, {"version": "fc17b0a42163ea104c2fc6ea4fc8194f8b896879824701b69cee4ec3525e14b3", "impliedFormat": 99}, {"version": "5c5ef9c90040f0eb8c4395a081f4900ca61d05184367cea7201892d1e6ad00bb", "impliedFormat": 99}, {"version": "581228c64dea7a741cd82cc867dbc74aaf333f00a415a542c94f32904ca40737", "impliedFormat": 99}, {"version": "70f5ae7a1693491abeb6c47fbcbe87da303d827f743bb1a0fe53bf164b649c82", "impliedFormat": 99}, {"version": "0e4b16f47b470e8fbfa8d48d3f2edcee1265f892dc6fbf2d1753277a359f22c0", "impliedFormat": 99}, {"version": "25067a844ad4063c1a5dddb8f431102ec63416ccd137a8729dbddf4670409a99", "impliedFormat": 99}, {"version": "896671cc8761bdd202530ac0e56554b27fa82d7a8868bbe0741b38d0112c83d5", "impliedFormat": 99}, {"version": "0919d67789e0a3f8a5d862d8d5ff8812a1d22fbfb60f79f5d551222d91feae74", "impliedFormat": 99}, {"version": "c44703f0912e1ad285a124e43d51743e81e935b2a026da5342ab4359776bfcb5", "impliedFormat": 99}, {"version": "9847394a460b2ba1c74250f153f83ff153af03408850d0251b47ce4d5972d663", "impliedFormat": 99}, {"version": "8554d2a8414bd899dac1ce32b70ea2c5e9233b25991743158689927f537a05cc", "impliedFormat": 1}, {"version": "cf41702adfda9c0cdc296d135daf549c837ddd58f18517cb43b2dc6106f8a010", "impliedFormat": 99}, {"version": "e2d3d92b851b78c368e4fd7e92c503d7e4683053f9017b6a7cb0b0fe103d9bdb", "impliedFormat": 99}, {"version": "336b589d137529f847fc4754b466f1297866cd7daf74f024a36d52f37661ef28", "impliedFormat": 99}, {"version": "184aeb5eaa3d273414cec3371de4bf250e9a0118f7f993a3b7073367d7f04d2b", "impliedFormat": 99}, {"version": "8ac3f63fc0101a5604b2eb60d047b07761e85b9fc4300e3d4a0fefe928304c92", "impliedFormat": 99}, {"version": "5bed905879da0eab8e40b1970373866e8138a01a47b16f647379f8b56cedbcae", "impliedFormat": 99}, {"version": "fe661627650afff35648a45b4f46ed2dae3518d7e0786869692036dd6fe8c6bb", "impliedFormat": 99}, {"version": "cd49aed6158f2f96af1bb89071a4f1e9d7c8db48502694583fd05d2ed91a7ff0", "impliedFormat": 1}, {"version": "e950ba3f48c047a5e3edcf4fed8882bd922aaadbab5847185ea36dd88aa229c0", "impliedFormat": 1}, {"version": "bfbf711ea6a40b77993d3caaa82005dbb767994592442cc38bc69abe7707d022", "impliedFormat": 1}, {"version": "0eccf218f52d092d4e4d2df0ed85e588742cf160bdf5729d27497f1691b54260", "impliedFormat": 1}, {"version": "02ad169938f048646d46a33580a0ce4e42274a6bdbc47ea92d0ed653a95a4905", "impliedFormat": 1}, {"version": "c4c59ec8ad587c1370f4b28fc721e9e4f7cfb6e49fa8b3187265ac8a9c08a59f", "impliedFormat": 1}, {"version": "d367a318cbabac820364882fdd51f755ddd341c1b40f29189e1d90b305e31d7c", "impliedFormat": 1}, {"version": "06b85cae3e1f5e6aab3e67907519fd317ae8192b402bf7822c58ec09df5e18d9", "impliedFormat": 1}, {"version": "f5ef87c57efe5065b2aac2ea42dad58cec7001214346b4156dd113dd8685205f", "impliedFormat": 1}, {"version": "9375425e113a0a24481a8b7c82eea387a2a1d29a9498951e2dae2d629571fd13", "impliedFormat": 1}, {"version": "c84c0d197a34f760b92d4be8941533203ea2bd798d62a3bef54b8e41f62b8c9e", "impliedFormat": 1}, {"version": "aad2fd4916b4a97e895350bcec0a04e4651e3bd005e678763bad80a2fdc03d1f", "impliedFormat": 1}, {"version": "d4a60e15448a475de2e22a03c45aaa527abcd272b303c965f4145a08d694f1e0", "impliedFormat": 1}, {"version": "1db4ebbc2cb527def516e82c267d694b3591272006b3627d328033dd639fc3d0", "impliedFormat": 1}, {"version": "e012cd78306a0d6d98adf66897b0912d7b8ca6e894b12725328253d982615349", "impliedFormat": 1}, {"version": "294bc9c756ed568b281afedeb2abd3aa9657a42c849b142960718ec005b4f7f4", "impliedFormat": 99}, {"version": "2be93d05a1ab4c451e32b558313a34f85b75924bac07ad77bf6858ee251c9a60", "impliedFormat": 99}, {"version": "7aca9e30cc1c496381d0ccdec397d0f94a19c7ddf8d9124ed004afcbb74f88b5", "impliedFormat": 99}, {"version": "4a828e37eda065ae1d4313cd5fdb711ac5e0576fb5b6ed96936bedf41565144d", "impliedFormat": 99}, {"version": "1fccbe798c9c7664cc3bb2ea97a7defe1811e8b5f0f06f0ba859590070f322cd", "impliedFormat": 99}, {"version": "a386eb65d69e8de7df69a58568d86e6238e79dd48534da0583fcb8c53fe36b93", "impliedFormat": 99}, {"version": "2e3af108ea6159f1e756457dab0ea7aefb51f3bda913936c07bbab59868f368c", "impliedFormat": 99}, {"version": "d45bc0581314a389d15543c6af1f8caf7203cb29cf289d6be13a74cc7478be98", "impliedFormat": 1}, {"version": "b7ac6117283c8a6a215e14f8018537bf0512469560e3e6972c8cbea3360fafc9", "impliedFormat": 1}, {"version": "ed1f58a60e9ff8e31f4fcd443e89bf8f94479394f3e8352a8d93d2090938e268", "impliedFormat": 99}, {"version": "bd9424667fbc23da1256cd1bdabdc8a9d330378b62128fd8295d50b0e5194d30", "impliedFormat": 1}, {"version": "66d19304720db82ab36f85e2e900da581d6da45fa24acbaf32e2fa21d0c60720", "impliedFormat": 1}, {"version": "2922e41051c6d71d9823b5d730b3ba9c0725862384bc982976a6738ccf859211", "impliedFormat": 1}, {"version": "5a1e39d2882a26321412b2d3803fd8ac6d80d9a04044ecd95788c61eeb6a85a7", "impliedFormat": 1}, {"version": "51e3ff7de50a1890fb524dcf04668a06c831ea93968ed58286c95e6b21b6db9e", "impliedFormat": 1}, {"version": "a3951316eb65c17ae4d5fa234684f0b8cf4fffd5a6a4050d7f6a15aa7af7b367", "impliedFormat": 1}, {"version": "074a619779785df871e24ee50548775e16822aca8d555ff3cda7eafcfd7a0c36", "impliedFormat": 1}, {"version": "7bc5857ac71def0f956ea65dc2dfeb52743ff30fbdfe9ecda7957726a92e3d52", "impliedFormat": 99}, {"version": "ea3c74b0a1c607143cf198c4daac5d026ef32b01e33fd2b77fbe20fc14a7e782", "impliedFormat": 1}, {"version": "afc12565b75863d07a0539d17ea862362910a248484d149b92a8f9b8311faa1f", "impliedFormat": 1}, {"version": "ae0ab1fb69341e241efa0e10d564be4bc83edd59a374212fa04035414b2a9c3c", "impliedFormat": 1}, {"version": "a4452d7d4ef15ce619a2f7c27da844ed03bdb9856de63daf70d78dd989f46ee5", "impliedFormat": 1}, {"version": "e8e18b803d61f1dd9470e41830406ca46f749fd4c121ee28a34a82e47c202f35", "impliedFormat": 1}, {"version": "a23c0b6810a1cc87754ff155d70a61619dd959f3ef2e53128a2a6e61c414c066", "impliedFormat": 1}, {"version": "623aede72a3893f2d0c05612179bfb5254e8dc847644f7994c65a115cc5d4700", "impliedFormat": 1}, {"version": "9682e7c471e0b9f1c78626e4bf0758533b9d6972463d3fac6f49f9f6779b2473", "impliedFormat": 1}, {"version": "a63496d5261c51888d240cdca713ed09aebd82f4b6915cb5a83c1372d88b1638", "impliedFormat": 99}, {"version": "98559a86640cc59bbf3a89d4def53020a305ab2a315b6168fe6a292bebb578d5", "impliedFormat": 99}, {"version": "61ee011d7b9fedfed24d2e4994c1b13f31b4f8542f5d0d1f04a3de8f1a00b89b", "impliedFormat": 1}, {"version": "322d6252aef550414ec56bc6977a513efe6e789377a7c26ce471babd694e30b3", "impliedFormat": 1}, {"version": "20690f73f28493ee8a7722143f40c9635774750506c6efa74a4bae745ef074ae", "impliedFormat": 1}, {"version": "986ade55edf74fda7847111c1eb178b30685762b7ac3eba818b97fae893646a8", "impliedFormat": 1}, {"version": "45cd96ebe9bf9983018dc9876801b0251149f86a395353b2601e769a68dfdc93", "impliedFormat": 1}, {"version": "ae07bba957fdc78f9ed54e81e612c91f077381043b1d0e13199e8ebc190e9f07", "impliedFormat": 1}, {"version": "3683be50448cdb2dbf87f6c9d88bd90397dc0d9134a12fe74d4c3442c6e1114f", "impliedFormat": 1}, {"version": "ed5791f0624f0f9068bbdca547e5060e85178678072a6f449f11425e3e4c635b", "impliedFormat": 1}, {"version": "ac9db0f117685c8d488df97d08822379d0c2c81af9f5dba9c722496ac2e9450e", "impliedFormat": 1}, {"version": "f28fee146ba567c5ecbac05f608148e94b61e0d3009a4b0bae3d1e0d9ad1544d", "impliedFormat": 1}, {"version": "bda4bfd0ce96c17350eb1ad25253118c78c02991b17b4c67283e3debea9cdaeb", "impliedFormat": 1}, {"version": "444a156c7915d334a1406a10b93e09f7448775192265f66a20d3e17477141cc1", "impliedFormat": 1}, {"version": "f5e62fe8160bfa4c1e860662d1f260607ce60a58519662ed7bacbbd91a43e3b8", "impliedFormat": 1}, {"version": "8259b52727c696ec545f9b0a8a8fdcf683d05e104f30a17ad7f0a5e75d8afef4", "impliedFormat": 1}, {"version": "1755a953fa9a650c368b41851d2ed2d453176f1a81f5751213298912bae4a200", "impliedFormat": 1}, {"version": "fcc6e4939fb0920459fd0d96bfbd9378644a6293d4c94047d73549e5043331f1", "impliedFormat": 1}, {"version": "954c32ea406e98443674dc9b06431393799fb9f127127967fb0e5190a256fd57", "impliedFormat": 1}, {"version": "aeb2dde4047fcebd424af7cc2e6241ffbbaeb5f0ce5d5b42d0cdd4f32f1067cc", "impliedFormat": 1}, {"version": "35fa03c42e890fe3c3c5b72dbecfc0d574b0e433fe926d963ba820555872ec25", "impliedFormat": 1}, {"version": "1a42151149fe0a05358e89a0cc1a28a3d1b3e4928c4531c2516d8636cc0653ed", "impliedFormat": 1}, {"version": "6a2b47984b345a36a8993187e413b9723e7a8be7b9b9fb31c397ac82fa472a18", "impliedFormat": 1}, {"version": "007c679a3419d30d2b8de34ee480b2d27eb7836c567f92ccd405e5256a5b9f53", "impliedFormat": 1}, {"version": "70cd13f0738ad8acbff5e1e8a7cf517b0637eb89381faf555a8a17e1b2e0bf7e", "impliedFormat": 1}, {"version": "a2df06f617c53ad98e7e096e13364f0e34fe655a9b88b3d65ebdcffb3094c53b", "impliedFormat": 1}, {"version": "98cc41d4765f2cf685a5bd5a8aa1ca2c5115975b52b1ae9bd588d9fa331de154", "impliedFormat": 1}, {"version": "10209948989e7c8d0df98202762fbb664b6bddee39d2dd9184f1f0362892fba2", "impliedFormat": 1}, {"version": "5b557100c81a12c40b9b028ee2efbfb4fbc806ad38ee6923d8896397f38841d9", "impliedFormat": 1}, {"version": "6f6013a289680f4335d74567b64edd269ee4a6c3b7b3aaaeb44396958d183df1", "impliedFormat": 1}, {"version": "0ef324adcd43825ab0315c9270a00eee626809768418b0c8f10209a50098e184", "impliedFormat": 1}, {"version": "bc8cd3dc52f8ff1edffa47f38bd6e2843ac47d01e4d092a5607bab71471e0719", "impliedFormat": 1}, {"version": "13e1afd72635d087b9bd567726f6f5aff2beabab5108620bb5be82cd1e87aca8", "impliedFormat": 1}, {"version": "d770f64eccd2d224ffe5f4aa2fdffc9a939f72636803b5f2e949459324b238f1", "impliedFormat": 1}, {"version": "4444223d26a71d7b215fb3b3d14aff12864d615d225fc1e4f506aa6ea9a349c3", "impliedFormat": 1}, {"version": "4f578523ba2f3a912b9918278cf437ed2fa2ad579291439c044a43a0bac78bac", "impliedFormat": 1}, {"version": "66cb334683bf4790bb235546bf249c28e42289d938bf1e9f69cbc2931ff6ab1a", "impliedFormat": 1}, {"version": "c8524fa9122337b96b86c5d40898c31085cd4106e7ba492d135b6c8b847cced0", "impliedFormat": 1}, {"version": "2de6b74e52a2ad1baa51d77ecb498910619902c79eaab72aa8857d76f78db40e", "impliedFormat": 1}, {"version": "84e7964321ea2f0866b08d3ec4b3b2f0c46ebbc97ce5546bcefde0c9a1106225", "impliedFormat": 1}, {"version": "c7b2b283a7d4b271ea7201e702a01903b986cb80206f46c15d7a5be9f0e4cdae", "impliedFormat": 1}, {"version": "e108267cd338f8fa73c6ef17b010e1354bf0ad434d8b79fc75b33031b59388ea", "impliedFormat": 1}, {"version": "77a0b13d8090ec796edf26540e11ab52035d2eb1a99226c969ce7a0248ea1cde", "impliedFormat": 1}, {"version": "8fd3e1d3da61bfbee23411daf73d8aa4bdae3087243d2dfa3fa926007a787d8b", "impliedFormat": 1}, {"version": "67c9bc12437a4c520ab47ce6e5cbf03fa21d60bb74fb55f94bf386a42c8d79df", "impliedFormat": 1}, {"version": "d21bd488e94d1ee649a1939a4008240671979afc9e4a675b6aaae21a7610c0a5", "impliedFormat": 99}, {"version": "817242bd066283652bdd18125d2dec47b8e4a36386af5b68e094706750c05bbc", "impliedFormat": 99}, {"version": "d5e918c15e5dd19cdcdb91967cc054eeccdd4c7c3c7e1e3b7db1e1f7f52773f9", "impliedFormat": 99}, {"version": "164c476f6c481c2b92e184dc12d5c03bd308b24a58569d68119f39f2dd553cd5", "impliedFormat": 99}, {"version": "16cd769ff23a1aa0ab34870af4c8cfde4e86c8da9b4173b64c1620aa42b1f3d7", "impliedFormat": 99}, {"version": "4645da89d905f267ed3444ca0002c28a75f209b1bec120a788ff5959ccdd14c4", "impliedFormat": 99}, {"version": "d3836e1ff434adac76ebbf1803aac23c28eb158c707121c99333c2d8e54710cc", "impliedFormat": 1}, {"version": "a4884cd40a7e19fa5bda74ab6d38e656ad29f8cc3e8ef03ae6b1542f07c5602a", "impliedFormat": 1}, {"version": "7f067b68a8b955d60844c0b0d362429774b70d4b1f926ec6e5d2c9b2d57b9975", "impliedFormat": 1}, {"version": "c841d1f2481e4510cfb37f5f9e6f858fa70c4ee652a8fdb67d9e4d28616af536", "impliedFormat": 1}, {"version": "7aa23ac731d2c2bebc8b060010188ba08ab7feada96597d69209fb68110f7820", "impliedFormat": 1}, {"version": "ca20962368e1603cc51788b6bf9b180c9314c04682578c706483dde37cb8d71c", "impliedFormat": 1}, {"version": "abdc43186869ed247b6e638edecac2dd3d3b0ba3737c15d1b2208cf0fdece11c", "impliedFormat": 1}, {"version": "d613fd21530285ba1ce43fe63d43ebf49b29083740e9b71420fd080ee59b72c4", "impliedFormat": 1}, {"version": "7501f84c338110641c797017b304465a749b9b0472d5ee105cb7466a9ab1c45c", "impliedFormat": 1}, {"version": "66d851a0fbc50669e30a35478ab1bd4145a471108e696c419cecc76869a4fb41", "impliedFormat": 1}, {"version": "147b8a2e318fd0aa27a11d91b4ead701677bb0ed14f07950450a5ea54efb8647", "impliedFormat": 1}, {"version": "86446b0fbc28b18f3aa8a02bad6b64b7ca7a2b4381dee7a7746317502611f33d", "impliedFormat": 1}, {"version": "a4ac929411b54c98fe28d712973ec9d04c8afaed299ac5e96982e27022de1969", "impliedFormat": 1}, {"version": "be3d2019bfce49f12faba2fbef9d7fe972f009267a0360d197aff97a52910539", "impliedFormat": 99}, {"version": "f246009b64de261f1109994eebfd4c62e8909ba4f81ae82fc0c8d805d091395a", "impliedFormat": 99}, {"version": "1288652d46ff463e3d66da4cab77fe3e54beae9cbe8452796209a059683e8a4c", "impliedFormat": 1}, {"version": "9c59a73485944806f546a0230ed72f91752916ecf544a467e9ff204d00b1d59f", "impliedFormat": 1}, {"version": "314607633858954476d2e3dcddf1e737c1921041bd621588ed43083604cb4049", "impliedFormat": 1}, {"version": "43efbfc6c0f7d66008c47178acf38d67f49cb2905ad932100b04fab409c069b2", "impliedFormat": 1}, {"version": "2322ca2b7d1f9eab41b80952c3091c5b196defd33b68c5ea7f95d8b873c78734", "impliedFormat": 1}, {"version": "770e668352851797a4adffdc7aeefabca46534ae4f867eeac581c25e3676d213", "impliedFormat": 99}, {"version": "3c3bc144ed295cd2e5d272e8edead2775e4f2d502b16da79d5530fe4813dd63d", "impliedFormat": 99}, {"version": "1b927a1fe68d37c8666a874a201ffa4efb2c04e641e9539af93691429a488690", "impliedFormat": 99}, {"version": "29fe137f3e7e57f636ef4f7be82d48d0b416c1247724844dee359395deace705", "impliedFormat": 99}, {"version": "5f1bd0d23820334a1bec99abb2dd2fd8c2542460deeaf8f1155508b7f0cb3c67", "impliedFormat": 1}, {"version": "977079894a03a1f4fb238f8531b806d9c3068f6e3d39fac521736683155e9f92", "impliedFormat": 1}, {"version": "757c722c2cd841f270f1154a96d8e7d5929a7309af596e841de7a2b9a315de63", "impliedFormat": 1}, {"version": "d944302a048338b81d4967cc9cda8b91a799ede41cae41006f8647b8b62e9d70", "impliedFormat": 1}, {"version": "9b47578d6296048e206f652b5172b496e5c95b78c397f7afbd38830dc781b91a", "impliedFormat": 1}, {"version": "b832500c086448178139c2cf2d8c17d3a2bba7c035c07771562fed9d35032e8a", "impliedFormat": 99}, {"version": "4f0120eff50541bc1c54eebb9859f582ff87e66c383819400152d403f631bc2e", "impliedFormat": 1}, {"version": "ced7cdf59b11c9d0b84262e9b4acecddbc13066bc356cebaf7c6066218980eba", "impliedFormat": 1}, {"version": "c14459fbd9e861cf4b6e387482a5a3b4c486b367407cc8494d24937089cf52bd", "impliedFormat": 1}, {"version": "a51bf628e208fba3ed38417a27231117bd20f5e7c012a4fa2311f222e9e9ca09", "impliedFormat": 1}, {"version": "550fb586e710bef0fafa1efab6edb7b4f0d5e9885de5ec6e0634ac2d7591e531", "impliedFormat": 99}, {"version": "6ddf06132ab882a16635044addb097579139d96a59fa3c435a478a2632d40c0c", "impliedFormat": 99}, {"version": "c7e4e2065c9ad413fcca14b1dd64a3b9bd7c63592e8e55fca5d8216f958efb87", "impliedFormat": 1}, {"version": "3aac0bda70794a4979c08827cc56148fa503b88e6df60130c600950bc8b8300f", "impliedFormat": 1}, {"version": "720e617a3315afbfcf6ace861cbe8524bbf8a0c323790957c52cd12c2e1090b7", "impliedFormat": 1}, {"version": "4c28e5da0d1c98af1f203f44381a38f1c28bbc06edb6f04a96bf3c933041e376", "impliedFormat": 1}, {"version": "7de4a9312703949e98a134e28db15993288f594f15804389dfe50d7b17db965c", "impliedFormat": 1}, {"version": "ad2c280919709a21a167075a8c2dbfe3c3576d2de9bae5e6b456cd07e3ac774f", "impliedFormat": 1}, {"version": "0d44431638864127389ea24be76100dad40e9367689dab898dc5140b12792de6", "impliedFormat": 1}, {"version": "97f106238574651e3d00c4111a3520c6dec818d7dfd6c7a9c26e35d0a468a72d", "impliedFormat": 1}, {"version": "80f883370480f0a10d6be2d25b43f857288b9fc4f2af0e49e48d7a964f8c012c", "impliedFormat": 99}, {"version": "43083049cabb84851840b019717e91f6a7488a6ed928b04e262ed34879c34cac", "impliedFormat": 99}, {"version": "792705b56aa3af616bcd36e6723018d36b5a3ceadaa590dae91853b7cc50f229", "impliedFormat": 1}, {"version": "bb1861671b3e8506d7953199d52b37b3697efc8387fca12f11e1789c7593e101", "impliedFormat": 1}, {"version": "31f84fc31de4eec1cfa1d9f9d5c66a86b86a2d16fab706cc8c11c77dd10c4d89", "impliedFormat": 1}, {"version": "889c85ec6a0326d839877c51d2378b4e1966c70bc0a8212b5e7dd4954cc2a810", "impliedFormat": 1}, {"version": "3a78d4c2cc09860aa6d475192ffdb61e58f1e59b1d4c8a98adf724a84ae6efe8", "impliedFormat": 1}, {"version": "4fbc09f3fc8e98ba96c0cee306afcfef296c6e49f9417f9e8a8192e037c43586", "impliedFormat": 1}, {"version": "3a4eff1c847fbbccb7a8b92376d3679f97e011124a830c113a7e1a7ea24aa1cf", "impliedFormat": 1}, {"version": "4708402ff429b94a941d2870557da4bbd480ddc724d5224691d26108967b84e9", "impliedFormat": 1}, {"version": "6cc8b13713579f82fecc120be3384f98645b946def9c0e47cdbdba2d2e8a327a", "impliedFormat": 1}, {"version": "0ce858bc6d306746de55d44522fb87fb5154ed4b532ff97f504f5457567ce488", "impliedFormat": 1}, {"version": "e5bcf4ab6d90084e3109159718bd7775f291d686e03a3bc8b1b61ddfce841533", "impliedFormat": 1}, {"version": "129803c19a3862c5a7fd986b05cf0830c153ea66995e4902aeadd46e30b2f9b9", "impliedFormat": 1}, {"version": "277f880353c81415728a9d9bafc783aeb55102b1a18f5e394782cbaeaef64797", "impliedFormat": 1}, {"version": "9816d81e79ac29d7ab9b865106790e6ae3005cbef959c986e23e25ac2fb82e34", "impliedFormat": 1}, {"version": "d5fa0ba7912774d9da9ac829bf81bac09235bef14bee6b7742a59eea684da3c2", "impliedFormat": 1}, {"version": "cbed44b5c121ad3e090ffc37934c476ed5e81b5ea8c391f3e52f6639c18e00e1", "impliedFormat": 1}, {"version": "4ef7a356dae30f8f46718b80c2d59b7066ad8c35220c5cdab7b4cf216cd34d41", "impliedFormat": 1}, {"version": "4755f4d348ac8af066501d6c84a79ef0af57e307c0bc649379f5e3aba0c9b2b8", "impliedFormat": 1}, {"version": "04f0c8dbfb8de66349fa5d70acc4d90f1f6bdb040d65b1e4d432e1aaa9faa550", "impliedFormat": 1}, {"version": "8032813f4d60c82d77eb76f432ac33ab78811844117583a2dd1d2d380e8c8acc", "impliedFormat": 1}, {"version": "ed00015def8e260497e4fa2966e4570af2ecc698a14f772ce8466120d083e87f", "impliedFormat": 1}, {"version": "5be6670f719abf31952da185de24bdec8b3ff544af1b42f617fedc523eb7dd0a", "impliedFormat": 1}, {"version": "349b62a4c8eca30c0c1b88ff7d7b4202b9023838d4920bb3285c53503479c16f", "impliedFormat": 1}, {"version": "2cd866f124dd39fbb4ce5ef4a423c58b248559bc2f4630c89a511b40ae8515bb", "impliedFormat": 1}, {"version": "cbc8ebf611f59eaea4110db8d42fa66555f8db6b067f61250f6bd48c4b0dba97", "impliedFormat": 1}, {"version": "89de4cf24e6bf82853a1ed01bbaaca52265ca145554fd61683c1c86724002ac5", "impliedFormat": 1}, {"version": "a3ec80e4314b6e9ec98b789ab254964228c2da07b2c828a57caa9ee2a946e028", "impliedFormat": 1}, {"version": "076a044cc974145f004da7d8af62a2bd1edcf764afd566dab1ee7e153ecd915e", "impliedFormat": 1}, {"version": "b4c7eff7fcb37d5397d385f46b35704dbc6b05c16587900cc5ac43b0496c0dc6", "impliedFormat": 1}, {"version": "1515aee78e45066b66b434eab804fa5aea31ca0c53ca0390d817de6999aaa55f", "impliedFormat": 1}, {"version": "d5eeb5cf8cf58d27dda28cdefceca0ef475117e39c280fccd4320e4c239da605", "impliedFormat": 1}, {"version": "63663a5a28011203f5f00a80e7181944f2282f2cd573d3133d31c0dc8494393f", "impliedFormat": 1}, {"version": "ca7cbcdb0e5267fd761ca413a27a92c711ecaae3f48a2cac53a18aa8e3b7c47e", "impliedFormat": 1}, {"version": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "6733a344b6da75e526d5bae2b1a2fa0026af4a70c179358ace70a39b5942af34", "impliedFormat": 1}, {"version": "5a12246ac00a5e83e4077cfe33cbef667f341427803e338ae89d2b16ae24c584", "impliedFormat": 1}, {"version": "ea49a596c3d7b9987d5bf48bbc7e8737c51f26099c61fe27247b89e1f1b1dfb7", "impliedFormat": 1}, {"version": "f4808a450da98603ccd9f24a03dceca88177609346a140e692f10b2bf727472a", "impliedFormat": 1}, {"version": "2725af34317958e392efadcd53e8c7b85159eb23e17ca6af5c200681444080fd", "impliedFormat": 1}, {"version": "7a261118c9024793e62b3bb0f06c921bba2c76893df7532ce45b28605b1acfc3", "impliedFormat": 99}, {"version": "96086e401cbfb1c00e5985c52b5d7fc73f002091cf1a9cf00c7b862cfe6fe835", "impliedFormat": 1}, {"version": "6450ebf7e94dfa012778c8d52ef60b42ba88662dced9b130a857bd5a8009a870", "impliedFormat": 1}, {"version": "4a068c0e19e6c952587f6c84e2d61ff38a668470ce75d95cafa3ca2e766b165c", "impliedFormat": 1}, {"version": "bb12f2c8de7d18625a5512f511ebcdb687f7eea498c22d6850a8dc6127029789", "impliedFormat": 1}, {"version": "a7f5b7c1f0e3ad6f296f7b9dc247af83e106ad469faa7c1c4d689cf1b3969788", "impliedFormat": 99}, {"version": "b1072140238296df9e02970e7c3c33046088617ccca23d2964a93ba59ab7d6b2", "impliedFormat": 1}, {"version": "54ddbc29b84ba9c7541cc0e3179c120438a0056d8c9125404dd4b5d85a039b41", "impliedFormat": 1}, {"version": "50777921dd77a545ec9616bf3b6d5ebd387c9bbcf43f0d33300daf4973f27c7b", "impliedFormat": 1}, {"version": "d3f1af3c46428e7a776c6d653f1703622eec17233c087a1ae8519cdf0642bdef", "impliedFormat": 1}, {"version": "a6a0ea9a9fb4224f91c3913a7974dbb7ec507d946f19ca471dcb365b3d2d5ef9", "impliedFormat": 1}, {"version": "342d749322d2d981660d1fc14614b6b88c10ff108492795554e9fab5426946a9", "impliedFormat": 1}, {"version": "5a47a480f50b2abe8da92961b68811be2fe836cc31f466ba1bf204bf509b4465", "impliedFormat": 1}, {"version": "3864005b53021fcda05eaeb7ed3965106e3d0c7ee7216f905da9e3b142cc42bf", "impliedFormat": 1}, {"version": "43ed26d4277c0c673b6b7fb4e581a49475f0749b6881e7c9ef74c049c9b1611f", "impliedFormat": 1}, {"version": "6936b08808611f02d3e37254311b958be3ea59125164c2cf7c668eb312b0c0a7", "impliedFormat": 1}, {"version": "a97b3c624c2b70bf6d7b201ac9cc4a37b3ef0d4cdb283ad089061c4910e680e4", "impliedFormat": 1}, {"version": "cfd695aa8c03fcfd2e5dece1d95237c13dde1bb52ed5e4154be84118d86cea6a", "impliedFormat": 1}, {"version": "0bc5db0fd69271f7b17bf6d9406e081368d4fe7d134ac1cad192322035c1db54", "impliedFormat": 1}, {"version": "97c9cf8776c7829d69cebc946c97531dc43fedeb06229fd656457c80f6fc3630", "impliedFormat": 1}, {"version": "ffa1d9d783c2a538498bc94861411584e6152e2c096707bcbed0fa49afb0686f", "impliedFormat": 1}, {"version": "9f379e7dc45ab77d3c31bd4b450add92bef9a5ebbd868a69e18acc19f1f419ff", "impliedFormat": 1}, {"version": "e0f9bc05fedc74140bf9380702b45ae59425efa0a69f46db17e871d9ae012376", "impliedFormat": 1}, {"version": "16dcd512e4dc3b524faca566ed30451dfeb6c5240e1094a518a17d1993c56f17", "impliedFormat": 1}, {"version": "2a6b911c66c8ad529bca6fab1e006ae5bdd2dd78b93f2a0637c6dca841b2da61", "impliedFormat": 1}, {"version": "997179f30b3153366d12e97201044c15768fe798a0044d7a88ac51ab05ec0c56", "impliedFormat": 1}, {"version": "4329178c1720031241fb2af44c806a4b8986bb89d153a909a660d59e1ad255aa", "impliedFormat": 1}, {"version": "0125e04080afe809a78b7e58ef55d10f87d1a4b4537d0507e1b08fc89cb192da", "impliedFormat": 1}, {"version": "955955d1f6d8783cb13045618421780e503547884db1a3e30be29a97fb3b0c20", "impliedFormat": 1}, {"version": "a5dc19bb9292f768f76749c09aeb4d9dc8d4013e36905894cffb4ca167c3bef7", "impliedFormat": 1}, {"version": "55707d1bf1d796f7e2d1de3dd63f1f60d8cbd278be9a833457ef653c16234755", "impliedFormat": 1}, {"version": "93fead4ab135a5a88ac64ec5313ce34c93eb312a2b90904b70d6c1620525a80f", "impliedFormat": 99}, {"version": "84f654d203fa1f061538bca17a76a35434b3508e9006905101a2edc80b8715d4", "impliedFormat": 1}, {"version": "4167d82936b5c48e9a43bcf39880bdb8745e954ee37185555b922ac585d9d029", "impliedFormat": 1}, {"version": "6b0c41c159169f475fa67597f2852e4c11439b80916a599c1271f533628673d5", "impliedFormat": 1}, {"version": "07e967ce61790faa33131d561229fd590f2c52d9b83833401596a90ab21a509d", "impliedFormat": 1}, {"version": "8857f71d699d501ef2af1024bb2ce9b7991e5150d228ce59e5e281eebccbf640", "impliedFormat": 1}, {"version": "98678af9fb7efe775c86452f86b21c6940dfd5a00b63081ba85391abc40c3859", "impliedFormat": 1}, {"version": "bcc7cfde1701cb02e9ffa887cee07c6a91be82977bcb5996b97ed71172c5866b", "impliedFormat": 99}, {"version": "23e26e4ee57593fcb267d8274d720891bbf16b5a854462a5de98e67682adb39a", "impliedFormat": 99}, {"version": "7b30a014de89b58cc9e73eb66df0afc2075d4d0e7a0fb08250919dd7cd7b9ff3", "impliedFormat": 1}, {"version": "a255951120d5cf3fc1161ea4adad10d33c95daac989d5fab793f91dfed0eedd0", "impliedFormat": 1}, {"version": "02a6848a9759d72ee6e965ca83cd98363ca9e545f3dd7f469307a3bd227137bf", "impliedFormat": 1}, {"version": "0ec8a3f12cbd2d4c6fa8cada2832e3392ef9b91ac58404d0bdf996c2b483868d", "impliedFormat": 99}, {"version": "5431b31ba72c3c6634b6f850bd868b1c7f82ca3f2dedc9d971eac38eaa3e094f", "impliedFormat": 1}, {"version": "ffc39a0fbf62f30a0bbab047f5e391708d775d39297ef4f04bb0d1e6ba5475e1", "impliedFormat": 99}, {"version": "f9b1253985f18756ee35f2dd33bc5ee471356ff33233571e921a3b01ab4dcca2", "impliedFormat": 1}, {"version": "66d972f27099704ea2f1b384cdb1b48f3c66dc86d4c2d6a4a2f39519c0e4adc9", "impliedFormat": 1}, {"version": "7bb99ae6f103a9b0a3e7907fa14556d70709fc8a679227e30e4562043296a1a1", "impliedFormat": 1}, {"version": "0c88e60c41c653adf171761a51748d2836722cbbbdaec1894aa3ca019a22148b", "impliedFormat": 1}, {"version": "aa4dafa1a182687dec16951a911c6eea53de9b86b224c8be5b1b2afef60a10bb", "impliedFormat": 1}, {"version": "5e8156bc284eac31c7dd306101bf03433b2255ffc26207d35dccda65d7677ef1", "impliedFormat": 1}, {"version": "e5b47982d01e1dc90c1c66fcaf7fd8c4aedac2408bf080438e5dbfa8ac6be15f", "impliedFormat": 99}, {"version": "dfd24c0c9520977ec3e8ba073df46528739f38ae2938ec3483d335ee36514513", "impliedFormat": 1}, {"version": "d9f450e7558fdcb451ac77022b36f4433dabc3e7cbfb8e232a49ea797b954c72", "impliedFormat": 1}, {"version": "123697e147f636ce9a302594e0cdc0b945fb7907322d57b92c186aa25747e9af", "impliedFormat": 1}, {"version": "732dd036594ac8ef1498d4bbd206298ef1f88e68944086cbf06be340fed84e94", "impliedFormat": 1}, {"version": "757c534a64fba5a62e26f4a7509c09326c60c932e85001a6b24e235d4537adef", "impliedFormat": 1}, {"version": "fc0a5f0430857df8e6917c1cbd6039b7c8ccbd881f4fc05bab0a56c9a01804c2", "impliedFormat": 1}, {"version": "c192fbe83c4db753d859aadbd7bc23a10f4355588e7a789639cd886b7068a8db", "impliedFormat": 1}, {"version": "69b19840e285955f3a186386ee2ed4fcc724a5f6d8c33c60b9adb0075e0d6b80", "impliedFormat": 1}, {"version": "b0e1714e9e99e2a80d20b9e70b73e94d582b5294287c69aa6451db30c7939d50", "impliedFormat": 1}, {"version": "68a68ed916183c285c0f2442fbcba01d6545d4f8f41a41f9f8c9ef370b3c0bfd", "impliedFormat": 1}, {"version": "f5cbf4270472bfe31e1796bb9548ae57f706465076089f7bb780f8fd81305446", "impliedFormat": 1}, {"version": "f47c8894187c7e8de04cedd3a14823a6565321418cab116f2894f272c0db1b40", "impliedFormat": 1}, {"version": "999c7bb4639c1f37eeac888b7c32590579c9dd0afad13c4318f036a912bc28cd", "impliedFormat": 1}, {"version": "0348338474c90092d26e0776f62aa979727891dfffed46abb9568eafb4098457", "impliedFormat": 1}, {"version": "9c6000e7d984f488b868cdc7ca8f1ffad8a9d0bd0aa6ad087a3f5c49cb1a566a", "impliedFormat": 1}, {"version": "8031849d3c9979a1f9b8c4fe7190de77d76af6dbf7629f751ad271ef182d8958", "impliedFormat": 1}, {"version": "bcc94fe5a7360ead91670de2ce2e6820ffc0d81481a16fddf6ac5ea327935f39", "impliedFormat": 1}, {"version": "d5f93e7b1b801d2401d90c5823b3c144d17bc1d4ed759e8bc5251405e385210e", "impliedFormat": 1}, {"version": "3ed9d1fb4e2c90c0bcbcc63cbb6eef788c73a064af39f6331a1ca48d3e98248a", "impliedFormat": 1}, {"version": "8a0e5cf388ce4fabdd2bf3459add69efa2ec368720a41fdc0b5ede52df1d80e1", "impliedFormat": 1}, {"version": "ba5478a051f57e2b577f7dd6c78d5b677002a0549673688b7c33c6bbe1fa86cd", "impliedFormat": 1}, {"version": "279a6a190b667df3201805e20f126406d1eea0d8cdc5f89919cbfb23481c58ee", "impliedFormat": 1}, {"version": "3a3167d40e2d51a3dd7cb564c5e6291fd89938534b61b5903a8c4b3dcfb473ac", "impliedFormat": 1}, {"version": "a8dc24dd71f9227b907acd46cce3a3cc8dabd688f2d8142ef9925063b70d9a99", "impliedFormat": 1}, {"version": "5d37154eb158c2133c414854f866f4fe74eef570d70834e5c62a31502934720c", "impliedFormat": 1}, {"version": "28d4d2d8df2599a45027f280e0c6593082b0f27269444bfac257a1000d7f3899", "impliedFormat": 1}, {"version": "683edb3fc10aeb9ba139e2c518cd22620c625a44164c6c74a90162112ea61d2b", "impliedFormat": 1}, {"version": "30a85812531dccd9acd853ec187a8f8a669b6bba0725a844cfc8ddba14cbcc94", "impliedFormat": 1}, {"version": "d2d4f5fb7f59bce5e041c1c7cc00221a3ba7c19d1349e739f54c7890d520eeae", "impliedFormat": 1}, {"version": "e02dd24be26ecbcc2e716e63620d0c55d3c4494bef6eebfe6e516815a152b1f5", "impliedFormat": 1}, {"version": "bcf04553be5e7f8b880cd8ee55d5bdd3b25f0a6887c3ae9a7151a1a8f3a4773f", "impliedFormat": 1}, {"version": "682d0c6ff5757f8438e85dcb39cc509e353c786363ec17f34fad33b49671125d", "impliedFormat": 1}, {"version": "47b425579a2c57e2b66e91c909d48edd121a9a547ac5ef01f06ab2f418df1c2e", "impliedFormat": 1}, {"version": "80b78d05c78f4b0e40efba55494640faaae02a12730179c5affd5764511472bc", "impliedFormat": 1}, {"version": "088b959b48e562265493f12cb28dee101f2488b0b5edb54a5ea17fd5d943c4f0", "impliedFormat": 1}, {"version": "fdf6cdf7d5268927b5827ff1dfa3cb2bd55c658f2efeac5381ecfef70d273ca2", "impliedFormat": 1}, {"version": "b45fec77c232f77ca58d976bf0a423e069dd9fd0aa3852cae20acf12e188af47", "impliedFormat": 1}, {"version": "fe936a68d281ccc2060f8c3de69a0682a807c8fd1b8abccc0a91b83195ca0f94", "impliedFormat": 1}, {"version": "2b04ef3fd9be3b468a301f72c196442b86cd3b40ddf275d2b228fe2beb599c9a", "impliedFormat": 1}, {"version": "d129e624947eeff1010fc28ae2c2bbf8c68b4b88c188a04a1eca4478295f5bf5", "impliedFormat": 1}, {"version": "94b7ea249855720c710a695109c586fcb728bcedb26b4ceb402cc8c70868eeba", "impliedFormat": 1}, {"version": "e59d36b7b6e8ba2dd36d032a5f5c279d2460968c8b4e691ca384f118fb09b52a", "impliedFormat": 1}, {"version": "4c6534725143687517f291840189289f76a718d0962e3fab076d61fd428174c9", "impliedFormat": 1}, {"version": "fb941c28064e71ad9f5d552cc30eee727867522ca7e3138bc0c769366cf12058", "impliedFormat": 1}, {"version": "89e061244da3fc21b7330f4bd32f47c1813dd4d7f1dc3d0883d88943f035b993", "impliedFormat": 1}, {"version": "07bd2baf95d48b7dbc6cb5b24e13b83b5ec9657ceb0e122f9665057d2af03361", "impliedFormat": 1}, {"version": "b63ce995a548932a991700c7e677099d6ed85a7999b879372739d51dfbdee1fc", "impliedFormat": 1}, {"version": "81a46bb692ee40afbac3d5fdc2c834c402fc5a1102ee3d25918191f39d65935a", "impliedFormat": 1}, {"version": "14a7e2a08ee486a88fd9746dfd988cf473a122ae518a24e2503a15dcd10bdf10", "impliedFormat": 1}, {"version": "3480def9cb4acd4fc8878d9d3a5fd5a585b358eb25ae31b5ecbd14085b43fc5a", "impliedFormat": 1}, {"version": "ee317ef4a4ff674d77d6db14ed12236e1ae879184f7d50ff801609dc22530015", "impliedFormat": 1}, {"version": "7ecd499836bd10605f54fb8d4c88a8fcd996c35be9b0bb5eaea9e1e66af8b8f2", "impliedFormat": 1}, {"version": "338f6a3a5bf7ba0239c4f4e2aeac04d217c8b84d78e502745898fdac1aca4f61", "impliedFormat": 1}, {"version": "a38464c132d7a81bae689269621d9384668226f175b1f460d2bd3faf38bd103a", "impliedFormat": 1}, {"version": "94d0255f8835da5b6e58b2c43c461ef1dcba345a44ad7a2d688c64b90a94ab9e", "impliedFormat": 1}, {"version": "c1ee41df353b471e09c7b8b077d3f457dcbbb020a3970ac4b7eecefb15c92b29", "impliedFormat": 1}, {"version": "4b58f42e8c058f8326405bfdea74bd1e38f1ee1782b2280248ac653df972ffab", "impliedFormat": 1}, {"version": "2f73eb263689362609bd9a2beb20e128f5237933553fa6d6d212ce83c2a7057d", "impliedFormat": 1}, {"version": "c4b9ab92a855ef10587607317b28312b70d94bef20b4df914ec456859e0fcb9c", "impliedFormat": 1}, {"version": "d3adcb55318426dd1657e5c220af116f34d1d4e9395d4e8819f3aa4c0d33b080", "impliedFormat": 1}, {"version": "f773f743fa62974b10eff98ee4856135d055e437abafa9ececb9f702348faa5f", "impliedFormat": 1}, {"version": "6cf42a4f3cfec648545925d43afaa8bb364ac10a839ffed88249da109361b275", "impliedFormat": 1}, {"version": "bc773c63d03bee4607b8cc0d195930e8e008ab832aec16594b283302646312e4", "impliedFormat": 1}, {"version": "4fa52e41c21594ef92e1773395bcc775b7f935506c6cea20dbca2c8a4b62a78a", "impliedFormat": 1}, {"version": "a3f29291dcfc8eac21bb961bd3e28a65a3696d83f02d6690c03404d08347aadd", "impliedFormat": 1}, {"version": "7e196a2d147a025189a0dbfd5b2884144442d9306aade72ac756c6d15ce44f01", "impliedFormat": 1}, {"version": "c4c368b0b139240b0efc44b98afb9bad864aaeb23b5b05ed54c3b8fcfa7788b9", "impliedFormat": 1}, {"version": "42c1f74ac5ddd0a3c63380973f1b75be13c10d8239aa5ce97d2024fa87bed65c", "impliedFormat": 1}, {"version": "1c25b51b3d26010962b85c2020de84d4b16da0b18ea6b9e46f731066becf2fde", "impliedFormat": 1}, {"version": "22d35aa24b765208ea8cb637ac1ae52b1a8251fcd6902609ff9f8da8d5ba39ec", "impliedFormat": 1}, {"version": "ec6f1b6ea4dea29f45484e99831f471da38c175febff3d3e0703320e84fab4bd", "impliedFormat": 1}, {"version": "571f401c7e7762c8af696b7fc3039138c465475a5bdff2455d19f6f20d21a04d", "impliedFormat": 1}, {"version": "8d7e34101de761176a8af96b0dd3e8a4e38946c05160c8b61865faec0542fd0f", "impliedFormat": 1}, {"version": "6afb806b7b32ee290623e5e28fef6331e7b257fc629ac4298c75a259d6a0d12b", "impliedFormat": 1}, {"version": "5ba43139c886c3d57f2c7a31b7a5fa2f0dd1f9da836b1b7e6cfbacdcd510ff77", "impliedFormat": 1}, {"version": "f3a9bdcf6d80e938918f8da065456ced3d06c6feaf0a7d238facf56c34ab63a7", "impliedFormat": 1}, {"version": "18cb04ba74ce655a2d4987c4c13af903e8769b18a62f027c42ffff966ab7ec28", "impliedFormat": 1}, {"version": "b2936d46b233beb8dcfb6197a7affd62a15c4fb537bc9078691d526672b6100e", "impliedFormat": 1}, {"version": "1bf42e9bb6118d86c788e73a5557bbefe8ade252d73acfb5c3334830844c853e", "impliedFormat": 1}, {"version": "e6ec451de20b5fb79e370b6349ccbbcb14b956795039f2fb2d8b41de788660e8", "impliedFormat": 1}, {"version": "3c27a0638843e7d52bf65c15f4ab2cf14bf59283f33d42e5a1c3ade9c8797113", "impliedFormat": 1}, {"version": "a83bba4791fa72490989525be6a09612579e52372af3986894313dd95f02440c", "impliedFormat": 1}, {"version": "5fcf737f56559135e12017d08df4954a742e1e0e34546571a103b54148cbf26d", "impliedFormat": 1}, {"version": "75383d8bc4c00102ed03ffb72b4e7032cdaa5d7d19304cfcde9d30c2c4e4b6a1", "impliedFormat": 1}, {"version": "266b4197b1e778d8bd04f87d9d9eab4f07db9de8dca7a0cbee13a82dd697877d", "impliedFormat": 1}, {"version": "d0d225f3d20e063e76f22e9ff41751393f52d532b0e2c090202c148f620ca8a5", "impliedFormat": 1}, {"version": "70918fb79668cba2152739092fd9b8d6aa526f7ab16742d255a75d3dc5f552a1", "impliedFormat": 1}, {"version": "3ef3a13529463ae5a9f71e32508d3038273dac88de79da10c69ca4d39de7fe34", "impliedFormat": 1}, {"version": "c84bc8b7d93a6f87b53f0913dffd692b013e2ec29c1025a63c16f2a28ca6c949", "impliedFormat": 1}, {"version": "9bab019bed0382a5499940ac3cd414bbee242d14e3fc0f2cfa1149a10547fd2c", "impliedFormat": 1}, {"version": "2f3bfb8b2968e8e82c204feaca4f2ed3030b886fa8500a271d9df2ed4c59dcf1", "impliedFormat": 1}, {"version": "13e1a28078841ebc6275499cb1314bedd425998b719949ea6ca6f4072a9421cf", "impliedFormat": 1}, {"version": "d6c4a53f7630b1c489db74d29d97248bd64dd85175eb07a43027baefccac0502", "impliedFormat": 1}, {"version": "251e3cde428b2d3eb8d515b1fae236469f76921e4b84bc7971fcd7f7a0ba97d2", "impliedFormat": 1}, {"version": "0e5beb987094cdd78231e73b88da51f4305751e9d3831642b945adbb0ba35641", "impliedFormat": 1}, {"version": "3d2135ac5b8b5832264990bf9c4f9bf065ac790d1fdcd8a201d182fefd067578", "impliedFormat": 1}, {"version": "7d29ec8698e7da1c7021455e7e19290c4029a039f214ddea1501ce3a31420e4e", "impliedFormat": 1}, {"version": "293e398e8b8308a6ab09d45aea6390df7782f64ed9fa410cecd507c212cd5e2c", "impliedFormat": 1}, {"version": "c157032b7f1c1027d8adb59e54c51776c874740e89922578ea451f92e07884cc", "impliedFormat": 1}, {"version": "f0d5e8f2fd30950ec0c446fcdab0caad38d93c2a224418ded899845e1249bdbb", "impliedFormat": 1}, {"version": "2c924e8de0b69940602e43e572bfbdae86e0df44b13056ee1f74cdbc97b1527f", "impliedFormat": 1}, {"version": "17864c2191f968c889ec449fe28848b0acd95b421d60a6266e68b9cc20c8b600", "impliedFormat": 1}, {"version": "46da5f627dba39e128c8b18e63788db5363fc56f9b8ab3bae2c834e7182af94c", "impliedFormat": 1}, {"version": "524333ed3cac525c792bf3bf1bbbae3fadaf4a02d6346561e8e2291b49b2739d", "impliedFormat": 1}, {"version": "c6dc775defa58432465c88c7cfcab52974b93f8fc344ac2a848a6baa8667f5de", "impliedFormat": 1}, {"version": "04d98d047371661ca38e70ccdcdaef3487f27c65e243972ed43ee2fb30cf4677", "impliedFormat": 1}, {"version": "057210af817106d0c11637613feb80efe81597e80c09826e3d586d887ef9c7ce", "impliedFormat": 1}, {"version": "34445fc37bcf4157e3db40ccbd7e585ea5cd78575e57ca40de40c7bcc250b242", "impliedFormat": 1}, {"version": "7e0801db65552d2ba7902b6966c11a8e42a4b25cffc07944f9d21df7ffa5236e", "impliedFormat": 1}, {"version": "ebc84be7fec5d9282a698fb77d74e5e5e8d4ad61c43c6e27d1f9b1313a193419", "impliedFormat": 1}, {"version": "7777fee0b73dbf1604afc30bbdad7f3372f91f56c80980e65e6954a8bfdd6369", "impliedFormat": 1}, {"version": "2e0d41d964fb93d4d79789958f11cb3d46cdca3fdf192ec79ae1bdb1b3b63961", "impliedFormat": 1}, {"version": "4db7173a18a6af913b07b7b11e94de88d11ac4271ab9dc85d96492461cbd734a", "impliedFormat": 1}, {"version": "0e899f1046bbab07cbda4a714fc45b9aa1356cedfc301e92eafccfddec029a84", "impliedFormat": 1}, {"version": "99398c11de737c08ca12e46f825433719c2583f5c5ad79d07d85a50703fab993", "impliedFormat": 1}, {"version": "68eac6e1e2458dbf974d4448f4799fc996f79c8a1d730ef70770a10d2ac58ebd", "impliedFormat": 1}, {"version": "29b76811be6e0ce8cceb0a286f3e41a996ebd89fafe327d59660e4ad4add61dd", "impliedFormat": 1}, {"version": "3cc189d1560724be6c605a35aa970560b09725878fcb3db88b6efe633e57d42b", "impliedFormat": 1}, {"version": "9501a95be053049b70d600fe071d2693b9854e45c6244e82bd5000f2187994a7", "impliedFormat": 1}, {"version": "a19853c5844ad6aa07cfafe35e9d06d293e095778f0bc3056b7ae4d9c18c683e", "impliedFormat": 1}, {"version": "1b2c1ed7f1ab48de8e6dad638911a303468c33f8fb348ca2eacae20695669145", "impliedFormat": 1}, {"version": "fd6f2017a1f72092e88f4497f0ea034918fcfcbd33d03d1e8a44ac19adb8fd8d", "impliedFormat": 1}, {"version": "214248208c27427623fc6462fceabd241a1dd19d8aa5bb75c2f0e5767e91f5e3", "impliedFormat": 1}, {"version": "ea36efec9fe424831dc9c42efd820d3db5c2d28c00ff8608ec9d25f55795c9de", "impliedFormat": 1}, {"version": "fd34e1c778435f973c182b7132782f3cf7a194b522f1ea09978c37d8fc0acd85", "impliedFormat": 1}, {"version": "14903c96a1e9fdc7057e6ac5887859e9e40c8f32a4bd96a79dab42ccea83c9bb", "impliedFormat": 1}, {"version": "0be995fa132ab206c7082feb314fe980b0810a20a8ce9f4c62097774357d5084", "impliedFormat": 1}, {"version": "12d982374f5fe9b7dab1c5057353142096bf2645bf35843ba25fa3b8507d71e8", "impliedFormat": 1}, {"version": "a7f2d46aefbf412cddf56887687b907d8f978467215a0c03947f674187161530", "impliedFormat": 1}, {"version": "312d803e3a4a8fcaf50557feacbc99b27d3893940ced758979a17eeb8374ca06", "impliedFormat": 1}, {"version": "5f2e3c41a86298505ded2ad2d598247b789167031d67a8d178601d3165518537", "impliedFormat": 1}, {"version": "e91d36aa7649a5ddda296019f47e05cf6555907bae77223c4c95f2c79769edda", "impliedFormat": 1}, {"version": "25e3471bd16a78541a0a4ef9bb8b77abeab3cfc47334e012b0ccdb427bda4542", "impliedFormat": 1}, {"version": "020fe4b9d25a541f8887a672b77a9d58352ac4027c29e96898254c6b299ccbc7", "impliedFormat": 1}, {"version": "0b0a7c93406a9ee05723a5db12ad2c6d78c9fe5e88254607b26ea6402d404ab0", "impliedFormat": 1}, {"version": "377b7609a77de383f3d145303e002833eba77804962a6468a7bc53adeac0f89a", "impliedFormat": 1}, {"version": "5b36ea6c3c58a987980aefad618a6673f61d2008f66e10b8d15f3ebafd969009", "impliedFormat": 1}, {"version": "28d01090136fcfd6db13d00e014ce2254cce0665c3da239295e2d7e336742c07", "impliedFormat": 1}, {"version": "aec08a458ce9ea11c13f74badae0aeaf8c292d5737b56cd06c199ce8fbe805f7", "impliedFormat": 1}, {"version": "452fcf2c69908d1946d2f02cc975f450b2f70585eddab63dcf70e050e37b30ac", "impliedFormat": 1}, {"version": "e80599bf4ef4579f28b0e623a64064a512330584dde58875b498ff8da7e347f7", "impliedFormat": 1}, {"version": "266ecc82b92c5d351efa6ef4f4aa726dc487046b2a6ef75ddfc8161eebf8e6d3", "signature": "5edc49c5396e09950c36cb19a92a3c2f3480cfdb06b0af3c72843ef9037635d9"}, {"version": "8f33d56233992bbb3dc7c5dac164a858f655968ee649ad4ac24872a6fa897f6e", "signature": "a0053fe9fb42ddd282323dfa3d2fee2b4d027a8d1186ff98589b66ed40bbe9e9"}, {"version": "9e4a643d9eb38779b452b4ed8ce048de3654add7397b538afbecf6bd925c57df", "signature": "ced621cfce869e3d6bf7ad857d722e4d8a6903220a19dfeeedb3cbf3434979bb"}, {"version": "087681a5c0e90ab784bd24c077a63017c399c9eabd7b0e6c3515a79ec644e43d", "signature": "72e3a6ef80de1b0c74f2b6174afa7a1f568edd571c63506f1adfce5daf633baa"}, {"version": "c0b83d8ae5647e0c414c1765e13633ce26b3e1838a75c9eb7fbc38d2be58ef36", "signature": "92f0ce3c6f133adca27ec65b3168a73b5ee27bdba619ecbf6045edc4c2702067"}, {"version": "26eb7dac4d240b77accacfdd591e544d80682d0b13302048806d0fab1b6fbb84", "signature": "11d2b4950ebc3b3496cdfe39ececdfb5b53aa3fb77fdf9bfec6fc6bb64284cc3"}, {"version": "d48cf52b7bb21552215692e9943a95b38a489d012b7e09d5537af76f17d3deac", "signature": "ccc4ca8f2cb55d6d545e8cc57dd7c36ee84aaf45795339dbaa3115971083bb3e"}, {"version": "a0b091f228629a964f59424d41f9aa3813df3917bd6496be1d8b683606e8470d", "signature": "cd9084240b7cb7aca8367e0d5d248108e91677d5bed040289d524b79b04b0ba2"}, {"version": "10962e88e109b10270e7d1a3a137a0844ba71c13051f974965f5f093be551f16", "signature": "69865e0c6bc189d8f23f0a75f1a3c0c7356692cafc987f365621715a24f3a183"}, {"version": "3381ae22c0932ed24381f749bc91beaa99e6a08695aaf1d807e743056c2543cd", "signature": "d7f6f39d73d506fb2f584fe3aeff95ed369990b50667c0aff649284a72b2ebbf"}, {"version": "35e0279cf6c0b05868b3b0381148348fff0de665b10f45061a336c1b64458bfd", "signature": "0ce9dee703725dc54cc62043e74d5173b48c7eed31dc194c05dbdbd7ca7434d1"}, {"version": "227f806923037faba206bd2e4212545dbd8a11c4fd24d1d0ecb7193f96a088a0", "signature": "b8e330197132a56cc8392e5672ec7b25644eb85f3149f13f28ef8dd2f4449e6a"}, {"version": "e6783e18e0d4ceb8d1b587ad9b8ca503c35e3da3ec73486e278d41c68268391e", "signature": "29be402f3e482570ec91de23e53dcc3d19b3dd74d6edd70caa552000ab8dcff0"}, {"version": "3d86ce8631612c56711ef220c85fd9dde1f86c94a3b98883d5d75c45ba89b5f7", "signature": "176d1f4ef41f528b712916c30a112d0f366483273e0d652b99f5fd2aa141f356"}, {"version": "b9baa2ec10ee2c821087d3631272da57e8b3d4715bc4c10d6aaad636bae64218", "signature": "ed60343f5b6ffc73ff1698e44f52a46285b461fb45d210e55e339d2647a06d6e"}, {"version": "1432d5501f5748ccf7f67c89789a35f1b10fa46311ece450f533ee14f579cb1d", "signature": "43aa25660aa2594bc573c0d5fd15bf30d3c095057aff8cd19d5c033d496e9b95"}, {"version": "15f56c92a1f49c0f9281e0d498af84b9ceaadade9b02e301fde27f932c4c6dd2", "signature": "6dcfafbf124d5821f98c219f5c2b29b3e5128e47a305c87c5e5dee8c7ca220db"}, {"version": "5d31c5699af99cf464ba3aa9fbd986328f8d75fc470248950c712093d4b1c97a", "signature": "9ae332420040aba67ad5560069a29969053b283aac8144e5a45e2a59137d7a1f"}, {"version": "43223dffec09ec9d7391406cb8217a2f8ed3f52bfde80c2710bc01fe2e3e2a1c", "signature": "eaf8c64f7d6c3575c6f28ff421b6ed62e9760340c9421b654437de3391eb38f3"}, {"version": "6518ef74c64b8f061007ca3ee6910e701f6a579d1762e197527fc0b2ccca7014", "signature": "7911d1e54f29b454db85f9886e4f1d4795afd195a0e4e317d5406e1efe14732d"}, {"version": "6f44b3c1bf411041728642fab07d547d20c479b4ef511a394eee940e1a5bda5b", "signature": "0980b09918ac0dd97e1778865bf3b88bd7067991b585ec80cad68fb7910492ac"}, {"version": "81af0696d8d951f3bc956c26f3d9cce805260d7932da499317b2a3ef3b484135", "signature": "68102e8fd56969ff48ffc72211c70ca3432152fd0f13e01616fb4f7bd3f70f74"}, {"version": "339f03f8d705305051abf618ac69f6d2aa939641a976fe86a0f748c1bb12194b", "signature": "53979b4d24198e290719a045bc309b93d75b73584b5028778556c058df6849b4"}, {"version": "e6d4df051f5e3cbbc87622b5e3a2fdfbd4be78eefd694c78614356beafcb80e0", "signature": "73f9da443096f7577034eebacd6bbde875b96bc540c6f9ef22f8b00a49247773"}, {"version": "9cad081f05b29b32502b077df69a4b152f5f8befefde8a01ae3ca80caba3a0b7", "signature": "7c82f227e015109cb3a9074c01c797c3076037c5c35120f46e61cd67a4ed17f8"}, {"version": "d0b57a1fd085f557319686d07e8e24b2e00338513f7fd41b071b7380590045d8", "signature": "b8a022e04e85f5762fff25890cf0d48075a0ef792b183acf859aad3c4807484f"}, {"version": "cfc23db1bcce095eb6b603d5a8b39b2e079d55a368edf7829a2fbef038891d57", "signature": "f096212a5160113b5a316f71104a0c7b0f0f8ac3a907a9b5e3728ec60a02efdc"}, {"version": "d143bc15373432cb3f90a6f9ef343e5720c3b90f8a3e6ea03e25e8dd3495e98a", "signature": "bf197c92bdcd6cd802c513088748ba8dfa79aa78927d7d92eb50016b2119804e"}, {"version": "828aec1fb283ddfb232c26d67a78b6b7d2cb87aff237582f21096d453c5ed150", "signature": "2cae4b24cefe0cd02c21b4c5854bf2551a63b09d674193c6c823fa75c9c1aba7"}, {"version": "03b385e89c8c9677d287f7e62779bea9dcd47098cbe3a76a48367ba2df5e47cb", "signature": "ab8920178d1c26a3a1ffb87c925162a059961caad26fa9be5a17e5d49c53c652"}, {"version": "8d6dc6334232511e14e075207e4f37430ea2747cccd5d1fa1a5164584d2641b4", "signature": "5183d558377fd988a15e6fc60559f14467726a44bcca7e045af70811a044c25a"}, {"version": "5ee5a9d3f7f7690310fdd3acb7d57247a6e4d1652e1dc8f4db4a654261e7d5d1", "signature": "3cb18d10ba8594ebb1b895053e482e45e433e4c88ae99a6ab2d167c3003cc509"}, {"version": "5525cf43e779439083e8bbc32c203365837dc6d5abddf5a763d2d23d2947ea95", "signature": "eeecd31c7b8ef34dd4c595c4eb2f32254c69f86332a902632edbde2d356c05eb"}, {"version": "160a66a5832a2896a3f9c892faffe9883cd684aedb96b89e32a0e3f97738af9e", "signature": "f8d36c3e83441dd470a8448127d8249eb4d45297663276e1d2b37c4b4814481b"}, {"version": "77690583e9a1621f73257a60b8e60b270a2245a3fb6a66ddea3bd65fa7b076e5", "signature": "e2c7bf6c4fe4bba2209b31a879058bbd392bde0706f9e4a9003eee291735e4de"}, {"version": "21867c915e4667d375ea0f663682e4e32714e4e2f149360a0480fb6419009330", "signature": "17edfa0d4a203f268957ca9d3b824f08de97013256c560fdd1796ed72ad9fcaa"}, {"version": "7b26543b2bfdb8ac9943fdc7879920fb84903ad1603231c91449676211b1cd28", "signature": "3bfd5ab7987e8029f8eedaa68736ea11a1ee4a32dfb44a5ac799cad680c71a6a"}, {"version": "f8b8ad681b6829bd7a65081ba43c7d7c29a379a0458feaf8c26e9aff242c7e54", "signature": "951a47439c96d6ade4374b996932b402baf20e3634e5410d0c5fe98844570fb4"}, {"version": "bcd91816765099238c9316eb5c89a6483724dc222ecc569a724818900bfaa1b1", "signature": "6e80d7b04cb715c28c46b41632b2f41f1e80b2c99a0faa93be1651df618d697d"}, {"version": "4887bfd4ef2900c0d428cd76a2cc3a2c4127f9d4b1942d20d1fae89468166a9f", "signature": "b466040d74650f88737dfe46f79d5bc017b9019884d851151102a37c90015403"}, {"version": "09b009e36f15e35287f08e3c8acef46a3d1253fe5d282c45a7e6ba6b29517cd6", "signature": "fc2f1f92fc11dbb2d7cce20a3b6f6d645ae9d29ded3a9710c196734b83eeca6f"}, {"version": "6605026876f40cb64388a6527300962985f13a0ac76949ccb49c92e4b18ff8fe", "impliedFormat": 99}, {"version": "3a415418dc1d6e107edf4c85aeadc137d7bb979c7f0c450eb377f94d271844fa", "impliedFormat": 1}, {"version": "a0db8c2715686497cc60204f86ae56ebed6854f86fe707a4343639a4bb89791b", "impliedFormat": 1}, {"version": "1f5299fb3b87f9c8db01164dea8fcaa43c4fbb12ae676f4ff3c7b391d3f4b2e9", "impliedFormat": 1}, {"version": "ea298815fdc0042ab8273c1acc981d7758aae64e9458e2258528dad2e4497fde", "impliedFormat": 1}, {"version": "fc50b78bfa0003bbd665d6124faabe7b6819f7072e83b2087b7923c4aa0a3210", "impliedFormat": 1}, {"version": "1357b92c7087a9a21a4a5e8ce9100ec6d7786cf342e5a80be5904194317e4ce1", "impliedFormat": 1}, {"version": "db23b081bc517d635e408c7aba4558dbd6cb3402fafd63222b0183c64abbf098", "impliedFormat": 1}, {"version": "51131938f453934581fc7f290306055ae5b50c3ad0352bf4322355d87bbb3e91", "impliedFormat": 1}, {"version": "ae9b51753d6050470a7f7ca38eab5438eb80ca825bc64721bb42d9674cdd6d63", "impliedFormat": 1}, {"version": "bbce586460c843c6f2974ae1ca497b963ce0cd24af104e8fa2361a0754494e4d", "impliedFormat": 1}, {"version": "ce6871d7bfbee883d1650658af8424ffbc145374f75a729b502fd72f29e1ef70", "impliedFormat": 1}, {"version": "2b47a49bd42c9af43f6eb9e8ce6324449afecd1955c73b4978a88507ae649726", "impliedFormat": 1}, {"version": "db23b081bc517d635e408c7aba4558dbd6cb3402fafd63222b0183c64abbf098", "impliedFormat": 1}, {"version": "2fcbf1c3cef9cbdd1b284ff3104022158b211be0396c0805b1f51dc71c991270", "impliedFormat": 1}, {"version": "aa662c9b4ca2d53b3ee48971eac73de381b782d464c70cdb4744714aceb225bb", "impliedFormat": 1}, {"version": "b0404c866ae7550870713baa39c7e3b3ea1912bda0f4d40e7036204c562c9603", "impliedFormat": 1}, {"version": "51ce6466134c176144476816f0649321bab0bcb515d0f558c1e16094fe8ef152", "impliedFormat": 99}, {"version": "900d25f20378bab32b04eda49a3a0368ac2f93d0754bf1d611a005a9dd3cc2f6", "impliedFormat": 1}, {"version": "5af147341be93156d6eb383a6eaade67cf4bfae08bcbd51253a9e3e3fcd7ec4d", "impliedFormat": 1}, {"version": "d9f3dc0f34fcf982bf4783e05ea4b63e0f0fa9fdee6d033ce1ff4c79abe61173", "impliedFormat": 1}, {"version": "1c3a0201fadfdab8ecbc9c5d54d3bebc86f6852b2752d1238de8072d8083522d", "impliedFormat": 1}, {"version": "87c70f85a0bcf3d574481ed90c11b5cf38930f0eff4607b1f34719ce8f65bc39", "impliedFormat": 1}, {"version": "db23b081bc517d635e408c7aba4558dbd6cb3402fafd63222b0183c64abbf098", "impliedFormat": 1}, {"version": "e06f0746f3bbc97f5be3abf0afc020886060920a35adcc5ef0e84235f3287b49", "impliedFormat": 1}, {"version": "2d8e1e60b2d19f4a5f3658bfe320255f512917a2d64316de93ebdcdb8579b2e7", "impliedFormat": 1}, {"version": "160f2e2e49fba05a642fcc9ed4880d74e9d1958822130eac1313977976c927db", "impliedFormat": 1}, {"version": "b9bcea9ecdf2ea1fa590bc02e554958a04eeaa5f39fb371c7cba6742665855ee", "impliedFormat": 1}, {"version": "fdc134a7841b3a7cd4e78827c9af0c5b59806e121d119ac8e522b9a66f41800c", "impliedFormat": 1}, {"version": "e00ce021e547d41860f0716717dbc617b54854d77ed9c76b89b44c89295ee119", "impliedFormat": 1}, {"version": "fe69378cb374942b1f03ab3482a13b1edca644b796b8ffd69b109b1a6dfbc801", "impliedFormat": 1}, {"version": "db23b081bc517d635e408c7aba4558dbd6cb3402fafd63222b0183c64abbf098", "impliedFormat": 1}, {"version": "22486bfd199d4a200a8c019bfb34efe3e1f234b5b9c7b1b7b50633dee4033965", "signature": "c6f9c90db254c504acaf905f908cfcc969eff278d4cf46f887eb618de9ab8a9c"}, {"version": "1958376f809c43df96e1961be3d7e7811c553f9fa473d2f01a9fdbb00e5b4d01", "signature": "fe3de0021830046fd6a433e65702332312d8c9513463bc4c99dc397ed1216ebe"}, {"version": "d48fe81270a9c7f7b7215107e3340065c6e7393f4812c57007864b5234b46a2e", "signature": "7ef8e6a2fc17c15e12050137f7f5a8d2b450e7fa2d4f20a826bed4f61beedd42"}, "71947b1db33234aa7a0b58adf83dcdacf991e1a131447c55abe8a8a77dd34b2c", {"version": "233b1365635ea0e5886830b5af66f5258923a73d95252d4258bfe5098ab117da", "signature": "d41947a78238e01bc8c4d151b9f93b977ee905c9acc03385c2d5777f142ad927"}, {"version": "7acfc4964739bad1a300a0db9e485bdcbf920fd6448db005f29df0cce4308084", "signature": "c0a3f1dd311e43682f8617aa523172124606c45b72e32db3f974b8ac698f8bc7"}, {"version": "820a2affc71bbf2c09e89420c50c38ad8a3e0d358ae3b1ca2945d6bc6b2e9833", "signature": "e15a8e680c8d9a63b2a271ad90787a7c819b40618a5acd59f5104cd2ece71909"}, {"version": "9dd1f46275978092fe2eb448d62510952cbf353d303770bebed87f365995c17e", "signature": "9aafb58572e481021bb572366291f9943068ad0e0d5deba79324eacb8e99fbd7"}, {"version": "459b0a8e5fae6793506c63e03045ef175a24347bf0dd54ba04031b97cfda5df8", "signature": "72ee5da64297338f113e1cf1a4aa459ec0cfe9d59dec95469ac483e1863be7ef"}, {"version": "8bb127a4d03c9c9b3b56a4f56d084715991ea02b3831688d056c0aca11c18bed", "signature": "a113597723daaf6a49c92437eb6613c9f76f9b3a83f51e029ed14dfa35496238"}, {"version": "957a818eea89bcf8a11baab17878c8e43d6cae0a18963891578a853e0c47fc98", "signature": "39b81374243b6e273eea2824e66d8c60f3dc73df68debf5f2251ca8a0e149099"}, {"version": "e7b30eb3d2109801143b210328e71ae9f4f9cc59fe1725fc7570de439b8b438e", "signature": "4ed685e4dd108fe57eb17d943193eb0fdccf915248ab2daf1b8a82653b824104"}, {"version": "4c26f665d2f305066727a75db69632ba793ecb81c31da5e34631560380314da1", "signature": "9b8989dbea4b77d5f832ef5ba10d598d6fbe1dc0c04e030191502d412851a4af"}, {"version": "5888bdfc8bd15a898e25c4612d139a1628236e355da1234c0739d4d78b74552a", "signature": "242c59c7cdb573bf329294e98b67c5a704a1ea637cf9e0475b9698e7f011623b"}, {"version": "bf8fafe68dfbf2a14b2587818bb103ee4eebcaa38ef9d545040ee9cde7cdace8", "signature": "b589434a412388973d39cfa29ee48627fb882f76311abb0f0ba93a5b2a485797"}, {"version": "5257b7529d94e0b0acedcdefa1287abd87f1a6e6a59158309937ca32c4ae7dd3", "signature": "ff718d381f13030396e68797c2d9ada167f920c21c40b2047e46dfbde815182e"}, {"version": "c024cedd5d4e75c21124a0dc0cf359782587c7b8026bf80f841ac2ac09e085de", "signature": "8289b9d60f23a12d9f0521925dd6bbe5f7fabae7ac0aae3ec9a77ece035ca9f8"}, {"version": "ddb2e280a484123fa7369b381801373dca7fc40159bbb624abeb797392ab36e3", "signature": "39d914ef2b2ae942640a68d26c03f6f861c886222da4fcaaca4d329975ca1736"}, {"version": "826e226ef4293d2e692c796d7cc81c94f353c57fd6016685b79d68134e5df047", "impliedFormat": 1}, {"version": "24f73778cc712f3d2ff4b69227957577487118bd41652b63c5f913ba12f7631a", "impliedFormat": 1}, {"version": "b645de8e8084e4cb7fb9ba2d8e3fd893dc3b5a6568fb7a5027aec4f779ef814d", "impliedFormat": 1}, {"version": "6e88e36ca6d6953fde31898701899e61239b22f09906d5970caac88107a4b01a", "impliedFormat": 1}, {"version": "8d0b0656ffc2f973bdea853fc00a3f99122db54fd7a0e13bf87d6f358fbc3c6e", "impliedFormat": 1}, {"version": "5ded11c97be84de8fc472b3639230daa947232f25da04803508539880e3939aa", "impliedFormat": 1}, {"version": "90e654b460f8ff7a3ff73ec54996290afebf00c8acc0fc634961af1509416db5", "impliedFormat": 1}, {"version": "81fdef0cbc4aeed4e29f7ed43d7b768ebe39e068da55c95bd710f47be289d6ac", "impliedFormat": 1}, {"version": "17d66f8a355e76a5a1ee0a82e3428541abdebe80db47f93586a21a2cd79d89bc", "impliedFormat": 1}, {"version": "e4db201366aed6e8ec28c4f9e998729477b0825ae7e517441e8000f192b8b033", "impliedFormat": 1}, {"version": "20fb7144dd7a3e5007cc44e96c843879216fd082bf5761bd420715d645eb683e", "impliedFormat": 1}, {"version": "026742bab94860543fde035a6bfd5d5103fab08afbdacf9a207a44b2bb6fccba", "impliedFormat": 1}, {"version": "4b0129cc010964d404b46ab0ecc2017e29e61be7f243a2f4b8633de40c6bf632", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4673a1ec28e3ac7a61a8244cfc943706374b390e1bdb4fcfb2bd68cea7ef5298", "signature": "a36523d0c5d8b66bc4536d66bad5f4e2c9ea8c05ff63acebd6a7c26b0a4531fc"}, {"version": "ee91c9386bf42039140be109e76d49afc236cb2d86a8b364501555f65a049ce4", "signature": "7e20cfe989916f4d2f3ee75d7a16be0cc58c86250171928bd0b6451260c1c1de"}, {"version": "122027e1c48560e6448bd74158f0a417959b9d6f1a8695d8116378bb1498d6c0", "signature": "88e537f12c77ee7f1233e8726d7309c4006fe87db718f770ed20ddcde109e0e7"}, {"version": "fa8f1e27271d817048a49b925783eab4380f7f104949b2b9f87033cb51da3ca0", "signature": "49eaa62237b2b4692cd6573eb8b2ffd0126374adebc00732abcd0fc2d0c00493"}, {"version": "ad570f1d3235b87f83c1ab93d01c7490d9f968e1289766cd0dde6b3df89d92bc", "signature": "8dc2376ae748170f516eb3520bc01bcc1e184c986bc8bd97377a8ebeaeab40e3"}, {"version": "1c01b93df6b39205cd7bb0e261334c2f8d8148071918d30def114552cea7feea", "signature": "16a36d8b5e7f9b59bcf0bafd895b4c541c539fa88fc49c4b489e8f8a3bd2bd9f"}, {"version": "ecbb86ba3e97a368beb5060aa749aa1dab7bbacb513369c9d3f10f4da8312765", "signature": "5cf0ed3a53253decf28dc9fbcc6d33fc2262f481a66159a063d9f1b7b8a5491b"}, {"version": "b2733d4a162b7a87d8d4966000a1c1f8bf9bb12ca0bca26de2a01d8bec5183b5", "signature": "b57c87399fd7dd448f85b7fc3f8bf6a453703a2f85103200c92c63a80fe3ca35"}, {"version": "777b82ed5ba9d45edacc3bae5a64b032ff3f401d610f1ad9fb8e2c1d69d44035", "signature": "c289a7211e6c24b3d84331e61ed40d28018b04a3afa644a4468ddc1df9ecb956"}, {"version": "13990a518f2eef97f810a81e9d2fd53e46725f299bdb37e264e05649deb668c4", "signature": "352e1646fb974d5ca77a08785bced3cb2ef155eeec5c346b8ab23fbeb5ecd275"}, {"version": "37a845fdee14d4a72067969f637b87108bf6b749537e7c5118b4705c8b92b4e4", "signature": "e28d936010a0af81f2258949eaf901df81eb4c2092b2a7880e99059cbdbd8d0d"}, {"version": "57977cc584b2a88a7b79f93f6f531714a42e21fdc9cca76234e30c6897eca0e0", "signature": "225e4f8f9e21df91facb57be0cb9e8b02a6072d4525a1a443fd2f7992913ebef"}, {"version": "34514022c6db91826a4a737ff035a076e93a843ff47c924b926469862ce87452", "signature": "6af3d3c73e8f6567637c913ce9e8c63ce9278e5afa1f9c92de51914b6da374a9"}, {"version": "28ea70f2366908f02d7566c16353d174eaa93b8915e594147e518bb90f35ccdb", "signature": "0b9e3355daf159213e6ce40908ba9b1c96542cc2480b3c17ab0f8a545a89d4d5"}, {"version": "13e491b95f368906923e3e966441a54181fe9a058559c3f12959b0ab373a4312", "signature": "b326c15c37443ace82dbaa0e328a6fd18cb254159d6f64226e3602abe0d5439e"}, {"version": "ed896776fb60eb3174dca9b97196d9aa4c84b69a61276ed682c045ec005c9926", "signature": "dc78eb0aa4a2be0c8265ffdf681e24384fbd902953e5abc8a2f3cfe3c7377553"}, {"version": "3802c07b65a324dca2eba044edb70b518c9a996ff7a5e5a9b9f254a8841a6c8d", "signature": "edf9ce0dab1c146d02830d0796ea414c270e7c7ce83c604df1dce181859f1dd0"}, "7ed6cc40c4b76f8ddab8a33dd8a745977add42ec752eca0af56187e9c638bdcb", {"version": "2dbe5f670a6f0c5d07b77cfecdb3ef5452a420dd25dc9567f8cc2d827d2969df", "signature": "be6886e2fd6c904d4716ef20b37dc0a67be3da98aec1f6da3cdbbc5fc339fde0"}, "f0a05ee02d108bbe391b21b605fb6ec26c01ea07a7910649d021aa204d7fe919", {"version": "0de238faa13efe748941c8ff5d30ba20a3eb59e2f3d53d3927a5339a8816b177", "signature": "20a4ffb87e2ece41b729466e8d99bc721f0e2a58f26f68bb23fa21ba519e7e40"}, "05bea5c221f9492413979a264e98ff878ba287a546fa760c31303b47c28ca442", {"version": "bbc88545afff0efb5b6158a5897192b102680cd8b5925d0859a521e3923639f0", "signature": "d568f31180dfa918e4303135be2b7373fbaa9d66e50f80765c4758596e2ec06f"}, {"version": "e18adefbadb60cac6ab3e5d15ba1fc05d6a37fd93859ab7106173826804e79f8", "signature": "1fbed4c96afa278d5d35a0cccbc81dfcac61882c0986d70ffa94cecce9a5b7f5"}, {"version": "d570e9462bdf4a1d1a882e02c7234b8ccf0ffc058c5bd1517100ac64ed7b905c", "signature": "d2f4b6999cc657cddd5312a4691793e73e362f13e2aaec77205b79c9581ab74e"}, {"version": "ea39901e5d3e1c171da5c2e7c3d3b98ccf9c10ec5c987b30283ef4883f549c47", "signature": "52d84c88f12c0be2209fbb2f922384d7f98cb90300438618d863f29854d0a978"}, {"version": "49f4936852d7d56d2135e9b82af989b90f5dafbf3e039d264cae14aecd19b679", "signature": "6a6708fc68dacf437258843bf41f1604fae1fb1b8fa3fe67e597b0d03921dd5b"}, {"version": "7c8eb1034d50bea066d3550f7d0b7ac892bac3e6636a5d8f6aa77ec756b2cdef", "signature": "0ee89a7f9f099360a0c18a1950ec30e5548e13ae02c69393f23808eb0d05d167"}, {"version": "4ca617c2f3131f13769ec0c9305171f3843e1bb29fe30613254cdd409d409e4d", "signature": "72bb174f4dff5996b0e6d10396ea2d9cd552f1b7f91def5c5544cc2a0f635ebc"}, {"version": "f3c2b33e8c2a7830b43c3331b379dc97483468461031a8e18e4105b2acdd9f70", "signature": "81d72d0be2fd6ac26cdcef96590add6c4e405fc434eb8c4bfb2b42b587903b14"}, {"version": "3d254df56b8c2b5232d973e5c7720ceab8c1359355e787169edd02adf7e0dc76", "signature": "bf746846ea96827c134647bb9c11a34c4c9f1d0025d81409ce8cb48b7dfa8a8c"}, {"version": "ead05f8d88fce1fcf4dcadd888356086fa03653c54bd3ca392d0b6bd6f69de5f", "signature": "8b903d347b90aaa02cbc45a46cf87d626626f6815bb745ebb137c39add54dd08"}, {"version": "c49f696e4c93704812cbf8586edc9e775feb9037db7f66739e6ee7b2899b7e5d", "signature": "54304be102fc10f3b637aa6caab6683c18e0b112fbc1177af5c31089f007d927"}, {"version": "52d54855358b472b2fd7ff3db856bac99a0084eb98d14d89643589b0195ba8e6", "signature": "7280c32316db1e5583a8aa4d92212126a391e6d7c12cc845b68c3026feeb8d99"}, {"version": "2c783350ebdd9e73a00f6f0f8ff1a582a93a719ada22690b80d3f1228a8837b4", "signature": "41d913765f653a3a14cf48065af89676493f90f905e21ebd7392788bf96f552b"}, {"version": "dc5fa6b9df1ca2bcf74c56796a7d7a5981fdf642c2174e6d92de2075a6fb3b82", "impliedFormat": 1}, {"version": "cf0b1683987955150d3ce38c3f1234868a0fc7ab1e59eacb7002f21220304fee", "signature": "407021be9d57787ef913c44937ceb68327b33e2984838c05166b389f314add82"}, {"version": "40c8fb6ab3bd2d3bb4b9d7a6cb3c92079f5713fc1eb03b9436f175ad7f89b635", "signature": "430821c45a08ccddf1671c28264c1b70f6ee71702b0344b6cbd03fa2b5c52b7d"}, {"version": "655d79bb9c198793217b551d701e494619265cf504b6ad80344fc4cf609ca4dc", "signature": "d07d323ddd6ff3f69e2ab876d3e3fd3d1b6f28b3c80a1974c42c10c97ad266a6"}, {"version": "8f32c5481e5d4644b52ec8b81c301cedd5a5a15107c04362e0883c09f73b3a5f", "signature": "c93f150765b866b56e7050ab9ae99f1fe14b1079bc54908887cc2c84ede44140"}, {"version": "0cae2671ca8ceab08c7276e4ece13e046e29fa8d90f7a2a9bc7134163a5b686b", "signature": "be1c2b0a17177efd8faebf58208e9e935b2bde8072d29895beb476ad3318f55e"}, {"version": "4ce79d46ddb390c3b13a0bd0dfe1c1b7c5defec87741a5b294ab71db9964f7af", "signature": "8cfd95519af7534a8f67d49c6d921887e95e3737e725752a5005988b1af6b25a"}, {"version": "bf6885f0869de75bae450e6f7924a8690819188f54266a76b79ff9376174a31b", "signature": "f96483f02b52187bef6b4717960115825dcdc64eb2e33d10b7dd467edb2dd0aa"}, {"version": "defbb543731b53033e9c6601dd3f8aec880b2ba1bfe2a6ce10d141638a06e115", "signature": "41029ea360092bdf9f8a16d7e2e1abd8e15726acd2fae4d6e89155b19ba3a20f"}, {"version": "198ce2da33c4183bab04c406fa01bd7b99bfd5fd31009ea76d0bbf611e1d6e7f", "signature": "97793548aa9c7c91a663811067670cdbf9e38578eee09babe76b323c67ca1783"}, {"version": "8035b82540442a8ab8bab340a2eca428529f018b2632bf7648cda87a4047f6bd", "signature": "2de7d582cc777d07a847293032087b58f889208951ee4db59a027f50fe1715c5"}, {"version": "d84adec0a302794ada005d7914bffd2896b688835e40d013cd5a0380ac06a410", "signature": "e4dc1c9283cc51df5b3c631cc21f4e5424d5ed5e943228bf82d26f2c775b202f"}, {"version": "6dc9c45277c77a9767e41a2523b703e8a3a54c05edfc7de0cffaf20cfeec7ce3", "signature": "83f61ad4cb438c181a13fa98fc0c7a8056cae7f0eaa6ff0edc53caeac56a9258"}, {"version": "31395e68737aff0768571afb7358ff8ed0baf344eb5b8ad06f74bf0e11bf0d16", "signature": "157282acb373f7d6f93ab311e048b5449741cbf0de037c99028ab897179950a0"}, {"version": "82c143b3e6a3889dda12df7b38ae7201622c128f79cf80570233b2c1e4563572", "signature": "34a80464ac66580976e6dc2a5a73cb9dae5e696660d5c492717f5836ae9450fb"}, {"version": "8a2cdbf67d917f11832f1b351f394381afe4e8afabad366adb8ac554888710e3", "signature": "96a07a9b07d64b5cd938b259e0b45ad42a6f796e872ae8a25bb0d3ee169290b0"}, {"version": "c14d79db90efaca82aa02efe36c6ca80b393005c23e08127d3dc3ff4ec56c017", "signature": "7122d6c6366e2c1331dda00d5ac5fc0a5a7a884cfdce868f4a7a5bc71eeba15f"}, {"version": "02cb7b91f4ad516d04293b041563a6ba016fcf09e5881fd5b7f092e44c147751", "signature": "a3bb6eb01c500166471ed6bc5cfcfd2f6a0e51a8590185a5a15882af719bc166"}, {"version": "7ff5dd744199f7d3905c57ac6bc161959a4ac6d8efb9942cfce54f1c4ac47138", "signature": "46cd36a90d45b94f9328beda6141b67a8081d9264d8361100419a93a0ed736ca"}, {"version": "04c214ebafb1995339d3841b92410f04a0c04b880cf8677b5661d5fad59e1a5d", "signature": "e0f93ce7a506cc99e047bc973a19268adf73373012f4bd8fc59b85773da69742"}, {"version": "edb5cc4e2300bdf7f0929d4351b0770143ac95dc1e3adbc0bd29dd3e95eacb54", "signature": "8db7ee7febf89e4390f96d6063017533dec0723d70ad9a7013fb55479c954466"}, {"version": "2f559a1a17bfc7cb079ab94cf00e5b73ff694bbf86f9301f717dfe8d626603da", "signature": "8317a0abe8b2e0dbe7486e9a9008c46c2b528a39809fd2d2f8ec46c16ea11cfc"}, {"version": "1fd8343fa300e405b13f1b4cdb8e733ce31f0a8ab88b1259bf2fe4cfc199b2ce", "signature": "bb36995373f4a2ea130b95f3f5e5a75dfca44b1043770d04c16092f78b8b45fb"}, {"version": "60a8fce8412a67ad6432d79ec199e4988d3afb43775ce54d0373d8fcbf3bee65", "signature": "757db28d9680603c03b7f2d76758182fdfc7068d68b7d58792962f8a366c2e4e"}, {"version": "075b60eb173129b71d9c7757d0622ec3c0ec350b000c3fe359ff8557d1381c3a", "signature": "7645f55ad6d4d1a237620361eb93dd30f0cd5fac97f9b79540cbbb9172ed375c"}, {"version": "8cba144df31a244265ee17bde72269fb2c10bdd2309c650c835192c2a224482a", "signature": "95254258c756f60676187125d6f97c30afb597ef5a2b63b49d0650014ae7170d"}, {"version": "6a71210294c434e2689cfb6dbba639155c80d6d4ca419aad17fea89206540579", "signature": "9f17f9ffdb1289cb3e9b9362769da7dc7b792ba8eaf8e421f94d29229d25ee3d"}, {"version": "aa8c37fcc87e9b5cb210edf84c1c2b69d65b99dc007f5cd454a5989ba2b68a5d", "signature": "150e3899efb311ed0487d9bb94e1d622fba3db6139f2fd14a0bf6c8b3234d49f"}, {"version": "6a17cd40d79bd46ae8737f65f1fa1b0952e406346c16213e9e831fdd0c693329", "signature": "4c8cb1b10ba46d43c607c890d62c3801e306cbe5c25abe02ed00f35d81f0dbb3"}, {"version": "2e9e4d1ee32531fcaad7aa1457bdf6c0b3065f848c2d8705d90593def15748c4", "signature": "a47dbf86377528f181f5b1cc46258b48cb1f4397f55a976aca9d00d22165085f"}, {"version": "fe1230527c5793c8d66a1fcfea532abf05d04d5d13e715e7b8ce07030a0225bf", "signature": "b7168ea292d755a7d0afe3a0c6023dd31f7eb9a64b82b11a24349ab9b000ce6c"}, {"version": "3d4ea216e96ee42e57578cf2fb3028f998e4ff16e3443ab1bb198d9f005dea26", "signature": "1bf68f6a774d0defed627ad41e75edbf95488d249946538f1ed5462731118b82"}, {"version": "01daeab3c0db382500dc0da8314a13955d01dde4b3681435ea6c744486cb8eb6", "signature": "a8a4f0147b83f9cc0939d9d48d390cd21c0ae443ead23dd9ae5145286c644517"}, {"version": "260684833dd4653be349b2d45d38ef1f2bd733cad4154179c5bda39e9ed2071b", "signature": "d74bde8a4a74697d79d9c040c74d25981b17d79600b1e249482aebbeda21ca00"}, {"version": "38e2558b7d04b74b82dab558d0aead47674738aab2126a09121e8e5ff3e65da0", "signature": "2e5369d4d3148b2b2c314c97718005353bbd6ef6041ae6c88b44f08e1e18b799"}, {"version": "50f5d7e7319844b6a84c071422d4d6c7d398043a6885045fbecb246fa6b52c8b", "signature": "e9e53f1833688b22ef952c318b32c2a00ca880ec198abb07ea496ec3450647c1"}, {"version": "e657381a7d36912683fa81d329676cad0fddbd592b40746c0d42ae9f1e34dd2b", "signature": "ab3879509cf0deb5f80a09c643bd675bbb156d6b4cd0d8b308a721ece5093d40"}, {"version": "294a91aa01fdf21c511070e019533482e8994f2a28d7a66988e98a0c5d96f34f", "signature": "428598e8392148100a9826c11dcb05ec52f13166b6af4a6eb104c3e21aa6c050"}, {"version": "25f9c2cfb43293bb9a34596821a30dfac7e7d114c32adf0bc7c2e0eae61b37c1", "signature": "b7c976ac143c45b4d69fed7d3542eb46aa05a65646d08a310cb594929e3f7878"}, {"version": "6af5c0ae24d2cc58631a95aa69e67f9626ace64959bf520da56a591d34683a7b", "signature": "38de06c672e12bcdb5dbc4ca0afdaa64abaa0972026f1586b53ee7c3a7d43f52"}, {"version": "b583b1a60930866cfb64ae383348bccfce70a4a3ab2fe7f9c38e6884a67ac9c0", "signature": "2a7f67b184b8989031b8bf9ad87c7fac8fa6a5ddb6156d7847f212e25036c027"}, {"version": "893f21e9c913947d91a06fca0f03322ba501f31e73c8c7ef32fe53611082dd4b", "signature": "e1db4860867256bf93081cc8fc5a510ef3f12fbe44ab18a4e1f9bc09dbd7cb0c"}, "f5f7f0b947e6a5b1b6b796dea85e72294b91b2fb7cf4a879256e0e20ec60d4ff", {"version": "8426f69256f63a2f4e7a1cb81b59ac5fc2fa54660936cb217d56bb15c6a0f724", "signature": "6c61dcf5013a9501b5313f4790539f479647a1a3fa37d4a59f915d7012ad8faa"}, {"version": "e243ec0d1e0b684cce701637d55a133c7159880009af11e1903cfe3ed4df47e5", "signature": "d2ecf82a15360bfa6609f89e0b6d2ee42ac3010a020f212dac2c657d7f0e2190"}, {"version": "e9d9a8df762e4d612849fd41a243608d532dd10a572542ce6cc0e28af45cd625", "signature": "ef35f39901f52f11bafe45f48f8cdc4a60249cca58be45861b714edd2b36984f"}, {"version": "5b3d2b1756948f724879afbacfffc5f99b87bf0a3d6953bd42f9708fb35b6eaa", "signature": "9802b4c8edb57c9ca3adbadab9f70698f29b3b3e7ff0191d1e313e1058b8a1b1"}, {"version": "5958392612705facd598d27c573326a07958e9343df3f7cc6cd6e89ad298d096", "signature": "7be1aae3313884110bccc7e554655db5ad36a8465852a6fd81454243dac4eb85"}, {"version": "e8aabf0f5c37a3137b2e03d64904bd4dc70e101cf292c349960080ea081e3bc4", "signature": "7192f5dd1fc933603bb0c645cb42784d5aaf09744db8bbafc3204778f5da4728"}, {"version": "c8916a23e7bb12274c895eba996443c32d78112308bd10706c7b4bd1c8c8e1b9", "signature": "3800caa9f3046e7242b025fa91e00d03aa96266a6e25bbc9ab3aa64558d2a1a3"}, {"version": "6e1a37249aa5c78ffdcbeed50a9478002322b57af2c66c820599b067872eab48", "signature": "55c97c2869520dc6afbbd4345c7739af52dbe427173ac29b2a5f53d467489531"}, {"version": "fa30b440580643ea80f366b03daa3ede817398edb7414156efb32fc127f75a16", "signature": "f60d5d15a0713d9c2f652e235321e6f65478f89e49390e09b36d323c412dc116"}, {"version": "8c98b924ecc8675fdd928dea9185fde5c76560dfd46834e1c6d457fac483c349", "signature": "a2df06f617c53ad98e7e096e13364f0e34fe655a9b88b3d65ebdcffb3094c53b"}, {"version": "743bb83c87a4cc441a79cbdeef2d5da37e1929c8742046c45434906c9d697d8b", "signature": "8d10041574c8d4bd840aa6da7d351a12e5a3fb19ee0d335535c17f084f05fafd"}, {"version": "0b5fffb424faf2080cd6bb232f9b4e4eaee886392e166bdc143f8534ad06cbcb", "signature": "a692ad18a78f54d5c90bb9f6da9ac60a080518172dc8fde0f08d5b1857e68404"}, {"version": "4b2871ac559593eea9f5f7240b8722565e133d4308a9bd2306a7696a4a65c372", "signature": "7c8ec99c9e4da014ccf73b1ffccb9a1540b941afb33a97e641ee708974990f38"}, {"version": "f26fd2594de9e468b973c9d9f47760460a89c2819ac229c5457131e281a70aad", "signature": "5e38c5f6d6f2a9fef833fcd36fa2c6684bb0241dcac7383d19c2620c84cdb79a"}, {"version": "f8f0739198dc388e002f0000dd22303ab55d681c793bdd82225f449020d6157c", "signature": "280d210cdc6ce38471497bff66ca63298903e8c87b7abdd513ccd2281a73b6c3"}, {"version": "c1ba83bba8d964da66c6a3a632f33f821fe3cb047b2b6194765476e3ceee03bc", "signature": "4332abfa89c5c1b50ac509d3aedc40227dd1162f5db8b9793d6c25ef10566b42"}, {"version": "b62ca1707388e5a75c440331689987ff38408e71d6ec18cdb598f019f6e0dddf", "signature": "977a204aa977e79b00f555fbc11ba1173108d001b2355bdb47a625f0b2f51874"}, {"version": "7a53518fe374944e6db5933ab584763b31f3be86c9604f1f9bcc498b6e7c103a", "signature": "3e7996fd34f3dff19ded33258e0d971133a7b9834fa9642b3b8e8651e8cbe77a"}, {"version": "a4d9724f1745402cb5e8ecc6bcc6ecc0b88bdbc25377effd46c6738028cdbdf4", "signature": "ef66ec08bb20ba98b062b2c40b30b53901799efdec142e09bf6c54388f2bc224"}, {"version": "bc44a7a0a4d42d4abb85daf7360289e5e5016f5050d2800753fe8d9b9b1b3969", "signature": "a1ac351f90cee605113ebfabec2b727e8f9c121de268f70c4e63840d96f528aa"}, {"version": "6a691164ce89cca6efdd87aa6c8dd38a3b4d772e61eddbb5f8c720b1a4b6f6ec", "signature": "d8d3470b8a4f0cf73e7da03e2b325c36119bbac184876f5c92f2fe7832f0c727"}, {"version": "b4de388dca4b9d69926d93a84b496f4d3e936a6e15286e567d3d27f9113839e9", "signature": "8540d84b399b21dfe30d78af3f56be549faeae8ec84398bd69cde621c8ebb61c"}, {"version": "d9458fdba0dea3ba242f13b09453a0c99680544475c5890f84da27bfb2a5ea07", "signature": "5ebba17051d09f86aada5cf3c430c8e3814ffcee412db11f21ee893f78fe7ba0"}, {"version": "732609b58bda0ad325140c908fe5958f3349fec12f4f0cbd6d6a89c1a62c75a1", "signature": "486c038fd751eaf43b83ca93f120fb94ccb671219110f15c306a63e55c3c0037"}, {"version": "8ec6903b88587a15e86c7cb2b30441446c01f479f7c1140fb21e6917edaffac3", "signature": "bdd491e0ab4e2256c3c32bbcb1050211506cd714a06cb833a42a67da46723195"}, {"version": "b57b2759c27997791d4c2bd391fd5d485f46fc2df6c27f1438f6c60c8abec927", "signature": "7e48b9ab6e8ee1f2f8fd2099cff796018da7c5d386a0908055e5a8b18ad71774"}, {"version": "4cd0701a497140e2bd55a61f7e8951c0fa89b349156eeeb111a221f4c32faca5", "signature": "6407c7a72994895245c8a4976f00e9b6d1fc8cab7095d77e827f7800d09abc3c"}, {"version": "6cdb11fcbac05f347b398071b2c8a8938a974d03473f8ced9e8b68af8b34f4f3", "signature": "2e54c2a84ae4cbb66504f664737fa7f263dc9fa07291f13b34b159926a1f15fd"}, {"version": "0f197a0e8890f056608cc699cf5d3b9a21166a1f66f7ce0f149230eb2f1b3c36", "signature": "0bd1422f0bdec6a3f4c69d4d01463707d039e8ab4002cccb3b35f30cd75b71e5"}, {"version": "8aeff4082142411184ef0018f8a8d55083124753f59ff34ff41326d794ba9a40", "signature": "734bad1a2388b5b07dbdc6cbc89a3a61642252a1c9caeb10c338272b4c6c1b02"}, {"version": "b694de0fe2d6b8d3c02f46d3293d4b6b92a5250565279427fd78995d9de1a763", "signature": "9704e88a97ad073ca93553b6757fb8ea7d63cf62781b5b50d0f38c6891f65182"}, {"version": "015e4162cb795c5d86f1b709cadefb08610d8417e3c9c8920daa1ce614e98193", "signature": "ca7c13d1ffc7b66a2bed60f9bc76476bfc22402b74e74d0cd147661f9a70d981"}, {"version": "d3a03cffd176e480d3cd956a891605ae7fa855eb012a8d0e6bf02a97029ceeca", "signature": "48e28a64b20f699dbc995c49d23778bba714c73476fe1d0472a42c2bb38d164b"}, {"version": "5ca5c59ea25d5f1877fe13095f1a9366828abf7db5c648fc8af8aeea28a7378b", "signature": "dec43d3c2303cd9d265cb25010e45dab130ec0e56af25c2204e98ba8cc07e103"}, {"version": "62a25bc59b31bf080440fc309116cf81678799c3cc60a2ee1342c1d5faad08b0", "signature": "25b4227a95e5f97be3b19c2355791edbcc501486e0ab2353739fc31885654794"}, {"version": "a2cfd70ec602369702b07d605cb19d54a2e133dafba8029e6c5ab2e03f6edbd0", "signature": "a8d0090815da9b70e39526548d56430b5ea331facc0e68f5e6768fb06e7071c7"}, {"version": "4e0003172a8e389b3f0b692a9033278b3d6af6e7bca9f2b8390504a6838ba1ae", "signature": "8131a569ad5fe2929288885bbb22d7cdc12ab58e5c9857c2eaf6e9f9fa33675f"}, {"version": "af70c2f894c9de56d97f4728293cb4421ac835e5568c66a6be86de0df300bff1", "signature": "06253ce2ca4c7b6c7a8cb66f7bf97282bbfd7f3d74991fa677fe1e829088870b"}, {"version": "f095078c2c0e55ca9e43bd235daca2db6cfa22dd5523db553624e751cd2af17e", "signature": "67e79f00483230b2ccc4414e148afbf5c1884efa4ca259eef17625d64db12c91"}, {"version": "40276de6e0f03455349d30ed36b16000dd653649209ded5a025bc8bf095fe022", "signature": "67af05129e05f497319d5454f89b7dd7d6ac5709594e220615edc2580b0f3e6f"}, {"version": "50fc5ee261e87a150badb57fed8596869e8d8f660f551c2d7e33e4777d63a4f4", "signature": "287a715db7539f508ffa293ebe1607de81efb20c77633df9ea049f52ad727569"}, {"version": "501fdcee00b5aec285262283f009c2a93d8107cb95867b2c720d623fd0402a3d", "signature": "821ad805da50eb354ff938ecb6b54eaebfec5312db3f2a43bedc21193e54c5da"}, {"version": "aef91706659faeb5d84efae043433ac1d99e15a36856a3cfe60ca1c932ac9569", "signature": "e4eb019d11d5fdefcff425390b770b825f44ee25c49d4722e9ec8ee87c7d11a7"}, {"version": "35fdbac4c9780134c1c1836b25ba5a61a25233a32f6161829c931287179bfb90", "signature": "a140ee62290eee563ef3682907ca4f52da693685a07d660ae03e7c997678503e"}, {"version": "072b91377bf55cb7af63a39be72d58ab28779e355670c2e31b3b6d62888e20e2", "signature": "7d9799d9d5702b7b6474c1fd94027785c76ee374c7b1c3dd0d130472dfbfdc6e"}, {"version": "fca600193a4ba3ddc817fef8f93d64e0206d3a5636b4ef4a3a6da3ea031f80ce", "signature": "bd69550d018622e34feb582de13be1d83f3572ffa79a706abfe1fa1d28e12a2c"}, {"version": "fbc0d0d37e6a3515f356e09259b78e0e662819d77fdda454ba832b3072a9a668", "signature": "e5a8f054c2a02927830ac68bebc595893de51d434cf19c4b7f212c25d29a541c"}, {"version": "e6e7cfeee577af8badb9619d0953ba5c919b4ae85063afa8f5a6e57a93e5651e", "signature": "d7214164fa5032c19e166f2137cdeab2fbdefde29a7138224503faef4874ff3d"}, {"version": "2892d958f77727422289973e1c66f56cd18b8954dd41ad5c60b5a36553d0b5a6", "impliedFormat": 99}, {"version": "237c02a4897f98dd6580d2f4563b49d613e0ac23ca53b074aa170eba32ba153e", "impliedFormat": 99}, {"version": "272a1cebeb24c7fa280d3d17e2ce82b8ebde56715551bff9fa1be7c81cb6ccff", "signature": "0d8354c9241a0d82ce035f01df06470f97bb62c859ce723a3da44d7db5a2bd75"}, {"version": "28e583b303f524339266fa750d8daad78171835a2d3a6b6fa92a0d6709334460", "signature": "21de57fae069f0c61dbe5f4b7b3bcc164a02f3091adaa3c68b8ab2f5c45a5a10"}, {"version": "03e7ebf24b4c6973e66bc2bca70169af280f85cd92d78f3a2a3c8b8292bce1b1", "signature": "851e14c42ead1fdfce47c646e7490bcf66cda22d385907af7df1cd3bf2080b90"}, {"version": "9f21fdd927c63f2c83014864252faabceb16402bec92204e8d8f4655206df7d6", "signature": "a54ad5afff19de33a047f6edea492305df52fad7dc8260a5f8ae220ad1fce592"}, {"version": "942eb6862d2513dc46a87d6737815c606bb47bafcb5fcf0e6fe4f4a0b2673c5f", "signature": "d58aae32f01a2f0eab44170c2c1e0e33c8fa7c09fc32cc2d404518f5677db87c"}, {"version": "8d84b1e158dd60a8edd9a8becf21e9a8e8423ffb387c2b9a613bcb5ada5c11ac", "signature": "bfa3b874a617f168d8cb51515c165c6d5e6ed83b0f97f6c6ecd218d7423f3ece"}, {"version": "9eab97ad5b49115fbec91a942ed1778525687ccb4ad678fe258a86dd1375238a", "signature": "eb7e8364787a16c4723667b6b03169fe998140c50f96db9b994d2c98cac60113"}, {"version": "fafc92ffebae3ab90a9c57cd9cfb8b98d991c78dbe3a1ae91b93bdee1efd82c4", "signature": "411f47e1e07ab31299b0c6afb9ce8a52329187a8d6231dbfd89bea02502c68dd"}, {"version": "269b933a1bf6475e51580d90cbc9f8cc85893b3a00ffea73dacea023ca76be7c", "signature": "121e9567daa79382ff60c2b6e6b1cec6ad202cf5382b8242a26cde6f6b4b5a78"}, {"version": "3fac6e4b6eddd8822469a4d4fc230f6e3db08df7ae840b2fee9a2ef1006ee258", "signature": "6cdb26f6979f52a157c795da1e9d7777c8cc1a08ec02cb8291b017846a2cd8e0"}, {"version": "7c266bf8ba1b62d1dc609a95f6c4ecf5443877f93e5ac0b323bd9ee8a4ddc840", "signature": "68c22224de097b19586688107c75a2aef283ebd1846b719bbc3a8835e32e5870"}, {"version": "d562d72210eabcc516cef3da8360870144b27aa997dbd561d03ed867ab576486", "signature": "a4dea30eead1fa2786a1bf384f907bb1e83afdacac01065fb03bb5b82e3b805e"}, {"version": "ca0242fcafaa1706b0a5e1238c8f1259053a7b7d5ca6a8eed75fdacf52fb5027", "signature": "cb3fc328810392bed21712da85fe28beb8dffc69eb2e45813bccca65484a72fc"}, {"version": "9126675250023990e0c7cb18fa13273f5a914283235bd77ff2e0303100a9e2c1", "signature": "e7419954da08d44082a1bdaf10d91d775b4eb0c15dfebeb4c26f548d8ce49b90"}, {"version": "0300ebba776b5003986d555bcb41542c64b8ab9f309ef1f3a494a99e5a9b3cdb", "signature": "7146069990641e16a158241bf9fed6f41dc26e264240868e263d82214dcfc326"}, {"version": "9573fa16ca94ca21c39349aa28dc784b102cab976eed44bff88ae6001ad214c8", "signature": "37f0dc799cffe46cace78995b3671eca02f6c658cf9e59b7857152394e8f0047"}, {"version": "999817a276beff59d2f05467ab5ace4982d62c564dc82fc0c081f1ee4c0593fa", "signature": "f1de625c4f06e99d1a266b10e126d638cd968042e9738228d38148d96a38104d"}, {"version": "f2c65525871b64f1b60b130e145e016469c60cf27781b178d507c7cca6baf0dd", "signature": "e220d9319ad730794a8867f900c08bbdf1c9b0fcdd3d90c33d32afe722c2c6ed"}, {"version": "890532f3aec49aaff03f214295ad30ed99c2a7ee37967c1cf80ed87a2be720b7", "signature": "c271a1faf6a805e53e958379fb2a371c4d0d8d7496d8eec5a6f886378ce6462e"}, {"version": "e4e7ff1299d76bf704aca8f225b40abc3c31996f11c290a6aab94c48420bd3fa", "signature": "290a0d79c21e3dcff32b158cd976709a89911c77e1b031b715a93147c164d3f6"}, {"version": "f4ccfa256cd0e7c9496ff8cb4ede5a5407ac3b9855f3ef0ba393a52aa722b1eb", "signature": "8f16849310c978593cf30bce8e3302a371cd72394da4ad774f511ea61f2e520a"}, {"version": "f761a58f6008427ef7d0ec448d7756b89841a4c0cc94f47d8680439da5460b8f", "signature": "fc0e306318b8b236c4c3f95d85bcdde11bc2fcef8e5ed588eec7448deb1888fa"}, {"version": "bdea4c24d6b616220864ebd9f15fc36911ab62ea9da1fc52cd8fe940f533f755", "signature": "8e9f67dfd4c1c14644ed53d6d98e7df32fa6523dd5f2c85eba6d78d3a8018169"}, {"version": "fe0ff4b245ce31299db50edbcb5650a20f410c460a4308e2b9c4204695f82c10", "signature": "343cd88e0fe34cf11fdee19678cf2f404092eb80bcb8230da4015f89d3973ea1"}, {"version": "4f50e5444b1572f1dd4dffcb63c75a3034bf52985e5dc17c85c5aa147c164883", "signature": "d7e135fc51ca3a073135490c702194e66956c7bdc64b6a279d9a8b14ef64c770"}, {"version": "2ebcf5ed9bf33c6b108a01556f202e357caac9371e5d7a02b7630b9244bd5766", "signature": "6f92bde9d8057cb1fc095dddac6b42afb30272c3f2314a8ff2ec6c834258b01c"}, {"version": "206c386a954a7978dbdf1b223e8a482add45a8fc644ab2edbd745f7952d75ea1", "signature": "064d836264003bbb9c3dff42e0d4d131e86a9f2cdb354a013a1d8973c28afce0"}, {"version": "1a64600dff5068d3b6633d7f386ed551851daf5f208f9ac675f7817a97036ae8", "impliedFormat": 99}, {"version": "0b8f5c2d6a469b87d23e4c7e3c062190fc275780c0a1c5948c03b20a939c840e", "impliedFormat": 99}, {"version": "b96875e0c026f6c9973ded63db53b9c8bcb83f381563e43538614b5c7a12ab51", "signature": "8ff79ec785741968e4ec989ce7d9e9f52623d52fb4e85d58f8ecd388b9f31155"}, {"version": "306ae215e666e1d820eb6d653278e23afafa8b319caaaed56d66749dd7ce6fc9", "signature": "30dc8a70b92c89735be1a2be881ff1443c0f9742ae84e2c6d090774c9d054ac0"}, {"version": "162a18cdf8b4f6fb8ecb7ec1fa825fe5994f07619931df4758da3215aca73d52", "signature": "281a7d4be94033631c0e209d60465efd9489b469530e6fbcf334c23d66cfafe8"}, {"version": "6e25645750f953516403eb44c28292ef09cc73136bf40ddf38c08be75b84d727", "impliedFormat": 1}, {"version": "ada1b327b03e09aa0fe98386e0ef6699706b3a45d8b36868ae36b663e8389919", "impliedFormat": 1}, {"version": "a8f4e3e45d6a33debddd1e8c021e3b72a45df572d74d4f11032e6cf13e04fbfa", "signature": "58b58204ffed0f7498f63fd00fc19511dc1186ce6ba966171c104c45d4c1683f"}, {"version": "d3dfd880cad0c466d316e2c133acd044e83d511c5bea7182d58ccb6135b25bc7", "signature": "b3bd0c010858c3d787cec4b1e97a90bf14e97c090f19859748b00709d659d546"}, {"version": "2329018381f821484fb5bf14d34744d9da7a1850974d7dd04aeb22018368d735", "signature": "abc1ceb3e05797f8f2b21f41f4f7d649030f9cd7e24ea3a726be50c26ecffc05"}, {"version": "080b057c382b8a65aa9427933759b8a54f16fb15640836743fbd3cdcb3ce7ba5", "signature": "aedd4297eeed440cd4f8c7ce630ade6eae3e98b05aa7152b1319d76438f003d4"}, {"version": "af5da0baf61f1ac45e7a08e1ba01ba43e2158ce359b28e6ccb95cd2c41b4ab18", "signature": "a56bce9f7f9ddd06dd0f9ac04966a4ba98c598f59fc3ec7b2f1a43d0fb11af81"}, {"version": "1ef54372a3ae9d7f48f8275792dbc88094e112a63a7dffbbd52bbb17d0cb92fe", "signature": "ec6d4eba7cfb2afc9e1f954bf5cb221c6b0c717760b3404bc04a7e3a7780d540"}, {"version": "30512377403cc7bbfe9dc71d5cbab7435d41d0689d582f56816f4700fa42e9f9", "signature": "8002eefc8f3c60aaf8909be45a0e23e51ca394e8b45a04275a567b661d313999"}, {"version": "b47a7e1df32e837dc7940049f37ce93fc7b375be8e71e6fc6ca31ead273a2f69", "signature": "54b9d2ab4d6482abc17434051e091ba696788e28394474cc603ac4b683a824dc"}, {"version": "3123ae7d5551beb43004d97c18e78c51e69e03b895282f7b55108769c1a8ae75", "signature": "c7c0e2a28abe5217717f48ac0d0ebd92cbe2291ef09e67d09f990ad94c23aee4"}, {"version": "1007caf6ee2bd087ac45b90f5a1d318580e5d8c93b5a9236a257256845403912", "signature": "fa655f434c9ed1658edad1e83ce77c9fe2b67c1e0be32e8b6f204ac696e9ad1c"}, {"version": "dbb09326c1f8d1b7bfff3bf50c5d3b405f93ee5d639753b2e84644ef03094856", "signature": "446e955a77e7b669a7fc9cd43882a057ffdd32dbeae8c338d2361db5de66a9a4"}, {"version": "3937ef332a852c077201f0f935a2e494a6e5750afe98d43d4456c7b37d60c750", "signature": "c6782e1b8e92be25ddf2837bc76e84351ace4d1b7e0cf7b5b8eebeadb2f099ea"}, {"version": "f41250a063b33eda0c283ba171805f487347327b2cc896455bb21d3e3a729bc4", "signature": "0ae0e74c54b405e7852f66a09dcae24f65af7415d6c9cb227ef2f0b4b16ee51e"}, {"version": "bb6556f9b1463ff8697d72c0d589804cdd0d618aade2e745bfea8cb365c1bbbc", "signature": "2a05ffec6f0cd169d68a40e83854d0909c3536be9161449a5be1affb6674b179"}, {"version": "8ca1ab12f8988335e76e87ed9f5bfe742fdaa0a6a82929420f3c5fdd3d6b3446", "signature": "77be3c9ce47b623a6fd6a25e9229a95a59c31166fd9fb847982073abdc911dfe"}, {"version": "959034e3a404271b5dc4097d5c319ffdbbc3797624d5a251bdd35478e4c461e7", "signature": "354a1c4c9c4a37076725e9c7660258abd76aab4afbf2ae5556b51e3d6b43ce1d"}, {"version": "9d13d91a5944bf3d60f88273e4d276f6bfc9749d244837a076859650e1c11fe5", "signature": "225ef9486887e2b8a06b2531d84debd31237b4943849c4c0dc9adba7d77d68d7"}, {"version": "0bd250e124b61f9a858b00c9da1b3bf137aac69ea7a4190d9aea57a88321a5d9", "signature": "22204d4499526fea6b27962c99051fee9a25bc895ee1dac0cefa2ba9c4159501"}, {"version": "d73e3650c757b3c58c55d86af7eabf1343fb0fa2c3b6f427f510f00a3e493484", "signature": "7e41852a94457f954f2e0925bf7e4e9f3a5af0839295ad0eb120fdd83f305f11"}, {"version": "86f338e51f4b5afafd7b2f195b76373786be58f1131ea62fc7db9c48d2ffdefd", "signature": "a418e3f688e7b18764aa6266d0e5b156b5d6cd3754fbf51ced7752d9bdbfd925"}, {"version": "cdd076a9fc915edd323478ad8a0c4fd7ab86505b0fb8d363b614e9d2f6019a03", "signature": "11688a120c94bb3870d839172cc57a3939a78e7979d0ec617fc42388759e8cd4"}, {"version": "1367b40de792075349c86a7025785cec5dc80e9df4c74f7d4f0f1daf8b404fb1", "impliedFormat": 99}, {"version": "c957d32576720b3eec32364548dfe6aea1fdd2e270b7d0b2a07c2a965eb6256f", "impliedFormat": 99}, {"version": "97693dfda12995ddd4318c38aee582d45b41c163113f857d5fe693379a902b05", "signature": "cd859f5527941e40997fc4c0a6fa9a772cd39f9127c05e6ce93bd6e12b3e243a"}, {"version": "743c15b5e4469017e3f0f1b9e8032a40f39e3acb95fab3ad172fd4389d357b83", "signature": "e1a61fc13b04e5b01d2b831fa0e30b762b454406c1ff7e763ef2400f4bfba395"}, {"version": "4ac76ec8ce6fb7d5609fff7117c2f8db9f1a56fe204c277e75813ce116170300", "signature": "d899e9d932f56558f11969fbbb750234408b19f0ab93737db5b9b0a45094429a"}, {"version": "9abba61e70528e93a5ed77011cff7f83b94e9a659cd117b45d696ee8669cbca1", "signature": "a611f7e8b69f908952d18bee73ffb98135784e1b419b19795ae8f7e66298c9c9"}, {"version": "a380fd3b1ba2d34e444500794397c026a9c0c3d0718f9b42e57aa9c986bc3414", "signature": "fd7108070cbf5d4b6dd25bff0ff4b36f0efa8cbde12d7dbd2967892cc0913021"}, {"version": "fcb7f84418a196e5517c7a35f9b2919bb2cba67ebdd7f010597b0a44351d8c73", "signature": "c49da8441829ae37698095bb3e741e04eb50c0e9e80067176e7cb3cc26ae0282"}, {"version": "e6952957b162a2b1906171bf2d707f44d49b28831c318afabb4bf4b23d9f5f5b", "signature": "1d798f3f4ade6cf30493df640da5777158974cd3d7dcf447edac99bb0fdde7da"}, {"version": "9c404d1dd3a045b9b2940a9a1d1c195314086bef41c85efabc817d41f5e0de26", "signature": "48495d708524657ac39ccdfd8db4357924657ac3e4195ff148b92ab5859bbefa"}, {"version": "5fd01e7323652975155fefd97420a785287718d483a0c42cbeedaf2bcc696050", "signature": "eed28a2a473b3f75d6ee77b2ad8477b3655f2b704e742e9fcce0fd08ed1582cc"}, {"version": "0f7019ed4f70f71e458c624a4f2d9888afc9d80a1a0a5887493ee693245006b1", "signature": "a72aac65b80dfa14710bb044ccd2bf822ac54cba16720bdc72c4eec5b798eafb"}, {"version": "a7abedb6b5324835144a8733c152bf301f7fda4723f380b288bcba2cf6d50c31", "signature": "ea73b199eeea6cea49afc160161d567915c55e7c6afe04683bacad6fb8067b2b"}, {"version": "46d868719b0e72c38c0bc96a9f4aac9aef9dcf8b6273ea3f9e1646d8c2345cf6", "signature": "8a2cb28fc6f0062b27036f4b29062448ed4b3d179fb3290e27ef575c803b8ca4"}, {"version": "37007e18776d56a573e9f6e9686a30949c28cc965df514efaa4c56ecd6407e4b", "signature": "b94ca7ae0500a9196c8669cb7750e7ba2c9197ff6aa87c19d3c6825d216364f6"}, {"version": "42eb170a9066080a9d3535c1493cf82312c534d973a6e7e548fb5e15b9e6e603", "signature": "8e9e2e56cdaea2a2209d3892d3c3a78b982fa8081b729fb75fee6ad668449719"}, {"version": "5e55835c7dc51eb371899e445c21f7c2f61be9d75ad0b549407ed24c9fc63848", "signature": "f981bb81786212dac584dbb6c114ef9de7564f9ca536c998074ab75dfc2fd5b3"}, {"version": "effe2092552e740c7151ed843575cb2df66da4c1d7b1eeba006cc21f07b3d7e0", "signature": "e0750dba7a200a4d02c10bed48ae7a54d1092593113728a557c17f7d3ac48af1"}, {"version": "9fd1e41ef16c55f07bf7309ae4528f07965aaab6c784a913002aa82c5ebc97e4", "signature": "d624c2c05c89f4a3ac96c0ec43e9d85cfde9b303c1d5f941c421d7a0e6f6715a"}, {"version": "929958c18fa7429cd06c321406e38136e081aba4719f120cd04d8deaae411c31", "signature": "defede40f363eb9e46ad1550d61d3cd38aebba42dc6fc96721b289db675ce631"}, {"version": "5212f0528d6ae518d001e972778393fe47d5fa34c7c49feb7e92985a35dd99d9", "signature": "39687268f1326447db63a82ed4f9e50b09b986966d5094bd62526fb193d68be6"}, {"version": "a39855ffa99352ac438a1647634793e14c166d7347775272e1bd00593715e3fe", "signature": "4dcb9262bd7316bcaf57df87920e7174afe1d3faf616647e86b0aed5a3718d21"}, {"version": "246871f9dd06cbe85506837722148ec9ed138097a650861f6233c687a0ea419c", "signature": "02a3247f9ad0df5b338d11eb63af2610364988e367152b351eb73709aec86260"}, {"version": "46e77a101cc171ef5febf850ec5b1dcd64fdd354189fbde5a13e3504737f9c70", "signature": "d86ed6e17a831110fad572c8274f394f49fffc69d2fd49e74605bda337f92f2f"}, {"version": "476ef07c426d291b3b8930af3ffc0cba1c1003fc376fe45c7165f9e80a62ff5c", "signature": "8045adb6388162f00b1aae484143645d043d71c496c6656a1da7dbbe91daa5da"}, {"version": "3476227ca2e82df1ae58f9dcf66ce7f3e665c890e92768d9ec3a334efe60081b", "signature": "0fdde209b2b2c1dcb87dcf5292482193a7e8acec8780433f9440433c4c6e2fea"}, {"version": "fdc945a1fc84ded8fe8c6475251e69270b95629654505034b6bc5f2967324098", "signature": "7ee28ba1d0a3ed4646d32382fcb65eb10a6bf8ae9a57c83fb6bf11be60bc28e7"}, {"version": "cd3f52efeeeb52c64f254f5434bac3f6f39d900f4ff878ecabd3c1de8788e125", "signature": "496f5f98e6772a532b1e71e8c666cfa5a2800acdb805032a231304ff70d8e349"}, {"version": "599d92514d5ab9a3759af410642bd7d9481cdefed8221c6d5367d120eacdc386", "signature": "1c1bbe15bce3f9c9b6f5121d117451f4d405b0cdb8d6f6527e0aa80ca0e62a80"}, {"version": "643ef4a38be23d52a241016eac6210fe050d129113ae5a379ec2ab01f2efa5f3", "signature": "e2c79c32f4c6a342906da9ada92759319c68cddf4ad848ffccb79740575fe79c"}, {"version": "6c80b79d96f8c96a339bec2aaa1c892c28918241173566607e48c300b0a8768a", "signature": "36eef5260e7d64e8685e1c88f35b5b7566e10293c64d87aee592ea4089c78e73"}, {"version": "dabb8a263609fe76c1ad08d6ba6b251ce664c46cc06e51f28d4f6d2a9e6a98c0", "signature": "a59226c6984deac0863d714ee14603dacdabf4a3f42926c8f24be5e80f6e5e14"}, {"version": "ae678a37e12b226b56007d4639a8ae038360b7a20949b4acb00304f0167e867c", "impliedFormat": 1}, {"version": "f578a27c9142fda3b216af8ae9647d6df5fc8ce97e483fe9812906624f34be7e", "signature": "a6a486682371cdf9d203ff278da0cd10f972a19f62da157ba2425641751af501"}, {"version": "126e22856bfc856b7c1c1b9a42afb55185df5e7d10be0f3e586333da15e49965", "signature": "372d4df151492f92317101dbe30de1eea073ab8ad3ce77243e8de0298d38b9a2"}, {"version": "0c28bb434b9d3a49a9854bc40bff665c39118738b809d9a46205bc72a14a8cb2", "signature": "8246f286e11b59af46418b0713b1b04d0e00c02e7e1829988928dcbd881f3b8b"}, {"version": "6c9aa58aac1887880330ed62227e744b54ebe708790fb1c9e34a2d1c6d9656d7", "signature": "7170961142e934bb6afb22b2c4e85a42e2391a076d00cfa84355245dab1a001e"}, {"version": "69460f0e3ff304a2d8ff6e08d4c981f0a8693000e7c62a377320ecd04847a7a6", "signature": "be8730ae1877dd768fecc2f91d1eb81eee4dd8849d8b35201bd0044361ae5ee5"}, {"version": "f81a9b400a4e90224d5f54c832c8fa6b0508cabb3e2978a1b2ca34d1dca008c3", "signature": "cd941e4f63efb3d1eba974e237447deeb58785a8b133a5f5d509a28516df16fb"}, {"version": "ff72b77e293d147269a914ab445827f16bd22faa3e806ea3eeea4dd994e9fa1c", "signature": "fa159778341fe775d6364e95fba7fe1f6a2aaaa78c8bf2f64124cbdcc863264a"}, {"version": "32309d3a79120a6698650ce8bf35c2319a4039cd36cd39623e4b55eaacbd8412", "signature": "32b8581ad94c5837d24cf653049648d594ac4655dfe139498b04bea7b69048dd"}, {"version": "600b843b4beae6f391083969bdf8fd2ce4cee5e996d66adc1469a19fec6a0016", "signature": "d4dd9f32ebb5b5a5caa1f11423337b9759ebb12e360ffe2a511cde6b01e9f52f"}, {"version": "f20091b1aa65acd3ebbba8204dd703335391ee2ef7599ad164c5ff83a29dc50a", "signature": "5fa1e0e27c99855246bb4de4d96c05f05af1fafb6b90eab9b769f0096aaa7273"}, {"version": "e723a46b1e5988b17cdde8f37bb95049f4ec8305b8f833e042c97a14974ca499", "signature": "8f1fa988cefc4c909c30c1399568da7f34ece27c9b8442de4cf69faded5ba579"}, {"version": "b1d66b0512b276f9c9b4c566cb1036b27e9e32c1b3488f7226c95965f6d5f47b", "signature": "86eb3de7533cbb60076182226d23594292aab5e358d31eafb9e4915954700809"}, {"version": "94e56a3d7201504f9b10537cfe69cd4116b753c2592a7e24903bfd2a7a1b52c2", "signature": "081ab85a0b82e027fcf317fd4bc1a28300d4e09d6dc33974b8eac121c4d2e49f"}, {"version": "fd4467133c8af17d6e279d78fef75900e99926b61ec48a85ac6c021174d2f916", "signature": "953b7f1a49b28928c06cb52553eee176e764034aa30e759c64b11abd7d22c10d"}, {"version": "97af3c7c91fb0b9dd1f140dede20b7770993eecf5ee33378ef86626fa35d6e81", "signature": "5327f6f27d2e2c4dde251568432639e6888d6dea275594a7eb4ffb520f5e2978"}, {"version": "0cda5f4fdfccd736955b705e2c2c2934234a5def6b04fe9a3cd3bf202cfa3792", "signature": "c62e922c211eee677c19828e6b03ae0c3fe2bbd2c98c2e6ba591906f42d89d69"}, {"version": "c579ab3ad5731ece97e489a87ccbe9ed91abe59dd65754cfbb5555479b0001f6", "signature": "8a685d282de5f110a75b5006dd52e6f811e530d4fd977e2de438190e39d36edf"}, {"version": "742b045440d5f0a73c7b68eafdc4170b1e4bc3814ffddd4ea2067b454d156945", "signature": "2658d6bbdf27154f2f3ff7ea653c0b46b4bbc72794ffa34792c474b88ec96589"}, {"version": "0ec716c7a3eaf8bb503118cc28a46af624440646c3bf11d493f650c95f46f26e", "signature": "c3ebc395a3c46796843cf70648f4df3bf022deb5d1d2fb023b4fd8504b1643f1"}, {"version": "a923726270fae4d90d63eaf98765ef02e8f457e3403728671c8ddf28ce2e12ba", "signature": "b2e347fb263087186c8cd9d6c7d5ee4ac00f722ca7e6c58a3474ed00e78dc13f"}, {"version": "72ff8375bcd3bab6e53d3fc8ec3caf65603637a80b11a0966a37cf2296d7b5f0", "signature": "8a4aae276777f45bf2ba6f1b8a08b4fca63faf16b5a668834c68cbe57f711960"}, {"version": "0e2644c7891a3048b53385424ff82bbbf875a2e194b9698931a55bf3860da487", "signature": "67c83cc9901931eec903e0370837ca3923e26959be1282050f23496a30a2eaee"}, {"version": "70f6511b8173c6d96a84a77671f5726b2f62315219c02fd50c2e7b22a979ec39", "signature": "0a573cd5f8ad520dceb98c59c87e33cb325f02e098c2c486655da4479ec97651"}, {"version": "858baa2582095427f7cd6438f771701df0fc43f2d3db899627d409db06dab1d5", "signature": "65826fb0861a709cd763b9c533fc295011b2a51f85a46d11956ab293ba06ccb8"}, {"version": "b42c7b7cbaa9492c91c3ea4817cb60b92f5e9a52c8313605e00929e4f3325956", "signature": "7d494ed337837a12f5c661dbdc793a13b7ce1253d7a95b6fcb612be0ccce11d2"}, {"version": "b9de5a6dd6c04fa65fc41c9a92769bb310d49bcada046b66696d42a5d445e60e", "signature": "66f122ad6c13d061d95a7ec9fc21e16dd099e62fb18877b1b4f84f2b62a9f276"}, {"version": "99603b3b2d86f159b97824f67ed12bcd6f612c34a153e4a40be94c38ba5fcd94", "signature": "0127a8b74e75cf53f5fd41bc12a157a5f6ef5b41005e0d24f8f3c8b4145d54ab"}, {"version": "aa893cecb4610d012b03c13c417e5e4b802f96054726a6a11b4dc12a93dfceb1", "signature": "9dbdfa8e589ba6003488be5a3098c62df160635e26490177071e91d62d991b23"}, {"version": "6ccc9e5641672aafee5a0ac3e4089494b403cff248d6ab2b854220fd53c90d8f", "signature": "34b85ebc30f00408fc76a6553d8556cab0aff91a0889288dd2913756456b6fbd"}, {"version": "ef03a78e44ad88e12ed3c40b9901a008e4741d8590cdc74181f4b50299cf0840", "signature": "65a4378b30dbfbe520034c7c78e12d48825099c944f6191cc84fde5f9d887983"}, {"version": "3f38cb6d91788d64d64f328f44aece4478b442bf7c09ff59eda8654407eb47bc", "signature": "f9315a43121c2bfb0e058d4e667bd7116f6b28a70e040b4bd6f3c124fc325ec0"}, {"version": "ee32d409a8f583635bbb9753c3cf16510b2bb87d2048629004617e917aa27e8c", "signature": "3271655b0cfc3ba7261ad80bd2d8e9c42421afea3e8a73378c1b1c9ac86b0d6c"}, {"version": "825ec81283938541ee40c80d7ea1ed0ab335dd4e3898a6d41222f5eb68c75ec2", "signature": "8402430a5147d18dc53bff8ed8f880ea3058fdbf7db9099073953f85b27a318e"}, {"version": "28bf4b8faf00977790cedfe9b8f72f4eef16f813fc5893c14b7a0275ce83cb57", "signature": "fc6bdf8f19e0442a9d30890bf89b32f6db633e6ede28307c59caa8d75f2464b0"}, {"version": "117a0c180accecf733b730ed383092b822c0a6c8302057683c6def64ed4a322f", "signature": "802fb89468b29cf740a7b17c272c84dc6bc43beafabc1e1215baa5c1709e83f3"}, {"version": "2bd9437fd983534103d82fa6299a8585902bbce195f1ddbd4430d47168978ba5", "signature": "d0e2b18f468fcb27a000b546c8a15641d5eda4722bb4c24c60dad739570008c9"}, {"version": "1280b790e37de02fb5c2ba29cf704b05e470540d6d4f844cd1f65a67db59e142", "signature": "cd8b8769571ff68a1fb3b472e4c3242aed2e890df9de87c7f8950bfd38eacb6c"}, {"version": "dae17bb19eb9561169c28bc4154ff0e71e8bf38ea64b16ae7f47eba5adbae77f", "signature": "c3798c0ab741247563af12e2c39ebb58ed6d5844f2554c83ff5f4c08a4be27e7"}, {"version": "97e1ed331f869603ab2480966c55c7b1d1b4e282be3fb39f867849831cb8b2e3", "signature": "e089961916bbfb5d543825eddc7708b8b4a05bcf86f4ca06f9aac6a7ad243910"}, {"version": "b926d89605a0995d53b42d7813a495dffb261db25547254943e82eac0c4ebaf1", "signature": "419df810ca4ceeaf1c12b573aeec0100f5e791505d277728d9b26108019cdd8e"}, {"version": "55db42ea0ae28143ef0fc080015e35ceaab6b1e14b447335cea727e6c59eecb3", "signature": "3a7bfc913ad43ec1e2f13884d2d0f5638acf529ee11e3dbe8e2a4cee4ebeba21"}, {"version": "1735d0edb39ef56aefd36a5ae858f34254dc3f7a7363507ac295fca144755743", "signature": "51975f1e24adda95878b7f28aa14eb9f1c2d970f75c567b0984d86c9d84fbcbc"}, {"version": "d60e7d6f514acbefcc0c53f4deebb018cfc605b8d3998394f79353aa7bfc9051", "signature": "ed769a673a188344cc71c0ac0fd21966cc2e3f5f68b0470f519c8ef887dc3a40"}, {"version": "61f3e7dc6966f719a97df38b0716c6c6d31add6666af22276c9c99a2f05258ec", "signature": "ad13e4a73507628a91427982ab92ea5407b5885c1c0e917c39952a1fff160d33"}, {"version": "7c6a7174c120ced22ba0495b80ff6de7971259221d5e87d35ef981bb32261cfd", "signature": "1e8ca15d5c8fc62c1c7a5bc4dcbe16e3c7acdbc85e07e3f42fd2fd310409f8e8"}, {"version": "509acb434e1307617eac4f36b107f4b1021068c911b37dff193a1c503ff96073", "signature": "dcd8a641e50dab14920558085fa85665faee0d5049b88e4ae35b4472466f2556"}, {"version": "c95b6dc3661a258548db31295e70a77ba35cd4410a97b6a4857ef2ec45f7287f", "signature": "500f15c0993e8c388bc76387c94ade41e0531185b0c7ae8d2e5fa047836126c3"}, {"version": "c36b15a07809cb5109ec387d1657db2e0b8142a3df41c3457ac7b0a67831d66a", "signature": "5619e78562597d0c7525d4464cb8765f8c3588034c4c937938fef28b120ee59f"}, {"version": "43dab119ff038e50767627540a48741310eddf725aa462da3dcd55ce5e3cbd69", "signature": "f624bf3dc9bdc44e1d8712396631f17cb09599a59a07d8ae21b1723a17539229"}, {"version": "67ea909e71af76d4a1ed8688363e3bb3b2383ced4d79e0e37b91bd660254628f", "signature": "33b3edd7610cb4d8d6df95c602324ff9f690a244a05072ed30494064aa267581"}, {"version": "254827b7f4867d3cb27ea6415b57979477ddd2360bcb69de715a6339766bfa84", "signature": "7ebddb4343933a680e0a34a8e235be5568697e840d6b5374ec4929dbaa17affc"}, {"version": "4e107a5d4bc734f476ce179d83d5b4354fa58f920a6bf090a7197ce54f0d7f7a", "signature": "b368c25acfbaa30862cef1d4c1b9fd89b216a644b648b00adb70ac9edcf50277"}, {"version": "3044578f678a956a9aaf117d99b59f7c5179fb3080c862203f6a5e3c53ce9197", "signature": "864c27efa1329446a6a18ca26fe6d19d02d9e4f6753c67905716da5b8ee96669"}, {"version": "65250d737f37a34c31618ec845641c0f90c75774053d4e9f460384a8d9ab6248", "impliedFormat": 1}, {"version": "210fd9c8df537e410cc128386c2c02cc9ece216f4e80c07072364804c9ff348b", "impliedFormat": 1}, {"version": "dadbd68f7b9ac5d2b0c32c533615326e6ee7f56c47c6fa53bbcd73ba2b2573e5", "impliedFormat": 1}, {"version": "2c17b10b66f15ae5471910f96940edfaf53331a191cac9f727f9be8908edf4aa", "impliedFormat": 1}, {"version": "e6ad733311f3aeb3bfcf1cd5f30e21c30719203f773b6ade91e82d37001bb50c", "impliedFormat": 1}, {"version": "a39669097ce1f8753ea2f56044fbc47945c1d32eb39ec7e6ad461fd530b4621b", "impliedFormat": 1}, {"version": "e30dcc4bac25afb7a521b5beb3c3812ae3404444bd827a9150f6b387b2f699ca", "impliedFormat": 1}, {"version": "af594dfd5f1c94e3d3c3fb424a49e2b92456c2530d078123463d2721832988f6", "impliedFormat": 1}, {"version": "965980d4fc7ea4999d131f7cc6a60387f1faf2c22d69df89e62b9c644cfac8ab", "impliedFormat": 1}, {"version": "1d3514a7a487d8cc2f4a8f8d345c551971e1282904465f74d787dd9e3519ec43", "impliedFormat": 1}, {"version": "db23b081bc517d635e408c7aba4558dbd6cb3402fafd63222b0183c64abbf098", "impliedFormat": 1}, {"version": "a849449c45ad62150da62a9ea05882b5774d9e5af5440230d49b029c48fc2576", "impliedFormat": 1}, {"version": "abf54f3e0e01ecc1d70d850035d4def9ea9d6bce5aef9cc5be35480435a0d6ab", "impliedFormat": 1}, {"version": "b90003654cd70b6e121b66936c574ab56775db984446aafd4170ad345a186257", "impliedFormat": 1}, {"version": "25c23bb03ea4401f589516c9813e324719815c91185e488166f7874b084880bc", "impliedFormat": 1}, {"version": "db23b081bc517d635e408c7aba4558dbd6cb3402fafd63222b0183c64abbf098", "impliedFormat": 1}, {"version": "e2c27c899a25a8e1a2f30551ff12fd386abb0e2d5fc78a782bbb251e46143115", "signature": "5046337ef6b4b3ea002f1a97e474c567a3812414fa74a0605f409a4ada4fe862"}, {"version": "44d96f244f231579d7711bc032520b8e5cc98e740ab62570ed25ac3dc0029d32", "signature": "fe58f6640f1008859ab0c3b9f4f4830cf2b0e6d70a025415204ee87d9c17da67"}, {"version": "62d256b028ad219f64b424ba3df66e45485436747db14a28326ccd3af9952a7e", "signature": "3bc42980d1efb4a5438710eab671e237db62e1768eb4518d7e85bc89774e5f6c"}, {"version": "a37f905601397fef975e8412d2537b219f53ea400a46ac963cf215340ee813f3", "signature": "59e9ca98ae1a67423e3347cc01f7a08ea5237e9bc25ed1828175b2f7a3d0b08f"}, {"version": "4fc5a0782ecd834687d6b229ddf8b947a23b38503e91ae69c6321b9443b8b3af", "signature": "aaa176cea635a3bc91d64a123fedd144adf8d3939c1ba13d98841deacfa749cb"}, {"version": "97d62c3dab76403535660405e7d13b6ae9a6b8b184ff47cf463c356bdf391acd", "signature": "5601ffa2f92e4817aba78ce7c46518ab6d0632279d5c71eab09c359de4f2406c"}, {"version": "da8b2a78b156b3a674661dfb0f3eca1267b4c82fb710791a8f954b974b3fa331", "signature": "ce60e3205c64b24a0e09fc4d6652047e52e89075d1947b620de4e610327f2ee5"}, {"version": "c0f8fcff9b8b298726cc33ebd7f5c8d4952525c9e6eadd72e8f43e0f9ab718cc", "signature": "bcb05efb699ce061792bd847123a8e25798b1df56535b5e754b8f6c1674a9ec5"}, {"version": "1660ef598ba65030a1f938dc5dd6434eb9c19aec5758e46e28fe74c996a5adf1", "signature": "64f5f31994ad5ee074356d2e93b2ce42061d955f41f0ded413e3ceace4515ed9"}, {"version": "d8611c0f3604b7c301bfd0a2eb3b65abe63dd18643bc108462ceaf7890a45aef", "signature": "cb31b86953a5a6652a91d9a0d00fb4ca8551d18ade21237404c6eb872f9f306c"}, {"version": "6b87349f3a74e965da3b8ac18ab41e2d30802e559a97fd8fec8d30de43f59aaf", "signature": "91aeff43100959f6dfbd2373ba4da72c9047c04f21209ddc648f844994232464"}, {"version": "8d859414e4696c7fb8b49266221e00f050dc126eac7be0cd3b26cc0d56f544c5", "signature": "8204e0e1d78a568ee778e9b44af71233d87d0e7a471695b2b4097a44debb4cf9"}, {"version": "ca294095745d4ad847cbe60631cde1f2b9ea9f47579b4fb71862c845dd990edb", "signature": "c6be3bed08f95ea4bc71e739567ec9a69dcc4e5b742df7429f870907fa16ff5d"}, {"version": "ad1a1b2d5b0c6f4a282446bef9ab0f80833c3e1286d00afce4ce1b1f2ef398d5", "signature": "2008c330c927bede7ec2e19010384a8c81471ad4c1a7dff01dfb410afcbe588c"}, {"version": "fd51d5bb7096c3a2a44250afdc7c68a35f8eddd12a196648d71cb874d9037c0b", "signature": "cbcb54e0fe60fb17b4b1719efbcf6eafa5294c04488955093da107362d129b32"}, {"version": "a46d9ddb371ef3fdbecc2ff555ab7ff436c9ce898ceb860a13ba10d41bc5111c", "signature": "79b78421dbeb05ea05c38f38ea94419637a550476f809f9b559a2981182fe4a9"}, {"version": "0b7406da86609d746d5152871b3c51b222bacc19cc02b228688556df7c0d8b9e", "signature": "53f949635a40b65f3469a736ed9e66ef2091026812f19ae0fe29eba6d045f948"}, {"version": "55deb9e6097ce4223a91012d00e9cdee2829f8dd7ec3d628b980677cc4be1d8b", "signature": "ba86160764e343780142d96a06857992367f895d6e01f7b7437793c6964f8b58"}, {"version": "1f6c4b78f8a40240e14c75da5e80f27690020d5b84d98d86b2d90901177c9d64", "signature": "cf1f77a70f4301691587dc898e08aad6c1099e78720cdd9b7f5c4568ddfa71aa"}, {"version": "f1df2089b4c0d557a78afcdbb91ac95ba586245759bdb772cc7677c26a0bcbb7", "signature": "3cfba1463b98811a73b25bd01c9484de061b5b0aa977f5072052cda53be0e34e"}, {"version": "4c2a5093be475a149fd306284c22c15425f42b721768c6fe1f0417c447e0b2ba", "signature": "d3bc2012b5c2268f3b6b20296dc203e85502804f4284356a41248809495b0147"}, {"version": "57946491311bc6520c34e97de5cd485eb59ff8a20250b4f39504e09dbb1bde7f", "signature": "e2c79c32f4c6a342906da9ada92759319c68cddf4ad848ffccb79740575fe79c"}, {"version": "2ef56daa830e8d15f90255fad1466d5600cc7acee1c0ceceb3cfc2bbf282eb6d", "signature": "8ef75534f01436d1dcc87fba42aab26239524f5433b2212f699ad6b200b0e8f3"}, {"version": "cb2beed493a5465ae690e321d717a5129f2bc4327376302c341ff73126055c61", "signature": "db4d01f00dbf78dad5a83518a4e59636f53218ce92c815021d3f58b1bed3ac2c"}, {"version": "ff224ae318d44453043057c281694041f653edbdebbc3cae5c11c7205bab3279", "signature": "7626dd4d82991f71c61c1be497bc0d102be4bc5a31183f0e6b6af4e472f31564"}, {"version": "4567cbecfd8c57dfbaaff6663adf9ef5e2f532bb086a9006501ca14d3635f99e", "signature": "4480aa67e754351677bbf5b1eb08e05d69a3340b964a73a14b53df0ce37584ed"}, {"version": "ba8ebf94771d756d15b8cbad68a219ad6007fccf0940cfbcc3b2e54ebeee48c6", "signature": "6b88b2fa956c9e2ef9199258774218b5f0ae348146612e30343a363689b628dc"}, {"version": "260c0e0ffcfd15a8c58596e7156cf0674eec54c227726f5ea042668480a91d86", "signature": "78b3545c823bcf9a196f4628224ae043f32c14583fc21e379aadf206e44935fc"}, {"version": "3b028407a14652304608223fe0bfa84801da05c96ec23453ff25c78aeebf65dc", "signature": "f1e883317e39f626db9a99a831754d37a63a74bba82e02a90a07929707613309"}, {"version": "3d19cc9cd1f93aad488303d0e6ad5fb31183b853535bd93bc0e0887161b3a053", "signature": "bb10430c8a053d2820e6d69b28425cdd1e4f5046a859bbfc348a0f82ead1991b"}, {"version": "a603d5523c8f095a31fa4225c7c9faf66c3485bb30548444fade58b7a2f1d386", "signature": "47dc26adf6616802a398d28a1fa9927c5fa760f4202af7699dad25210419ce9f"}, {"version": "4d8e9fd1a91f0c3ce713bd8f19de5f227d117afccddd98e9fee8e6a5840e9699", "signature": "2e87db5af83e30a7b995b52efbd684116ee101dafe86a15f991f833c1f15ecd2"}, {"version": "a36859b071bbfd6bd9134ee41615637f18e60cd96515952e55584ee0f26b5b69", "signature": "797fb60829cf3fb91cd18ed97a6cb46c2a3510d419e7b573daa355236f2c6c82"}, {"version": "6cfbdb3d250588eba4913e4d356f5edfc9fbffff5318e687dc5e3ba6da782765", "signature": "0e4a52d249c0612070f7a9748cef46d80660356bdd754e49516b1001efc76c84"}, {"version": "a44e3d485cb9372bafda66b5aa684f22de43fc3c37c8f521f1e7297d7593b101", "signature": "728e43ec72cd0525daecacb041ce273178cbf0bd254c667b82b96e1abfe0649f"}, {"version": "1de8485b418deccef8a566d7e951c2b989eff4184ab8e00619c48b95ea06e6b4", "signature": "932ed37fb8285e778486e30701eafbb12eaed650ba5821ecba7cfd226a48b764"}, {"version": "4dbf7c29828085480000672e8dafb8a3e7486d2a0f02f6308893574d8776173d", "signature": "f1e883317e39f626db9a99a831754d37a63a74bba82e02a90a07929707613309"}, {"version": "d363cc69832072a7a7ecd17ad1e29456db9dec7d1a1df00002f8b5ec8f74029b", "signature": "7ab30f896a09fc14031e266a5926762beedc30dae5003fa3e5713b650abbf03c"}, {"version": "6a121d1914e5e6b1b4f6f0e6718f3884e0b661f127b8354686732674b7eca614", "signature": "8483ae9113a24ea5c9ed45f0aa5796319252bac6d8134edb47fcdb2d4b3c93d9"}, {"version": "67fa43f2ca9001343b0ab94168bfc0755c8a1903e183b470e096d3d948ab99c0", "signature": "aa03e5c57d5657e1ba009359b9803311ca0714e32037ee58ddee9e790cdd1fe0"}, {"version": "8a78c6dc2a8e1ca5d89bd6aaf9547101b29631abc6b1a315032e1444c6144eb9", "signature": "20b38ae6f4f35e03d95b7988980c3900a414ae9fdd6a9e142d015eae11e24675"}, {"version": "307c2ebaf239056702ab6c26cfee650045302315c9b31b63c10943bdc483a2a2", "signature": "0cbb0d0acecfff043b95f9aadab6b218a976396264a2c9ce639c9db8692e2ef2"}, {"version": "a6f7b76f33113e8d0ceb1aeb805572838846861706f83eb6ff6b85fa218ccd32", "signature": "f5dc146086a63e86b7d278013ae3a988b5d73ddd1ca419f6943b3975b78541e1"}, {"version": "9e70be8dc8a736f65be8f8aae97f636c20fa04872a7a593f336d3e1374268c70", "signature": "513bd4b1f2f7456cfe5fd4cbdb13432c6debcacc82b6cc08ed5f4ffcde7a05a2"}, {"version": "450a252b05cf93a40d2921f29b563df32af3639a596c9a7567170c18a0416bdb", "signature": "b6cbc9ed628b0e0b5bdd67b3861e10bc38d9d6584d4dd4e7cdcc51fa2ae3c382"}, {"version": "696aeebce2f169a5f92fb9ee4ebd51401050ada6b77d188682d375873d41fd12", "signature": "2df6d23d210099f56e88780995ffa0048f5fe6509b95c3282a686a7e2bea2990"}, {"version": "0371e2693da484598ca66c4ae33cd0eac9b5b3712a52a5dc4f8528a5b3b434ef", "signature": "5c9b1141fe471ed1b6e76011e6fb1958bd4f8e789255cb628c0206dfe26f06d5"}, {"version": "501a579dcd164ee1951404c369bd50edc12d1f1d1b9b8ea3b9e0f7f701ebb71a", "signature": "ade68199bf96a07d263a5e1a7dc9912ec3f95a068ba55979c99a519c9a0116ac"}, {"version": "707b844d0f500c08a5fb28363fc9a4d433d70012a8c9aaf73797dcc10cf0229a", "signature": "72216c4e3dd93a5b14ef143f067f9ad2df3780c384d86d3bd4592da79002b62b"}, {"version": "4a9756f0623fd580932f5d21c3e378bf7af00b8c63fcfdfce124be7ff99bdcf5", "signature": "2c49ad87c3a42d9cac72934f3070ee457482a88e3cab085ba09c2ad432e25f25"}, {"version": "7fe7ba3f1b21e1426747114075a4c2fb08235ac17e969f8736f3df9aaefb7bd3", "signature": "e2c79c32f4c6a342906da9ada92759319c68cddf4ad848ffccb79740575fe79c"}, {"version": "5d8cbb21ed6602afa6c92d0c20d3cd200dcf7b2cc4e0c36dd032df5daef25822", "signature": "a8c637fc21fd764b38f76fa7807997a3b99b8d0802a165f18949e4980aaff8c4"}, {"version": "a97be511e43b9558f2c1ec535b4701104f4fed62b82ccd5f78d1582408677387", "signature": "cee34aed36d5085f15f35e9f5b5cd50356fec4215bbff9c6646b3ebb4399f108"}, {"version": "d95c9f15b523d54fec8f0097c204a9ca2f4beeae6308b13fdbabf0418bd6a9c6", "signature": "92493f12be38fa0e2bc4f4c530979d7c7171f5a1b6279b4ec4d0d44da8b03576"}, {"version": "480acd1ac7946611f440f341ea61cb5be1ce1fb9e055224d1a9e3cffd4fb5600", "signature": "92356dc32a97bfd019f91f21313d7b6a12d3d7181d718903f99c7d7b74ad2fa1"}, {"version": "ae19e0cfdcd15b44f86c53009e054cdd8d3e9ccc66db85d357b53004ecad2cf9", "signature": "4726d7d56001c16ef6d1bbe8b425e124024b311f177dcedc7f88f100b1db95d7"}, {"version": "97d534c16d001e2f997626e4e5bc39fa0a57164514d666b6d7ca471468e47746", "signature": "f79dbded9fd94a26eb482118511ecc376249826e18be346fc0f696029aff079a"}, {"version": "26881e042f46e481f0ed574c751daa0df759e02eca62532db6af0a64c9fa10d8", "signature": "7b9443b9abf97079890143b05b88ab498b76cb86306e65d61b0fb86d98a04a4a"}, {"version": "69114482b475336f7b47631224066709b52220d265222368101f66ca3b86f15c", "signature": "8da95beaf47e64c0ec2c52ac3229f5a14e6d5f920c958cba7e27e966aa20bd25"}, {"version": "c6d35bfb57277b74a5f026fdedc7f18d04869d323044df6e64bf28a5779aa200", "signature": "ea12f5c3567d3e92d92e11c281a0323b40005d4c18a786aa1ed69f8e66923f3f"}, {"version": "ddbc18891178c8d09a2d3faa99ca76ce55006cdf034aec8ba700f9bff3919fbb", "signature": "76fb686e7514be9b0853f97fad2a6962a7cf98eee3ebfce1928730172c847de0"}, {"version": "c2ff1b71be77012880816faf1e27e9928c0c4897a0c44ebc2faa38bc6f33b18d", "signature": "c41fd75e095dc61ef14ef2d94105bc8e18334ffa82b3de3a9ee996eb71398558"}, {"version": "fdb3fff0f0ea55311bc8068b397c13aeeb3171680dce2c399fff0c5c53cca993", "signature": "e2c79c32f4c6a342906da9ada92759319c68cddf4ad848ffccb79740575fe79c"}, {"version": "175e0bba87c242772cff7c87d0fd8678f2f9f92440c6f972b22da19e9a89b431", "signature": "909e24665338a9c01957b08eafa940a35dbd52c5a056699afac67490c46e0325"}, {"version": "8cdfed1c6612416686c0f3e78f63246ffd1f4f7e3c1edfa3fcd1f972123a7ade", "signature": "e813da480b515bba0020679457c4ad4351f3de33dd9cceb0030fe24d1c8e23e2"}, {"version": "393127e62cea24e2e2d51f52e19c8e3867e874c82bc31ccb26fa97cb2e2f6c5f", "signature": "434e936a91e2d56c3c87d0a37bd61d6333271c7672e7ef4b7e6d3c7f846db7c5"}, {"version": "9a5330c3368d6076a65da477f8ea0ea5eb98c25020a8876d827b1e2fe77d35c5", "signature": "529d4d26552fd130f7268a3cb3720fd10fbdd30d309f6aded9b756ca1eba8637"}, {"version": "135e77a652b434a32d8f65e9b77b6c4a9dbbd90cf179a5d241c0d13fecc3d26d", "signature": "ad3c6449ddd50630f57ae3b2e67625e0bc50116459193083301e84ceff8e0647"}, {"version": "61f8baa51ec44b1c2c9f73e8ec36fc76096f313d959fca2c75e3c3c07162a610", "signature": "aa976862da5873b359364c20d687d875e31f3c89ed0306a2a5e93c9e79564843"}, {"version": "d919436b7dbe6d63fbb488192ed42cf4c2458251c98c81fc3ef1539cbe39e222", "signature": "395b2571925ffaca5db7fa6695cd1d73ce440673fb252766a0454266bb9d31ce"}, {"version": "ff4203ea35bc33380b4e5472a9a5fdad472ba58cca79e4e17b0ced5e412b47c8", "signature": "3331d403d925026c03149175d2b0efe29fc4b711e3d027794c0fa166e9990382"}, {"version": "d58d21700751149b4aff8d1134f5f5e91bb0c4ea08e40208ee2633de108a8693", "signature": "e780d1418d3d179998ca995763b3a0a00ce7ebff0ae3773d8be3c1ecbf23cedf"}, {"version": "c7c37ddbbc6d0b2724ffa8593772abb682f1af4d7033cf720cb757d3f8006270", "signature": "c4ba6f5d567cf061e021c26c6c4fc2d85b6835b6d88f3b7830636c9bdbcbf309"}, {"version": "e81dab61199a4fcb55771e30abb91300a5db4cc679f64e4ed28c16e93b79d244", "signature": "5066301a2a96cabf5293b1b12c051f29085da6d385903fa3664fc11ac6b33543"}, {"version": "6fc0c474f78718d387d7dbe9a0b3e365f41b2831140bc41ef5abebfa2e2523f5", "signature": "f9642ae4db198aff723e3951827d3f955174851c8bbf67b8a7c166ae8d93ce7b"}, {"version": "4a3b944d1e41634714e8fbc83e7b02419141bb4a18cc2a0ca8f9abd6c9f6b75d", "signature": "9b052f45092537e54ae1dd5b049658aa26fa93248e368c544b5fdfc5c8190349"}, {"version": "1ff5c2edb652cf75e1406a3937d8538d483cfb66918b9cc3a4219ec6d57d8736", "signature": "14c5bd8de00f675fed1b24f6d6d64efd4a63bbfd98debc1f7461662ed229893a"}, {"version": "d8d9c8b6c9265121e3f207df55f52006f985de285a14429f0de08f24df8f5d82", "signature": "7fb265a2538b40b5a2b1c815f22a8205698039ec855f57d94b5b58caff56524d"}, {"version": "a74e5bb772dc118d5c38a2e055eef0fc7e37c427dc5bd4e8d60b2f11bb8e284d", "signature": "dc6308a639b9e37f84ee54c43ffe720240b7051e8d2096b9e6341051004972de"}, {"version": "a4eb5d0818de4485da0ce1116ea80f5c0f5ea640bd69e8dd5ce59ca2d61bfdf7", "impliedFormat": 1}, {"version": "eabd6637b71b65733a9953715b01a30b81f09c6c4cf76a4c100eb33b7a3dc51a", "impliedFormat": 1}, {"version": "ee11560011ffd82196b95c994663bdd2d2c85858b2ed825eeb2020a0195bb238", "impliedFormat": 1}, {"version": "850cee49148d0d728ff257f5aaa3262154e64834986ce1cbbabb48c01d012f73", "impliedFormat": 1}, {"version": "b9efe18dcb98c023f274d4affd00d7fcbf54305bb8a5d02293ad7ccd8c29e0bf", "impliedFormat": 1}, {"version": "cf2ac6044669e9e2c94ed4737d8f080aff9596d59fdce3ed4c6a5cde6836e85d", "impliedFormat": 1}, {"version": "b6fc6cca6b1703062c50e7a2ffb5e166099984ed9ec4cf11b648d928d5893eae", "signature": "2b276dc4dd6779b4f20a0a627931bc09b97d9f6cae354f51cb18109c4663fc42"}, {"version": "b8f7f069f32c5534cd889ed4d007fca354ac9dea61f7e6e556b5b556d11d3d01", "signature": "e701a8f63c2fa11426f1fb228a5eed0f6ffab7501917d706d05fdf2eae550cd4"}, {"version": "5149f20ca0cfdc42942458502de7a3605fa3dba45181d13f3bff8388c748c2b3", "signature": "f730175d6b57f996320295eb9f8cba8892961632a617dedc1be95f32c70f8a15"}, {"version": "c9b5333971e890959864c78272a771e7b59304640500fcab1cdfacde8806231c", "signature": "1ce2f46f1372c2056b03cac1ea5465b6343d921fe1b538a76a8bf003b3b9f8e9"}, {"version": "0bbdff037e92666f7398b23b5bb22f58a2cdee386d41cf261e2dd7e053df3c1d", "signature": "8174bde75a2130e28db87c20b767e0794833ca927e76ee2c544ed8fb32bd51be"}, {"version": "9191577500eda261497237aea260f803d754302b308c2a7a0c468da116484e66", "signature": "a5816de5931ef2838334dfcb14d193b0277c2e243ea13201ce1c12720bf1655b"}, {"version": "aa87246527abc01f625684c50893052f1b762eea06b80c739f02c957fa3c237e", "signature": "638cf7e1f31ba34b52ba8a5eb1376d52ddf9e3f9cce4bc091e41a1113138e004"}, {"version": "1bfecefe224a8d9f22f2c6e762c2fba1f6bfa8545a3a20a38d7941cf35d2d3de", "signature": "29b88f0294b8b9f972f7a3d704434ad9cf27e9c10f5300ee213c0eae5de901b0"}, {"version": "7abe25e1c6f4e4bf1e7f3c1843d939b3cf6d8a6934d361e1e8673ba8d51b2658", "signature": "4bdf4e06d190a737a613e43cfaddea174d149bbf461b1358afe941c9a8221b30"}, {"version": "9687d068144c40daeec9e2eb4fd94609ecee2e1967ff2a245eabbdc1d69e8460", "signature": "2a9213248a4d9663b0af512b43ca7c8d052298c4d124d360a08145c2d02dff7e"}, {"version": "8646110de78b296a5904e2957c965992ac80d140f7fe0f8800ca8e76dc9efbd5", "signature": "a5b93891311fd4a496a7dc2622eead15624320be99a40f615121515d3f863c38"}, {"version": "b915e53d1287c2e3417728326216f6e5177fa2a47142fb9e043a2d4b56fc3d1a", "signature": "075ee8e044156e6862c68612c350bb1fe4f25c612686ec49b5ef90d90964737d"}, {"version": "b5e80e5a75b1deea8e8bc5fd9d89e7e6b1a02ac152227dea2535bad44e8b42e4", "signature": "3b27d28f0c5d584f5856525b40dd579d2be5ee4c3efb5bf335c28424fb71a2e3"}, {"version": "53d21528643db14a9d02cd539677acb860f23c620b61eefee24e845c899cc5ec", "signature": "e4b09c21315be1b4866b89297ae4bcb8d76ff4f7a21152e1be15215f18c6abbf"}, {"version": "fccc11db87b105883f96d3132afc65068aec73a3c8803501a03762c5173d97da", "signature": "b31a195bba1a368440db70789c692154b94b0d8d92e002e8af1050040448b6f3"}, {"version": "45d1e52281789c601cc9c04e7d57c7a73b79e31c891e7b2b8d8c9d1b7e2da344", "signature": "1548352004d5c3495876210592d3a7beec9e0b65f6099e9e6673a5821dd4685f"}, {"version": "4fdad453534ef7bb4e1021ca18805df2b48dead07c353a1792015c13d77d5b6f", "signature": "cc468b14d68ce093c1fe2bb3aa8ebdc046333cedf37315ec18ae24ec69655e92"}, {"version": "3a9ebafed6ffa1482b99b4d0339184d99d79e43c35a404e1e34de384716de83c", "signature": "51cc383c64f6ab2a5b037253d8bb4eda27c04221a57669dec5df76e88509ceda"}, {"version": "7a6dd0b8711f68e152bfbc2a1fc66451f0bcc2a8fd1b61481a3a9f0b54f9e56f", "signature": "061d390a8e60541296bf0195a96b6226d86a473452d6fed18e8411ccde0c26a1"}, {"version": "7c1d76957614a8171501a66c0a5715da738fe676c0c1faa240641b53d46a98a7", "signature": "0251080beb4263da93d8c324118f03fa3dfc5da3541e9e9a1f49a8d65828ce56"}, {"version": "24fc8d457d4ecde4ac64b2bed733fd5da8fc76b9f07622f02a55b9eddb35af13", "signature": "11b56de9d4951012c78b2d2d8ad3e34a7f821d43e8df48d19c173f3538c6d9c0"}, {"version": "cd29b20226b1adbeaf3f3712dbcd874305408c7003b294103b9705d143ad9b69", "signature": "0e3fe1b302a5ae3db504f1371ff7ffd7d08865df2ccf939bbc325a330fe5f57d"}, {"version": "746a82ebd3ea9083649bc4fd1c4f110be987dd96c138c50da73fc81a583c1734", "signature": "844c792fd095f7b8921539694743900ca2389780d0b62d9ada3b13fb75dcf51f"}, {"version": "7f1b92a19d2e39496f34fd3bf34abc24b568d3fe7a42d1f9ceb14596b0dfc1c0", "signature": "f53f00c831faa8396c8e4d0810ff46d2faafab1399f97cdcdda961749fac9f3b"}, {"version": "15ff3c8a15caba0e3cf79a1da2ba9af2f68adf2b777e9e8df3ce5295464682a5", "signature": "04e2973bb7d608c9ce70494c33202a584c34f987068f6e9515aefca17b04aeb7"}, {"version": "a890eb51e88550e8129a13721c2ddc484a69783f3270b9ef481bce2e4f706623", "impliedFormat": 1}, {"version": "6a8880bfe90a0ad8582e198e89738b4dbdc4d0c2da12251577789714432dd561", "impliedFormat": 1}, {"version": "78d1fd4698c3dc2e612431c6ac5369406d01512667921cf820dbf140efae0b68", "impliedFormat": 1}, {"version": "0ee820d295866ed35d6bb88bee2cfbb0258913a644074bbdaae4e0e31c5304fa", "signature": "4875f7180f73bd13bd936fa3bbda5372d5d7f96436a674eac0cce00d8d2409f3"}, {"version": "da2c7b3a9459e57a95c7192f5d371521c6089d7fc0bf62f8ce9d4a58176746da", "signature": "66ee5e73e05d7aaf6bfda449b5a3abcc502266a72de08f1684defd42b2f78a0c"}, {"version": "2678bc8cb374979e1530b65d630fae9ff28a59c59ef1d6271c96f4630dff8917", "signature": "44e51dd5a5c8e0dda9c16ba827fdc0871f2cec48d8039c3b06909594b91f3217"}, {"version": "be3a814cc05ad6738551f98072ec6602230397e26b3a64e4ed6b52481b72ebab", "signature": "ea6d10a29e5140b96f9edb5049be847d5785dc03b72a601e9067e2fe9d101160"}, {"version": "9ea129fa4969bd5cbc671835eeadfda6ee3841ba889d97c50184ef3299d65e7b", "signature": "0b86db017db888f635133dbb462c6cd82760576fbefa9def6e226854a9348745"}, {"version": "65fb90ff2d088a40abe139ec5a2b92ba6284fb80151b72953f962fefc678eb56", "signature": "42cae0963df1b6b6bc2a2526e8c8965c44adf7c671e2e3d8dc2057bc055787ca"}, {"version": "99d4f2f8232bc325d972bdf8ca74292ccbf77d0a9ac1a24170bfd989e9e4fd2c", "signature": "57023f1a472d2df54cc8a60902d1e8237f28cc5d9c2f8f1e3043e7b88cba3dc1"}, {"version": "cc0f1ed70da7c2787fa8ba13e2b1cdaddac89fb0a28b43085678b628b0fa186f", "signature": "fc0cdae09f73e10684cecfb17fa7101dc40e575c27d6184e79ce402a07952c9e"}, {"version": "a4c773e7e93633c9bab22972f29f342936cad7de972e9063850aeedb37569144", "signature": "4c1a74fbdd202a3e23a2a160757b159093562ade1c66ab7e14071860d4ead29c"}, {"version": "9f68889bd79c889b785348402ac95038cb5f1c56706184727b630addb1fc9bb6", "signature": "d22cf63e6365a797492c7f48ff4031866a239247d724c98658b681da466c1f35"}, {"version": "67a183228d6eb5fe09f85eb3d47a301099ee716ff3eedf3fc6f9136144a047d3", "signature": "3eb6e459412bccc7fd770a38845a5c561d98981d81590b64dc16c7798e6cdc2c"}, {"version": "b74069849ea69346c4f24a391ee8def5d55012003b22ec60d6a0299bec9c37a4", "signature": "ff4b4b35662b7bbcf54750d0982ff1b2e4904c586766e2ac5c86337f1ca785c1"}, {"version": "cf065417e917672945467353fc6c720765202311b5b20c21046d64eb62e52f78", "signature": "d2f4b6999cc657cddd5312a4691793e73e362f13e2aaec77205b79c9581ab74e"}, {"version": "ae10edaa99634186d86e50adbf7567bd9fe33c3fa69aae46cb907147138f59e4", "signature": "3e633116dbeee6a3b61cc8aad3ad205976c45280bae4239bc60ed0964d63726f"}, {"version": "8895b7a60c231c07553e7f9d0dff5c3d7f88d2214acc98705b88b164e509e7e2", "signature": "7af561e1117d1019edf46461a0164293eb81c03a2f8e067b3eed0c845ebc7682"}, {"version": "f45063503a19c31bb4e04bd19bded2b08d1b52bda3ad22cb845393de94982c44", "signature": "c02f771b7b31a403fadc48d41d0d0724bf52633733c5d3c888cb5b186e3e4a02"}, {"version": "2e05df011b286af12e1a0e4e9fbb7bdd2af3aa48da953764f27036f15835508c", "signature": "5e99cba396ce12190749388695f9857f9e9a819451db9793a57fcaf9882bd462"}, {"version": "ec800676c1a1cc12a524c6d7976bb3309ba643a003c179753b657ab754b1eb86", "signature": "a5ec14a97bbe49e2ebace91d1df3972eac3505a75c3f675aa0b4aeef0bb47cd2"}, {"version": "5beb363bfaa6784fec12ec210e3d676049604f783de412a335e3a220ae86a953", "signature": "ea73b199eeea6cea49afc160161d567915c55e7c6afe04683bacad6fb8067b2b"}, {"version": "e20de5a0c8544e6c1caaa0b0d07eabab37ad8de0b29310d589504f4a48cce0ef", "signature": "a3bca9da230b3753387fb9164343eb651849119e2c66de4f11f579330afb55ec"}, {"version": "d9099fce3452f33f870098a7a4ad66c366fb1fcf2be7e080831f72aea38a5d8a", "signature": "abcabeb699c59df6e439c4ed4381dcae421867080cef2fc1eded77837f6e478b"}, {"version": "969ee160c7647a4492411c8709fb87ce94875a5199a1c36b764f8d755ffbfba9", "signature": "badc12bae4e4ff1a2fba477617ee265c8bcf08f12f1cf5666d74f822b406c08f"}, "2b72fb831efd4fb6d6d42046f3276ffdf7133e3b8257f718a4656bff99033f64", {"version": "ce9722c8051926b460926d264240c246707c822826e5c7f3d0c6eb396bd8416e", "signature": "746a81741ea20dbbfa90a7644796644aed1574966655a3548e86fcd6493506d5"}, {"version": "364e146502c83f8f195d48bed0a1933bc7b2ada8354e0438d4f6e45fd4b17d0e", "signature": "a43c0ba1baeb4ce2e73f2fb380d1c1cd3c90798ce2ab524a9925da875bf80db4"}, {"version": "684dab496ca05836711870bc9faa70203ae5b60d48c78b79ee3c4978dc564a41", "signature": "343adc3b06f077bc8f865b73da1281ed963cab8c0b97970bf6acc8555451b3fa"}, {"version": "401146fa7df148824540295f0ef1414cdb87a8e7d5cafb0472377ea9b9575b69", "signature": "eb6faf3b65bfa293b54f335f2e479db9896361934a32ea58ad8823115667d898"}, {"version": "9163d100a2280d5ab401b5cd0a6fa8f6362efefd789390144ba275585108dc03", "signature": "84544ed9bb7b242ace02689908ecb6028865d9a4d037f118815ce720356cdc09"}, {"version": "54608e9d379f9deac1290e15e4e8af80dcf0f6cb7974ea362cd6b9f20d9a57b2", "signature": "6a4c1b92ac6595f69458188e6d37b5e4e4ad484cfb4846ff2e9e87ec9c259c69"}, {"version": "96eb0e766a8330d5c339becb0a3a4931163df113ce1016b9f7b35ab638b11974", "signature": "ea73b199eeea6cea49afc160161d567915c55e7c6afe04683bacad6fb8067b2b"}, {"version": "31c4208e23748d652a7d865b2cd963c4438658bf3058c183004b3d1b7b7227b7", "signature": "31c3cf4f4125ffe85757bac28d6ab1ca0add923e676b3c07c25ed4fd0652264b"}, {"version": "7d4d32f3ae4c93ec60ea71b3f91044555e9ca04e38895e388f25523c5fcbeacc", "signature": "9063baca27710f781548e06ad55b5f3b6a173f3583e70a658ce0aa248900c112"}, {"version": "057b572f694ee2863d1637c7e588f9fa4d685d3acb9269b89ca351104f7002b6", "signature": "f80fdc652685bbe3128e68f96aab7c02adc03f15837b5e55037b41e08bbd9c83"}, {"version": "bbdd1afaba1838f914e99faf12295ff9c54df25b3241b1bfab420e0fa020aec6", "signature": "cace0be675c0617e06586694cc7686d7d98ee4cd3c4439ae1282b4fdacc04f72"}, "969ba8bbf85f79a53e27e540e7a80c82985acd21ba16f376baf7c5e392c2e793", {"version": "cb2b3e48d4683bbd78edc061a4978b4fb47bd19a493d4b5f7c80cce351b3262a", "signature": "4e69ac4161eaf7ec111a321ce9258b3050bba3953f9c4a2494d60017a42b8c71"}, {"version": "c7e05b8268a0ad7049b994cd49de4ca3b7c4629aef2567cfedcda562298823e4", "signature": "b0a844f50b549a626630dc8d318bfbe6e4eb22db570ff19a952c56bb782c6370"}, {"version": "11ba0fbeff6729869e956090dd2150978e9ef4e08ae33c2afa97b57f3ed49aa8", "impliedFormat": 1}, {"version": "79c5c4e1843a8c557ca7cc71bedf7c99847dc81b77c19fa6fb0ad2eadef8c0bc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2eed828a7189c5ccc1a89c1484157eef7c5b336184fa3c3936eea98b100a9833", "signature": "3c90d7200a0464afc4072fa1b79a3042c44f6e2d8532124f421f5ef21241353e"}, {"version": "14f1bb64a1198f07e43a7e24d6d1460ade3c1221611e82589c0f858704f77bd8", "signature": "c1053bf07d2c3432618543b6ec0087ad7a42a040ceaa88e02d9afb55ed8c75f4"}, {"version": "3d5ebdab81a4368fd1dd9b169449a270de4a496aad2901b4ec07185af5eb0aaa", "signature": "109477510d70556d223dcbf1b56c4e418b2ae634948c47d745a815b7a20ad269"}, {"version": "05e9ae1f231a106e776c3e163933f01dda3fa2ac97375092da5fcb9503184155", "signature": "3dc195d2292085e9273af430f9cd7536d0b55f6fad8bccad2c3c3666e665cbf7"}, {"version": "c47fde9d5bc3ad630dd6bdfe311c6f1f29bcda54e4948fa2d0ee8349289fe087", "signature": "5736f31049d8874ca4966316c2fe5a26b4a213c8243a6ce10594534f85f11674"}, {"version": "d7db3a7cd0010357e111585bdbd95a1c4b8dd7ae258a8360a586c75315817e99", "signature": "e24e1ab6be79676f21680f40b3f08279e9b94120d14be0ef52bd5105b8fe4606"}, {"version": "1b9c6eedf824c7a2660885e690b616dd638987c29dfeb3a04eff4f959cb86a6f", "signature": "f573cab6b6991d342ae52ddc227ccf92dca03266241db5912a7a7aeda320c1eb"}, {"version": "9b96273ff24ad34d6baec646025e8410103af87e9a809933bc4e432be809b114", "signature": "fde0337a61e3208e8d08282d2a9fce86682d0f9fa08b8bd5dc633cd4597dd71e"}, "b24a7b1d29d01c8c7b37e914728cf463d7f7f704fe3c4e9c8460544df7d15bb0", {"version": "29aec904af26c66a083e1e168dbf3067f5b850e02761f5f9ad65f1d98f65c0f2", "signature": "f3eddc6a8beec771ce1d9e16e22099eeaa5257854d64050a91c3d0ae40898720"}, {"version": "54120a8fc592d598dd74377946420741ab7ddccf9e6d4ef87bd39ddc098d1823", "signature": "7853da2b916c32ae17ca070931b1001f8d953fbd809078523a06b8895a3cdad2"}, {"version": "cdb5d2b6647bbf3daa0992d257600664cb3c53cab0a9d3c9b21820bedddf5052", "signature": "616b6d9d8e12a694ec3c816791f1f4f8c8a60ddf1573f86347830daa82a9861d"}, {"version": "c36b3f26ca255d8541d4b2800176d10c0a09d6412a7362de27dd4dc2aa37651f", "signature": "f02de6700dd4d41e1a747fc85c35fce1dbb9cdd7779c559f04ade632fd55b4a9"}, {"version": "e30893fa8c1a4e441e7939138ab9603ae563472cc756ea44f015af49fa023e6b", "signature": "8d3f223aed0f3b4961063ed5634ce2619ce48a6cdda043ad2648452bef85b4ac"}, {"version": "65e2d2fa358a6e574660281d22ada0933b2863251d150ff48fffb324b23842b2", "signature": "5acfdddce9c28a9ef24e810fe4839ad34439d7a995a42e4590c46036a5d03fc0"}, {"version": "de851fa3c0d45494d346abf6486725dcc6264a88e191f396b187ee95863989f2", "signature": "87351b1b046960e745624565d1019a9d446f015e1871b52400fb1a1d9c16e4b8"}, {"version": "577236084683480eb0abac1a527536926f918e99d49b165ef1c7755976338d2f", "signature": "d13a17ceabd1ad4149a1e7aaf9c4c93d9139ee3e3d56b803b81794bcafe6703f"}, {"version": "2d7b368f00484a7819836ea208fa44c9e71c9d14c85e50da2b1d3cee89852b82", "signature": "232bc7cd86071765d882836093c6e3f05a0d645ec5975c5e53ddb9633e4c3d2e"}, {"version": "dd4e4f95e38aadaafff8519a3f102af247014efad62fb6629b7f40d28ef7eb7f", "signature": "8b9e4388dfbe3fa5bb77f1f5dd70276932f5b34eac62dd15d0efa08239bb200c"}, {"version": "d3fda6133ff0ac483206349ba587444f6e6869d619f71d2b26c57fbef0630cc0", "signature": "89fef0ad71bcd5094356b9f5d7da61972773748ef81cd55dc7d81abd75577ff8"}, {"version": "567c2fefbeff41b8dc6a38de2ab82a4a1f3bd86df8352fd0adf238370f4422ad", "signature": "8e6bb866e8a37c93f018dd7163b97f99f823cec653a62dad3011d870a053e04c"}, {"version": "3f2bf1b28b9a9b1bf7c2a2f0b05f38a6f91bd0f14287a984579a14db09d667dd", "signature": "d90ad862259eb076c8d28ba6329fc7cdf90ebaa985890e77b8cad0fd1e6d2d5e"}, {"version": "b767c26406c04f91fd73abd590e3a8a2923aed90573facba1868c6ca2d3d2b74", "signature": "6fb575e0f93a7441cdc7073319e739ec147bb15b6090b1440a4f80fc179a5ba7"}, {"version": "b9699d6b8eed747ea73709199c44ab101e37c90e7f037c91c1da6f04312878d7", "signature": "4ff21e50436fb183ccd1a9781cec66499ef161677bf5b8a96ec1d3db81476b2c"}, {"version": "2182e480648e1226c5e20ee3480d9723f1055d8fe75119e266c8093058948af5", "signature": "14a2f6c598bb0fdb03fd343e5a02ee8bfbcbf34c23f3a49531d2a39857965a87"}, {"version": "54dcbabbca4588382ce546bd616d37459a602de8f30c73c0c5de8145f3079772", "impliedFormat": 1}, {"version": "b21b7a426e9a4dccabd4f132a45ff88d290a622456d557694b23f38227a1fd66", "impliedFormat": 1}, {"version": "7860c4219e664d2c62b343b40413e9d321b2f71c0630244b4fae0660d8fc0420", "impliedFormat": 1}, {"version": "f1072f6842507d8dedcb0fd4d2553842f7398a88ebe95f0871b90c3a97484c63", "impliedFormat": 1}, {"version": "d30be035e383b47a8eb119975e5b266089d511a1d3c11cec28e3e18927902bc7", "impliedFormat": 1}, {"version": "92fc8cd4a4abd0340120358e785c6bbf357f8e3cf3f1afd0fcf4879e92c601bc", "impliedFormat": 1}, {"version": "904b721c0900e59c04381bdd3fcbd8f7faed4ee93ce311b2d6f1a896e0cfc80f", "impliedFormat": 1}, {"version": "7d37c4e2e4a9b28101c08b276d9830b5223c4ad02b9d1d3190f4f5ee12e48e2a", "signature": "eb31bebb31b5558c11323b1edcf601be5c78f56b550cfbf4578598ab784f4c7b"}, {"version": "d6ce552eea970ca2148aadfdeba4f969252d5e989cb14b6efe6a1b3750969f12", "signature": "fb587c890032a35c7f30b7239094e525406f17f95351b8e6fdc3bb88ff6aab02"}, {"version": "0613e9a9415db68c85ed76160e8e93ab8a2d8c6ca3e8ca715d6ed5d196724b4d", "signature": "3687d7ae294118b20c0f2b70ff588763a71b24e593092354ef7cb944f00939d3"}, {"version": "6f05169d600ef1e1844ab9418fc75ed492bddd23a1cb534261eee204ca187287", "signature": "2a05e5a1c5a1ef5a5331aaccffa1a4ad77f89af926d7d87d93d5f612100e330b"}, {"version": "9aa118ad7f7f4f84ca46bd0cd483ec3d699991af08808fc6c5b4937210e1d59c", "signature": "b77fe0d611be3c57afb37efbcd600c799509d31a8a59a3ea01c06b41866cc3dd"}, {"version": "6f2959b9f6985a0cef5773521326c205880e3c7f221df152de136f64a097d96d", "signature": "f7ffcedfc27eb3ed6d3ef28120f3bb9cffadf5aa1ec86bf73777f521d4a991b0"}, {"version": "6893f291a019310c3f973a4815d9544922de851ebcfc9a9cf3be8010dfd1d8d6", "signature": "7baccef88670a3e45c19bd18a050f3ede1c00a5ed80dd3fc4eb3d9127bfd78b3"}, {"version": "c098b323589a3125fc7d14e6e04f22af3851cf29ac70bb814ac81a7ac5214fe5", "signature": "f1c207e9608d76994e6e0eb7f56276c7952254f3d7c6f73e33273c4c3ca92fb6"}, {"version": "82e8d8d43fb51926ae2ec7ad1e619b081ba1773afaac3cf8753a71c41e780acf", "signature": "128d4af0745d550342f68b7d350d21cdb9236c11b8b99075daf1245a3f42c705"}, {"version": "7f55900bf8adb4f4584d45900c2173acf4e29181bc044c02f84429eb8ea2f993", "signature": "842b16bb205df100255bd18a404ecb8f67a467f053cceb4ccce650d45135d3d2"}, {"version": "f82c4b09fa6e798c7fecbcaf06f200b3f827546b7ff668eb98aa3a46aba6ad45", "signature": "7624af53136900cb397af4156007815f871bbc0c5bd7bee740a52211d9579c62"}, {"version": "fce2730f4103fd7fe5303d4eec20b6d65c01d9c0931ab2d4b43e2cdb1788c204", "signature": "b12c0060d23ed10f4da3ec0ae3b25e71ee22798be6a0f0ea408ad439f74c87d7"}, {"version": "3652be454f7c28fdf483e600181baf515b0d4b2104235afdd5a2fe4bf148bdcc", "signature": "0b42deffcf594387bbfae3a1cee7857582a84b023fdb7dfc1a4b7a0fe8ac9f6b"}, {"version": "f1f5e94ab264af6a850342a72398036512d04cfcd2f71268f2ee88fa5facc87e", "signature": "692b302a038587028d3699d20e838eb36219b941495e06fbf9e3347b7b1d32f9"}, {"version": "9ac1477e4f0a5ee6e73d7fd22e78ba705f5e632f93f6c5fdd0d2c9c2c3ccad40", "signature": "111c425efed7503849866a10a4876b5d7f2281c49291fc314aaa3c09452e697b"}, {"version": "0c8b01a924ff43afe5fe4866f8a811db43477857919deb3e349e63b4fd89fbab", "signature": "833c9479b55255f8601ea469ad6b2f95867a7c7dd3426c8e2053cc46f5320bc4"}, {"version": "5d2a04517d5fb1aa10f13a3b7f08e372b8e3ea08f11f6fe8f8205151daf0f43c", "signature": "7afcbf2e790d74ae9773c94161715a9153c245c6d1419eca2e9d51bb5a74d90d"}, {"version": "04482039ee04e6568da958203538f1a78c3a461ccd4fb50719a8bda781a1947c", "signature": "0aa605991af3589ee7bafb8be53df5f6a67f941db456bc0fb513c926dd19da67"}, {"version": "c19b302923a1a6a621638073b1846f26affcb2d270566c05d9f859ddcc90b75a", "signature": "bcde2bc0efb87b9e3e869e5e5cafed84ff35037e67c19e35cbbc5a4d5e211dcb"}, {"version": "0fb0c6d83f51e3ab49d381b763d3e743d80b9047c4b10ee1866952e43e7bcd60", "signature": "b0559fd9734266b0807017eb5260ae05d5bf2ea9d6569dc7fcaf51f3e94059c6"}, {"version": "f6c9b4c8b55b73fffc09b7fb5262d96e23c5241dc25cf2428d8ab63fdd100165", "signature": "035bad4a304d87fc27de5ca83e13283f200b4ce471cc1b52a5472d8b459509f9"}, {"version": "b57b3a9b256115955f4f2685031c7c8202b839b370ef1b6a5c8c01522ea3027c", "signature": "2cffaa690c5f712aa8c8d6f76981a9c8c3e15b3e21e679fda6ea16679420e2cb"}, {"version": "c2d17db1c5905681a672a461fe498c1fccadfb2c80dd134845c149bbab20e2c9", "signature": "f154950e72455d55a6c70e8b02170e462edc90c067758c673301b636a4d28e29"}, {"version": "0b7a91485a74fb49d315e7e362d32aaf3b1bc8e74da7017eaea1bbc14fa3035b", "signature": "f50d341cda8ca2a89f37994077ffdff5bb5bc76358de4d7acd874bd0c0cd304b"}, {"version": "8d0d9da0585ea392adb81dcce8c1db0620f27c0a902a77753d4a2c70319fcc73", "signature": "22062363bd12612cbca97ac6aee2b631be9392d1aa980f5a6d77cda1b3b41ca9"}, {"version": "d41515bff7a45986652bb285fb9accd73070a15eaea8e6271e8918630f94302d", "signature": "17da42f7a96645558f94f88c3b3b39d85a21127d85f70f062e9dd7dc444b53af"}, {"version": "0e9f14fc5f96394fecd2807e01456cfd8f13108b1bce63957b62023d8bc31446", "signature": "f9a1b49a08bd8def631760028f736f9007d17aa7035d9ff5a49d9bdd214c8b14"}, {"version": "8151eeb32694bfdbda1267b8f5a93abb261e7bdc74d7bc64cafab51f22fa368c", "signature": "b828f67422f7e704d181f11c08c82d62153f65355a184db99f443952e359849a"}, {"version": "8c158b7156cdaa02c5e26bca7e30f0204caca1d59e5243bf930a05cb2beef079", "signature": "6cce660c55af1ad7f68b0e288950306972b16f029a69716aa9545ca8d1a57feb"}, {"version": "637a55cbf08aa0c1cd0cc4b780b0d6d7f9e2063c33f31ad4c772e8039cbed6b7", "signature": "e6edf245b918fcc0e576dc3db5d3ce4d54846ae0ad873df36dbe2c8b7b429e4b"}, {"version": "6196a0a38acc08bff08a4c9880b54a8009eb7df14b840cdb631dbe3442b666f7", "signature": "e0f9a068d48e3ffc3ab2b2002c388ba9dc728352d4f5c9af2ad724a5f708d247"}, {"version": "bcd37c72f40922975034bc1aeb957bd133f12af9c1ebbfbd0d75be10062b72a2", "signature": "a8e63a90d988d73d2b52ae626e754721edbb28f9a3256f667f44459af9a89ab1"}, {"version": "27f007f2dc8e93aa94c7f0aae433d5911ad159a88cbe8cd50e95281037463c9b", "signature": "e97ee0f667a76c9ce8b20507f144debeb37a8cdcca831015ebc9d4ec9008f4ab"}, {"version": "4b377268ab43d7f622fd2b975c3a42bde34a42e1eba21185e67f7b928c7d946b", "signature": "1da27be73a09d430c2a359606196d01e75405cb315ddb25a73692b8dd7c74baf"}, {"version": "4757180d54526d779b02fa7d4d4420b60c778928ee810a10be7cbdeb559b5a82", "signature": "320d863fffc0a781c42a9b3dbc6c87a5aef6aa328f05c6caf0c8c287c8da8065"}, {"version": "3e7f9706f68ced56bae408c5e10182865f80ea9901f6b7d624055b8a153fc21f", "signature": "ce56709311eac194946b826bc8321f9ae51a664684de0c951928c4077806133f"}, {"version": "5651c3825a5c81e7321cc9ff74ddad9b9b0b5145712f40750cf423ce401122a1", "signature": "1230339ed000116214eab094f96cd7bf7b232ee06ad937ff9cef99053d87d1da"}, {"version": "0fbea23a532d80159804975b91377043b218280813e6e428925b80b35d273626", "signature": "a463a3ae572ef02021b06d9062230b714984fe1368fe6cc59dc1c9d170cbc536"}, {"version": "7f0b713be3ad14ea6bec48041d80531e33f8b09f79aefd70aa329c087c575a57", "signature": "062b29e7f4605bf76bda51d1d66f28a5cc7903440ecf8ab38dffd454cf155f4b"}, {"version": "e565138c499535dab8e237ad68b19d7856ea0eb87f494d99a4d5f098d96e956a", "signature": "7a0ac0f8f5db1f049eec818e807e56c4032a9542b8f71266bfaab52e663022f5"}, {"version": "e62f6c7b9ca23c9ac999e8b22aa0c0f600578024cf1e84cdee414c8c8e702bf3", "signature": "b509576a07f01b63306ebc500ed36c8c8d897dfee58fea261407f262a45bef8b"}, {"version": "cebc3b0e2676ace5946b3e4032580796f2de9973977a85e289512a164d00d959", "signature": "f1e883317e39f626db9a99a831754d37a63a74bba82e02a90a07929707613309"}, {"version": "4d2c84414cb9468f055a6d3dee84ff3b4ad8ec692ca68811959970aa61818746", "signature": "eb7c0a55ea711898436045c1be690a47717d5f49877f5f051949e7a76e1c6b11"}, {"version": "19cc309ed978810de89bd83de283519b4637d2f21f14d1914de093835a08af18", "signature": "6bea3ec00aec67a2fc88fbf564d88a98537c17ecadd3be9f8d0d41a6146af4b8"}, {"version": "99f047f4f7c50116dd7a05aac549b162ff0187e25a9fd74305ce311e4ec1e24c", "signature": "94f3c8adc63bfda779d84b6dfe90be3bf90c0d8cadfa3daff2f01770b2a29202"}, {"version": "3078e70018fe10ffb2a22bfd87766ba8160ccf498a5863903d492bd5a49c8e2e", "signature": "7e7d9f22525c40123bda21faca786e21948eac644978d0b2c5c2f74150622551"}, {"version": "9c14b1574537c27c3c853ce9661d912ea619ec88291985010d03b72a25b24944", "signature": "f1e883317e39f626db9a99a831754d37a63a74bba82e02a90a07929707613309"}, {"version": "0c636965c948948c28c1a73cb8ac34913b3bf330933e1a1196afbe5d46c513bd", "signature": "1ff0537cafbca656f42f6f82421b71196bc2174a969250c0d4fb0a2c4a20e026"}, {"version": "88312928c49a4e302199b9c7f54c709e37483996f7207553e68cc2a6adcda294", "signature": "0274ec7659d91f1a9708c4602743cff06db50be1f88b4bd835719b61f4d4122c"}, {"version": "b3845f3a4fb5fce6b7c02f97e02fce0073cccdbd00740d6b54d3a35eb42801d7", "signature": "941ad2036aa70db8b842413deed82f8e227934cf847d1a3e5d0053cc34819b2b"}, {"version": "869531ab274214d692e02eb843c49add8776c0346706aabba19b57291a18aada", "signature": "f1e883317e39f626db9a99a831754d37a63a74bba82e02a90a07929707613309"}, {"version": "6df4565c0b81e790b026971f510423d589df7d0a308c38b32b684c351f4e7d06", "signature": "b443335cc41aef03dd496cf81d94bfe1c813b4aaa805d514d48b887de207a278"}, {"version": "81343e9564b7632f770faa7a8ccf68d50cce742c3a79a602b23c45a39ac3c7ac", "signature": "1a1b383421ba07b6527f140b43ac4129bc843000f9578f7109aef70e5005bae4"}, {"version": "c3c8f32ce8a94656bcb3857b52704bfeb8c3023a9f8e5a18874f71fd078447d9", "signature": "65f4d092fb2eba9158d57fd5058f2fb3110dfc14075587b1f0a1b42e08ddf319"}, {"version": "9d42c4b7671873ebe441e6b58aae02f6a6aa00e6a4b519503f1940300a7b6052", "signature": "8ca267360dc28e78362c76fc89879a67fbcfc6870fb0421c7ea6ae6692bb9ffb"}, {"version": "139706ff1386b4e0e23806fae72c4955b82af32d766030cf889b4ee6f0bedd10", "signature": "d47bea5ced1bef51a46898f332644136b7e6f2594be602174f8153fe98341e70"}, {"version": "196ee083a022b05fae53e791f17fe1ea9db4ad44bed2ded95044ca4989f31f8d", "impliedFormat": 1}, {"version": "d1848043d55fb7c15ed02a3fd1262f1b80e36e53434ea46ea8fe61b0abdbdb2f", "impliedFormat": 1}, {"version": "7606f813433ba849caab293de4d8f0c770c5de7ddd6156ab97932dbe727c0dcf", "impliedFormat": 1}, {"version": "98b1a0cdee99d1784fd7a5fbc99b5d29da595a75d156fcafa1909bb2a760b57b", "impliedFormat": 1}, {"version": "3331235a8d8a8c039312cd4ef5f6121b5d98b2ca6b7070e4bf6370f03d8d95c9", "impliedFormat": 1}, {"version": "857a7881aeb5c60ec5b0f1eb4d63561d61d91da1e46d4f79c9f98c7815054455", "signature": "0836cb54e72bc71da2262dc519fc156d8e3d93d1eca539917c95112c61ec8b44"}, {"version": "140279cac623fd1480fe8686db89c5c69e628802cc9388c89f9988b24ff339f1", "signature": "52b7c790549c2622a3e7a4346118a613069ef1858fbaa44148e2007acd7ffec9"}, {"version": "2e28f09fcedc0f551d3a6a91a4ee095f5e4b62f5067eae69d06ee6f3fa6886af", "signature": "2af96ca5b6e690bb6b033b3078fc99d5558669c12c0e48a78430bda716f8ae7f"}, {"version": "0c174919e92d2a7a13376b6f555af56db0cc11a189d8c86dd9141f0d3e989457", "signature": "ddf1f5ca62aa847ac87622e71fd74676e41f62dcd30060ba5a255a0eca31808a"}, {"version": "9de39b9fd331828d74575c09ad3e454b27a636edee24567c3c1c97b4ae7399f4", "signature": "dbd31aebd51265394d09363d8da921e18ccdbf83cf8294272a40adcef6ff6ecd"}, {"version": "0ea0d8bfb0b543229321d98f1d42eab243110b4d86dcb5709e3c9f39abd9f535", "signature": "9b941b97b6db653a07b8a3eb5c1d68a57e3b6af2035f9c9233836f851ab0d195"}, {"version": "3e3c6e48bf67a4900f3f85538436ac9100652478b0ff161b63f2e320282dc8b5", "signature": "385f09951c31ee07f49222abe22a46a6d94a838618746808b97e416e32666999"}, {"version": "e4767cfca0d2cb21449f89628143a6975815978284f82869f1d8c9ece8641ad0", "signature": "4da2badf7555f44213dceae55a22e84c08de45f3ac2dc5145b35f544353ab6c8"}, {"version": "e8f14a4b7af3774c4df37a1439bf38cc1a83a5587ba5944773f183a4f8fc679a", "signature": "58feeb5f454ce2112908e1a1b3df384ed65977c141b36a5490df853121709138"}, {"version": "2d1ee4f3d144f32332f4e2164a26db4dfcb3d85f97ff4c0d1cabdbd268f4c27e", "signature": "992cfcc36319db7d11b3a8bf06ca8bfb67c3b88390bb1de1a6290e763154e439"}, {"version": "39a6133fedd721b67a9f5ab86854740b912db4e16c2668aeea2ff18698a660cd", "signature": "3bdd72c07ef3b8e68a886ecd826e56376b0b1b0125dbd942848593278fa6d610"}, {"version": "0aaced4bdc9520b32298706d7f3a13d9a73eb8e062a0993b8c154079f959cc5f", "signature": "ea73b199eeea6cea49afc160161d567915c55e7c6afe04683bacad6fb8067b2b"}, {"version": "a4d930016d8529edd8066cfc0be7fb49521013ca6234d78f1108bb8b342f36a8", "signature": "1e943165f01b025e337fa6ff927fcfad1fbad3d1f12cea63cc10893017ab30c1"}, {"version": "00b17dcf0db42b344d5b29353dfa5bfdb5e7442022e8f49911b1c4ef9d13dd1e", "signature": "19880fe071957b2e82506edf5e132e26be33b677af9867328e363abf497da95d"}, {"version": "f187300505ad48ae2a7b355a19b8996fb3857d9d0ab5c96cfc3c914092907883", "signature": "8daa4ff44cea4ba78a6f7cbaae87aaacee373119e1e3ae0e3d705e5ae6ced2cc"}, {"version": "bb1bfa981381683256aec5914aa0519bbafa21be2bc17578f72e041bdc0e70c1", "signature": "ea73b199eeea6cea49afc160161d567915c55e7c6afe04683bacad6fb8067b2b"}, {"version": "0dea04abce1754c7ffb834364b8270dc3131c0366259579be0ddd76181cdb056", "signature": "f1e4652b2f9102d04a4feb10824c8b56a730b09efe5f9b4627af2909c3572aa5"}, {"version": "407b1d56ba223f79edbbbb3077c8910937558b86e3c19bf2526db1eaba77848d", "signature": "96d6f1495168c8e7170892c3776984f6a98511ef20830cc9ff7810ed7201864d"}, {"version": "08fa08f689fdeb5d8ffb6df72bf09f71f596234dd28df548f97c720ccb554923", "signature": "352c1d99e55ce0027d61c5fe96eb475b88694557eb577a59469049532102576c"}, {"version": "8f18c2ca6009c7ab7dbfec0b6ca57d97981e19573358ffc2ffbc11b6bf9fa5bb", "signature": "e25229dc83aac1700e88b62c8263d0827b2a1e824de91badba3a7b1e14517560"}, {"version": "044acdd3451bdba444cd10d63f2ca35b2c6869fdf47ed5638058ba4ab6aab557", "signature": "32148f91c43267ff509f7938ac49dae0dae9717075b7ae5e12551dbc58049b0b"}, {"version": "7a01d9dc9aa687ab3d982319a5d13f86304c873bed2020713730eb7a8c6a2c9f", "signature": "3c876467035c085c51f1d79a16156ff549aab975208c3c7118c848e540280c7f"}, {"version": "b02100099d0910e5150d921976964211771614388e0a476594d7046d85614355", "signature": "a9f443026ca09324c56c151b10fb1b5bd1a091ad52f685d3011523c4423fd91d"}, {"version": "1bdc64d77e7b41c406dc94856d98270e872867163019116306013e808b031765", "signature": "c01e390f73b41f967d3b0dec8e14356992aac2e57a0b2afe4de4c5be4a64ef96"}, {"version": "a307fcb4017865d02fec7c275cb1fc7c96a16d54e4175014f46b85510f1f0fb2", "signature": "89ae17f0aff6a228668369b0e020b0deb0af3bc3908226f087bbaa8ffa05cc4b"}, {"version": "d90c13353411363785eaacd66e1c4d60950602238c1554a7ccc9128b654ba357", "signature": "362df238fbdc9869bf3fc4873b553372f6a9c6aa9196e110599a39f26e5e55fa"}, {"version": "d0219b463afe0e01f99c1b17f35a0711482484de8b5544f0767fb53f912a8382", "signature": "b1af3a7e82db7d8fc100000084f6cfbc21d05fa0c8e7c603c3d03d77af9a6865"}, {"version": "316b9984eb51012f733cb8e1cf5247705a8a45aaf8d3f30a0c3c745292167869", "signature": "934cbd035f21fd19c5bd171c204cc0ec5dee68a25b1e6d7c518afe99f1654fd4"}, {"version": "9988e45ef4643302807392630f6887a6c515b10ffc26af83fe20f7a15854fe6e", "signature": "2dcc1606a19255c1a7bf997c0ac420aa8e41d0b47d859476a0c4624445b9398e"}, {"version": "cd5ad71141d3749c21a2daa9d623e5aa89f98ea5575f3bd96049d60261564163", "signature": "21b56382637f32b26404fee181d1da68e89d3e91232b907cb8f9c35a8ee16c65"}, {"version": "45fb9dd5a15a0e3a0f2c99bc3c4532bb97df2515c693ac724ac0091d825925cb", "signature": "b6c5586f65bfd3556667e5046fa3a204a1266d511aaadc0837fff2cf26cc4ec4"}, {"version": "220a777b553987ece81de045b9db6e54dcce3e665378052b0cfeeec401ede282", "signature": "396870901ccc2348a7fb15787671349bade4f72efb121dfac8b80f7fd4af7bbe"}, {"version": "08a2626a17ff6d24a967d49493f3c6b4eac246c6ad397a1814740346f8d0b9af", "signature": "ba0fd4217feee2360910a51bc19a571917dc28cb6d1ae944a2091cf8c2fa51da"}, {"version": "dc2177701b15947ce8043018ad0d77c79a65530fc7a04515fbb83e69b786a888", "signature": "74670690c18ba9cf4e2044bc854f498bf2d1e013607a205a7621af51bac4d2d6"}, {"version": "09ca024b57da9869bee062e1827fa53569643a329e3c343dad09e229e80f1b6e", "signature": "bef7318da12f99166425a9addc08866a22a5671571b916bc934c4e69a97f21d0"}, {"version": "4f156b4b3c53dcab777cc2353a713900d6209ec3d1831db8f463f66838482104", "signature": "b36d470bdef65cd139186384069ba77b563cbd808fdb8476f7cb37d4baa9224f"}, {"version": "05680c9a4c81d6e7bd9d11cb9409d5b42e6ef1271842e6aa5c733574a1519b9d", "signature": "758bc04191f0493dcbcf184706d6e8289e90c68e983718b11238e5c14d6f38de"}, {"version": "0d8af8c5bfcf893b4a4b828051ed68df41387d425f35c0bea9bf8eb9613d35fc", "signature": "f72a4ab2bf5ab95f2a2f0b8aaa2fda258d9144de5a34fe3ab5361194fb2dee2a"}, {"version": "2756c2bf0dbcab359928480667ea69da280051f4c5ebc9b60ff9abfdf166b2bc", "signature": "82ca778d5fa9a5b019cf25b60043f5bb8a56b525c46d7f334194aa1faa8e2983"}, {"version": "ca710f59f8b76ac8bd490df44d581cf269753e54e5015a1607301c9187fac455", "signature": "9730a82e3a4b407eb5cf7f2f41bbd03a1badeeca668d1326dd00726b2e665db1"}, {"version": "63bb760b9d12d94d008e007ad842e3f94b51600ae0dbca805187f35d137fec42", "signature": "0de930b4e6bb4a9252df48ffc4ece0ae5464044690d0386379392c678a32f74f"}, {"version": "ffd107a2696eda3f7f8e51cb92a3b2357e450c892a189aa857df06c7b50a3155", "signature": "9824d918504378ec76fa424cf2677b515f3d8debfcc570153044eec983d74590"}, "6fdee6c7d01b6ea38abfb75dee47e27f67716813a4d32b45c185efd4d231dd7f", {"version": "68bc841723981b4d2a88606f8af00e1654107e271b2962d6d4d0093a2fbdd8a5", "signature": "70ddbc5262d9fe19ea634bf45260686439b159791e75a0b21c34cb5084737543"}, {"version": "b6bf5d0ed77ff4b10ed445bff07bf444e05a479023f9e49a86c6ecd8fe3013bb", "signature": "6481d0413c0e05a1e56f74e077f6cb1d8bef46d41e811fb929afece58274ee80"}, {"version": "84620850163d37cc04ff4dda9da8835c41fb89135f866361615cc66adbeef427", "signature": "bed68db1643ebe734c219e24b473dd61b129cedd34b9d333a11be1f57c7f2a9b"}, {"version": "a9a98103d7d331ac35050e23223bc3ea97086a8d4bdb68ff4706e08a7cd92f2b", "signature": "a2c3a171fa5497b6141097dc4eceadd1f040f0ca749eff4ff688c246f11ff00e"}, {"version": "dc12eb956c497d38764a061fa0dc3b3ff8b2e37a6a284eebd763727ef2cce8dd", "signature": "f4976fb90ef03eee6565a009993da1c4b861a5630d740027f49c4a0ca54c6392"}, {"version": "df4577f1cca654cdc095939ea528e8b995263b542b7cb5f895b69018a9a17ff2", "signature": "e8c83f37796eda35240f89bebd8510635f7fb946880146bf92f0fe411fd9f286"}, {"version": "dc80f0abad010c360bc6bb75921d4ff129a68aca1213e3fea8ff29dd296411d7", "signature": "30c73e0bd08eaeb49c49b4ab22c0780ca7f8f80010b67f7642b619109d994e8a"}, {"version": "2cd995e26ca5910c07d30e0b49bbea56c4bc7963ac1aab01c8b234826ad5f9c5", "signature": "51fcceaf02d328f69fb251cc87146f8e09b27a9153109276127901e235bce3dc"}, {"version": "9fcd573efe6df9e1ef93717466ab8f03334a2b83c0079ff2389a567d45b495e3", "signature": "e8b3815bd65c2121fd5309b727226a4d6049842c4be09865748831269b349bd6"}, {"version": "f025f1823b8a18a419105c2ce1855e665aa19c2bfae5d5c590162f89990eae7b", "signature": "4c59345bb724fae3b9232a3ae3392d1dba5ffa9c8e76e4cce2769ebef4e13650"}, {"version": "9bb003d33d664b0a462a6dd9d9b02fb81ca934488650e1739e579d8739f2b385", "signature": "c36ba3b4371b2b82db20e1357b04cd0b0776f93a1becfafc1807bd6233c7898f"}, {"version": "a2311ff7bbf7db7466c2483dd1000da84143a99b0b26280dc3dc9ca0d31eb41f", "signature": "44d4eafdd7e5bf41e4d13a7138e88e10a9b98a405ea10bea53c3a1ea7f3bc48e"}, {"version": "897d9d915059191ca92e838aeab6ba2d4fc55c25b8ef5118b716c10ad78432d3", "signature": "dba16c52fdd0de0b3b28eee885c93a499a3f340c7c780de942bf5674ebddcc2c"}, {"version": "6437be61fcfcdf45a37673dddbbe9a3dffe9a1efe38d4193332272001447bf3c", "signature": "e7d81e5eef39a69c667edc3ca6be8e68d4c7eba8ada6126923372942c9b887f2"}, {"version": "2779e6519d1c682049d98729296d9d20490c1edbfa5c82583fd59b8150478119", "signature": "6df7474dd14ecb84220fff8e7be3d35c6e200d16caba0bb573908aee928dcc91"}, {"version": "e7537885e73ab7d3e20e05b51bec69b04eece65047938e0542473058143b19e8", "signature": "898a7fd5bed5210a68ecfe6084286f6803d294402a9a93afec8cbd78b0b762f6"}, {"version": "2e208cafad2a4e2d126cff137f341544285c3f6bfe5d3f223373fb441ea60c63", "signature": "4cb041ead82e5783bed775599bb0a877ebf2b373ff387cc1fe0ab872af2996c7"}, {"version": "a7a9a76cb87b27defa5b0252654353e5269fe2ab92a6b9b8d2ef48523a455551", "signature": "77638df5a8825c1a14f5676b10575d8304135123569a2ab8c37171ae4245cd2d"}, {"version": "7c0666e4805f5b530b11301d10213e76f055756592d23868d721804f485cea2c", "signature": "aff07781d4e805c055e998b2f9d1eab1afcd5c0d8f7cc80760444707d4b494f6"}, {"version": "b4c22652734d4f0c29cfe0ea0f9cac2ced9c30635f0ae1218e06d59f16a5d333", "signature": "b77538baf22e80670ca341fbf7ba92560663dceb5c7f06d99f84014d7166b798"}, {"version": "2bd689785cfd582d82c6cd7ba6f79df2836fab7f21da2dcbdefd61e45462749a", "signature": "674732ea5849688b69502fca7c5c040502f3a9a3291bef5f1cc50b25d51eb6b9"}, {"version": "25c9c2407eadc49bdf0a44ea7990a6da3409d9af448edfa1607d11dcfb1596b6", "signature": "8ff52818c332ca74e4ced8da1b292bc8d329d304bd5a5c46bfc7b22d7945ce93"}, {"version": "05ca4643052697bb429f5dfb4ee65608366729775b9e833f75e18dbc57815189", "signature": "7cbea6591b34503226fd3918170cd2b42bd19486eab54530f631ba9ef3ba8f17"}, {"version": "320f051c4765b494bad13a88c1c0fc1d8f6ecfd6a8964d79f5c20776a825254a", "signature": "43bc453fdb24554381e8f90250afcb35c8de5b6744e70f72019298afc98ea6d6"}, {"version": "f61b26be02a745a8e022d72da3315bfa18bbd5feb0be3ff3c8106416a23e4065", "signature": "a2b66a1be8aad3f257c48127ae57f68e29c7530b1d3a4bd563f0599e54e618b9"}, {"version": "ce01faa38e9c7f17f2e641d94827483fa33d9cc1132a9a51984ff446ff32cabc", "signature": "52fa017b5a088b5a3527098584bbdb91de6281dd8dd24d1b7d50364129ae5e3f"}, {"version": "fd403d6fc50e7d9257ac5ca5ec3d8fc233a6e720b6713f8439a5032fbe191851", "signature": "2913c7d3da01226a1e62306b316a12f752fa4ce2759a36ab6b2315b3678e5a63"}, {"version": "94414d1eb847dc6397988407aaa63885b509559ef624043e47309691e35caf71", "impliedFormat": 1}, {"version": "8eae958ea5677fc50a7fa741d8ea9ac27233629293312206a0abe61c1cfb1e94", "impliedFormat": 1}, {"version": "7c73dcdc2c2436263d14344c835f61367b2c144bce7bb1dc4e9d75bdf618e3c4", "impliedFormat": 1}, {"version": "ba0ee52d485318920880608139d59c7d00ee818c2b02a334f686d33e363f00c9", "impliedFormat": 1}, {"version": "2abd37ce7fd81b9e45a97538597ed2bff9737dd4d95738ae752a6772cdc08967", "impliedFormat": 1}, {"version": "7f40ffd177492b8a881bf1500355334279926317eab1cbcfd3bd0e24f7ec9106", "impliedFormat": 1}, {"version": "05861cd8cbbec4d01bae5f00fc1bdc7c23e2c6a3a7fc2dbd4f65fe54aa1ec715", "impliedFormat": 1}, {"version": "6cd9fa47196c9f0e5cf75cf45a6a9a4e978e2bd03b96dcfde295c37cb1cf92f6", "impliedFormat": 1}, {"version": "de2b2db59ca8501072922f7fa2dd6a1674fc6f4226e5556bc0a78d7db00efab1", "impliedFormat": 1}, {"version": "f3367ef4fa757dcb6fe4dbccb8dffda2ecfeeca7d369673ddb46f4921580e8a6", "impliedFormat": 1}, {"version": "6f8bb4bd3e71166cc6a92a1388abaa9adb27a333ffc62f068adb468133e9ec4c", "impliedFormat": 1}, {"version": "dfcba9146c76e58de915be51505e9b25ff600e15b47f755deda09be9406d7d96", "signature": "4b064aaaa64ed969faf1790e1676cb9a5703d3e876e4b38f1c71c1b9ce288067"}, {"version": "fbf45743f4680b45dd00788a74c6f364de6410c3314d9e209690f17a96599b27", "signature": "644c10d36dc1a702f346c0307f2adba1592f10cdcb2ec14b77cadbd3942c8737"}, {"version": "db292c9a0a89685b4dc3cf8e6f63d2f2811eac5262360be65c43ba26f83cae16", "signature": "e5c1ae4bd22fe2f2e8e5339ec7bde4aaf92c1496ab9966f6589cdd98e21e26d1"}, {"version": "a8095d56a2bc5fe61b7b12d37472a20262f989337f2e851f50aafaaeecbac632", "signature": "1b3b68e4d1c9490766c00d25c337a0a7b7cd7829e73cc004ba163eb99f9d8168"}, {"version": "e2ab8941fbb94e2eb3e02b1c45024fed0f7339d376f80e6345949f85ad90c275", "signature": "dfc91318e127f0136ce9ec908d7c9da0af40be86c5e54cc4e1477b9b65561f72"}, {"version": "d0668685127c21784abff557d30144a3bd9b031202cf5d9bed1a1a9fa6a8150a", "signature": "242ecea5496246906286685af3dabf34d0ecec50af606cd3f188d0aa277d4b14"}, {"version": "2d526d2a2d6dfd1980fb6eaf7aa668c24d339e27b41cac2a72efc75abe301b35", "impliedFormat": 1}, {"version": "d148975189723e3957ff6cd71225483248a28331715255ed8c7581928f843edb", "signature": "d5f9fb7a707aa6791dc08e3d1df82c9044c6159f99e249577dbfe8a6b3f9607c"}, {"version": "efa68833e1f440b6211bc05b4dd7cf0ec39d5e6cd910c124dcc14afa402dd891", "signature": "a8716ac52c60fde45924f671169f8b777fece61c116b77b2e07292dcc538c95d"}, {"version": "d7333f3106be173d61a5bac9cfcdcfeb25b536beb2ce228d9e6366e15512c4c2", "signature": "dde714f4395ce7f0291ccb356493f83163af5911bc52d2546733cd77c5619bb8"}, {"version": "8c1aa351c17ce25aabc6e065aa679c6f5a770a73338131d51a4345a862d9a7ac", "signature": "9f59b660ce7cf33662c561963eb6a16565d00ac80b07eb371a39d0af849df289"}, {"version": "65ac4e77140d9d97b1cafe5051149c8aa2e17bda8212c0b6f37ef868dfd836e4", "signature": "86fbd2457af760ff7243e6b608335bba7a71b28944ea83815aaecf3a1ab82038"}, {"version": "bb548ad9e924f5f94087073ce83aca292a79fd4d6a94f4b46d9478ae8a197be9", "signature": "8e59d82f2b4fe60fffc894117ac3916905476734539b4b8b80688a84ad96343f"}, {"version": "b09a30d25b58b70d7b0c42caba8b076807ea55f2456040300593015bc424677b", "signature": "87c18c88b485541e41f03db746ae61f29645a93803cab8e3ff14c4a3cbe50f32"}, {"version": "ab38ae947eb3222fc6bb469cbbcc7f1a7bc493c048fd300de1081713d22a8e56", "signature": "17af490c595d4a984e5c2e1afaf410d5e6fbcb69207ebc1cdf2449abf652b161"}, {"version": "5c49098642deda2abd875bf7051585d65e3327517731235b11e2324a9c05b3aa", "signature": "bb50ee31eecd316cdb3ac981ef71637633d43b52cc9a1ebda812c0b5c6d0fec0"}, {"version": "7012c609f2e21c62b1277fe0085b746216fddbf5d6f2f62cdf92123620a1768e", "signature": "593b5b78df12f553128e272560337d0f50f21d9a303a89e99f0a97e6fbf1a08d"}, {"version": "af246ff989931b27770350a68501ff9e4c62ed36fa33814969453fea1ee9dc19", "signature": "ca9039a8e680a2a66fb8dd52b9b4d5c4e356358095a6ef5b0e4d4de620db5e5c"}, {"version": "3ced0da1682673a487e54be999d7dd2e6a5ba266d3cbb6c66a8c14f568f4eace", "signature": "0fd45cbac57a6ff00d9d11f811334886caa7279b7a3c4ebda131606cb29e9d14"}, {"version": "13d80be7392621390fec957dbeca8b07888c3036e301135320234ea4948dadd7", "signature": "824fa384dcd1e6b09dbae9868d98626ca557273971da006e14a1ba2205a480f3"}, {"version": "83adac44fce9e6c4ef1d4149e1b6799b72cbe0b74a8afbd9145bb332bc5ecad0", "signature": "769705ad6f65f817465a88fd4fd49e906632a6ae12b5160867dc9f4f00df1113"}, {"version": "a1a6c7ab3e0056be8e15f7c93f6e94c1dc81469b764062604ce0bb087024fff0", "signature": "c560828b051557e67edb8e6b70dcd9218a9cfd44ca2698c91d623d2e9604f7ac"}, {"version": "1ead5d9b7addb8b4d28529b78770c33bf9ae219dace6bd3df0bd088d4d576a3d", "signature": "6f4e18e5bcc04b29f831892fd9fe80b692d52bf33959026a272c61a78fc24665"}, {"version": "177f96b32fe030530e6706e6a8f833dd9561e8f47673c9d2d57ad4e93f7991ae", "signature": "a95004f240fe0ff570e12f7de0db1e98f0274e1f01ceeab7ab3419f12f073955"}, {"version": "9a6131ab<PERSON><PERSON><PERSON><PERSON>18db064dd45fb71eb5d1fe4f0905bc81ad36bdf27d93f2", "signature": "16b70b574cb50620aecc5ecb1864b6c49e61f03fcac75a137e1395886ab478b6"}, {"version": "939b7e63e8c05d86ac7f91db82e6a1d106b5e881706e7928713712f37aad2dd7", "signature": "d7eebdc28c71c086ffba4029c887249e30936acac52c623ab90534e31cd2c90d"}, {"version": "839192bd2fde155d5adee8369f2ab8da82ab389513d506741c9f78fb5997d0f4", "signature": "6542815e9e5728cdf9747ee40305d64f51ee33dc3f45f35da631fe280d1b9554"}, {"version": "4fe383d640210aa326cbb6245a39795b6c4124270c122b66d9fc36c37d96c5b0", "signature": "f55437b91f22677962024d2ade4f2ab83b4f33010bd88e804266d9be818dba2f"}, {"version": "63f43507a8fdeaf75ec7031d892647f0ee555b00d3c87740a0f402e322be946f", "signature": "1049ddacaa916774f239b05221a803d0241e143fae388b2a7a6ea3557757ab0d"}, {"version": "e6bb232f4779dace29a8718b75f656eeb20dc7c0426bd84192f2e1dc6c2386a2", "signature": "226001e8c28d77e89536e80bf3dd949376af927efdc06612ddb3b7980f055ac5"}, {"version": "9cff09610daef5c731eb9cf32411bb1b17a3118573b652e00a109ff724801adb", "signature": "4b80ab02faf35e968104154bdb4c384d47bcfbcab4a4871949b911bf931b76ec"}, {"version": "c7492b006b94670b77f2480c601df31346f6da26b8e9f45c1acd3474e0fc7fc7", "signature": "40ee3fcbe2cf10a8878f8ca2884b35980114878f5723789bb71ec3b8f0c5a4a5"}, {"version": "16a4f5c580166dd73398174290538a758bbff02840cefd86a0b222ef0cff3240", "signature": "55679b2b80fb4dc766f08225db095449837cb278b5c42438a27d4b1c9702c3d2"}, {"version": "ebcefe8be6c586c58efbf776794f0ab698a8ca5de770122cf3fc1d1a9a7d6511", "signature": "141ae42fae7d3f046aa267fbf0f32c6db199189b038119c45b22ae3f5c27c268"}, {"version": "ed75c14c489bc2d740725e917c7031163b885e661885b0e4d085312e57763459", "signature": "aaf26baa67c1afceffacf6766657cbd4293f1b5580bfb5a82ae8d0e99970e943"}, {"version": "11e280d053d2d7076225edab802200807d0f5f1e4620e2d740f7153528eae825", "signature": "d0d69ce157385a11f0c1411822157b8a38134fa370a07da58d27cef5cfdf7335"}, {"version": "6dfc9ca2b276abb38e284ddd8db5d0ea637ede36f94d8938c47960ceb79af0d8", "signature": "5f113eb60eafe0d58bf1e4cb856c725aefbaa026f5b8e132a20bfff6df72554f"}, {"version": "9636d687f2c95072c0bd26f9f249180cd46632414d6ec67eb07bf051b3470873", "signature": "5bdf914c382a53b150605968abaec644b057bd393c5ccb32edf69033004ecb60"}, {"version": "dbec637c45f4c7393b0aa020bc94cfe307ff082ab42c6ea32468eeebd4cdb65a", "signature": "0e0d1c20a491b16404e2b96b3fc573622cfe7a2a4045147ba7bacf0db2e06997"}, {"version": "a0ae11ed630a92315394b9bb3b91bf76f5b8ea9f94dbb6d8caf97f642f66c9e3", "signature": "eba2892a76427b47235c52c50d7c6623a84ea3b0e8fd43fb9da413886652ec7e"}, {"version": "8e116fd8e1c0e99150d8d7fb36a02652e3a1f8670deda3a13026ba33625ece1d", "signature": "ce1f776f13a06a843d9f792d4c795d1850d07f9b327b4de5c0ff75964ae3c540"}, {"version": "43afeb63686cc1a3dc9100356eecd8567c4a4b486b68cc9dd4555c94c5941301", "signature": "965670529ebf05ba24a4cc5f00f44f96a48a7fd3c6b9ade1d29df18c007085db"}, {"version": "f2dd33155d77e46fe9a6083181c8be8a5ccade2d60a8c987e34d91d910edd4e3", "signature": "4cb3fb5b6601b4cf832a2f844436b0b45f592889df082917567a468a88724fc8"}, {"version": "a24d91b0e60507f43701715353a505c2db90a8b9e1d4e28143bcb0a3c3d4d0b3", "signature": "403978050a7c3897de76431fbd099725b4a0ff205c2e178189e578ad5e835802"}, {"version": "42e298f614e61b1b6a029c664ffa23d1db003c474ac674751c6b406235fcbd61", "signature": "86512e0e4f1e73b5a6d1cb6831679b1169718646596d4d9e10c85fcf5de46f71"}, {"version": "3f1f79af422884005e8b07774d5778f6491100648844f98614706cefe4ec2323", "signature": "566ea721c808b03b14712ddb2ecb3bf384b5b82b6034870ba3af53b35443b495"}, {"version": "dedc00113b991c95706f4a65e8cd78e2cb853fbe245600b643ac98830d0137bd", "signature": "7bb272ace26d3234e3e2d392e5cfdaf6a32576de48fd92211706deedee10eaf3"}, {"version": "203f65933066de16a1ad1f724ecb174bab749914e9a783a35c4ba2a810cc5ee8", "signature": "efb955caa7727a67351bc6b40c94165f21429c489a3c2e2c4ed5ccbf02f573f0"}, {"version": "52680ad172a7df05ccc909a484db1b22b23f2563344f4cf245196d25271d84b5", "signature": "4d72add7f47f07edefb27ee9e77e8c4b7284537bca053e71fb99dca7ea0b2b20"}, {"version": "a7f27fc15a100e9809c40fbc3f331f1c5b01c99c977cb250bfb867ee3037fc5d", "signature": "fbaea9b1718ff35e6a722ffd92aeb745f75393b73ca148660bb29c2d97a89e4a"}, {"version": "c8784fc08bbb40ff96a8518617b4024b3248f7bce5f3410cd1e0c35e47981158", "signature": "bd5db336214abbce34dffc13d21985563e5a5687ba5a5f5d9dba40a06e6282ab"}, {"version": "c27673a969b8db47c26a05ad57da5ea7496fb894715fd899f234621d0ee4476c", "signature": "1039c3b890cba937cbeef94ecb0bd5603239ea20ae986a7e54c3bca13cfd71f3"}, {"version": "6aa7fcaa1a4725335610a9ce38a18c01cf3cbd7e53deaf1f87419e7a3da31b96", "signature": "a3584228ba83bd72807bc6a8aab8199b67425dd460a5771ca8fb6c97493f7fb6"}, {"version": "c6bfaf771c037f550f9c766272977c744d611dd19eff9f7c90bd21a084f8c155", "signature": "d5b1681c3cd9715d2f4bc60bbfd7724c3211d24cf5ee42aa83137ab9494f5dfb"}, {"version": "a55caf6461b44431b469c6f5f875b4897883c48b499c57e80f1009604ec12cd8", "signature": "25aafcc883195a33859faa96007c06b9ef9f3225a4c9ddbcabf12ab77a860097"}, {"version": "53cf5d57b26c938cb27d8fdaffb5d6005f7e828a091d9f1b7a8ad43459fffb49", "signature": "ea73b199eeea6cea49afc160161d567915c55e7c6afe04683bacad6fb8067b2b"}, {"version": "03ad5aab3b3092b0d6f94f8c1f1ef4d12e7c2354047e77ca8a9e908f6b5a2db8", "signature": "d4c6a653fba99d3ea02764318edaf19774b558acf0494834130fd18b92ec0ee8"}, {"version": "e5b3fffd5e6dc112c381b5ad6c78b6592cee63fcbc36733f6e8195179bdfc1ae", "signature": "1e11e60df5bea9d6f4487251e2779252906d4568d04c967b1e405765a7885069"}, {"version": "47df4949e5f4aff5786518bb90c032ff14798169b2db8624a986f3b1747c9c03", "signature": "aa6f82378e7d6f780509dd05361cc92ca692e5fa2018be07557421d9f91e95a1"}, {"version": "86c9ac3b20f0da8cf760ebfb0e6f2bff74f4426a893a65b8eca1cf9365345ff3", "signature": "f98a1948ae44affeee36940d9d00045327321215649307653315ef4ee6746710"}, {"version": "0bc3b780d78a638bb195ae3c7cf3b11f8f32f0f2ae62a15d722e28bca66a70fc", "signature": "e182ab7c845cedd3f5007378cc743d7402d233a4831218ed6a8e4fa05529aaf1"}, {"version": "47a9a2d9b3e39e61530324e9397596814b5ab0e9d33efdf4a233dac1e3ccd4cd", "signature": "6d240cabc99333fb608e47997dcebe75c2d5a1c130833d0a02ed0cbcf0fbc745"}, {"version": "12b0ff05629ae54320b27387da2dca80d7637d44327b3bd7d9aacd052ac05d96", "signature": "8b9c358404ad9e57e9215bbcd8b66af06d51d797926e9a4937e80946a103d446"}, {"version": "8a08d7d7974517d1d7edc8bb7e10ba9aabc1c16ff5f09eae2f46ec410c17722d", "signature": "6f0dba00312ceb0c44a1fa56324cb52ef0d2430db75d209e04b31dd53510ed7f"}, {"version": "2f4ffa06ebd3aac32e2e20f1bdc8a16fe908ba614eb3f7cec38b50f89f1629a1", "signature": "f4c381d49956ed36c552cd593519e250f872c3238c2ae9bb10ca1618d15a63c9"}, {"version": "ae77c8d1d6db8bb8ea7cc15313b29534fa33a94d8d771098fa9a3f2c9d43c6ae", "signature": "577d9c1c6ef7eb99d308e53068b4549356d67370c09a36b31bf26dd8dfb68fe5"}, {"version": "3728d4c04d0621545e7913942bc774ded0d167cb13c76151f8d53359145f49c4", "signature": "39af6173248b4d83e6527240e8b6656aa4a1100c9b25cfe9a2e9fa64775ec836"}, {"version": "02af9ae567364f8be24758ad29081c67bcae271aea8a6fb6b34fcaa693639136", "signature": "41705730444db5e4f3bd59d544796f20f88474d35500c229495c0ae73eb37caf"}, {"version": "2f6f5f7744648dd62cb01e468e2c37e6cf917f5fdc3be574b8df27d82206dfcc", "signature": "6f07b963b0d7403a6c51338552e718961bc83b5dc060cb79483c7c9bd90a7d1a"}, {"version": "8675ecfac87075f4e479ab71654da84ae71c9ea53f61580cbdf98b8ed4816103", "signature": "747ee62bc019552458742d182fcf1cb25500125e876a2cd1158bcd8231415a25"}, {"version": "bff9ae8cfd3e17ecdb033bf9ca7906ae71679f82c3fe950c557e2f1218b0bd67", "signature": "24876dbb56ff3f0c6a01e42d6a3496dbc518b6e525c5bfcd11ca751c52d2ab51"}, {"version": "321825a0208d677294095cc3f9b737d166cb2d94897ae1bcdcf52b0b1c8b4273", "signature": "4cd22b4830d397aee0c7d6a8221ec18f399bdd76b178a09c353ec33e1112182b"}, {"version": "064e6909a130436bd5cd277f3db57e9d92ebf73ccfaad1f299df6272ff444dca", "signature": "e39d189b34bd020b97d99f628e29c50eeeec2bffe8168b015ceaf56d9ec9351b"}, {"version": "8a9a09badfa1467689cb184eba80a0d8ef0c2405e65d3d67deec592982237d4e", "signature": "2c6bc1a29ff16832f470cf7ee4bb82a97bd6e5c3d93d52787aac9d3b8d3088ac"}, {"version": "7f02fe7fbef4b19da095854c6e5a2c8899b453738510063110f1a67cd97a8145", "signature": "9f4a62a5a9e4906bfb2f5f7762629399283ed2f36bb10aaa20398edbb2605716"}, {"version": "4ecd52e435ee7c6e1b51adbf3c6b77cb70ecbcdb05d51a40ae4af7252ac05ef0", "signature": "2a9e2bfda3babe2a4591c9d8c6a92ad6da4c4b7643a710bad8f22bf026322bc1"}, {"version": "031c6833ed599afc0bd732ee27d63343f368da61215b38203ac9d75157bf6e15", "signature": "72166356120b8c2ecdbb34ffabe76a659331a100cd25c03abc92ebe6ae1c5500"}, {"version": "86042edc8a5d015ca5a7271f7eb27d271c34240317a27407fb948349d8e91f27", "signature": "fae914da99d8e43ebf303e77a157fed9c99a06b03cde5fd61f714f5e0221a2b8"}, {"version": "85468ba1274091c968d7dcb6d4726bf1a619eb008c90fc0710885399f55656bb", "signature": "8ae9e39bdfff25556c8137514206e82a1cc4cda1247dbeeb29a67400f74277d8"}, {"version": "7a587dba906ecb1651d40cc9537fe9e90e380ffd1fd37720edb61799765f95e8", "signature": "03a48b5aa40955a9ba391c41af27bc86fd1fc55900073c9b32fd4cce399b6724"}, {"version": "369168bd18e5868be9b34ed57b89c8a49b4203046b2a8c1f0002153295bd29fa", "signature": "29b81089cf94728a13b291a55f3bb0e9e5fb12f064c83a622dc523d710077707"}, {"version": "531648a7412bbcb438976351e78adabffd77193e8ec528aa463427d9e8a4dbb1", "signature": "d519d473351f5750dd4ef490bd1e5bb37f188f53e6c0ecfb2a86dadbc95a28dd"}, {"version": "5cdc5c9de849094ed32d4e1ade0e39d2ab4f3cef0533b0e55eb1ee73c51fe99c", "signature": "dc95126c3039b3d5a87e644c26ed32e887464ef61b6bd6895deaebb84eeb1d0d"}, {"version": "98b0f30512eb6fdccfe637dc2866c2156fd1222f4e898b1c8953087f91273c5e", "signature": "691fb27d77ad2bc4b9063af8f6f220ed955f45c46398e57f58dc26b2a1283bf5"}, {"version": "01fe8a3a7e264533d9ed0220d4e264669a680afc0fffb5d99db7b32b0ab52185", "signature": "b67833977b5638109fb81e959abee3b855dded56ded7935f05a66b65defa77b1"}, {"version": "c9035395dd45e1235b2272928a189e2f4971b6c1b6e372b6ac41ad3f39917e81", "signature": "ca05dbef65ad6bf32f92b27c046df9c7843c1d73ffe09c44d45f55fa3cb1b474"}, {"version": "af7ecf3fa9feaf8a69b3db2bc0153b06ee3b7e82d394786377f738a523abb44f", "signature": "d275ce5674c1ead9d0179d0e7e92dccdea8ca1a2b96810100225dbf67fdaec0d"}, {"version": "c8c20fc0611e49eae7c2539f845f720f24fd67b12d64ea315f8a759675812937", "signature": "09937190ea744b18735cfaeba05632111850233b87834ddaeca9631ef72bee84"}, {"version": "456b786509085df222f56a5746754a1b65e4e11fe4bedba62a07540d4f7fdd9f", "signature": "1046413cad442c1704672503874067c2a34b4b152e6b6874d2774cf29cc55d07"}, {"version": "d76789a0f4c8fefc83da1bd0295c476e798041a94961ceff6232a382f1df9359", "signature": "a65dee4d1a530442231f420f6876111b82a636233a434e5e9ce6528416166ef3"}, {"version": "7f9de5d48c296a118f1c7e4a3f1ac7dda8ed162383c5aa8dc38a611ee016d766", "signature": "f99ce921ae5aa449055fda05e1550918d3fb93aa9b147be03cc5d6ad1e194eea"}, {"version": "51055e0b10b87445f858b84e436bd30dcc17db0ff06812e08b9be774ef295f0b", "signature": "6d48e4168441d59008406da3eead8c8898e564351e189f2c5a5a071e3e062bc4"}, {"version": "c904702ae295bb78d63f6c7829105343f5e1a91ef5270b87fe14ab90251b517a", "signature": "5cdee12c672b994bad0bb9294e717c850593e409673964a4e169455d4b02795a"}, {"version": "03db9038fae2001d680121292171dd3a0a5d64a7ae60852ee548c10b1726207c", "signature": "02f224b44640eefa0e8e45dc4f794a1211778500acde6c4585bdb44e35b0b7c4"}, {"version": "c9b771ad0f770bd600d3f53b5f3de45c32ae18cdc26cea0385fe84845680b1b3", "signature": "0f334a7a31794c932358109bb9cf3f8488f2d5750964d85a4e6c066673c8961b"}, {"version": "bec3dbdf0f25b6acf35562a9088ecb13deeb98dd0cd976de0ec801f4d8869ee8", "signature": "3a73a702226cfd064ad06a6cc9c8e936ae7d4d6fa19a8d4b406f35bd699211eb"}, {"version": "3258a47e0a2ee3b4a61420020d0959ded7cfa44251fbba5a34e68c91dfce6514", "signature": "38bb3badcdc2658c8d9e720d58669d06241038d7f5a66e5568fc182f6e9dbae9"}, {"version": "1e7de2898673498ae1cd673e19cb93d3365a337753a8edbe0bdd903e10014ead", "signature": "5ddd91273ac65da9b0a9e263830b8957212572bb9748dd6fece3bcb6d276bc04"}, {"version": "98707f8a5f3d671576a12443a758854e765693468b8805fed1479c3241fdbaeb", "signature": "598ce399ed7f6c46916863dbd9e0fe3bab58306b028527a32a2d0aca0a35d251"}, {"version": "356b9becf4929baaa247137cfdd79cee6615e6fb0661bdc9a6fb604028f47a04", "signature": "f4196d6d001eaccd37cc1752f7d207883c1a898534f04ebaba29c0d0b4ee2d13"}, {"version": "114b5950f62ea1ab53f2033bd740b10355e0e2b3eb2fb635c82f847e9720064c", "signature": "ffd0dbf0ca198626bc14fe3b5140a013ad5347ea6830a6cc26eb96d9c1a2f600"}, {"version": "06d811d5487f75128d9bf65eac3d843be15f91c0f7f654574e100c67fadbf88a", "signature": "3fa0e8b7946d1fc4fc4278916d706549eb9b4a557aa267da8adc7a4235e5c293"}, {"version": "3b26690041e20d39a50245126fa915cd3d30f0e8362350810677b746de1717b5", "signature": "b069a0b3acd2ef1b7b970b79963a521036a465eed57b95e73d91e112aea7ae88"}, {"version": "ea9f597021cda1f8692ac3e1c19db37da391f5a81f669d0bceb2bcb70e68a0d5", "signature": "31990022974bd9846c37697bacc0580a0c11451403b5cea21e30206ce71a3dec"}, {"version": "80af740e8e93c88a8b90dca3e371f5fd57480e2dbfb9287bad6017486bedc2f4", "signature": "10a332c9ed97b58175715bbe9c3cb3a2c690fe3af115ba95241006fd7ef6d882"}, {"version": "0c489cf66684f53ba9f1ae2c4ab599decbbc1d50ddc9ff4982534f2380cb761d", "impliedFormat": 1}, {"version": "d3200399d7374d318efdb7ed7fda3134f4098d61fb962320a73cd8b6454b5ec6", "impliedFormat": 1}, {"version": "cde6cd20d9a690db945a9cbedc5076a9ebd7d8537e1c21e787e912d7d5596a73", "impliedFormat": 1}, {"version": "ae5aa6f86565025db2e1a111112f846b11abe33278b65aaf9e83951882f5b617", "impliedFormat": 1}, {"version": "ec11e9775a2498f9a8948738297f45c84edc539756850ca3ed04d933b0549519", "impliedFormat": 1}, {"version": "e197e87479bb73822ab710f3a8c33a8132a2b556f8a1fe9a53749d7d381fe774", "impliedFormat": 1}, {"version": "1181fbba8f8ce525462e90940e58b57acf5653895a5c9130356af668898aa505", "impliedFormat": 1}, {"version": "52c48ced7516d60b2e6bd16b701ac27121df29f355da4376a2eb6ae5caefc6bb", "impliedFormat": 1}, {"version": "a8bb8fee56d4154b390d953051cdd2ba8c27f2c1a7527e1130070a513e8ddea3", "impliedFormat": 1}, {"version": "d5b7055045850165e93dc4ceaff0c65c7dbcea33f2fb3651405ed1c45305cbee", "impliedFormat": 1}, {"version": "ae5c464f10078095728dc882352181004feef7501b63dcce0c90c25674921937", "impliedFormat": 1}, {"version": "b073c2ff592c6b01972626fc5b7ccb8bd10a533315a3f370b20c14d8477f6b7a", "impliedFormat": 1}, {"version": "7e3161198d2b94729ba2f0cc95a80e1df6366b47a62c0d38075777b7183c6302", "impliedFormat": 1}, {"version": "33088ce0f4330e6d9ed720f36b190238feaaba4418d7db006ad6bbd2d48169d7", "impliedFormat": 1}, {"version": "db23b081bc517d635e408c7aba4558dbd6cb3402fafd63222b0183c64abbf098", "impliedFormat": 1}, {"version": "4f9e82c7c080d7bb01a45d43e93638e6bde70c06888e2e9eb1c770c4a51bf815", "impliedFormat": 1}, {"version": "635b3a98da228f79f1db99654d5c9002b01a95ad1df69b71420a99593e1abb7c", "impliedFormat": 1}, {"version": "db23b081bc517d635e408c7aba4558dbd6cb3402fafd63222b0183c64abbf098", "impliedFormat": 1}, {"version": "744028c1d6996be85210e6e61430d77c0742d4c150ab12f2612e7b55ba99c92b", "signature": "f5f4f65dbbbf8cc03a81cbae8e709b0e32a4515ec25ca8328f3e81620980c194"}, {"version": "3481f4a78c110f1696395dafbd852ebb7476313e42702ebfdb0fa0590249290d", "signature": "8c431608f19d79b0f94ece0db1d488c8a452848cc36635b59f8be37cdb93a38a"}, {"version": "2a48b874830bcb4ead2c8e22f24bac0300a4391799eb43dbf94e81f2c66eadb4", "signature": "687113fb71e188a35c1e6df9245223506862ad02d6b42113d1ba3f5e92cdf527"}, {"version": "e9bec12f554152ba0dd9e3f647256a374e3f6d991c67d422cf8a97a97fe7c2bd", "signature": "60e540c0b30c5a56b8610e26e3a6c46526c4026fcd67e579f76c79435c6dc82d"}, {"version": "0be2fdaf63ed187ce0205b3c028b9623a4161d2d20316c1110c2a5361e5c1ba9", "signature": "b3cef00d8c89f21ac30b26af316acb6ecc6a6bcdd4b974ea3664c869b7a37eb5"}, {"version": "06bfe93fa2d7ead4b83d08ba6134088f04df39d492fd91c396d74208d436ab04", "signature": "1fbd640a05537c2d37f89a9451096b83b28a6ba35f8b2f27758c426786b7604f"}, {"version": "402d8a681e53a6835815cca7efd41c304501c5a645fafaee4502e5a39ef74675", "signature": "e8010b2d747603392eeb066640300fc7ba069aad817a56bbd7459b7d3c51e9dd"}, {"version": "18383ab20baa394eab42fffae0b056a01e9fc7162add93ce88c723e9e39408e6", "signature": "b068b00848b913d5a69f646a4112079e325cb2580f08276fbb5c1b331738bb0a"}, {"version": "910f1a7ea530c5a7ca54aed7070d1f3cbe040a9f9756fa1903c45e554a90b9e7", "signature": "2397b141b80357b685197278032ed5ff841f850d4c2e5d18e2458c9a3adfd325"}, {"version": "29d166ea6107cde5cd4cb4bec59b787bf1ac3b794658a822665bdc8f51f234ef", "signature": "5572d44cf5f4d395c918c238cf629ecec9679799774d1f319b51878dfbcc01eb"}, {"version": "e273551c36b2ac596e9d2f86fee84ed49709f870035040a106827c609dd86cda", "signature": "a80d43174a22c660d82e904b352ce682ec55b013f714ad5c2328af1af23c9923"}, {"version": "37b6bf58c0c9b05d24e5f875056e5756fa373892257956f704311573ba17e3b0", "signature": "1d799a8d52dcc60c2d5c65cd2d27d54192e755e0efd876f77d77375669b3efb5"}, {"version": "46edab29c75c1470cf087e2d8b5a81c0805f57021936dc833211144bd4f2066e", "signature": "e2c79c32f4c6a342906da9ada92759319c68cddf4ad848ffccb79740575fe79c"}, {"version": "a11da2a6373973c93093e8124cb85536210bc13e2cdec5b8f3c8d54f97de6fad", "signature": "aaa399d5e954df3cb57c46f8550047a04cacb4998b7331ea73c8ac041e68b77c"}, {"version": "762154c29bce87dbbdb98c4086616a4a1c59b762f9933f8baa00150d3844fdc2", "signature": "639eba2861389210a9817391cfa9918746f730d3b6bd39038f8945f45e5ee409"}, {"version": "833d8c214bc7cc1095d23e7867bd87bc5ab620ac7449774b8c232930063fcbd8", "signature": "9609581625a7bb84617348fc8272b20da45ef8d976d61f36a404ddaeda411474"}, {"version": "cfdac251677e38fd1dea09d850ebadc08f1341829fbc0a3166814b878c438b6e", "signature": "87b04c4afe6e36b4930a486c935d9e6a7f5dd5ef09554683da93b53110c563fe"}, {"version": "54679257d0d880c9bfdfc1481964a1096dbaf1cc3f9b1452c6f07bf14217cbde", "signature": "31bb9e72325fa10edba8e20e1afc6b9b50ef2714576d9056c5b37058395d6fc0"}, "099f4016b0a88f8f3a66dd77f0333c68820f1c368d09a472ead736f4e4759a55", {"version": "6c31a37253c464915afe1ace2e3016b740875d4f7774742789e3aeee2c4691aa", "signature": "dc5e937ec10c7eba5a8cfac5d4a29d254d530bec09b099a054258ed3ed244795"}, {"version": "111e5adca5e4612cc316be390028f394d662a31f7fd12694c23c9b148d6e49bf", "signature": "e26616e2a418ad12b298b229f5070bf84d562a432dc928a90999865ba5b90d4b"}, {"version": "9cd5ec3f657d2818886137ab05658445644d5cb33016ac61b15b334c595b38e6", "signature": "dc371e521b6f6b2eaa263bb579d5dd8225f31b1ebf7b0ba4c53f18cd18e5793a"}, {"version": "6c312b57e503ff5334471eaa28188073f591c42130556dc3ebde42fde19d0fa3", "signature": "8370831497217b75dc4b65ab016eecafcd0019a14cb741c595e31972d91dc73e"}, {"version": "802f1a64a37250733ff9ee9e1c3893196d06d1a4af048efa6f783ae43a285fd2", "signature": "054c467b1984805b7fe65aaa5e7a92b67ba297212d87e0fb2d30a068fec62b71"}, {"version": "d9a0434ad2b0a1400fc3d3d21cedc607125deb53676c5d07d5d391ffacb8c8f2", "signature": "aae88b20b8bdcb45670f336ed7ef1f9eb5a3b44089ffc9fe0e03a501d42fa48c"}, {"version": "b8b624dce114470ff0583982e38efd2369bcb0c01a0f7b0727d610d511cb7b08", "signature": "9a12a8e3d0c1dec23c11385e8b05546b413973c2c5103c8479f152abb3d9a0ec"}, {"version": "2531ed5d8d074eeef743da0a494933e7b3398b9e0154410c24352d2ee0a44cbc", "signature": "629263fd934e7924a795a4aa82e92516821cbde7bc69f59cdbff2ca5a30e7c4a"}, {"version": "dc8442398a24cf91f27653829f8db6b83d980c1c89ee047e36107e79ce8f63d2", "signature": "bf15e5857d7e999ca170c693048b51388d26a52a09d192d13766974c138b0ad1"}, {"version": "06ff1b70d193111e84a4cd2219b76974b54f77ea3c9d03d1e3772659626d8868", "signature": "844331eea199f6f97a84f58b070f627a5bbe99169ca35fa25eaf9cc3d1cb5319"}, "35ffce207f216bd533315bb81f229a00cb92ad26b46e9de912c2a024a8cdf79a", {"version": "602b37af5251a0e67338de3dfc71050015f1923fb7ab6e8fe4aadffb24c0fcbf", "signature": "df9993c2cfd8acf26c62e3bb830118a1f34cfea9b0933283f5981b1a06bf092f"}, {"version": "0860c5abe518bc999516eb50a8d2d8347a63aa918e35169b1ed65666425a85fd", "signature": "2ed31afb5930fcacfcae58083d9c46557aa62d57770540c7dd5e29034eedd39d"}, {"version": "dad0ad2df3c5b3cdb77a48877ed10e47ecdfcf5de4113d7d3d965662df0ccef0", "impliedFormat": 99}, {"version": "085cb50774e19afd33a0a4f2c8d94d50c1e11afe9b464bd1657d3bf1e877c682", "signature": "ba0ea24cc6764a83bf867bf3cac59169a1e9a3742927ca4fa0a1fd4b4fed4909"}, {"version": "56184686c05cf0758a4b4811ae2144a50206812011482b5e21edb733b9bcd3ff", "signature": "4c5db8991604d67d7b864901da44e5aba8d72e71cc7bfde3f72cecb13448f0ac"}, {"version": "45c25e7bd492cc09e6c3091db3115b477ca3a4d05a56c04b3d497efbf0ad14f2", "signature": "6c6d875c91b46800c000ff519d4f94d3d3591536f6f36ad8487e4e20efd79c76"}, {"version": "539700f46814ca61210e3e30d3372c22b3cd55d971cfb4c24d89614e7213ecf0", "signature": "f01a166f49dcb388d6cf73a758114a803a60d4e5a7fa6cab540802f4314457e3"}, {"version": "59888acf012e5b515906d68eb9cf25850a19dea4cdce041dd3fba6ebb06d733a", "signature": "026927c8e8e3850a4db1ab43b8fbc51a8eecd5fb294f0d11e928a9dd16f9946a"}, {"version": "746670a74344fbd07903828f9d9345a75b57b1d486cf2737463c31740e981064", "signature": "9fa413fc63293f7d1309dac8f8f943b9206a8ba8798c42921f32a0fe2b3ef5de"}, {"version": "84fa95fec11cccf32c2b638ab5f06299aacb0d9fefb2fe1be4d1abf75bd8ceeb", "signature": "dcc7ff9cafec3dc1ca59f0854a379613091eb7fd630c8a33698fee1f727cb853"}, {"version": "6e20e77b45f6848e73d1e573b281d4e129607d02bbc6cd0d1eb848497ca311a1", "signature": "5f94b7beb89fc7cc872192fe7126d92560308e5fa567270c8fc787e24231a811"}, {"version": "175cfff38884ad3642f52114c3052dbdd00ddab55bc3cdcae040e9eedbd217cd", "signature": "1b58df151662810b4a859743d3fc09529f41ac31996b77bf7d3be00252dff4f2"}, {"version": "fb3b8ebf97dd3b30e1997cb71a5043de923a13603afb76a45b2b21e7f9a4c4fa", "signature": "7f0a30d913e96d7d0888e81ff778aaecee52b3a03db8b6740ff8cf30a6716bba"}, {"version": "2b7bfb550a360da06c0649fe9daf03a8cf3de6091db33d1cd86f3d0371cf605a", "signature": "b0b55f1d96bc5dcfcb32fd1055444c1c7cc404586973d84386a280f0653a07f6"}, {"version": "50161016eac2fa1e88567d375f0c6b8368ab1a3166442192d127e1600257d064", "signature": "90eb7eeea98ac1f94a2f501daf06583923f2db2417916c3c5ed46d522c201bc9"}, {"version": "ee4798990de0a62119dd23dacebfbb03bae9c0017bc8033ae4665cde62aec527", "signature": "3ac0eab47a8de73fc33db580a3f6db2f54a5d28a164f3cc555ee59548f3bb53b"}, {"version": "4018472ef34557547083010c3c619523012ed7b7229f259613be921199b99767", "signature": "54091862b56b7f6eceff6b780ec07b76b6c3d48b56b13f2fc589e3d424e6879a"}, {"version": "02e41dbe252ffd4d3a6c62ff8d226fed7d0130fa34265e1eebdf789bf42b97c7", "signature": "1a902cbcd9f40bc9c5a3b407633ecc0816c08517c6931ea4716c85034f5d4c03"}, {"version": "be6ea53c8ff58f4260008812c6714f2e07c37fdedcbf9a9bc7df8f6aea8b8f46", "impliedFormat": 99}, {"version": "bfc2f76b0d1d0a43cb3c43e37d36ae9f4bea5b896bdb47b55dce461a9a445a7d", "signature": "4ca7b634f897ef27d0f240ba88f6c663e6a5ce87e20b50bf26b4a95d222c116e"}, {"version": "63ee46e1d653ef1e553f4fa1bb0d9ea82d4724fd8583df6a3b407b1d29ea44f6", "signature": "1ed9922ad9b7e4d750b127182148f129b902b0e802098b03cecbf498f07d836b"}, {"version": "2f65e2bef06b6e66651891f6cb2ab85509a0bd484f9d1e28a41f3cda7bedb828", "signature": "d78a47f946892f22b0a442ea4df380b4e5584e8b05f7e7a677d4c4ac0ffbab30"}, {"version": "dbf202b073831af6d8d9e70a4927cb8abb00f2e25bda6937bf6287232c4cc98b", "signature": "33ab246c6cdfffd781c64c3d27770cecc7dd43c362a0d4cff22870f9cb43c6c9"}, {"version": "93f41baf99eb6de3b2a868783b1391c4cbd5fbc1afec5a3f215f7d9eb49b5f91", "signature": "7b6315ea729912bdc345ea44a7af80141a131d9c702ba29e85fbb1c0aba43795"}, {"version": "ac8c9b526ea14015307094c8222a1613708f3e4b3edc0e24af8fbef4e05c7f75", "signature": "9a10947ed01adf13dde5f8f47a839ce19a580ac1343d1af50bf5861a41bd82e2"}, {"version": "5225cbed35aecb12a032fcc286d7f32a36228f4e4cca8bbc34bf1d73ef758d43", "signature": "67ce34800d11100f35f508b91b512529f4a342f6a9085edda423d8a50860f421"}, {"version": "bd8526ae4a57a5dfe6c3a77e5ff6f96dc58a53b6bc65b3f27c94743a082dd9ca", "signature": "7da8f741e8b664a4034e44b3cf7223e0920c9e1151e11684c66169dda4f7359e"}, {"version": "0dd57cf14c89e25b3720d4137ae6b4838b008618b006d722812dcc0e78bb3713", "signature": "7a2405a286f89cdf7ceb96cdf817b28bf1aec00f77db2f414ecdc0f0362acf33"}, {"version": "282a61c42a353ffefecb43f108eb06974ba79347950fa9a7388b8f9ebd45749f", "signature": "a4cab147fd29bc2ed06d7064eb440a75301023931dbccc5a228a97e2b66eaec4"}, {"version": "eb4f6125b09352633c5088e5fbc343d971825059fa5cb95a5b275d9e142848f8", "signature": "39100db8b557aeecb64b255218e599817410fa0f142380e03f696a80644919ab"}, {"version": "d1b9e434a325f84edd1d1c92b4c8204be6bcec387d92dcc9a0ee3c46f1e6bb79", "signature": "47df76b4864ba030bcd8fd7cbc42a6e00f8b2541e8d719faefaf11c53af2fcc4"}, {"version": "3a4d91783f397eef4386c1d7ea08c10453283db890b77e94d1037e3b46b529ee", "signature": "b2e347fb263087186c8cd9d6c7d5ee4ac00f722ca7e6c58a3474ed00e78dc13f"}, {"version": "7f20ea09b6da2130304758b84bd35f76fb4e89a0c6259a42557437eb88788c98", "signature": "ba84f8df8dea0b7796911db6b5122b6aa01106ed111f1043beb1f5d5651a6e1d"}, {"version": "1626689a0d98466e2144fc4f081b5d08491f0360d3a2fe1dc5d11f094e808f68", "signature": "89496640e063c548573d764f444b0f7edd0809a11478bccd59d2fad40775a24f"}, {"version": "c3c13c4a346686516463c052886c0d0586132245d4ae7a371d8838019836fd6d", "signature": "6931d4c61a41de033a8753e8efefb3509e58c0807c192549c237aae19f9db93b"}, {"version": "a58a9f183e5d0c3cffd6f2d773235e199eca6333de223844d28d4349d8dffa5c", "signature": "459860f95e1a2a18f79b2d8c5c68b8aa9c7154c411225693ce714124dcabf27b"}, {"version": "a2370fe3ff9a62fc473367e273da4f99079016955b273825a0f90cde69223753", "signature": "d165bf3655ce953e2909e4f6492fd75ce331dc2505ddc0dc4b28f83bee368f49"}, {"version": "b3bd42ada96085da958191d1d655dda8d4c663c1e1635171b1498176da98454c", "signature": "ea73b199eeea6cea49afc160161d567915c55e7c6afe04683bacad6fb8067b2b"}, {"version": "6e35bdee1e59b4bb986d5ffda6155a7eae47871f80920672d4647d1a5b817242", "signature": "f920ca903f35416a3c2c76ac9c2c337b2b1cb8a66f15b47135ee60e72b613564"}, {"version": "cc7836cfe41ec48b46474c6536a5960ceb1a58248b1fa60f0c2fb72c11f0d658", "signature": "b069644448d503760d057a2c4fdfb61f295f9fbfbd5c0575a60443129db5499a"}, {"version": "b5f11d920e1de47ed76c8714b636bb236032bfa485283839e1a2b738db150a62", "signature": "449e6d5626f8d70416741f3b2ab5b83403c1af5762002ddb763191726be5fba5"}, {"version": "ee98c475e41cafd6ce33ca2eaaa132694776360e5f81c5f986cbe45b5a026b26", "signature": "821495ec62d1e19a105a14de6666ba494ef6002c86dfa0b878678ffa139c19fb"}, {"version": "828bca08ebe36e669e6655a137119043c6aef8ef4ad2b4aad86b2ba3eb89d17f", "signature": "d899e9d932f56558f11969fbbb750234408b19f0ab93737db5b9b0a45094429a"}, {"version": "e3f1ba7f1bf234b4fd8f7818b0abbff585edbdadac689d50bb7c110424b1012f", "signature": "5b14d124355d98aa8ff886ab7008a05d18ba8cca479c3e117a8fa1e96bc70aad"}, {"version": "e5b0b48db871cf80c7b74b0bf55c766dd3483d0df01dc5ccabf570c113201ccb", "signature": "017683713589ac072f72794fdc86243876d175e616ef5f2d905fe67939dc7d7a"}, {"version": "05ce067edd2e5b15fe73eac3f71ecd5f45cf422886bb1545c46c988cd1ccfeca", "signature": "9463ebaf255434c3b7fe8e1300ea856d3b9f115750f4f1bc9f14b71967a70c78"}, {"version": "2f3145a4886a3c684f479d043d6b12eeeb7a15de85d7e99e7d93b18b4207de43", "signature": "6214a16be2298db8c277f15faa0556c5a8fc36dbf7457768264a3dbfec276ac6"}, {"version": "281f23d0e3b3f3cac0d54c07954a7cce982f2a2b95f895116ffc7f22514659be", "signature": "a39c87ad88eeacb60fae16859df6f2deb4de6bc0b4f6f128b13362ba02597617"}, {"version": "a4ef2e8267558cb85224f86a81e9b4658a080ce70061ad0e452ec4d6ffdd6b28", "signature": "9f5049a41cfc211135c8524862457dba7fefc8de86f9e5871de235bb2396c3e5"}, {"version": "ebdcf48b65b0635d83b3ef078d7a6402a61e1e8cf90c807f48faf928a546a187", "signature": "338f6efe81c2d2612627cfd616f14982ce564a64821356688155f81fa041237f"}, {"version": "6b5b6acd5a6d367b8db20a6751651fbca46753c6a5079e7723ad1d5dfb9b2200", "signature": "34898a5c52ca0fd02319335e7bfe074664c7e18e00e03fee7d64b12f4b702d6a"}, {"version": "f59c5515daa57db7ff66fe547f03ac6e847fab046a8f499e6e75b4c275880f4f", "signature": "ed5884cdddf2a29bcd2b72625a10e1511b92e734554a7ee44fd7170d2157129e"}, {"version": "0df11e4d2f63756cda52af487aaf19322ee7cd8e4298daad9ae07ab16797393e", "signature": "cd26c2c7789a27a20f69c512299802ecbcd0ecbfe837bf7e513c77ba6ca6daf1"}, {"version": "b64362d6d39f6d8f27c62a533a0c2e99f02da3b79ac45130ebec1097846a61d1", "signature": "58bba8e3d2d34c41054c1b0e2e6cb13ae4e23a3b98e5fdccc8feb76a6c467a2f"}, {"version": "6446e234ce1b94ff3497a5cba332cdc565e44cb52b3c02f68f9317178a096561", "signature": "898a668f823abcce84b4f49cafba5605e67be4a369eb4e55ae532dd94eba37c2"}, {"version": "1f41c857332e3c47701344a24c728b590373e07de9e1c8727658d80d5930ef16", "signature": "8f982cee9bdf28becace369f2c17c9ce5d01a471207bce4f0d3ab9a8c1151c02"}, {"version": "0f87af6eb2404f7cffab55ce670bba2d1d0decb570c4c8e33313934bb6b9b731", "signature": "f415244585e3f2c6d6f5707e15ffcf5741a9938a4f33f81c50aed0e75760a40e"}, {"version": "4ea0413f5d424d60b15f7ee13df809efc640980f69e71be52fa102d1e3a2f3b2", "signature": "b2e347fb263087186c8cd9d6c7d5ee4ac00f722ca7e6c58a3474ed00e78dc13f"}, {"version": "a2c65c4b02086567a03a183d799d1eef0ce6cf9399c04243027ad9f9abf6fe00", "signature": "bcd849f29d0123b9e9fc206a0dc32c4227df98cd122f24196d132bbd194318e3"}, {"version": "74f812187be6c0bee8fdcaa46068e719b4a8703c633587324f7936f6fbdfd12e", "signature": "e04fe7924d2ada37861a53d82dfc3efff1f256c37912d7010590b13b8014e93c"}, {"version": "b824bdc250816233ba28b3ae46c101736820f20f12fa3ddaaea1f5986625957b", "signature": "0b3d5fe48f5a10ec69e6d3acbc2ed26eee8b2ef3a1a779abd5fb81ec4f9f28ff"}, {"version": "f3f88ec204fadfec412f300621cef254f974f73739cf84ebffd77f3f8ebe5320", "signature": "d6b09ae15fbb8f81c31388b10e8126bb962da4997f6b1efe4627ef06a08a625c"}, {"version": "14f64a17786b7bca55917aba8301f21d648ae80ad92563220262cb1e323f8dd4", "signature": "9bfd86c3439a08cb3bcde83c0cbf40f4e3c7b4de26b51f5b4f4953f8c4a3c1d4"}, {"version": "07db0a3764c4f50594eb6d2d5d4785fbec1c744c7f7e504adf9c0d8f8659f731", "signature": "34898a5c52ca0fd02319335e7bfe074664c7e18e00e03fee7d64b12f4b702d6a"}, {"version": "04420d139610f3cf1bb0610b1594ffe7be51f3a862a31f84497375c9ee63b5ea", "signature": "be5570351c1fb9cd64a32c608160a3ff5a6e4e9bfa0352c43090db35fc6c7427"}, {"version": "ffa54e55d284df8153a8ebc97d3898ee19a48d41c18880442f526b2446b576ea", "signature": "722558176d81df6f01bb652451781cf1996379106415c9942efadf8681a573b7"}, {"version": "30d6afa47131a6b834d5764e192b6610b4592de1a2d37961387dc3a386a183d8", "signature": "8f658fcf4367c6bbb51d8574d75a6bad5f719237b7a63f62c66a072f5b4387a5"}, {"version": "6b616ddef4cd3582441d49e3aa812ee5702739b4705bb74e1f34f05ddb43f0ca", "signature": "e9b3e3ce2e5b567f03cfa686c5a3ca38786df58b2d9c567b918d6ad6c3478099"}, {"version": "9dae138b938f9013186207a98171ac38e373785c1bbc0e814351a2e9416ee1e2", "signature": "d5e4d68ab8182e306f6eca640daeda5dc304f53ac71f5d4addef4439e5900a0f"}, {"version": "52436333bec86b3c7f55e5373437d08e72a3bf0282d5fd3a5b43c0a3366b1687", "signature": "4a7aa684aba3e31308283dfec28af008863f8efb5001c72f888d5cc78005e8d5"}, {"version": "8bf9a3c520cd5b2128cb3f35762ca0ab65c17b836141d92093c0c252f68dd9e0", "signature": "64e8a9a408b26503c6b5ad9f55f14f9da197fbf0015ac2996dbad1fcaaeff5b1"}, {"version": "68e5680a75d230d4da02159e1303037f2598716fd58e3e4acb9186d06e61b509", "signature": "e406cc965540d3ad13f0605319087d4d67e1566df20127feb485ff4a5e777198"}, {"version": "ba651826bcb7f6f448d8aeb509d28e860ee35b36ec63672ccb8567a241d17c71", "signature": "7482b21d6fc9bc5807aa2ed4e7129a215109a0f6e8e67f6c07107aae4864d625"}, {"version": "9b2b60faa9abbeb509d7c153352471de174fc3ca28f9ed718a5ea17980b47956", "signature": "79f2a54cdec3451d89c39f56a9fac853409f6840f56632ccaff6fe7815389451"}, {"version": "f554c748a09d70a1cb49fa0d93d5ddd3e4572dbe13c0cde56a362872ed24df46", "signature": "d7d05ed6c79ed92df9f6816037f99f77de1ba7088bdbb634f34f56ab87d16a97"}, {"version": "cb3cb7a04c1a2fc5d288898bd323d4a2e867821e95067b414b0f354c174b0b0f", "signature": "c3d0d214a5edec6036a51ba25a69414e5e0f3cfbbfa6faab317b4e7b718c678f"}, {"version": "b1d70d1639ab072c627507efc35eaaf21b721a773dd9a0e140460f9aae42da95", "signature": "819da60dbbe9eaf1cb201e88af1ce0c0b09a65fe4d48ce09b9426863766b2105"}, {"version": "f07c73bb6da3bdc67325ba29984198db70301d9e38ff6ef1dcda8892453fe25f", "signature": "11fa45be015d0e8f7ee9b4ac82759840bcbd9d668c29ed716efc6ceb59d364fd"}, {"version": "bebca54805737b2b860b6b32da4800b99afce17ea5ccdd8e9c2e1bf2bb53635f", "signature": "2cd0aea7382ea32efd618da87831fffa2deedd4e8bf84a8daa131c1a9402d7a0"}, {"version": "3d95fb407ea49a31bad74f53dcce6dea4b8ce4492cb6d72519dbb744ddaed912", "signature": "968f0a432369174970065a05ca06b59079c79175cf7ca32ce2136aa91f903553"}, {"version": "7fc39f99bbe50ca035dbde09036e14d250e619598ce99e5cc28a525206121a6b", "signature": "25602620441c13311286aa197947b050085903ad1bce0b033cf319834aff365b"}, {"version": "bd6caa7e276fbbbc07a29ead8e8cb3dade100c299a6f6638f9ce5fe2c0c8cabf", "signature": "e5ada20dc9ffdeb27c3714903fb4b09b2a68e1e5fba73b5c657feda3c9a3ddd3"}, {"version": "e3e5d7d93c69f4a342d516d7ea284d8febb034ad1760731201a0e6fd1daf04ba", "signature": "9ed31331b1170ed572d11e62a3d58f228976fc25461623da3a130ea104cbf612"}, {"version": "a5f1003985df7c40af62d7bf69de9298e2cd3a00546fa33bad71a661717038dd", "signature": "aca19f4b4d7bd946251e61c04018649196acc6bf984bceb28750c04d8f93f4c8"}, {"version": "ab19549392bb41023bca8692941002060c69048bf4bb8ffbf81a7a0100988908", "signature": "b9d97fc4475de490025d6a79b7da621df452b41aaabd3d72f8af1a77521f9f97"}, {"version": "2849d7b93ad2f242d9beb3e8e2f573f895a9cc65d861016f2f6a1a634738dea9", "signature": "3afaf33e2ca541e65aadb4a6e2fa80e43e508844ead63ca39e40ff6e4fb25df8"}, {"version": "95d78e47fe1aeef87983f0ea98a9dad0b505ff07494ee74a50cc0423698b1112", "signature": "5c749631a5ff8ceea288e16ef82e6d40f3a4e5b7dd94ead85ae832adc3f06f97"}, {"version": "c2b95b269730a46e4fa3ee98a8a3b93d100fb5979804dac72c83491c343e2cc8", "signature": "b4a8acfa683e5d0406b9aa8a77b1b492717ef592f8d8f6d2e53f6428b9cd25d5"}, {"version": "bcf0497ba4d7f6afc165c6162e89862e38bc12522a4b53446ad84ffc240313f1", "signature": "ca2084d6836d80cf487751b0cd9dc5730a92cb3c176752dc03c38e112056c471"}, {"version": "c8ff2f7fbcce7260d9e3c6bc28bb168c58dbea4bfeb691c435cbf5d8c4de7427", "signature": "42a252ab64f63df265959958b3fc5775cfcfb4dd011b2e65912921ac8b3fa393"}, {"version": "38f3488c72ea9ecc851eab9df2a5cb718bccd249e9a41548efdffa98f4572b17", "signature": "32d48fc364de91fe6e7db882fb4a91466de34537b9882c4c0bf4028e2d948d48"}, {"version": "7ad35f9e73a39d666ede098986c3fca64aa417a418bb89e78686f89ded01d954", "signature": "b8324296458959fa6cc41a03c3e81b4d2697246fa649578b9b0f4c13ca594e37"}, {"version": "fb0c00088894f969d1d393f129f8f52874e13709eb37fa5ac726ef8640148144", "signature": "139271d64bf333c056c3e7fedd26023209b9e4d4ea2d9d28081ef2ae7c29242b"}, {"version": "93c3533560c3bc287696f48d78567fa5d516d99ea2998179f6544b1e03772302", "signature": "da91160170ec16f481bbdcadd90313a490005a9c0352ca8342eaaffd27aae860"}, {"version": "cb108c54939564ea91c1eb02f5e7e53915bfa559f68bbe1e8af054e708535940", "signature": "aa1725e38b5a0b4009f6a74f296f173e7926c119a60454dbd2e523861735df69"}, {"version": "92a462004a5a891dc855891da22219f655c53f75a71c5718f736bae8d827b402", "signature": "ed52292f573dfcea52ffdc29f27dc9901bc096a8f718689e7b54d3c10c866df8"}, {"version": "1ac9580dc775a5dc6199d938b49363b70e6a4ef3076820ff2eb058dbd6d10dfd", "signature": "fe4b9a92ba0df2b3d8c090bede1eefd502d4ffde1382e3a40c577f34a9feacff"}, {"version": "51bf47c3c8a3936a1b19e5d14c220aa81245e08f68323a8f167bcac5e345f24b", "signature": "da257a40bd0d9f9d6f18758a3a8cce4889743277c6c12f9da1c608210e605c47"}, {"version": "cfe9daf92d9b2c62dde00071d6dc6888c327db10baf6ef507e07071a24eec26d", "signature": "55e4c386bd1e71d457cc2737f6fd6f3bf0ce6ccd4c6f8b6b11e0bc2966cdba45"}, {"version": "91b663bfed1ea0035e5d7898af0bd7054a70a1881fb25de553f175fb21a7258a", "signature": "2cec837f8db6381cee76e562405a759e366b18a9c0c972e6eb438126c2180bf4"}, {"version": "1eb62fa77d5bb361f62cdfbab1c832399c89bc69ebfa86f202407149800fc3fa", "signature": "7b00364881695891fc565a45e9fedcfda58137ad83258baee1667a3449e1b94e"}, {"version": "7de933c1495bb86efa5177f050ec3d6f9ca2bdc499e7c392e38090fa56c142cd", "signature": "eb9eb9b55b469dc3ab177181bf46103dd95a1dc18caca944a105466d496a3813"}, {"version": "fd5aba727561f291335929a3af12ceef0a9a32f6812eeaf22e009c213fb87371", "signature": "e13c9b0516789d47b3e4ffa8f3976313b11d9967b956a6b9f7274b130c5257a6"}, {"version": "90656bac082da6cdc88ad6d4a6c8cd6386fc36f669a544015301293dedb29b85", "signature": "262fa38951654ea1f340dc72d06a0f4b42674717b4ee4bec1e50303f117009f5"}, {"version": "ba2c0e4dd2d09da051a720927ce4d1d9deecd02c622f1f55cae9de82938105f0", "signature": "a5fe09910b6a79252e8182ec5bf817440f07cba075c0f9bca66202d4b888360e"}, {"version": "8f1894909067a6f6487348ae2e98986be1e4978fb6fdbe2dea42c7b97111bef3", "signature": "cba3d1068e888dfe126581537bdc356710ce1f93212eb352189d318e45ce6120"}, {"version": "39868582a3360c8f00be41c1bc0437cdd9d0614fe396757449f2938d78500885", "signature": "122f6beddb0d797655f7ce0e01ed8040044b520bcc2e5a618bb036e02f9ccf2a"}, {"version": "e378ee4e0e1cfa3811ab93579c4eb3245324c320f3c01a2b69f8b8fb220e1101", "signature": "6cc0885b14be39631ad3e3e42c6f5a4ac1e7cd01452e03e2681f56d0392f3441"}, {"version": "c218091e446d7ba14b2b71e3f8ecf9f76d49aed7bbe122ca7045320c5ac32ff5", "signature": "1b2a35f9b827d266c07b5f08ff87205f1135fc454fd96b020a49ca9be9e041c0"}, {"version": "561e9492eae1120449b03c1daf37731daf854b41cb0d3df488e94b986c3061c7", "signature": "f4b6ec2b83230ced9ba6d396be9df26ef68ce6b138bd95bbfeb82056fb566c9d"}, {"version": "c971a9086476b23485b49a5c43b00bf6b67f06bb7e5b4556760de430e4f6344c", "signature": "fe5b23d5d2c56c483e4cc8d28a309950b20172be01730ea30ef06bfcf8758d0e"}, {"version": "5548cc0c81cd35b7d87341e89d781b0365e37db5d9d3ace39ef8be6937b91272", "signature": "566ea721c808b03b14712ddb2ecb3bf384b5b82b6034870ba3af53b35443b495"}, {"version": "32dabeda02d689f2ee0edd5705b17c973cbff41cf8e070229c5c17b8ee072a64", "signature": "d1dfb628e3097009663f3a0503385f559695a690fcebaa6e68ad4f2ba53877d6"}, {"version": "e186399c8765066d927aa1cdcedece89e7527f6746859fec91997c504125ad87", "signature": "3db73067a61593b120610c196b1c26451cc4578ab767d67abe2f251ed7f0f576"}, {"version": "d563d6b8ee815b4559baba26a30fa7877f44fa39efb9a82b7b34e11438520978", "signature": "2a69bf57b561f6717f1fef0c58e956daa9b66eb603096862c46fb5c44c94489d"}, {"version": "4545a7ac6ae29b45d9e1c7eafdd13bb49fad6973e9e1b83ca027da50f29c3792", "signature": "a886320b3bc5311baa611b69f2202fe9218a0f7a50103332c329fc133da3d164"}, {"version": "1cd0671058dd1719bb184bc7486ed2296dce262ff46521fd642cbeff65bc2e3b", "signature": "bd5f72bd3ac98cd1eddd6bc229a4105dafc195a4ee753bd4788f7bc17fd15552"}, {"version": "07dbfc5f93002ac86ec6581346dc782a44f85cb3a6850d8b06460885b3f8c1d1", "signature": "8d869bb31403d0a6e2e94ee102f47f4f8274adef89ef2083a6da00ea64f9f4c4"}, {"version": "3b2a464a19629c19743ada29e0f23cec0af390e59433a2b75015a3e33ad6f878", "signature": "f2b306b6f53e4a1e4dff10fe1aa98cf0575c75a4c36843521e01e3c06833cc43"}, {"version": "cf2f5c4299b29b28bb157b56e0869df9777f9f35f8431ee395170b32c90ec49f", "signature": "d57d0b7cf0e241411351f499e5e502c39e3ba140a35660bb72b05a1e3f42b280"}, {"version": "1055ee292336e3740fd7336b9fb2904c3a4c2d15a88d877aa11b671df83e692c", "signature": "1c046c1658dea6723bda8a607fe2f863e50d75c385cd501813cbfbad3ed83441"}, {"version": "7ea28577c7c0fc4d73f759c8d076e7eeea3b08d4da1cea49b0f3f76c48b73fe4", "signature": "c7498f3f8b8e09fe723418074970ca202bbc3e9cd4158cd0939a279c517a91df"}, {"version": "5fbc609434a9cc407917106e4feb50bc5b738e8995159f1ce6077ef8a79abc4d", "signature": "016172652f9ecd297bf6595feac997ac53edb2ae3aa7e3e39f90c4daba751834"}, {"version": "f92b1830bb37a1df8373246c2ac3c0624bec8e19d3d88ba1d9b22c38cab99fcc", "signature": "f3ac8b88c98a4037bbc253fa0d7a88ea43b52d42b58d515bbe947abfa96373f3"}, {"version": "5f9622bd5d26b26dc8e4b0466fd79926ef39a7cabb7459350ff20a5fc4538551", "signature": "a7dadaab5bfb408541b911cc073cf37369354741ddc4b64adf19ea1afe41f5da"}, {"version": "ad8d40ff61b6c210a831f106e1ae97cb6cd397f74fc294af51c4310b7cffc695", "signature": "1006d98c19e9fca10adf4a07910e3bd0d34ea21d567cbcd4d31ebc93fd75d7c4"}, {"version": "57db4ccceb5f3d25fb61a88e1bc479c2a720b05f0b81439bf572e488e2a194f8", "signature": "0dea725303e05ad166a61a743ccc7e4a29044fadac82421a06108fb31fd73c26"}, {"version": "f54b5b6b5aa1def486fbac1f6e3e302c9ff52e70a59bd830feb3e8ccd34d70df", "signature": "4f229bc61bd99862c7a85c84525ea166f158652011af4f8e97aa53301cc16dbb"}, {"version": "69c4d1b1827d4c89335517658e32b4488b1773f977da47e1e202d41157dbe322", "signature": "32a7a848d6bf776fe4a17efe317e5f425a5d04bd63980d0600be7cfd8aac289b"}, {"version": "b145ffbe0e94ef33832e29a6456d99567d391f62f43cc0264521e26e50d3faf7", "signature": "f6cafe6a3f8d329bab0e193339c69156a956ca689231efd7c956b5c680b78c63"}, {"version": "5497f9a2ab281a7d32629ab4d9eb4affe6d09e51e7f83878eab6b14a9975706e", "signature": "8db1e13af6c34a337f77c946682fe023a6c29659d2728e1020597fb9e221bd23"}, {"version": "e97c388b2a308b08f453362b7c7480c3acaa9d5a52086ae1ce1c857bac2e10e3", "signature": "86649af7748917ce79046301ee0d43d1bfcdf53dcf323fcbbf9bb9a6696d7de0"}, {"version": "44bab8ced43078310e1affbf44ca0682f42151082fe1d59a653699697ecabd7d", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "6ff52c2b8704b5419ca72b86fbb3aaba794e7143fe57cdc49042ecdc7a5951cd", "impliedFormat": 1}, {"version": "dc265c71a6e6823017b49104929b065014f689b4dfff724f5a9d6ce8328b19bb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "80aac188695136d5ba5cebd0b5786392e783247627b21e6ee048c10a4f9eb938", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0f80e8f9529d471302aaee32eb01f01493b81609f91e48aba2bd9cc5040fca75", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "685bc0a81ae117ea328b7903f6ce188ad7bf5f7789dda7dd226ec841a5dbee02", "impliedFormat": 1}, {"version": "95606fa69bbf0a1d08fde6092fc89c6eef3fab12f431db03eb33ebbcf23f692b", "signature": "8c133670e1285c516e9f5acbc92a9dd1caa1765eb881f32ce082573499f73f0f"}, {"version": "3aed11b3e885f66e475f3fa93da527c338d1dec7db22462cca44a17c67ea9eed", "affectsGlobalScope": true}, {"version": "68aba9c37b535b42ce96b78a4cfa93813bf4525f86dceb88a6d726c5f7c6c14f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c438b413e94ff76dfa20ae005f33a1c84f2480d1d66e0fd687501020d0de9b50", "impliedFormat": 1}, {"version": "bc6a78961535181265845bf9b9e8a147ffd0ca275097ceb670a9b92afa825152", "impliedFormat": 1}, {"version": "1fc4b0908c44f39b1f2e5a728d472670a0ea0970d2c6b5691c88167fe541ff82", "impliedFormat": 1}, {"version": "123ec69e4b3a686eb49afd94ebe3292a5c84a867ecbcb6bb84bdd720a12af803", "impliedFormat": 1}, {"version": "51851805d06a6878796c3a00ccf0839fe18111a38d1bae84964c269f16bcc2b7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "90c85ddbb8de82cd19198bda062065fc51b7407c0f206f2e399e65a52e979720", "impliedFormat": 1}, {"version": "dab288a3d92e6a3323566256ba56987b78b100341866fa3cc245c9cd2fd55706", "impliedFormat": 1}, {"version": "7ecfe97b43aa6c8b8f90caa599d5648bb559962e74e6f038f73a77320569dd78", "impliedFormat": 1}, {"version": "7db7569fbb3e2b01ba8751c761cdd3f0debd104170d5665b7dc20a11630df3a9", "impliedFormat": 1}, {"version": "cde4d7f6274468180fa39847b183aec22626e8212ff885d535c53f4cd7c225fd", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "072b0ac82ae8fe05b0d4f2eadb7f6edd0ebd84175ecad2f9e09261290a86bcee", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f6eedd1053167b8a651d8d9c70b1772e1b501264a36dfa881d7d4b30d623a9bc", "impliedFormat": 1}, {"version": "fb28748ff8d015f52e99daee4f454e57cec1a22141f1257c317f3630a15edeb7", "impliedFormat": 1}, {"version": "08fb2b0e1ef13a2df43f6d8e97019c36dfbc0475cf4d274c6838e2c9223fe39d", "impliedFormat": 1}, {"version": "5d9394b829cfd504b2fe17287aaad8ce1dcfb2a2183c962a90a85b96da2c1c90", "impliedFormat": 1}, {"version": "c969bf4c7cdfe4d5dd28aa09432f99d09ad1d8d8b839959646579521d0467d1a", "impliedFormat": 1}, {"version": "6c3857edaeeaaf43812f527830ebeece9266b6e8eb5271ab6d2f0008306c9947", "impliedFormat": 1}, {"version": "bc6a77e750f4d34584e46b1405b771fb69a224197dd6bafe5b0392a29a70b665", "impliedFormat": 1}, {"version": "46cac76114704902baa535b30fb66a26aeaf9430f3b3ab44746e329f12e85498", "impliedFormat": 1}, {"version": "ed4ae81196cccc10f297d228bca8d02e31058e6d723a3c5bc4be5fb3c61c6a34", "impliedFormat": 1}, {"version": "84044697c8b3e08ef24e4b32cfe6440143d07e469a5e34bda0635276d32d9f35", "impliedFormat": 1}, {"version": "6999f789ed86a40f3bc4d7e644e8d42ffda569465969df8077cd6c4e3505dd76", "impliedFormat": 1}, {"version": "0c9f2b308e5696d0802b613aff47c99f092add29408e654f7ab6026134250c18", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "30ec6f9c683b988c3cfaa0c4690692049c4e7ed7dc6f6e94f56194c06b86f5e1", "impliedFormat": 1}, {"version": "884560fda6c3868f925f022adc3a1289fe6507bbb45adb10fa1bbcc73a941bb0", "impliedFormat": 1}, {"version": "6b2bb67b0942bcfce93e1d6fad5f70afd54940a2b13df7f311201fba54b2cbe9", "impliedFormat": 1}, {"version": "dd3706b25d06fe23c73d16079e8c66ac775831ef419da00716bf2aee530a04a4", "impliedFormat": 1}, {"version": "1298327149e93a60c24a3b5db6048f7cc8fd4e3259e91d05fc44306a04b1b873", "impliedFormat": 1}, {"version": "d67e08745494b000da9410c1ae2fdc9965fc6d593fe0f381a47491f75417d457", "impliedFormat": 1}, {"version": "b40652bf8ce4a18133b31349086523b219724dca8df3448c1a0742528e7ad5b9", "impliedFormat": 1}, {"version": "3181290a158e54a78c1a57c41791ec1cbdc860ae565916daa1bf4e425b7edac7", "impliedFormat": 1}, {"version": "a77fdb357c78b70142b2fdbbfb72958d69e8f765fd2a3c69946c1018e89d4638", "impliedFormat": 1}, {"version": "3c2ac350c3baa61fd2b1925844109e098f4376d0768a4643abc82754fd752748", "impliedFormat": 1}, {"version": "826d48e49c905cedb906cbde6ccaf758827ff5867d4daa006b5a79e0fb489357", "impliedFormat": 1}, {"version": "5ef157fbb39494a581bd24f21b60488fe248d452c479738b5e41b48720ea69b8", "impliedFormat": 1}, {"version": "289be113bad7ee27ee7fa5b1e373c964c9789a5e9ed7db5ddcb631371120b953", "impliedFormat": 1}, {"version": "a1136cf18dbe1b9b600c65538fd48609a1a4772d115a0c1d775839fe6544487c", "impliedFormat": 1}, {"version": "e8482f41c6e001657302dcb3a1ba30359a0983574caee9405ef863cb9eac3b95", "impliedFormat": 1}, {"version": "ab99ac6bfd84d493602cc1baa2c726a4df8f2ec8f3750b565ad17368c440db3b", "impliedFormat": 1}, {"version": "d44028ae0127eb3e9fcfa5f55a8b81d64775ce15aca1020fe25c511bbb055834", "impliedFormat": 1}, {"version": "2708349d5a11a5c2e5f3a0765259ebe7ee00cdcc8161cb9990cb4910328442a1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4e0a4d84b15692ea8669fe4f3d05a4f204567906b1347da7a58b75f45bae48d3", "impliedFormat": 1}, {"version": "0f04bc8950ad634ac8ac70f704f200ef06f8852af9017f97c446de4def5b3546", "impliedFormat": 1}, {"version": "3c3aa211dfaba6f6432034d633bba5f1f11054c695a12a607cb71d3e6d5b1aec", "impliedFormat": 1}, {"version": "d20072cb51d8baad944bedd935a25c7f10c29744e9a648d2c72c215337356077", "impliedFormat": 1}, {"version": "35cbbc58882d2c158032d7f24ba8953d7e1caeb8cb98918d85819496109f55d2", "impliedFormat": 1}], "root": [1582, 1588, 1589], "options": {"allowUnusedLabels": true, "composite": false, "declaration": false, "declarationMap": false, "esModuleInterop": true, "experimentalDecorators": true, "importHelpers": true, "inlineSourceMap": true, "inlineSources": true, "module": 6, "noEmitHelpers": true, "noEmitOnError": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "../../../..", "removeComments": false, "strictNullChecks": false, "target": 9, "tsBuildInfoFile": "./.tsbuildinfo", "useDefineForClassFields": false}, "referencedMap": [[119, 1], [120, 2], [640, 1], [184, 3], [185, 4], [426, 5], [178, 6], [162, 7], [163, 8], [192, 9], [199, 10], [195, 11], [324, 12], [419, 13], [180, 14], [179, 15], [812, 15], [177, 15], [183, 16], [208, 17], [209, 18], [172, 7], [173, 19], [204, 7], [323, 20], [205, 21], [206, 22], [197, 11], [869, 23], [207, 7], [337, 24], [230, 16], [181, 15], [201, 25], [198, 7], [196, 7], [112, 26], [116, 27], [114, 28], [115, 7], [111, 7], [107, 29], [39, 30], [106, 31], [109, 32], [104, 33], [169, 15], [226, 34], [841, 35], [842, 36], [235, 37], [431, 38], [332, 39], [243, 40], [164, 41], [191, 42], [190, 15], [1468, 43], [325, 44], [326, 45], [194, 46], [170, 47], [427, 48], [227, 49], [229, 50], [228, 51], [232, 52], [1452, 53], [166, 54], [167, 55], [168, 56], [231, 57], [171, 58], [200, 59], [210, 60], [187, 61], [188, 7], [186, 62], [298, 63], [299, 64], [317, 65], [347, 66], [348, 67], [176, 58], [813, 68], [189, 7], [175, 69], [174, 70], [252, 71], [253, 72], [623, 73], [388, 39], [433, 74], [301, 75], [302, 76], [870, 77], [338, 78], [393, 79], [440, 38], [297, 80], [316, 81], [202, 82], [110, 7], [121, 83], [117, 84], [127, 85], [129, 86], [128, 87], [138, 88], [137, 89], [136, 90], [134, 91], [133, 92], [135, 7], [150, 93], [141, 94], [142, 7], [148, 95], [143, 15], [144, 7], [147, 95], [146, 96], [145, 94], [149, 97], [233, 98], [386, 7], [387, 99], [463, 100], [222, 101], [221, 7], [462, 102], [126, 103], [131, 104], [219, 105], [220, 7], [218, 106], [156, 107], [139, 108], [132, 15], [157, 109], [159, 110], [130, 90], [153, 111], [154, 112], [140, 113], [213, 114], [155, 115], [214, 116], [122, 7], [234, 117], [236, 118], [265, 119], [266, 120], [249, 121], [250, 122], [223, 47], [224, 123], [242, 124], [244, 125], [241, 126], [238, 127], [239, 128], [240, 129], [394, 130], [403, 131], [404, 132], [395, 133], [396, 134], [389, 135], [392, 136], [262, 137], [256, 138], [263, 139], [257, 140], [264, 141], [248, 142], [247, 143], [255, 144], [370, 145], [359, 146], [358, 145], [259, 147], [364, 146], [367, 148], [372, 148], [363, 149], [371, 146], [369, 146], [360, 146], [366, 146], [368, 146], [374, 150], [362, 150], [365, 146], [361, 146], [373, 149], [453, 151], [357, 146], [407, 152], [355, 15], [417, 153], [418, 153], [425, 154], [423, 155], [422, 156], [421, 153], [424, 155], [414, 157], [416, 158], [428, 159], [415, 160], [420, 161], [269, 47], [270, 162], [267, 163], [268, 164], [460, 165], [457, 127], [459, 166], [458, 127], [237, 47], [390, 167], [391, 168], [331, 169], [333, 170], [203, 171], [211, 172], [437, 173], [438, 174], [436, 175], [434, 176], [435, 177], [401, 178], [402, 179], [409, 47], [410, 180], [430, 181], [429, 182], [432, 183], [349, 7], [350, 184], [328, 185], [327, 186], [346, 187], [343, 7], [344, 7], [345, 185], [330, 188], [215, 11], [334, 189], [341, 190], [342, 191], [276, 192], [320, 193], [319, 190], [275, 190], [313, 7], [312, 7], [315, 194], [314, 195], [353, 196], [352, 197], [306, 198], [321, 7], [322, 199], [303, 200], [354, 201], [304, 202], [336, 203], [335, 190], [305, 204], [296, 205], [351, 206], [307, 190], [279, 207], [278, 208], [280, 209], [308, 190], [281, 210], [282, 190], [293, 190], [284, 211], [283, 190], [285, 190], [286, 190], [309, 212], [287, 209], [288, 190], [310, 213], [294, 190], [289, 190], [290, 190], [291, 209], [295, 214], [318, 215], [292, 216], [311, 213], [339, 217], [340, 218], [356, 219], [260, 220], [408, 221], [261, 222], [399, 163], [400, 223], [405, 224], [406, 225], [397, 226], [398, 227], [161, 228], [212, 229], [151, 15], [273, 230], [274, 231], [375, 47], [376, 232], [271, 230], [272, 233], [251, 135], [254, 234], [450, 235], [451, 236], [442, 237], [443, 238], [445, 239], [446, 240], [447, 241], [448, 242], [439, 243], [452, 244], [444, 245], [449, 246], [455, 247], [456, 248], [454, 15], [674, 249], [675, 250], [673, 251], [676, 252], [677, 253], [678, 254], [679, 255], [680, 256], [681, 257], [682, 258], [683, 259], [684, 260], [685, 261], [1635, 262], [1593, 263], [1594, 264], [1595, 265], [1596, 266], [1597, 267], [1598, 268], [1600, 269], [1602, 270], [1603, 271], [1604, 272], [1605, 273], [1606, 274], [1636, 275], [1607, 269], [1608, 276], [1609, 277], [1612, 278], [1613, 279], [1616, 280], [1617, 281], [1618, 269], [1621, 282], [1630, 283], [1633, 284], [1623, 285], [1624, 286], [1626, 267], [1628, 287], [1629, 267], [1212, 288], [1122, 289], [125, 138], [1213, 7], [1214, 290], [1215, 291], [1055, 292], [1050, 293], [1053, 294], [1051, 295], [1052, 296], [1054, 297], [1291, 298], [1292, 299], [1290, 300], [1289, 301], [1288, 301], [1296, 302], [1294, 303], [1295, 304], [624, 7], [632, 305], [633, 306], [634, 7], [636, 307], [635, 308], [643, 309], [638, 310], [642, 311], [637, 7], [641, 312], [644, 313], [639, 314], [646, 315], [645, 316], [1413, 317], [1414, 318], [1416, 319], [1412, 320], [1411, 7], [1415, 321], [1402, 7], [1407, 322], [1419, 323], [1406, 7], [1408, 324], [1405, 325], [1409, 326], [1417, 327], [1418, 328], [630, 329], [626, 330], [627, 331], [629, 332], [628, 333], [654, 334], [647, 7], [653, 335], [651, 336], [650, 337], [652, 338], [649, 339], [648, 7], [970, 340], [969, 341], [966, 342], [955, 7], [967, 343], [968, 344], [965, 345], [964, 346], [957, 347], [1152, 348], [1150, 349], [1149, 350], [1154, 351], [1153, 352], [385, 353], [380, 354], [381, 355], [383, 356], [379, 357], [378, 7], [384, 358], [477, 359], [475, 7], [466, 360], [469, 361], [465, 7], [476, 362], [474, 363], [467, 364], [471, 363], [464, 7], [473, 365], [468, 366], [472, 367], [470, 368], [846, 11], [103, 369], [53, 370], [51, 370], [499, 371], [78, 372], [66, 373], [46, 374], [76, 373], [77, 373], [80, 375], [81, 373], [48, 376], [82, 373], [83, 373], [84, 373], [85, 373], [86, 377], [87, 378], [88, 373], [44, 373], [89, 373], [90, 373], [91, 377], [92, 373], [93, 373], [94, 379], [95, 373], [96, 375], [97, 373], [45, 373], [98, 373], [99, 373], [100, 380], [43, 381], [478, 382], [479, 382], [480, 373], [481, 382], [482, 382], [483, 382], [484, 373], [485, 373], [486, 382], [487, 382], [488, 382], [489, 382], [490, 382], [491, 382], [492, 373], [493, 382], [494, 382], [495, 382], [496, 382], [497, 373], [498, 383], [500, 384], [501, 382], [502, 382], [503, 382], [504, 382], [505, 373], [506, 382], [507, 382], [508, 385], [509, 382], [510, 382], [511, 380], [512, 373], [513, 373], [49, 386], [514, 382], [515, 382], [516, 373], [517, 387], [518, 382], [519, 383], [520, 382], [521, 382], [522, 382], [523, 385], [524, 382], [525, 385], [526, 382], [527, 388], [528, 389], [529, 373], [530, 382], [531, 373], [532, 382], [533, 390], [534, 390], [535, 390], [536, 373], [537, 373], [538, 382], [543, 382], [539, 382], [540, 373], [541, 382], [542, 373], [544, 373], [545, 382], [546, 382], [547, 380], [548, 382], [549, 382], [550, 373], [551, 382], [552, 382], [553, 373], [554, 382], [555, 382], [556, 382], [557, 382], [558, 382], [559, 382], [560, 382], [561, 382], [562, 373], [563, 382], [564, 382], [565, 382], [566, 391], [567, 382], [568, 382], [569, 382], [570, 382], [571, 382], [572, 382], [573, 373], [574, 373], [575, 373], [576, 373], [577, 373], [578, 382], [579, 382], [580, 382], [79, 392], [52, 393], [101, 11], [54, 394], [55, 395], [64, 396], [63, 397], [59, 398], [58, 397], [60, 399], [57, 400], [56, 401], [62, 402], [61, 399], [65, 403], [47, 404], [42, 405], [40, 382], [41, 406], [70, 377], [67, 382], [847, 407], [581, 408], [1083, 409], [1584, 410], [1587, 411], [622, 412], [620, 413], [1581, 414], [1570, 415], [657, 416], [655, 417], [658, 418], [1135, 419], [1139, 420], [1138, 421], [1137, 422], [1136, 423], [727, 424], [815, 425], [854, 426], [856, 427], [855, 428], [1209, 429], [1216, 430], [902, 431], [903, 432], [744, 433], [745, 434], [857, 435], [858, 436], [1339, 437], [1340, 438], [758, 439], [777, 440], [667, 441], [670, 442], [972, 437], [971, 443], [973, 444], [814, 445], [816, 446], [731, 447], [732, 448], [1572, 449], [1571, 419], [1580, 450], [1579, 451], [1574, 452], [1573, 451], [1577, 419], [1578, 453], [1576, 454], [1575, 419], [1322, 437], [1126, 455], [1123, 456], [1125, 457], [859, 458], [860, 459], [861, 460], [784, 437], [785, 461], [827, 428], [829, 462], [828, 428], [830, 463], [831, 464], [664, 465], [668, 419], [669, 466], [833, 467], [832, 428], [778, 437], [779, 468], [672, 423], [878, 469], [1274, 470], [991, 471], [1097, 472], [746, 472], [660, 473], [661, 474], [862, 471], [715, 475], [687, 423], [949, 423], [1263, 472], [864, 476], [1145, 477], [656, 423], [585, 423], [1259, 476], [738, 423], [662, 472], [765, 478], [766, 478], [764, 478], [663, 479], [770, 428], [768, 428], [1210, 428], [769, 480], [771, 428], [776, 481], [775, 480], [772, 428], [767, 480], [773, 423], [774, 482], [759, 480], [1525, 483], [621, 484], [1436, 485], [671, 486], [1299, 487], [1124, 488], [689, 489], [686, 490], [690, 491], [780, 492], [1108, 493], [1074, 494], [714, 495], [665, 496], [1084, 497], [1086, 498], [1275, 499], [863, 500], [894, 501], [985, 502], [691, 503], [716, 504], [1491, 505], [1389, 506], [1454, 507], [897, 508], [720, 509], [853, 494], [692, 489], [1550, 510], [1276, 511], [1284, 512], [1277, 513], [688, 514], [717, 515], [1551, 489], [1100, 516], [1098, 517], [865, 518], [1426, 519], [1431, 520], [1099, 521], [1155, 522], [1439, 523], [1443, 524], [1337, 525], [750, 526], [693, 527], [1142, 528], [781, 529], [694, 530], [1526, 531], [1056, 532], [1057, 533], [782, 534], [751, 535], [1140, 536], [1378, 537], [1358, 538], [1552, 539], [1141, 540], [783, 541], [1338, 542], [1359, 543], [1362, 544], [695, 545], [1058, 532], [1059, 533], [752, 546], [1146, 547], [728, 548], [1013, 549], [1360, 550], [1545, 551], [1260, 552], [739, 553], [987, 554], [1029, 555], [1107, 556], [817, 557], [730, 558], [722, 559], [594, 423], [607, 423], [595, 423], [593, 423], [589, 423], [1068, 423], [590, 476], [591, 476], [659, 423], [596, 476], [836, 423], [601, 560], [586, 423], [1085, 423], [893, 561], [1473, 476], [597, 476], [598, 423], [1490, 476], [614, 423], [615, 423], [617, 562], [616, 423], [618, 563], [1453, 564], [600, 423], [852, 423], [592, 476], [1549, 423], [587, 423], [604, 476], [729, 423], [603, 565], [611, 423], [613, 476], [1161, 476], [609, 476], [610, 476], [612, 476], [1442, 476], [1144, 476], [1012, 476], [1361, 423], [608, 423], [666, 423], [602, 476], [986, 423], [1028, 423], [588, 423], [606, 566], [599, 476], [1567, 428], [1568, 567], [1569, 568], [1190, 569], [819, 423], [713, 570], [1093, 571], [1073, 572], [1070, 573], [1072, 574], [1071, 575], [1069, 576], [1040, 577], [1042, 578], [1041, 579], [798, 580], [797, 424], [1043, 581], [1049, 582], [1045, 583], [1046, 584], [1047, 585], [1044, 564], [1048, 586], [1147, 587], [799, 588], [753, 589], [800, 590], [754, 545], [756, 591], [757, 592], [740, 593], [912, 594], [913, 595], [915, 596], [917, 597], [918, 598], [914, 599], [919, 600], [920, 601], [922, 602], [924, 603], [921, 604], [923, 605], [925, 606], [928, 607], [926, 608], [927, 608], [741, 609], [930, 610], [929, 611], [978, 612], [979, 613], [983, 614], [980, 615], [981, 616], [984, 617], [982, 618], [931, 619], [932, 620], [934, 621], [937, 622], [938, 623], [942, 624], [940, 625], [936, 626], [939, 627], [941, 628], [945, 629], [944, 630], [943, 631], [946, 632], [948, 633], [947, 634], [1025, 635], [1027, 636], [1026, 637], [974, 638], [975, 616], [977, 639], [976, 640], [1004, 641], [1006, 642], [1005, 643], [1008, 644], [1007, 645], [1009, 646], [1011, 647], [1010, 648], [997, 649], [998, 650], [1000, 651], [999, 652], [1001, 632], [1003, 653], [1002, 654], [989, 655], [990, 656], [993, 657], [992, 658], [994, 659], [988, 643], [996, 660], [995, 661], [866, 662], [867, 663], [868, 664], [887, 665], [888, 428], [890, 666], [889, 667], [891, 668], [871, 669], [892, 670], [880, 671], [895, 672], [896, 673], [900, 674], [905, 675], [898, 676], [899, 677], [904, 678], [881, 679], [872, 680], [882, 681], [883, 682], [886, 683], [908, 684], [906, 685], [907, 686], [885, 687], [884, 472], [909, 688], [911, 689], [910, 690], [837, 691], [838, 692], [839, 693], [840, 694], [843, 695], [844, 696], [845, 697], [848, 698], [849, 699], [851, 700], [850, 701], [951, 702], [950, 703], [952, 632], [954, 704], [953, 705], [802, 706], [810, 707], [801, 708], [804, 709], [805, 710], [806, 711], [807, 712], [803, 713], [808, 714], [811, 715], [809, 716], [1017, 486], [1018, 717], [1019, 718], [1015, 719], [1020, 720], [1014, 721], [1016, 722], [1021, 723], [1022, 724], [1024, 725], [1023, 726], [1033, 727], [1030, 616], [1036, 728], [1031, 729], [1032, 730], [1034, 731], [1035, 732], [818, 733], [822, 734], [820, 735], [821, 736], [823, 737], [824, 738], [826, 739], [825, 740], [725, 727], [1060, 741], [1063, 742], [1067, 743], [1064, 744], [1065, 428], [1061, 745], [1066, 746], [1062, 747], [748, 748], [749, 749], [719, 750], [1075, 751], [1076, 752], [733, 753], [786, 754], [787, 755], [788, 756], [789, 757], [790, 758], [791, 759], [723, 760], [724, 761], [1077, 762], [792, 763], [793, 764], [794, 765], [795, 766], [796, 767], [735, 768], [1079, 769], [734, 770], [1078, 771], [736, 772], [1080, 773], [1087, 774], [1088, 775], [737, 776], [1089, 777], [1090, 778], [711, 779], [712, 780], [743, 781], [1092, 782], [742, 783], [1091, 784], [718, 564], [1037, 785], [1039, 786], [1038, 787], [755, 788], [1464, 789], [1467, 790], [1466, 791], [1465, 792], [1094, 428], [1096, 793], [1095, 794], [1129, 795], [1127, 795], [1130, 795], [1131, 796], [1128, 795], [1132, 797], [1134, 798], [1133, 799], [1469, 747], [1479, 800], [1483, 801], [747, 802], [1478, 803], [1482, 804], [933, 802], [1481, 727], [1475, 805], [1476, 806], [1477, 807], [1471, 808], [1470, 727], [1480, 809], [1472, 808], [935, 802], [700, 810], [699, 428], [701, 811], [1508, 812], [1510, 813], [1509, 814], [1474, 815], [1506, 816], [1507, 727], [1399, 817], [1400, 818], [1395, 819], [1396, 820], [1397, 821], [1398, 822], [1401, 662], [1420, 823], [1421, 824], [1422, 825], [1424, 826], [1423, 827], [1163, 828], [1164, 829], [1160, 830], [1162, 831], [1367, 832], [1368, 833], [1193, 834], [1365, 835], [1366, 836], [1186, 837], [1187, 838], [1184, 839], [1185, 831], [834, 428], [835, 840], [1158, 841], [1369, 842], [1371, 843], [1370, 844], [1156, 845], [1157, 846], [1159, 847], [1183, 848], [1180, 849], [1182, 850], [1181, 851], [1165, 428], [1170, 852], [1169, 853], [1166, 423], [1168, 854], [1167, 855], [1171, 856], [1173, 857], [1174, 858], [1172, 859], [1175, 860], [1176, 428], [1177, 428], [1178, 861], [1179, 428], [1372, 428], [1394, 862], [1393, 863], [1381, 864], [1382, 865], [1384, 866], [1383, 867], [1385, 868], [1375, 869], [1387, 870], [1374, 871], [1386, 872], [1373, 873], [1273, 874], [1376, 875], [1380, 876], [1377, 877], [1388, 878], [1379, 879], [619, 471], [879, 880], [1351, 881], [1347, 882], [1348, 883], [1346, 884], [1350, 885], [1345, 886], [1349, 887], [1341, 888], [1342, 889], [1343, 890], [877, 891], [1344, 892], [1355, 893], [1356, 894], [1353, 895], [1354, 896], [1357, 428], [1364, 897], [1363, 898], [873, 423], [1352, 899], [1493, 900], [1494, 901], [1495, 902], [1496, 902], [1501, 903], [1502, 904], [1497, 905], [1498, 906], [1499, 907], [1500, 908], [1492, 423], [1503, 428], [1505, 909], [1504, 910], [1390, 911], [1392, 912], [1391, 913], [1455, 428], [1456, 914], [1457, 915], [1458, 916], [1459, 917], [1460, 918], [1564, 919], [1566, 920], [1565, 921], [1461, 922], [1463, 923], [1462, 924], [1511, 925], [1512, 926], [1515, 927], [1514, 928], [1516, 929], [1513, 930], [1518, 931], [1519, 932], [1520, 933], [1521, 934], [1523, 935], [1522, 936], [1524, 937], [1517, 938], [1553, 428], [1561, 939], [1554, 940], [1555, 941], [1556, 942], [1557, 943], [1558, 941], [1559, 944], [1560, 945], [1279, 946], [1280, 947], [1281, 948], [1282, 949], [1283, 950], [1278, 951], [1336, 952], [1335, 953], [1330, 954], [1331, 955], [1333, 956], [1334, 957], [1285, 958], [1329, 959], [1297, 960], [1298, 961], [1300, 962], [1301, 963], [1302, 964], [1304, 965], [1306, 966], [1305, 967], [1307, 968], [1308, 439], [1309, 969], [1310, 970], [1311, 971], [1319, 972], [1312, 973], [1316, 974], [1313, 975], [1317, 976], [1314, 977], [1315, 978], [1318, 979], [1320, 980], [1321, 981], [1323, 982], [1324, 983], [1325, 984], [1326, 985], [1327, 986], [1328, 987], [1332, 988], [876, 989], [874, 423], [875, 990], [696, 991], [698, 992], [697, 993], [1563, 994], [1562, 995], [1103, 996], [1104, 997], [1101, 998], [1102, 841], [1106, 999], [1105, 428], [1109, 1000], [1118, 1001], [1110, 1002], [1111, 1003], [1112, 1004], [1113, 841], [1115, 1005], [1116, 1006], [1114, 998], [1117, 428], [1120, 1007], [1119, 1008], [1219, 1009], [1220, 1010], [1221, 1011], [1194, 1012], [1197, 1013], [1198, 1014], [1195, 1015], [1196, 1016], [1199, 1017], [1208, 1018], [1202, 1019], [1203, 1020], [1200, 1015], [1201, 1016], [1206, 1021], [1207, 1022], [1204, 1015], [1205, 1016], [1217, 1023], [1218, 1024], [1222, 1025], [1223, 1026], [1225, 1027], [1256, 1028], [1228, 1029], [1229, 1030], [1226, 1031], [1227, 1032], [1232, 1033], [1233, 1034], [1230, 1031], [1231, 1032], [1234, 1035], [1235, 1036], [1188, 1037], [1189, 1038], [1236, 1039], [1255, 1040], [1143, 423], [1237, 1041], [1238, 1042], [1241, 1043], [1240, 424], [1242, 1037], [1244, 1044], [1243, 424], [1245, 1037], [1247, 1045], [1246, 424], [1248, 1037], [1250, 1046], [1249, 424], [1251, 1037], [1253, 1047], [1252, 424], [1254, 1037], [1239, 423], [1191, 1048], [1192, 1049], [1224, 1050], [1258, 1051], [1257, 428], [1264, 1052], [1265, 662], [1266, 1053], [1268, 1054], [1269, 1055], [1261, 1056], [1262, 1057], [1270, 428], [1272, 1058], [1271, 1059], [1267, 1060], [1485, 1061], [1484, 1062], [1488, 1063], [1489, 1064], [1486, 1065], [1487, 727], [1425, 1066], [1429, 1067], [1449, 1068], [1430, 1069], [1428, 1070], [1433, 1071], [1434, 1072], [1432, 1073], [1435, 428], [1438, 1074], [1437, 1075], [1440, 1076], [1441, 1077], [1427, 1078], [1446, 1079], [1444, 423], [1447, 1080], [1445, 1081], [1451, 1082], [1448, 428], [1450, 1083], [1546, 1084], [1548, 1085], [1547, 1086], [702, 428], [703, 1087], [705, 1088], [704, 428], [707, 1089], [706, 428], [708, 428], [710, 1090], [709, 1091], [1530, 1092], [1528, 423], [1532, 1093], [916, 1094], [1527, 1095], [1531, 1096], [1529, 1097], [1535, 1098], [1534, 1099], [1537, 424], [1538, 1100], [605, 471], [1540, 1101], [1542, 1102], [1541, 1103], [1539, 727], [1533, 1104], [726, 1105], [1536, 428], [1544, 1106], [1543, 1107], [763, 1108], [762, 428], [761, 1109], [760, 423], [582, 423], [584, 1110], [583, 1111], [1582, 1112], [1588, 1113]], "version": "5.9.2"}
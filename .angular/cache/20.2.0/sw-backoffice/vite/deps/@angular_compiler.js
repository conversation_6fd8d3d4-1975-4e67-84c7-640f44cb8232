import {
  AST,
  ASTWithName,
  ASTWithSource,
  AbsoluteSourceSpan,
  ArrayType,
  ArrowFunctionExpr,
  Attribute,
  Binary,
  BinaryOperator,
  BinaryOperatorExpr,
  BindingPipe,
  BindingPipeType,
  BindingType,
  Block,
  BlockNode,
  BlockParameter,
  BoundAttribute,
  BoundDeferredTrigger,
  BoundElementProperty,
  BoundEvent,
  BoundText,
  BuiltinType,
  BuiltinTypeName,
  CUSTOM_ELEMENTS_SCHEMA,
  Call,
  Chain,
  ChangeDetectionStrategy,
  CombinedRecursiveAstVisitor,
  CommaExpr,
  Comment,
  CompilerConfig,
  CompilerFacadeImpl,
  Component,
  Component$1,
  Conditional,
  ConditionalExpr,
  ConstantPool,
  Content,
  CssSelector,
  DEFAULT_INTERPOLATION_CONFIG,
  DYNAMIC_TYPE,
  DeclareFunctionStmt,
  DeclareVarStmt,
  DeferredBlock,
  DeferredBlockError,
  DeferredBlockLoading,
  DeferredBlockPlaceholder,
  DeferredTrigger,
  Directive,
  Directive$1,
  DomElementSchemaRegistry,
  DynamicImportExpr,
  EOF,
  Element,
  Element$1,
  ElementSchemaRegistry,
  EmitterVisitorContext,
  EmptyExpr$1,
  Expansion,
  ExpansionCase,
  Expression,
  ExpressionBinding,
  ExpressionStatement,
  ExpressionType,
  ExternalExpr,
  ExternalReference,
  FactoryTarget,
  ForLoopBlock,
  ForLoopBlockEmpty,
  FunctionExpr,
  HostElement,
  HoverDeferredTrigger,
  HtmlParser,
  HtmlTagDefinition,
  I18NHtmlParser,
  Icu$1,
  Identifiers,
  IdleDeferredTrigger,
  IfBlock,
  IfBlockBranch,
  IfStmt,
  ImmediateDeferredTrigger,
  ImplicitReceiver,
  InstantiateExpr,
  InteractionDeferredTrigger,
  Interpolation$1,
  InterpolationConfig,
  InvokeFunctionExpr,
  JSDocComment,
  JitEvaluator,
  KeyedRead,
  LeadingComment,
  LetDeclaration,
  LetDeclaration$1,
  Lexer,
  LiteralArray,
  LiteralArrayExpr,
  LiteralExpr,
  LiteralMap,
  LiteralMapExpr,
  LiteralPrimitive,
  LocalizedString,
  MapType,
  MessageBundle,
  NONE_TYPE,
  NO_ERRORS_SCHEMA,
  NeverDeferredTrigger,
  NodeWithI18n,
  NonNullAssert,
  NotExpr,
  ParenthesizedExpr,
  ParenthesizedExpression,
  ParseError,
  ParseErrorLevel,
  ParseLocation,
  ParseSourceFile,
  ParseSourceSpan,
  ParseSpan,
  ParseTreeResult,
  ParsedEvent,
  ParsedEventType,
  ParsedProperty,
  ParsedPropertyType,
  ParsedVariable,
  Parser,
  PrefixNot,
  PropertyRead,
  R3NgModuleMetadataKind,
  R3SelectorScopeMode,
  R3TargetBinder,
  R3TemplateDependencyKind,
  ReadKeyExpr,
  ReadPropExpr,
  ReadVarExpr,
  RecursiveAstVisitor,
  RecursiveVisitor,
  RecursiveVisitor$1,
  Reference,
  ResourceLoader,
  ReturnStatement,
  SECURITY_SCHEMA,
  STRING_TYPE,
  SafeCall,
  SafeKeyedRead,
  SafePropertyRead,
  SelectorContext,
  SelectorListContext,
  SelectorMatcher,
  SelectorlessMatcher,
  Serializer,
  SplitInterpolation,
  Statement,
  StmtModifier,
  StringToken,
  StringTokenKind,
  SwitchBlock,
  SwitchBlockCase,
  TagContentType,
  TaggedTemplateLiteral,
  TaggedTemplateLiteralExpr,
  Template,
  TemplateBindingParseResult,
  TemplateLiteral,
  TemplateLiteralElement,
  TemplateLiteralElementExpr,
  TemplateLiteralExpr,
  Text,
  Text$3,
  TextAttribute,
  ThisReceiver,
  TimerDeferredTrigger,
  Token,
  TokenType,
  TransplantedType,
  TreeError,
  Type,
  TypeModifier,
  TypeofExpr,
  TypeofExpression,
  Unary,
  UnaryOperator,
  UnaryOperatorExpr,
  UnknownBlock,
  VERSION,
  Variable,
  VariableBinding,
  Version,
  ViewEncapsulation$1,
  ViewportDeferredTrigger,
  VoidExpr,
  VoidExpression,
  WrappedNodeExpr,
  Xliff,
  Xliff2,
  Xmb,
  XmlParser,
  Xtb,
  compileClassDebugInfo,
  compileClassMetadata,
  compileComponentClassMetadata,
  compileComponentDeclareClassMetadata,
  compileComponentFromMetadata,
  compileDeclareClassMetadata,
  compileDeclareComponentFromMetadata,
  compileDeclareDirectiveFromMetadata,
  compileDeclareFactoryFunction,
  compileDeclareInjectableFromMetadata,
  compileDeclareInjectorFromMetadata,
  compileDeclareNgModuleFromMetadata,
  compileDeclarePipeFromMetadata,
  compileDeferResolverFunction,
  compileDirectiveFromMetadata,
  compileFactoryFunction,
  compileHmrInitializer,
  compileHmrUpdateCallback,
  compileInjectable,
  compileInjector,
  compileNgModule,
  compileOpaqueAsyncClassMetadata,
  compilePipeFromMetadata,
  computeMsgId,
  core,
  createCssSelectorFromNode,
  createInjectableType,
  createMayBeForwardRefExpression,
  devOnlyGuardedExpression,
  emitDistinctChangesOnlyDefaultValue,
  encapsulateStyle,
  escapeRegExp,
  findMatchingDirectivesAndPipes,
  getHtmlTagDefinition,
  getNsPrefix,
  getSafePropertyAccessString,
  identifierName,
  isNgContainer,
  isNgContent,
  isNgTemplate,
  jsDocComment,
  leadingComment,
  literal,
  literalMap,
  makeBindingParser,
  mergeNsAndName,
  output_ast,
  parseHostBindings,
  parseTemplate,
  preserveWhitespacesDefault,
  publishFacade,
  r3JitTypeSourceSpan,
  sanitizeIdentifier,
  setEnableTemplateSourceLocations,
  splitNsName,
  verifyHostBindings,
  visitAll,
  visitAll$1
} from "./chunk-UTD366X7.js";
import "./chunk-WOR4A3D2.js";
export {
  AST,
  ASTWithName,
  ASTWithSource,
  AbsoluteSourceSpan,
  ArrayType,
  ArrowFunctionExpr,
  Attribute,
  Binary,
  BinaryOperator,
  BinaryOperatorExpr,
  BindingPipe,
  BindingPipeType,
  BindingType,
  Block,
  BlockParameter,
  BoundElementProperty,
  BuiltinType,
  BuiltinTypeName,
  CUSTOM_ELEMENTS_SCHEMA,
  Call,
  Chain,
  ChangeDetectionStrategy,
  CombinedRecursiveAstVisitor,
  CommaExpr,
  Comment,
  CompilerConfig,
  CompilerFacadeImpl,
  Component,
  Conditional,
  ConditionalExpr,
  ConstantPool,
  CssSelector,
  DEFAULT_INTERPOLATION_CONFIG,
  DYNAMIC_TYPE,
  DeclareFunctionStmt,
  DeclareVarStmt,
  Directive,
  DomElementSchemaRegistry,
  DynamicImportExpr,
  EOF,
  Element,
  ElementSchemaRegistry,
  EmitterVisitorContext,
  EmptyExpr$1 as EmptyExpr,
  Expansion,
  ExpansionCase,
  Expression,
  ExpressionBinding,
  ExpressionStatement,
  ExpressionType,
  ExternalExpr,
  ExternalReference,
  FactoryTarget,
  FunctionExpr,
  HtmlParser,
  HtmlTagDefinition,
  I18NHtmlParser,
  IfStmt,
  ImplicitReceiver,
  InstantiateExpr,
  Interpolation$1 as Interpolation,
  InterpolationConfig,
  InvokeFunctionExpr,
  JSDocComment,
  JitEvaluator,
  KeyedRead,
  LeadingComment,
  LetDeclaration,
  Lexer,
  LiteralArray,
  LiteralArrayExpr,
  LiteralExpr,
  LiteralMap,
  LiteralMapExpr,
  LiteralPrimitive,
  LocalizedString,
  MapType,
  MessageBundle,
  NONE_TYPE,
  NO_ERRORS_SCHEMA,
  NodeWithI18n,
  NonNullAssert,
  NotExpr,
  ParenthesizedExpr,
  ParenthesizedExpression,
  ParseError,
  ParseErrorLevel,
  ParseLocation,
  ParseSourceFile,
  ParseSourceSpan,
  ParseSpan,
  ParseTreeResult,
  ParsedEvent,
  ParsedEventType,
  ParsedProperty,
  ParsedPropertyType,
  ParsedVariable,
  Parser,
  PrefixNot,
  PropertyRead,
  Identifiers as R3Identifiers,
  R3NgModuleMetadataKind,
  R3SelectorScopeMode,
  R3TargetBinder,
  R3TemplateDependencyKind,
  ReadKeyExpr,
  ReadPropExpr,
  ReadVarExpr,
  RecursiveAstVisitor,
  RecursiveVisitor,
  ResourceLoader,
  ReturnStatement,
  SECURITY_SCHEMA,
  STRING_TYPE,
  SafeCall,
  SafeKeyedRead,
  SafePropertyRead,
  SelectorContext,
  SelectorListContext,
  SelectorMatcher,
  SelectorlessMatcher,
  Serializer,
  SplitInterpolation,
  Statement,
  StmtModifier,
  StringToken,
  StringTokenKind,
  TagContentType,
  TaggedTemplateLiteral,
  TaggedTemplateLiteralExpr,
  TemplateBindingParseResult,
  TemplateLiteral,
  TemplateLiteralElement,
  TemplateLiteralElementExpr,
  TemplateLiteralExpr,
  Text,
  ThisReceiver,
  BlockNode as TmplAstBlockNode,
  BoundAttribute as TmplAstBoundAttribute,
  BoundDeferredTrigger as TmplAstBoundDeferredTrigger,
  BoundEvent as TmplAstBoundEvent,
  BoundText as TmplAstBoundText,
  Component$1 as TmplAstComponent,
  Content as TmplAstContent,
  DeferredBlock as TmplAstDeferredBlock,
  DeferredBlockError as TmplAstDeferredBlockError,
  DeferredBlockLoading as TmplAstDeferredBlockLoading,
  DeferredBlockPlaceholder as TmplAstDeferredBlockPlaceholder,
  DeferredTrigger as TmplAstDeferredTrigger,
  Directive$1 as TmplAstDirective,
  Element$1 as TmplAstElement,
  ForLoopBlock as TmplAstForLoopBlock,
  ForLoopBlockEmpty as TmplAstForLoopBlockEmpty,
  HostElement as TmplAstHostElement,
  HoverDeferredTrigger as TmplAstHoverDeferredTrigger,
  Icu$1 as TmplAstIcu,
  IdleDeferredTrigger as TmplAstIdleDeferredTrigger,
  IfBlock as TmplAstIfBlock,
  IfBlockBranch as TmplAstIfBlockBranch,
  ImmediateDeferredTrigger as TmplAstImmediateDeferredTrigger,
  InteractionDeferredTrigger as TmplAstInteractionDeferredTrigger,
  LetDeclaration$1 as TmplAstLetDeclaration,
  NeverDeferredTrigger as TmplAstNeverDeferredTrigger,
  RecursiveVisitor$1 as TmplAstRecursiveVisitor,
  Reference as TmplAstReference,
  SwitchBlock as TmplAstSwitchBlock,
  SwitchBlockCase as TmplAstSwitchBlockCase,
  Template as TmplAstTemplate,
  Text$3 as TmplAstText,
  TextAttribute as TmplAstTextAttribute,
  TimerDeferredTrigger as TmplAstTimerDeferredTrigger,
  UnknownBlock as TmplAstUnknownBlock,
  Variable as TmplAstVariable,
  ViewportDeferredTrigger as TmplAstViewportDeferredTrigger,
  Token,
  TokenType,
  TransplantedType,
  TreeError,
  Type,
  TypeModifier,
  TypeofExpr,
  TypeofExpression,
  Unary,
  UnaryOperator,
  UnaryOperatorExpr,
  VERSION,
  VariableBinding,
  Version,
  ViewEncapsulation$1 as ViewEncapsulation,
  VoidExpr,
  VoidExpression,
  WrappedNodeExpr,
  Xliff,
  Xliff2,
  Xmb,
  XmlParser,
  Xtb,
  compileClassDebugInfo,
  compileClassMetadata,
  compileComponentClassMetadata,
  compileComponentDeclareClassMetadata,
  compileComponentFromMetadata,
  compileDeclareClassMetadata,
  compileDeclareComponentFromMetadata,
  compileDeclareDirectiveFromMetadata,
  compileDeclareFactoryFunction,
  compileDeclareInjectableFromMetadata,
  compileDeclareInjectorFromMetadata,
  compileDeclareNgModuleFromMetadata,
  compileDeclarePipeFromMetadata,
  compileDeferResolverFunction,
  compileDirectiveFromMetadata,
  compileFactoryFunction,
  compileHmrInitializer,
  compileHmrUpdateCallback,
  compileInjectable,
  compileInjector,
  compileNgModule,
  compileOpaqueAsyncClassMetadata,
  compilePipeFromMetadata,
  computeMsgId,
  core,
  createCssSelectorFromNode,
  createInjectableType,
  createMayBeForwardRefExpression,
  devOnlyGuardedExpression,
  emitDistinctChangesOnlyDefaultValue,
  encapsulateStyle,
  escapeRegExp,
  findMatchingDirectivesAndPipes,
  getHtmlTagDefinition,
  getNsPrefix,
  getSafePropertyAccessString,
  identifierName,
  isNgContainer,
  isNgContent,
  isNgTemplate,
  jsDocComment,
  leadingComment,
  literal,
  literalMap,
  makeBindingParser,
  mergeNsAndName,
  output_ast as outputAst,
  parseHostBindings,
  parseTemplate,
  preserveWhitespacesDefault,
  publishFacade,
  r3JitTypeSourceSpan,
  sanitizeIdentifier,
  setEnableTemplateSourceLocations,
  splitNsName,
  visitAll$1 as tmplAstVisitAll,
  verifyHostBindings,
  visitAll
};

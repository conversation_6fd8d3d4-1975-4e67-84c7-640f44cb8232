import {
  AbstractControl,
  AbstractControlDirective,
  AbstractFormGroupDirective,
  COMPOSITION_BUFFER_MODE,
  CheckboxControlValueAccessor,
  CheckboxRequiredValidator,
  ControlContainer,
  ControlEvent,
  DefaultValueAccessor,
  EmailValidator,
  FormArray,
  FormArrayName,
  FormBuilder,
  FormControl,
  FormControlDirective,
  FormControlName,
  FormGroup,
  FormGroupDirective,
  FormGroupName,
  FormRecord,
  FormResetEvent,
  FormSubmittedEvent,
  FormsModule,
  MaxLengthValidator,
  MaxValidator,
  MinLengthValidator,
  MinValidator,
  NG_ASYNC_VALIDATORS,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  NgControl,
  NgControlStatus,
  NgControlStatusGroup,
  NgForm,
  NgModel,
  NgModelGroup,
  NgSelectOption,
  NonNullableFormBuilder,
  NumberValueAccessor,
  PatternValidator,
  PristineChangeEvent,
  RadioControlValueAccessor,
  RangeValueAccessor,
  ReactiveFormsModule,
  RequiredValidator,
  SelectControlValueAccessor,
  SelectMultipleControlValueAccessor,
  StatusChangeEvent,
  TouchedChangeEvent,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  VERSION,
  Validators,
  ValueChangeEvent,
  init_forms,
  isFormArray,
  isFormControl,
  isFormGroup,
  isFormRecord,
  ɵInternalFormsSharedModule,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-L7SPBUC7.js";
import "./chunk-TEPKXNLU.js";
import "./chunk-4RF3VZXG.js";
import "./chunk-WUNHS5KN.js";
import "./chunk-N726T63C.js";
import "./chunk-H5IUTQT7.js";
import "./chunk-CTL5GG7J.js";
import "./chunk-WOR4A3D2.js";
init_forms();
export {
  AbstractControl,
  AbstractControlDirective,
  AbstractFormGroupDirective,
  COMPOSITION_BUFFER_MODE,
  CheckboxControlValueAccessor,
  CheckboxRequiredValidator,
  ControlContainer,
  ControlEvent,
  DefaultValueAccessor,
  EmailValidator,
  FormArray,
  FormArrayName,
  FormBuilder,
  FormControl,
  FormControlDirective,
  FormControlName,
  FormGroup,
  FormGroupDirective,
  FormGroupName,
  FormRecord,
  FormResetEvent,
  FormSubmittedEvent,
  FormsModule,
  MaxLengthValidator,
  MaxValidator,
  MinLengthValidator,
  MinValidator,
  NG_ASYNC_VALIDATORS,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  NgControl,
  NgControlStatus,
  NgControlStatusGroup,
  NgForm,
  NgModel,
  NgModelGroup,
  NgSelectOption,
  NonNullableFormBuilder,
  NumberValueAccessor,
  PatternValidator,
  PristineChangeEvent,
  RadioControlValueAccessor,
  RangeValueAccessor,
  ReactiveFormsModule,
  RequiredValidator,
  SelectControlValueAccessor,
  SelectMultipleControlValueAccessor,
  StatusChangeEvent,
  TouchedChangeEvent,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  VERSION,
  Validators,
  ValueChangeEvent,
  isFormArray,
  isFormControl,
  isFormGroup,
  isFormRecord,
  ɵInternalFormsSharedModule,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
};

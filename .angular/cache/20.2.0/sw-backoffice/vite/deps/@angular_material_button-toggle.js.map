{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/button-toggle.mjs"], "sourcesContent": ["import { _Id<PERSON>enerator, FocusMonitor } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { hasModifierKey, RIGHT_ARROW, DOWN_ARROW, LEFT_ARROW, UP_ARROW, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, inject, ChangeDetectorRef, EventEmitter, booleanAttribute, Directive, ContentChildren, Input, Output, ElementRef, HostAttributeToken, signal, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { MatRipple } from './ripple.mjs';\nimport { MatPseudoCheckbox } from './pseudo-checkbox.mjs';\nimport { _animationsDisabled } from './animation.mjs';\nimport { _StructuralStylesLoader } from './structural-styles.mjs';\nimport { MatCommonModule } from './common-module.mjs';\nimport { MatRippleModule } from './ripple-module.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/layout';\n\n/**\n * Injection token that can be used to configure the\n * default options for all button toggles within an app.\n */\nconst _c0 = [\"button\"];\nconst _c1 = [\"*\"];\nfunction MatButtonToggle_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵelement(1, \"mat-pseudo-checkbox\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n  }\n}\nconst MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS = new InjectionToken('MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS', {\n  providedIn: 'root',\n  factory: MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    hideSingleSelectionIndicator: false,\n    hideMultipleSelectionIndicator: false,\n    disabledInteractive: false\n  };\n}\n/**\n * Injection token that can be used to reference instances of `MatButtonToggleGroup`.\n * It serves as alternative token to the actual `MatButtonToggleGroup` class which\n * could cause unnecessary retention of the class and its component metadata.\n */\nconst MAT_BUTTON_TOGGLE_GROUP = new InjectionToken('MatButtonToggleGroup');\n/**\n * Provider Expression that allows mat-button-toggle-group to register as a ControlValueAccessor.\n * This allows it to support [(ngModel)].\n * @docs-private\n */\nconst MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatButtonToggleGroup),\n  multi: true\n};\n/** Change event object emitted by button toggle. */\nclass MatButtonToggleChange {\n  source;\n  value;\n  constructor(/** The button toggle that emits the event. */\n  source, /** The value assigned to the button toggle. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/** Exclusive selection button toggle group that behaves like a radio-button group. */\nclass MatButtonToggleGroup {\n  _changeDetector = inject(ChangeDetectorRef);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _multiple = false;\n  _disabled = false;\n  _disabledInteractive = false;\n  _selectionModel;\n  /**\n   * Reference to the raw value that the consumer tried to assign. The real\n   * value will exclude any values from this one that don't correspond to a\n   * toggle. Useful for the cases where the value is assigned before the toggles\n   * have been initialized or at the same that they're being swapped out.\n   */\n  _rawValue;\n  /**\n   * The method to be called in order to update ngModel.\n   * Now `ngModel` binding is not supported in multiple selection mode.\n   */\n  _controlValueAccessorChangeFn = () => {};\n  /** onTouch function registered via registerOnTouch (ControlValueAccessor). */\n  _onTouched = () => {};\n  /** Child button toggle buttons. */\n  _buttonToggles;\n  /** The appearance for all the buttons in the group. */\n  appearance;\n  /** `name` attribute for the underlying `input` element. */\n  get name() {\n    return this._name;\n  }\n  set name(value) {\n    this._name = value;\n    this._markButtonsForCheck();\n  }\n  _name = inject(_IdGenerator).getId('mat-button-toggle-group-');\n  /** Whether the toggle group is vertical. */\n  vertical;\n  /** Value of the toggle group. */\n  get value() {\n    const selected = this._selectionModel ? this._selectionModel.selected : [];\n    if (this.multiple) {\n      return selected.map(toggle => toggle.value);\n    }\n    return selected[0] ? selected[0].value : undefined;\n  }\n  set value(newValue) {\n    this._setSelectionByValue(newValue);\n    this.valueChange.emit(this.value);\n  }\n  /**\n   * Event that emits whenever the value of the group changes.\n   * Used to facilitate two-way data binding.\n   * @docs-private\n   */\n  valueChange = new EventEmitter();\n  /** Selected button toggles in the group. */\n  get selected() {\n    const selected = this._selectionModel ? this._selectionModel.selected : [];\n    return this.multiple ? selected : selected[0] || null;\n  }\n  /** Whether multiple button toggles can be selected. */\n  get multiple() {\n    return this._multiple;\n  }\n  set multiple(value) {\n    this._multiple = value;\n    this._markButtonsForCheck();\n  }\n  /** Whether multiple button toggle group is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._markButtonsForCheck();\n  }\n  /** Whether buttons in the group should be interactive while they're disabled. */\n  get disabledInteractive() {\n    return this._disabledInteractive;\n  }\n  set disabledInteractive(value) {\n    this._disabledInteractive = value;\n    this._markButtonsForCheck();\n  }\n  /** The layout direction of the toggle button group. */\n  get dir() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Event emitted when the group's value changes. */\n  change = new EventEmitter();\n  /** Whether checkmark indicator for single-selection button toggle groups is hidden. */\n  get hideSingleSelectionIndicator() {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value) {\n    this._hideSingleSelectionIndicator = value;\n    this._markButtonsForCheck();\n  }\n  _hideSingleSelectionIndicator;\n  /** Whether checkmark indicator for multiple-selection button toggle groups is hidden. */\n  get hideMultipleSelectionIndicator() {\n    return this._hideMultipleSelectionIndicator;\n  }\n  set hideMultipleSelectionIndicator(value) {\n    this._hideMultipleSelectionIndicator = value;\n    this._markButtonsForCheck();\n  }\n  _hideMultipleSelectionIndicator;\n  constructor() {\n    const defaultOptions = inject(MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    this.appearance = defaultOptions && defaultOptions.appearance ? defaultOptions.appearance : 'standard';\n    this.hideSingleSelectionIndicator = defaultOptions?.hideSingleSelectionIndicator ?? false;\n    this.hideMultipleSelectionIndicator = defaultOptions?.hideMultipleSelectionIndicator ?? false;\n  }\n  ngOnInit() {\n    this._selectionModel = new SelectionModel(this.multiple, undefined, false);\n  }\n  ngAfterContentInit() {\n    this._selectionModel.select(...this._buttonToggles.filter(toggle => toggle.checked));\n    if (!this.multiple) {\n      this._initializeTabIndex();\n    }\n  }\n  /**\n   * Sets the model value. Implemented as part of ControlValueAccessor.\n   * @param value Value to be set to the model.\n   */\n  writeValue(value) {\n    this.value = value;\n    this._changeDetector.markForCheck();\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  /** Handle keydown event calling to single-select button toggle. */\n  _keydown(event) {\n    if (this.multiple || this.disabled || hasModifierKey(event)) {\n      return;\n    }\n    const target = event.target;\n    const buttonId = target.id;\n    const index = this._buttonToggles.toArray().findIndex(toggle => {\n      return toggle.buttonId === buttonId;\n    });\n    let nextButton = null;\n    switch (event.keyCode) {\n      case SPACE:\n      case ENTER:\n        nextButton = this._buttonToggles.get(index) || null;\n        break;\n      case UP_ARROW:\n        nextButton = this._getNextButton(index, -1);\n        break;\n      case LEFT_ARROW:\n        nextButton = this._getNextButton(index, this.dir === 'ltr' ? -1 : 1);\n        break;\n      case DOWN_ARROW:\n        nextButton = this._getNextButton(index, 1);\n        break;\n      case RIGHT_ARROW:\n        nextButton = this._getNextButton(index, this.dir === 'ltr' ? 1 : -1);\n        break;\n      default:\n        return;\n    }\n    if (nextButton) {\n      event.preventDefault();\n      nextButton._onButtonClick();\n      nextButton.focus();\n    }\n  }\n  /** Dispatch change event with current selection and group value. */\n  _emitChangeEvent(toggle) {\n    const event = new MatButtonToggleChange(toggle, this.value);\n    this._rawValue = event.value;\n    this._controlValueAccessorChangeFn(event.value);\n    this.change.emit(event);\n  }\n  /**\n   * Syncs a button toggle's selected state with the model value.\n   * @param toggle Toggle to be synced.\n   * @param select Whether the toggle should be selected.\n   * @param isUserInput Whether the change was a result of a user interaction.\n   * @param deferEvents Whether to defer emitting the change events.\n   */\n  _syncButtonToggle(toggle, select, isUserInput = false, deferEvents = false) {\n    // Deselect the currently-selected toggle, if we're in single-selection\n    // mode and the button being toggled isn't selected at the moment.\n    if (!this.multiple && this.selected && !toggle.checked) {\n      this.selected.checked = false;\n    }\n    if (this._selectionModel) {\n      if (select) {\n        this._selectionModel.select(toggle);\n      } else {\n        this._selectionModel.deselect(toggle);\n      }\n    } else {\n      deferEvents = true;\n    }\n    // We need to defer in some cases in order to avoid \"changed after checked errors\", however\n    // the side-effect is that we may end up updating the model value out of sequence in others\n    // The `deferEvents` flag allows us to decide whether to do it on a case-by-case basis.\n    if (deferEvents) {\n      Promise.resolve().then(() => this._updateModelValue(toggle, isUserInput));\n    } else {\n      this._updateModelValue(toggle, isUserInput);\n    }\n  }\n  /** Checks whether a button toggle is selected. */\n  _isSelected(toggle) {\n    return this._selectionModel && this._selectionModel.isSelected(toggle);\n  }\n  /** Determines whether a button toggle should be checked on init. */\n  _isPrechecked(toggle) {\n    if (typeof this._rawValue === 'undefined') {\n      return false;\n    }\n    if (this.multiple && Array.isArray(this._rawValue)) {\n      return this._rawValue.some(value => toggle.value != null && value === toggle.value);\n    }\n    return toggle.value === this._rawValue;\n  }\n  /** Initializes the tabindex attribute using the radio pattern. */\n  _initializeTabIndex() {\n    this._buttonToggles.forEach(toggle => {\n      toggle.tabIndex = -1;\n    });\n    if (this.selected) {\n      this.selected.tabIndex = 0;\n    } else {\n      for (let i = 0; i < this._buttonToggles.length; i++) {\n        const toggle = this._buttonToggles.get(i);\n        if (!toggle.disabled) {\n          toggle.tabIndex = 0;\n          break;\n        }\n      }\n    }\n  }\n  /** Obtain the subsequent toggle to which the focus shifts. */\n  _getNextButton(startIndex, offset) {\n    const items = this._buttonToggles;\n    for (let i = 1; i <= items.length; i++) {\n      const index = (startIndex + offset * i + items.length) % items.length;\n      const item = items.get(index);\n      if (item && !item.disabled) {\n        return item;\n      }\n    }\n    return null;\n  }\n  /** Updates the selection state of the toggles in the group based on a value. */\n  _setSelectionByValue(value) {\n    this._rawValue = value;\n    if (!this._buttonToggles) {\n      return;\n    }\n    const toggles = this._buttonToggles.toArray();\n    if (this.multiple && value) {\n      if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Value must be an array in multiple-selection mode.');\n      }\n      this._clearSelection();\n      value.forEach(currentValue => this._selectValue(currentValue, toggles));\n    } else {\n      this._clearSelection();\n      this._selectValue(value, toggles);\n    }\n    // In single selection mode we need at least one enabled toggle to always be focusable.\n    if (!this.multiple && toggles.every(toggle => toggle.tabIndex === -1)) {\n      for (const toggle of toggles) {\n        if (!toggle.disabled) {\n          toggle.tabIndex = 0;\n          break;\n        }\n      }\n    }\n  }\n  /** Clears the selected toggles. */\n  _clearSelection() {\n    this._selectionModel.clear();\n    this._buttonToggles.forEach(toggle => {\n      toggle.checked = false;\n      // If the button toggle is in single select mode, initialize the tabIndex.\n      if (!this.multiple) {\n        toggle.tabIndex = -1;\n      }\n    });\n  }\n  /** Selects a value if there's a toggle that corresponds to it. */\n  _selectValue(value, toggles) {\n    for (const toggle of toggles) {\n      if (toggle.value === value) {\n        toggle.checked = true;\n        this._selectionModel.select(toggle);\n        if (!this.multiple) {\n          // If the button toggle is in single select mode, reset the tabIndex.\n          toggle.tabIndex = 0;\n        }\n        break;\n      }\n    }\n  }\n  /** Syncs up the group's value with the model and emits the change event. */\n  _updateModelValue(toggle, isUserInput) {\n    // Only emit the change event for user input.\n    if (isUserInput) {\n      this._emitChangeEvent(toggle);\n    }\n    // Note: we emit this one no matter whether it was a user interaction, because\n    // it is used by Angular to sync up the two-way data binding.\n    this.valueChange.emit(this.value);\n  }\n  /** Marks all of the child button toggles to be checked. */\n  _markButtonsForCheck() {\n    this._buttonToggles?.forEach(toggle => toggle._markForCheck());\n  }\n  static ɵfac = function MatButtonToggleGroup_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatButtonToggleGroup)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatButtonToggleGroup,\n    selectors: [[\"mat-button-toggle-group\"]],\n    contentQueries: function MatButtonToggleGroup_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatButtonToggle, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._buttonToggles = _t);\n      }\n    },\n    hostAttrs: [1, \"mat-button-toggle-group\"],\n    hostVars: 6,\n    hostBindings: function MatButtonToggleGroup_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown\", function MatButtonToggleGroup_keydown_HostBindingHandler($event) {\n          return ctx._keydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"role\", ctx.multiple ? \"group\" : \"radiogroup\")(\"aria-disabled\", ctx.disabled);\n        i0.ɵɵclassProp(\"mat-button-toggle-vertical\", ctx.vertical)(\"mat-button-toggle-group-appearance-standard\", ctx.appearance === \"standard\");\n      }\n    },\n    inputs: {\n      appearance: \"appearance\",\n      name: \"name\",\n      vertical: [2, \"vertical\", \"vertical\", booleanAttribute],\n      value: \"value\",\n      multiple: [2, \"multiple\", \"multiple\", booleanAttribute],\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute],\n      hideSingleSelectionIndicator: [2, \"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute],\n      hideMultipleSelectionIndicator: [2, \"hideMultipleSelectionIndicator\", \"hideMultipleSelectionIndicator\", booleanAttribute]\n    },\n    outputs: {\n      valueChange: \"valueChange\",\n      change: \"change\"\n    },\n    exportAs: [\"matButtonToggleGroup\"],\n    features: [i0.ɵɵProvidersFeature([MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR, {\n      provide: MAT_BUTTON_TOGGLE_GROUP,\n      useExisting: MatButtonToggleGroup\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatButtonToggleGroup, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-button-toggle-group',\n      providers: [MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR, {\n        provide: MAT_BUTTON_TOGGLE_GROUP,\n        useExisting: MatButtonToggleGroup\n      }],\n      host: {\n        'class': 'mat-button-toggle-group',\n        '(keydown)': '_keydown($event)',\n        '[attr.role]': \"multiple ? 'group' : 'radiogroup'\",\n        '[attr.aria-disabled]': 'disabled',\n        '[class.mat-button-toggle-vertical]': 'vertical',\n        '[class.mat-button-toggle-group-appearance-standard]': 'appearance === \"standard\"'\n      },\n      exportAs: 'matButtonToggleGroup'\n    }]\n  }], () => [], {\n    _buttonToggles: [{\n      type: ContentChildren,\n      args: [forwardRef(() => MatButtonToggle), {\n        // Note that this would technically pick up toggles\n        // from nested groups, but that's not a case that we support.\n        descendants: true\n      }]\n    }],\n    appearance: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    vertical: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    value: [{\n      type: Input\n    }],\n    valueChange: [{\n      type: Output\n    }],\n    multiple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    change: [{\n      type: Output\n    }],\n    hideSingleSelectionIndicator: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    hideMultipleSelectionIndicator: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/** Single button inside of a toggle group. */\nclass MatButtonToggle {\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _elementRef = inject(ElementRef);\n  _focusMonitor = inject(FocusMonitor);\n  _idGenerator = inject(_IdGenerator);\n  _animationDisabled = _animationsDisabled();\n  _checked = false;\n  /**\n   * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n   * take precedence so this may be omitted.\n   */\n  ariaLabel;\n  /**\n   * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n   */\n  ariaLabelledby = null;\n  /** Underlying native `button` element. */\n  _buttonElement;\n  /** The parent button toggle group (exclusive selection). Optional. */\n  buttonToggleGroup;\n  /** Unique ID for the underlying `button` element. */\n  get buttonId() {\n    return `${this.id}-button`;\n  }\n  /** The unique ID for this button toggle. */\n  id;\n  /** HTML's 'name' attribute used to group radios for unique selection. */\n  name;\n  /** MatButtonToggleGroup reads this to assign its own value. */\n  value;\n  /** Tabindex of the toggle. */\n  get tabIndex() {\n    return this._tabIndex();\n  }\n  set tabIndex(value) {\n    this._tabIndex.set(value);\n  }\n  _tabIndex;\n  /** Whether ripples are disabled on the button toggle. */\n  disableRipple;\n  /** The appearance style of the button. */\n  get appearance() {\n    return this.buttonToggleGroup ? this.buttonToggleGroup.appearance : this._appearance;\n  }\n  set appearance(value) {\n    this._appearance = value;\n  }\n  _appearance;\n  /** Whether the button is checked. */\n  get checked() {\n    return this.buttonToggleGroup ? this.buttonToggleGroup._isSelected(this) : this._checked;\n  }\n  set checked(value) {\n    if (value !== this._checked) {\n      this._checked = value;\n      if (this.buttonToggleGroup) {\n        this.buttonToggleGroup._syncButtonToggle(this, this._checked);\n      }\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Whether the button is disabled. */\n  get disabled() {\n    return this._disabled || this.buttonToggleGroup && this.buttonToggleGroup.disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n  }\n  _disabled = false;\n  /** Whether the button should remain interactive when it is disabled. */\n  get disabledInteractive() {\n    return this._disabledInteractive || this.buttonToggleGroup !== null && this.buttonToggleGroup.disabledInteractive;\n  }\n  set disabledInteractive(value) {\n    this._disabledInteractive = value;\n  }\n  _disabledInteractive;\n  /** Event emitted when the group value changes. */\n  change = new EventEmitter();\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const toggleGroup = inject(MAT_BUTTON_TOGGLE_GROUP, {\n      optional: true\n    });\n    const defaultTabIndex = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    }) || '';\n    const defaultOptions = inject(MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    this._tabIndex = signal(parseInt(defaultTabIndex) || 0, ...(ngDevMode ? [{\n      debugName: \"_tabIndex\"\n    }] : []));\n    this.buttonToggleGroup = toggleGroup;\n    this.appearance = defaultOptions && defaultOptions.appearance ? defaultOptions.appearance : 'standard';\n    this.disabledInteractive = defaultOptions?.disabledInteractive ?? false;\n  }\n  ngOnInit() {\n    const group = this.buttonToggleGroup;\n    this.id = this.id || this._idGenerator.getId('mat-button-toggle-');\n    if (group) {\n      if (group._isPrechecked(this)) {\n        this.checked = true;\n      } else if (group._isSelected(this) !== this._checked) {\n        // As side effect of the circular dependency between the toggle group and the button,\n        // we may end up in a state where the button is supposed to be checked on init, but it\n        // isn't, because the checked value was assigned too early. This can happen when Ivy\n        // assigns the static input value before the `ngOnInit` has run.\n        group._syncButtonToggle(this, this._checked);\n      }\n    }\n  }\n  ngAfterViewInit() {\n    // This serves two purposes:\n    // 1. We don't want the animation to fire on the first render for pre-checked toggles so we\n    //    delay adding the class until the view is rendered.\n    // 2. We don't want animation if the `NoopAnimationsModule` is provided.\n    if (!this._animationDisabled) {\n      this._elementRef.nativeElement.classList.add('mat-button-toggle-animations-enabled');\n    }\n    this._focusMonitor.monitor(this._elementRef, true);\n  }\n  ngOnDestroy() {\n    const group = this.buttonToggleGroup;\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    // Remove the toggle from the selection once it's destroyed. Needs to happen\n    // on the next tick in order to avoid \"changed after checked\" errors.\n    if (group && group._isSelected(this)) {\n      group._syncButtonToggle(this, false, false, true);\n    }\n  }\n  /** Focuses the button. */\n  focus(options) {\n    this._buttonElement.nativeElement.focus(options);\n  }\n  /** Checks the button toggle due to an interaction with the underlying native button. */\n  _onButtonClick() {\n    if (this.disabled) {\n      return;\n    }\n    const newChecked = this.isSingleSelector() ? true : !this._checked;\n    if (newChecked !== this._checked) {\n      this._checked = newChecked;\n      if (this.buttonToggleGroup) {\n        this.buttonToggleGroup._syncButtonToggle(this, this._checked, true);\n        this.buttonToggleGroup._onTouched();\n      }\n    }\n    if (this.isSingleSelector()) {\n      const focusable = this.buttonToggleGroup._buttonToggles.find(toggle => {\n        return toggle.tabIndex === 0;\n      });\n      // Modify the tabindex attribute of the last focusable button toggle to -1.\n      if (focusable) {\n        focusable.tabIndex = -1;\n      }\n      // Modify the tabindex attribute of the presently selected button toggle to 0.\n      this.tabIndex = 0;\n    }\n    // Emit a change event when it's the single selector\n    this.change.emit(new MatButtonToggleChange(this, this.value));\n  }\n  /**\n   * Marks the button toggle as needing checking for change detection.\n   * This method is exposed because the parent button toggle group will directly\n   * update bound properties of the radio button.\n   */\n  _markForCheck() {\n    // When the group value changes, the button will not be notified.\n    // Use `markForCheck` to explicit update button toggle's status.\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Gets the name that should be assigned to the inner DOM node. */\n  _getButtonName() {\n    if (this.isSingleSelector()) {\n      return this.buttonToggleGroup.name;\n    }\n    return this.name || null;\n  }\n  /** Whether the toggle is in single selection mode. */\n  isSingleSelector() {\n    return this.buttonToggleGroup && !this.buttonToggleGroup.multiple;\n  }\n  static ɵfac = function MatButtonToggle_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatButtonToggle)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatButtonToggle,\n    selectors: [[\"mat-button-toggle\"]],\n    viewQuery: function MatButtonToggle_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._buttonElement = _t.first);\n      }\n    },\n    hostAttrs: [\"role\", \"presentation\", 1, \"mat-button-toggle\"],\n    hostVars: 14,\n    hostBindings: function MatButtonToggle_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"focus\", function MatButtonToggle_focus_HostBindingHandler() {\n          return ctx.focus();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-label\", null)(\"aria-labelledby\", null)(\"id\", ctx.id)(\"name\", null);\n        i0.ɵɵclassProp(\"mat-button-toggle-standalone\", !ctx.buttonToggleGroup)(\"mat-button-toggle-checked\", ctx.checked)(\"mat-button-toggle-disabled\", ctx.disabled)(\"mat-button-toggle-disabled-interactive\", ctx.disabledInteractive)(\"mat-button-toggle-appearance-standard\", ctx.appearance === \"standard\");\n      }\n    },\n    inputs: {\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n      id: \"id\",\n      name: \"name\",\n      value: \"value\",\n      tabIndex: \"tabIndex\",\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      appearance: \"appearance\",\n      checked: [2, \"checked\", \"checked\", booleanAttribute],\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute]\n    },\n    outputs: {\n      change: \"change\"\n    },\n    exportAs: [\"matButtonToggle\"],\n    ngContentSelectors: _c1,\n    decls: 7,\n    vars: 13,\n    consts: [[\"button\", \"\"], [\"type\", \"button\", 1, \"mat-button-toggle-button\", \"mat-focus-indicator\", 3, \"click\", \"id\", \"disabled\"], [1, \"mat-button-toggle-checkbox-wrapper\"], [1, \"mat-button-toggle-label-content\"], [1, \"mat-button-toggle-focus-overlay\"], [\"matRipple\", \"\", 1, \"mat-button-toggle-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [\"state\", \"checked\", \"aria-hidden\", \"true\", \"appearance\", \"minimal\", 3, \"disabled\"]],\n    template: function MatButtonToggle_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"button\", 1, 0);\n        i0.ɵɵlistener(\"click\", function MatButtonToggle_Template_button_click_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onButtonClick());\n        });\n        i0.ɵɵconditionalCreate(2, MatButtonToggle_Conditional_2_Template, 2, 1, \"div\", 2);\n        i0.ɵɵelementStart(3, \"span\", 3);\n        i0.ɵɵprojection(4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(5, \"span\", 4)(6, \"span\", 5);\n      }\n      if (rf & 2) {\n        const button_r3 = i0.ɵɵreference(1);\n        i0.ɵɵproperty(\"id\", ctx.buttonId)(\"disabled\", ctx.disabled && !ctx.disabledInteractive || null);\n        i0.ɵɵattribute(\"role\", ctx.isSingleSelector() ? \"radio\" : \"button\")(\"tabindex\", ctx.disabled && !ctx.disabledInteractive ? -1 : ctx.tabIndex)(\"aria-pressed\", !ctx.isSingleSelector() ? ctx.checked : null)(\"aria-checked\", ctx.isSingleSelector() ? ctx.checked : null)(\"name\", ctx._getButtonName())(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-disabled\", ctx.disabled && ctx.disabledInteractive ? \"true\" : null);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx.buttonToggleGroup && (!ctx.buttonToggleGroup.multiple && !ctx.buttonToggleGroup.hideSingleSelectionIndicator || ctx.buttonToggleGroup.multiple && !ctx.buttonToggleGroup.hideMultipleSelectionIndicator) ? 2 : -1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"matRippleTrigger\", button_r3)(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled);\n      }\n    },\n    dependencies: [MatRipple, MatPseudoCheckbox],\n    styles: [\".mat-button-toggle-standalone,.mat-button-toggle-group{position:relative;display:inline-flex;flex-direction:row;white-space:nowrap;overflow:hidden;-webkit-tap-highlight-color:rgba(0,0,0,0);border-radius:var(--mat-button-toggle-legacy-shape);transform:translateZ(0)}.mat-button-toggle-standalone:not([class*=mat-elevation-z]),.mat-button-toggle-group:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}@media(forced-colors: active){.mat-button-toggle-standalone,.mat-button-toggle-group{outline:solid 1px}}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.mat-button-toggle-group-appearance-standard{border-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large));border:solid 1px var(--mat-button-toggle-divider-color, var(--mat-sys-outline))}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard .mat-pseudo-checkbox,.mat-button-toggle-group-appearance-standard .mat-pseudo-checkbox{--mat-pseudo-checkbox-minimal-selected-checkmark-color: var(--mat-button-toggle-selected-state-text-color, var(--mat-sys-on-secondary-container))}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard:not([class*=mat-elevation-z]),.mat-button-toggle-group-appearance-standard:not([class*=mat-elevation-z]){box-shadow:none}@media(forced-colors: active){.mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.mat-button-toggle-group-appearance-standard{outline:0}}.mat-button-toggle-vertical{flex-direction:column}.mat-button-toggle-vertical .mat-button-toggle-label-content{display:block}.mat-button-toggle{white-space:nowrap;position:relative;color:var(--mat-button-toggle-legacy-text-color);font-family:var(--mat-button-toggle-legacy-label-text-font);font-size:var(--mat-button-toggle-legacy-label-text-size);line-height:var(--mat-button-toggle-legacy-label-text-line-height);font-weight:var(--mat-button-toggle-legacy-label-text-weight);letter-spacing:var(--mat-button-toggle-legacy-label-text-tracking);--mat-pseudo-checkbox-minimal-selected-checkmark-color: var(--mat-button-toggle-legacy-selected-state-text-color)}.mat-button-toggle.cdk-keyboard-focused .mat-button-toggle-focus-overlay{opacity:var(--mat-button-toggle-legacy-focus-state-layer-opacity)}.mat-button-toggle .mat-icon svg{vertical-align:top}.mat-button-toggle-checkbox-wrapper{display:inline-block;justify-content:flex-start;align-items:center;width:0;height:18px;line-height:18px;overflow:hidden;box-sizing:border-box;position:absolute;top:50%;left:16px;transform:translate3d(0, -50%, 0)}[dir=rtl] .mat-button-toggle-checkbox-wrapper{left:auto;right:16px}.mat-button-toggle-appearance-standard .mat-button-toggle-checkbox-wrapper{left:12px}[dir=rtl] .mat-button-toggle-appearance-standard .mat-button-toggle-checkbox-wrapper{left:auto;right:12px}.mat-button-toggle-checked .mat-button-toggle-checkbox-wrapper{width:18px}.mat-button-toggle-animations-enabled .mat-button-toggle-checkbox-wrapper{transition:width 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-button-toggle-vertical .mat-button-toggle-checkbox-wrapper{transition:none}.mat-button-toggle-checked{color:var(--mat-button-toggle-legacy-selected-state-text-color);background-color:var(--mat-button-toggle-legacy-selected-state-background-color)}.mat-button-toggle-disabled{pointer-events:none;color:var(--mat-button-toggle-legacy-disabled-state-text-color);background-color:var(--mat-button-toggle-legacy-disabled-state-background-color);--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color: var(--mat-button-toggle-legacy-disabled-state-text-color)}.mat-button-toggle-disabled.mat-button-toggle-checked{background-color:var(--mat-button-toggle-legacy-disabled-selected-state-background-color)}.mat-button-toggle-disabled-interactive{pointer-events:auto}.mat-button-toggle-appearance-standard{color:var(--mat-button-toggle-text-color, var(--mat-sys-on-surface));background-color:var(--mat-button-toggle-background-color, transparent);font-family:var(--mat-button-toggle-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mat-button-toggle-label-text-size, var(--mat-sys-label-large-size));line-height:var(--mat-button-toggle-label-text-line-height, var(--mat-sys-label-large-line-height));font-weight:var(--mat-button-toggle-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mat-button-toggle-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:solid 1px var(--mat-button-toggle-divider-color, var(--mat-sys-outline))}[dir=rtl] .mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:solid 1px var(--mat-button-toggle-divider-color, var(--mat-sys-outline))}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:none;border-top:solid 1px var(--mat-button-toggle-divider-color, var(--mat-sys-outline))}.mat-button-toggle-appearance-standard.mat-button-toggle-checked{color:var(--mat-button-toggle-selected-state-text-color, var(--mat-sys-on-secondary-container));background-color:var(--mat-button-toggle-selected-state-background-color, var(--mat-sys-secondary-container))}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled{color:var(--mat-button-toggle-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-button-toggle-disabled-state-background-color, transparent)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled .mat-pseudo-checkbox{--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color: var(--mat-button-toggle-disabled-selected-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled.mat-button-toggle-checked{color:var(--mat-button-toggle-disabled-selected-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-button-toggle-disabled-selected-state-background-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{background-color:var(--mat-button-toggle-state-layer-color, var(--mat-sys-on-surface))}.mat-button-toggle-appearance-standard:hover .mat-button-toggle-focus-overlay{opacity:var(--mat-button-toggle-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-button-toggle-appearance-standard.cdk-keyboard-focused .mat-button-toggle-focus-overlay{opacity:var(--mat-button-toggle-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}@media(hover: none){.mat-button-toggle-appearance-standard:hover .mat-button-toggle-focus-overlay{display:none}}.mat-button-toggle-label-content{-webkit-user-select:none;user-select:none;display:inline-block;padding:0 16px;line-height:var(--mat-button-toggle-legacy-height);position:relative}.mat-button-toggle-appearance-standard .mat-button-toggle-label-content{padding:0 12px;line-height:var(--mat-button-toggle-height, 40px)}.mat-button-toggle-label-content>*{vertical-align:middle}.mat-button-toggle-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;pointer-events:none;opacity:0;background-color:var(--mat-button-toggle-legacy-state-layer-color)}@media(forced-colors: active){.mat-button-toggle-checked .mat-button-toggle-focus-overlay{border-bottom:solid 500px;opacity:.5;height:0}.mat-button-toggle-checked:hover .mat-button-toggle-focus-overlay{opacity:.6}.mat-button-toggle-checked.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{border-bottom:solid 500px}}.mat-button-toggle .mat-button-toggle-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-button-toggle-button{border:0;background:none;color:inherit;padding:0;margin:0;font:inherit;outline:none;width:100%;cursor:pointer}.mat-button-toggle-animations-enabled .mat-button-toggle-button{transition:padding 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-button-toggle-vertical .mat-button-toggle-button{transition:none}.mat-button-toggle-disabled .mat-button-toggle-button{cursor:default}.mat-button-toggle-button::-moz-focus-inner{border:0}.mat-button-toggle-checked .mat-button-toggle-button:has(.mat-button-toggle-checkbox-wrapper){padding-left:30px}[dir=rtl] .mat-button-toggle-checked .mat-button-toggle-button:has(.mat-button-toggle-checkbox-wrapper){padding-left:0;padding-right:30px}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard{--mat-focus-indicator-border-radius: var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large))}.mat-button-toggle-group-appearance-standard:not(.mat-button-toggle-vertical) .mat-button-toggle:last-of-type .mat-button-toggle-button::before{border-top-right-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large));border-bottom-right-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large))}.mat-button-toggle-group-appearance-standard:not(.mat-button-toggle-vertical) .mat-button-toggle:first-of-type .mat-button-toggle-button::before{border-top-left-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large));border-bottom-left-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large))}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical .mat-button-toggle:last-of-type .mat-button-toggle-button::before{border-bottom-right-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large));border-bottom-left-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large))}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical .mat-button-toggle:first-of-type .mat-button-toggle-button::before{border-top-right-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large));border-top-left-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large))}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatButtonToggle, [{\n    type: Component,\n    args: [{\n      selector: 'mat-button-toggle',\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matButtonToggle',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[class.mat-button-toggle-standalone]': '!buttonToggleGroup',\n        '[class.mat-button-toggle-checked]': 'checked',\n        '[class.mat-button-toggle-disabled]': 'disabled',\n        '[class.mat-button-toggle-disabled-interactive]': 'disabledInteractive',\n        '[class.mat-button-toggle-appearance-standard]': 'appearance === \"standard\"',\n        'class': 'mat-button-toggle',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[attr.id]': 'id',\n        '[attr.name]': 'null',\n        '(focus)': 'focus()',\n        'role': 'presentation'\n      },\n      imports: [MatRipple, MatPseudoCheckbox],\n      template: \"<button #button class=\\\"mat-button-toggle-button mat-focus-indicator\\\"\\n        type=\\\"button\\\"\\n        [id]=\\\"buttonId\\\"\\n        [attr.role]=\\\"isSingleSelector() ? 'radio' : 'button'\\\"\\n        [attr.tabindex]=\\\"disabled && !disabledInteractive ? -1 : tabIndex\\\"\\n        [attr.aria-pressed]=\\\"!isSingleSelector() ? checked : null\\\"\\n        [attr.aria-checked]=\\\"isSingleSelector() ? checked : null\\\"\\n        [disabled]=\\\"(disabled && !disabledInteractive) || null\\\"\\n        [attr.name]=\\\"_getButtonName()\\\"\\n        [attr.aria-label]=\\\"ariaLabel\\\"\\n        [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n        [attr.aria-disabled]=\\\"disabled && disabledInteractive ? 'true' : null\\\"\\n        (click)=\\\"_onButtonClick()\\\">\\n  @if (buttonToggleGroup && (\\n    !buttonToggleGroup.multiple && !buttonToggleGroup.hideSingleSelectionIndicator ||\\n    buttonToggleGroup.multiple && !buttonToggleGroup.hideMultipleSelectionIndicator)\\n  ) {\\n    <div class=\\\"mat-button-toggle-checkbox-wrapper\\\">\\n      <mat-pseudo-checkbox\\n        [disabled]=\\\"disabled\\\"\\n        state=\\\"checked\\\"\\n        aria-hidden=\\\"true\\\"\\n        appearance=\\\"minimal\\\"/>\\n    </div>\\n  }\\n\\n  <span class=\\\"mat-button-toggle-label-content\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</button>\\n\\n<span class=\\\"mat-button-toggle-focus-overlay\\\"></span>\\n<span class=\\\"mat-button-toggle-ripple\\\" matRipple\\n     [matRippleTrigger]=\\\"button\\\"\\n     [matRippleDisabled]=\\\"this.disableRipple || this.disabled\\\">\\n</span>\\n\",\n      styles: [\".mat-button-toggle-standalone,.mat-button-toggle-group{position:relative;display:inline-flex;flex-direction:row;white-space:nowrap;overflow:hidden;-webkit-tap-highlight-color:rgba(0,0,0,0);border-radius:var(--mat-button-toggle-legacy-shape);transform:translateZ(0)}.mat-button-toggle-standalone:not([class*=mat-elevation-z]),.mat-button-toggle-group:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}@media(forced-colors: active){.mat-button-toggle-standalone,.mat-button-toggle-group{outline:solid 1px}}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.mat-button-toggle-group-appearance-standard{border-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large));border:solid 1px var(--mat-button-toggle-divider-color, var(--mat-sys-outline))}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard .mat-pseudo-checkbox,.mat-button-toggle-group-appearance-standard .mat-pseudo-checkbox{--mat-pseudo-checkbox-minimal-selected-checkmark-color: var(--mat-button-toggle-selected-state-text-color, var(--mat-sys-on-secondary-container))}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard:not([class*=mat-elevation-z]),.mat-button-toggle-group-appearance-standard:not([class*=mat-elevation-z]){box-shadow:none}@media(forced-colors: active){.mat-button-toggle-standalone.mat-button-toggle-appearance-standard,.mat-button-toggle-group-appearance-standard{outline:0}}.mat-button-toggle-vertical{flex-direction:column}.mat-button-toggle-vertical .mat-button-toggle-label-content{display:block}.mat-button-toggle{white-space:nowrap;position:relative;color:var(--mat-button-toggle-legacy-text-color);font-family:var(--mat-button-toggle-legacy-label-text-font);font-size:var(--mat-button-toggle-legacy-label-text-size);line-height:var(--mat-button-toggle-legacy-label-text-line-height);font-weight:var(--mat-button-toggle-legacy-label-text-weight);letter-spacing:var(--mat-button-toggle-legacy-label-text-tracking);--mat-pseudo-checkbox-minimal-selected-checkmark-color: var(--mat-button-toggle-legacy-selected-state-text-color)}.mat-button-toggle.cdk-keyboard-focused .mat-button-toggle-focus-overlay{opacity:var(--mat-button-toggle-legacy-focus-state-layer-opacity)}.mat-button-toggle .mat-icon svg{vertical-align:top}.mat-button-toggle-checkbox-wrapper{display:inline-block;justify-content:flex-start;align-items:center;width:0;height:18px;line-height:18px;overflow:hidden;box-sizing:border-box;position:absolute;top:50%;left:16px;transform:translate3d(0, -50%, 0)}[dir=rtl] .mat-button-toggle-checkbox-wrapper{left:auto;right:16px}.mat-button-toggle-appearance-standard .mat-button-toggle-checkbox-wrapper{left:12px}[dir=rtl] .mat-button-toggle-appearance-standard .mat-button-toggle-checkbox-wrapper{left:auto;right:12px}.mat-button-toggle-checked .mat-button-toggle-checkbox-wrapper{width:18px}.mat-button-toggle-animations-enabled .mat-button-toggle-checkbox-wrapper{transition:width 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-button-toggle-vertical .mat-button-toggle-checkbox-wrapper{transition:none}.mat-button-toggle-checked{color:var(--mat-button-toggle-legacy-selected-state-text-color);background-color:var(--mat-button-toggle-legacy-selected-state-background-color)}.mat-button-toggle-disabled{pointer-events:none;color:var(--mat-button-toggle-legacy-disabled-state-text-color);background-color:var(--mat-button-toggle-legacy-disabled-state-background-color);--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color: var(--mat-button-toggle-legacy-disabled-state-text-color)}.mat-button-toggle-disabled.mat-button-toggle-checked{background-color:var(--mat-button-toggle-legacy-disabled-selected-state-background-color)}.mat-button-toggle-disabled-interactive{pointer-events:auto}.mat-button-toggle-appearance-standard{color:var(--mat-button-toggle-text-color, var(--mat-sys-on-surface));background-color:var(--mat-button-toggle-background-color, transparent);font-family:var(--mat-button-toggle-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mat-button-toggle-label-text-size, var(--mat-sys-label-large-size));line-height:var(--mat-button-toggle-label-text-line-height, var(--mat-sys-label-large-line-height));font-weight:var(--mat-button-toggle-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mat-button-toggle-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:solid 1px var(--mat-button-toggle-divider-color, var(--mat-sys-outline))}[dir=rtl] .mat-button-toggle-group-appearance-standard .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:solid 1px var(--mat-button-toggle-divider-color, var(--mat-sys-outline))}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical .mat-button-toggle-appearance-standard+.mat-button-toggle-appearance-standard{border-left:none;border-right:none;border-top:solid 1px var(--mat-button-toggle-divider-color, var(--mat-sys-outline))}.mat-button-toggle-appearance-standard.mat-button-toggle-checked{color:var(--mat-button-toggle-selected-state-text-color, var(--mat-sys-on-secondary-container));background-color:var(--mat-button-toggle-selected-state-background-color, var(--mat-sys-secondary-container))}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled{color:var(--mat-button-toggle-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-button-toggle-disabled-state-background-color, transparent)}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled .mat-pseudo-checkbox{--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color: var(--mat-button-toggle-disabled-selected-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-button-toggle-appearance-standard.mat-button-toggle-disabled.mat-button-toggle-checked{color:var(--mat-button-toggle-disabled-selected-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-button-toggle-disabled-selected-state-background-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{background-color:var(--mat-button-toggle-state-layer-color, var(--mat-sys-on-surface))}.mat-button-toggle-appearance-standard:hover .mat-button-toggle-focus-overlay{opacity:var(--mat-button-toggle-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-button-toggle-appearance-standard.cdk-keyboard-focused .mat-button-toggle-focus-overlay{opacity:var(--mat-button-toggle-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}@media(hover: none){.mat-button-toggle-appearance-standard:hover .mat-button-toggle-focus-overlay{display:none}}.mat-button-toggle-label-content{-webkit-user-select:none;user-select:none;display:inline-block;padding:0 16px;line-height:var(--mat-button-toggle-legacy-height);position:relative}.mat-button-toggle-appearance-standard .mat-button-toggle-label-content{padding:0 12px;line-height:var(--mat-button-toggle-height, 40px)}.mat-button-toggle-label-content>*{vertical-align:middle}.mat-button-toggle-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;pointer-events:none;opacity:0;background-color:var(--mat-button-toggle-legacy-state-layer-color)}@media(forced-colors: active){.mat-button-toggle-checked .mat-button-toggle-focus-overlay{border-bottom:solid 500px;opacity:.5;height:0}.mat-button-toggle-checked:hover .mat-button-toggle-focus-overlay{opacity:.6}.mat-button-toggle-checked.mat-button-toggle-appearance-standard .mat-button-toggle-focus-overlay{border-bottom:solid 500px}}.mat-button-toggle .mat-button-toggle-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-button-toggle-button{border:0;background:none;color:inherit;padding:0;margin:0;font:inherit;outline:none;width:100%;cursor:pointer}.mat-button-toggle-animations-enabled .mat-button-toggle-button{transition:padding 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-button-toggle-vertical .mat-button-toggle-button{transition:none}.mat-button-toggle-disabled .mat-button-toggle-button{cursor:default}.mat-button-toggle-button::-moz-focus-inner{border:0}.mat-button-toggle-checked .mat-button-toggle-button:has(.mat-button-toggle-checkbox-wrapper){padding-left:30px}[dir=rtl] .mat-button-toggle-checked .mat-button-toggle-button:has(.mat-button-toggle-checkbox-wrapper){padding-left:0;padding-right:30px}.mat-button-toggle-standalone.mat-button-toggle-appearance-standard{--mat-focus-indicator-border-radius: var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large))}.mat-button-toggle-group-appearance-standard:not(.mat-button-toggle-vertical) .mat-button-toggle:last-of-type .mat-button-toggle-button::before{border-top-right-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large));border-bottom-right-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large))}.mat-button-toggle-group-appearance-standard:not(.mat-button-toggle-vertical) .mat-button-toggle:first-of-type .mat-button-toggle-button::before{border-top-left-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large));border-bottom-left-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large))}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical .mat-button-toggle:last-of-type .mat-button-toggle-button::before{border-bottom-right-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large));border-bottom-left-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large))}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical .mat-button-toggle:first-of-type .mat-button-toggle-button::before{border-top-right-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large));border-top-left-radius:var(--mat-button-toggle-shape, var(--mat-sys-corner-extra-large))}\\n\"]\n    }]\n  }], () => [], {\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    _buttonElement: [{\n      type: ViewChild,\n      args: ['button']\n    }],\n    id: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    tabIndex: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    appearance: [{\n      type: Input\n    }],\n    checked: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    change: [{\n      type: Output\n    }]\n  });\n})();\nclass MatButtonToggleModule {\n  static ɵfac = function MatButtonToggleModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatButtonToggleModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatButtonToggleModule,\n    imports: [MatCommonModule, MatRippleModule, MatButtonToggleGroup, MatButtonToggle],\n    exports: [MatCommonModule, MatButtonToggleGroup, MatButtonToggle]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatRippleModule, MatButtonToggle, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatButtonToggleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatRippleModule, MatButtonToggleGroup, MatButtonToggle],\n      exports: [MatCommonModule, MatButtonToggleGroup, MatButtonToggle]\n    }]\n  }], null, null);\n})();\nexport { MAT_BUTTON_TOGGLE_DEFAULT_OPTIONS, MAT_BUTTON_TOGGLE_GROUP, MAT_BUTTON_TOGGLE_GROUP_DEFAULT_OPTIONS_FACTORY, MAT_BUTTON_TOGGLE_GROUP_VALUE_ACCESSOR, MatButtonToggle, MatButtonToggleChange, MatButtonToggleGroup, MatButtonToggleModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA;AACA;AACA;AAeA,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,uBAAuB,CAAC;AACxC,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,YAAY,OAAO,QAAQ;AAAA,EAC3C;AACF;AACA,IAAM,oCAAoC,IAAI,eAAe,qCAAqC;AAAA,EAChG,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,SAAS,kDAAkD;AACzD,SAAO;AAAA,IACL,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,qBAAqB;AAAA,EACvB;AACF;AAMA,IAAM,0BAA0B,IAAI,eAAe,sBAAsB;AAMzE,IAAM,yCAAyC;AAAA,EAC7C,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,oBAAoB;AAAA,EAClD,OAAO;AACT;AAEA,IAAM,wBAAN,MAA4B;AAAA,EAC1B;AAAA,EACA;AAAA,EACA,YACA,QACA,OAAO;AACL,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,kBAAkB,OAAO,iBAAiB;AAAA,EAC1C,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,uBAAuB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gCAAgC,MAAM;AAAA,EAAC;AAAA;AAAA,EAEvC,aAAa,MAAM;AAAA,EAAC;AAAA;AAAA,EAEpB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,OAAO;AACd,SAAK,QAAQ;AACb,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,QAAQ,OAAO,YAAY,EAAE,MAAM,0BAA0B;AAAA;AAAA,EAE7D;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,UAAM,WAAW,KAAK,kBAAkB,KAAK,gBAAgB,WAAW,CAAC;AACzE,QAAI,KAAK,UAAU;AACjB,aAAO,SAAS,IAAI,YAAU,OAAO,KAAK;AAAA,IAC5C;AACA,WAAO,SAAS,CAAC,IAAI,SAAS,CAAC,EAAE,QAAQ;AAAA,EAC3C;AAAA,EACA,IAAI,MAAM,UAAU;AAClB,SAAK,qBAAqB,QAAQ;AAClC,SAAK,YAAY,KAAK,KAAK,KAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,IAAI,aAAa;AAAA;AAAA,EAE/B,IAAI,WAAW;AACb,UAAM,WAAW,KAAK,kBAAkB,KAAK,gBAAgB,WAAW,CAAC;AACzE,WAAO,KAAK,WAAW,WAAW,SAAS,CAAC,KAAK;AAAA,EACnD;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA,EAEA,IAAI,sBAAsB;AACxB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,oBAAoB,OAAO;AAC7B,SAAK,uBAAuB;AAC5B,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA,EAEA,IAAI,MAAM;AACR,WAAO,KAAK,QAAQ,KAAK,KAAK,UAAU,QAAQ,QAAQ;AAAA,EAC1D;AAAA;AAAA,EAEA,SAAS,IAAI,aAAa;AAAA;AAAA,EAE1B,IAAI,+BAA+B;AACjC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,6BAA6B,OAAO;AACtC,SAAK,gCAAgC;AACrC,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,iCAAiC;AACnC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,+BAA+B,OAAO;AACxC,SAAK,kCAAkC;AACvC,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA,cAAc;AACZ,UAAM,iBAAiB,OAAO,mCAAmC;AAAA,MAC/D,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,aAAa,kBAAkB,eAAe,aAAa,eAAe,aAAa;AAC5F,SAAK,+BAA+B,gBAAgB,gCAAgC;AACpF,SAAK,iCAAiC,gBAAgB,kCAAkC;AAAA,EAC1F;AAAA,EACA,WAAW;AACT,SAAK,kBAAkB,IAAI,eAAe,KAAK,UAAU,QAAW,KAAK;AAAA,EAC3E;AAAA,EACA,qBAAqB;AACnB,SAAK,gBAAgB,OAAO,GAAG,KAAK,eAAe,OAAO,YAAU,OAAO,OAAO,CAAC;AACnF,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AAChB,SAAK,QAAQ;AACb,SAAK,gBAAgB,aAAa;AAAA,EACpC;AAAA;AAAA,EAEA,iBAAiB,IAAI;AACnB,SAAK,gCAAgC;AAAA,EACvC;AAAA;AAAA,EAEA,kBAAkB,IAAI;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA,EAEA,iBAAiB,YAAY;AAC3B,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,SAAS,OAAO;AACd,QAAI,KAAK,YAAY,KAAK,YAAY,eAAe,KAAK,GAAG;AAC3D;AAAA,IACF;AACA,UAAM,SAAS,MAAM;AACrB,UAAM,WAAW,OAAO;AACxB,UAAM,QAAQ,KAAK,eAAe,QAAQ,EAAE,UAAU,YAAU;AAC9D,aAAO,OAAO,aAAa;AAAA,IAC7B,CAAC;AACD,QAAI,aAAa;AACjB,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AACH,qBAAa,KAAK,eAAe,IAAI,KAAK,KAAK;AAC/C;AAAA,MACF,KAAK;AACH,qBAAa,KAAK,eAAe,OAAO,EAAE;AAC1C;AAAA,MACF,KAAK;AACH,qBAAa,KAAK,eAAe,OAAO,KAAK,QAAQ,QAAQ,KAAK,CAAC;AACnE;AAAA,MACF,KAAK;AACH,qBAAa,KAAK,eAAe,OAAO,CAAC;AACzC;AAAA,MACF,KAAK;AACH,qBAAa,KAAK,eAAe,OAAO,KAAK,QAAQ,QAAQ,IAAI,EAAE;AACnE;AAAA,MACF;AACE;AAAA,IACJ;AACA,QAAI,YAAY;AACd,YAAM,eAAe;AACrB,iBAAW,eAAe;AAC1B,iBAAW,MAAM;AAAA,IACnB;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,QAAQ;AACvB,UAAM,QAAQ,IAAI,sBAAsB,QAAQ,KAAK,KAAK;AAC1D,SAAK,YAAY,MAAM;AACvB,SAAK,8BAA8B,MAAM,KAAK;AAC9C,SAAK,OAAO,KAAK,KAAK;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAkB,QAAQ,QAAQ,cAAc,OAAO,cAAc,OAAO;AAG1E,QAAI,CAAC,KAAK,YAAY,KAAK,YAAY,CAAC,OAAO,SAAS;AACtD,WAAK,SAAS,UAAU;AAAA,IAC1B;AACA,QAAI,KAAK,iBAAiB;AACxB,UAAI,QAAQ;AACV,aAAK,gBAAgB,OAAO,MAAM;AAAA,MACpC,OAAO;AACL,aAAK,gBAAgB,SAAS,MAAM;AAAA,MACtC;AAAA,IACF,OAAO;AACL,oBAAc;AAAA,IAChB;AAIA,QAAI,aAAa;AACf,cAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,kBAAkB,QAAQ,WAAW,CAAC;AAAA,IAC1E,OAAO;AACL,WAAK,kBAAkB,QAAQ,WAAW;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA,EAEA,YAAY,QAAQ;AAClB,WAAO,KAAK,mBAAmB,KAAK,gBAAgB,WAAW,MAAM;AAAA,EACvE;AAAA;AAAA,EAEA,cAAc,QAAQ;AACpB,QAAI,OAAO,KAAK,cAAc,aAAa;AACzC,aAAO;AAAA,IACT;AACA,QAAI,KAAK,YAAY,MAAM,QAAQ,KAAK,SAAS,GAAG;AAClD,aAAO,KAAK,UAAU,KAAK,WAAS,OAAO,SAAS,QAAQ,UAAU,OAAO,KAAK;AAAA,IACpF;AACA,WAAO,OAAO,UAAU,KAAK;AAAA,EAC/B;AAAA;AAAA,EAEA,sBAAsB;AACpB,SAAK,eAAe,QAAQ,YAAU;AACpC,aAAO,WAAW;AAAA,IACpB,CAAC;AACD,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,WAAW;AAAA,IAC3B,OAAO;AACL,eAAS,IAAI,GAAG,IAAI,KAAK,eAAe,QAAQ,KAAK;AACnD,cAAM,SAAS,KAAK,eAAe,IAAI,CAAC;AACxC,YAAI,CAAC,OAAO,UAAU;AACpB,iBAAO,WAAW;AAClB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,YAAY,QAAQ;AACjC,UAAM,QAAQ,KAAK;AACnB,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,KAAK;AACtC,YAAM,SAAS,aAAa,SAAS,IAAI,MAAM,UAAU,MAAM;AAC/D,YAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,UAAI,QAAQ,CAAC,KAAK,UAAU;AAC1B,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,qBAAqB,OAAO;AAC1B,SAAK,YAAY;AACjB,QAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,IACF;AACA,UAAM,UAAU,KAAK,eAAe,QAAQ;AAC5C,QAAI,KAAK,YAAY,OAAO;AAC1B,UAAI,CAAC,MAAM,QAAQ,KAAK,MAAM,OAAO,cAAc,eAAe,YAAY;AAC5E,cAAM,MAAM,oDAAoD;AAAA,MAClE;AACA,WAAK,gBAAgB;AACrB,YAAM,QAAQ,kBAAgB,KAAK,aAAa,cAAc,OAAO,CAAC;AAAA,IACxE,OAAO;AACL,WAAK,gBAAgB;AACrB,WAAK,aAAa,OAAO,OAAO;AAAA,IAClC;AAEA,QAAI,CAAC,KAAK,YAAY,QAAQ,MAAM,YAAU,OAAO,aAAa,EAAE,GAAG;AACrE,iBAAW,UAAU,SAAS;AAC5B,YAAI,CAAC,OAAO,UAAU;AACpB,iBAAO,WAAW;AAClB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,gBAAgB,MAAM;AAC3B,SAAK,eAAe,QAAQ,YAAU;AACpC,aAAO,UAAU;AAEjB,UAAI,CAAC,KAAK,UAAU;AAClB,eAAO,WAAW;AAAA,MACpB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,aAAa,OAAO,SAAS;AAC3B,eAAW,UAAU,SAAS;AAC5B,UAAI,OAAO,UAAU,OAAO;AAC1B,eAAO,UAAU;AACjB,aAAK,gBAAgB,OAAO,MAAM;AAClC,YAAI,CAAC,KAAK,UAAU;AAElB,iBAAO,WAAW;AAAA,QACpB;AACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB,QAAQ,aAAa;AAErC,QAAI,aAAa;AACf,WAAK,iBAAiB,MAAM;AAAA,IAC9B;AAGA,SAAK,YAAY,KAAK,KAAK,KAAK;AAAA,EAClC;AAAA;AAAA,EAEA,uBAAuB;AACrB,SAAK,gBAAgB,QAAQ,YAAU,OAAO,cAAc,CAAC;AAAA,EAC/D;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,yBAAyB,CAAC;AAAA,IACvC,gBAAgB,SAAS,oCAAoC,IAAI,KAAK,UAAU;AAC9E,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,iBAAiB,CAAC;AAAA,MAChD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,yBAAyB;AAAA,IACxC,UAAU;AAAA,IACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,WAAW,SAAS,gDAAgD,QAAQ;AACxF,iBAAO,IAAI,SAAS,MAAM;AAAA,QAC5B,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,QAAQ,IAAI,WAAW,UAAU,YAAY,EAAE,iBAAiB,IAAI,QAAQ;AAC3F,QAAG,YAAY,8BAA8B,IAAI,QAAQ,EAAE,+CAA+C,IAAI,eAAe,UAAU;AAAA,MACzI;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,OAAO;AAAA,MACP,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,qBAAqB,CAAC,GAAG,uBAAuB,uBAAuB,gBAAgB;AAAA,MACvF,8BAA8B,CAAC,GAAG,gCAAgC,gCAAgC,gBAAgB;AAAA,MAClH,gCAAgC,CAAC,GAAG,kCAAkC,kCAAkC,gBAAgB;AAAA,IAC1H;AAAA,IACA,SAAS;AAAA,MACP,aAAa;AAAA,MACb,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAC,sBAAsB;AAAA,IACjC,UAAU,CAAI,mBAAmB,CAAC,wCAAwC;AAAA,MACxE,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,wCAAwC;AAAA,QAClD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,aAAa;AAAA,QACb,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,sCAAsC;AAAA,QACtC,uDAAuD;AAAA,MACzD;AAAA,MACA,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,MAAM,eAAe,GAAG;AAAA;AAAA;AAAA,QAGxC,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,8BAA8B,CAAC;AAAA,MAC7B,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gCAAgC,CAAC;AAAA,MAC/B,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAEH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,cAAc,OAAO,UAAU;AAAA,EAC/B,gBAAgB,OAAO,YAAY;AAAA,EACnC,eAAe,OAAO,YAAY;AAAA,EAClC,qBAAqB,oBAAoB;AAAA,EACzC,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AAAA;AAAA,EAEjB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,GAAG,KAAK,EAAE;AAAA,EACnB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,UAAU,IAAI,KAAK;AAAA,EAC1B;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,oBAAoB,KAAK,kBAAkB,aAAa,KAAK;AAAA,EAC3E;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK,oBAAoB,KAAK,kBAAkB,YAAY,IAAI,IAAI,KAAK;AAAA,EAClF;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,QAAI,UAAU,KAAK,UAAU;AAC3B,WAAK,WAAW;AAChB,UAAI,KAAK,mBAAmB;AAC1B,aAAK,kBAAkB,kBAAkB,MAAM,KAAK,QAAQ;AAAA,MAC9D;AACA,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,KAAK,qBAAqB,KAAK,kBAAkB;AAAA,EAC5E;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,sBAAsB;AACxB,WAAO,KAAK,wBAAwB,KAAK,sBAAsB,QAAQ,KAAK,kBAAkB;AAAA,EAChG;AAAA,EACA,IAAI,oBAAoB,OAAO;AAC7B,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA;AAAA;AAAA,EAEA,SAAS,IAAI,aAAa;AAAA,EAC1B,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,uBAAuB;AAC3D,UAAM,cAAc,OAAO,yBAAyB;AAAA,MAClD,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,kBAAkB,OAAO,IAAI,mBAAmB,UAAU,GAAG;AAAA,MACjE,UAAU;AAAA,IACZ,CAAC,KAAK;AACN,UAAM,iBAAiB,OAAO,mCAAmC;AAAA,MAC/D,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,YAAY,OAAO,SAAS,eAAe,KAAK,GAAG,GAAI,YAAY,CAAC;AAAA,MACvE,WAAW;AAAA,IACb,CAAC,IAAI,CAAC,CAAE;AACR,SAAK,oBAAoB;AACzB,SAAK,aAAa,kBAAkB,eAAe,aAAa,eAAe,aAAa;AAC5F,SAAK,sBAAsB,gBAAgB,uBAAuB;AAAA,EACpE;AAAA,EACA,WAAW;AACT,UAAM,QAAQ,KAAK;AACnB,SAAK,KAAK,KAAK,MAAM,KAAK,aAAa,MAAM,oBAAoB;AACjE,QAAI,OAAO;AACT,UAAI,MAAM,cAAc,IAAI,GAAG;AAC7B,aAAK,UAAU;AAAA,MACjB,WAAW,MAAM,YAAY,IAAI,MAAM,KAAK,UAAU;AAKpD,cAAM,kBAAkB,MAAM,KAAK,QAAQ;AAAA,MAC7C;AAAA,IACF;AAAA,EACF;AAAA,EACA,kBAAkB;AAKhB,QAAI,CAAC,KAAK,oBAAoB;AAC5B,WAAK,YAAY,cAAc,UAAU,IAAI,sCAAsC;AAAA,IACrF;AACA,SAAK,cAAc,QAAQ,KAAK,aAAa,IAAI;AAAA,EACnD;AAAA,EACA,cAAc;AACZ,UAAM,QAAQ,KAAK;AACnB,SAAK,cAAc,eAAe,KAAK,WAAW;AAGlD,QAAI,SAAS,MAAM,YAAY,IAAI,GAAG;AACpC,YAAM,kBAAkB,MAAM,OAAO,OAAO,IAAI;AAAA,IAClD;AAAA,EACF;AAAA;AAAA,EAEA,MAAM,SAAS;AACb,SAAK,eAAe,cAAc,MAAM,OAAO;AAAA,EACjD;AAAA;AAAA,EAEA,iBAAiB;AACf,QAAI,KAAK,UAAU;AACjB;AAAA,IACF;AACA,UAAM,aAAa,KAAK,iBAAiB,IAAI,OAAO,CAAC,KAAK;AAC1D,QAAI,eAAe,KAAK,UAAU;AAChC,WAAK,WAAW;AAChB,UAAI,KAAK,mBAAmB;AAC1B,aAAK,kBAAkB,kBAAkB,MAAM,KAAK,UAAU,IAAI;AAClE,aAAK,kBAAkB,WAAW;AAAA,MACpC;AAAA,IACF;AACA,QAAI,KAAK,iBAAiB,GAAG;AAC3B,YAAM,YAAY,KAAK,kBAAkB,eAAe,KAAK,YAAU;AACrE,eAAO,OAAO,aAAa;AAAA,MAC7B,CAAC;AAED,UAAI,WAAW;AACb,kBAAU,WAAW;AAAA,MACvB;AAEA,WAAK,WAAW;AAAA,IAClB;AAEA,SAAK,OAAO,KAAK,IAAI,sBAAsB,MAAM,KAAK,KAAK,CAAC;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AAGd,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,iBAAiB;AACf,QAAI,KAAK,iBAAiB,GAAG;AAC3B,aAAO,KAAK,kBAAkB;AAAA,IAChC;AACA,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA;AAAA,EAEA,mBAAmB;AACjB,WAAO,KAAK,qBAAqB,CAAC,KAAK,kBAAkB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,IACjC,WAAW,SAAS,sBAAsB,IAAI,KAAK;AACjD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AAAA,MACvE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,QAAQ,gBAAgB,GAAG,mBAAmB;AAAA,IAC1D,UAAU;AAAA,IACV,cAAc,SAAS,6BAA6B,IAAI,KAAK;AAC3D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,2CAA2C;AACzE,iBAAO,IAAI,MAAM;AAAA,QACnB,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,cAAc,IAAI,EAAE,mBAAmB,IAAI,EAAE,MAAM,IAAI,EAAE,EAAE,QAAQ,IAAI;AACtF,QAAG,YAAY,gCAAgC,CAAC,IAAI,iBAAiB,EAAE,6BAA6B,IAAI,OAAO,EAAE,8BAA8B,IAAI,QAAQ,EAAE,0CAA0C,IAAI,mBAAmB,EAAE,yCAAyC,IAAI,eAAe,UAAU;AAAA,MACxS;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,WAAW,CAAC,GAAG,cAAc,WAAW;AAAA,MACxC,gBAAgB,CAAC,GAAG,mBAAmB,gBAAgB;AAAA,MACvD,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU;AAAA,MACV,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,YAAY;AAAA,MACZ,SAAS,CAAC,GAAG,WAAW,WAAW,gBAAgB;AAAA,MACnD,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,qBAAqB,CAAC,GAAG,uBAAuB,uBAAuB,gBAAgB;AAAA,IACzF;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAC,iBAAiB;AAAA,IAC5B,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,QAAQ,UAAU,GAAG,4BAA4B,uBAAuB,GAAG,SAAS,MAAM,UAAU,GAAG,CAAC,GAAG,oCAAoC,GAAG,CAAC,GAAG,iCAAiC,GAAG,CAAC,GAAG,iCAAiC,GAAG,CAAC,aAAa,IAAI,GAAG,4BAA4B,GAAG,oBAAoB,mBAAmB,GAAG,CAAC,SAAS,WAAW,eAAe,QAAQ,cAAc,WAAW,GAAG,UAAU,CAAC;AAAA,IAC7a,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,UAAU,GAAG,CAAC;AACnC,QAAG,WAAW,SAAS,SAAS,mDAAmD;AACjF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,eAAe,CAAC;AAAA,QAC5C,CAAC;AACD,QAAG,oBAAoB,GAAG,wCAAwC,GAAG,GAAG,OAAO,CAAC;AAChF,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa,EAAE;AAClB,QAAG,UAAU,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAAA,MACzC;AACA,UAAI,KAAK,GAAG;AACV,cAAM,YAAe,YAAY,CAAC;AAClC,QAAG,WAAW,MAAM,IAAI,QAAQ,EAAE,YAAY,IAAI,YAAY,CAAC,IAAI,uBAAuB,IAAI;AAC9F,QAAG,YAAY,QAAQ,IAAI,iBAAiB,IAAI,UAAU,QAAQ,EAAE,YAAY,IAAI,YAAY,CAAC,IAAI,sBAAsB,KAAK,IAAI,QAAQ,EAAE,gBAAgB,CAAC,IAAI,iBAAiB,IAAI,IAAI,UAAU,IAAI,EAAE,gBAAgB,IAAI,iBAAiB,IAAI,IAAI,UAAU,IAAI,EAAE,QAAQ,IAAI,eAAe,CAAC,EAAE,cAAc,IAAI,SAAS,EAAE,mBAAmB,IAAI,cAAc,EAAE,iBAAiB,IAAI,YAAY,IAAI,sBAAsB,SAAS,IAAI;AACnb,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,sBAAsB,CAAC,IAAI,kBAAkB,YAAY,CAAC,IAAI,kBAAkB,gCAAgC,IAAI,kBAAkB,YAAY,CAAC,IAAI,kBAAkB,kCAAkC,IAAI,EAAE;AACtO,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,SAAS,EAAE,qBAAqB,IAAI,iBAAiB,IAAI,QAAQ;AAAA,MACrG;AAAA,IACF;AAAA,IACA,cAAc,CAAC,WAAW,iBAAiB;AAAA,IAC3C,QAAQ,CAAC,y/TAAy/T;AAAA,IAClgU,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,wCAAwC;AAAA,QACxC,qCAAqC;AAAA,QACrC,sCAAsC;AAAA,QACtC,kDAAkD;AAAA,QAClD,iDAAiD;AAAA,QACjD,SAAS;AAAA,QACT,qBAAqB;AAAA,QACrB,0BAA0B;AAAA,QAC1B,aAAa;AAAA,QACb,eAAe;AAAA,QACf,WAAW;AAAA,QACX,QAAQ;AAAA,MACV;AAAA,MACA,SAAS,CAAC,WAAW,iBAAiB;AAAA,MACtC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,y/TAAy/T;AAAA,IACpgU,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,iBAAiB,sBAAsB,eAAe;AAAA,IACjF,SAAS,CAAC,iBAAiB,sBAAsB,eAAe;AAAA,EAClE,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,iBAAiB,iBAAiB,eAAe;AAAA,EAC9E,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,iBAAiB,sBAAsB,eAAe;AAAA,MACjF,SAAS,CAAC,iBAAiB,sBAAsB,eAAe;AAAA,IAClE,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}
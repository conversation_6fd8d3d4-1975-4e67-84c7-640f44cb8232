{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/public-api.mjs", "../../../../../../node_modules/@angular/material/fesm2022/grid-list.mjs"], "sourcesContent": ["/**\n * Class for determining, from a list of tiles, the (row, col) position of each of those tiles\n * in the grid. This is necessary (rather than just rendering the tiles in normal document flow)\n * because the tiles can have a rowspan.\n *\n * The positioning algorithm greedily places each tile as soon as it encounters a gap in the grid\n * large enough to accommodate it so that the tiles still render in the same order in which they\n * are given.\n *\n * The basis of the algorithm is the use of an array to track the already placed tiles. Each\n * element of the array corresponds to a column, and the value indicates how many cells in that\n * column are already occupied; zero indicates an empty cell. Moving \"down\" to the next row\n * decrements each value in the tracking array (indicating that the column is one cell closer to\n * being free).\n *\n * @docs-private\n */\nclass TileCoordinator {\n    /** Tracking array (see class description). */\n    tracker;\n    /** Index at which the search for the next gap will start. */\n    columnIndex = 0;\n    /** The current row index. */\n    rowIndex = 0;\n    /** Gets the total number of rows occupied by tiles */\n    get rowCount() {\n        return this.rowIndex + 1;\n    }\n    /**\n     * Gets the total span of rows occupied by tiles.\n     * Ex: A list with 1 row that contains a tile with rowspan 2 will have a total rowspan of 2.\n     */\n    get rowspan() {\n        const lastRowMax = Math.max(...this.tracker);\n        // if any of the tiles has a rowspan that pushes it beyond the total row count,\n        // add the difference to the rowcount\n        return lastRowMax > 1 ? this.rowCount + lastRowMax - 1 : this.rowCount;\n    }\n    /** The computed (row, col) position of each tile (the output). */\n    positions;\n    /**\n     * Updates the tile positions.\n     * @param numColumns Amount of columns in the grid.\n     * @param tiles Tiles to be positioned.\n     */\n    update(numColumns, tiles) {\n        this.columnIndex = 0;\n        this.rowIndex = 0;\n        this.tracker = new Array(numColumns);\n        this.tracker.fill(0, 0, this.tracker.length);\n        this.positions = tiles.map(tile => this._trackTile(tile));\n    }\n    /** Calculates the row and col position of a tile. */\n    _trackTile(tile) {\n        // Find a gap large enough for this tile.\n        const gapStartIndex = this._findMatchingGap(tile.colspan);\n        // Place tile in the resulting gap.\n        this._markTilePosition(gapStartIndex, tile);\n        // The next time we look for a gap, the search will start at columnIndex, which should be\n        // immediately after the tile that has just been placed.\n        this.columnIndex = gapStartIndex + tile.colspan;\n        return new TilePosition(this.rowIndex, gapStartIndex);\n    }\n    /** Finds the next available space large enough to fit the tile. */\n    _findMatchingGap(tileCols) {\n        if (tileCols > this.tracker.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`mat-grid-list: tile with colspan ${tileCols} is wider than ` +\n                `grid with cols=\"${this.tracker.length}\".`);\n        }\n        // Start index is inclusive, end index is exclusive.\n        let gapStartIndex = -1;\n        let gapEndIndex = -1;\n        // Look for a gap large enough to fit the given tile. Empty spaces are marked with a zero.\n        do {\n            // If we've reached the end of the row, go to the next row.\n            if (this.columnIndex + tileCols > this.tracker.length) {\n                this._nextRow();\n                gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n                gapEndIndex = this._findGapEndIndex(gapStartIndex);\n                continue;\n            }\n            gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n            // If there are no more empty spaces in this row at all, move on to the next row.\n            if (gapStartIndex == -1) {\n                this._nextRow();\n                gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n                gapEndIndex = this._findGapEndIndex(gapStartIndex);\n                continue;\n            }\n            gapEndIndex = this._findGapEndIndex(gapStartIndex);\n            // If a gap large enough isn't found, we want to start looking immediately after the current\n            // gap on the next iteration.\n            this.columnIndex = gapStartIndex + 1;\n            // Continue iterating until we find a gap wide enough for this tile. Since gapEndIndex is\n            // exclusive, gapEndIndex is 0 means we didn't find a gap and should continue.\n        } while (gapEndIndex - gapStartIndex < tileCols || gapEndIndex == 0);\n        // If we still didn't manage to find a gap, ensure that the index is\n        // at least zero so the tile doesn't get pulled out of the grid.\n        return Math.max(gapStartIndex, 0);\n    }\n    /** Move \"down\" to the next row. */\n    _nextRow() {\n        this.columnIndex = 0;\n        this.rowIndex++;\n        // Decrement all spaces by one to reflect moving down one row.\n        for (let i = 0; i < this.tracker.length; i++) {\n            this.tracker[i] = Math.max(0, this.tracker[i] - 1);\n        }\n    }\n    /**\n     * Finds the end index (exclusive) of a gap given the index from which to start looking.\n     * The gap ends when a non-zero value is found.\n     */\n    _findGapEndIndex(gapStartIndex) {\n        for (let i = gapStartIndex + 1; i < this.tracker.length; i++) {\n            if (this.tracker[i] != 0) {\n                return i;\n            }\n        }\n        // The gap ends with the end of the row.\n        return this.tracker.length;\n    }\n    /** Update the tile tracker to account for the given tile in the given space. */\n    _markTilePosition(start, tile) {\n        for (let i = 0; i < tile.colspan; i++) {\n            this.tracker[start + i] = tile.rowspan;\n        }\n    }\n}\n/**\n * Simple data structure for tile position (row, col).\n * @docs-private\n */\nclass TilePosition {\n    row;\n    col;\n    constructor(row, col) {\n        this.row = row;\n        this.col = col;\n    }\n}\n\n// Privately exported for the grid-list harness.\nconst ɵTileCoordinator = TileCoordinator;\n\nexport { TileCoordinator, ɵTileCoordinator };\n\n", "import { TileCoordinator } from './public-api.mjs';\nconst _c0 = [\"*\"];\nconst _c1 = [[[\"\", \"mat-grid-avatar\", \"\"], [\"\", \"matGridAvatar\", \"\"]], [[\"\", \"mat-line\", \"\"], [\"\", \"matLine\", \"\"]], \"*\"];\nconst _c2 = [\"[mat-grid-avatar], [matGridAvatar]\", \"[mat-line], [matLine]\", \"*\"];\nconst _c3 = \".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\\n\";\nexport { ɵTileCoordinator } from './public-api.mjs';\nimport { setLines, MatLine, MatLineModule } from './line.mjs';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, ContentChildren, Directive, NgModule } from '@angular/core';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { MatCommonModule } from './common-module.mjs';\nimport 'rxjs/operators';\nimport '@angular/cdk/a11y';\n\n/**\n * Injection token used to provide a grid list to a tile and to avoid circular imports.\n * @docs-private\n */\nconst MAT_GRID_LIST = new InjectionToken('MAT_GRID_LIST');\nclass MatGridTile {\n  _element = inject(ElementRef);\n  _gridList = inject(MAT_GRID_LIST, {\n    optional: true\n  });\n  _rowspan = 1;\n  _colspan = 1;\n  constructor() {}\n  /** Amount of rows that the grid tile takes up. */\n  get rowspan() {\n    return this._rowspan;\n  }\n  set rowspan(value) {\n    this._rowspan = Math.round(coerceNumberProperty(value));\n  }\n  /** Amount of columns that the grid tile takes up. */\n  get colspan() {\n    return this._colspan;\n  }\n  set colspan(value) {\n    this._colspan = Math.round(coerceNumberProperty(value));\n  }\n  /**\n   * Sets the style of the grid-tile element.  Needs to be set manually to avoid\n   * \"Changed after checked\" errors that would occur with HostBinding.\n   */\n  _setStyle(property, value) {\n    this._element.nativeElement.style[property] = value;\n  }\n  static ɵfac = function MatGridTile_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatGridTile)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatGridTile,\n    selectors: [[\"mat-grid-tile\"]],\n    hostAttrs: [1, \"mat-grid-tile\"],\n    hostVars: 2,\n    hostBindings: function MatGridTile_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"rowspan\", ctx.rowspan)(\"colspan\", ctx.colspan);\n      }\n    },\n    inputs: {\n      rowspan: \"rowspan\",\n      colspan: \"colspan\"\n    },\n    exportAs: [\"matGridTile\"],\n    ngContentSelectors: _c0,\n    decls: 2,\n    vars: 0,\n    consts: [[1, \"mat-grid-tile-content\"]],\n    template: function MatGridTile_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵdomElementStart(0, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵdomElementEnd();\n      }\n    },\n    styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatGridTile, [{\n    type: Component,\n    args: [{\n      selector: 'mat-grid-tile',\n      exportAs: 'matGridTile',\n      host: {\n        'class': 'mat-grid-tile',\n        // Ensures that the \"rowspan\" and \"colspan\" input value is reflected in\n        // the DOM. This is needed for the grid-tile harness.\n        '[attr.rowspan]': 'rowspan',\n        '[attr.colspan]': 'colspan'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<div class=\\\"mat-grid-tile-content\\\">\\n  <ng-content></ng-content>\\n</div>\\n\",\n      styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\\n\"]\n    }]\n  }], () => [], {\n    rowspan: [{\n      type: Input\n    }],\n    colspan: [{\n      type: Input\n    }]\n  });\n})();\nclass MatGridTileText {\n  _element = inject(ElementRef);\n  _lines;\n  constructor() {}\n  ngAfterContentInit() {\n    setLines(this._lines, this._element);\n  }\n  static ɵfac = function MatGridTileText_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatGridTileText)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatGridTileText,\n    selectors: [[\"mat-grid-tile-header\"], [\"mat-grid-tile-footer\"]],\n    contentQueries: function MatGridTileText_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatLine, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lines = _t);\n      }\n    },\n    ngContentSelectors: _c2,\n    decls: 4,\n    vars: 0,\n    consts: [[1, \"mat-grid-list-text\"]],\n    template: function MatGridTileText_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c1);\n        i0.ɵɵprojection(0);\n        i0.ɵɵdomElementStart(1, \"div\", 0);\n        i0.ɵɵprojection(2, 1);\n        i0.ɵɵdomElementEnd();\n        i0.ɵɵprojection(3, 2);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatGridTileText, [{\n    type: Component,\n    args: [{\n      selector: 'mat-grid-tile-header, mat-grid-tile-footer',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<ng-content select=\\\"[mat-grid-avatar], [matGridAvatar]\\\"></ng-content>\\n<div class=\\\"mat-grid-list-text\\\"><ng-content select=\\\"[mat-line], [matLine]\\\"></ng-content></div>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], () => [], {\n    _lines: [{\n      type: ContentChildren,\n      args: [MatLine, {\n        descendants: true\n      }]\n    }]\n  });\n})();\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatGridAvatarCssMatStyler {\n  static ɵfac = function MatGridAvatarCssMatStyler_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatGridAvatarCssMatStyler)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatGridAvatarCssMatStyler,\n    selectors: [[\"\", \"mat-grid-avatar\", \"\"], [\"\", \"matGridAvatar\", \"\"]],\n    hostAttrs: [1, \"mat-grid-avatar\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatGridAvatarCssMatStyler, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-grid-avatar], [matGridAvatar]',\n      host: {\n        'class': 'mat-grid-avatar'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatGridTileHeaderCssMatStyler {\n  static ɵfac = function MatGridTileHeaderCssMatStyler_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatGridTileHeaderCssMatStyler)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatGridTileHeaderCssMatStyler,\n    selectors: [[\"mat-grid-tile-header\"]],\n    hostAttrs: [1, \"mat-grid-tile-header\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatGridTileHeaderCssMatStyler, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-grid-tile-header',\n      host: {\n        'class': 'mat-grid-tile-header'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatGridTileFooterCssMatStyler {\n  static ɵfac = function MatGridTileFooterCssMatStyler_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatGridTileFooterCssMatStyler)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatGridTileFooterCssMatStyler,\n    selectors: [[\"mat-grid-tile-footer\"]],\n    hostAttrs: [1, \"mat-grid-tile-footer\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatGridTileFooterCssMatStyler, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-grid-tile-footer',\n      host: {\n        'class': 'mat-grid-tile-footer'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * RegExp that can be used to check whether a value will\n * be allowed inside a CSS `calc()` expression.\n */\nconst cssCalcAllowedValue = /^-?\\d+((\\.\\d+)?[A-Za-z%$]?)+$/;\n/**\n * Sets the style properties for an individual tile, given the position calculated by the\n * Tile Coordinator.\n * @docs-private\n */\nclass TileStyler {\n  _gutterSize;\n  _rows = 0;\n  _rowspan = 0;\n  _cols;\n  _direction;\n  /**\n   * Adds grid-list layout info once it is available. Cannot be processed in the constructor\n   * because these properties haven't been calculated by that point.\n   *\n   * @param gutterSize Size of the grid's gutter.\n   * @param tracker Instance of the TileCoordinator.\n   * @param cols Amount of columns in the grid.\n   * @param direction Layout direction of the grid.\n   */\n  init(gutterSize, tracker, cols, direction) {\n    this._gutterSize = normalizeUnits(gutterSize);\n    this._rows = tracker.rowCount;\n    this._rowspan = tracker.rowspan;\n    this._cols = cols;\n    this._direction = direction;\n  }\n  /**\n   * Computes the amount of space a single 1x1 tile would take up (width or height).\n   * Used as a basis for other calculations.\n   * @param sizePercent Percent of the total grid-list space that one 1x1 tile would take up.\n   * @param gutterFraction Fraction of the gutter size taken up by one 1x1 tile.\n   * @return The size of a 1x1 tile as an expression that can be evaluated via CSS calc().\n   */\n  getBaseTileSize(sizePercent, gutterFraction) {\n    // Take the base size percent (as would be if evenly dividing the size between cells),\n    // and then subtracting the size of one gutter. However, since there are no gutters on the\n    // edges, each tile only uses a fraction (gutterShare = numGutters / numCells) of the gutter\n    // size. (Imagine having one gutter per tile, and then breaking up the extra gutter on the\n    // edge evenly among the cells).\n    return `(${sizePercent}% - (${this._gutterSize} * ${gutterFraction}))`;\n  }\n  /**\n   * Gets The horizontal or vertical position of a tile, e.g., the 'top' or 'left' property value.\n   * @param offset Number of tiles that have already been rendered in the row/column.\n   * @param baseSize Base size of a 1x1 tile (as computed in getBaseTileSize).\n   * @return Position of the tile as a CSS calc() expression.\n   */\n  getTilePosition(baseSize, offset) {\n    // The position comes the size of a 1x1 tile plus gutter for each previous tile in the\n    // row/column (offset).\n    return offset === 0 ? '0' : calc(`(${baseSize} + ${this._gutterSize}) * ${offset}`);\n  }\n  /**\n   * Gets the actual size of a tile, e.g., width or height, taking rowspan or colspan into account.\n   * @param baseSize Base size of a 1x1 tile (as computed in getBaseTileSize).\n   * @param span The tile's rowspan or colspan.\n   * @return Size of the tile as a CSS calc() expression.\n   */\n  getTileSize(baseSize, span) {\n    return `(${baseSize} * ${span}) + (${span - 1} * ${this._gutterSize})`;\n  }\n  /**\n   * Sets the style properties to be applied to a tile for the given row and column index.\n   * @param tile Tile to which to apply the styling.\n   * @param rowIndex Index of the tile's row.\n   * @param colIndex Index of the tile's column.\n   */\n  setStyle(tile, rowIndex, colIndex) {\n    // Percent of the available horizontal space that one column takes up.\n    let percentWidthPerTile = 100 / this._cols;\n    // Fraction of the vertical gutter size that each column takes up.\n    // For example, if there are 5 columns, each column uses 4/5 = 0.8 times the gutter width.\n    let gutterWidthFractionPerTile = (this._cols - 1) / this._cols;\n    this.setColStyles(tile, colIndex, percentWidthPerTile, gutterWidthFractionPerTile);\n    this.setRowStyles(tile, rowIndex, percentWidthPerTile, gutterWidthFractionPerTile);\n  }\n  /** Sets the horizontal placement of the tile in the list. */\n  setColStyles(tile, colIndex, percentWidth, gutterWidth) {\n    // Base horizontal size of a column.\n    let baseTileWidth = this.getBaseTileSize(percentWidth, gutterWidth);\n    // The width and horizontal position of each tile is always calculated the same way, but the\n    // height and vertical position depends on the rowMode.\n    let side = this._direction === 'rtl' ? 'right' : 'left';\n    tile._setStyle(side, this.getTilePosition(baseTileWidth, colIndex));\n    tile._setStyle('width', calc(this.getTileSize(baseTileWidth, tile.colspan)));\n  }\n  /**\n   * Calculates the total size taken up by gutters across one axis of a list.\n   */\n  getGutterSpan() {\n    return `${this._gutterSize} * (${this._rowspan} - 1)`;\n  }\n  /**\n   * Calculates the total size taken up by tiles across one axis of a list.\n   * @param tileHeight Height of the tile.\n   */\n  getTileSpan(tileHeight) {\n    return `${this._rowspan} * ${this.getTileSize(tileHeight, 1)}`;\n  }\n  /**\n   * Calculates the computed height and returns the correct style property to set.\n   * This method can be implemented by each type of TileStyler.\n   * @docs-private\n   */\n  getComputedHeight() {\n    return null;\n  }\n}\n/**\n * This type of styler is instantiated when the user passes in a fixed row height.\n * Example `<mat-grid-list cols=\"3\" rowHeight=\"100px\">`\n * @docs-private\n */\nclass FixedTileStyler extends TileStyler {\n  fixedRowHeight;\n  constructor(fixedRowHeight) {\n    super();\n    this.fixedRowHeight = fixedRowHeight;\n  }\n  init(gutterSize, tracker, cols, direction) {\n    super.init(gutterSize, tracker, cols, direction);\n    this.fixedRowHeight = normalizeUnits(this.fixedRowHeight);\n    if (!cssCalcAllowedValue.test(this.fixedRowHeight) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Invalid value \"${this.fixedRowHeight}\" set as rowHeight.`);\n    }\n  }\n  setRowStyles(tile, rowIndex) {\n    tile._setStyle('top', this.getTilePosition(this.fixedRowHeight, rowIndex));\n    tile._setStyle('height', calc(this.getTileSize(this.fixedRowHeight, tile.rowspan)));\n  }\n  getComputedHeight() {\n    return ['height', calc(`${this.getTileSpan(this.fixedRowHeight)} + ${this.getGutterSpan()}`)];\n  }\n  reset(list) {\n    list._setListStyle(['height', null]);\n    if (list._tiles) {\n      list._tiles.forEach(tile => {\n        tile._setStyle('top', null);\n        tile._setStyle('height', null);\n      });\n    }\n  }\n}\n/**\n * This type of styler is instantiated when the user passes in a width:height ratio\n * for the row height.  Example `<mat-grid-list cols=\"3\" rowHeight=\"3:1\">`\n * @docs-private\n */\nclass RatioTileStyler extends TileStyler {\n  /** Ratio width:height given by user to determine row height. */\n  rowHeightRatio;\n  baseTileHeight;\n  constructor(value) {\n    super();\n    this._parseRatio(value);\n  }\n  setRowStyles(tile, rowIndex, percentWidth, gutterWidth) {\n    let percentHeightPerTile = percentWidth / this.rowHeightRatio;\n    this.baseTileHeight = this.getBaseTileSize(percentHeightPerTile, gutterWidth);\n    // Use padding-top and margin-top to maintain the given aspect ratio, as\n    // a percentage-based value for these properties is applied versus the *width* of the\n    // containing block. See http://www.w3.org/TR/CSS2/box.html#margin-properties\n    tile._setStyle('marginTop', this.getTilePosition(this.baseTileHeight, rowIndex));\n    tile._setStyle('paddingTop', calc(this.getTileSize(this.baseTileHeight, tile.rowspan)));\n  }\n  getComputedHeight() {\n    return ['paddingBottom', calc(`${this.getTileSpan(this.baseTileHeight)} + ${this.getGutterSpan()}`)];\n  }\n  reset(list) {\n    list._setListStyle(['paddingBottom', null]);\n    list._tiles.forEach(tile => {\n      tile._setStyle('marginTop', null);\n      tile._setStyle('paddingTop', null);\n    });\n  }\n  _parseRatio(value) {\n    const ratioParts = value.split(':');\n    if (ratioParts.length !== 2 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`mat-grid-list: invalid ratio given for row-height: \"${value}\"`);\n    }\n    this.rowHeightRatio = parseFloat(ratioParts[0]) / parseFloat(ratioParts[1]);\n  }\n}\n/**\n * This type of styler is instantiated when the user selects a \"fit\" row height mode.\n * In other words, the row height will reflect the total height of the container divided\n * by the number of rows.  Example `<mat-grid-list cols=\"3\" rowHeight=\"fit\">`\n *\n * @docs-private\n */\nclass FitTileStyler extends TileStyler {\n  setRowStyles(tile, rowIndex) {\n    // Percent of the available vertical space that one row takes up.\n    let percentHeightPerTile = 100 / this._rowspan;\n    // Fraction of the horizontal gutter size that each column takes up.\n    let gutterHeightPerTile = (this._rows - 1) / this._rows;\n    // Base vertical size of a column.\n    let baseTileHeight = this.getBaseTileSize(percentHeightPerTile, gutterHeightPerTile);\n    tile._setStyle('top', this.getTilePosition(baseTileHeight, rowIndex));\n    tile._setStyle('height', calc(this.getTileSize(baseTileHeight, tile.rowspan)));\n  }\n  reset(list) {\n    if (list._tiles) {\n      list._tiles.forEach(tile => {\n        tile._setStyle('top', null);\n        tile._setStyle('height', null);\n      });\n    }\n  }\n}\n/** Wraps a CSS string in a calc function */\nfunction calc(exp) {\n  return `calc(${exp})`;\n}\n/** Appends pixels to a CSS string if no units are given. */\nfunction normalizeUnits(value) {\n  return value.match(/([A-Za-z%]+)$/) ? value : `${value}px`;\n}\n\n// TODO(kara): Conditional (responsive) column count / row size.\n// TODO(kara): Re-layout on window resize / media change (debounced).\n// TODO(kara): gridTileHeader and gridTileFooter.\nconst MAT_FIT_MODE = 'fit';\nclass MatGridList {\n  _element = inject(ElementRef);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  /** Number of columns being rendered. */\n  _cols;\n  /** Used for determining the position of each tile in the grid. */\n  _tileCoordinator;\n  /**\n   * Row height value passed in by user. This can be one of three types:\n   * - Number value (ex: \"100px\"):  sets a fixed row height to that value\n   * - Ratio value (ex: \"4:3\"): sets the row height based on width:height ratio\n   * - \"Fit\" mode (ex: \"fit\"): sets the row height to total height divided by number of rows\n   */\n  _rowHeight;\n  /** The amount of space between tiles. This will be something like '5px' or '2em'. */\n  _gutter = '1px';\n  /** Sets position and size styles for a tile */\n  _tileStyler;\n  /** Query list of tiles that are being rendered. */\n  _tiles;\n  constructor() {}\n  /** Amount of columns in the grid list. */\n  get cols() {\n    return this._cols;\n  }\n  set cols(value) {\n    this._cols = Math.max(1, Math.round(coerceNumberProperty(value)));\n  }\n  /** Size of the grid list's gutter in pixels. */\n  get gutterSize() {\n    return this._gutter;\n  }\n  set gutterSize(value) {\n    this._gutter = `${value == null ? '' : value}`;\n  }\n  /** Set internal representation of row height from the user-provided value. */\n  get rowHeight() {\n    return this._rowHeight;\n  }\n  set rowHeight(value) {\n    const newValue = `${value == null ? '' : value}`;\n    if (newValue !== this._rowHeight) {\n      this._rowHeight = newValue;\n      this._setTileStyler(this._rowHeight);\n    }\n  }\n  ngOnInit() {\n    this._checkCols();\n    this._checkRowHeight();\n  }\n  /**\n   * The layout calculation is fairly cheap if nothing changes, so there's little cost\n   * to run it frequently.\n   */\n  ngAfterContentChecked() {\n    this._layoutTiles();\n  }\n  /** Throw a friendly error if cols property is missing */\n  _checkCols() {\n    if (!this.cols && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`mat-grid-list: must pass in number of columns. ` + `Example: <mat-grid-list cols=\"3\">`);\n    }\n  }\n  /** Default to equal width:height if rowHeight property is missing */\n  _checkRowHeight() {\n    if (!this._rowHeight) {\n      this._setTileStyler('1:1');\n    }\n  }\n  /** Creates correct Tile Styler subtype based on rowHeight passed in by user */\n  _setTileStyler(rowHeight) {\n    if (this._tileStyler) {\n      this._tileStyler.reset(this);\n    }\n    if (rowHeight === MAT_FIT_MODE) {\n      this._tileStyler = new FitTileStyler();\n    } else if (rowHeight && rowHeight.indexOf(':') > -1) {\n      this._tileStyler = new RatioTileStyler(rowHeight);\n    } else {\n      this._tileStyler = new FixedTileStyler(rowHeight);\n    }\n  }\n  /** Computes and applies the size and position for all children grid tiles. */\n  _layoutTiles() {\n    if (!this._tileCoordinator) {\n      this._tileCoordinator = new TileCoordinator();\n    }\n    const tracker = this._tileCoordinator;\n    const tiles = this._tiles.filter(tile => !tile._gridList || tile._gridList === this);\n    const direction = this._dir ? this._dir.value : 'ltr';\n    this._tileCoordinator.update(this.cols, tiles);\n    this._tileStyler.init(this.gutterSize, tracker, this.cols, direction);\n    tiles.forEach((tile, index) => {\n      const pos = tracker.positions[index];\n      this._tileStyler.setStyle(tile, pos.row, pos.col);\n    });\n    this._setListStyle(this._tileStyler.getComputedHeight());\n  }\n  /** Sets style on the main grid-list element, given the style name and value. */\n  _setListStyle(style) {\n    if (style) {\n      this._element.nativeElement.style[style[0]] = style[1];\n    }\n  }\n  static ɵfac = function MatGridList_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatGridList)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatGridList,\n    selectors: [[\"mat-grid-list\"]],\n    contentQueries: function MatGridList_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatGridTile, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tiles = _t);\n      }\n    },\n    hostAttrs: [1, \"mat-grid-list\"],\n    hostVars: 1,\n    hostBindings: function MatGridList_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"cols\", ctx.cols);\n      }\n    },\n    inputs: {\n      cols: \"cols\",\n      gutterSize: \"gutterSize\",\n      rowHeight: \"rowHeight\"\n    },\n    exportAs: [\"matGridList\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_GRID_LIST,\n      useExisting: MatGridList\n    }])],\n    ngContentSelectors: _c0,\n    decls: 2,\n    vars: 0,\n    template: function MatGridList_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵdomElementStart(0, \"div\");\n        i0.ɵɵprojection(1);\n        i0.ɵɵdomElementEnd();\n      }\n    },\n    styles: [_c3],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatGridList, [{\n    type: Component,\n    args: [{\n      selector: 'mat-grid-list',\n      exportAs: 'matGridList',\n      host: {\n        'class': 'mat-grid-list',\n        // Ensures that the \"cols\" input value is reflected in the DOM. This is\n        // needed for the grid-list harness.\n        '[attr.cols]': 'cols'\n      },\n      providers: [{\n        provide: MAT_GRID_LIST,\n        useExisting: MatGridList\n      }],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<div>\\n  <ng-content></ng-content>\\n</div>\",\n      styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\\n\"]\n    }]\n  }], () => [], {\n    _tiles: [{\n      type: ContentChildren,\n      args: [MatGridTile, {\n        descendants: true\n      }]\n    }],\n    cols: [{\n      type: Input\n    }],\n    gutterSize: [{\n      type: Input\n    }],\n    rowHeight: [{\n      type: Input\n    }]\n  });\n})();\nclass MatGridListModule {\n  static ɵfac = function MatGridListModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatGridListModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatGridListModule,\n    imports: [MatLineModule, MatCommonModule, MatGridList, MatGridTile, MatGridTileText, MatGridTileHeaderCssMatStyler, MatGridTileFooterCssMatStyler, MatGridAvatarCssMatStyler],\n    exports: [MatGridList, MatGridTile, MatGridTileText, MatLineModule, MatCommonModule, MatGridTileHeaderCssMatStyler, MatGridTileFooterCssMatStyler, MatGridAvatarCssMatStyler]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatLineModule, MatCommonModule, MatLineModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatGridListModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatLineModule, MatCommonModule, MatGridList, MatGridTile, MatGridTileText, MatGridTileHeaderCssMatStyler, MatGridTileFooterCssMatStyler, MatGridAvatarCssMatStyler],\n      exports: [MatGridList, MatGridTile, MatGridTileText, MatLineModule, MatCommonModule, MatGridTileHeaderCssMatStyler, MatGridTileFooterCssMatStyler, MatGridAvatarCssMatStyler]\n    }]\n  }], null, null);\n})();\nexport { MatGridAvatarCssMatStyler, MatGridList, MatGridListModule, MatGridTile, MatGridTileFooterCssMatStyler, MatGridTileHeaderCssMatStyler, MatGridTileText, MatLine };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA,IAAM,kBAAN,MAAsB;AAAA;AAAA,EAElB;AAAA;AAAA,EAEA,cAAc;AAAA;AAAA,EAEd,WAAW;AAAA;AAAA,EAEX,IAAI,WAAW;AACX,WAAO,KAAK,WAAW;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AACV,UAAM,aAAa,KAAK,IAAI,GAAG,KAAK,OAAO;AAG3C,WAAO,aAAa,IAAI,KAAK,WAAW,aAAa,IAAI,KAAK;AAAA,EAClE;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,YAAY,OAAO;AACtB,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,UAAU,IAAI,MAAM,UAAU;AACnC,SAAK,QAAQ,KAAK,GAAG,GAAG,KAAK,QAAQ,MAAM;AAC3C,SAAK,YAAY,MAAM,IAAI,UAAQ,KAAK,WAAW,IAAI,CAAC;AAAA,EAC5D;AAAA;AAAA,EAEA,WAAW,MAAM;AAEb,UAAM,gBAAgB,KAAK,iBAAiB,KAAK,OAAO;AAExD,SAAK,kBAAkB,eAAe,IAAI;AAG1C,SAAK,cAAc,gBAAgB,KAAK;AACxC,WAAO,IAAI,aAAa,KAAK,UAAU,aAAa;AAAA,EACxD;AAAA;AAAA,EAEA,iBAAiB,UAAU;AACvB,QAAI,WAAW,KAAK,QAAQ,WAAW,OAAO,cAAc,eAAe,YAAY;AACnF,YAAM,MAAM,oCAAoC,QAAQ,kCACjC,KAAK,QAAQ,MAAM,IAAI;AAAA,IAClD;AAEA,QAAI,gBAAgB;AACpB,QAAI,cAAc;AAElB,OAAG;AAEC,UAAI,KAAK,cAAc,WAAW,KAAK,QAAQ,QAAQ;AACnD,aAAK,SAAS;AACd,wBAAgB,KAAK,QAAQ,QAAQ,GAAG,KAAK,WAAW;AACxD,sBAAc,KAAK,iBAAiB,aAAa;AACjD;AAAA,MACJ;AACA,sBAAgB,KAAK,QAAQ,QAAQ,GAAG,KAAK,WAAW;AAExD,UAAI,iBAAiB,IAAI;AACrB,aAAK,SAAS;AACd,wBAAgB,KAAK,QAAQ,QAAQ,GAAG,KAAK,WAAW;AACxD,sBAAc,KAAK,iBAAiB,aAAa;AACjD;AAAA,MACJ;AACA,oBAAc,KAAK,iBAAiB,aAAa;AAGjD,WAAK,cAAc,gBAAgB;AAAA,IAGvC,SAAS,cAAc,gBAAgB,YAAY,eAAe;AAGlE,WAAO,KAAK,IAAI,eAAe,CAAC;AAAA,EACpC;AAAA;AAAA,EAEA,WAAW;AACP,SAAK,cAAc;AACnB,SAAK;AAEL,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC1C,WAAK,QAAQ,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,QAAQ,CAAC,IAAI,CAAC;AAAA,IACrD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,eAAe;AAC5B,aAAS,IAAI,gBAAgB,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC1D,UAAI,KAAK,QAAQ,CAAC,KAAK,GAAG;AACtB,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,WAAO,KAAK,QAAQ;AAAA,EACxB;AAAA;AAAA,EAEA,kBAAkB,OAAO,MAAM;AAC3B,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,KAAK;AACnC,WAAK,QAAQ,QAAQ,CAAC,IAAI,KAAK;AAAA,IACnC;AAAA,EACJ;AACJ;AAKA,IAAM,eAAN,MAAmB;AAAA,EACf;AAAA,EACA;AAAA,EACA,YAAY,KAAK,KAAK;AAClB,SAAK,MAAM;AACX,SAAK,MAAM;AAAA,EACf;AACJ;AAGA,IAAM,mBAAmB;;;ACxIzB;AACA;AAIA;AAXA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,CAAC,CAAC,IAAI,mBAAmB,EAAE,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,YAAY,EAAE,GAAG,CAAC,IAAI,WAAW,EAAE,CAAC,GAAG,GAAG;AACvH,IAAM,MAAM,CAAC,sCAAsC,yBAAyB,GAAG;AAC/E,IAAM,MAAM;AAeZ,IAAM,gBAAgB,IAAI,eAAe,eAAe;AACxD,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,WAAW,OAAO,UAAU;AAAA,EAC5B,YAAY,OAAO,eAAe;AAAA,IAChC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,WAAW;AAAA,EACX,WAAW;AAAA,EACX,cAAc;AAAA,EAAC;AAAA;AAAA,EAEf,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,KAAK,MAAM,qBAAqB,KAAK,CAAC;AAAA,EACxD;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,KAAK,MAAM,qBAAqB,KAAK,CAAC;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,UAAU,OAAO;AACzB,SAAK,SAAS,cAAc,MAAM,QAAQ,IAAI;AAAA,EAChD;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,IAC7B,WAAW,CAAC,GAAG,eAAe;AAAA,IAC9B,UAAU;AAAA,IACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,WAAW,IAAI,OAAO,EAAE,WAAW,IAAI,OAAO;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,IACxB,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,uBAAuB,CAAC;AAAA,IACrC,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,kBAAkB,GAAG,OAAO,CAAC;AAChC,QAAG,aAAa,CAAC;AACjB,QAAG,gBAAgB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,69DAA69D;AAAA,IACt+D,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA;AAAA;AAAA,QAGT,kBAAkB;AAAA,QAClB,kBAAkB;AAAA,MACpB;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,QAAQ,CAAC,69DAA69D;AAAA,IACx+D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,WAAW,OAAO,UAAU;AAAA,EAC5B;AAAA,EACA,cAAc;AAAA,EAAC;AAAA,EACf,qBAAqB;AACnB,aAAS,KAAK,QAAQ,KAAK,QAAQ;AAAA,EACrC;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,sBAAsB,GAAG,CAAC,sBAAsB,CAAC;AAAA,IAC9D,gBAAgB,SAAS,+BAA+B,IAAI,KAAK,UAAU;AACzE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,SAAS,CAAC;AAAA,MACxC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS;AAAA,MAC5D;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,oBAAoB,CAAC;AAAA,IAClC,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,aAAa,CAAC;AACjB,QAAG,kBAAkB,GAAG,OAAO,CAAC;AAChC,QAAG,aAAa,GAAG,CAAC;AACpB,QAAG,gBAAgB;AACnB,QAAG,aAAa,GAAG,CAAC;AAAA,MACtB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,QACd,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,OAAO,OAAO,SAAS,kCAAkC,mBAAmB;AAC1E,WAAO,KAAK,qBAAqB,4BAA2B;AAAA,EAC9D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IAClE,WAAW,CAAC,GAAG,iBAAiB;AAAA,EAClC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC,OAAO,OAAO,SAAS,sCAAsC,mBAAmB;AAC9E,WAAO,KAAK,qBAAqB,gCAA+B;AAAA,EAClE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,IACpC,WAAW,CAAC,GAAG,sBAAsB;AAAA,EACvC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC,OAAO,OAAO,SAAS,sCAAsC,mBAAmB;AAC9E,WAAO,KAAK,qBAAqB,gCAA+B;AAAA,EAClE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,IACpC,WAAW,CAAC,GAAG,sBAAsB;AAAA,EACvC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,sBAAsB;AAM5B,IAAM,aAAN,MAAiB;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,EACR,WAAW;AAAA,EACX;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,KAAK,YAAY,SAAS,MAAM,WAAW;AACzC,SAAK,cAAc,eAAe,UAAU;AAC5C,SAAK,QAAQ,QAAQ;AACrB,SAAK,WAAW,QAAQ;AACxB,SAAK,QAAQ;AACb,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,aAAa,gBAAgB;AAM3C,WAAO,IAAI,WAAW,QAAQ,KAAK,WAAW,MAAM,cAAc;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,UAAU,QAAQ;AAGhC,WAAO,WAAW,IAAI,MAAM,KAAK,IAAI,QAAQ,MAAM,KAAK,WAAW,OAAO,MAAM,EAAE;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,UAAU,MAAM;AAC1B,WAAO,IAAI,QAAQ,MAAM,IAAI,QAAQ,OAAO,CAAC,MAAM,KAAK,WAAW;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,MAAM,UAAU,UAAU;AAEjC,QAAI,sBAAsB,MAAM,KAAK;AAGrC,QAAI,8BAA8B,KAAK,QAAQ,KAAK,KAAK;AACzD,SAAK,aAAa,MAAM,UAAU,qBAAqB,0BAA0B;AACjF,SAAK,aAAa,MAAM,UAAU,qBAAqB,0BAA0B;AAAA,EACnF;AAAA;AAAA,EAEA,aAAa,MAAM,UAAU,cAAc,aAAa;AAEtD,QAAI,gBAAgB,KAAK,gBAAgB,cAAc,WAAW;AAGlE,QAAI,OAAO,KAAK,eAAe,QAAQ,UAAU;AACjD,SAAK,UAAU,MAAM,KAAK,gBAAgB,eAAe,QAAQ,CAAC;AAClE,SAAK,UAAU,SAAS,KAAK,KAAK,YAAY,eAAe,KAAK,OAAO,CAAC,CAAC;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,WAAO,GAAG,KAAK,WAAW,OAAO,KAAK,QAAQ;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,YAAY;AACtB,WAAO,GAAG,KAAK,QAAQ,MAAM,KAAK,YAAY,YAAY,CAAC,CAAC;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,WAAO;AAAA,EACT;AACF;AAMA,IAAM,kBAAN,cAA8B,WAAW;AAAA,EACvC;AAAA,EACA,YAAY,gBAAgB;AAC1B,UAAM;AACN,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,KAAK,YAAY,SAAS,MAAM,WAAW;AACzC,UAAM,KAAK,YAAY,SAAS,MAAM,SAAS;AAC/C,SAAK,iBAAiB,eAAe,KAAK,cAAc;AACxD,QAAI,CAAC,oBAAoB,KAAK,KAAK,cAAc,MAAM,OAAO,cAAc,eAAe,YAAY;AACrG,YAAM,MAAM,kBAAkB,KAAK,cAAc,qBAAqB;AAAA,IACxE;AAAA,EACF;AAAA,EACA,aAAa,MAAM,UAAU;AAC3B,SAAK,UAAU,OAAO,KAAK,gBAAgB,KAAK,gBAAgB,QAAQ,CAAC;AACzE,SAAK,UAAU,UAAU,KAAK,KAAK,YAAY,KAAK,gBAAgB,KAAK,OAAO,CAAC,CAAC;AAAA,EACpF;AAAA,EACA,oBAAoB;AAClB,WAAO,CAAC,UAAU,KAAK,GAAG,KAAK,YAAY,KAAK,cAAc,CAAC,MAAM,KAAK,cAAc,CAAC,EAAE,CAAC;AAAA,EAC9F;AAAA,EACA,MAAM,MAAM;AACV,SAAK,cAAc,CAAC,UAAU,IAAI,CAAC;AACnC,QAAI,KAAK,QAAQ;AACf,WAAK,OAAO,QAAQ,UAAQ;AAC1B,aAAK,UAAU,OAAO,IAAI;AAC1B,aAAK,UAAU,UAAU,IAAI;AAAA,MAC/B,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAMA,IAAM,kBAAN,cAA8B,WAAW;AAAA;AAAA,EAEvC;AAAA,EACA;AAAA,EACA,YAAY,OAAO;AACjB,UAAM;AACN,SAAK,YAAY,KAAK;AAAA,EACxB;AAAA,EACA,aAAa,MAAM,UAAU,cAAc,aAAa;AACtD,QAAI,uBAAuB,eAAe,KAAK;AAC/C,SAAK,iBAAiB,KAAK,gBAAgB,sBAAsB,WAAW;AAI5E,SAAK,UAAU,aAAa,KAAK,gBAAgB,KAAK,gBAAgB,QAAQ,CAAC;AAC/E,SAAK,UAAU,cAAc,KAAK,KAAK,YAAY,KAAK,gBAAgB,KAAK,OAAO,CAAC,CAAC;AAAA,EACxF;AAAA,EACA,oBAAoB;AAClB,WAAO,CAAC,iBAAiB,KAAK,GAAG,KAAK,YAAY,KAAK,cAAc,CAAC,MAAM,KAAK,cAAc,CAAC,EAAE,CAAC;AAAA,EACrG;AAAA,EACA,MAAM,MAAM;AACV,SAAK,cAAc,CAAC,iBAAiB,IAAI,CAAC;AAC1C,SAAK,OAAO,QAAQ,UAAQ;AAC1B,WAAK,UAAU,aAAa,IAAI;AAChC,WAAK,UAAU,cAAc,IAAI;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,YAAY,OAAO;AACjB,UAAM,aAAa,MAAM,MAAM,GAAG;AAClC,QAAI,WAAW,WAAW,MAAM,OAAO,cAAc,eAAe,YAAY;AAC9E,YAAM,MAAM,uDAAuD,KAAK,GAAG;AAAA,IAC7E;AACA,SAAK,iBAAiB,WAAW,WAAW,CAAC,CAAC,IAAI,WAAW,WAAW,CAAC,CAAC;AAAA,EAC5E;AACF;AAQA,IAAM,gBAAN,cAA4B,WAAW;AAAA,EACrC,aAAa,MAAM,UAAU;AAE3B,QAAI,uBAAuB,MAAM,KAAK;AAEtC,QAAI,uBAAuB,KAAK,QAAQ,KAAK,KAAK;AAElD,QAAI,iBAAiB,KAAK,gBAAgB,sBAAsB,mBAAmB;AACnF,SAAK,UAAU,OAAO,KAAK,gBAAgB,gBAAgB,QAAQ,CAAC;AACpE,SAAK,UAAU,UAAU,KAAK,KAAK,YAAY,gBAAgB,KAAK,OAAO,CAAC,CAAC;AAAA,EAC/E;AAAA,EACA,MAAM,MAAM;AACV,QAAI,KAAK,QAAQ;AACf,WAAK,OAAO,QAAQ,UAAQ;AAC1B,aAAK,UAAU,OAAO,IAAI;AAC1B,aAAK,UAAU,UAAU,IAAI;AAAA,MAC/B,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,SAAS,KAAK,KAAK;AACjB,SAAO,QAAQ,GAAG;AACpB;AAEA,SAAS,eAAe,OAAO;AAC7B,SAAO,MAAM,MAAM,eAAe,IAAI,QAAQ,GAAG,KAAK;AACxD;AAKA,IAAM,eAAe;AACrB,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,WAAW,OAAO,UAAU;AAAA,EAC5B,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA,EAEA,UAAU;AAAA;AAAA,EAEV;AAAA;AAAA,EAEA;AAAA,EACA,cAAc;AAAA,EAAC;AAAA;AAAA,EAEf,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,OAAO;AACd,SAAK,QAAQ,KAAK,IAAI,GAAG,KAAK,MAAM,qBAAqB,KAAK,CAAC,CAAC;AAAA,EAClE;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,UAAU,GAAG,SAAS,OAAO,KAAK,KAAK;AAAA,EAC9C;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,UAAM,WAAW,GAAG,SAAS,OAAO,KAAK,KAAK;AAC9C,QAAI,aAAa,KAAK,YAAY;AAChC,WAAK,aAAa;AAClB,WAAK,eAAe,KAAK,UAAU;AAAA,IACrC;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,WAAW;AAChB,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA,EAEA,aAAa;AACX,QAAI,CAAC,KAAK,SAAS,OAAO,cAAc,eAAe,YAAY;AACjE,YAAM,MAAM,kFAAuF;AAAA,IACrG;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,eAAe,KAAK;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,WAAW;AACxB,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,MAAM,IAAI;AAAA,IAC7B;AACA,QAAI,cAAc,cAAc;AAC9B,WAAK,cAAc,IAAI,cAAc;AAAA,IACvC,WAAW,aAAa,UAAU,QAAQ,GAAG,IAAI,IAAI;AACnD,WAAK,cAAc,IAAI,gBAAgB,SAAS;AAAA,IAClD,OAAO;AACL,WAAK,cAAc,IAAI,gBAAgB,SAAS;AAAA,IAClD;AAAA,EACF;AAAA;AAAA,EAEA,eAAe;AACb,QAAI,CAAC,KAAK,kBAAkB;AAC1B,WAAK,mBAAmB,IAAI,gBAAgB;AAAA,IAC9C;AACA,UAAM,UAAU,KAAK;AACrB,UAAM,QAAQ,KAAK,OAAO,OAAO,UAAQ,CAAC,KAAK,aAAa,KAAK,cAAc,IAAI;AACnF,UAAM,YAAY,KAAK,OAAO,KAAK,KAAK,QAAQ;AAChD,SAAK,iBAAiB,OAAO,KAAK,MAAM,KAAK;AAC7C,SAAK,YAAY,KAAK,KAAK,YAAY,SAAS,KAAK,MAAM,SAAS;AACpE,UAAM,QAAQ,CAAC,MAAM,UAAU;AAC7B,YAAM,MAAM,QAAQ,UAAU,KAAK;AACnC,WAAK,YAAY,SAAS,MAAM,IAAI,KAAK,IAAI,GAAG;AAAA,IAClD,CAAC;AACD,SAAK,cAAc,KAAK,YAAY,kBAAkB,CAAC;AAAA,EACzD;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,QAAI,OAAO;AACT,WAAK,SAAS,cAAc,MAAM,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,IAC7B,gBAAgB,SAAS,2BAA2B,IAAI,KAAK,UAAU;AACrE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,aAAa,CAAC;AAAA,MAC5C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS;AAAA,MAC5D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,eAAe;AAAA,IAC9B,UAAU;AAAA,IACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,QAAQ,IAAI,IAAI;AAAA,MACjC;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,WAAW;AAAA,IACb;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,IACxB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,IACH,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,kBAAkB,GAAG,KAAK;AAC7B,QAAG,aAAa,CAAC;AACjB,QAAG,gBAAgB;AAAA,MACrB;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,GAAG;AAAA,IACZ,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA;AAAA;AAAA,QAGT,eAAe;AAAA,MACjB;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,QAAQ,CAAC,69DAA69D;AAAA,IACx+D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,eAAe,iBAAiB,aAAa,aAAa,iBAAiB,+BAA+B,+BAA+B,yBAAyB;AAAA,IAC5K,SAAS,CAAC,aAAa,aAAa,iBAAiB,eAAe,iBAAiB,+BAA+B,+BAA+B,yBAAyB;AAAA,EAC9K,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,eAAe,iBAAiB,eAAe,eAAe;AAAA,EAC1E,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,eAAe,iBAAiB,aAAa,aAAa,iBAAiB,+BAA+B,+BAA+B,yBAAyB;AAAA,MAC5K,SAAS,CAAC,aAAa,aAAa,iBAAiB,eAAe,iBAAiB,+BAA+B,+BAA+B,yBAAyB;AAAA,IAC9K,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}
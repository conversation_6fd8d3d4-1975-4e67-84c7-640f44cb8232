import {
  matSelectAnimations
} from "./chunk-W7VW6W5G.js";
import {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger
} from "./chunk-PL7FGCFP.js";
import {
  MatOptgroup,
  MatOption
} from "./chunk-YRYNL6RK.js";
import "./chunk-JXY6IMKW.js";
import "./chunk-PCM3ZV55.js";
import "./chunk-XA3EVXH3.js";
import "./chunk-ZLRBTOGV.js";
import {
  Mat<PERSON><PERSON>r,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>refix,
  MatSuffix
} from "./chunk-7Q5MVAYX.js";
import "./chunk-6WOOFAIE.js";
import "./chunk-3PDZ4P6Y.js";
import "./chunk-WC44IMZZ.js";
import "./chunk-FXYI3MHX.js";
import "./chunk-I6D73JZR.js";
import "./chunk-PJXVVDJU.js";
import "./chunk-NH5K7WKZ.js";
import "./chunk-3ABE436P.js";
import "./chunk-VENV3F3G.js";
import "./chunk-ASGGP7ZP.js";
import "./chunk-KFGWFW3D.js";
import "./chunk-N6ZT3LDK.js";
import "./chunk-I5ACE36H.js";
import "./chunk-2H75U2CE.js";
import "./chunk-NINB62GJ.js";
import "./chunk-2ZKSKDON.js";
import "./chunk-L7SPBUC7.js";
import "./chunk-LOMOHIKG.js";
import "./chunk-US4SVPPB.js";
import "./chunk-NRYQPY3W.js";
import "./chunk-7UJZXIJQ.js";
import "./chunk-XSYV5OGZ.js";
import "./chunk-A7WVRDWP.js";
import "./chunk-6UYTHYI5.js";
import "./chunk-QVBVXVIF.js";
import "./chunk-TEPKXNLU.js";
import "./chunk-4RF3VZXG.js";
import "./chunk-WUNHS5KN.js";
import "./chunk-N726T63C.js";
import "./chunk-H5IUTQT7.js";
import "./chunk-CTL5GG7J.js";
import "./chunk-WOR4A3D2.js";
export {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatError,
  MatFormField,
  MatHint,
  MatLabel,
  MatOptgroup,
  MatOption,
  MatPrefix,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  MatSuffix,
  matSelectAnimations
};

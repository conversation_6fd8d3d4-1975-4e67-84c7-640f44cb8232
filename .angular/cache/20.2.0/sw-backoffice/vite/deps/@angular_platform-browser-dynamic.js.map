{"version": 3, "sources": ["../../../../../../node_modules/@angular/platform-browser-dynamic/fesm2022/platform-browser-dynamic.mjs"], "sourcesContent": ["/**\n * @license Angular v20.2.1\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Version, Injector, Compiler, ViewEncapsulation, Injectable, createPlatformFactory, COMPILER_OPTIONS, CompilerFactory } from '@angular/core';\nimport { CompilerConfig, ResourceLoader } from '@angular/compiler';\nimport { platformBrowser } from '@angular/platform-browser';\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser-dynamic package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('20.2.1');\nconst COMPILER_PROVIDERS = [{\n  provide: Compiler,\n  useFactory: () => new Compiler()\n}];\n/**\n * @publicApi\n *\n * @deprecated\n * Ivy JIT mode doesn't require accessing this symbol.\n */\nclass JitCompilerFactory {\n  _defaultOptions;\n  /** @internal */\n  constructor(defaultOptions) {\n    const compilerOptions = {\n      defaultEncapsulation: ViewEncapsulation.Emulated\n    };\n    this._defaultOptions = [compilerOptions, ...defaultOptions];\n  }\n  createCompiler(options = []) {\n    const opts = _mergeOptions(this._defaultOptions.concat(options));\n    const injector = Injector.create({\n      providers: [COMPILER_PROVIDERS, {\n        provide: CompilerConfig,\n        useFactory: () => {\n          return new CompilerConfig({\n            defaultEncapsulation: opts.defaultEncapsulation,\n            preserveWhitespaces: opts.preserveWhitespaces\n          });\n        },\n        deps: []\n      }, opts.providers]\n    });\n    return injector.get(Compiler);\n  }\n}\nfunction _mergeOptions(optionsArr) {\n  return {\n    defaultEncapsulation: _lastDefined(optionsArr.map(options => options.defaultEncapsulation)),\n    providers: _mergeArrays(optionsArr.map(options => options.providers)),\n    preserveWhitespaces: _lastDefined(optionsArr.map(options => options.preserveWhitespaces))\n  };\n}\nfunction _lastDefined(args) {\n  for (let i = args.length - 1; i >= 0; i--) {\n    if (args[i] !== undefined) {\n      return args[i];\n    }\n  }\n  return undefined;\n}\nfunction _mergeArrays(parts) {\n  const result = [];\n  parts.forEach(part => part && result.push(...part));\n  return result;\n}\nclass ResourceLoaderImpl extends ResourceLoader {\n  get(url) {\n    let resolve;\n    let reject;\n    const promise = new Promise((res, rej) => {\n      resolve = res;\n      reject = rej;\n    });\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', url, true);\n    xhr.responseType = 'text';\n    xhr.onload = function () {\n      const response = xhr.response;\n      let status = xhr.status;\n      // fix status code when it is 0 (0 status is undocumented).\n      // Occurs when accessing file resources or on Android 4.1 stock browser\n      // while retrieving files from application cache.\n      if (status === 0) {\n        status = response ? 200 : 0;\n      }\n      if (200 <= status && status <= 300) {\n        resolve(response);\n      } else {\n        reject(`Failed to load ${url}`);\n      }\n    };\n    xhr.onerror = function () {\n      reject(`Failed to load ${url}`);\n    };\n    xhr.send();\n    return promise;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵResourceLoaderImpl_BaseFactory;\n    return function ResourceLoaderImpl_Factory(__ngFactoryType__) {\n      return (ɵResourceLoaderImpl_BaseFactory || (ɵResourceLoaderImpl_BaseFactory = i0.ɵɵgetInheritedFactory(ResourceLoaderImpl)))(__ngFactoryType__ || ResourceLoaderImpl);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ResourceLoaderImpl,\n    factory: ResourceLoaderImpl.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResourceLoaderImpl, [{\n    type: Injectable\n  }], null, null);\n})();\nconst INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS = [{\n  provide: COMPILER_OPTIONS,\n  useValue: {\n    providers: [{\n      provide: ResourceLoader,\n      useClass: ResourceLoaderImpl,\n      deps: []\n    }]\n  },\n  multi: true\n}, {\n  provide: CompilerFactory,\n  useClass: JitCompilerFactory,\n  deps: [COMPILER_OPTIONS]\n}];\n/**\n * @deprecated Use the `platformBrowser` function instead from `@angular/platform-browser`.\n * In case you are not in a CLI app and rely on JIT compilation, you will also need to import `@angular/compiler`\n */\nconst platformBrowserDynamic = createPlatformFactory(platformBrowser, 'browserDynamic', INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\nexport { JitCompilerFactory, VERSION, platformBrowserDynamic };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA;AACA;AAEA;AAUA,IAAM,UAAU,IAAI,QAAQ,QAAQ;AACpC,IAAM,qBAAqB,CAAC;AAAA,EAC1B,SAAS;AAAA,EACT,YAAY,MAAM,IAAI,SAAS;AACjC,CAAC;AAOD,IAAM,qBAAN,MAAyB;AAAA,EACvB;AAAA;AAAA,EAEA,YAAY,gBAAgB;AAC1B,UAAM,kBAAkB;AAAA,MACtB,sBAAsB,kBAAkB;AAAA,IAC1C;AACA,SAAK,kBAAkB,CAAC,iBAAiB,GAAG,cAAc;AAAA,EAC5D;AAAA,EACA,eAAe,UAAU,CAAC,GAAG;AAC3B,UAAM,OAAO,cAAc,KAAK,gBAAgB,OAAO,OAAO,CAAC;AAC/D,UAAM,WAAW,SAAS,OAAO;AAAA,MAC/B,WAAW,CAAC,oBAAoB;AAAA,QAC9B,SAAS;AAAA,QACT,YAAY,MAAM;AAChB,iBAAO,IAAI,eAAe;AAAA,YACxB,sBAAsB,KAAK;AAAA,YAC3B,qBAAqB,KAAK;AAAA,UAC5B,CAAC;AAAA,QACH;AAAA,QACA,MAAM,CAAC;AAAA,MACT,GAAG,KAAK,SAAS;AAAA,IACnB,CAAC;AACD,WAAO,SAAS,IAAI,QAAQ;AAAA,EAC9B;AACF;AACA,SAAS,cAAc,YAAY;AACjC,SAAO;AAAA,IACL,sBAAsB,aAAa,WAAW,IAAI,aAAW,QAAQ,oBAAoB,CAAC;AAAA,IAC1F,WAAW,aAAa,WAAW,IAAI,aAAW,QAAQ,SAAS,CAAC;AAAA,IACpE,qBAAqB,aAAa,WAAW,IAAI,aAAW,QAAQ,mBAAmB,CAAC;AAAA,EAC1F;AACF;AACA,SAAS,aAAa,MAAM;AAC1B,WAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,QAAI,KAAK,CAAC,MAAM,QAAW;AACzB,aAAO,KAAK,CAAC;AAAA,IACf;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,aAAa,OAAO;AAC3B,QAAM,SAAS,CAAC;AAChB,QAAM,QAAQ,UAAQ,QAAQ,OAAO,KAAK,GAAG,IAAI,CAAC;AAClD,SAAO;AACT;AACA,IAAM,qBAAN,MAAM,4BAA2B,eAAe;AAAA,EAC9C,IAAI,KAAK;AACP,QAAI;AACJ,QAAI;AACJ,UAAM,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAQ;AACxC,gBAAU;AACV,eAAS;AAAA,IACX,CAAC;AACD,UAAM,MAAM,IAAI,eAAe;AAC/B,QAAI,KAAK,OAAO,KAAK,IAAI;AACzB,QAAI,eAAe;AACnB,QAAI,SAAS,WAAY;AACvB,YAAM,WAAW,IAAI;AACrB,UAAI,SAAS,IAAI;AAIjB,UAAI,WAAW,GAAG;AAChB,iBAAS,WAAW,MAAM;AAAA,MAC5B;AACA,UAAI,OAAO,UAAU,UAAU,KAAK;AAClC,gBAAQ,QAAQ;AAAA,MAClB,OAAO;AACL,eAAO,kBAAkB,GAAG,EAAE;AAAA,MAChC;AAAA,IACF;AACA,QAAI,UAAU,WAAY;AACxB,aAAO,kBAAkB,GAAG,EAAE;AAAA,IAChC;AACA,QAAI,KAAK;AACT,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,2BAA2B,mBAAmB;AAC5D,cAAQ,oCAAoC,kCAAqC,sBAAsB,mBAAkB,IAAI,qBAAqB,mBAAkB;AAAA,IACtK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,oBAAmB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,8CAA8C,CAAC;AAAA,EACnD,SAAS;AAAA,EACT,UAAU;AAAA,IACR,WAAW,CAAC;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,MAAM,CAAC;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACT,GAAG;AAAA,EACD,SAAS;AAAA,EACT,UAAU;AAAA,EACV,MAAM,CAAC,gBAAgB;AACzB,CAAC;AAKD,IAAM,yBAAyB,sBAAsB,iBAAiB,kBAAkB,2CAA2C;", "names": []}
{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/portal.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { ElementRef, NgModuleRef, EnvironmentInjector, createComponent, Injector, inject, TemplateRef, ViewContainerRef, Directive, DOCUMENT, EventEmitter, Input, Output, NgModule } from '@angular/core';\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\nfunction throwNullPortalError() {\n  throw Error('Must provide a portal to attach');\n}\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\nfunction throwPortalAlreadyAttachedError() {\n  throw Error('Host already has a portal attached');\n}\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\nfunction throwPortalOutletAlreadyDisposedError() {\n  throw Error('This PortalOutlet has already been disposed');\n}\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\nfunction throwUnknownPortalTypeError() {\n  throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' + 'a ComponentPortal or a TemplatePortal.');\n}\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\nfunction throwNullPortalOutletError() {\n  throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\nfunction throwNoPortalAttachedError() {\n  throw Error('Attempting to detach a portal that is not attached to a host');\n}\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\nclass Portal {\n  _attachedHost;\n  /** Attach this portal to a host. */\n  attach(host) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (host == null) {\n        throwNullPortalOutletError();\n      }\n      if (host.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n    }\n    this._attachedHost = host;\n    return host.attach(this);\n  }\n  /** Detach this portal from its host */\n  detach() {\n    let host = this._attachedHost;\n    if (host != null) {\n      this._attachedHost = null;\n      host.detach();\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwNoPortalAttachedError();\n    }\n  }\n  /** Whether this portal is attached to a host. */\n  get isAttached() {\n    return this._attachedHost != null;\n  }\n  /**\n   * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n   * the PortalOutlet when it is performing an `attach()` or `detach()`.\n   */\n  setAttachedHost(host) {\n    this._attachedHost = host;\n  }\n}\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal extends Portal {\n  /** The type of the component that will be instantiated for attachment. */\n  component;\n  /**\n   * Where the attached component should live in Angular's *logical* component tree.\n   * This is different from where the component *renders*, which is determined by the PortalOutlet.\n   * The origin is necessary when the host is outside of the Angular application context.\n   */\n  viewContainerRef;\n  /** Injector used for the instantiation of the component. */\n  injector;\n  /**\n   * List of DOM nodes that should be projected through `<ng-content>` of the attached component.\n   */\n  projectableNodes;\n  constructor(component, viewContainerRef, injector, projectableNodes) {\n    super();\n    this.component = component;\n    this.viewContainerRef = viewContainerRef;\n    this.injector = injector;\n    this.projectableNodes = projectableNodes;\n  }\n}\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\nclass TemplatePortal extends Portal {\n  templateRef;\n  viewContainerRef;\n  context;\n  injector;\n  constructor(/** The embedded template that will be used to instantiate an embedded View in the host. */\n  templateRef, /** Reference to the ViewContainer into which the template will be stamped out. */\n  viewContainerRef, /** Contextual data to be passed in to the embedded view. */\n  context, /** The injector to use for the embedded view. */\n  injector) {\n    super();\n    this.templateRef = templateRef;\n    this.viewContainerRef = viewContainerRef;\n    this.context = context;\n    this.injector = injector;\n  }\n  get origin() {\n    return this.templateRef.elementRef;\n  }\n  /**\n   * Attach the portal to the provided `PortalOutlet`.\n   * When a context is provided it will override the `context` property of the `TemplatePortal`\n   * instance.\n   */\n  attach(host, context = this.context) {\n    this.context = context;\n    return super.attach(host);\n  }\n  detach() {\n    this.context = undefined;\n    return super.detach();\n  }\n}\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\nclass DomPortal extends Portal {\n  /** DOM node hosting the portal's content. */\n  element;\n  constructor(element) {\n    super();\n    this.element = element instanceof ElementRef ? element.nativeElement : element;\n  }\n}\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\nclass BasePortalOutlet {\n  /** The portal currently attached to the host. */\n  _attachedPortal;\n  /** A function that will permanently dispose this host. */\n  _disposeFn;\n  /** Whether this host has already been permanently disposed. */\n  _isDisposed = false;\n  /** Whether this host has an attached portal. */\n  hasAttached() {\n    return !!this._attachedPortal;\n  }\n  /** Attaches a portal. */\n  attach(portal) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!portal) {\n        throwNullPortalError();\n      }\n      if (this.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n      if (this._isDisposed) {\n        throwPortalOutletAlreadyDisposedError();\n      }\n    }\n    if (portal instanceof ComponentPortal) {\n      this._attachedPortal = portal;\n      return this.attachComponentPortal(portal);\n    } else if (portal instanceof TemplatePortal) {\n      this._attachedPortal = portal;\n      return this.attachTemplatePortal(portal);\n      // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n    } else if (this.attachDomPortal && portal instanceof DomPortal) {\n      this._attachedPortal = portal;\n      return this.attachDomPortal(portal);\n    }\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwUnknownPortalTypeError();\n    }\n  }\n  // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n  attachDomPortal = null;\n  /** Detaches a previously attached portal. */\n  detach() {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost(null);\n      this._attachedPortal = null;\n    }\n    this._invokeDisposeFn();\n  }\n  /** Permanently dispose of this portal host. */\n  dispose() {\n    if (this.hasAttached()) {\n      this.detach();\n    }\n    this._invokeDisposeFn();\n    this._isDisposed = true;\n  }\n  /** @docs-private */\n  setDisposeFn(fn) {\n    this._disposeFn = fn;\n  }\n  _invokeDisposeFn() {\n    if (this._disposeFn) {\n      this._disposeFn();\n      this._disposeFn = null;\n    }\n  }\n}\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\nclass DomPortalOutlet extends BasePortalOutlet {\n  outletElement;\n  _appRef;\n  _defaultInjector;\n  /**\n   * @param outletElement Element into which the content is projected.\n   * @param _appRef Reference to the application. Only used in component portals when there\n   *   is no `ViewContainerRef` available.\n   * @param _defaultInjector Injector to use as a fallback when the portal being attached doesn't\n   *   have one. Only used for component portals.\n   */\n  constructor(/** Element into which the content is projected. */\n  outletElement, _appRef, _defaultInjector) {\n    super();\n    this.outletElement = outletElement;\n    this._appRef = _appRef;\n    this._defaultInjector = _defaultInjector;\n  }\n  /**\n   * Attach the given ComponentPortal to DOM element.\n   * @param portal Portal to be attached\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal(portal) {\n    let componentRef;\n    // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the view to the application.\n    if (portal.viewContainerRef) {\n      const injector = portal.injector || portal.viewContainerRef.injector;\n      const ngModuleRef = injector.get(NgModuleRef, null, {\n        optional: true\n      }) || undefined;\n      componentRef = portal.viewContainerRef.createComponent(portal.component, {\n        index: portal.viewContainerRef.length,\n        injector,\n        ngModuleRef,\n        projectableNodes: portal.projectableNodes || undefined\n      });\n      this.setDisposeFn(() => componentRef.destroy());\n    } else {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._appRef) {\n        throw Error('Cannot attach component portal to outlet without an ApplicationRef.');\n      }\n      const appRef = this._appRef;\n      const elementInjector = portal.injector || this._defaultInjector || Injector.NULL;\n      const environmentInjector = elementInjector.get(EnvironmentInjector, appRef.injector);\n      componentRef = createComponent(portal.component, {\n        elementInjector,\n        environmentInjector,\n        projectableNodes: portal.projectableNodes || undefined\n      });\n      appRef.attachView(componentRef.hostView);\n      this.setDisposeFn(() => {\n        // Verify that the ApplicationRef has registered views before trying to detach a host view.\n        // This check also protects the `detachView` from being called on a destroyed ApplicationRef.\n        if (appRef.viewCount > 0) {\n          appRef.detachView(componentRef.hostView);\n        }\n        componentRef.destroy();\n      });\n    }\n    // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n    this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n    this._attachedPortal = portal;\n    return componentRef;\n  }\n  /**\n   * Attaches a template portal to the DOM as an embedded view.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal(portal) {\n    let viewContainer = portal.viewContainerRef;\n    let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector\n    });\n    // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n    // But for the DomPortalOutlet the view can be added everywhere in the DOM\n    // (e.g Overlay Container) To move the view to the specified host element. We just\n    // re-append the existing root nodes.\n    viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode));\n    // Note that we want to detect changes after the nodes have been moved so that\n    // any directives inside the portal that are looking at the DOM inside a lifecycle\n    // hook won't be invoked too early.\n    viewRef.detectChanges();\n    this.setDisposeFn(() => {\n      let index = viewContainer.indexOf(viewRef);\n      if (index !== -1) {\n        viewContainer.remove(index);\n      }\n    });\n    this._attachedPortal = portal;\n    // TODO(jelbourn): Return locals from view.\n    return viewRef;\n  }\n  /**\n   * Attaches a DOM portal by transferring its content into the outlet.\n   * @param portal Portal to be attached.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  attachDomPortal = portal => {\n    const element = portal.element;\n    if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('DOM portal content must be attached to a parent node.');\n    }\n    // Anchor used to save the element's previous position so\n    // that we can restore it when the portal is detached.\n    const anchorNode = this.outletElement.ownerDocument.createComment('dom-portal');\n    element.parentNode.insertBefore(anchorNode, element);\n    this.outletElement.appendChild(element);\n    this._attachedPortal = portal;\n    super.setDisposeFn(() => {\n      // We can't use `replaceWith` here because IE doesn't support it.\n      if (anchorNode.parentNode) {\n        anchorNode.parentNode.replaceChild(element, anchorNode);\n      }\n    });\n  };\n  /**\n   * Clears out a portal from the DOM.\n   */\n  dispose() {\n    super.dispose();\n    this.outletElement.remove();\n  }\n  /** Gets the root HTMLElement for an instantiated component. */\n  _getComponentRootNode(componentRef) {\n    return componentRef.hostView.rootNodes[0];\n  }\n}\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\nclass CdkPortal extends TemplatePortal {\n  constructor() {\n    const templateRef = inject(TemplateRef);\n    const viewContainerRef = inject(ViewContainerRef);\n    super(templateRef, viewContainerRef);\n  }\n  static ɵfac = function CdkPortal_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkPortal)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkPortal,\n    selectors: [[\"\", \"cdkPortal\", \"\"]],\n    exportAs: [\"cdkPortal\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkPortal, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortal]',\n      exportAs: 'cdkPortal'\n    }]\n  }], () => [], null);\n})();\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\nclass TemplatePortalDirective extends CdkPortal {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵTemplatePortalDirective_BaseFactory;\n    return function TemplatePortalDirective_Factory(__ngFactoryType__) {\n      return (ɵTemplatePortalDirective_BaseFactory || (ɵTemplatePortalDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TemplatePortalDirective)))(__ngFactoryType__ || TemplatePortalDirective);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: TemplatePortalDirective,\n    selectors: [[\"\", \"cdk-portal\", \"\"], [\"\", \"portal\", \"\"]],\n    exportAs: [\"cdkPortal\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkPortal,\n      useExisting: TemplatePortalDirective\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TemplatePortalDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-portal], [portal]',\n      exportAs: 'cdkPortal',\n      providers: [{\n        provide: CdkPortal,\n        useExisting: TemplatePortalDirective\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\nclass CdkPortalOutlet extends BasePortalOutlet {\n  _moduleRef = inject(NgModuleRef, {\n    optional: true\n  });\n  _document = inject(DOCUMENT);\n  _viewContainerRef = inject(ViewContainerRef);\n  /** Whether the portal component is initialized. */\n  _isInitialized = false;\n  /** Reference to the currently-attached component/view ref. */\n  _attachedRef;\n  constructor() {\n    super();\n  }\n  /** Portal associated with the Portal outlet. */\n  get portal() {\n    return this._attachedPortal;\n  }\n  set portal(portal) {\n    // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n    // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n    // and attach a portal programmatically in the parent component. When Angular does the first CD\n    // round, it will fire the setter with empty string, causing the user's content to be cleared.\n    if (this.hasAttached() && !portal && !this._isInitialized) {\n      return;\n    }\n    if (this.hasAttached()) {\n      super.detach();\n    }\n    if (portal) {\n      super.attach(portal);\n    }\n    this._attachedPortal = portal || null;\n  }\n  /** Emits when a portal is attached to the outlet. */\n  attached = new EventEmitter();\n  /** Component or view reference that is attached to the portal. */\n  get attachedRef() {\n    return this._attachedRef;\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n  }\n  ngOnDestroy() {\n    super.dispose();\n    this._attachedRef = this._attachedPortal = null;\n  }\n  /**\n   * Attach the given ComponentPortal to this PortalOutlet.\n   *\n   * @param portal Portal to be attached to the portal outlet.\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal(portal) {\n    portal.setAttachedHost(this);\n    // If the portal specifies an origin, use that as the logical location of the component\n    // in the application tree. Otherwise use the location of this PortalOutlet.\n    const viewContainerRef = portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n    const ref = viewContainerRef.createComponent(portal.component, {\n      index: viewContainerRef.length,\n      injector: portal.injector || viewContainerRef.injector,\n      projectableNodes: portal.projectableNodes || undefined,\n      ngModuleRef: this._moduleRef || undefined\n    });\n    // If we're using a view container that's different from the injected one (e.g. when the portal\n    // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n    // inside of the alternate view container.\n    if (viewContainerRef !== this._viewContainerRef) {\n      this._getRootNode().appendChild(ref.hostView.rootNodes[0]);\n    }\n    super.setDisposeFn(() => ref.destroy());\n    this._attachedPortal = portal;\n    this._attachedRef = ref;\n    this.attached.emit(ref);\n    return ref;\n  }\n  /**\n   * Attach the given TemplatePortal to this PortalHost as an embedded View.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal(portal) {\n    portal.setAttachedHost(this);\n    const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector\n    });\n    super.setDisposeFn(() => this._viewContainerRef.clear());\n    this._attachedPortal = portal;\n    this._attachedRef = viewRef;\n    this.attached.emit(viewRef);\n    return viewRef;\n  }\n  /**\n   * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n   * @param portal Portal to be attached.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  attachDomPortal = portal => {\n    const element = portal.element;\n    if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('DOM portal content must be attached to a parent node.');\n    }\n    // Anchor used to save the element's previous position so\n    // that we can restore it when the portal is detached.\n    const anchorNode = this._document.createComment('dom-portal');\n    portal.setAttachedHost(this);\n    element.parentNode.insertBefore(anchorNode, element);\n    this._getRootNode().appendChild(element);\n    this._attachedPortal = portal;\n    super.setDisposeFn(() => {\n      if (anchorNode.parentNode) {\n        anchorNode.parentNode.replaceChild(element, anchorNode);\n      }\n    });\n  };\n  /** Gets the root node of the portal outlet. */\n  _getRootNode() {\n    const nativeElement = this._viewContainerRef.element.nativeElement;\n    // The directive could be set on a template which will result in a comment\n    // node being the root. Use the comment's parent node if that is the case.\n    return nativeElement.nodeType === nativeElement.ELEMENT_NODE ? nativeElement : nativeElement.parentNode;\n  }\n  static ɵfac = function CdkPortalOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkPortalOutlet)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkPortalOutlet,\n    selectors: [[\"\", \"cdkPortalOutlet\", \"\"]],\n    inputs: {\n      portal: [0, \"cdkPortalOutlet\", \"portal\"]\n    },\n    outputs: {\n      attached: \"attached\"\n    },\n    exportAs: [\"cdkPortalOutlet\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkPortalOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortalOutlet]',\n      exportAs: 'cdkPortalOutlet'\n    }]\n  }], () => [], {\n    portal: [{\n      type: Input,\n      args: ['cdkPortalOutlet']\n    }],\n    attached: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass PortalHostDirective extends CdkPortalOutlet {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵPortalHostDirective_BaseFactory;\n    return function PortalHostDirective_Factory(__ngFactoryType__) {\n      return (ɵPortalHostDirective_BaseFactory || (ɵPortalHostDirective_BaseFactory = i0.ɵɵgetInheritedFactory(PortalHostDirective)))(__ngFactoryType__ || PortalHostDirective);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: PortalHostDirective,\n    selectors: [[\"\", \"cdkPortalHost\", \"\"], [\"\", \"portalHost\", \"\"]],\n    inputs: {\n      portal: [0, \"cdkPortalHost\", \"portal\"]\n    },\n    exportAs: [\"cdkPortalHost\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkPortalOutlet,\n      useExisting: PortalHostDirective\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PortalHostDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortalHost], [portalHost]',\n      exportAs: 'cdkPortalHost',\n      inputs: [{\n        name: 'portal',\n        alias: 'cdkPortalHost'\n      }],\n      providers: [{\n        provide: CdkPortalOutlet,\n        useExisting: PortalHostDirective\n      }]\n    }]\n  }], null, null);\n})();\nclass PortalModule {\n  static ɵfac = function PortalModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PortalModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: PortalModule,\n    imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n    exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PortalModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n      exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective]\n    }]\n  }], null, null);\n})();\nexport { BasePortalOutlet, CdkPortal, CdkPortalOutlet, ComponentPortal, DomPortal, DomPortalOutlet, Portal, PortalHostDirective, PortalModule, TemplatePortal, TemplatePortalDirective };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAMA,SAAS,uBAAuB;AAC9B,QAAM,MAAM,iCAAiC;AAC/C;AAKA,SAAS,kCAAkC;AACzC,QAAM,MAAM,oCAAoC;AAClD;AAKA,SAAS,wCAAwC;AAC/C,QAAM,MAAM,6CAA6C;AAC3D;AAKA,SAAS,8BAA8B;AACrC,QAAM,MAAM,qHAA0H;AACxI;AAKA,SAAS,6BAA6B;AACpC,QAAM,MAAM,sDAAsD;AACpE;AAKA,SAAS,6BAA6B;AACpC,QAAM,MAAM,8DAA8D;AAC5E;AAMA,IAAM,SAAN,MAAa;AAAA,EACX;AAAA;AAAA,EAEA,OAAO,MAAM;AACX,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,QAAQ,MAAM;AAChB,mCAA2B;AAAA,MAC7B;AACA,UAAI,KAAK,YAAY,GAAG;AACtB,wCAAgC;AAAA,MAClC;AAAA,IACF;AACA,SAAK,gBAAgB;AACrB,WAAO,KAAK,OAAO,IAAI;AAAA,EACzB;AAAA;AAAA,EAEA,SAAS;AACP,QAAI,OAAO,KAAK;AAChB,QAAI,QAAQ,MAAM;AAChB,WAAK,gBAAgB;AACrB,WAAK,OAAO;AAAA,IACd,WAAW,OAAO,cAAc,eAAe,WAAW;AACxD,iCAA2B;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,MAAM;AACpB,SAAK,gBAAgB;AAAA,EACvB;AACF;AAIA,IAAM,kBAAN,cAA8B,OAAO;AAAA;AAAA,EAEnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,EACA,YAAY,WAAW,kBAAkB,UAAU,kBAAkB;AACnE,UAAM;AACN,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAChB,SAAK,mBAAmB;AAAA,EAC1B;AACF;AAIA,IAAM,iBAAN,cAA6B,OAAO;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YACA,aACA,kBACA,SACA,UAAU;AACR,UAAM;AACN,SAAK,cAAc;AACnB,SAAK,mBAAmB;AACxB,SAAK,UAAU;AACf,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,MAAM,UAAU,KAAK,SAAS;AACnC,SAAK,UAAU;AACf,WAAO,MAAM,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AACP,SAAK,UAAU;AACf,WAAO,MAAM,OAAO;AAAA,EACtB;AACF;AAMA,IAAM,YAAN,cAAwB,OAAO;AAAA;AAAA,EAE7B;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AACN,SAAK,UAAU,mBAAmB,aAAa,QAAQ,gBAAgB;AAAA,EACzE;AACF;AAKA,IAAM,mBAAN,MAAuB;AAAA;AAAA,EAErB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,cAAc;AAAA;AAAA,EAEd,cAAc;AACZ,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA;AAAA,EAEA,OAAO,QAAQ;AACb,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,QAAQ;AACX,6BAAqB;AAAA,MACvB;AACA,UAAI,KAAK,YAAY,GAAG;AACtB,wCAAgC;AAAA,MAClC;AACA,UAAI,KAAK,aAAa;AACpB,8CAAsC;AAAA,MACxC;AAAA,IACF;AACA,QAAI,kBAAkB,iBAAiB;AACrC,WAAK,kBAAkB;AACvB,aAAO,KAAK,sBAAsB,MAAM;AAAA,IAC1C,WAAW,kBAAkB,gBAAgB;AAC3C,WAAK,kBAAkB;AACvB,aAAO,KAAK,qBAAqB,MAAM;AAAA,IAEzC,WAAW,KAAK,mBAAmB,kBAAkB,WAAW;AAC9D,WAAK,kBAAkB;AACvB,aAAO,KAAK,gBAAgB,MAAM;AAAA,IACpC;AACA,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,kCAA4B;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAAA;AAAA,EAElB,SAAS;AACP,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,gBAAgB,IAAI;AACzC,WAAK,kBAAkB;AAAA,IACzB;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,YAAY,GAAG;AACtB,WAAK,OAAO;AAAA,IACd;AACA,SAAK,iBAAiB;AACtB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,aAAa,IAAI;AACf,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW;AAChB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AACF;AAMA,IAAM,kBAAN,cAA8B,iBAAiB;AAAA,EAC7C;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YACA,eAAe,SAAS,kBAAkB;AACxC,UAAM;AACN,SAAK,gBAAgB;AACrB,SAAK,UAAU;AACf,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,QAAQ;AAC5B,QAAI;AAKJ,QAAI,OAAO,kBAAkB;AAC3B,YAAM,WAAW,OAAO,YAAY,OAAO,iBAAiB;AAC5D,YAAM,cAAc,SAAS,IAAI,eAAa,MAAM;AAAA,QAClD,UAAU;AAAA,MACZ,CAAC,KAAK;AACN,qBAAe,OAAO,iBAAiB,gBAAgB,OAAO,WAAW;AAAA,QACvE,OAAO,OAAO,iBAAiB;AAAA,QAC/B;AAAA,QACA;AAAA,QACA,kBAAkB,OAAO,oBAAoB;AAAA,MAC/C,CAAC;AACD,WAAK,aAAa,MAAM,aAAa,QAAQ,CAAC;AAAA,IAChD,OAAO;AACL,WAAK,OAAO,cAAc,eAAe,cAAc,CAAC,KAAK,SAAS;AACpE,cAAM,MAAM,qEAAqE;AAAA,MACnF;AACA,YAAM,SAAS,KAAK;AACpB,YAAM,kBAAkB,OAAO,YAAY,KAAK,oBAAoB,SAAS;AAC7E,YAAM,sBAAsB,gBAAgB,IAAI,qBAAqB,OAAO,QAAQ;AACpF,qBAAe,gBAAgB,OAAO,WAAW;AAAA,QAC/C;AAAA,QACA;AAAA,QACA,kBAAkB,OAAO,oBAAoB;AAAA,MAC/C,CAAC;AACD,aAAO,WAAW,aAAa,QAAQ;AACvC,WAAK,aAAa,MAAM;AAGtB,YAAI,OAAO,YAAY,GAAG;AACxB,iBAAO,WAAW,aAAa,QAAQ;AAAA,QACzC;AACA,qBAAa,QAAQ;AAAA,MACvB,CAAC;AAAA,IACH;AAGA,SAAK,cAAc,YAAY,KAAK,sBAAsB,YAAY,CAAC;AACvE,SAAK,kBAAkB;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,QAAQ;AAC3B,QAAI,gBAAgB,OAAO;AAC3B,QAAI,UAAU,cAAc,mBAAmB,OAAO,aAAa,OAAO,SAAS;AAAA,MACjF,UAAU,OAAO;AAAA,IACnB,CAAC;AAKD,YAAQ,UAAU,QAAQ,cAAY,KAAK,cAAc,YAAY,QAAQ,CAAC;AAI9E,YAAQ,cAAc;AACtB,SAAK,aAAa,MAAM;AACtB,UAAI,QAAQ,cAAc,QAAQ,OAAO;AACzC,UAAI,UAAU,IAAI;AAChB,sBAAc,OAAO,KAAK;AAAA,MAC5B;AAAA,IACF,CAAC;AACD,SAAK,kBAAkB;AAEvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,YAAU;AAC1B,UAAM,UAAU,OAAO;AACvB,QAAI,CAAC,QAAQ,eAAe,OAAO,cAAc,eAAe,YAAY;AAC1E,YAAM,MAAM,uDAAuD;AAAA,IACrE;AAGA,UAAM,aAAa,KAAK,cAAc,cAAc,cAAc,YAAY;AAC9E,YAAQ,WAAW,aAAa,YAAY,OAAO;AACnD,SAAK,cAAc,YAAY,OAAO;AACtC,SAAK,kBAAkB;AACvB,UAAM,aAAa,MAAM;AAEvB,UAAI,WAAW,YAAY;AACzB,mBAAW,WAAW,aAAa,SAAS,UAAU;AAAA,MACxD;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,UAAM,QAAQ;AACd,SAAK,cAAc,OAAO;AAAA,EAC5B;AAAA;AAAA,EAEA,sBAAsB,cAAc;AAClC,WAAO,aAAa,SAAS,UAAU,CAAC;AAAA,EAC1C;AACF;AAMA,IAAM,YAAN,MAAM,mBAAkB,eAAe;AAAA,EACrC,cAAc;AACZ,UAAM,cAAc,OAAO,WAAW;AACtC,UAAM,mBAAmB,OAAO,gBAAgB;AAChD,UAAM,aAAa,gBAAgB;AAAA,EACrC;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,IACjC,UAAU,CAAC,WAAW;AAAA,IACtB,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,0BAAN,MAAM,iCAAgC,UAAU;AAAA,EAC9C,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,gCAAgC,mBAAmB;AACjE,cAAQ,yCAAyC,uCAA0C,sBAAsB,wBAAuB,IAAI,qBAAqB,wBAAuB;AAAA,IAC1L;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,GAAG,CAAC,IAAI,UAAU,EAAE,CAAC;AAAA,IACtD,UAAU,CAAC,WAAW;AAAA,IACtB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,kBAAN,MAAM,yBAAwB,iBAAiB;AAAA,EAC7C,aAAa,OAAO,eAAa;AAAA,IAC/B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,OAAO,QAAQ;AAAA,EAC3B,oBAAoB,OAAO,gBAAgB;AAAA;AAAA,EAE3C,iBAAiB;AAAA;AAAA,EAEjB;AAAA,EACA,cAAc;AACZ,UAAM;AAAA,EACR;AAAA;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,QAAQ;AAKjB,QAAI,KAAK,YAAY,KAAK,CAAC,UAAU,CAAC,KAAK,gBAAgB;AACzD;AAAA,IACF;AACA,QAAI,KAAK,YAAY,GAAG;AACtB,YAAM,OAAO;AAAA,IACf;AACA,QAAI,QAAQ;AACV,YAAM,OAAO,MAAM;AAAA,IACrB;AACA,SAAK,kBAAkB,UAAU;AAAA,EACnC;AAAA;AAAA,EAEA,WAAW,IAAI,aAAa;AAAA;AAAA,EAE5B,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW;AACT,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,cAAc;AACZ,UAAM,QAAQ;AACd,SAAK,eAAe,KAAK,kBAAkB;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB,QAAQ;AAC5B,WAAO,gBAAgB,IAAI;AAG3B,UAAM,mBAAmB,OAAO,oBAAoB,OAAO,OAAO,mBAAmB,KAAK;AAC1F,UAAM,MAAM,iBAAiB,gBAAgB,OAAO,WAAW;AAAA,MAC7D,OAAO,iBAAiB;AAAA,MACxB,UAAU,OAAO,YAAY,iBAAiB;AAAA,MAC9C,kBAAkB,OAAO,oBAAoB;AAAA,MAC7C,aAAa,KAAK,cAAc;AAAA,IAClC,CAAC;AAID,QAAI,qBAAqB,KAAK,mBAAmB;AAC/C,WAAK,aAAa,EAAE,YAAY,IAAI,SAAS,UAAU,CAAC,CAAC;AAAA,IAC3D;AACA,UAAM,aAAa,MAAM,IAAI,QAAQ,CAAC;AACtC,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,SAAS,KAAK,GAAG;AACtB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,QAAQ;AAC3B,WAAO,gBAAgB,IAAI;AAC3B,UAAM,UAAU,KAAK,kBAAkB,mBAAmB,OAAO,aAAa,OAAO,SAAS;AAAA,MAC5F,UAAU,OAAO;AAAA,IACnB,CAAC;AACD,UAAM,aAAa,MAAM,KAAK,kBAAkB,MAAM,CAAC;AACvD,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,SAAS,KAAK,OAAO;AAC1B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,YAAU;AAC1B,UAAM,UAAU,OAAO;AACvB,QAAI,CAAC,QAAQ,eAAe,OAAO,cAAc,eAAe,YAAY;AAC1E,YAAM,MAAM,uDAAuD;AAAA,IACrE;AAGA,UAAM,aAAa,KAAK,UAAU,cAAc,YAAY;AAC5D,WAAO,gBAAgB,IAAI;AAC3B,YAAQ,WAAW,aAAa,YAAY,OAAO;AACnD,SAAK,aAAa,EAAE,YAAY,OAAO;AACvC,SAAK,kBAAkB;AACvB,UAAM,aAAa,MAAM;AACvB,UAAI,WAAW,YAAY;AACzB,mBAAW,WAAW,aAAa,SAAS,UAAU;AAAA,MACxD;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,eAAe;AACb,UAAM,gBAAgB,KAAK,kBAAkB,QAAQ;AAGrD,WAAO,cAAc,aAAa,cAAc,eAAe,gBAAgB,cAAc;AAAA,EAC/F;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,IACvC,QAAQ;AAAA,MACN,QAAQ,CAAC,GAAG,mBAAmB,QAAQ;AAAA,IACzC;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,IACA,UAAU,CAAC,iBAAiB;AAAA,IAC5B,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,sBAAN,MAAM,6BAA4B,gBAAgB;AAAA,EAChD,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,4BAA4B,mBAAmB;AAC7D,cAAQ,qCAAqC,mCAAsC,sBAAsB,oBAAmB,IAAI,qBAAqB,oBAAmB;AAAA,IAC1K;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,GAAG,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,IAC7D,QAAQ;AAAA,MACN,QAAQ,CAAC,GAAG,iBAAiB,QAAQ;AAAA,IACvC;AAAA,IACA,UAAU,CAAC,eAAe;AAAA,IAC1B,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,MACD,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,WAAW,iBAAiB,yBAAyB,mBAAmB;AAAA,IAClF,SAAS,CAAC,WAAW,iBAAiB,yBAAyB,mBAAmB;AAAA,EACpF,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,WAAW,iBAAiB,yBAAyB,mBAAmB;AAAA,MAClF,SAAS,CAAC,WAAW,iBAAiB,yBAAyB,mBAAmB;AAAA,IACpF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}
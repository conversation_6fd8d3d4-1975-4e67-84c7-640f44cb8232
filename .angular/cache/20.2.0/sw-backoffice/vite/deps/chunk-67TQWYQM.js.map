{"version": 3, "sources": ["../../../../../../node_modules/@angular/platform-browser/fesm2022/platform-browser.mjs"], "sourcesContent": ["/**\n * @license Angular v20.2.1\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nexport { BrowserModule, bootstrapApplication, createApplication, platformBrowser, provideProtractorTestingSupport, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, KeyEventsPlugin as ɵKeyEventsPlugin } from './browser.mjs';\nimport { ɵgetDOM as _getDOM, DOCUMENT } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, ɵglobal as _global, ApplicationRef, InjectionToken, ɵConsole as _Console, Optional, Injector, NgModule, forwardRef, ɵRuntimeError as _RuntimeError, ɵXSS_SECURITY_URL as _XSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow as _allowSanitizationBypassAndThrow, ɵunwrapSafeValue as _unwrapSafeValue, ɵ_sanitizeUrl as __sanitizeUrl, ɵ_sanitizeHtml as __sanitizeHtml, ɵbypassSanitizationTrustHtml as _bypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle as _bypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript as _bypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl as _bypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl as _bypassSanitizationTrustResourceUrl, ɵwithI18nSupport as _withI18nSupport, ɵwithEventReplay as _withEventReplay, ɵwithIncrementalHydration as _withIncrementalHydration, makeEnvironmentProviders, ɵwithDomHydration as _withDomHydration, ENVIRONMENT_INITIALIZER, inject, NgZone, ɵZONELESS_ENABLED as _ZONELESS_ENABLED, ɵformatRuntimeError as _formatRuntimeError, ɵIS_ENABLED_BLOCKING_INITIAL_NAVIGATION as _IS_ENABLED_BLOCKING_INITIAL_NAVIGATION, Version } from '@angular/core';\nimport { EventManagerPlugin, EVENT_MANAGER_PLUGINS } from './dom_renderer.mjs';\nexport { EventManager, REMOVE_STYLES_ON_COMPONENT_DESTROY, DomRendererFactory2 as ɵDomRendererFactory2, SharedStylesHost as ɵSharedStylesHost } from './dom_renderer.mjs';\nimport { ɵwithHttpTransferCache as _withHttpTransferCache } from '@angular/common/http';\n\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n  _doc;\n  _dom;\n  constructor(_doc) {\n    this._doc = _doc;\n    this._dom = _getDOM();\n  }\n  /**\n   * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * If an existing element is found, it is returned and is not modified in any way.\n   * @param tag The definition of a `<meta>` element to match or create.\n   * @param forceCreation True to create a new element without checking whether one already exists.\n   * @returns The existing element with the same attributes and values if found,\n   * the new element if no match is found, or `null` if the tag parameter is not defined.\n   */\n  addTag(tag, forceCreation = false) {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n  /**\n   * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * @param tags An array of tag definitions to match or create.\n   * @param forceCreation True to create new elements without checking whether they already exist.\n   * @returns The matching elements if found, or the new elements.\n   */\n  addTags(tags, forceCreation = false) {\n    if (!tags) return [];\n    return tags.reduce((result, tag) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n      return result;\n    }, []);\n  }\n  /**\n   * Retrieves a `<meta>` tag element in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching element, if any.\n   */\n  getTag(attrSelector) {\n    if (!attrSelector) return null;\n    return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n  }\n  /**\n   * Retrieves a set of `<meta>` tag elements in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching elements, if any.\n   */\n  getTags(attrSelector) {\n    if (!attrSelector) return [];\n    const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n    return list ? [].slice.call(list) : [];\n  }\n  /**\n   * Modifies an existing `<meta>` tag element in the current HTML document.\n   * @param tag The tag description with which to replace the existing tag content.\n   * @param selector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n   * replacement tag.\n   * @return The modified element.\n   */\n  updateTag(tag, selector) {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const meta = this.getTag(selector);\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n    return this._getOrCreateElement(tag, true);\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param attrSelector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   */\n  removeTag(attrSelector) {\n    this.removeTagElement(this.getTag(attrSelector));\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param meta The tag definition to match against to identify an existing tag.\n   */\n  removeTagElement(meta) {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n  _getOrCreateElement(meta, forceCreation = false) {\n    if (!forceCreation) {\n      const selector = this._parseSelector(meta);\n      // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n      const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n      if (elem !== undefined) return elem;\n    }\n    const element = this._dom.createElement('meta');\n    this._setMetaElementAttributes(meta, element);\n    const head = this._doc.getElementsByTagName('head')[0];\n    head.appendChild(element);\n    return element;\n  }\n  _setMetaElementAttributes(tag, el) {\n    Object.keys(tag).forEach(prop => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n    return el;\n  }\n  _parseSelector(tag) {\n    const attr = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n  _containsAttributes(tag, elem) {\n    return Object.keys(tag).every(key => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n  }\n  _getMetaKeyMap(prop) {\n    return META_KEYS_MAP[prop] || prop;\n  }\n  static ɵfac = function Meta_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Meta)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Meta,\n    factory: Meta.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Meta, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n  httpEquiv: 'http-equiv'\n};\n\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n  _doc;\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  /**\n   * Get the title of the current HTML document.\n   */\n  getTitle() {\n    return this._doc.title;\n  }\n  /**\n   * Set the title of the current HTML document.\n   * @param newTitle\n   */\n  setTitle(newTitle) {\n    this._doc.title = newTitle || '';\n  }\n  static ɵfac = function Title_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Title)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Title,\n    factory: Title.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Title, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/// <reference path=\"../../../goog.d.ts\" />\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n  if (typeof COMPILED === 'undefined' || !COMPILED) {\n    // Note: we can't export `ng` when using closure enhanced optimization as:\n    // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n    // - we can't declare a closure extern as the namespace `ng` is already used within Google\n    //   for typings for angularJS (via `goog.provide('ng....')`).\n    const ng = _global['ng'] = _global['ng'] || {};\n    ng[name] = value;\n  }\n}\nclass ChangeDetectionPerfRecord {\n  msPerTick;\n  numTicks;\n  constructor(msPerTick, numTicks) {\n    this.msPerTick = msPerTick;\n    this.numTicks = numTicks;\n  }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n  appRef;\n  constructor(ref) {\n    this.appRef = ref.injector.get(ApplicationRef);\n  }\n  // tslint:disable:no-console\n  /**\n   * Exercises change detection in a loop and then prints the average amount of\n   * time in milliseconds how long a single round of change detection takes for\n   * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n   * of 500 milliseconds.\n   *\n   * Optionally, a user may pass a `config` parameter containing a map of\n   * options. Supported options are:\n   *\n   * `record` (boolean) - causes the profiler to record a CPU profile while\n   * it exercises the change detector. Example:\n   *\n   * ```ts\n   * ng.profiler.timeChangeDetection({record: true})\n   * ```\n   */\n  timeChangeDetection(config) {\n    const record = config && config['record'];\n    const profileName = 'Change Detection';\n    // Profiler is not available in Android browsers without dev tools opened\n    if (record && 'profile' in console && typeof console.profile === 'function') {\n      console.profile(profileName);\n    }\n    const start = performance.now();\n    let numTicks = 0;\n    while (numTicks < 5 || performance.now() - start < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n    const end = performance.now();\n    if (record && 'profileEnd' in console && typeof console.profileEnd === 'function') {\n      console.profileEnd(profileName);\n    }\n    const msPerTick = (end - start) / numTicks;\n    console.log(`ran ${numTicks} change detection cycles`);\n    console.log(`${msPerTick.toFixed(2)} ms per check`);\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n}\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n  /**\n   * Match all nodes.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n   */\n  static all() {\n    return () => true;\n  }\n  /**\n   * Match elements by the given CSS selector.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n   */\n  static css(selector) {\n    return debugElement => {\n      return debugElement.nativeElement != null ? elementMatches(debugElement.nativeElement, selector) : false;\n    };\n  }\n  /**\n   * Match nodes that have the given directive present.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n   */\n  static directive(type) {\n    return debugNode => debugNode.providerTokens.indexOf(type) !== -1;\n  }\n}\nfunction elementMatches(n, selector) {\n  if (_getDOM().isElementNode(n)) {\n    return n.matches && n.matches(selector) || n.msMatchesSelector && n.msMatchesSelector(selector) || n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n  }\n  return false;\n}\n\n/// <reference types=\"hammerjs\" />\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n  'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see {@link HammerGestureConfig}\n *\n * @ngModule HammerModule\n * @publicApi\n *\n * @deprecated The HammerJS integration is deprecated. Replace it by your own implementation.\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerGestureConfig' : '');\n/**\n * Injection token used to provide a HammerLoader to Angular.\n *\n * @see {@link HammerLoader}\n *\n * @publicApi\n *\n * @deprecated The HammerJS integration is deprecated. Replace it by your own implementation.\n */\nconst HAMMER_LOADER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerLoader' : '');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n *\n * @deprecated The HammerJS integration is deprecated. Replace it by your own implementation.\n */\nclass HammerGestureConfig {\n  /**\n   * A set of supported event names for gestures to be used in Angular.\n   * Angular supports all built-in recognizers, as listed in\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   */\n  events = [];\n  /**\n   * Maps gesture event names to a set of configuration options\n   * that specify overrides to the default values for specific properties.\n   *\n   * The key is a supported event name to be configured,\n   * and the options object contains a set of properties, with override values\n   * to be applied to the named recognizer event.\n   * For example, to disable recognition of the rotate event, specify\n   *  `{\"rotate\": {\"enable\": false}}`.\n   *\n   * Properties that are not present take the HammerJS default values.\n   * For information about which properties are supported for which events,\n   * and their allowed and default values, see\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   *\n   */\n  overrides = {};\n  /**\n   * Properties whose default values can be overridden for a given event.\n   * Different sets of properties apply to different events.\n   * For information about which properties are supported for which events,\n   * and their allowed and default values, see\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   */\n  options;\n  /**\n   * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n   * and attaches it to a given HTML element.\n   * @param element The element that will recognize gestures.\n   * @returns A HammerJS event-manager object.\n   */\n  buildHammer(element) {\n    const mc = new Hammer(element, this.options);\n    mc.get('pinch').set({\n      enable: true\n    });\n    mc.get('rotate').set({\n      enable: true\n    });\n    for (const eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n    return mc;\n  }\n  static ɵfac = function HammerGestureConfig_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HammerGestureConfig)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HammerGestureConfig,\n    factory: HammerGestureConfig.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGestureConfig, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n  _config;\n  _injector;\n  loader;\n  _loaderPromise = null;\n  constructor(doc, _config, _injector, loader) {\n    super(doc);\n    this._config = _config;\n    this._injector = _injector;\n    this.loader = loader;\n  }\n  supports(eventName) {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n    if (!window.Hammer && !this.loader) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // Get a `Console` through an injector to tree-shake the\n        // class when it is unused in production.\n        const _console = this._injector.get(_Console);\n        _console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` + `loaded and no custom loader has been specified.`);\n      }\n      return false;\n    }\n    return true;\n  }\n  addEventListener(element, eventName, handler) {\n    const zone = this.manager.getZone();\n    eventName = eventName.toLowerCase();\n    // If Hammer is not present but a loader is specified, we defer adding the event listener\n    // until Hammer is loaded.\n    if (!window.Hammer && this.loader) {\n      this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n      // This `addEventListener` method returns a function to remove the added listener.\n      // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n      // than remove anything.\n      let cancelRegistration = false;\n      let deregister = () => {\n        cancelRegistration = true;\n      };\n      zone.runOutsideAngular(() => this._loaderPromise.then(() => {\n        // If Hammer isn't actually loaded when the custom loader resolves, give up.\n        if (!window.Hammer) {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const _console = this._injector.get(_Console);\n            _console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n          }\n          deregister = () => {};\n          return;\n        }\n        if (!cancelRegistration) {\n          // Now that Hammer is loaded and the listener is being loaded for real,\n          // the deregistration function changes from canceling registration to\n          // removal.\n          deregister = this.addEventListener(element, eventName, handler);\n        }\n      }).catch(() => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          const _console = this._injector.get(_Console);\n          _console.warn(`The \"${eventName}\" event cannot be bound because the custom ` + `Hammer.JS loader failed.`);\n        }\n        deregister = () => {};\n      }));\n      // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n      // can change the behavior of `deregister` once the listener is added. Using a closure in\n      // this way allows us to avoid any additional data structures to track listener removal.\n      return () => {\n        deregister();\n      };\n    }\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const mc = this._config.buildHammer(element);\n      const callback = function (eventObj) {\n        zone.runGuarded(function () {\n          handler(eventObj);\n        });\n      };\n      mc.on(eventName, callback);\n      return () => {\n        mc.off(eventName, callback);\n        // destroy mc to prevent memory leak\n        if (typeof mc.destroy === 'function') {\n          mc.destroy();\n        }\n      };\n    });\n  }\n  isCustomEvent(eventName) {\n    return this._config.events.indexOf(eventName) > -1;\n  }\n  static ɵfac = function HammerGesturesPlugin_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HammerGesturesPlugin)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(HAMMER_GESTURE_CONFIG), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(HAMMER_LOADER, 8));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HammerGesturesPlugin,\n    factory: HammerGesturesPlugin.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGesturesPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: HammerGestureConfig,\n    decorators: [{\n      type: Inject,\n      args: [HAMMER_GESTURE_CONFIG]\n    }]\n  }, {\n    type: i0.Injector\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [HAMMER_LOADER]\n    }]\n  }], null);\n})();\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's `EventManager`.\n *\n * @publicApi\n *\n * @deprecated The hammer integration is deprecated. Replace it by your own implementation.\n */\nclass HammerModule {\n  static ɵfac = function HammerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HammerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HammerModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [{\n      provide: EVENT_MANAGER_PLUGINS,\n      useClass: HammerGesturesPlugin,\n      multi: true,\n      deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Injector, [new Optional(), HAMMER_LOADER]]\n    }, {\n      provide: HAMMER_GESTURE_CONFIG,\n      useClass: HammerGestureConfig\n    }]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: HammerGesturesPlugin,\n        multi: true,\n        deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Injector, [new Optional(), HAMMER_LOADER]]\n      }, {\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: HammerGestureConfig\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n  static ɵfac = function DomSanitizer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomSanitizer)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomSanitizer,\n    factory: function DomSanitizer_Factory(__ngFactoryType__) {\n      let __ngConditionalFactory__ = null;\n      if (__ngFactoryType__) {\n        __ngConditionalFactory__ = new (__ngFactoryType__ || DomSanitizer)();\n      } else {\n        __ngConditionalFactory__ = i0.ɵɵinject(DomSanitizerImpl);\n      }\n      return __ngConditionalFactory__;\n    },\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useExisting: forwardRef(() => DomSanitizerImpl)\n    }]\n  }], null, null);\n})();\nclass DomSanitizerImpl extends DomSanitizer {\n  _doc;\n  constructor(_doc) {\n    super();\n    this._doc = _doc;\n  }\n  sanitize(ctx, value) {\n    if (value == null) return null;\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return value;\n      case SecurityContext.HTML:\n        if (_allowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n          return _unwrapSafeValue(value);\n        }\n        return __sanitizeHtml(this._doc, String(value)).toString();\n      case SecurityContext.STYLE:\n        if (_allowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n          return _unwrapSafeValue(value);\n        }\n        return value;\n      case SecurityContext.SCRIPT:\n        if (_allowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n          return _unwrapSafeValue(value);\n        }\n        throw new _RuntimeError(5200 /* RuntimeErrorCode.SANITIZATION_UNSAFE_SCRIPT */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'unsafe value used in a script context');\n      case SecurityContext.URL:\n        if (_allowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n          return _unwrapSafeValue(value);\n        }\n        return __sanitizeUrl(String(value));\n      case SecurityContext.RESOURCE_URL:\n        if (_allowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n          return _unwrapSafeValue(value);\n        }\n        throw new _RuntimeError(5201 /* RuntimeErrorCode.SANITIZATION_UNSAFE_RESOURCE_URL */, (typeof ngDevMode === 'undefined' || ngDevMode) && `unsafe value used in a resource URL context (see ${_XSS_SECURITY_URL})`);\n      default:\n        throw new _RuntimeError(5202 /* RuntimeErrorCode.SANITIZATION_UNEXPECTED_CTX */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Unexpected SecurityContext ${ctx} (see ${_XSS_SECURITY_URL})`);\n    }\n  }\n  bypassSecurityTrustHtml(value) {\n    return _bypassSanitizationTrustHtml(value);\n  }\n  bypassSecurityTrustStyle(value) {\n    return _bypassSanitizationTrustStyle(value);\n  }\n  bypassSecurityTrustScript(value) {\n    return _bypassSanitizationTrustScript(value);\n  }\n  bypassSecurityTrustUrl(value) {\n    return _bypassSanitizationTrustUrl(value);\n  }\n  bypassSecurityTrustResourceUrl(value) {\n    return _bypassSanitizationTrustResourceUrl(value);\n  }\n  static ɵfac = function DomSanitizerImpl_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomSanitizerImpl)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomSanitizerImpl,\n    factory: DomSanitizerImpl.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizerImpl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * The list of features as an enum to uniquely type each `HydrationFeature`.\n * @see {@link HydrationFeature}\n *\n * @publicApi\n */\nvar HydrationFeatureKind;\n(function (HydrationFeatureKind) {\n  HydrationFeatureKind[HydrationFeatureKind[\"NoHttpTransferCache\"] = 0] = \"NoHttpTransferCache\";\n  HydrationFeatureKind[HydrationFeatureKind[\"HttpTransferCacheOptions\"] = 1] = \"HttpTransferCacheOptions\";\n  HydrationFeatureKind[HydrationFeatureKind[\"I18nSupport\"] = 2] = \"I18nSupport\";\n  HydrationFeatureKind[HydrationFeatureKind[\"EventReplay\"] = 3] = \"EventReplay\";\n  HydrationFeatureKind[HydrationFeatureKind[\"IncrementalHydration\"] = 4] = \"IncrementalHydration\";\n})(HydrationFeatureKind || (HydrationFeatureKind = {}));\n/**\n * Helper function to create an object that represents a Hydration feature.\n */\nfunction hydrationFeature(ɵkind, ɵproviders = [], ɵoptions = {}) {\n  return {\n    ɵkind,\n    ɵproviders\n  };\n}\n/**\n * Disables HTTP transfer cache. Effectively causes HTTP requests to be performed twice: once on the\n * server and other one on the browser.\n *\n * @publicApi\n */\nfunction withNoHttpTransferCache() {\n  // This feature has no providers and acts as a flag that turns off\n  // HTTP transfer cache (which otherwise is turned on by default).\n  return hydrationFeature(HydrationFeatureKind.NoHttpTransferCache);\n}\n/**\n * The function accepts an object, which allows to configure cache parameters,\n * such as which headers should be included (no headers are included by default),\n * whether POST requests should be cached or a callback function to determine if a\n * particular request should be cached.\n *\n * @publicApi\n */\nfunction withHttpTransferCacheOptions(options) {\n  // This feature has no providers and acts as a flag to pass options to the HTTP transfer cache.\n  return hydrationFeature(HydrationFeatureKind.HttpTransferCacheOptions, _withHttpTransferCache(options));\n}\n/**\n * Enables support for hydrating i18n blocks.\n *\n * @publicApi 20.0\n */\nfunction withI18nSupport() {\n  return hydrationFeature(HydrationFeatureKind.I18nSupport, _withI18nSupport());\n}\n/**\n * Enables support for replaying user events (e.g. `click`s) that happened on a page\n * before hydration logic has completed. Once an application is hydrated, all captured\n * events are replayed and relevant event listeners are executed.\n *\n * @usageNotes\n *\n * Basic example of how you can enable event replay in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withEventReplay())]\n * });\n * ```\n * @publicApi\n * @see {@link provideClientHydration}\n */\nfunction withEventReplay() {\n  return hydrationFeature(HydrationFeatureKind.EventReplay, _withEventReplay());\n}\n/**\n * Enables support for incremental hydration using the `hydrate` trigger syntax.\n *\n * @usageNotes\n *\n * Basic example of how you can enable incremental hydration in your application when\n * the `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withIncrementalHydration())]\n * });\n * ```\n * @publicApi 20.0\n * @see {@link provideClientHydration}\n */\nfunction withIncrementalHydration() {\n  return hydrationFeature(HydrationFeatureKind.IncrementalHydration, _withIncrementalHydration());\n}\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether compatible ZoneJS was used in an application\n * and logs a warning in a console if it's not the case.\n */\nfunction provideZoneJsCompatibilityDetector() {\n  return [{\n    provide: ENVIRONMENT_INITIALIZER,\n    useValue: () => {\n      const ngZone = inject(NgZone);\n      const isZoneless = inject(_ZONELESS_ENABLED);\n      // Checking `ngZone instanceof NgZone` would be insufficient here,\n      // because custom implementations might use NgZone as a base class.\n      if (!isZoneless && ngZone.constructor !== NgZone) {\n        const console = inject(_Console);\n        const message = _formatRuntimeError(-5000 /* RuntimeErrorCode.UNSUPPORTED_ZONEJS_INSTANCE */, 'Angular detected that hydration was enabled for an application ' + 'that uses a custom or a noop Zone.js implementation. ' + 'This is not yet a fully supported configuration.');\n        console.warn(message);\n      }\n    },\n    multi: true\n  }];\n}\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether enabledBlocking initial navigation is used in an application\n * and logs a warning in a console if it's not compatible with hydration.\n */\nfunction provideEnabledBlockingInitialNavigationDetector() {\n  return [{\n    provide: ENVIRONMENT_INITIALIZER,\n    useValue: () => {\n      const isEnabledBlockingInitialNavigation = inject(_IS_ENABLED_BLOCKING_INITIAL_NAVIGATION, {\n        optional: true\n      });\n      if (isEnabledBlockingInitialNavigation) {\n        const console = inject(_Console);\n        const message = _formatRuntimeError(5001 /* RuntimeErrorCode.HYDRATION_CONFLICTING_FEATURES */, 'Configuration error: found both hydration and enabledBlocking initial navigation ' + 'in the same application, which is a contradiction.');\n        console.warn(message);\n      }\n    },\n    multi: true\n  }];\n}\n/**\n * Sets up providers necessary to enable hydration functionality for the application.\n *\n * By default, the function enables the recommended set of features for the optimal\n * performance for most of the applications. It includes the following features:\n *\n * * Reconciling DOM hydration. Learn more about it [here](guide/hydration).\n * * [`HttpClient`](api/common/http/HttpClient) response caching while running on the server and\n * transferring this cache to the client to avoid extra HTTP requests. Learn more about data caching\n * [here](guide/ssr#caching-data-when-using-httpclient).\n *\n * These functions allow you to disable some of the default features or enable new ones:\n *\n * * {@link withNoHttpTransferCache} to disable HTTP transfer cache\n * * {@link withHttpTransferCacheOptions} to configure some HTTP transfer cache options\n * * {@link withI18nSupport} to enable hydration support for i18n blocks\n * * {@link withEventReplay} to enable support for replaying user events\n *\n * @usageNotes\n *\n * Basic example of how you can enable hydration in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration()]\n * });\n * ```\n *\n * Alternatively if you are using NgModules, you would add `provideClientHydration`\n * to your root app module's provider list.\n * ```ts\n * @NgModule({\n *   declarations: [RootCmp],\n *   bootstrap: [RootCmp],\n *   providers: [provideClientHydration()],\n * })\n * export class AppModule {}\n * ```\n *\n * @see {@link withNoHttpTransferCache}\n * @see {@link withHttpTransferCacheOptions}\n * @see {@link withI18nSupport}\n * @see {@link withEventReplay}\n *\n * @param features Optional features to configure additional hydration behaviors.\n * @returns A set of providers to enable hydration.\n *\n * @publicApi 17.0\n */\nfunction provideClientHydration(...features) {\n  const providers = [];\n  const featuresKind = new Set();\n  for (const {\n    ɵproviders,\n    ɵkind\n  } of features) {\n    featuresKind.add(ɵkind);\n    if (ɵproviders.length) {\n      providers.push(ɵproviders);\n    }\n  }\n  const hasHttpTransferCacheOptions = featuresKind.has(HydrationFeatureKind.HttpTransferCacheOptions);\n  if (typeof ngDevMode !== 'undefined' && ngDevMode && featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) && hasHttpTransferCacheOptions) {\n    throw new _RuntimeError(5001 /* RuntimeErrorCode.HYDRATION_CONFLICTING_FEATURES */, 'Configuration error: found both withHttpTransferCacheOptions() and withNoHttpTransferCache() in the same call to provideClientHydration(), which is a contradiction.');\n  }\n  return makeEnvironmentProviders([typeof ngDevMode !== 'undefined' && ngDevMode ? provideZoneJsCompatibilityDetector() : [], typeof ngDevMode !== 'undefined' && ngDevMode ? provideEnabledBlockingInitialNavigationDetector() : [], _withDomHydration(), featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) || hasHttpTransferCacheOptions ? [] : _withHttpTransferCache({}), providers]);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('20.2.1');\nexport { By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManagerPlugin, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, HydrationFeatureKind, Meta, Title, VERSION, disableDebugTools, enableDebugTools, provideClientHydration, withEventReplay, withHttpTransferCacheOptions, withI18nSupport, withIncrementalHydration, withNoHttpTransferCache, DomSanitizerImpl as ɵDomSanitizerImpl, HammerGesturesPlugin as ɵHammerGesturesPlugin };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAAAA;AAAA,EAAA;AAAA,yBAAAC;AAAA,EAAA,gCAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0PA,SAAS,YAAY,MAAM,OAAO;AAChC,MAAI,OAAO,aAAa,eAAe,CAAC,UAAU;AAKhD,UAAM,KAAK,QAAQ,IAAI,IAAI,QAAQ,IAAI,KAAK,CAAC;AAC7C,OAAG,IAAI,IAAI;AAAA,EACb;AACF;AAwEA,SAAS,iBAAiB,KAAK;AAC7B,cAAY,sBAAsB,IAAI,gBAAgB,GAAG,CAAC;AAC1D,SAAO;AACT;AAMA,SAAS,oBAAoB;AAC3B,cAAY,sBAAsB,IAAI;AACxC;AA4CA,SAAS,eAAe,GAAG,UAAU;AACnC,MAAI,OAAQ,EAAE,cAAc,CAAC,GAAG;AAC9B,WAAO,EAAE,WAAW,EAAE,QAAQ,QAAQ,KAAK,EAAE,qBAAqB,EAAE,kBAAkB,QAAQ,KAAK,EAAE,yBAAyB,EAAE,sBAAsB,QAAQ;AAAA,EAChK;AACA,SAAO;AACT;AAqdA,SAAS,iBAAiB,OAAO,aAAa,CAAC,GAAG,WAAW,CAAC,GAAG;AAC/D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAOA,SAAS,0BAA0B;AAGjC,SAAO,iBAAiB,qBAAqB,mBAAmB;AAClE;AASA,SAAS,6BAA6B,SAAS;AAE7C,SAAO,iBAAiB,qBAAqB,0BAA0B,sBAAuB,OAAO,CAAC;AACxG;AAMA,SAASD,mBAAkB;AACzB,SAAO,iBAAiB,qBAAqB,aAAa,gBAAiB,CAAC;AAC9E;AAkBA,SAASD,mBAAkB;AACzB,SAAO,iBAAiB,qBAAqB,aAAa,gBAAiB,CAAC;AAC9E;AAgBA,SAASE,4BAA2B;AAClC,SAAO,iBAAiB,qBAAqB,sBAAsB,yBAA0B,CAAC;AAChG;AAMA,SAAS,qCAAqC;AAC5C,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU,MAAM;AACd,YAAM,SAAS,OAAO,MAAM;AAC5B,YAAM,aAAa,OAAO,gBAAiB;AAG3C,UAAI,CAAC,cAAc,OAAO,gBAAgB,QAAQ;AAChD,cAAMC,WAAU,OAAO,OAAQ;AAC/B,cAAM,UAAU,mBAAoB,MAA0D,sKAAgL;AAC9Q,QAAAA,SAAQ,KAAK,OAAO;AAAA,MACtB;AAAA,IACF;AAAA,IACA,OAAO;AAAA,EACT,CAAC;AACH;AAMA,SAAS,kDAAkD;AACzD,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU,MAAM;AACd,YAAM,qCAAqC,OAAO,wCAAyC;AAAA,QACzF,UAAU;AAAA,MACZ,CAAC;AACD,UAAI,oCAAoC;AACtC,cAAMA,WAAU,OAAO,OAAQ;AAC/B,cAAM,UAAU,mBAAoB,MAA4D,qIAA0I;AAC1O,QAAAA,SAAQ,KAAK,OAAO;AAAA,MACtB;AAAA,IACF;AAAA,IACA,OAAO;AAAA,EACT,CAAC;AACH;AAkDA,SAAS,0BAA0B,UAAU;AAC3C,QAAM,YAAY,CAAC;AACnB,QAAM,eAAe,oBAAI,IAAI;AAC7B,aAAW;AAAA,IACT;AAAA,IACA;AAAA,EACF,KAAK,UAAU;AACb,iBAAa,IAAI,KAAK;AACtB,QAAI,WAAW,QAAQ;AACrB,gBAAU,KAAK,UAAU;AAAA,IAC3B;AAAA,EACF;AACA,QAAM,8BAA8B,aAAa,IAAI,qBAAqB,wBAAwB;AAClG,MAAI,OAAO,cAAc,eAAe,aAAa,aAAa,IAAI,qBAAqB,mBAAmB,KAAK,6BAA6B;AAC9I,UAAM,IAAI,aAAc,MAA4D,sKAAsK;AAAA,EAC5P;AACA,SAAO,yBAAyB,CAAC,OAAO,cAAc,eAAe,YAAY,mCAAmC,IAAI,CAAC,GAAG,OAAO,cAAc,eAAe,YAAY,gDAAgD,IAAI,CAAC,GAAG,iBAAkB,GAAG,aAAa,IAAI,qBAAqB,mBAAmB,KAAK,8BAA8B,CAAC,IAAI,sBAAuB,CAAC,CAAC,GAAG,SAAS,CAAC;AAClY;AAphCA,IAqCM,MAqJA,eAcA,OA4DA,2BAYA,iBA6CA,sBAgCA,IAgDA,aA+CA,uBAUA,eAQA,qBAqEA,sBA2IA,cAmEA,cA2BA,kBAqFF,sBA6ME;AA9hCN;AAAA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAwBA,IAAM,OAAN,MAAM,MAAK;AAAA,MACT;AAAA,MACA;AAAA,MACA,YAAY,MAAM;AAChB,aAAK,OAAO;AACZ,aAAK,OAAO,OAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWA,OAAO,KAAK,gBAAgB,OAAO;AACjC,YAAI,CAAC,IAAK,QAAO;AACjB,eAAO,KAAK,oBAAoB,KAAK,aAAa;AAAA,MACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,QAAQ,MAAM,gBAAgB,OAAO;AACnC,YAAI,CAAC,KAAM,QAAO,CAAC;AACnB,eAAO,KAAK,OAAO,CAAC,QAAQ,QAAQ;AAClC,cAAI,KAAK;AACP,mBAAO,KAAK,KAAK,oBAAoB,KAAK,aAAa,CAAC;AAAA,UAC1D;AACA,iBAAO;AAAA,QACT,GAAG,CAAC,CAAC;AAAA,MACP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,OAAO,cAAc;AACnB,YAAI,CAAC,aAAc,QAAO;AAC1B,eAAO,KAAK,KAAK,cAAc,QAAQ,YAAY,GAAG,KAAK;AAAA,MAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,QAAQ,cAAc;AACpB,YAAI,CAAC,aAAc,QAAO,CAAC;AAC3B,cAAM,OAAoB,KAAK,KAAK,iBAAiB,QAAQ,YAAY,GAAG;AAC5E,eAAO,OAAO,CAAC,EAAE,MAAM,KAAK,IAAI,IAAI,CAAC;AAAA,MACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUA,UAAU,KAAK,UAAU;AACvB,YAAI,CAAC,IAAK,QAAO;AACjB,mBAAW,YAAY,KAAK,eAAe,GAAG;AAC9C,cAAM,OAAO,KAAK,OAAO,QAAQ;AACjC,YAAI,MAAM;AACR,iBAAO,KAAK,0BAA0B,KAAK,IAAI;AAAA,QACjD;AACA,eAAO,KAAK,oBAAoB,KAAK,IAAI;AAAA,MAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,UAAU,cAAc;AACtB,aAAK,iBAAiB,KAAK,OAAO,YAAY,CAAC;AAAA,MACjD;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,iBAAiB,MAAM;AACrB,YAAI,MAAM;AACR,eAAK,KAAK,OAAO,IAAI;AAAA,QACvB;AAAA,MACF;AAAA,MACA,oBAAoB,MAAM,gBAAgB,OAAO;AAC/C,YAAI,CAAC,eAAe;AAClB,gBAAM,WAAW,KAAK,eAAe,IAAI;AAIzC,gBAAM,OAAO,KAAK,QAAQ,QAAQ,EAAE,OAAO,CAAAC,UAAQ,KAAK,oBAAoB,MAAMA,KAAI,CAAC,EAAE,CAAC;AAC1F,cAAI,SAAS,OAAW,QAAO;AAAA,QACjC;AACA,cAAM,UAAU,KAAK,KAAK,cAAc,MAAM;AAC9C,aAAK,0BAA0B,MAAM,OAAO;AAC5C,cAAM,OAAO,KAAK,KAAK,qBAAqB,MAAM,EAAE,CAAC;AACrD,aAAK,YAAY,OAAO;AACxB,eAAO;AAAA,MACT;AAAA,MACA,0BAA0B,KAAK,IAAI;AACjC,eAAO,KAAK,GAAG,EAAE,QAAQ,UAAQ,GAAG,aAAa,KAAK,eAAe,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC;AACtF,eAAO;AAAA,MACT;AAAA,MACA,eAAe,KAAK;AAClB,cAAM,OAAO,IAAI,OAAO,SAAS;AACjC,eAAO,GAAG,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,MAC9B;AAAA,MACA,oBAAoB,KAAK,MAAM;AAC7B,eAAO,OAAO,KAAK,GAAG,EAAE,MAAM,SAAO,KAAK,aAAa,KAAK,eAAe,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC;AAAA,MAC/F;AAAA,MACA,eAAe,MAAM;AACnB,eAAO,cAAc,IAAI,KAAK;AAAA,MAChC;AAAA,MACA,OAAO,OAAO,SAAS,aAAa,mBAAmB;AACrD,eAAO,KAAK,qBAAqB,OAAS,SAAS,QAAQ,CAAC;AAAA,MAC9D;AAAA,MACA,OAAO,QAA0B,mBAAmB;AAAA,QAClD,OAAO;AAAA,QACP,SAAS,MAAK;AAAA,QACd,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AACA,KAAC,MAAM;AACL,OAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,MAAM,CAAC;AAAA,QAC7E,MAAM;AAAA,QACN,MAAM,CAAC;AAAA,UACL,YAAY;AAAA,QACd,CAAC;AAAA,MACH,CAAC,GAAG,MAAM,CAAC;AAAA,QACT,MAAM;AAAA,QACN,YAAY,CAAC;AAAA,UACX,MAAM;AAAA,UACN,MAAM,CAAC,QAAQ;AAAA,QACjB,CAAC;AAAA,MACH,CAAC,GAAG,IAAI;AAAA,IACV,GAAG;AAIH,IAAM,gBAAgB;AAAA,MACpB,WAAW;AAAA,IACb;AAYA,IAAM,QAAN,MAAM,OAAM;AAAA,MACV;AAAA,MACA,YAAY,MAAM;AAChB,aAAK,OAAO;AAAA,MACd;AAAA;AAAA;AAAA;AAAA,MAIA,WAAW;AACT,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,SAAS,UAAU;AACjB,aAAK,KAAK,QAAQ,YAAY;AAAA,MAChC;AAAA,MACA,OAAO,OAAO,SAAS,cAAc,mBAAmB;AACtD,eAAO,KAAK,qBAAqB,QAAU,SAAS,QAAQ,CAAC;AAAA,MAC/D;AAAA,MACA,OAAO,QAA0B,mBAAmB;AAAA,QAClD,OAAO;AAAA,QACP,SAAS,OAAM;AAAA,QACf,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AACA,KAAC,MAAM;AACL,OAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,OAAO,CAAC;AAAA,QAC9E,MAAM;AAAA,QACN,MAAM,CAAC;AAAA,UACL,YAAY;AAAA,QACd,CAAC;AAAA,MACH,CAAC,GAAG,MAAM,CAAC;AAAA,QACT,MAAM;AAAA,QACN,YAAY,CAAC;AAAA,UACX,MAAM;AAAA,UACN,MAAM,CAAC,QAAQ;AAAA,QACjB,CAAC;AAAA,MACH,CAAC,GAAG,IAAI;AAAA,IACV,GAAG;AAoBH,IAAM,4BAAN,MAAgC;AAAA,MAC9B;AAAA,MACA;AAAA,MACA,YAAY,WAAW,UAAU;AAC/B,aAAK,YAAY;AACjB,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAKA,IAAM,kBAAN,MAAsB;AAAA,MACpB;AAAA,MACA,YAAY,KAAK;AACf,aAAK,SAAS,IAAI,SAAS,IAAI,cAAc;AAAA,MAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkBA,oBAAoB,QAAQ;AAC1B,cAAM,SAAS,UAAU,OAAO,QAAQ;AACxC,cAAM,cAAc;AAEpB,YAAI,UAAU,aAAa,WAAW,OAAO,QAAQ,YAAY,YAAY;AAC3E,kBAAQ,QAAQ,WAAW;AAAA,QAC7B;AACA,cAAM,QAAQ,YAAY,IAAI;AAC9B,YAAI,WAAW;AACf,eAAO,WAAW,KAAK,YAAY,IAAI,IAAI,QAAQ,KAAK;AACtD,eAAK,OAAO,KAAK;AACjB;AAAA,QACF;AACA,cAAM,MAAM,YAAY,IAAI;AAC5B,YAAI,UAAU,gBAAgB,WAAW,OAAO,QAAQ,eAAe,YAAY;AACjF,kBAAQ,WAAW,WAAW;AAAA,QAChC;AACA,cAAM,aAAa,MAAM,SAAS;AAClC,gBAAQ,IAAI,OAAO,QAAQ,0BAA0B;AACrD,gBAAQ,IAAI,GAAG,UAAU,QAAQ,CAAC,CAAC,eAAe;AAClD,eAAO,IAAI,0BAA0B,WAAW,QAAQ;AAAA,MAC1D;AAAA,IACF;AACA,IAAM,uBAAuB;AAgC7B,IAAM,KAAN,MAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASP,OAAO,MAAM;AACX,eAAO,MAAM;AAAA,MACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,OAAO,IAAI,UAAU;AACnB,eAAO,kBAAgB;AACrB,iBAAO,aAAa,iBAAiB,OAAO,eAAe,aAAa,eAAe,QAAQ,IAAI;AAAA,QACrG;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,OAAO,UAAU,MAAM;AACrB,eAAO,eAAa,UAAU,eAAe,QAAQ,IAAI,MAAM;AAAA,MACjE;AAAA,IACF;AAYA,IAAM,cAAc;AAAA;AAAA,MAElB,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,UAAU;AAAA,MACV,aAAa;AAAA,MACb,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,WAAW;AAAA;AAAA,MAEX,SAAS;AAAA,MACT,cAAc;AAAA,MACd,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,WAAW;AAAA,MACX,YAAY;AAAA;AAAA,MAEZ,SAAS;AAAA,MACT,WAAW;AAAA;AAAA,MAEX,UAAU;AAAA,MACV,eAAe;AAAA,MACf,cAAc;AAAA,MACd,aAAa;AAAA,MACb,gBAAgB;AAAA;AAAA,MAEhB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA;AAAA,MAEb,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAUA,IAAM,wBAAwB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,wBAAwB,EAAE;AAU3H,IAAM,gBAAgB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,iBAAiB,EAAE;AAQ5G,IAAM,sBAAN,MAAM,qBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMxB,SAAS,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAiBV,YAAY,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,YAAY,SAAS;AACnB,cAAM,KAAK,IAAI,OAAO,SAAS,KAAK,OAAO;AAC3C,WAAG,IAAI,OAAO,EAAE,IAAI;AAAA,UAClB,QAAQ;AAAA,QACV,CAAC;AACD,WAAG,IAAI,QAAQ,EAAE,IAAI;AAAA,UACnB,QAAQ;AAAA,QACV,CAAC;AACD,mBAAW,aAAa,KAAK,WAAW;AACtC,aAAG,IAAI,SAAS,EAAE,IAAI,KAAK,UAAU,SAAS,CAAC;AAAA,QACjD;AACA,eAAO;AAAA,MACT;AAAA,MACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,eAAO,KAAK,qBAAqB,sBAAqB;AAAA,MACxD;AAAA,MACA,OAAO,QAA0B,mBAAmB;AAAA,QAClD,OAAO;AAAA,QACP,SAAS,qBAAoB;AAAA,MAC/B,CAAC;AAAA,IACH;AACA,KAAC,MAAM;AACL,OAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,QAC5F,MAAM;AAAA,MACR,CAAC,GAAG,MAAM,IAAI;AAAA,IAChB,GAAG;AAMH,IAAM,uBAAN,MAAM,8BAA6B,mBAAmB;AAAA,MACpD;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB;AAAA,MACjB,YAAY,KAAK,SAAS,WAAW,QAAQ;AAC3C,cAAM,GAAG;AACT,aAAK,UAAU;AACf,aAAK,YAAY;AACjB,aAAK,SAAS;AAAA,MAChB;AAAA,MACA,SAAS,WAAW;AAClB,YAAI,CAAC,YAAY,eAAe,UAAU,YAAY,CAAC,KAAK,CAAC,KAAK,cAAc,SAAS,GAAG;AAC1F,iBAAO;AAAA,QACT;AACA,YAAI,CAAC,OAAO,UAAU,CAAC,KAAK,QAAQ;AAClC,cAAI,OAAO,cAAc,eAAe,WAAW;AAGjD,kBAAM,WAAW,KAAK,UAAU,IAAI,OAAQ;AAC5C,qBAAS,KAAK,QAAQ,SAAS,kGAAuG;AAAA,UACxI;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MACA,iBAAiB,SAAS,WAAW,SAAS;AAC5C,cAAM,OAAO,KAAK,QAAQ,QAAQ;AAClC,oBAAY,UAAU,YAAY;AAGlC,YAAI,CAAC,OAAO,UAAU,KAAK,QAAQ;AACjC,eAAK,iBAAiB,KAAK,kBAAkB,KAAK,kBAAkB,MAAM,KAAK,OAAO,CAAC;AAIvF,cAAI,qBAAqB;AACzB,cAAI,aAAa,MAAM;AACrB,iCAAqB;AAAA,UACvB;AACA,eAAK,kBAAkB,MAAM,KAAK,eAAe,KAAK,MAAM;AAE1D,gBAAI,CAAC,OAAO,QAAQ;AAClB,kBAAI,OAAO,cAAc,eAAe,WAAW;AACjD,sBAAM,WAAW,KAAK,UAAU,IAAI,OAAQ;AAC5C,yBAAS,KAAK,mEAAmE;AAAA,cACnF;AACA,2BAAa,MAAM;AAAA,cAAC;AACpB;AAAA,YACF;AACA,gBAAI,CAAC,oBAAoB;AAIvB,2BAAa,KAAK,iBAAiB,SAAS,WAAW,OAAO;AAAA,YAChE;AAAA,UACF,CAAC,EAAE,MAAM,MAAM;AACb,gBAAI,OAAO,cAAc,eAAe,WAAW;AACjD,oBAAM,WAAW,KAAK,UAAU,IAAI,OAAQ;AAC5C,uBAAS,KAAK,QAAQ,SAAS,qEAA0E;AAAA,YAC3G;AACA,yBAAa,MAAM;AAAA,YAAC;AAAA,UACtB,CAAC,CAAC;AAIF,iBAAO,MAAM;AACX,uBAAW;AAAA,UACb;AAAA,QACF;AACA,eAAO,KAAK,kBAAkB,MAAM;AAElC,gBAAM,KAAK,KAAK,QAAQ,YAAY,OAAO;AAC3C,gBAAM,WAAW,SAAU,UAAU;AACnC,iBAAK,WAAW,WAAY;AAC1B,sBAAQ,QAAQ;AAAA,YAClB,CAAC;AAAA,UACH;AACA,aAAG,GAAG,WAAW,QAAQ;AACzB,iBAAO,MAAM;AACX,eAAG,IAAI,WAAW,QAAQ;AAE1B,gBAAI,OAAO,GAAG,YAAY,YAAY;AACpC,iBAAG,QAAQ;AAAA,YACb;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,cAAc,WAAW;AACvB,eAAO,KAAK,QAAQ,OAAO,QAAQ,SAAS,IAAI;AAAA,MAClD;AAAA,MACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,eAAO,KAAK,qBAAqB,uBAAyB,SAAS,QAAQ,GAAM,SAAS,qBAAqB,GAAM,SAAY,QAAQ,GAAM,SAAS,eAAe,CAAC,CAAC;AAAA,MAC3K;AAAA,MACA,OAAO,QAA0B,mBAAmB;AAAA,QAClD,OAAO;AAAA,QACP,SAAS,sBAAqB;AAAA,MAChC,CAAC;AAAA,IACH;AACA,KAAC,MAAM;AACL,OAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,QAC7F,MAAM;AAAA,MACR,CAAC,GAAG,MAAM,CAAC;AAAA,QACT,MAAM;AAAA,QACN,YAAY,CAAC;AAAA,UACX,MAAM;AAAA,UACN,MAAM,CAAC,QAAQ;AAAA,QACjB,CAAC;AAAA,MACH,GAAG;AAAA,QACD,MAAM;AAAA,QACN,YAAY,CAAC;AAAA,UACX,MAAM;AAAA,UACN,MAAM,CAAC,qBAAqB;AAAA,QAC9B,CAAC;AAAA,MACH,GAAG;AAAA,QACD,MAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,YAAY,CAAC;AAAA,UACX,MAAM;AAAA,QACR,GAAG;AAAA,UACD,MAAM;AAAA,UACN,MAAM,CAAC,aAAa;AAAA,QACtB,CAAC;AAAA,MACH,CAAC,GAAG,IAAI;AAAA,IACV,GAAG;AAcH,IAAM,eAAN,MAAM,cAAa;AAAA,MACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,eAAO,KAAK,qBAAqB,eAAc;AAAA,MACjD;AAAA,MACA,OAAO,OAAyB,iBAAiB;AAAA,QAC/C,MAAM;AAAA,MACR,CAAC;AAAA,MACD,OAAO,OAAyB,iBAAiB;AAAA,QAC/C,WAAW,CAAC;AAAA,UACV,SAAS;AAAA,UACT,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM,CAAC,UAAU,uBAAuB,UAAU,CAAC,IAAI,SAAS,GAAG,aAAa,CAAC;AAAA,QACnF,GAAG;AAAA,UACD,SAAS;AAAA,UACT,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,KAAC,MAAM;AACL,OAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,QACrF,MAAM;AAAA,QACN,MAAM,CAAC;AAAA,UACL,WAAW,CAAC;AAAA,YACV,SAAS;AAAA,YACT,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM,CAAC,UAAU,uBAAuB,UAAU,CAAC,IAAI,SAAS,GAAG,aAAa,CAAC;AAAA,UACnF,GAAG;AAAA,YACD,SAAS;AAAA,YACT,UAAU;AAAA,UACZ,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC,GAAG,MAAM,IAAI;AAAA,IAChB,GAAG;AAiCH,IAAM,eAAN,MAAM,cAAa;AAAA,MACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,eAAO,KAAK,qBAAqB,eAAc;AAAA,MACjD;AAAA,MACA,OAAO,QAA0B,mBAAmB;AAAA,QAClD,OAAO;AAAA,QACP,SAAS,SAAS,qBAAqB,mBAAmB;AACxD,cAAI,2BAA2B;AAC/B,cAAI,mBAAmB;AACrB,uCAA2B,KAAK,qBAAqB,eAAc;AAAA,UACrE,OAAO;AACL,uCAA8B,SAAS,gBAAgB;AAAA,UACzD;AACA,iBAAO;AAAA,QACT;AAAA,QACA,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AACA,KAAC,MAAM;AACL,OAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,QACrF,MAAM;AAAA,QACN,MAAM,CAAC;AAAA,UACL,YAAY;AAAA,UACZ,aAAa,WAAW,MAAM,gBAAgB;AAAA,QAChD,CAAC;AAAA,MACH,CAAC,GAAG,MAAM,IAAI;AAAA,IAChB,GAAG;AACH,IAAM,mBAAN,MAAM,0BAAyB,aAAa;AAAA,MAC1C;AAAA,MACA,YAAY,MAAM;AAChB,cAAM;AACN,aAAK,OAAO;AAAA,MACd;AAAA,MACA,SAAS,KAAK,OAAO;AACnB,YAAI,SAAS,KAAM,QAAO;AAC1B,gBAAQ,KAAK;AAAA,UACX,KAAK,gBAAgB;AACnB,mBAAO;AAAA,UACT,KAAK,gBAAgB;AACnB,gBAAI;AAAA,cAAiC;AAAA,cAAO;AAAA;AAAA,YAA4B,GAAG;AACzE,qBAAO,gBAAiB,KAAK;AAAA,YAC/B;AACA,mBAAO,cAAe,KAAK,MAAM,OAAO,KAAK,CAAC,EAAE,SAAS;AAAA,UAC3D,KAAK,gBAAgB;AACnB,gBAAI;AAAA,cAAiC;AAAA,cAAO;AAAA;AAAA,YAA8B,GAAG;AAC3E,qBAAO,gBAAiB,KAAK;AAAA,YAC/B;AACA,mBAAO;AAAA,UACT,KAAK,gBAAgB;AACnB,gBAAI;AAAA,cAAiC;AAAA,cAAO;AAAA;AAAA,YAAgC,GAAG;AAC7E,qBAAO,gBAAiB,KAAK;AAAA,YAC/B;AACA,kBAAM,IAAI,aAAc,OAAyD,OAAO,cAAc,eAAe,cAAc,uCAAuC;AAAA,UAC5K,KAAK,gBAAgB;AACnB,gBAAI;AAAA,cAAiC;AAAA,cAAO;AAAA;AAAA,YAA0B,GAAG;AACvE,qBAAO,gBAAiB,KAAK;AAAA,YAC/B;AACA,mBAAO,aAAc,OAAO,KAAK,CAAC;AAAA,UACpC,KAAK,gBAAgB;AACnB,gBAAI;AAAA,cAAiC;AAAA,cAAO;AAAA;AAAA,YAA0C,GAAG;AACvF,qBAAO,gBAAiB,KAAK;AAAA,YAC/B;AACA,kBAAM,IAAI,aAAc,OAA+D,OAAO,cAAc,eAAe,cAAc,oDAAoD,gBAAiB,GAAG;AAAA,UACnN;AACE,kBAAM,IAAI,aAAc,OAA0D,OAAO,cAAc,eAAe,cAAc,8BAA8B,GAAG,SAAS,gBAAiB,GAAG;AAAA,QACtM;AAAA,MACF;AAAA,MACA,wBAAwB,OAAO;AAC7B,eAAO,4BAA6B,KAAK;AAAA,MAC3C;AAAA,MACA,yBAAyB,OAAO;AAC9B,eAAO,6BAA8B,KAAK;AAAA,MAC5C;AAAA,MACA,0BAA0B,OAAO;AAC/B,eAAO,8BAA+B,KAAK;AAAA,MAC7C;AAAA,MACA,uBAAuB,OAAO;AAC5B,eAAO,2BAA4B,KAAK;AAAA,MAC1C;AAAA,MACA,+BAA+B,OAAO;AACpC,eAAO,mCAAoC,KAAK;AAAA,MAClD;AAAA,MACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,eAAO,KAAK,qBAAqB,mBAAqB,SAAS,QAAQ,CAAC;AAAA,MAC1E;AAAA,MACA,OAAO,QAA0B,mBAAmB;AAAA,QAClD,OAAO;AAAA,QACP,SAAS,kBAAiB;AAAA,QAC1B,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AACA,KAAC,MAAM;AACL,OAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,QACzF,MAAM;AAAA,QACN,MAAM,CAAC;AAAA,UACL,YAAY;AAAA,QACd,CAAC;AAAA,MACH,CAAC,GAAG,MAAM,CAAC;AAAA,QACT,MAAM;AAAA,QACN,YAAY,CAAC;AAAA,UACX,MAAM;AAAA,UACN,MAAM,CAAC,QAAQ;AAAA,QACjB,CAAC;AAAA,MACH,CAAC,GAAG,IAAI;AAAA,IACV,GAAG;AASH,KAAC,SAAUC,uBAAsB;AAC/B,MAAAA,sBAAqBA,sBAAqB,qBAAqB,IAAI,CAAC,IAAI;AACxE,MAAAA,sBAAqBA,sBAAqB,0BAA0B,IAAI,CAAC,IAAI;AAC7E,MAAAA,sBAAqBA,sBAAqB,aAAa,IAAI,CAAC,IAAI;AAChE,MAAAA,sBAAqBA,sBAAqB,aAAa,IAAI,CAAC,IAAI;AAChE,MAAAA,sBAAqBA,sBAAqB,sBAAsB,IAAI,CAAC,IAAI;AAAA,IAC3E,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAsMtD,IAAM,UAAU,IAAI,QAAQ,QAAQ;AAAA;AAAA;", "names": ["withEventReplay", "withI18nSupport", "withIncrementalHydration", "console", "elem", "HydrationFeatureKind"]}
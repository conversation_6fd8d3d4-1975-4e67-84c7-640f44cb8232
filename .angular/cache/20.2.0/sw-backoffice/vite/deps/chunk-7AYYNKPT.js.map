{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/expansion.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/accordion.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, ViewContainerRef, DOCUMENT, NgZone, ElementRef, Renderer2, EventEmitter, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, ContentChild, ViewChild, ChangeDetectorRef, HostAttributeToken, numberAttribute, QueryList, ContentChildren, NgModule } from '@angular/core';\nimport { CdkAccordionItem, CdkAccordion, CdkAccordionModule } from '@angular/cdk/accordion';\nimport { TemplatePortal, CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport { _IdGenerator, FocusMonitor, FocusKeyManager } from '@angular/cdk/a11y';\nimport { startWith, filter, take } from 'rxjs/operators';\nimport { ENTER, hasModifierKey, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, Subscription, EMPTY, merge } from 'rxjs';\nimport { UniqueSelectionDispatcher } from '@angular/cdk/collections';\nimport { _animationsDisabled } from './animation.mjs';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _StructuralStylesLoader } from './structural-styles.mjs';\nimport { MatCommonModule } from './common-module.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/bidi';\n\n/**\n * Token used to provide a `MatAccordion` to `MatExpansionPanel`.\n * Used primarily to avoid circular imports between `MatAccordion` and `MatExpansionPanel`.\n */\nconst _c0 = [\"body\"];\nconst _c1 = [\"bodyWrapper\"];\nconst _c2 = [[[\"mat-expansion-panel-header\"]], \"*\", [[\"mat-action-row\"]]];\nconst _c3 = [\"mat-expansion-panel-header\", \"*\", \"mat-action-row\"];\nfunction MatExpansionPanel_ng_template_7_Template(rf, ctx) {}\nconst _c4 = [[[\"mat-panel-title\"]], [[\"mat-panel-description\"]], \"*\"];\nconst _c5 = [\"mat-panel-title\", \"mat-panel-description\", \"*\"];\nfunction MatExpansionPanelHeader_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵdomElementStart(0, \"span\", 1);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵdomElementStart(1, \"svg\", 2);\n    i0.ɵɵdomElement(2, \"path\", 3);\n    i0.ɵɵdomElementEnd()();\n  }\n}\nconst MAT_ACCORDION = new InjectionToken('MAT_ACCORDION');\n\n/**\n * Token used to provide a `MatExpansionPanel` to `MatExpansionPanelContent`.\n * Used to avoid circular imports between `MatExpansionPanel` and `MatExpansionPanelContent`.\n */\nconst MAT_EXPANSION_PANEL = new InjectionToken('MAT_EXPANSION_PANEL');\n\n/**\n * Expansion panel content that will be rendered lazily\n * after the panel is opened for the first time.\n */\nclass MatExpansionPanelContent {\n  _template = inject(TemplateRef);\n  _expansionPanel = inject(MAT_EXPANSION_PANEL, {\n    optional: true\n  });\n  constructor() {}\n  static ɵfac = function MatExpansionPanelContent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatExpansionPanelContent)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatExpansionPanelContent,\n    selectors: [[\"ng-template\", \"matExpansionPanelContent\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelContent, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matExpansionPanelContent]'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Injection token that can be used to configure the default\n * options for the expansion panel component.\n */\nconst MAT_EXPANSION_PANEL_DEFAULT_OPTIONS = new InjectionToken('MAT_EXPANSION_PANEL_DEFAULT_OPTIONS');\n/**\n * This component can be used as a single element to show expandable content, or as one of\n * multiple children of an element with the MatAccordion directive attached.\n */\nclass MatExpansionPanel extends CdkAccordionItem {\n  _viewContainerRef = inject(ViewContainerRef);\n  _animationsDisabled = _animationsDisabled();\n  _document = inject(DOCUMENT);\n  _ngZone = inject(NgZone);\n  _elementRef = inject(ElementRef);\n  _renderer = inject(Renderer2);\n  _cleanupTransitionEnd;\n  /** Whether the toggle indicator should be hidden. */\n  get hideToggle() {\n    return this._hideToggle || this.accordion && this.accordion.hideToggle;\n  }\n  set hideToggle(value) {\n    this._hideToggle = value;\n  }\n  _hideToggle = false;\n  /** The position of the expansion indicator. */\n  get togglePosition() {\n    return this._togglePosition || this.accordion && this.accordion.togglePosition;\n  }\n  set togglePosition(value) {\n    this._togglePosition = value;\n  }\n  _togglePosition;\n  /** An event emitted after the body's expansion animation happens. */\n  afterExpand = new EventEmitter();\n  /** An event emitted after the body's collapse animation happens. */\n  afterCollapse = new EventEmitter();\n  /** Stream that emits for changes in `@Input` properties. */\n  _inputChanges = new Subject();\n  /** Optionally defined accordion the expansion panel belongs to. */\n  accordion = inject(MAT_ACCORDION, {\n    optional: true,\n    skipSelf: true\n  });\n  /** Content that will be rendered lazily. */\n  _lazyContent;\n  /** Element containing the panel's user-provided content. */\n  _body;\n  /** Element wrapping the panel body. */\n  _bodyWrapper;\n  /** Portal holding the user's content. */\n  _portal;\n  /** ID for the associated header element. Used for a11y labelling. */\n  _headerId = inject(_IdGenerator).getId('mat-expansion-panel-header-');\n  constructor() {\n    super();\n    const defaultOptions = inject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    this._expansionDispatcher = inject(UniqueSelectionDispatcher);\n    if (defaultOptions) {\n      this.hideToggle = defaultOptions.hideToggle;\n    }\n  }\n  /** Determines whether the expansion panel should have spacing between it and its siblings. */\n  _hasSpacing() {\n    if (this.accordion) {\n      return this.expanded && this.accordion.displayMode === 'default';\n    }\n    return false;\n  }\n  /** Gets the expanded state string. */\n  _getExpandedState() {\n    return this.expanded ? 'expanded' : 'collapsed';\n  }\n  /** Toggles the expanded state of the expansion panel. */\n  toggle() {\n    this.expanded = !this.expanded;\n  }\n  /** Sets the expanded state of the expansion panel to false. */\n  close() {\n    this.expanded = false;\n  }\n  /** Sets the expanded state of the expansion panel to true. */\n  open() {\n    this.expanded = true;\n  }\n  ngAfterContentInit() {\n    if (this._lazyContent && this._lazyContent._expansionPanel === this) {\n      // Render the content as soon as the panel becomes open.\n      this.opened.pipe(startWith(null), filter(() => this.expanded && !this._portal), take(1)).subscribe(() => {\n        this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n      });\n    }\n    this._setupAnimationEvents();\n  }\n  ngOnChanges(changes) {\n    this._inputChanges.next(changes);\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._cleanupTransitionEnd?.();\n    this._inputChanges.complete();\n  }\n  /** Checks whether the expansion panel's content contains the currently-focused element. */\n  _containsFocus() {\n    if (this._body) {\n      const focusedElement = this._document.activeElement;\n      const bodyElement = this._body.nativeElement;\n      return focusedElement === bodyElement || bodyElement.contains(focusedElement);\n    }\n    return false;\n  }\n  _transitionEndListener = ({\n    target,\n    propertyName\n  }) => {\n    if (target === this._bodyWrapper?.nativeElement && propertyName === 'grid-template-rows') {\n      this._ngZone.run(() => {\n        if (this.expanded) {\n          this.afterExpand.emit();\n        } else {\n          this.afterCollapse.emit();\n        }\n      });\n    }\n  };\n  _setupAnimationEvents() {\n    // This method is defined separately, because we need to\n    // disable this logic in some internal components.\n    this._ngZone.runOutsideAngular(() => {\n      if (this._animationsDisabled) {\n        this.opened.subscribe(() => this._ngZone.run(() => this.afterExpand.emit()));\n        this.closed.subscribe(() => this._ngZone.run(() => this.afterCollapse.emit()));\n      } else {\n        setTimeout(() => {\n          const element = this._elementRef.nativeElement;\n          this._cleanupTransitionEnd = this._renderer.listen(element, 'transitionend', this._transitionEndListener);\n          element.classList.add('mat-expansion-panel-animations-enabled');\n        }, 200);\n      }\n    });\n  }\n  static ɵfac = function MatExpansionPanel_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatExpansionPanel)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatExpansionPanel,\n    selectors: [[\"mat-expansion-panel\"]],\n    contentQueries: function MatExpansionPanel_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelContent, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lazyContent = _t.first);\n      }\n    },\n    viewQuery: function MatExpansionPanel_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._body = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._bodyWrapper = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-expansion-panel\"],\n    hostVars: 4,\n    hostBindings: function MatExpansionPanel_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-expanded\", ctx.expanded)(\"mat-expansion-panel-spacing\", ctx._hasSpacing());\n      }\n    },\n    inputs: {\n      hideToggle: [2, \"hideToggle\", \"hideToggle\", booleanAttribute],\n      togglePosition: \"togglePosition\"\n    },\n    outputs: {\n      afterExpand: \"afterExpand\",\n      afterCollapse: \"afterCollapse\"\n    },\n    exportAs: [\"matExpansionPanel\"],\n    features: [i0.ɵɵProvidersFeature([\n    // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n    // to the same accordion.\n    {\n      provide: MAT_ACCORDION,\n      useValue: undefined\n    }, {\n      provide: MAT_EXPANSION_PANEL,\n      useExisting: MatExpansionPanel\n    }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c3,\n    decls: 9,\n    vars: 4,\n    consts: [[\"bodyWrapper\", \"\"], [\"body\", \"\"], [1, \"mat-expansion-panel-content-wrapper\"], [\"role\", \"region\", 1, \"mat-expansion-panel-content\", 3, \"id\"], [1, \"mat-expansion-panel-body\"], [3, \"cdkPortalOutlet\"]],\n    template: function MatExpansionPanel_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c2);\n        i0.ɵɵprojection(0);\n        i0.ɵɵelementStart(1, \"div\", 2, 0)(3, \"div\", 3, 1)(5, \"div\", 4);\n        i0.ɵɵprojection(6, 1);\n        i0.ɵɵtemplate(7, MatExpansionPanel_ng_template_7_Template, 0, 0, \"ng-template\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵprojection(8, 2);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵattribute(\"inert\", ctx.expanded ? null : \"\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"aria-labelledby\", ctx._headerId);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"cdkPortalOutlet\", ctx._portal);\n      }\n    },\n    dependencies: [CdkPortalOutlet],\n    styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;overflow:hidden;position:relative;background:var(--mat-expansion-container-background-color, var(--mat-sys-surface));color:var(--mat-expansion-container-text-color, var(--mat-sys-on-surface));border-radius:var(--mat-expansion-container-shape, 12px)}.mat-expansion-panel.mat-expansion-panel-animations-enabled{transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:var(--mat-expansion-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12))}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape, 12px);border-top-left-radius:var(--mat-expansion-container-shape, 12px)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape, 12px);border-bottom-left-radius:var(--mat-expansion-container-shape, 12px)}@media(forced-colors: active){.mat-expansion-panel{outline:solid 1px}}.mat-expansion-panel-content-wrapper{display:grid;grid-template-rows:0fr;grid-template-columns:100%}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content-wrapper{transition:grid-template-rows 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{grid-template-rows:1fr}@supports not (grid-template-rows: 0fr){.mat-expansion-panel-content-wrapper{height:0}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{height:auto}}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;min-height:0;visibility:hidden;font-family:var(--mat-expansion-container-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-expansion-container-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-expansion-container-text-weight, var(--mat-sys-body-large-weight));line-height:var(--mat-expansion-container-text-line-height, var(--mat-sys-body-large-line-height));letter-spacing:var(--mat-expansion-container-text-tracking, var(--mat-sys-body-large-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content{transition:visibility 190ms linear}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper>.mat-expansion-panel-content{visibility:visible}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color, var(--mat-sys-outline))}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanel, [{\n    type: Component,\n    args: [{\n      selector: 'mat-expansion-panel',\n      exportAs: 'matExpansionPanel',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [\n      // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n      // to the same accordion.\n      {\n        provide: MAT_ACCORDION,\n        useValue: undefined\n      }, {\n        provide: MAT_EXPANSION_PANEL,\n        useExisting: MatExpansionPanel\n      }],\n      host: {\n        'class': 'mat-expansion-panel',\n        '[class.mat-expanded]': 'expanded',\n        '[class.mat-expansion-panel-spacing]': '_hasSpacing()'\n      },\n      imports: [CdkPortalOutlet],\n      template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content-wrapper\\\" [attr.inert]=\\\"expanded ? null : ''\\\" #bodyWrapper>\\n  <div class=\\\"mat-expansion-panel-content\\\"\\n       role=\\\"region\\\"\\n       [attr.aria-labelledby]=\\\"_headerId\\\"\\n       [id]=\\\"id\\\"\\n       #body>\\n    <div class=\\\"mat-expansion-panel-body\\\">\\n      <ng-content></ng-content>\\n      <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n    </div>\\n    <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;overflow:hidden;position:relative;background:var(--mat-expansion-container-background-color, var(--mat-sys-surface));color:var(--mat-expansion-container-text-color, var(--mat-sys-on-surface));border-radius:var(--mat-expansion-container-shape, 12px)}.mat-expansion-panel.mat-expansion-panel-animations-enabled{transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:var(--mat-expansion-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12))}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape, 12px);border-top-left-radius:var(--mat-expansion-container-shape, 12px)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape, 12px);border-bottom-left-radius:var(--mat-expansion-container-shape, 12px)}@media(forced-colors: active){.mat-expansion-panel{outline:solid 1px}}.mat-expansion-panel-content-wrapper{display:grid;grid-template-rows:0fr;grid-template-columns:100%}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content-wrapper{transition:grid-template-rows 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{grid-template-rows:1fr}@supports not (grid-template-rows: 0fr){.mat-expansion-panel-content-wrapper{height:0}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{height:auto}}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;min-height:0;visibility:hidden;font-family:var(--mat-expansion-container-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-expansion-container-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-expansion-container-text-weight, var(--mat-sys-body-large-weight));line-height:var(--mat-expansion-container-text-line-height, var(--mat-sys-body-large-line-height));letter-spacing:var(--mat-expansion-container-text-tracking, var(--mat-sys-body-large-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content{transition:visibility 190ms linear}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper>.mat-expansion-panel-content{visibility:visible}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color, var(--mat-sys-outline))}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\\n\"]\n    }]\n  }], () => [], {\n    hideToggle: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    togglePosition: [{\n      type: Input\n    }],\n    afterExpand: [{\n      type: Output\n    }],\n    afterCollapse: [{\n      type: Output\n    }],\n    _lazyContent: [{\n      type: ContentChild,\n      args: [MatExpansionPanelContent]\n    }],\n    _body: [{\n      type: ViewChild,\n      args: ['body']\n    }],\n    _bodyWrapper: [{\n      type: ViewChild,\n      args: ['bodyWrapper']\n    }]\n  });\n})();\n/**\n * Actions of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelActionRow {\n  static ɵfac = function MatExpansionPanelActionRow_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatExpansionPanelActionRow)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatExpansionPanelActionRow,\n    selectors: [[\"mat-action-row\"]],\n    hostAttrs: [1, \"mat-action-row\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelActionRow, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-action-row',\n      host: {\n        class: 'mat-action-row'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Header element of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelHeader {\n  panel = inject(MatExpansionPanel, {\n    host: true\n  });\n  _element = inject(ElementRef);\n  _focusMonitor = inject(FocusMonitor);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _parentChangeSubscription = Subscription.EMPTY;\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const panel = this.panel;\n    const defaultOptions = inject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    });\n    const accordionHideToggleChange = panel.accordion ? panel.accordion._stateChanges.pipe(filter(changes => !!(changes['hideToggle'] || changes['togglePosition']))) : EMPTY;\n    this.tabIndex = parseInt(tabIndex || '') || 0;\n    // Since the toggle state depends on an @Input on the panel, we\n    // need to subscribe and trigger change detection manually.\n    this._parentChangeSubscription = merge(panel.opened, panel.closed, accordionHideToggleChange, panel._inputChanges.pipe(filter(changes => {\n      return !!(changes['hideToggle'] || changes['disabled'] || changes['togglePosition']);\n    }))).subscribe(() => this._changeDetectorRef.markForCheck());\n    // Avoids focus being lost if the panel contained the focused element and was closed.\n    panel.closed.pipe(filter(() => panel._containsFocus())).subscribe(() => this._focusMonitor.focusVia(this._element, 'program'));\n    if (defaultOptions) {\n      this.expandedHeight = defaultOptions.expandedHeight;\n      this.collapsedHeight = defaultOptions.collapsedHeight;\n    }\n  }\n  /** Height of the header while the panel is expanded. */\n  expandedHeight;\n  /** Height of the header while the panel is collapsed. */\n  collapsedHeight;\n  /** Tab index of the header. */\n  tabIndex = 0;\n  /**\n   * Whether the associated panel is disabled. Implemented as a part of `FocusableOption`.\n   * @docs-private\n   */\n  get disabled() {\n    return this.panel.disabled;\n  }\n  /** Toggles the expanded state of the panel. */\n  _toggle() {\n    if (!this.disabled) {\n      this.panel.toggle();\n    }\n  }\n  /** Gets whether the panel is expanded. */\n  _isExpanded() {\n    return this.panel.expanded;\n  }\n  /** Gets the expanded state string of the panel. */\n  _getExpandedState() {\n    return this.panel._getExpandedState();\n  }\n  /** Gets the panel id. */\n  _getPanelId() {\n    return this.panel.id;\n  }\n  /** Gets the toggle position for the header. */\n  _getTogglePosition() {\n    return this.panel.togglePosition;\n  }\n  /** Gets whether the expand indicator should be shown. */\n  _showToggle() {\n    return !this.panel.hideToggle && !this.panel.disabled;\n  }\n  /**\n   * Gets the current height of the header. Null if no custom height has been\n   * specified, and if the default height from the stylesheet should be used.\n   */\n  _getHeaderHeight() {\n    const isExpanded = this._isExpanded();\n    if (isExpanded && this.expandedHeight) {\n      return this.expandedHeight;\n    } else if (!isExpanded && this.collapsedHeight) {\n      return this.collapsedHeight;\n    }\n    return null;\n  }\n  /** Handle keydown event calling to toggle() if appropriate. */\n  _keydown(event) {\n    switch (event.keyCode) {\n      // Toggle for space and enter keys.\n      case SPACE:\n      case ENTER:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this._toggle();\n        }\n        break;\n      default:\n        if (this.panel.accordion) {\n          this.panel.accordion._handleHeaderKeydown(event);\n        }\n        return;\n    }\n  }\n  /**\n   * Focuses the panel header. Implemented as a part of `FocusableOption`.\n   * @param origin Origin of the action that triggered the focus.\n   * @docs-private\n   */\n  focus(origin, options) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._element, origin, options);\n    } else {\n      this._element.nativeElement.focus(options);\n    }\n  }\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._element).subscribe(origin => {\n      if (origin && this.panel.accordion) {\n        this.panel.accordion._handleHeaderFocus(this);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._parentChangeSubscription.unsubscribe();\n    this._focusMonitor.stopMonitoring(this._element);\n  }\n  static ɵfac = function MatExpansionPanelHeader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatExpansionPanelHeader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatExpansionPanelHeader,\n    selectors: [[\"mat-expansion-panel-header\"]],\n    hostAttrs: [\"role\", \"button\", 1, \"mat-expansion-panel-header\", \"mat-focus-indicator\"],\n    hostVars: 13,\n    hostBindings: function MatExpansionPanelHeader_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatExpansionPanelHeader_click_HostBindingHandler() {\n          return ctx._toggle();\n        })(\"keydown\", function MatExpansionPanelHeader_keydown_HostBindingHandler($event) {\n          return ctx._keydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"id\", ctx.panel._headerId)(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-controls\", ctx._getPanelId())(\"aria-expanded\", ctx._isExpanded())(\"aria-disabled\", ctx.panel.disabled);\n        i0.ɵɵstyleProp(\"height\", ctx._getHeaderHeight());\n        i0.ɵɵclassProp(\"mat-expanded\", ctx._isExpanded())(\"mat-expansion-toggle-indicator-after\", ctx._getTogglePosition() === \"after\")(\"mat-expansion-toggle-indicator-before\", ctx._getTogglePosition() === \"before\");\n      }\n    },\n    inputs: {\n      expandedHeight: \"expandedHeight\",\n      collapsedHeight: \"collapsedHeight\",\n      tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)]\n    },\n    ngContentSelectors: _c5,\n    decls: 5,\n    vars: 3,\n    consts: [[1, \"mat-content\"], [1, \"mat-expansion-indicator\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 -960 960 960\", \"aria-hidden\", \"true\", \"focusable\", \"false\"], [\"d\", \"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\"]],\n    template: function MatExpansionPanelHeader_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c4);\n        i0.ɵɵdomElementStart(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵprojection(2, 1);\n        i0.ɵɵprojection(3, 2);\n        i0.ɵɵdomElementEnd();\n        i0.ɵɵconditionalCreate(4, MatExpansionPanelHeader_Conditional_4_Template, 3, 0, \"span\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-content-hide-toggle\", !ctx._showToggle());\n        i0.ɵɵadvance(4);\n        i0.ɵɵconditional(ctx._showToggle() ? 4 : -1);\n      }\n    },\n    styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;height:var(--mat-expansion-header-collapsed-state-height, 48px);font-family:var(--mat-expansion-header-text-font, var(--mat-sys-title-medium-font));font-size:var(--mat-expansion-header-text-size, var(--mat-sys-title-medium-size));font-weight:var(--mat-expansion-header-text-weight, var(--mat-sys-title-medium-weight));line-height:var(--mat-expansion-header-text-line-height, var(--mat-sys-title-medium-line-height));letter-spacing:var(--mat-expansion-header-text-tracking, var(--mat-sys-title-medium-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-header{transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header::before{border-radius:inherit}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height, 64px)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color, var(--mat-sys-surface))}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color, var(--mat-sys-on-surface))}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color, var(--mat-sys-on-surface-variant))}.mat-expansion-panel-animations-enabled .mat-expansion-indicator{transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header.mat-expanded .mat-expansion-indicator{transform:rotate(180deg)}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-legacy-header-indicator-display, none)}.mat-expansion-indicator svg{width:24px;height:24px;margin:0 -8px;vertical-align:middle;fill:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-header-indicator-display, inline-block)}@media(forced-colors: active){.mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-expansion-panel-header',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-expansion-panel-header mat-focus-indicator',\n        'role': 'button',\n        '[attr.id]': 'panel._headerId',\n        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n        '[attr.aria-controls]': '_getPanelId()',\n        '[attr.aria-expanded]': '_isExpanded()',\n        '[attr.aria-disabled]': 'panel.disabled',\n        '[class.mat-expanded]': '_isExpanded()',\n        '[class.mat-expansion-toggle-indicator-after]': `_getTogglePosition() === 'after'`,\n        '[class.mat-expansion-toggle-indicator-before]': `_getTogglePosition() === 'before'`,\n        '[style.height]': '_getHeaderHeight()',\n        '(click)': '_toggle()',\n        '(keydown)': '_keydown($event)'\n      },\n      template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n\\n@if (_showToggle()) {\\n  <span class=\\\"mat-expansion-indicator\\\">\\n    <svg\\n      xmlns=\\\"http://www.w3.org/2000/svg\\\"\\n      viewBox=\\\"0 -960 960 960\\\"\\n      aria-hidden=\\\"true\\\"\\n      focusable=\\\"false\\\">\\n      <path d=\\\"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\\\"/>\\n    </svg>\\n  </span>\\n}\\n\",\n      styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;height:var(--mat-expansion-header-collapsed-state-height, 48px);font-family:var(--mat-expansion-header-text-font, var(--mat-sys-title-medium-font));font-size:var(--mat-expansion-header-text-size, var(--mat-sys-title-medium-size));font-weight:var(--mat-expansion-header-text-weight, var(--mat-sys-title-medium-weight));line-height:var(--mat-expansion-header-text-line-height, var(--mat-sys-title-medium-line-height));letter-spacing:var(--mat-expansion-header-text-tracking, var(--mat-sys-title-medium-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-header{transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header::before{border-radius:inherit}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height, 64px)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color, var(--mat-sys-surface))}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color, var(--mat-sys-on-surface))}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color, var(--mat-sys-on-surface-variant))}.mat-expansion-panel-animations-enabled .mat-expansion-indicator{transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header.mat-expanded .mat-expansion-indicator{transform:rotate(180deg)}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-legacy-header-indicator-display, none)}.mat-expansion-indicator svg{width:24px;height:24px;margin:0 -8px;vertical-align:middle;fill:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-header-indicator-display, inline-block)}@media(forced-colors: active){.mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}}\\n\"]\n    }]\n  }], () => [], {\n    expandedHeight: [{\n      type: Input\n    }],\n    collapsedHeight: [{\n      type: Input\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }]\n  });\n})();\n/**\n * Description element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelDescription {\n  static ɵfac = function MatExpansionPanelDescription_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatExpansionPanelDescription)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatExpansionPanelDescription,\n    selectors: [[\"mat-panel-description\"]],\n    hostAttrs: [1, \"mat-expansion-panel-header-description\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelDescription, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-panel-description',\n      host: {\n        class: 'mat-expansion-panel-header-description'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Title element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelTitle {\n  static ɵfac = function MatExpansionPanelTitle_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatExpansionPanelTitle)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatExpansionPanelTitle,\n    selectors: [[\"mat-panel-title\"]],\n    hostAttrs: [1, \"mat-expansion-panel-header-title\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelTitle, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-panel-title',\n      host: {\n        class: 'mat-expansion-panel-header-title'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Directive for a Material Design Accordion.\n */\nclass MatAccordion extends CdkAccordion {\n  _keyManager;\n  /** Headers belonging to this accordion. */\n  _ownHeaders = new QueryList();\n  /** All headers inside the accordion. Includes headers inside nested accordions. */\n  _headers;\n  /** Whether the expansion indicator should be hidden. */\n  hideToggle = false;\n  /**\n   * Display mode used for all expansion panels in the accordion. Currently two display\n   * modes exist:\n   *  default - a gutter-like spacing is placed around any expanded panel, placing the expanded\n   *     panel at a different elevation from the rest of the accordion.\n   *  flat - no spacing is placed around expanded panels, showing all panels at the same\n   *     elevation.\n   */\n  displayMode = 'default';\n  /** The position of the expansion indicator. */\n  togglePosition = 'after';\n  ngAfterContentInit() {\n    this._headers.changes.pipe(startWith(this._headers)).subscribe(headers => {\n      this._ownHeaders.reset(headers.filter(header => header.panel.accordion === this));\n      this._ownHeaders.notifyOnChanges();\n    });\n    this._keyManager = new FocusKeyManager(this._ownHeaders).withWrap().withHomeAndEnd();\n  }\n  /** Handles keyboard events coming in from the panel headers. */\n  _handleHeaderKeydown(event) {\n    this._keyManager.onKeydown(event);\n  }\n  _handleHeaderFocus(header) {\n    this._keyManager.updateActiveItem(header);\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._keyManager?.destroy();\n    this._ownHeaders.destroy();\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatAccordion_BaseFactory;\n    return function MatAccordion_Factory(__ngFactoryType__) {\n      return (ɵMatAccordion_BaseFactory || (ɵMatAccordion_BaseFactory = i0.ɵɵgetInheritedFactory(MatAccordion)))(__ngFactoryType__ || MatAccordion);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatAccordion,\n    selectors: [[\"mat-accordion\"]],\n    contentQueries: function MatAccordion_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelHeader, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._headers = _t);\n      }\n    },\n    hostAttrs: [1, \"mat-accordion\"],\n    hostVars: 2,\n    hostBindings: function MatAccordion_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-accordion-multi\", ctx.multi);\n      }\n    },\n    inputs: {\n      hideToggle: [2, \"hideToggle\", \"hideToggle\", booleanAttribute],\n      displayMode: \"displayMode\",\n      togglePosition: \"togglePosition\"\n    },\n    exportAs: [\"matAccordion\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_ACCORDION,\n      useExisting: MatAccordion\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAccordion, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-accordion',\n      exportAs: 'matAccordion',\n      providers: [{\n        provide: MAT_ACCORDION,\n        useExisting: MatAccordion\n      }],\n      host: {\n        class: 'mat-accordion',\n        // Class binding which is only used by the test harness as there is no other\n        // way for the harness to detect if multiple panel support is enabled.\n        '[class.mat-accordion-multi]': 'this.multi'\n      }\n    }]\n  }], null, {\n    _headers: [{\n      type: ContentChildren,\n      args: [MatExpansionPanelHeader, {\n        descendants: true\n      }]\n    }],\n    hideToggle: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    displayMode: [{\n      type: Input\n    }],\n    togglePosition: [{\n      type: Input\n    }]\n  });\n})();\nclass MatExpansionModule {\n  static ɵfac = function MatExpansionModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatExpansionModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatExpansionModule,\n    imports: [MatCommonModule, CdkAccordionModule, PortalModule, MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent],\n    exports: [MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, CdkAccordionModule, PortalModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CdkAccordionModule, PortalModule, MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent],\n      exports: [MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Time and timing curve for expansion panel animations.\n * @deprecated No longer used. Will be removed.\n * @breaking-change 21.0.0\n */\nconst EXPANSION_PANEL_ANIMATION_TIMING = '225ms cubic-bezier(0.4,0.0,0.2,1)';\n/**\n * Animations used by the Material expansion panel.\n *\n * A bug in angular animation's `state` when ViewContainers are moved using ViewContainerRef.move()\n * causes the animation state of moved components to become `void` upon exit, and not update again\n * upon reentry into the DOM. This can lead a to situation for the expansion panel where the state\n * of the panel is `expanded` or `collapsed` but the animation state is `void`.\n *\n * To correctly handle animating to the next state, we animate between `void` and `collapsed` which\n * are defined to have the same styles. Since angular animates from the current styles to the\n * destination state's style definition, in situations where we are moving from `void`'s styles to\n * `collapsed` this acts a noop since no style values change.\n *\n * In the case where angular's animation state is out of sync with the expansion panel's state, the\n * expansion panel being `expanded` and angular animations being `void`, the animation from the\n * `expanded`'s effective styles (though in a `void` animation state) to the collapsed state will\n * occur as expected.\n *\n * Angular Bug: https://github.com/angular/angular/issues/18847\n *\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nconst matExpansionAnimations = {\n  // Represents:\n  // trigger('indicatorRotate', [\n  //   state('collapsed, void', style({transform: 'rotate(0deg)'})),\n  //   state('expanded', style({transform: 'rotate(180deg)'})),\n  //   transition(\n  //     'expanded <=> collapsed, void => collapsed',\n  //     animate(EXPANSION_PANEL_ANIMATION_TIMING),\n  //   ),\n  // ])\n  /** Animation that rotates the indicator arrow. */\n  indicatorRotate: {\n    type: 7,\n    name: 'indicatorRotate',\n    definitions: [{\n      type: 0,\n      name: 'collapsed, void',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(0deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'expanded',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(180deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'expanded <=> collapsed, void => collapsed',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('bodyExpansion', [\n  //   state('collapsed, void', style({height: '0px', visibility: 'hidden'})),\n  //   // Clear the `visibility` while open, otherwise the content will be visible when placed in\n  //   // a parent that's `visibility: hidden`, because `visibility` doesn't apply to descendants\n  //   // that have a `visibility` of their own (see #27436).\n  //   state('expanded', style({height: '*', visibility: ''})),\n  //   transition(\n  //     'expanded <=> collapsed, void => collapsed',\n  //     animate(EXPANSION_PANEL_ANIMATION_TIMING),\n  //   ),\n  // ])\n  /** Animation that expands and collapses the panel content. */\n  bodyExpansion: {\n    type: 7,\n    name: 'bodyExpansion',\n    definitions: [{\n      type: 0,\n      name: 'collapsed, void',\n      styles: {\n        type: 6,\n        styles: {\n          'height': '0px',\n          'visibility': 'hidden'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'expanded',\n      styles: {\n        type: 6,\n        styles: {\n          'height': '*',\n          'visibility': ''\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'expanded <=> collapsed, void => collapsed',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { EXPANSION_PANEL_ANIMATION_TIMING, MAT_ACCORDION, MAT_EXPANSION_PANEL, MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, MatAccordion, MatExpansionModule, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelContent, MatExpansionPanelDescription, MatExpansionPanelHeader, MatExpansionPanelTitle, matExpansionAnimations };\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, booleanAttribute, Directive, Input, ChangeDetectorRef, EventEmitter, signal, Output, NgModule } from '@angular/core';\nimport { Subject, Subscription } from 'rxjs';\nimport { _IdGenerator } from './id-generator.mjs';\nimport { UniqueSelectionDispatcher } from './unique-selection-dispatcher.mjs';\n\n/**\n * Injection token that can be used to reference instances of `CdkAccordion`. It serves\n * as alternative token to the actual `CdkAccordion` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_ACCORDION = new InjectionToken('CdkAccordion');\n/**\n * Directive whose purpose is to manage the expanded state of CdkAccordionItem children.\n */\nclass CdkAccordion {\n  /** Emits when the state of the accordion changes */\n  _stateChanges = new Subject();\n  /** Stream that emits true/false when openAll/closeAll is triggered. */\n  _openCloseAllActions = new Subject();\n  /** A readonly id value to use for unique selection coordination. */\n  id = inject(_IdGenerator).getId('cdk-accordion-');\n  /** Whether the accordion should allow multiple expanded accordion items simultaneously. */\n  multi = false;\n  /** Opens all enabled accordion items in an accordion where multi is enabled. */\n  openAll() {\n    if (this.multi) {\n      this._openCloseAllActions.next(true);\n    }\n  }\n  /** Closes all enabled accordion items. */\n  closeAll() {\n    this._openCloseAllActions.next(false);\n  }\n  ngOnChanges(changes) {\n    this._stateChanges.next(changes);\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n    this._openCloseAllActions.complete();\n  }\n  static ɵfac = function CdkAccordion_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkAccordion)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkAccordion,\n    selectors: [[\"cdk-accordion\"], [\"\", \"cdkAccordion\", \"\"]],\n    inputs: {\n      multi: [2, \"multi\", \"multi\", booleanAttribute]\n    },\n    exportAs: [\"cdkAccordion\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CDK_ACCORDION,\n      useExisting: CdkAccordion\n    }]), i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAccordion, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-accordion, [cdkAccordion]',\n      exportAs: 'cdkAccordion',\n      providers: [{\n        provide: CDK_ACCORDION,\n        useExisting: CdkAccordion\n      }]\n    }]\n  }], null, {\n    multi: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * A basic directive expected to be extended and decorated as a component.  Sets up all\n * events and attributes needed to be managed by a CdkAccordion parent.\n */\nclass CdkAccordionItem {\n  accordion = inject(CDK_ACCORDION, {\n    optional: true,\n    skipSelf: true\n  });\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _expansionDispatcher = inject(UniqueSelectionDispatcher);\n  /** Subscription to openAll/closeAll events. */\n  _openCloseAllSubscription = Subscription.EMPTY;\n  /** Event emitted every time the AccordionItem is closed. */\n  closed = new EventEmitter();\n  /** Event emitted every time the AccordionItem is opened. */\n  opened = new EventEmitter();\n  /** Event emitted when the AccordionItem is destroyed. */\n  destroyed = new EventEmitter();\n  /**\n   * Emits whenever the expanded state of the accordion changes.\n   * Primarily used to facilitate two-way binding.\n   * @docs-private\n   */\n  expandedChange = new EventEmitter();\n  /** The unique AccordionItem id. */\n  id = inject(_IdGenerator).getId('cdk-accordion-child-');\n  /** Whether the AccordionItem is expanded. */\n  get expanded() {\n    return this._expanded;\n  }\n  set expanded(expanded) {\n    // Only emit events and update the internal value if the value changes.\n    if (this._expanded !== expanded) {\n      this._expanded = expanded;\n      this.expandedChange.emit(expanded);\n      if (expanded) {\n        this.opened.emit();\n        /**\n         * In the unique selection dispatcher, the id parameter is the id of the CdkAccordionItem,\n         * the name value is the id of the accordion.\n         */\n        const accordionId = this.accordion ? this.accordion.id : this.id;\n        this._expansionDispatcher.notify(this.id, accordionId);\n      } else {\n        this.closed.emit();\n      }\n      // Ensures that the animation will run when the value is set outside of an `@Input`.\n      // This includes cases like the open, close and toggle methods.\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  _expanded = false;\n  /** Whether the AccordionItem is disabled. */\n  get disabled() {\n    return this._disabled();\n  }\n  set disabled(value) {\n    this._disabled.set(value);\n  }\n  _disabled = signal(false, ...(ngDevMode ? [{\n    debugName: \"_disabled\"\n  }] : []));\n  /** Unregister function for _expansionDispatcher. */\n  _removeUniqueSelectionListener = () => {};\n  constructor() {}\n  ngOnInit() {\n    this._removeUniqueSelectionListener = this._expansionDispatcher.listen((id, accordionId) => {\n      if (this.accordion && !this.accordion.multi && this.accordion.id === accordionId && this.id !== id) {\n        this.expanded = false;\n      }\n    });\n    // When an accordion item is hosted in an accordion, subscribe to open/close events.\n    if (this.accordion) {\n      this._openCloseAllSubscription = this._subscribeToOpenCloseAllActions();\n    }\n  }\n  /** Emits an event for the accordion item being destroyed. */\n  ngOnDestroy() {\n    this.opened.complete();\n    this.closed.complete();\n    this.destroyed.emit();\n    this.destroyed.complete();\n    this._removeUniqueSelectionListener();\n    this._openCloseAllSubscription.unsubscribe();\n  }\n  /** Toggles the expanded state of the accordion item. */\n  toggle() {\n    if (!this.disabled) {\n      this.expanded = !this.expanded;\n    }\n  }\n  /** Sets the expanded state of the accordion item to false. */\n  close() {\n    if (!this.disabled) {\n      this.expanded = false;\n    }\n  }\n  /** Sets the expanded state of the accordion item to true. */\n  open() {\n    if (!this.disabled) {\n      this.expanded = true;\n    }\n  }\n  _subscribeToOpenCloseAllActions() {\n    return this.accordion._openCloseAllActions.subscribe(expanded => {\n      // Only change expanded state if item is enabled\n      if (!this.disabled) {\n        this.expanded = expanded;\n      }\n    });\n  }\n  static ɵfac = function CdkAccordionItem_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkAccordionItem)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkAccordionItem,\n    selectors: [[\"cdk-accordion-item\"], [\"\", \"cdkAccordionItem\", \"\"]],\n    inputs: {\n      expanded: [2, \"expanded\", \"expanded\", booleanAttribute],\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n    },\n    outputs: {\n      closed: \"closed\",\n      opened: \"opened\",\n      destroyed: \"destroyed\",\n      expandedChange: \"expandedChange\"\n    },\n    exportAs: [\"cdkAccordionItem\"],\n    features: [i0.ɵɵProvidersFeature([\n    // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n    // registering to the same accordion.\n    {\n      provide: CDK_ACCORDION,\n      useValue: undefined\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAccordionItem, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-accordion-item, [cdkAccordionItem]',\n      exportAs: 'cdkAccordionItem',\n      providers: [\n      // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n      // registering to the same accordion.\n      {\n        provide: CDK_ACCORDION,\n        useValue: undefined\n      }]\n    }]\n  }], () => [], {\n    closed: [{\n      type: Output\n    }],\n    opened: [{\n      type: Output\n    }],\n    destroyed: [{\n      type: Output\n    }],\n    expandedChange: [{\n      type: Output\n    }],\n    expanded: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass CdkAccordionModule {\n  static ɵfac = function CdkAccordionModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkAccordionModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CdkAccordionModule,\n    imports: [CdkAccordion, CdkAccordionItem],\n    exports: [CdkAccordion, CdkAccordionItem]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAccordionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkAccordion, CdkAccordionItem],\n      exports: [CdkAccordion, CdkAccordionItem]\n    }]\n  }], null, null);\n})();\nexport { CDK_ACCORDION, CdkAccordion, CdkAccordionItem, CdkAccordionModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;ACDA;AACA;AACA;AASA,IAAM,gBAAgB,IAAI,eAAe,cAAc;AAIvD,IAAM,eAAN,MAAM,cAAa;AAAA;AAAA,EAEjB,gBAAgB,IAAI,QAAQ;AAAA;AAAA,EAE5B,uBAAuB,IAAI,QAAQ;AAAA;AAAA,EAEnC,KAAK,OAAO,YAAY,EAAE,MAAM,gBAAgB;AAAA;AAAA,EAEhD,QAAQ;AAAA;AAAA,EAER,UAAU;AACR,QAAI,KAAK,OAAO;AACd,WAAK,qBAAqB,KAAK,IAAI;AAAA,IACrC;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AACT,SAAK,qBAAqB,KAAK,KAAK;AAAA,EACtC;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,cAAc,KAAK,OAAO;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,SAAS;AAC5B,SAAK,qBAAqB,SAAS;AAAA,EACrC;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,GAAG,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,IACvD,QAAQ;AAAA,MACN,OAAO,CAAC,GAAG,SAAS,SAAS,gBAAgB;AAAA,IAC/C;AAAA,IACA,UAAU,CAAC,cAAc;AAAA,IACzB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,oBAAoB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,OAAO,eAAe;AAAA,IAChC,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,uBAAuB,OAAO,yBAAyB;AAAA;AAAA,EAEvD,4BAA4B,aAAa;AAAA;AAAA,EAEzC,SAAS,IAAI,aAAa;AAAA;AAAA,EAE1B,SAAS,IAAI,aAAa;AAAA;AAAA,EAE1B,YAAY,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7B,iBAAiB,IAAI,aAAa;AAAA;AAAA,EAElC,KAAK,OAAO,YAAY,EAAE,MAAM,sBAAsB;AAAA;AAAA,EAEtD,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,UAAU;AAErB,QAAI,KAAK,cAAc,UAAU;AAC/B,WAAK,YAAY;AACjB,WAAK,eAAe,KAAK,QAAQ;AACjC,UAAI,UAAU;AACZ,aAAK,OAAO,KAAK;AAKjB,cAAM,cAAc,KAAK,YAAY,KAAK,UAAU,KAAK,KAAK;AAC9D,aAAK,qBAAqB,OAAO,KAAK,IAAI,WAAW;AAAA,MACvD,OAAO;AACL,aAAK,OAAO,KAAK;AAAA,MACnB;AAGA,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,WAAW;AACb,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,UAAU,IAAI,KAAK;AAAA,EAC1B;AAAA,EACA,YAAY,OAAO,OAAO,GAAI,YAAY,CAAC;AAAA,IACzC,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA;AAAA,EAER,iCAAiC,MAAM;AAAA,EAAC;AAAA,EACxC,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,SAAK,iCAAiC,KAAK,qBAAqB,OAAO,CAAC,IAAI,gBAAgB;AAC1F,UAAI,KAAK,aAAa,CAAC,KAAK,UAAU,SAAS,KAAK,UAAU,OAAO,eAAe,KAAK,OAAO,IAAI;AAClG,aAAK,WAAW;AAAA,MAClB;AAAA,IACF,CAAC;AAED,QAAI,KAAK,WAAW;AAClB,WAAK,4BAA4B,KAAK,gCAAgC;AAAA,IACxE;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,OAAO,SAAS;AACrB,SAAK,OAAO,SAAS;AACrB,SAAK,UAAU,KAAK;AACpB,SAAK,UAAU,SAAS;AACxB,SAAK,+BAA+B;AACpC,SAAK,0BAA0B,YAAY;AAAA,EAC7C;AAAA;AAAA,EAEA,SAAS;AACP,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW,CAAC,KAAK;AAAA,IACxB;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ;AACN,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA;AAAA,EAEA,OAAO;AACL,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,kCAAkC;AAChC,WAAO,KAAK,UAAU,qBAAqB,UAAU,cAAY;AAE/D,UAAI,CAAC,KAAK,UAAU;AAClB,aAAK,WAAW;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,oBAAoB,GAAG,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,IAChE,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACxD;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU,CAAC,kBAAkB;AAAA,IAC7B,UAAU,CAAI,mBAAmB;AAAA;AAAA;AAAA,MAGjC;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA;AAAA;AAAA,QAGX;AAAA,UACE,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,cAAc,gBAAgB;AAAA,IACxC,SAAS,CAAC,cAAc,gBAAgB;AAAA,EAC1C,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,gBAAgB;AAAA,MACxC,SAAS,CAAC,cAAc,gBAAgB;AAAA,IAC1C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AD/QH;AAEA;AAaA,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,aAAa;AAC1B,IAAM,MAAM,CAAC,CAAC,CAAC,4BAA4B,CAAC,GAAG,KAAK,CAAC,CAAC,gBAAgB,CAAC,CAAC;AACxE,IAAM,MAAM,CAAC,8BAA8B,KAAK,gBAAgB;AAChE,SAAS,yCAAyC,IAAI,KAAK;AAAC;AAC5D,IAAM,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,uBAAuB,CAAC,GAAG,GAAG;AACpE,IAAM,MAAM,CAAC,mBAAmB,yBAAyB,GAAG;AAC5D,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,kBAAkB,GAAG,QAAQ,CAAC;AACjC,IAAG,eAAe;AAClB,IAAG,kBAAkB,GAAG,OAAO,CAAC;AAChC,IAAG,aAAa,GAAG,QAAQ,CAAC;AAC5B,IAAG,gBAAgB,EAAE;AAAA,EACvB;AACF;AACA,IAAM,gBAAgB,IAAI,eAAe,eAAe;AAMxD,IAAM,sBAAsB,IAAI,eAAe,qBAAqB;AAMpE,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,OAAO,WAAW;AAAA,EAC9B,kBAAkB,OAAO,qBAAqB;AAAA,IAC5C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA0B;AAAA,EAC7D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,4BAA4B,EAAE,CAAC;AAAA,EAC7D,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,IAAM,sCAAsC,IAAI,eAAe,qCAAqC;AAKpG,IAAM,oBAAN,MAAM,2BAA0B,iBAAiB;AAAA,EAC/C,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,sBAAsB,oBAAoB;AAAA,EAC1C,YAAY,OAAO,QAAQ;AAAA,EAC3B,UAAU,OAAO,MAAM;AAAA,EACvB,cAAc,OAAO,UAAU;AAAA,EAC/B,YAAY,OAAO,SAAS;AAAA,EAC5B;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,eAAe,KAAK,aAAa,KAAK,UAAU;AAAA,EAC9D;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,cAAc;AAAA;AAAA,EAEd,IAAI,iBAAiB;AACnB,WAAO,KAAK,mBAAmB,KAAK,aAAa,KAAK,UAAU;AAAA,EAClE;AAAA,EACA,IAAI,eAAe,OAAO;AACxB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA;AAAA;AAAA,EAEA,cAAc,IAAI,aAAa;AAAA;AAAA,EAE/B,gBAAgB,IAAI,aAAa;AAAA;AAAA,EAEjC,gBAAgB,IAAI,QAAQ;AAAA;AAAA,EAE5B,YAAY,OAAO,eAAe;AAAA,IAChC,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,YAAY,OAAO,YAAY,EAAE,MAAM,6BAA6B;AAAA,EACpE,cAAc;AACZ,UAAM;AACN,UAAM,iBAAiB,OAAO,qCAAqC;AAAA,MACjE,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,uBAAuB,OAAO,yBAAyB;AAC5D,QAAI,gBAAgB;AAClB,WAAK,aAAa,eAAe;AAAA,IACnC;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,QAAI,KAAK,WAAW;AAClB,aAAO,KAAK,YAAY,KAAK,UAAU,gBAAgB;AAAA,IACzD;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,KAAK,WAAW,aAAa;AAAA,EACtC;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,WAAW,CAAC,KAAK;AAAA,EACxB;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,OAAO;AACL,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,gBAAgB,KAAK,aAAa,oBAAoB,MAAM;AAEnE,WAAK,OAAO,KAAK,UAAU,IAAI,GAAG,OAAO,MAAM,KAAK,YAAY,CAAC,KAAK,OAAO,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AACvG,aAAK,UAAU,IAAI,eAAe,KAAK,aAAa,WAAW,KAAK,iBAAiB;AAAA,MACvF,CAAC;AAAA,IACH;AACA,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,cAAc,KAAK,OAAO;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAClB,SAAK,wBAAwB;AAC7B,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA;AAAA,EAEA,iBAAiB;AACf,QAAI,KAAK,OAAO;AACd,YAAM,iBAAiB,KAAK,UAAU;AACtC,YAAM,cAAc,KAAK,MAAM;AAC/B,aAAO,mBAAmB,eAAe,YAAY,SAAS,cAAc;AAAA,IAC9E;AACA,WAAO;AAAA,EACT;AAAA,EACA,yBAAyB,CAAC;AAAA,IACxB;AAAA,IACA;AAAA,EACF,MAAM;AACJ,QAAI,WAAW,KAAK,cAAc,iBAAiB,iBAAiB,sBAAsB;AACxF,WAAK,QAAQ,IAAI,MAAM;AACrB,YAAI,KAAK,UAAU;AACjB,eAAK,YAAY,KAAK;AAAA,QACxB,OAAO;AACL,eAAK,cAAc,KAAK;AAAA,QAC1B;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,wBAAwB;AAGtB,SAAK,QAAQ,kBAAkB,MAAM;AACnC,UAAI,KAAK,qBAAqB;AAC5B,aAAK,OAAO,UAAU,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,YAAY,KAAK,CAAC,CAAC;AAC3E,aAAK,OAAO,UAAU,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,cAAc,KAAK,CAAC,CAAC;AAAA,MAC/E,OAAO;AACL,mBAAW,MAAM;AACf,gBAAM,UAAU,KAAK,YAAY;AACjC,eAAK,wBAAwB,KAAK,UAAU,OAAO,SAAS,iBAAiB,KAAK,sBAAsB;AACxG,kBAAQ,UAAU,IAAI,wCAAwC;AAAA,QAChE,GAAG,GAAG;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,IACnC,gBAAgB,SAAS,iCAAiC,IAAI,KAAK,UAAU;AAC3E,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,0BAA0B,CAAC;AAAA,MACzD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AAAA,MACrE;AAAA,IACF;AAAA,IACA,WAAW,SAAS,wBAAwB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAC5D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AAAA,MACrE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,qBAAqB;AAAA,IACpC,UAAU;AAAA,IACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,gBAAgB,IAAI,QAAQ,EAAE,+BAA+B,IAAI,YAAY,CAAC;AAAA,MAC/F;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,gBAAgB;AAAA,IAClB;AAAA,IACA,SAAS;AAAA,MACP,aAAa;AAAA,MACb,eAAe;AAAA,IACjB;AAAA,IACA,UAAU,CAAC,mBAAmB;AAAA,IAC9B,UAAU,CAAI,mBAAmB;AAAA;AAAA;AAAA,MAGjC;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,IAAC,CAAC,GAAM,4BAA+B,oBAAoB;AAAA,IAC3D,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,eAAe,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,qCAAqC,GAAG,CAAC,QAAQ,UAAU,GAAG,+BAA+B,GAAG,IAAI,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,GAAG,iBAAiB,CAAC;AAAA,IAC9M,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,aAAa,CAAC;AACjB,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AAC7D,QAAG,aAAa,GAAG,CAAC;AACpB,QAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,eAAe,CAAC;AACjF,QAAG,aAAa;AAChB,QAAG,aAAa,GAAG,CAAC;AACpB,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU;AACb,QAAG,YAAY,SAAS,IAAI,WAAW,OAAO,EAAE;AAChD,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,MAAM,IAAI,EAAE;AAC1B,QAAG,YAAY,mBAAmB,IAAI,SAAS;AAC/C,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,mBAAmB,IAAI,OAAO;AAAA,MAC9C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,eAAe;AAAA,IAC9B,QAAQ,CAAC,80GAA80G;AAAA,IACv1G,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW;AAAA;AAAA;AAAA,QAGX;AAAA,UACE,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,QAAG;AAAA,UACD,SAAS;AAAA,UACT,aAAa;AAAA,QACf;AAAA,MAAC;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,wBAAwB;AAAA,QACxB,uCAAuC;AAAA,MACzC;AAAA,MACA,SAAS,CAAC,eAAe;AAAA,MACzB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,80GAA80G;AAAA,IACz1G,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAIH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,OAAO,OAAO,SAAS,mCAAmC,mBAAmB;AAC3E,WAAO,KAAK,qBAAqB,6BAA4B;AAAA,EAC/D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,IAC9B,WAAW,CAAC,GAAG,gBAAgB;AAAA,EACjC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,QAAQ,OAAO,mBAAmB;AAAA,IAChC,MAAM;AAAA,EACR,CAAC;AAAA,EACD,WAAW,OAAO,UAAU;AAAA,EAC5B,gBAAgB,OAAO,YAAY;AAAA,EACnC,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,4BAA4B,aAAa;AAAA,EACzC,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,uBAAuB;AAC3D,UAAM,QAAQ,KAAK;AACnB,UAAM,iBAAiB,OAAO,qCAAqC;AAAA,MACjE,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,WAAW,OAAO,IAAI,mBAAmB,UAAU,GAAG;AAAA,MAC1D,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,4BAA4B,MAAM,YAAY,MAAM,UAAU,cAAc,KAAK,OAAO,aAAW,CAAC,EAAE,QAAQ,YAAY,KAAK,QAAQ,gBAAgB,EAAE,CAAC,IAAI;AACpK,SAAK,WAAW,SAAS,YAAY,EAAE,KAAK;AAG5C,SAAK,4BAA4B,MAAM,MAAM,QAAQ,MAAM,QAAQ,2BAA2B,MAAM,cAAc,KAAK,OAAO,aAAW;AACvI,aAAO,CAAC,EAAE,QAAQ,YAAY,KAAK,QAAQ,UAAU,KAAK,QAAQ,gBAAgB;AAAA,IACpF,CAAC,CAAC,CAAC,EAAE,UAAU,MAAM,KAAK,mBAAmB,aAAa,CAAC;AAE3D,UAAM,OAAO,KAAK,OAAO,MAAM,MAAM,eAAe,CAAC,CAAC,EAAE,UAAU,MAAM,KAAK,cAAc,SAAS,KAAK,UAAU,SAAS,CAAC;AAC7H,QAAI,gBAAgB;AAClB,WAAK,iBAAiB,eAAe;AACrC,WAAK,kBAAkB,eAAe;AAAA,IACxC;AAAA,EACF;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,IAAI,WAAW;AACb,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,MAAM,OAAO;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,KAAK,MAAM,kBAAkB;AAAA,EACtC;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA,EAEA,qBAAqB;AACnB,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,CAAC,KAAK,MAAM,cAAc,CAAC,KAAK,MAAM;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,UAAM,aAAa,KAAK,YAAY;AACpC,QAAI,cAAc,KAAK,gBAAgB;AACrC,aAAO,KAAK;AAAA,IACd,WAAW,CAAC,cAAc,KAAK,iBAAiB;AAC9C,aAAO,KAAK;AAAA,IACd;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,SAAS,OAAO;AACd,YAAQ,MAAM,SAAS;AAAA;AAAA,MAErB,KAAK;AAAA,MACL,KAAK;AACH,YAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,gBAAM,eAAe;AACrB,eAAK,QAAQ;AAAA,QACf;AACA;AAAA,MACF;AACE,YAAI,KAAK,MAAM,WAAW;AACxB,eAAK,MAAM,UAAU,qBAAqB,KAAK;AAAA,QACjD;AACA;AAAA,IACJ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,QAAQ,SAAS;AACrB,QAAI,QAAQ;AACV,WAAK,cAAc,SAAS,KAAK,UAAU,QAAQ,OAAO;AAAA,IAC5D,OAAO;AACL,WAAK,SAAS,cAAc,MAAM,OAAO;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,cAAc,QAAQ,KAAK,QAAQ,EAAE,UAAU,YAAU;AAC5D,UAAI,UAAU,KAAK,MAAM,WAAW;AAClC,aAAK,MAAM,UAAU,mBAAmB,IAAI;AAAA,MAC9C;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,0BAA0B,YAAY;AAC3C,SAAK,cAAc,eAAe,KAAK,QAAQ;AAAA,EACjD;AAAA,EACA,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,4BAA4B,CAAC;AAAA,IAC1C,WAAW,CAAC,QAAQ,UAAU,GAAG,8BAA8B,qBAAqB;AAAA,IACpF,UAAU;AAAA,IACV,cAAc,SAAS,qCAAqC,IAAI,KAAK;AACnE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,mDAAmD;AACjF,iBAAO,IAAI,QAAQ;AAAA,QACrB,CAAC,EAAE,WAAW,SAAS,mDAAmD,QAAQ;AAChF,iBAAO,IAAI,SAAS,MAAM;AAAA,QAC5B,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,MAAM,IAAI,MAAM,SAAS,EAAE,YAAY,IAAI,WAAW,KAAK,IAAI,QAAQ,EAAE,iBAAiB,IAAI,YAAY,CAAC,EAAE,iBAAiB,IAAI,YAAY,CAAC,EAAE,iBAAiB,IAAI,MAAM,QAAQ;AACnM,QAAG,YAAY,UAAU,IAAI,iBAAiB,CAAC;AAC/C,QAAG,YAAY,gBAAgB,IAAI,YAAY,CAAC,EAAE,wCAAwC,IAAI,mBAAmB,MAAM,OAAO,EAAE,yCAAyC,IAAI,mBAAmB,MAAM,QAAQ;AAAA,MAChN;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,UAAU,CAAC,GAAG,YAAY,YAAY,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK,CAAC;AAAA,IAC3F;AAAA,IACA,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,SAAS,8BAA8B,WAAW,kBAAkB,eAAe,QAAQ,aAAa,OAAO,GAAG,CAAC,KAAK,uDAAuD,CAAC;AAAA,IAC9O,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,kBAAkB,GAAG,QAAQ,CAAC;AACjC,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa,GAAG,CAAC;AACpB,QAAG,aAAa,GAAG,CAAC;AACpB,QAAG,gBAAgB;AACnB,QAAG,oBAAoB,GAAG,gDAAgD,GAAG,GAAG,QAAQ,CAAC;AAAA,MAC3F;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,2BAA2B,CAAC,IAAI,YAAY,CAAC;AAC5D,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,YAAY,IAAI,IAAI,EAAE;AAAA,MAC7C;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,s/IAAw/I;AAAA,IACjgJ,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,gDAAgD;AAAA,QAChD,iDAAiD;AAAA,QACjD,kBAAkB;AAAA,QAClB,WAAW;AAAA,QACX,aAAa;AAAA,MACf;AAAA,MACA,UAAU;AAAA,MACV,QAAQ,CAAC,s/IAAw/I;AAAA,IACngJ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK;AAAA,MAC/D,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAIH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,OAAO,OAAO,SAAS,qCAAqC,mBAAmB;AAC7E,WAAO,KAAK,qBAAqB,+BAA8B;AAAA,EACjE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,IACrC,WAAW,CAAC,GAAG,wCAAwC;AAAA,EACzD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAIH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,IAC/B,WAAW,CAAC,GAAG,kCAAkC;AAAA,EACnD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,eAAN,MAAM,sBAAqB,aAAa;AAAA,EACtC;AAAA;AAAA,EAEA,cAAc,IAAI,UAAU;AAAA;AAAA,EAE5B;AAAA;AAAA,EAEA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASb,cAAc;AAAA;AAAA,EAEd,iBAAiB;AAAA,EACjB,qBAAqB;AACnB,SAAK,SAAS,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,aAAW;AACxE,WAAK,YAAY,MAAM,QAAQ,OAAO,YAAU,OAAO,MAAM,cAAc,IAAI,CAAC;AAChF,WAAK,YAAY,gBAAgB;AAAA,IACnC,CAAC;AACD,SAAK,cAAc,IAAI,gBAAgB,KAAK,WAAW,EAAE,SAAS,EAAE,eAAe;AAAA,EACrF;AAAA;AAAA,EAEA,qBAAqB,OAAO;AAC1B,SAAK,YAAY,UAAU,KAAK;AAAA,EAClC;AAAA,EACA,mBAAmB,QAAQ;AACzB,SAAK,YAAY,iBAAiB,MAAM;AAAA,EAC1C;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAClB,SAAK,aAAa,QAAQ;AAC1B,SAAK,YAAY,QAAQ;AAAA,EAC3B;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,qBAAqB,mBAAmB;AACtD,cAAQ,8BAA8B,4BAA+B,sBAAsB,aAAY,IAAI,qBAAqB,aAAY;AAAA,IAC9I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,IAC7B,gBAAgB,SAAS,4BAA4B,IAAI,KAAK,UAAU;AACtE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,yBAAyB,CAAC;AAAA,MACxD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW;AAAA,MAC9D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,eAAe;AAAA,IAC9B,UAAU;AAAA,IACV,cAAc,SAAS,0BAA0B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,uBAAuB,IAAI,KAAK;AAAA,MACjD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,aAAa;AAAA,MACb,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU,CAAC,cAAc;AAAA,IACzB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,OAAO;AAAA;AAAA;AAAA,QAGP,+BAA+B;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,QAC9B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,oBAAoB,cAAc,cAAc,mBAAmB,4BAA4B,yBAAyB,wBAAwB,8BAA8B,wBAAwB;AAAA,IACjO,SAAS,CAAC,cAAc,mBAAmB,4BAA4B,yBAAyB,wBAAwB,8BAA8B,wBAAwB;AAAA,EAChL,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,oBAAoB,YAAY;AAAA,EAC7D,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,oBAAoB,cAAc,cAAc,mBAAmB,4BAA4B,yBAAyB,wBAAwB,8BAA8B,wBAAwB;AAAA,MACjO,SAAS,CAAC,cAAc,mBAAmB,4BAA4B,yBAAyB,wBAAwB,8BAA8B,wBAAwB;AAAA,IAChL,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,mCAAmC;AAyBzC,IAAM,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAW7B,iBAAiB;AAAA,IACf,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,WAAW;AAAA,QACb;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,WAAW;AAAA,QACb;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,eAAe;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,UAAU;AAAA,UACV,cAAc;AAAA,QAChB;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,UAAU;AAAA,UACV,cAAc;AAAA,QAChB;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,EACZ;AACF;", "names": []}
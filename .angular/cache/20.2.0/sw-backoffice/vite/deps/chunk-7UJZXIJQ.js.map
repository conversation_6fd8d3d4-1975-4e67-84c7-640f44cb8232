{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/scrolling2.mjs"], "sourcesContent": ["/** The possible ways the browser may handle the horizontal scroll axis in RTL languages. */\nvar RtlScrollAxisType;\n(function (RtlScrollAxisType) {\n    /**\n     * scrollLeft is 0 when scrolled all the way left and (scrollWidth - clientWidth) when scrolled\n     * all the way right.\n     */\n    RtlScrollAxisType[RtlScrollAxisType[\"NORMAL\"] = 0] = \"NORMAL\";\n    /**\n     * scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and 0 when scrolled\n     * all the way right.\n     */\n    RtlScrollAxisType[RtlScrollAxisType[\"NEGATED\"] = 1] = \"NEGATED\";\n    /**\n     * scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and 0 when scrolled\n     * all the way right.\n     */\n    RtlScrollAxisType[RtlScrollAxisType[\"INVERTED\"] = 2] = \"INVERTED\";\n})(RtlScrollAxisType || (RtlScrollAxisType = {}));\n/** Cached result of the way the browser handles the horizontal scroll axis in RTL mode. */\nlet rtlScrollAxisType;\n/** Cached result of the check that indicates whether the browser supports scroll behaviors. */\nlet scrollBehaviorSupported;\n/** Check whether the browser supports scroll behaviors. */\nfunction supportsScrollBehavior() {\n    if (scrollBehaviorSupported == null) {\n        // If we're not in the browser, it can't be supported. Also check for `Element`, because\n        // some projects stub out the global `document` during SSR which can throw us off.\n        if (typeof document !== 'object' || !document || typeof Element !== 'function' || !Element) {\n            scrollBehaviorSupported = false;\n            return scrollBehaviorSupported;\n        }\n        // If the element can have a `scrollBehavior` style, we can be sure that it's supported.\n        if ('scrollBehavior' in document.documentElement.style) {\n            scrollBehaviorSupported = true;\n        }\n        else {\n            // At this point we have 3 possibilities: `scrollTo` isn't supported at all, it's\n            // supported but it doesn't handle scroll behavior, or it has been polyfilled.\n            const scrollToFunction = Element.prototype.scrollTo;\n            if (scrollToFunction) {\n                // We can detect if the function has been polyfilled by calling `toString` on it. Native\n                // functions are obfuscated using `[native code]`, whereas if it was overwritten we'd get\n                // the actual function source. Via https://davidwalsh.name/detect-native-function. Consider\n                // polyfilled functions as supporting scroll behavior.\n                scrollBehaviorSupported = !/\\{\\s*\\[native code\\]\\s*\\}/.test(scrollToFunction.toString());\n            }\n            else {\n                scrollBehaviorSupported = false;\n            }\n        }\n    }\n    return scrollBehaviorSupported;\n}\n/**\n * Checks the type of RTL scroll axis used by this browser. As of time of writing, Chrome is NORMAL,\n * Firefox & Safari are NEGATED, and IE & Edge are INVERTED.\n */\nfunction getRtlScrollAxisType() {\n    // We can't check unless we're on the browser. Just assume 'normal' if we're not.\n    if (typeof document !== 'object' || !document) {\n        return RtlScrollAxisType.NORMAL;\n    }\n    if (rtlScrollAxisType == null) {\n        // Create a 1px wide scrolling container and a 2px wide content element.\n        const scrollContainer = document.createElement('div');\n        const containerStyle = scrollContainer.style;\n        scrollContainer.dir = 'rtl';\n        containerStyle.width = '1px';\n        containerStyle.overflow = 'auto';\n        containerStyle.visibility = 'hidden';\n        containerStyle.pointerEvents = 'none';\n        containerStyle.position = 'absolute';\n        const content = document.createElement('div');\n        const contentStyle = content.style;\n        contentStyle.width = '2px';\n        contentStyle.height = '1px';\n        scrollContainer.appendChild(content);\n        document.body.appendChild(scrollContainer);\n        rtlScrollAxisType = RtlScrollAxisType.NORMAL;\n        // The viewport starts scrolled all the way to the right in RTL mode. If we are in a NORMAL\n        // browser this would mean that the scrollLeft should be 1. If it's zero instead we know we're\n        // dealing with one of the other two types of browsers.\n        if (scrollContainer.scrollLeft === 0) {\n            // In a NEGATED browser the scrollLeft is always somewhere in [-maxScrollAmount, 0]. For an\n            // INVERTED browser it is always somewhere in [0, maxScrollAmount]. We can determine which by\n            // setting to the scrollLeft to 1. This is past the max for a NEGATED browser, so it will\n            // return 0 when we read it again.\n            scrollContainer.scrollLeft = 1;\n            rtlScrollAxisType =\n                scrollContainer.scrollLeft === 0 ? RtlScrollAxisType.NEGATED : RtlScrollAxisType.INVERTED;\n        }\n        scrollContainer.remove();\n    }\n    return rtlScrollAxisType;\n}\n\nexport { RtlScrollAxisType, getRtlScrollAxisType, supportsScrollBehavior };\n\n"], "mappings": ";AACA,IAAI;AAAA,CACH,SAAUA,oBAAmB;AAK1B,EAAAA,mBAAkBA,mBAAkB,QAAQ,IAAI,CAAC,IAAI;AAKrD,EAAAA,mBAAkBA,mBAAkB,SAAS,IAAI,CAAC,IAAI;AAKtD,EAAAA,mBAAkBA,mBAAkB,UAAU,IAAI,CAAC,IAAI;AAC3D,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAEhD,IAAI;AAEJ,IAAI;AAEJ,SAAS,yBAAyB;AAC9B,MAAI,2BAA2B,MAAM;AAGjC,QAAI,OAAO,aAAa,YAAY,CAAC,YAAY,OAAO,YAAY,cAAc,CAAC,SAAS;AACxF,gCAA0B;AAC1B,aAAO;AAAA,IACX;AAEA,QAAI,oBAAoB,SAAS,gBAAgB,OAAO;AACpD,gCAA0B;AAAA,IAC9B,OACK;AAGD,YAAM,mBAAmB,QAAQ,UAAU;AAC3C,UAAI,kBAAkB;AAKlB,kCAA0B,CAAC,4BAA4B,KAAK,iBAAiB,SAAS,CAAC;AAAA,MAC3F,OACK;AACD,kCAA0B;AAAA,MAC9B;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAKA,SAAS,uBAAuB;AAE5B,MAAI,OAAO,aAAa,YAAY,CAAC,UAAU;AAC3C,WAAO,kBAAkB;AAAA,EAC7B;AACA,MAAI,qBAAqB,MAAM;AAE3B,UAAM,kBAAkB,SAAS,cAAc,KAAK;AACpD,UAAM,iBAAiB,gBAAgB;AACvC,oBAAgB,MAAM;AACtB,mBAAe,QAAQ;AACvB,mBAAe,WAAW;AAC1B,mBAAe,aAAa;AAC5B,mBAAe,gBAAgB;AAC/B,mBAAe,WAAW;AAC1B,UAAM,UAAU,SAAS,cAAc,KAAK;AAC5C,UAAM,eAAe,QAAQ;AAC7B,iBAAa,QAAQ;AACrB,iBAAa,SAAS;AACtB,oBAAgB,YAAY,OAAO;AACnC,aAAS,KAAK,YAAY,eAAe;AACzC,wBAAoB,kBAAkB;AAItC,QAAI,gBAAgB,eAAe,GAAG;AAKlC,sBAAgB,aAAa;AAC7B,0BACI,gBAAgB,eAAe,IAAI,kBAAkB,UAAU,kBAAkB;AAAA,IACzF;AACA,oBAAgB,OAAO;AAAA,EAC3B;AACA,SAAO;AACX;", "names": ["RtlScrollAxisType"]}
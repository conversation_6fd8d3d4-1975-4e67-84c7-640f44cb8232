import {
  init_common
} from "./chunk-TEPKXNLU.js";
import {
  init_core
} from "./chunk-WUNHS5KN.js";
import {
  init_esm5
} from "./chunk-N726T63C.js";
import {
  init_operators
} from "./chunk-H5IUTQT7.js";

// node_modules/@angular/material/fesm2022/form-field.mjs
init_common();
init_core();
init_esm5();
init_operators();
var matFormFieldAnimations = {
  // Represents:
  // trigger('transitionMessages', [
  //   // TODO(mmalerba): Use angular animations for label animation as well.
  //   state('enter', style({opacity: 1, transform: 'translateY(0%)'})),
  //   transition('void => enter', [
  //     style({opacity: 0, transform: 'translateY(-5px)'}),
  //     animate('300ms cubic-bezier(0.55, 0, 0.55, 0.2)'),
  //   ]),
  // ])
  /** Animation that transitions the form field's error and hint messages. */
  transitionMessages: {
    type: 7,
    name: "transitionMessages",
    definitions: [
      {
        type: 0,
        name: "enter",
        styles: {
          type: 6,
          styles: { opacity: 1, transform: "translateY(0%)" },
          offset: null
        }
      },
      {
        type: 1,
        expr: "void => enter",
        animation: [
          { type: 6, styles: { opacity: 0, transform: "translateY(-5px)" }, offset: null },
          { type: 4, styles: null, timings: "300ms cubic-bezier(0.55, 0, 0.55, 0.2)" }
        ],
        options: null
      }
    ],
    options: {}
  }
};

export {
  matFormFieldAnimations
};
//# sourceMappingURL=chunk-BTCG6SHQ.js.map

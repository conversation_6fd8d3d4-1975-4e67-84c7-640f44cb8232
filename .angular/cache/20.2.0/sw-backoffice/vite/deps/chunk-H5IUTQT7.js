import {
  ArgumentOutOfRangeError,
  AsyncSubject,
  BehaviorSubject,
  CombineLatestOperator,
  EmptyError,
  Notification,
  Observable,
  OuterSubscriber,
  ReplaySubject,
  SimpleInnerSubscriber,
  SimpleOuterSubscriber,
  Subject,
  Subscriber,
  Subscription,
  TimeoutError,
  ZipOperator,
  __extends,
  asap,
  async,
  concat,
  connectableObservableDescriptor,
  defer,
  empty,
  filter,
  from,
  identity,
  init_ArgumentOutOfRangeError,
  init_AsyncSubject,
  init_BehaviorSubject,
  init_ConnectableObservable,
  init_EmptyError,
  init_Notification,
  init_Observable,
  init_OuterSubscriber,
  init_ReplaySubject,
  init_Subject,
  init_Subscriber,
  init_Subscription,
  init_TimeoutError,
  init_asap,
  init_async,
  init_combineLatest,
  init_concat,
  init_concatAll,
  init_defer,
  init_empty,
  init_filter,
  init_from,
  init_groupBy,
  init_identity,
  init_innerSubscribe,
  init_isArray,
  init_isFunction,
  init_isNumeric,
  init_isScheduler,
  init_map,
  init_merge,
  init_mergeAll,
  init_mergeMap,
  init_noop,
  init_not,
  init_observeOn,
  init_of,
  init_pipe,
  init_race,
  init_refCount,
  init_subscribeToResult,
  init_throwError,
  init_timer,
  init_tslib_es6,
  init_zip,
  innerSubscribe,
  isArray,
  isFunction,
  isNumeric,
  isScheduler,
  map,
  merge,
  mergeMap,
  noop,
  not,
  of,
  pipe,
  race,
  refCount,
  subscribeToResult,
  throwError,
  timer,
  zip
} from "./chunk-CTL5GG7J.js";
import {
  __esm
} from "./chunk-WOR4A3D2.js";

// node_modules/rxjs/_esm5/internal/operators/audit.js
function audit(durationSelector) {
  return function auditOperatorFunction(source) {
    return source.lift(new AuditOperator(durationSelector));
  };
}
var AuditOperator, AuditSubscriber;
var init_audit = __esm({
  "node_modules/rxjs/_esm5/internal/operators/audit.js"() {
    init_tslib_es6();
    init_innerSubscribe();
    AuditOperator = (function() {
      function AuditOperator2(durationSelector) {
        this.durationSelector = durationSelector;
      }
      AuditOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new AuditSubscriber(subscriber, this.durationSelector));
      };
      return AuditOperator2;
    })();
    AuditSubscriber = (function(_super) {
      __extends(AuditSubscriber2, _super);
      function AuditSubscriber2(destination, durationSelector) {
        var _this = _super.call(this, destination) || this;
        _this.durationSelector = durationSelector;
        _this.hasValue = false;
        return _this;
      }
      AuditSubscriber2.prototype._next = function(value) {
        this.value = value;
        this.hasValue = true;
        if (!this.throttled) {
          var duration = void 0;
          try {
            var durationSelector = this.durationSelector;
            duration = durationSelector(value);
          } catch (err) {
            return this.destination.error(err);
          }
          var innerSubscription = innerSubscribe(duration, new SimpleInnerSubscriber(this));
          if (!innerSubscription || innerSubscription.closed) {
            this.clearThrottle();
          } else {
            this.add(this.throttled = innerSubscription);
          }
        }
      };
      AuditSubscriber2.prototype.clearThrottle = function() {
        var _a = this, value = _a.value, hasValue = _a.hasValue, throttled = _a.throttled;
        if (throttled) {
          this.remove(throttled);
          this.throttled = void 0;
          throttled.unsubscribe();
        }
        if (hasValue) {
          this.value = void 0;
          this.hasValue = false;
          this.destination.next(value);
        }
      };
      AuditSubscriber2.prototype.notifyNext = function() {
        this.clearThrottle();
      };
      AuditSubscriber2.prototype.notifyComplete = function() {
        this.clearThrottle();
      };
      return AuditSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/auditTime.js
function auditTime(duration, scheduler) {
  if (scheduler === void 0) {
    scheduler = async;
  }
  return audit(function() {
    return timer(duration, scheduler);
  });
}
var init_auditTime = __esm({
  "node_modules/rxjs/_esm5/internal/operators/auditTime.js"() {
    init_async();
    init_audit();
    init_timer();
  }
});

// node_modules/rxjs/_esm5/internal/operators/buffer.js
function buffer(closingNotifier) {
  return function bufferOperatorFunction(source) {
    return source.lift(new BufferOperator(closingNotifier));
  };
}
var BufferOperator, BufferSubscriber;
var init_buffer = __esm({
  "node_modules/rxjs/_esm5/internal/operators/buffer.js"() {
    init_tslib_es6();
    init_innerSubscribe();
    BufferOperator = (function() {
      function BufferOperator2(closingNotifier) {
        this.closingNotifier = closingNotifier;
      }
      BufferOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new BufferSubscriber(subscriber, this.closingNotifier));
      };
      return BufferOperator2;
    })();
    BufferSubscriber = (function(_super) {
      __extends(BufferSubscriber2, _super);
      function BufferSubscriber2(destination, closingNotifier) {
        var _this = _super.call(this, destination) || this;
        _this.buffer = [];
        _this.add(innerSubscribe(closingNotifier, new SimpleInnerSubscriber(_this)));
        return _this;
      }
      BufferSubscriber2.prototype._next = function(value) {
        this.buffer.push(value);
      };
      BufferSubscriber2.prototype.notifyNext = function() {
        var buffer2 = this.buffer;
        this.buffer = [];
        this.destination.next(buffer2);
      };
      return BufferSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/bufferCount.js
function bufferCount(bufferSize, startBufferEvery) {
  if (startBufferEvery === void 0) {
    startBufferEvery = null;
  }
  return function bufferCountOperatorFunction(source) {
    return source.lift(new BufferCountOperator(bufferSize, startBufferEvery));
  };
}
var BufferCountOperator, BufferCountSubscriber, BufferSkipCountSubscriber;
var init_bufferCount = __esm({
  "node_modules/rxjs/_esm5/internal/operators/bufferCount.js"() {
    init_tslib_es6();
    init_Subscriber();
    BufferCountOperator = (function() {
      function BufferCountOperator2(bufferSize, startBufferEvery) {
        this.bufferSize = bufferSize;
        this.startBufferEvery = startBufferEvery;
        if (!startBufferEvery || bufferSize === startBufferEvery) {
          this.subscriberClass = BufferCountSubscriber;
        } else {
          this.subscriberClass = BufferSkipCountSubscriber;
        }
      }
      BufferCountOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new this.subscriberClass(subscriber, this.bufferSize, this.startBufferEvery));
      };
      return BufferCountOperator2;
    })();
    BufferCountSubscriber = (function(_super) {
      __extends(BufferCountSubscriber2, _super);
      function BufferCountSubscriber2(destination, bufferSize) {
        var _this = _super.call(this, destination) || this;
        _this.bufferSize = bufferSize;
        _this.buffer = [];
        return _this;
      }
      BufferCountSubscriber2.prototype._next = function(value) {
        var buffer2 = this.buffer;
        buffer2.push(value);
        if (buffer2.length == this.bufferSize) {
          this.destination.next(buffer2);
          this.buffer = [];
        }
      };
      BufferCountSubscriber2.prototype._complete = function() {
        var buffer2 = this.buffer;
        if (buffer2.length > 0) {
          this.destination.next(buffer2);
        }
        _super.prototype._complete.call(this);
      };
      return BufferCountSubscriber2;
    })(Subscriber);
    BufferSkipCountSubscriber = (function(_super) {
      __extends(BufferSkipCountSubscriber2, _super);
      function BufferSkipCountSubscriber2(destination, bufferSize, startBufferEvery) {
        var _this = _super.call(this, destination) || this;
        _this.bufferSize = bufferSize;
        _this.startBufferEvery = startBufferEvery;
        _this.buffers = [];
        _this.count = 0;
        return _this;
      }
      BufferSkipCountSubscriber2.prototype._next = function(value) {
        var _a = this, bufferSize = _a.bufferSize, startBufferEvery = _a.startBufferEvery, buffers = _a.buffers, count2 = _a.count;
        this.count++;
        if (count2 % startBufferEvery === 0) {
          buffers.push([]);
        }
        for (var i = buffers.length; i--; ) {
          var buffer2 = buffers[i];
          buffer2.push(value);
          if (buffer2.length === bufferSize) {
            buffers.splice(i, 1);
            this.destination.next(buffer2);
          }
        }
      };
      BufferSkipCountSubscriber2.prototype._complete = function() {
        var _a = this, buffers = _a.buffers, destination = _a.destination;
        while (buffers.length > 0) {
          var buffer2 = buffers.shift();
          if (buffer2.length > 0) {
            destination.next(buffer2);
          }
        }
        _super.prototype._complete.call(this);
      };
      return BufferSkipCountSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/bufferTime.js
function bufferTime(bufferTimeSpan) {
  var length = arguments.length;
  var scheduler = async;
  if (isScheduler(arguments[arguments.length - 1])) {
    scheduler = arguments[arguments.length - 1];
    length--;
  }
  var bufferCreationInterval = null;
  if (length >= 2) {
    bufferCreationInterval = arguments[1];
  }
  var maxBufferSize = Number.POSITIVE_INFINITY;
  if (length >= 3) {
    maxBufferSize = arguments[2];
  }
  return function bufferTimeOperatorFunction(source) {
    return source.lift(new BufferTimeOperator(bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler));
  };
}
function dispatchBufferTimeSpanOnly(state) {
  var subscriber = state.subscriber;
  var prevContext = state.context;
  if (prevContext) {
    subscriber.closeContext(prevContext);
  }
  if (!subscriber.closed) {
    state.context = subscriber.openContext();
    state.context.closeAction = this.schedule(state, state.bufferTimeSpan);
  }
}
function dispatchBufferCreation(state) {
  var bufferCreationInterval = state.bufferCreationInterval, bufferTimeSpan = state.bufferTimeSpan, subscriber = state.subscriber, scheduler = state.scheduler;
  var context = subscriber.openContext();
  var action = this;
  if (!subscriber.closed) {
    subscriber.add(context.closeAction = scheduler.schedule(dispatchBufferClose, bufferTimeSpan, { subscriber, context }));
    action.schedule(state, bufferCreationInterval);
  }
}
function dispatchBufferClose(arg) {
  var subscriber = arg.subscriber, context = arg.context;
  subscriber.closeContext(context);
}
var BufferTimeOperator, Context, BufferTimeSubscriber;
var init_bufferTime = __esm({
  "node_modules/rxjs/_esm5/internal/operators/bufferTime.js"() {
    init_tslib_es6();
    init_async();
    init_Subscriber();
    init_isScheduler();
    BufferTimeOperator = (function() {
      function BufferTimeOperator2(bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler) {
        this.bufferTimeSpan = bufferTimeSpan;
        this.bufferCreationInterval = bufferCreationInterval;
        this.maxBufferSize = maxBufferSize;
        this.scheduler = scheduler;
      }
      BufferTimeOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new BufferTimeSubscriber(subscriber, this.bufferTimeSpan, this.bufferCreationInterval, this.maxBufferSize, this.scheduler));
      };
      return BufferTimeOperator2;
    })();
    Context = /* @__PURE__ */ (function() {
      function Context2() {
        this.buffer = [];
      }
      return Context2;
    })();
    BufferTimeSubscriber = (function(_super) {
      __extends(BufferTimeSubscriber2, _super);
      function BufferTimeSubscriber2(destination, bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler) {
        var _this = _super.call(this, destination) || this;
        _this.bufferTimeSpan = bufferTimeSpan;
        _this.bufferCreationInterval = bufferCreationInterval;
        _this.maxBufferSize = maxBufferSize;
        _this.scheduler = scheduler;
        _this.contexts = [];
        var context = _this.openContext();
        _this.timespanOnly = bufferCreationInterval == null || bufferCreationInterval < 0;
        if (_this.timespanOnly) {
          var timeSpanOnlyState = { subscriber: _this, context, bufferTimeSpan };
          _this.add(context.closeAction = scheduler.schedule(dispatchBufferTimeSpanOnly, bufferTimeSpan, timeSpanOnlyState));
        } else {
          var closeState = { subscriber: _this, context };
          var creationState = { bufferTimeSpan, bufferCreationInterval, subscriber: _this, scheduler };
          _this.add(context.closeAction = scheduler.schedule(dispatchBufferClose, bufferTimeSpan, closeState));
          _this.add(scheduler.schedule(dispatchBufferCreation, bufferCreationInterval, creationState));
        }
        return _this;
      }
      BufferTimeSubscriber2.prototype._next = function(value) {
        var contexts = this.contexts;
        var len = contexts.length;
        var filledBufferContext;
        for (var i = 0; i < len; i++) {
          var context_1 = contexts[i];
          var buffer2 = context_1.buffer;
          buffer2.push(value);
          if (buffer2.length == this.maxBufferSize) {
            filledBufferContext = context_1;
          }
        }
        if (filledBufferContext) {
          this.onBufferFull(filledBufferContext);
        }
      };
      BufferTimeSubscriber2.prototype._error = function(err) {
        this.contexts.length = 0;
        _super.prototype._error.call(this, err);
      };
      BufferTimeSubscriber2.prototype._complete = function() {
        var _a = this, contexts = _a.contexts, destination = _a.destination;
        while (contexts.length > 0) {
          var context_2 = contexts.shift();
          destination.next(context_2.buffer);
        }
        _super.prototype._complete.call(this);
      };
      BufferTimeSubscriber2.prototype._unsubscribe = function() {
        this.contexts = null;
      };
      BufferTimeSubscriber2.prototype.onBufferFull = function(context) {
        this.closeContext(context);
        var closeAction = context.closeAction;
        closeAction.unsubscribe();
        this.remove(closeAction);
        if (!this.closed && this.timespanOnly) {
          context = this.openContext();
          var bufferTimeSpan = this.bufferTimeSpan;
          var timeSpanOnlyState = { subscriber: this, context, bufferTimeSpan };
          this.add(context.closeAction = this.scheduler.schedule(dispatchBufferTimeSpanOnly, bufferTimeSpan, timeSpanOnlyState));
        }
      };
      BufferTimeSubscriber2.prototype.openContext = function() {
        var context = new Context();
        this.contexts.push(context);
        return context;
      };
      BufferTimeSubscriber2.prototype.closeContext = function(context) {
        this.destination.next(context.buffer);
        var contexts = this.contexts;
        var spliceIndex = contexts ? contexts.indexOf(context) : -1;
        if (spliceIndex >= 0) {
          contexts.splice(contexts.indexOf(context), 1);
        }
      };
      return BufferTimeSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/bufferToggle.js
function bufferToggle(openings, closingSelector) {
  return function bufferToggleOperatorFunction(source) {
    return source.lift(new BufferToggleOperator(openings, closingSelector));
  };
}
var BufferToggleOperator, BufferToggleSubscriber;
var init_bufferToggle = __esm({
  "node_modules/rxjs/_esm5/internal/operators/bufferToggle.js"() {
    init_tslib_es6();
    init_Subscription();
    init_subscribeToResult();
    init_OuterSubscriber();
    BufferToggleOperator = (function() {
      function BufferToggleOperator2(openings, closingSelector) {
        this.openings = openings;
        this.closingSelector = closingSelector;
      }
      BufferToggleOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new BufferToggleSubscriber(subscriber, this.openings, this.closingSelector));
      };
      return BufferToggleOperator2;
    })();
    BufferToggleSubscriber = (function(_super) {
      __extends(BufferToggleSubscriber2, _super);
      function BufferToggleSubscriber2(destination, openings, closingSelector) {
        var _this = _super.call(this, destination) || this;
        _this.closingSelector = closingSelector;
        _this.contexts = [];
        _this.add(subscribeToResult(_this, openings));
        return _this;
      }
      BufferToggleSubscriber2.prototype._next = function(value) {
        var contexts = this.contexts;
        var len = contexts.length;
        for (var i = 0; i < len; i++) {
          contexts[i].buffer.push(value);
        }
      };
      BufferToggleSubscriber2.prototype._error = function(err) {
        var contexts = this.contexts;
        while (contexts.length > 0) {
          var context_1 = contexts.shift();
          context_1.subscription.unsubscribe();
          context_1.buffer = null;
          context_1.subscription = null;
        }
        this.contexts = null;
        _super.prototype._error.call(this, err);
      };
      BufferToggleSubscriber2.prototype._complete = function() {
        var contexts = this.contexts;
        while (contexts.length > 0) {
          var context_2 = contexts.shift();
          this.destination.next(context_2.buffer);
          context_2.subscription.unsubscribe();
          context_2.buffer = null;
          context_2.subscription = null;
        }
        this.contexts = null;
        _super.prototype._complete.call(this);
      };
      BufferToggleSubscriber2.prototype.notifyNext = function(outerValue, innerValue) {
        outerValue ? this.closeBuffer(outerValue) : this.openBuffer(innerValue);
      };
      BufferToggleSubscriber2.prototype.notifyComplete = function(innerSub) {
        this.closeBuffer(innerSub.context);
      };
      BufferToggleSubscriber2.prototype.openBuffer = function(value) {
        try {
          var closingSelector = this.closingSelector;
          var closingNotifier = closingSelector.call(this, value);
          if (closingNotifier) {
            this.trySubscribe(closingNotifier);
          }
        } catch (err) {
          this._error(err);
        }
      };
      BufferToggleSubscriber2.prototype.closeBuffer = function(context) {
        var contexts = this.contexts;
        if (contexts && context) {
          var buffer2 = context.buffer, subscription = context.subscription;
          this.destination.next(buffer2);
          contexts.splice(contexts.indexOf(context), 1);
          this.remove(subscription);
          subscription.unsubscribe();
        }
      };
      BufferToggleSubscriber2.prototype.trySubscribe = function(closingNotifier) {
        var contexts = this.contexts;
        var buffer2 = [];
        var subscription = new Subscription();
        var context = { buffer: buffer2, subscription };
        contexts.push(context);
        var innerSubscription = subscribeToResult(this, closingNotifier, context);
        if (!innerSubscription || innerSubscription.closed) {
          this.closeBuffer(context);
        } else {
          innerSubscription.context = context;
          this.add(innerSubscription);
          subscription.add(innerSubscription);
        }
      };
      return BufferToggleSubscriber2;
    })(OuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/bufferWhen.js
function bufferWhen(closingSelector) {
  return function(source) {
    return source.lift(new BufferWhenOperator(closingSelector));
  };
}
var BufferWhenOperator, BufferWhenSubscriber;
var init_bufferWhen = __esm({
  "node_modules/rxjs/_esm5/internal/operators/bufferWhen.js"() {
    init_tslib_es6();
    init_Subscription();
    init_innerSubscribe();
    BufferWhenOperator = (function() {
      function BufferWhenOperator2(closingSelector) {
        this.closingSelector = closingSelector;
      }
      BufferWhenOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new BufferWhenSubscriber(subscriber, this.closingSelector));
      };
      return BufferWhenOperator2;
    })();
    BufferWhenSubscriber = (function(_super) {
      __extends(BufferWhenSubscriber2, _super);
      function BufferWhenSubscriber2(destination, closingSelector) {
        var _this = _super.call(this, destination) || this;
        _this.closingSelector = closingSelector;
        _this.subscribing = false;
        _this.openBuffer();
        return _this;
      }
      BufferWhenSubscriber2.prototype._next = function(value) {
        this.buffer.push(value);
      };
      BufferWhenSubscriber2.prototype._complete = function() {
        var buffer2 = this.buffer;
        if (buffer2) {
          this.destination.next(buffer2);
        }
        _super.prototype._complete.call(this);
      };
      BufferWhenSubscriber2.prototype._unsubscribe = function() {
        this.buffer = void 0;
        this.subscribing = false;
      };
      BufferWhenSubscriber2.prototype.notifyNext = function() {
        this.openBuffer();
      };
      BufferWhenSubscriber2.prototype.notifyComplete = function() {
        if (this.subscribing) {
          this.complete();
        } else {
          this.openBuffer();
        }
      };
      BufferWhenSubscriber2.prototype.openBuffer = function() {
        var closingSubscription = this.closingSubscription;
        if (closingSubscription) {
          this.remove(closingSubscription);
          closingSubscription.unsubscribe();
        }
        var buffer2 = this.buffer;
        if (this.buffer) {
          this.destination.next(buffer2);
        }
        this.buffer = [];
        var closingNotifier;
        try {
          var closingSelector = this.closingSelector;
          closingNotifier = closingSelector();
        } catch (err) {
          return this.error(err);
        }
        closingSubscription = new Subscription();
        this.closingSubscription = closingSubscription;
        this.add(closingSubscription);
        this.subscribing = true;
        closingSubscription.add(innerSubscribe(closingNotifier, new SimpleInnerSubscriber(this)));
        this.subscribing = false;
      };
      return BufferWhenSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/catchError.js
function catchError(selector) {
  return function catchErrorOperatorFunction(source) {
    var operator = new CatchOperator(selector);
    var caught = source.lift(operator);
    return operator.caught = caught;
  };
}
var CatchOperator, CatchSubscriber;
var init_catchError = __esm({
  "node_modules/rxjs/_esm5/internal/operators/catchError.js"() {
    init_tslib_es6();
    init_innerSubscribe();
    CatchOperator = (function() {
      function CatchOperator2(selector) {
        this.selector = selector;
      }
      CatchOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new CatchSubscriber(subscriber, this.selector, this.caught));
      };
      return CatchOperator2;
    })();
    CatchSubscriber = (function(_super) {
      __extends(CatchSubscriber2, _super);
      function CatchSubscriber2(destination, selector, caught) {
        var _this = _super.call(this, destination) || this;
        _this.selector = selector;
        _this.caught = caught;
        return _this;
      }
      CatchSubscriber2.prototype.error = function(err) {
        if (!this.isStopped) {
          var result = void 0;
          try {
            result = this.selector(err, this.caught);
          } catch (err2) {
            _super.prototype.error.call(this, err2);
            return;
          }
          this._unsubscribeAndRecycle();
          var innerSubscriber = new SimpleInnerSubscriber(this);
          this.add(innerSubscriber);
          var innerSubscription = innerSubscribe(result, innerSubscriber);
          if (innerSubscription !== innerSubscriber) {
            this.add(innerSubscription);
          }
        }
      };
      return CatchSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/combineAll.js
function combineAll(project) {
  return function(source) {
    return source.lift(new CombineLatestOperator(project));
  };
}
var init_combineAll = __esm({
  "node_modules/rxjs/_esm5/internal/operators/combineAll.js"() {
    init_combineLatest();
  }
});

// node_modules/rxjs/_esm5/internal/operators/combineLatest.js
function combineLatest() {
  var observables = [];
  for (var _i = 0; _i < arguments.length; _i++) {
    observables[_i] = arguments[_i];
  }
  var project = null;
  if (typeof observables[observables.length - 1] === "function") {
    project = observables.pop();
  }
  if (observables.length === 1 && isArray(observables[0])) {
    observables = observables[0].slice();
  }
  return function(source) {
    return source.lift.call(from([source].concat(observables)), new CombineLatestOperator(project));
  };
}
var init_combineLatest2 = __esm({
  "node_modules/rxjs/_esm5/internal/operators/combineLatest.js"() {
    init_isArray();
    init_combineLatest();
    init_from();
  }
});

// node_modules/rxjs/_esm5/internal/operators/concat.js
function concat2() {
  var observables = [];
  for (var _i = 0; _i < arguments.length; _i++) {
    observables[_i] = arguments[_i];
  }
  return function(source) {
    return source.lift.call(concat.apply(void 0, [source].concat(observables)));
  };
}
var init_concat2 = __esm({
  "node_modules/rxjs/_esm5/internal/operators/concat.js"() {
    init_concat();
  }
});

// node_modules/rxjs/_esm5/internal/operators/concatMap.js
function concatMap(project, resultSelector) {
  return mergeMap(project, resultSelector, 1);
}
var init_concatMap = __esm({
  "node_modules/rxjs/_esm5/internal/operators/concatMap.js"() {
    init_mergeMap();
  }
});

// node_modules/rxjs/_esm5/internal/operators/concatMapTo.js
function concatMapTo(innerObservable, resultSelector) {
  return concatMap(function() {
    return innerObservable;
  }, resultSelector);
}
var init_concatMapTo = __esm({
  "node_modules/rxjs/_esm5/internal/operators/concatMapTo.js"() {
    init_concatMap();
  }
});

// node_modules/rxjs/_esm5/internal/operators/count.js
function count(predicate) {
  return function(source) {
    return source.lift(new CountOperator(predicate, source));
  };
}
var CountOperator, CountSubscriber;
var init_count = __esm({
  "node_modules/rxjs/_esm5/internal/operators/count.js"() {
    init_tslib_es6();
    init_Subscriber();
    CountOperator = (function() {
      function CountOperator2(predicate, source) {
        this.predicate = predicate;
        this.source = source;
      }
      CountOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new CountSubscriber(subscriber, this.predicate, this.source));
      };
      return CountOperator2;
    })();
    CountSubscriber = (function(_super) {
      __extends(CountSubscriber2, _super);
      function CountSubscriber2(destination, predicate, source) {
        var _this = _super.call(this, destination) || this;
        _this.predicate = predicate;
        _this.source = source;
        _this.count = 0;
        _this.index = 0;
        return _this;
      }
      CountSubscriber2.prototype._next = function(value) {
        if (this.predicate) {
          this._tryPredicate(value);
        } else {
          this.count++;
        }
      };
      CountSubscriber2.prototype._tryPredicate = function(value) {
        var result;
        try {
          result = this.predicate(value, this.index++, this.source);
        } catch (err) {
          this.destination.error(err);
          return;
        }
        if (result) {
          this.count++;
        }
      };
      CountSubscriber2.prototype._complete = function() {
        this.destination.next(this.count);
        this.destination.complete();
      };
      return CountSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/debounce.js
function debounce(durationSelector) {
  return function(source) {
    return source.lift(new DebounceOperator(durationSelector));
  };
}
var DebounceOperator, DebounceSubscriber;
var init_debounce = __esm({
  "node_modules/rxjs/_esm5/internal/operators/debounce.js"() {
    init_tslib_es6();
    init_innerSubscribe();
    DebounceOperator = (function() {
      function DebounceOperator2(durationSelector) {
        this.durationSelector = durationSelector;
      }
      DebounceOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new DebounceSubscriber(subscriber, this.durationSelector));
      };
      return DebounceOperator2;
    })();
    DebounceSubscriber = (function(_super) {
      __extends(DebounceSubscriber2, _super);
      function DebounceSubscriber2(destination, durationSelector) {
        var _this = _super.call(this, destination) || this;
        _this.durationSelector = durationSelector;
        _this.hasValue = false;
        return _this;
      }
      DebounceSubscriber2.prototype._next = function(value) {
        try {
          var result = this.durationSelector.call(this, value);
          if (result) {
            this._tryNext(value, result);
          }
        } catch (err) {
          this.destination.error(err);
        }
      };
      DebounceSubscriber2.prototype._complete = function() {
        this.emitValue();
        this.destination.complete();
      };
      DebounceSubscriber2.prototype._tryNext = function(value, duration) {
        var subscription = this.durationSubscription;
        this.value = value;
        this.hasValue = true;
        if (subscription) {
          subscription.unsubscribe();
          this.remove(subscription);
        }
        subscription = innerSubscribe(duration, new SimpleInnerSubscriber(this));
        if (subscription && !subscription.closed) {
          this.add(this.durationSubscription = subscription);
        }
      };
      DebounceSubscriber2.prototype.notifyNext = function() {
        this.emitValue();
      };
      DebounceSubscriber2.prototype.notifyComplete = function() {
        this.emitValue();
      };
      DebounceSubscriber2.prototype.emitValue = function() {
        if (this.hasValue) {
          var value = this.value;
          var subscription = this.durationSubscription;
          if (subscription) {
            this.durationSubscription = void 0;
            subscription.unsubscribe();
            this.remove(subscription);
          }
          this.value = void 0;
          this.hasValue = false;
          _super.prototype._next.call(this, value);
        }
      };
      return DebounceSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/debounceTime.js
function debounceTime(dueTime, scheduler) {
  if (scheduler === void 0) {
    scheduler = async;
  }
  return function(source) {
    return source.lift(new DebounceTimeOperator(dueTime, scheduler));
  };
}
function dispatchNext(subscriber) {
  subscriber.debouncedNext();
}
var DebounceTimeOperator, DebounceTimeSubscriber;
var init_debounceTime = __esm({
  "node_modules/rxjs/_esm5/internal/operators/debounceTime.js"() {
    init_tslib_es6();
    init_Subscriber();
    init_async();
    DebounceTimeOperator = (function() {
      function DebounceTimeOperator2(dueTime, scheduler) {
        this.dueTime = dueTime;
        this.scheduler = scheduler;
      }
      DebounceTimeOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new DebounceTimeSubscriber(subscriber, this.dueTime, this.scheduler));
      };
      return DebounceTimeOperator2;
    })();
    DebounceTimeSubscriber = (function(_super) {
      __extends(DebounceTimeSubscriber2, _super);
      function DebounceTimeSubscriber2(destination, dueTime, scheduler) {
        var _this = _super.call(this, destination) || this;
        _this.dueTime = dueTime;
        _this.scheduler = scheduler;
        _this.debouncedSubscription = null;
        _this.lastValue = null;
        _this.hasValue = false;
        return _this;
      }
      DebounceTimeSubscriber2.prototype._next = function(value) {
        this.clearDebounce();
        this.lastValue = value;
        this.hasValue = true;
        this.add(this.debouncedSubscription = this.scheduler.schedule(dispatchNext, this.dueTime, this));
      };
      DebounceTimeSubscriber2.prototype._complete = function() {
        this.debouncedNext();
        this.destination.complete();
      };
      DebounceTimeSubscriber2.prototype.debouncedNext = function() {
        this.clearDebounce();
        if (this.hasValue) {
          var lastValue = this.lastValue;
          this.lastValue = null;
          this.hasValue = false;
          this.destination.next(lastValue);
        }
      };
      DebounceTimeSubscriber2.prototype.clearDebounce = function() {
        var debouncedSubscription = this.debouncedSubscription;
        if (debouncedSubscription !== null) {
          this.remove(debouncedSubscription);
          debouncedSubscription.unsubscribe();
          this.debouncedSubscription = null;
        }
      };
      return DebounceTimeSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/defaultIfEmpty.js
function defaultIfEmpty(defaultValue) {
  if (defaultValue === void 0) {
    defaultValue = null;
  }
  return function(source) {
    return source.lift(new DefaultIfEmptyOperator(defaultValue));
  };
}
var DefaultIfEmptyOperator, DefaultIfEmptySubscriber;
var init_defaultIfEmpty = __esm({
  "node_modules/rxjs/_esm5/internal/operators/defaultIfEmpty.js"() {
    init_tslib_es6();
    init_Subscriber();
    DefaultIfEmptyOperator = (function() {
      function DefaultIfEmptyOperator2(defaultValue) {
        this.defaultValue = defaultValue;
      }
      DefaultIfEmptyOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new DefaultIfEmptySubscriber(subscriber, this.defaultValue));
      };
      return DefaultIfEmptyOperator2;
    })();
    DefaultIfEmptySubscriber = (function(_super) {
      __extends(DefaultIfEmptySubscriber2, _super);
      function DefaultIfEmptySubscriber2(destination, defaultValue) {
        var _this = _super.call(this, destination) || this;
        _this.defaultValue = defaultValue;
        _this.isEmpty = true;
        return _this;
      }
      DefaultIfEmptySubscriber2.prototype._next = function(value) {
        this.isEmpty = false;
        this.destination.next(value);
      };
      DefaultIfEmptySubscriber2.prototype._complete = function() {
        if (this.isEmpty) {
          this.destination.next(this.defaultValue);
        }
        this.destination.complete();
      };
      return DefaultIfEmptySubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/util/isDate.js
function isDate(value) {
  return value instanceof Date && !isNaN(+value);
}
var init_isDate = __esm({
  "node_modules/rxjs/_esm5/internal/util/isDate.js"() {
  }
});

// node_modules/rxjs/_esm5/internal/operators/delay.js
function delay(delay2, scheduler) {
  if (scheduler === void 0) {
    scheduler = async;
  }
  var absoluteDelay = isDate(delay2);
  var delayFor = absoluteDelay ? +delay2 - scheduler.now() : Math.abs(delay2);
  return function(source) {
    return source.lift(new DelayOperator(delayFor, scheduler));
  };
}
var DelayOperator, DelaySubscriber, DelayMessage;
var init_delay = __esm({
  "node_modules/rxjs/_esm5/internal/operators/delay.js"() {
    init_tslib_es6();
    init_async();
    init_isDate();
    init_Subscriber();
    init_Notification();
    DelayOperator = (function() {
      function DelayOperator2(delay2, scheduler) {
        this.delay = delay2;
        this.scheduler = scheduler;
      }
      DelayOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new DelaySubscriber(subscriber, this.delay, this.scheduler));
      };
      return DelayOperator2;
    })();
    DelaySubscriber = (function(_super) {
      __extends(DelaySubscriber2, _super);
      function DelaySubscriber2(destination, delay2, scheduler) {
        var _this = _super.call(this, destination) || this;
        _this.delay = delay2;
        _this.scheduler = scheduler;
        _this.queue = [];
        _this.active = false;
        _this.errored = false;
        return _this;
      }
      DelaySubscriber2.dispatch = function(state) {
        var source = state.source;
        var queue = source.queue;
        var scheduler = state.scheduler;
        var destination = state.destination;
        while (queue.length > 0 && queue[0].time - scheduler.now() <= 0) {
          queue.shift().notification.observe(destination);
        }
        if (queue.length > 0) {
          var delay_1 = Math.max(0, queue[0].time - scheduler.now());
          this.schedule(state, delay_1);
        } else {
          this.unsubscribe();
          source.active = false;
        }
      };
      DelaySubscriber2.prototype._schedule = function(scheduler) {
        this.active = true;
        var destination = this.destination;
        destination.add(scheduler.schedule(DelaySubscriber2.dispatch, this.delay, {
          source: this,
          destination: this.destination,
          scheduler
        }));
      };
      DelaySubscriber2.prototype.scheduleNotification = function(notification) {
        if (this.errored === true) {
          return;
        }
        var scheduler = this.scheduler;
        var message = new DelayMessage(scheduler.now() + this.delay, notification);
        this.queue.push(message);
        if (this.active === false) {
          this._schedule(scheduler);
        }
      };
      DelaySubscriber2.prototype._next = function(value) {
        this.scheduleNotification(Notification.createNext(value));
      };
      DelaySubscriber2.prototype._error = function(err) {
        this.errored = true;
        this.queue = [];
        this.destination.error(err);
        this.unsubscribe();
      };
      DelaySubscriber2.prototype._complete = function() {
        this.scheduleNotification(Notification.createComplete());
        this.unsubscribe();
      };
      return DelaySubscriber2;
    })(Subscriber);
    DelayMessage = /* @__PURE__ */ (function() {
      function DelayMessage2(time, notification) {
        this.time = time;
        this.notification = notification;
      }
      return DelayMessage2;
    })();
  }
});

// node_modules/rxjs/_esm5/internal/operators/delayWhen.js
function delayWhen(delayDurationSelector, subscriptionDelay) {
  if (subscriptionDelay) {
    return function(source) {
      return new SubscriptionDelayObservable(source, subscriptionDelay).lift(new DelayWhenOperator(delayDurationSelector));
    };
  }
  return function(source) {
    return source.lift(new DelayWhenOperator(delayDurationSelector));
  };
}
var DelayWhenOperator, DelayWhenSubscriber, SubscriptionDelayObservable, SubscriptionDelaySubscriber;
var init_delayWhen = __esm({
  "node_modules/rxjs/_esm5/internal/operators/delayWhen.js"() {
    init_tslib_es6();
    init_Subscriber();
    init_Observable();
    init_OuterSubscriber();
    init_subscribeToResult();
    DelayWhenOperator = (function() {
      function DelayWhenOperator2(delayDurationSelector) {
        this.delayDurationSelector = delayDurationSelector;
      }
      DelayWhenOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new DelayWhenSubscriber(subscriber, this.delayDurationSelector));
      };
      return DelayWhenOperator2;
    })();
    DelayWhenSubscriber = (function(_super) {
      __extends(DelayWhenSubscriber2, _super);
      function DelayWhenSubscriber2(destination, delayDurationSelector) {
        var _this = _super.call(this, destination) || this;
        _this.delayDurationSelector = delayDurationSelector;
        _this.completed = false;
        _this.delayNotifierSubscriptions = [];
        _this.index = 0;
        return _this;
      }
      DelayWhenSubscriber2.prototype.notifyNext = function(outerValue, _innerValue, _outerIndex, _innerIndex, innerSub) {
        this.destination.next(outerValue);
        this.removeSubscription(innerSub);
        this.tryComplete();
      };
      DelayWhenSubscriber2.prototype.notifyError = function(error, innerSub) {
        this._error(error);
      };
      DelayWhenSubscriber2.prototype.notifyComplete = function(innerSub) {
        var value = this.removeSubscription(innerSub);
        if (value) {
          this.destination.next(value);
        }
        this.tryComplete();
      };
      DelayWhenSubscriber2.prototype._next = function(value) {
        var index = this.index++;
        try {
          var delayNotifier = this.delayDurationSelector(value, index);
          if (delayNotifier) {
            this.tryDelay(delayNotifier, value);
          }
        } catch (err) {
          this.destination.error(err);
        }
      };
      DelayWhenSubscriber2.prototype._complete = function() {
        this.completed = true;
        this.tryComplete();
        this.unsubscribe();
      };
      DelayWhenSubscriber2.prototype.removeSubscription = function(subscription) {
        subscription.unsubscribe();
        var subscriptionIdx = this.delayNotifierSubscriptions.indexOf(subscription);
        if (subscriptionIdx !== -1) {
          this.delayNotifierSubscriptions.splice(subscriptionIdx, 1);
        }
        return subscription.outerValue;
      };
      DelayWhenSubscriber2.prototype.tryDelay = function(delayNotifier, value) {
        var notifierSubscription = subscribeToResult(this, delayNotifier, value);
        if (notifierSubscription && !notifierSubscription.closed) {
          var destination = this.destination;
          destination.add(notifierSubscription);
          this.delayNotifierSubscriptions.push(notifierSubscription);
        }
      };
      DelayWhenSubscriber2.prototype.tryComplete = function() {
        if (this.completed && this.delayNotifierSubscriptions.length === 0) {
          this.destination.complete();
        }
      };
      return DelayWhenSubscriber2;
    })(OuterSubscriber);
    SubscriptionDelayObservable = (function(_super) {
      __extends(SubscriptionDelayObservable2, _super);
      function SubscriptionDelayObservable2(source, subscriptionDelay) {
        var _this = _super.call(this) || this;
        _this.source = source;
        _this.subscriptionDelay = subscriptionDelay;
        return _this;
      }
      SubscriptionDelayObservable2.prototype._subscribe = function(subscriber) {
        this.subscriptionDelay.subscribe(new SubscriptionDelaySubscriber(subscriber, this.source));
      };
      return SubscriptionDelayObservable2;
    })(Observable);
    SubscriptionDelaySubscriber = (function(_super) {
      __extends(SubscriptionDelaySubscriber2, _super);
      function SubscriptionDelaySubscriber2(parent, source) {
        var _this = _super.call(this) || this;
        _this.parent = parent;
        _this.source = source;
        _this.sourceSubscribed = false;
        return _this;
      }
      SubscriptionDelaySubscriber2.prototype._next = function(unused) {
        this.subscribeToSource();
      };
      SubscriptionDelaySubscriber2.prototype._error = function(err) {
        this.unsubscribe();
        this.parent.error(err);
      };
      SubscriptionDelaySubscriber2.prototype._complete = function() {
        this.unsubscribe();
        this.subscribeToSource();
      };
      SubscriptionDelaySubscriber2.prototype.subscribeToSource = function() {
        if (!this.sourceSubscribed) {
          this.sourceSubscribed = true;
          this.unsubscribe();
          this.source.subscribe(this.parent);
        }
      };
      return SubscriptionDelaySubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/dematerialize.js
function dematerialize() {
  return function dematerializeOperatorFunction(source) {
    return source.lift(new DeMaterializeOperator());
  };
}
var DeMaterializeOperator, DeMaterializeSubscriber;
var init_dematerialize = __esm({
  "node_modules/rxjs/_esm5/internal/operators/dematerialize.js"() {
    init_tslib_es6();
    init_Subscriber();
    DeMaterializeOperator = (function() {
      function DeMaterializeOperator2() {
      }
      DeMaterializeOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new DeMaterializeSubscriber(subscriber));
      };
      return DeMaterializeOperator2;
    })();
    DeMaterializeSubscriber = (function(_super) {
      __extends(DeMaterializeSubscriber2, _super);
      function DeMaterializeSubscriber2(destination) {
        return _super.call(this, destination) || this;
      }
      DeMaterializeSubscriber2.prototype._next = function(value) {
        value.observe(this.destination);
      };
      return DeMaterializeSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/distinct.js
function distinct(keySelector, flushes) {
  return function(source) {
    return source.lift(new DistinctOperator(keySelector, flushes));
  };
}
var DistinctOperator, DistinctSubscriber;
var init_distinct = __esm({
  "node_modules/rxjs/_esm5/internal/operators/distinct.js"() {
    init_tslib_es6();
    init_innerSubscribe();
    DistinctOperator = (function() {
      function DistinctOperator2(keySelector, flushes) {
        this.keySelector = keySelector;
        this.flushes = flushes;
      }
      DistinctOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new DistinctSubscriber(subscriber, this.keySelector, this.flushes));
      };
      return DistinctOperator2;
    })();
    DistinctSubscriber = (function(_super) {
      __extends(DistinctSubscriber2, _super);
      function DistinctSubscriber2(destination, keySelector, flushes) {
        var _this = _super.call(this, destination) || this;
        _this.keySelector = keySelector;
        _this.values = /* @__PURE__ */ new Set();
        if (flushes) {
          _this.add(innerSubscribe(flushes, new SimpleInnerSubscriber(_this)));
        }
        return _this;
      }
      DistinctSubscriber2.prototype.notifyNext = function() {
        this.values.clear();
      };
      DistinctSubscriber2.prototype.notifyError = function(error) {
        this._error(error);
      };
      DistinctSubscriber2.prototype._next = function(value) {
        if (this.keySelector) {
          this._useKeySelector(value);
        } else {
          this._finalizeNext(value, value);
        }
      };
      DistinctSubscriber2.prototype._useKeySelector = function(value) {
        var key;
        var destination = this.destination;
        try {
          key = this.keySelector(value);
        } catch (err) {
          destination.error(err);
          return;
        }
        this._finalizeNext(key, value);
      };
      DistinctSubscriber2.prototype._finalizeNext = function(key, value) {
        var values = this.values;
        if (!values.has(key)) {
          values.add(key);
          this.destination.next(value);
        }
      };
      return DistinctSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/distinctUntilChanged.js
function distinctUntilChanged(compare, keySelector) {
  return function(source) {
    return source.lift(new DistinctUntilChangedOperator(compare, keySelector));
  };
}
var DistinctUntilChangedOperator, DistinctUntilChangedSubscriber;
var init_distinctUntilChanged = __esm({
  "node_modules/rxjs/_esm5/internal/operators/distinctUntilChanged.js"() {
    init_tslib_es6();
    init_Subscriber();
    DistinctUntilChangedOperator = (function() {
      function DistinctUntilChangedOperator2(compare, keySelector) {
        this.compare = compare;
        this.keySelector = keySelector;
      }
      DistinctUntilChangedOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new DistinctUntilChangedSubscriber(subscriber, this.compare, this.keySelector));
      };
      return DistinctUntilChangedOperator2;
    })();
    DistinctUntilChangedSubscriber = (function(_super) {
      __extends(DistinctUntilChangedSubscriber2, _super);
      function DistinctUntilChangedSubscriber2(destination, compare, keySelector) {
        var _this = _super.call(this, destination) || this;
        _this.keySelector = keySelector;
        _this.hasKey = false;
        if (typeof compare === "function") {
          _this.compare = compare;
        }
        return _this;
      }
      DistinctUntilChangedSubscriber2.prototype.compare = function(x, y) {
        return x === y;
      };
      DistinctUntilChangedSubscriber2.prototype._next = function(value) {
        var key;
        try {
          var keySelector = this.keySelector;
          key = keySelector ? keySelector(value) : value;
        } catch (err) {
          return this.destination.error(err);
        }
        var result = false;
        if (this.hasKey) {
          try {
            var compare = this.compare;
            result = compare(this.key, key);
          } catch (err) {
            return this.destination.error(err);
          }
        } else {
          this.hasKey = true;
        }
        if (!result) {
          this.key = key;
          this.destination.next(value);
        }
      };
      return DistinctUntilChangedSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/distinctUntilKeyChanged.js
function distinctUntilKeyChanged(key, compare) {
  return distinctUntilChanged(function(x, y) {
    return compare ? compare(x[key], y[key]) : x[key] === y[key];
  });
}
var init_distinctUntilKeyChanged = __esm({
  "node_modules/rxjs/_esm5/internal/operators/distinctUntilKeyChanged.js"() {
    init_distinctUntilChanged();
  }
});

// node_modules/rxjs/_esm5/internal/operators/throwIfEmpty.js
function throwIfEmpty(errorFactory) {
  if (errorFactory === void 0) {
    errorFactory = defaultErrorFactory;
  }
  return function(source) {
    return source.lift(new ThrowIfEmptyOperator(errorFactory));
  };
}
function defaultErrorFactory() {
  return new EmptyError();
}
var ThrowIfEmptyOperator, ThrowIfEmptySubscriber;
var init_throwIfEmpty = __esm({
  "node_modules/rxjs/_esm5/internal/operators/throwIfEmpty.js"() {
    init_tslib_es6();
    init_EmptyError();
    init_Subscriber();
    ThrowIfEmptyOperator = (function() {
      function ThrowIfEmptyOperator2(errorFactory) {
        this.errorFactory = errorFactory;
      }
      ThrowIfEmptyOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new ThrowIfEmptySubscriber(subscriber, this.errorFactory));
      };
      return ThrowIfEmptyOperator2;
    })();
    ThrowIfEmptySubscriber = (function(_super) {
      __extends(ThrowIfEmptySubscriber2, _super);
      function ThrowIfEmptySubscriber2(destination, errorFactory) {
        var _this = _super.call(this, destination) || this;
        _this.errorFactory = errorFactory;
        _this.hasValue = false;
        return _this;
      }
      ThrowIfEmptySubscriber2.prototype._next = function(value) {
        this.hasValue = true;
        this.destination.next(value);
      };
      ThrowIfEmptySubscriber2.prototype._complete = function() {
        if (!this.hasValue) {
          var err = void 0;
          try {
            err = this.errorFactory();
          } catch (e) {
            err = e;
          }
          this.destination.error(err);
        } else {
          return this.destination.complete();
        }
      };
      return ThrowIfEmptySubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/take.js
function take(count2) {
  return function(source) {
    if (count2 === 0) {
      return empty();
    } else {
      return source.lift(new TakeOperator(count2));
    }
  };
}
var TakeOperator, TakeSubscriber;
var init_take = __esm({
  "node_modules/rxjs/_esm5/internal/operators/take.js"() {
    init_tslib_es6();
    init_Subscriber();
    init_ArgumentOutOfRangeError();
    init_empty();
    TakeOperator = (function() {
      function TakeOperator2(total) {
        this.total = total;
        if (this.total < 0) {
          throw new ArgumentOutOfRangeError();
        }
      }
      TakeOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new TakeSubscriber(subscriber, this.total));
      };
      return TakeOperator2;
    })();
    TakeSubscriber = (function(_super) {
      __extends(TakeSubscriber2, _super);
      function TakeSubscriber2(destination, total) {
        var _this = _super.call(this, destination) || this;
        _this.total = total;
        _this.count = 0;
        return _this;
      }
      TakeSubscriber2.prototype._next = function(value) {
        var total = this.total;
        var count2 = ++this.count;
        if (count2 <= total) {
          this.destination.next(value);
          if (count2 === total) {
            this.destination.complete();
            this.unsubscribe();
          }
        }
      };
      return TakeSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/elementAt.js
function elementAt(index, defaultValue) {
  if (index < 0) {
    throw new ArgumentOutOfRangeError();
  }
  var hasDefaultValue = arguments.length >= 2;
  return function(source) {
    return source.pipe(filter(function(v, i) {
      return i === index;
    }), take(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(function() {
      return new ArgumentOutOfRangeError();
    }));
  };
}
var init_elementAt = __esm({
  "node_modules/rxjs/_esm5/internal/operators/elementAt.js"() {
    init_ArgumentOutOfRangeError();
    init_filter();
    init_throwIfEmpty();
    init_defaultIfEmpty();
    init_take();
  }
});

// node_modules/rxjs/_esm5/internal/operators/endWith.js
function endWith() {
  var array = [];
  for (var _i = 0; _i < arguments.length; _i++) {
    array[_i] = arguments[_i];
  }
  return function(source) {
    return concat(source, of.apply(void 0, array));
  };
}
var init_endWith = __esm({
  "node_modules/rxjs/_esm5/internal/operators/endWith.js"() {
    init_concat();
    init_of();
  }
});

// node_modules/rxjs/_esm5/internal/operators/every.js
function every(predicate, thisArg) {
  return function(source) {
    return source.lift(new EveryOperator(predicate, thisArg, source));
  };
}
var EveryOperator, EverySubscriber;
var init_every = __esm({
  "node_modules/rxjs/_esm5/internal/operators/every.js"() {
    init_tslib_es6();
    init_Subscriber();
    EveryOperator = (function() {
      function EveryOperator2(predicate, thisArg, source) {
        this.predicate = predicate;
        this.thisArg = thisArg;
        this.source = source;
      }
      EveryOperator2.prototype.call = function(observer, source) {
        return source.subscribe(new EverySubscriber(observer, this.predicate, this.thisArg, this.source));
      };
      return EveryOperator2;
    })();
    EverySubscriber = (function(_super) {
      __extends(EverySubscriber2, _super);
      function EverySubscriber2(destination, predicate, thisArg, source) {
        var _this = _super.call(this, destination) || this;
        _this.predicate = predicate;
        _this.thisArg = thisArg;
        _this.source = source;
        _this.index = 0;
        _this.thisArg = thisArg || _this;
        return _this;
      }
      EverySubscriber2.prototype.notifyComplete = function(everyValueMatch) {
        this.destination.next(everyValueMatch);
        this.destination.complete();
      };
      EverySubscriber2.prototype._next = function(value) {
        var result = false;
        try {
          result = this.predicate.call(this.thisArg, value, this.index++, this.source);
        } catch (err) {
          this.destination.error(err);
          return;
        }
        if (!result) {
          this.notifyComplete(false);
        }
      };
      EverySubscriber2.prototype._complete = function() {
        this.notifyComplete(true);
      };
      return EverySubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/exhaust.js
function exhaust() {
  return function(source) {
    return source.lift(new SwitchFirstOperator());
  };
}
var SwitchFirstOperator, SwitchFirstSubscriber;
var init_exhaust = __esm({
  "node_modules/rxjs/_esm5/internal/operators/exhaust.js"() {
    init_tslib_es6();
    init_innerSubscribe();
    SwitchFirstOperator = (function() {
      function SwitchFirstOperator2() {
      }
      SwitchFirstOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new SwitchFirstSubscriber(subscriber));
      };
      return SwitchFirstOperator2;
    })();
    SwitchFirstSubscriber = (function(_super) {
      __extends(SwitchFirstSubscriber2, _super);
      function SwitchFirstSubscriber2(destination) {
        var _this = _super.call(this, destination) || this;
        _this.hasCompleted = false;
        _this.hasSubscription = false;
        return _this;
      }
      SwitchFirstSubscriber2.prototype._next = function(value) {
        if (!this.hasSubscription) {
          this.hasSubscription = true;
          this.add(innerSubscribe(value, new SimpleInnerSubscriber(this)));
        }
      };
      SwitchFirstSubscriber2.prototype._complete = function() {
        this.hasCompleted = true;
        if (!this.hasSubscription) {
          this.destination.complete();
        }
      };
      SwitchFirstSubscriber2.prototype.notifyComplete = function() {
        this.hasSubscription = false;
        if (this.hasCompleted) {
          this.destination.complete();
        }
      };
      return SwitchFirstSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/exhaustMap.js
function exhaustMap(project, resultSelector) {
  if (resultSelector) {
    return function(source) {
      return source.pipe(exhaustMap(function(a, i) {
        return from(project(a, i)).pipe(map(function(b, ii) {
          return resultSelector(a, b, i, ii);
        }));
      }));
    };
  }
  return function(source) {
    return source.lift(new ExhaustMapOperator(project));
  };
}
var ExhaustMapOperator, ExhaustMapSubscriber;
var init_exhaustMap = __esm({
  "node_modules/rxjs/_esm5/internal/operators/exhaustMap.js"() {
    init_tslib_es6();
    init_map();
    init_from();
    init_innerSubscribe();
    ExhaustMapOperator = (function() {
      function ExhaustMapOperator2(project) {
        this.project = project;
      }
      ExhaustMapOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new ExhaustMapSubscriber(subscriber, this.project));
      };
      return ExhaustMapOperator2;
    })();
    ExhaustMapSubscriber = (function(_super) {
      __extends(ExhaustMapSubscriber2, _super);
      function ExhaustMapSubscriber2(destination, project) {
        var _this = _super.call(this, destination) || this;
        _this.project = project;
        _this.hasSubscription = false;
        _this.hasCompleted = false;
        _this.index = 0;
        return _this;
      }
      ExhaustMapSubscriber2.prototype._next = function(value) {
        if (!this.hasSubscription) {
          this.tryNext(value);
        }
      };
      ExhaustMapSubscriber2.prototype.tryNext = function(value) {
        var result;
        var index = this.index++;
        try {
          result = this.project(value, index);
        } catch (err) {
          this.destination.error(err);
          return;
        }
        this.hasSubscription = true;
        this._innerSub(result);
      };
      ExhaustMapSubscriber2.prototype._innerSub = function(result) {
        var innerSubscriber = new SimpleInnerSubscriber(this);
        var destination = this.destination;
        destination.add(innerSubscriber);
        var innerSubscription = innerSubscribe(result, innerSubscriber);
        if (innerSubscription !== innerSubscriber) {
          destination.add(innerSubscription);
        }
      };
      ExhaustMapSubscriber2.prototype._complete = function() {
        this.hasCompleted = true;
        if (!this.hasSubscription) {
          this.destination.complete();
        }
        this.unsubscribe();
      };
      ExhaustMapSubscriber2.prototype.notifyNext = function(innerValue) {
        this.destination.next(innerValue);
      };
      ExhaustMapSubscriber2.prototype.notifyError = function(err) {
        this.destination.error(err);
      };
      ExhaustMapSubscriber2.prototype.notifyComplete = function() {
        this.hasSubscription = false;
        if (this.hasCompleted) {
          this.destination.complete();
        }
      };
      return ExhaustMapSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/expand.js
function expand(project, concurrent, scheduler) {
  if (concurrent === void 0) {
    concurrent = Number.POSITIVE_INFINITY;
  }
  concurrent = (concurrent || 0) < 1 ? Number.POSITIVE_INFINITY : concurrent;
  return function(source) {
    return source.lift(new ExpandOperator(project, concurrent, scheduler));
  };
}
var ExpandOperator, ExpandSubscriber;
var init_expand = __esm({
  "node_modules/rxjs/_esm5/internal/operators/expand.js"() {
    init_tslib_es6();
    init_innerSubscribe();
    ExpandOperator = (function() {
      function ExpandOperator2(project, concurrent, scheduler) {
        this.project = project;
        this.concurrent = concurrent;
        this.scheduler = scheduler;
      }
      ExpandOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new ExpandSubscriber(subscriber, this.project, this.concurrent, this.scheduler));
      };
      return ExpandOperator2;
    })();
    ExpandSubscriber = (function(_super) {
      __extends(ExpandSubscriber2, _super);
      function ExpandSubscriber2(destination, project, concurrent, scheduler) {
        var _this = _super.call(this, destination) || this;
        _this.project = project;
        _this.concurrent = concurrent;
        _this.scheduler = scheduler;
        _this.index = 0;
        _this.active = 0;
        _this.hasCompleted = false;
        if (concurrent < Number.POSITIVE_INFINITY) {
          _this.buffer = [];
        }
        return _this;
      }
      ExpandSubscriber2.dispatch = function(arg) {
        var subscriber = arg.subscriber, result = arg.result, value = arg.value, index = arg.index;
        subscriber.subscribeToProjection(result, value, index);
      };
      ExpandSubscriber2.prototype._next = function(value) {
        var destination = this.destination;
        if (destination.closed) {
          this._complete();
          return;
        }
        var index = this.index++;
        if (this.active < this.concurrent) {
          destination.next(value);
          try {
            var project = this.project;
            var result = project(value, index);
            if (!this.scheduler) {
              this.subscribeToProjection(result, value, index);
            } else {
              var state = { subscriber: this, result, value, index };
              var destination_1 = this.destination;
              destination_1.add(this.scheduler.schedule(ExpandSubscriber2.dispatch, 0, state));
            }
          } catch (e) {
            destination.error(e);
          }
        } else {
          this.buffer.push(value);
        }
      };
      ExpandSubscriber2.prototype.subscribeToProjection = function(result, value, index) {
        this.active++;
        var destination = this.destination;
        destination.add(innerSubscribe(result, new SimpleInnerSubscriber(this)));
      };
      ExpandSubscriber2.prototype._complete = function() {
        this.hasCompleted = true;
        if (this.hasCompleted && this.active === 0) {
          this.destination.complete();
        }
        this.unsubscribe();
      };
      ExpandSubscriber2.prototype.notifyNext = function(innerValue) {
        this._next(innerValue);
      };
      ExpandSubscriber2.prototype.notifyComplete = function() {
        var buffer2 = this.buffer;
        this.active--;
        if (buffer2 && buffer2.length > 0) {
          this._next(buffer2.shift());
        }
        if (this.hasCompleted && this.active === 0) {
          this.destination.complete();
        }
      };
      return ExpandSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/finalize.js
function finalize(callback) {
  return function(source) {
    return source.lift(new FinallyOperator(callback));
  };
}
var FinallyOperator, FinallySubscriber;
var init_finalize = __esm({
  "node_modules/rxjs/_esm5/internal/operators/finalize.js"() {
    init_tslib_es6();
    init_Subscriber();
    init_Subscription();
    FinallyOperator = (function() {
      function FinallyOperator2(callback) {
        this.callback = callback;
      }
      FinallyOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new FinallySubscriber(subscriber, this.callback));
      };
      return FinallyOperator2;
    })();
    FinallySubscriber = (function(_super) {
      __extends(FinallySubscriber2, _super);
      function FinallySubscriber2(destination, callback) {
        var _this = _super.call(this, destination) || this;
        _this.add(new Subscription(callback));
        return _this;
      }
      return FinallySubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/find.js
function find(predicate, thisArg) {
  if (typeof predicate !== "function") {
    throw new TypeError("predicate is not a function");
  }
  return function(source) {
    return source.lift(new FindValueOperator(predicate, source, false, thisArg));
  };
}
var FindValueOperator, FindValueSubscriber;
var init_find = __esm({
  "node_modules/rxjs/_esm5/internal/operators/find.js"() {
    init_tslib_es6();
    init_Subscriber();
    FindValueOperator = (function() {
      function FindValueOperator2(predicate, source, yieldIndex, thisArg) {
        this.predicate = predicate;
        this.source = source;
        this.yieldIndex = yieldIndex;
        this.thisArg = thisArg;
      }
      FindValueOperator2.prototype.call = function(observer, source) {
        return source.subscribe(new FindValueSubscriber(observer, this.predicate, this.source, this.yieldIndex, this.thisArg));
      };
      return FindValueOperator2;
    })();
    FindValueSubscriber = (function(_super) {
      __extends(FindValueSubscriber2, _super);
      function FindValueSubscriber2(destination, predicate, source, yieldIndex, thisArg) {
        var _this = _super.call(this, destination) || this;
        _this.predicate = predicate;
        _this.source = source;
        _this.yieldIndex = yieldIndex;
        _this.thisArg = thisArg;
        _this.index = 0;
        return _this;
      }
      FindValueSubscriber2.prototype.notifyComplete = function(value) {
        var destination = this.destination;
        destination.next(value);
        destination.complete();
        this.unsubscribe();
      };
      FindValueSubscriber2.prototype._next = function(value) {
        var _a = this, predicate = _a.predicate, thisArg = _a.thisArg;
        var index = this.index++;
        try {
          var result = predicate.call(thisArg || this, value, index, this.source);
          if (result) {
            this.notifyComplete(this.yieldIndex ? index : value);
          }
        } catch (err) {
          this.destination.error(err);
        }
      };
      FindValueSubscriber2.prototype._complete = function() {
        this.notifyComplete(this.yieldIndex ? -1 : void 0);
      };
      return FindValueSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/findIndex.js
function findIndex(predicate, thisArg) {
  return function(source) {
    return source.lift(new FindValueOperator(predicate, source, true, thisArg));
  };
}
var init_findIndex = __esm({
  "node_modules/rxjs/_esm5/internal/operators/findIndex.js"() {
    init_find();
  }
});

// node_modules/rxjs/_esm5/internal/operators/first.js
function first(predicate, defaultValue) {
  var hasDefaultValue = arguments.length >= 2;
  return function(source) {
    return source.pipe(predicate ? filter(function(v, i) {
      return predicate(v, i, source);
    }) : identity, take(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(function() {
      return new EmptyError();
    }));
  };
}
var init_first = __esm({
  "node_modules/rxjs/_esm5/internal/operators/first.js"() {
    init_EmptyError();
    init_filter();
    init_take();
    init_defaultIfEmpty();
    init_throwIfEmpty();
    init_identity();
  }
});

// node_modules/rxjs/_esm5/internal/operators/ignoreElements.js
function ignoreElements() {
  return function ignoreElementsOperatorFunction(source) {
    return source.lift(new IgnoreElementsOperator());
  };
}
var IgnoreElementsOperator, IgnoreElementsSubscriber;
var init_ignoreElements = __esm({
  "node_modules/rxjs/_esm5/internal/operators/ignoreElements.js"() {
    init_tslib_es6();
    init_Subscriber();
    IgnoreElementsOperator = (function() {
      function IgnoreElementsOperator2() {
      }
      IgnoreElementsOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new IgnoreElementsSubscriber(subscriber));
      };
      return IgnoreElementsOperator2;
    })();
    IgnoreElementsSubscriber = (function(_super) {
      __extends(IgnoreElementsSubscriber2, _super);
      function IgnoreElementsSubscriber2() {
        return _super !== null && _super.apply(this, arguments) || this;
      }
      IgnoreElementsSubscriber2.prototype._next = function(unused) {
      };
      return IgnoreElementsSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/isEmpty.js
function isEmpty() {
  return function(source) {
    return source.lift(new IsEmptyOperator());
  };
}
var IsEmptyOperator, IsEmptySubscriber;
var init_isEmpty = __esm({
  "node_modules/rxjs/_esm5/internal/operators/isEmpty.js"() {
    init_tslib_es6();
    init_Subscriber();
    IsEmptyOperator = (function() {
      function IsEmptyOperator2() {
      }
      IsEmptyOperator2.prototype.call = function(observer, source) {
        return source.subscribe(new IsEmptySubscriber(observer));
      };
      return IsEmptyOperator2;
    })();
    IsEmptySubscriber = (function(_super) {
      __extends(IsEmptySubscriber2, _super);
      function IsEmptySubscriber2(destination) {
        return _super.call(this, destination) || this;
      }
      IsEmptySubscriber2.prototype.notifyComplete = function(isEmpty2) {
        var destination = this.destination;
        destination.next(isEmpty2);
        destination.complete();
      };
      IsEmptySubscriber2.prototype._next = function(value) {
        this.notifyComplete(false);
      };
      IsEmptySubscriber2.prototype._complete = function() {
        this.notifyComplete(true);
      };
      return IsEmptySubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/takeLast.js
function takeLast(count2) {
  return function takeLastOperatorFunction(source) {
    if (count2 === 0) {
      return empty();
    } else {
      return source.lift(new TakeLastOperator(count2));
    }
  };
}
var TakeLastOperator, TakeLastSubscriber;
var init_takeLast = __esm({
  "node_modules/rxjs/_esm5/internal/operators/takeLast.js"() {
    init_tslib_es6();
    init_Subscriber();
    init_ArgumentOutOfRangeError();
    init_empty();
    TakeLastOperator = (function() {
      function TakeLastOperator2(total) {
        this.total = total;
        if (this.total < 0) {
          throw new ArgumentOutOfRangeError();
        }
      }
      TakeLastOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new TakeLastSubscriber(subscriber, this.total));
      };
      return TakeLastOperator2;
    })();
    TakeLastSubscriber = (function(_super) {
      __extends(TakeLastSubscriber2, _super);
      function TakeLastSubscriber2(destination, total) {
        var _this = _super.call(this, destination) || this;
        _this.total = total;
        _this.ring = new Array();
        _this.count = 0;
        return _this;
      }
      TakeLastSubscriber2.prototype._next = function(value) {
        var ring = this.ring;
        var total = this.total;
        var count2 = this.count++;
        if (ring.length < total) {
          ring.push(value);
        } else {
          var index = count2 % total;
          ring[index] = value;
        }
      };
      TakeLastSubscriber2.prototype._complete = function() {
        var destination = this.destination;
        var count2 = this.count;
        if (count2 > 0) {
          var total = this.count >= this.total ? this.total : this.count;
          var ring = this.ring;
          for (var i = 0; i < total; i++) {
            var idx = count2++ % total;
            destination.next(ring[idx]);
          }
        }
        destination.complete();
      };
      return TakeLastSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/last.js
function last(predicate, defaultValue) {
  var hasDefaultValue = arguments.length >= 2;
  return function(source) {
    return source.pipe(predicate ? filter(function(v, i) {
      return predicate(v, i, source);
    }) : identity, takeLast(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(function() {
      return new EmptyError();
    }));
  };
}
var init_last = __esm({
  "node_modules/rxjs/_esm5/internal/operators/last.js"() {
    init_EmptyError();
    init_filter();
    init_takeLast();
    init_throwIfEmpty();
    init_defaultIfEmpty();
    init_identity();
  }
});

// node_modules/rxjs/_esm5/internal/operators/mapTo.js
function mapTo(value) {
  return function(source) {
    return source.lift(new MapToOperator(value));
  };
}
var MapToOperator, MapToSubscriber;
var init_mapTo = __esm({
  "node_modules/rxjs/_esm5/internal/operators/mapTo.js"() {
    init_tslib_es6();
    init_Subscriber();
    MapToOperator = (function() {
      function MapToOperator2(value) {
        this.value = value;
      }
      MapToOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new MapToSubscriber(subscriber, this.value));
      };
      return MapToOperator2;
    })();
    MapToSubscriber = (function(_super) {
      __extends(MapToSubscriber2, _super);
      function MapToSubscriber2(destination, value) {
        var _this = _super.call(this, destination) || this;
        _this.value = value;
        return _this;
      }
      MapToSubscriber2.prototype._next = function(x) {
        this.destination.next(this.value);
      };
      return MapToSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/materialize.js
function materialize() {
  return function materializeOperatorFunction(source) {
    return source.lift(new MaterializeOperator());
  };
}
var MaterializeOperator, MaterializeSubscriber;
var init_materialize = __esm({
  "node_modules/rxjs/_esm5/internal/operators/materialize.js"() {
    init_tslib_es6();
    init_Subscriber();
    init_Notification();
    MaterializeOperator = (function() {
      function MaterializeOperator2() {
      }
      MaterializeOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new MaterializeSubscriber(subscriber));
      };
      return MaterializeOperator2;
    })();
    MaterializeSubscriber = (function(_super) {
      __extends(MaterializeSubscriber2, _super);
      function MaterializeSubscriber2(destination) {
        return _super.call(this, destination) || this;
      }
      MaterializeSubscriber2.prototype._next = function(value) {
        this.destination.next(Notification.createNext(value));
      };
      MaterializeSubscriber2.prototype._error = function(err) {
        var destination = this.destination;
        destination.next(Notification.createError(err));
        destination.complete();
      };
      MaterializeSubscriber2.prototype._complete = function() {
        var destination = this.destination;
        destination.next(Notification.createComplete());
        destination.complete();
      };
      return MaterializeSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/scan.js
function scan(accumulator, seed) {
  var hasSeed = false;
  if (arguments.length >= 2) {
    hasSeed = true;
  }
  return function scanOperatorFunction(source) {
    return source.lift(new ScanOperator(accumulator, seed, hasSeed));
  };
}
var ScanOperator, ScanSubscriber;
var init_scan = __esm({
  "node_modules/rxjs/_esm5/internal/operators/scan.js"() {
    init_tslib_es6();
    init_Subscriber();
    ScanOperator = (function() {
      function ScanOperator2(accumulator, seed, hasSeed) {
        if (hasSeed === void 0) {
          hasSeed = false;
        }
        this.accumulator = accumulator;
        this.seed = seed;
        this.hasSeed = hasSeed;
      }
      ScanOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new ScanSubscriber(subscriber, this.accumulator, this.seed, this.hasSeed));
      };
      return ScanOperator2;
    })();
    ScanSubscriber = (function(_super) {
      __extends(ScanSubscriber2, _super);
      function ScanSubscriber2(destination, accumulator, _seed, hasSeed) {
        var _this = _super.call(this, destination) || this;
        _this.accumulator = accumulator;
        _this._seed = _seed;
        _this.hasSeed = hasSeed;
        _this.index = 0;
        return _this;
      }
      Object.defineProperty(ScanSubscriber2.prototype, "seed", {
        get: function() {
          return this._seed;
        },
        set: function(value) {
          this.hasSeed = true;
          this._seed = value;
        },
        enumerable: true,
        configurable: true
      });
      ScanSubscriber2.prototype._next = function(value) {
        if (!this.hasSeed) {
          this.seed = value;
          this.destination.next(value);
        } else {
          return this._tryNext(value);
        }
      };
      ScanSubscriber2.prototype._tryNext = function(value) {
        var index = this.index++;
        var result;
        try {
          result = this.accumulator(this.seed, value, index);
        } catch (err) {
          this.destination.error(err);
        }
        this.seed = result;
        this.destination.next(result);
      };
      return ScanSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/reduce.js
function reduce(accumulator, seed) {
  if (arguments.length >= 2) {
    return function reduceOperatorFunctionWithSeed(source) {
      return pipe(scan(accumulator, seed), takeLast(1), defaultIfEmpty(seed))(source);
    };
  }
  return function reduceOperatorFunction(source) {
    return pipe(scan(function(acc, value, index) {
      return accumulator(acc, value, index + 1);
    }), takeLast(1))(source);
  };
}
var init_reduce = __esm({
  "node_modules/rxjs/_esm5/internal/operators/reduce.js"() {
    init_scan();
    init_takeLast();
    init_defaultIfEmpty();
    init_pipe();
  }
});

// node_modules/rxjs/_esm5/internal/operators/max.js
function max(comparer) {
  var max2 = typeof comparer === "function" ? function(x, y) {
    return comparer(x, y) > 0 ? x : y;
  } : function(x, y) {
    return x > y ? x : y;
  };
  return reduce(max2);
}
var init_max = __esm({
  "node_modules/rxjs/_esm5/internal/operators/max.js"() {
    init_reduce();
  }
});

// node_modules/rxjs/_esm5/internal/operators/merge.js
function merge2() {
  var observables = [];
  for (var _i = 0; _i < arguments.length; _i++) {
    observables[_i] = arguments[_i];
  }
  return function(source) {
    return source.lift.call(merge.apply(void 0, [source].concat(observables)));
  };
}
var init_merge2 = __esm({
  "node_modules/rxjs/_esm5/internal/operators/merge.js"() {
    init_merge();
  }
});

// node_modules/rxjs/_esm5/internal/operators/mergeMapTo.js
function mergeMapTo(innerObservable, resultSelector, concurrent) {
  if (concurrent === void 0) {
    concurrent = Number.POSITIVE_INFINITY;
  }
  if (typeof resultSelector === "function") {
    return mergeMap(function() {
      return innerObservable;
    }, resultSelector, concurrent);
  }
  if (typeof resultSelector === "number") {
    concurrent = resultSelector;
  }
  return mergeMap(function() {
    return innerObservable;
  }, concurrent);
}
var init_mergeMapTo = __esm({
  "node_modules/rxjs/_esm5/internal/operators/mergeMapTo.js"() {
    init_mergeMap();
  }
});

// node_modules/rxjs/_esm5/internal/operators/mergeScan.js
function mergeScan(accumulator, seed, concurrent) {
  if (concurrent === void 0) {
    concurrent = Number.POSITIVE_INFINITY;
  }
  return function(source) {
    return source.lift(new MergeScanOperator(accumulator, seed, concurrent));
  };
}
var MergeScanOperator, MergeScanSubscriber;
var init_mergeScan = __esm({
  "node_modules/rxjs/_esm5/internal/operators/mergeScan.js"() {
    init_tslib_es6();
    init_innerSubscribe();
    MergeScanOperator = (function() {
      function MergeScanOperator2(accumulator, seed, concurrent) {
        this.accumulator = accumulator;
        this.seed = seed;
        this.concurrent = concurrent;
      }
      MergeScanOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new MergeScanSubscriber(subscriber, this.accumulator, this.seed, this.concurrent));
      };
      return MergeScanOperator2;
    })();
    MergeScanSubscriber = (function(_super) {
      __extends(MergeScanSubscriber2, _super);
      function MergeScanSubscriber2(destination, accumulator, acc, concurrent) {
        var _this = _super.call(this, destination) || this;
        _this.accumulator = accumulator;
        _this.acc = acc;
        _this.concurrent = concurrent;
        _this.hasValue = false;
        _this.hasCompleted = false;
        _this.buffer = [];
        _this.active = 0;
        _this.index = 0;
        return _this;
      }
      MergeScanSubscriber2.prototype._next = function(value) {
        if (this.active < this.concurrent) {
          var index = this.index++;
          var destination = this.destination;
          var ish = void 0;
          try {
            var accumulator = this.accumulator;
            ish = accumulator(this.acc, value, index);
          } catch (e) {
            return destination.error(e);
          }
          this.active++;
          this._innerSub(ish);
        } else {
          this.buffer.push(value);
        }
      };
      MergeScanSubscriber2.prototype._innerSub = function(ish) {
        var innerSubscriber = new SimpleInnerSubscriber(this);
        var destination = this.destination;
        destination.add(innerSubscriber);
        var innerSubscription = innerSubscribe(ish, innerSubscriber);
        if (innerSubscription !== innerSubscriber) {
          destination.add(innerSubscription);
        }
      };
      MergeScanSubscriber2.prototype._complete = function() {
        this.hasCompleted = true;
        if (this.active === 0 && this.buffer.length === 0) {
          if (this.hasValue === false) {
            this.destination.next(this.acc);
          }
          this.destination.complete();
        }
        this.unsubscribe();
      };
      MergeScanSubscriber2.prototype.notifyNext = function(innerValue) {
        var destination = this.destination;
        this.acc = innerValue;
        this.hasValue = true;
        destination.next(innerValue);
      };
      MergeScanSubscriber2.prototype.notifyComplete = function() {
        var buffer2 = this.buffer;
        this.active--;
        if (buffer2.length > 0) {
          this._next(buffer2.shift());
        } else if (this.active === 0 && this.hasCompleted) {
          if (this.hasValue === false) {
            this.destination.next(this.acc);
          }
          this.destination.complete();
        }
      };
      return MergeScanSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/min.js
function min(comparer) {
  var min2 = typeof comparer === "function" ? function(x, y) {
    return comparer(x, y) < 0 ? x : y;
  } : function(x, y) {
    return x < y ? x : y;
  };
  return reduce(min2);
}
var init_min = __esm({
  "node_modules/rxjs/_esm5/internal/operators/min.js"() {
    init_reduce();
  }
});

// node_modules/rxjs/_esm5/internal/operators/multicast.js
function multicast(subjectOrSubjectFactory, selector) {
  return function multicastOperatorFunction(source) {
    var subjectFactory;
    if (typeof subjectOrSubjectFactory === "function") {
      subjectFactory = subjectOrSubjectFactory;
    } else {
      subjectFactory = function subjectFactory2() {
        return subjectOrSubjectFactory;
      };
    }
    if (typeof selector === "function") {
      return source.lift(new MulticastOperator(subjectFactory, selector));
    }
    var connectable = Object.create(source, connectableObservableDescriptor);
    connectable.source = source;
    connectable.subjectFactory = subjectFactory;
    return connectable;
  };
}
var MulticastOperator;
var init_multicast = __esm({
  "node_modules/rxjs/_esm5/internal/operators/multicast.js"() {
    init_ConnectableObservable();
    MulticastOperator = (function() {
      function MulticastOperator2(subjectFactory, selector) {
        this.subjectFactory = subjectFactory;
        this.selector = selector;
      }
      MulticastOperator2.prototype.call = function(subscriber, source) {
        var selector = this.selector;
        var subject = this.subjectFactory();
        var subscription = selector(subject).subscribe(subscriber);
        subscription.add(source.subscribe(subject));
        return subscription;
      };
      return MulticastOperator2;
    })();
  }
});

// node_modules/rxjs/_esm5/internal/operators/onErrorResumeNext.js
function onErrorResumeNext() {
  var nextSources = [];
  for (var _i = 0; _i < arguments.length; _i++) {
    nextSources[_i] = arguments[_i];
  }
  if (nextSources.length === 1 && isArray(nextSources[0])) {
    nextSources = nextSources[0];
  }
  return function(source) {
    return source.lift(new OnErrorResumeNextOperator(nextSources));
  };
}
var OnErrorResumeNextOperator, OnErrorResumeNextSubscriber;
var init_onErrorResumeNext = __esm({
  "node_modules/rxjs/_esm5/internal/operators/onErrorResumeNext.js"() {
    init_tslib_es6();
    init_from();
    init_isArray();
    init_innerSubscribe();
    OnErrorResumeNextOperator = (function() {
      function OnErrorResumeNextOperator2(nextSources) {
        this.nextSources = nextSources;
      }
      OnErrorResumeNextOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new OnErrorResumeNextSubscriber(subscriber, this.nextSources));
      };
      return OnErrorResumeNextOperator2;
    })();
    OnErrorResumeNextSubscriber = (function(_super) {
      __extends(OnErrorResumeNextSubscriber2, _super);
      function OnErrorResumeNextSubscriber2(destination, nextSources) {
        var _this = _super.call(this, destination) || this;
        _this.destination = destination;
        _this.nextSources = nextSources;
        return _this;
      }
      OnErrorResumeNextSubscriber2.prototype.notifyError = function() {
        this.subscribeToNextSource();
      };
      OnErrorResumeNextSubscriber2.prototype.notifyComplete = function() {
        this.subscribeToNextSource();
      };
      OnErrorResumeNextSubscriber2.prototype._error = function(err) {
        this.subscribeToNextSource();
        this.unsubscribe();
      };
      OnErrorResumeNextSubscriber2.prototype._complete = function() {
        this.subscribeToNextSource();
        this.unsubscribe();
      };
      OnErrorResumeNextSubscriber2.prototype.subscribeToNextSource = function() {
        var next = this.nextSources.shift();
        if (!!next) {
          var innerSubscriber = new SimpleInnerSubscriber(this);
          var destination = this.destination;
          destination.add(innerSubscriber);
          var innerSubscription = innerSubscribe(next, innerSubscriber);
          if (innerSubscription !== innerSubscriber) {
            destination.add(innerSubscription);
          }
        } else {
          this.destination.complete();
        }
      };
      return OnErrorResumeNextSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/pairwise.js
function pairwise() {
  return function(source) {
    return source.lift(new PairwiseOperator());
  };
}
var PairwiseOperator, PairwiseSubscriber;
var init_pairwise = __esm({
  "node_modules/rxjs/_esm5/internal/operators/pairwise.js"() {
    init_tslib_es6();
    init_Subscriber();
    PairwiseOperator = (function() {
      function PairwiseOperator2() {
      }
      PairwiseOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new PairwiseSubscriber(subscriber));
      };
      return PairwiseOperator2;
    })();
    PairwiseSubscriber = (function(_super) {
      __extends(PairwiseSubscriber2, _super);
      function PairwiseSubscriber2(destination) {
        var _this = _super.call(this, destination) || this;
        _this.hasPrev = false;
        return _this;
      }
      PairwiseSubscriber2.prototype._next = function(value) {
        var pair;
        if (this.hasPrev) {
          pair = [this.prev, value];
        } else {
          this.hasPrev = true;
        }
        this.prev = value;
        if (pair) {
          this.destination.next(pair);
        }
      };
      return PairwiseSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/partition.js
function partition(predicate, thisArg) {
  return function(source) {
    return [
      filter(predicate, thisArg)(source),
      filter(not(predicate, thisArg))(source)
    ];
  };
}
var init_partition = __esm({
  "node_modules/rxjs/_esm5/internal/operators/partition.js"() {
    init_not();
    init_filter();
  }
});

// node_modules/rxjs/_esm5/internal/operators/pluck.js
function pluck() {
  var properties = [];
  for (var _i = 0; _i < arguments.length; _i++) {
    properties[_i] = arguments[_i];
  }
  var length = properties.length;
  if (length === 0) {
    throw new Error("list of properties cannot be empty.");
  }
  return function(source) {
    return map(plucker(properties, length))(source);
  };
}
function plucker(props, length) {
  var mapper = function(x) {
    var currentProp = x;
    for (var i = 0; i < length; i++) {
      var p = currentProp != null ? currentProp[props[i]] : void 0;
      if (p !== void 0) {
        currentProp = p;
      } else {
        return void 0;
      }
    }
    return currentProp;
  };
  return mapper;
}
var init_pluck = __esm({
  "node_modules/rxjs/_esm5/internal/operators/pluck.js"() {
    init_map();
  }
});

// node_modules/rxjs/_esm5/internal/operators/publish.js
function publish(selector) {
  return selector ? multicast(function() {
    return new Subject();
  }, selector) : multicast(new Subject());
}
var init_publish = __esm({
  "node_modules/rxjs/_esm5/internal/operators/publish.js"() {
    init_Subject();
    init_multicast();
  }
});

// node_modules/rxjs/_esm5/internal/operators/publishBehavior.js
function publishBehavior(value) {
  return function(source) {
    return multicast(new BehaviorSubject(value))(source);
  };
}
var init_publishBehavior = __esm({
  "node_modules/rxjs/_esm5/internal/operators/publishBehavior.js"() {
    init_BehaviorSubject();
    init_multicast();
  }
});

// node_modules/rxjs/_esm5/internal/operators/publishLast.js
function publishLast() {
  return function(source) {
    return multicast(new AsyncSubject())(source);
  };
}
var init_publishLast = __esm({
  "node_modules/rxjs/_esm5/internal/operators/publishLast.js"() {
    init_AsyncSubject();
    init_multicast();
  }
});

// node_modules/rxjs/_esm5/internal/operators/publishReplay.js
function publishReplay(bufferSize, windowTime2, selectorOrScheduler, scheduler) {
  if (selectorOrScheduler && typeof selectorOrScheduler !== "function") {
    scheduler = selectorOrScheduler;
  }
  var selector = typeof selectorOrScheduler === "function" ? selectorOrScheduler : void 0;
  var subject = new ReplaySubject(bufferSize, windowTime2, scheduler);
  return function(source) {
    return multicast(function() {
      return subject;
    }, selector)(source);
  };
}
var init_publishReplay = __esm({
  "node_modules/rxjs/_esm5/internal/operators/publishReplay.js"() {
    init_ReplaySubject();
    init_multicast();
  }
});

// node_modules/rxjs/_esm5/internal/operators/race.js
function race2() {
  var observables = [];
  for (var _i = 0; _i < arguments.length; _i++) {
    observables[_i] = arguments[_i];
  }
  return function raceOperatorFunction(source) {
    if (observables.length === 1 && isArray(observables[0])) {
      observables = observables[0];
    }
    return source.lift.call(race.apply(void 0, [source].concat(observables)));
  };
}
var init_race2 = __esm({
  "node_modules/rxjs/_esm5/internal/operators/race.js"() {
    init_isArray();
    init_race();
  }
});

// node_modules/rxjs/_esm5/internal/operators/repeat.js
function repeat(count2) {
  if (count2 === void 0) {
    count2 = -1;
  }
  return function(source) {
    if (count2 === 0) {
      return empty();
    } else if (count2 < 0) {
      return source.lift(new RepeatOperator(-1, source));
    } else {
      return source.lift(new RepeatOperator(count2 - 1, source));
    }
  };
}
var RepeatOperator, RepeatSubscriber;
var init_repeat = __esm({
  "node_modules/rxjs/_esm5/internal/operators/repeat.js"() {
    init_tslib_es6();
    init_Subscriber();
    init_empty();
    RepeatOperator = (function() {
      function RepeatOperator2(count2, source) {
        this.count = count2;
        this.source = source;
      }
      RepeatOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new RepeatSubscriber(subscriber, this.count, this.source));
      };
      return RepeatOperator2;
    })();
    RepeatSubscriber = (function(_super) {
      __extends(RepeatSubscriber2, _super);
      function RepeatSubscriber2(destination, count2, source) {
        var _this = _super.call(this, destination) || this;
        _this.count = count2;
        _this.source = source;
        return _this;
      }
      RepeatSubscriber2.prototype.complete = function() {
        if (!this.isStopped) {
          var _a = this, source = _a.source, count2 = _a.count;
          if (count2 === 0) {
            return _super.prototype.complete.call(this);
          } else if (count2 > -1) {
            this.count = count2 - 1;
          }
          source.subscribe(this._unsubscribeAndRecycle());
        }
      };
      return RepeatSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/repeatWhen.js
function repeatWhen(notifier) {
  return function(source) {
    return source.lift(new RepeatWhenOperator(notifier));
  };
}
var RepeatWhenOperator, RepeatWhenSubscriber;
var init_repeatWhen = __esm({
  "node_modules/rxjs/_esm5/internal/operators/repeatWhen.js"() {
    init_tslib_es6();
    init_Subject();
    init_innerSubscribe();
    RepeatWhenOperator = (function() {
      function RepeatWhenOperator2(notifier) {
        this.notifier = notifier;
      }
      RepeatWhenOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new RepeatWhenSubscriber(subscriber, this.notifier, source));
      };
      return RepeatWhenOperator2;
    })();
    RepeatWhenSubscriber = (function(_super) {
      __extends(RepeatWhenSubscriber2, _super);
      function RepeatWhenSubscriber2(destination, notifier, source) {
        var _this = _super.call(this, destination) || this;
        _this.notifier = notifier;
        _this.source = source;
        _this.sourceIsBeingSubscribedTo = true;
        return _this;
      }
      RepeatWhenSubscriber2.prototype.notifyNext = function() {
        this.sourceIsBeingSubscribedTo = true;
        this.source.subscribe(this);
      };
      RepeatWhenSubscriber2.prototype.notifyComplete = function() {
        if (this.sourceIsBeingSubscribedTo === false) {
          return _super.prototype.complete.call(this);
        }
      };
      RepeatWhenSubscriber2.prototype.complete = function() {
        this.sourceIsBeingSubscribedTo = false;
        if (!this.isStopped) {
          if (!this.retries) {
            this.subscribeToRetries();
          }
          if (!this.retriesSubscription || this.retriesSubscription.closed) {
            return _super.prototype.complete.call(this);
          }
          this._unsubscribeAndRecycle();
          this.notifications.next(void 0);
        }
      };
      RepeatWhenSubscriber2.prototype._unsubscribe = function() {
        var _a = this, notifications = _a.notifications, retriesSubscription = _a.retriesSubscription;
        if (notifications) {
          notifications.unsubscribe();
          this.notifications = void 0;
        }
        if (retriesSubscription) {
          retriesSubscription.unsubscribe();
          this.retriesSubscription = void 0;
        }
        this.retries = void 0;
      };
      RepeatWhenSubscriber2.prototype._unsubscribeAndRecycle = function() {
        var _unsubscribe = this._unsubscribe;
        this._unsubscribe = null;
        _super.prototype._unsubscribeAndRecycle.call(this);
        this._unsubscribe = _unsubscribe;
        return this;
      };
      RepeatWhenSubscriber2.prototype.subscribeToRetries = function() {
        this.notifications = new Subject();
        var retries;
        try {
          var notifier = this.notifier;
          retries = notifier(this.notifications);
        } catch (e) {
          return _super.prototype.complete.call(this);
        }
        this.retries = retries;
        this.retriesSubscription = innerSubscribe(retries, new SimpleInnerSubscriber(this));
      };
      return RepeatWhenSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/retry.js
function retry(count2) {
  if (count2 === void 0) {
    count2 = -1;
  }
  return function(source) {
    return source.lift(new RetryOperator(count2, source));
  };
}
var RetryOperator, RetrySubscriber;
var init_retry = __esm({
  "node_modules/rxjs/_esm5/internal/operators/retry.js"() {
    init_tslib_es6();
    init_Subscriber();
    RetryOperator = (function() {
      function RetryOperator2(count2, source) {
        this.count = count2;
        this.source = source;
      }
      RetryOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new RetrySubscriber(subscriber, this.count, this.source));
      };
      return RetryOperator2;
    })();
    RetrySubscriber = (function(_super) {
      __extends(RetrySubscriber2, _super);
      function RetrySubscriber2(destination, count2, source) {
        var _this = _super.call(this, destination) || this;
        _this.count = count2;
        _this.source = source;
        return _this;
      }
      RetrySubscriber2.prototype.error = function(err) {
        if (!this.isStopped) {
          var _a = this, source = _a.source, count2 = _a.count;
          if (count2 === 0) {
            return _super.prototype.error.call(this, err);
          } else if (count2 > -1) {
            this.count = count2 - 1;
          }
          source.subscribe(this._unsubscribeAndRecycle());
        }
      };
      return RetrySubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/retryWhen.js
function retryWhen(notifier) {
  return function(source) {
    return source.lift(new RetryWhenOperator(notifier, source));
  };
}
var RetryWhenOperator, RetryWhenSubscriber;
var init_retryWhen = __esm({
  "node_modules/rxjs/_esm5/internal/operators/retryWhen.js"() {
    init_tslib_es6();
    init_Subject();
    init_innerSubscribe();
    RetryWhenOperator = (function() {
      function RetryWhenOperator2(notifier, source) {
        this.notifier = notifier;
        this.source = source;
      }
      RetryWhenOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new RetryWhenSubscriber(subscriber, this.notifier, this.source));
      };
      return RetryWhenOperator2;
    })();
    RetryWhenSubscriber = (function(_super) {
      __extends(RetryWhenSubscriber2, _super);
      function RetryWhenSubscriber2(destination, notifier, source) {
        var _this = _super.call(this, destination) || this;
        _this.notifier = notifier;
        _this.source = source;
        return _this;
      }
      RetryWhenSubscriber2.prototype.error = function(err) {
        if (!this.isStopped) {
          var errors = this.errors;
          var retries = this.retries;
          var retriesSubscription = this.retriesSubscription;
          if (!retries) {
            errors = new Subject();
            try {
              var notifier = this.notifier;
              retries = notifier(errors);
            } catch (e) {
              return _super.prototype.error.call(this, e);
            }
            retriesSubscription = innerSubscribe(retries, new SimpleInnerSubscriber(this));
          } else {
            this.errors = void 0;
            this.retriesSubscription = void 0;
          }
          this._unsubscribeAndRecycle();
          this.errors = errors;
          this.retries = retries;
          this.retriesSubscription = retriesSubscription;
          errors.next(err);
        }
      };
      RetryWhenSubscriber2.prototype._unsubscribe = function() {
        var _a = this, errors = _a.errors, retriesSubscription = _a.retriesSubscription;
        if (errors) {
          errors.unsubscribe();
          this.errors = void 0;
        }
        if (retriesSubscription) {
          retriesSubscription.unsubscribe();
          this.retriesSubscription = void 0;
        }
        this.retries = void 0;
      };
      RetryWhenSubscriber2.prototype.notifyNext = function() {
        var _unsubscribe = this._unsubscribe;
        this._unsubscribe = null;
        this._unsubscribeAndRecycle();
        this._unsubscribe = _unsubscribe;
        this.source.subscribe(this);
      };
      return RetryWhenSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/sample.js
function sample(notifier) {
  return function(source) {
    return source.lift(new SampleOperator(notifier));
  };
}
var SampleOperator, SampleSubscriber;
var init_sample = __esm({
  "node_modules/rxjs/_esm5/internal/operators/sample.js"() {
    init_tslib_es6();
    init_innerSubscribe();
    SampleOperator = (function() {
      function SampleOperator2(notifier) {
        this.notifier = notifier;
      }
      SampleOperator2.prototype.call = function(subscriber, source) {
        var sampleSubscriber = new SampleSubscriber(subscriber);
        var subscription = source.subscribe(sampleSubscriber);
        subscription.add(innerSubscribe(this.notifier, new SimpleInnerSubscriber(sampleSubscriber)));
        return subscription;
      };
      return SampleOperator2;
    })();
    SampleSubscriber = (function(_super) {
      __extends(SampleSubscriber2, _super);
      function SampleSubscriber2() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.hasValue = false;
        return _this;
      }
      SampleSubscriber2.prototype._next = function(value) {
        this.value = value;
        this.hasValue = true;
      };
      SampleSubscriber2.prototype.notifyNext = function() {
        this.emitValue();
      };
      SampleSubscriber2.prototype.notifyComplete = function() {
        this.emitValue();
      };
      SampleSubscriber2.prototype.emitValue = function() {
        if (this.hasValue) {
          this.hasValue = false;
          this.destination.next(this.value);
        }
      };
      return SampleSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/sampleTime.js
function sampleTime(period, scheduler) {
  if (scheduler === void 0) {
    scheduler = async;
  }
  return function(source) {
    return source.lift(new SampleTimeOperator(period, scheduler));
  };
}
function dispatchNotification(state) {
  var subscriber = state.subscriber, period = state.period;
  subscriber.notifyNext();
  this.schedule(state, period);
}
var SampleTimeOperator, SampleTimeSubscriber;
var init_sampleTime = __esm({
  "node_modules/rxjs/_esm5/internal/operators/sampleTime.js"() {
    init_tslib_es6();
    init_Subscriber();
    init_async();
    SampleTimeOperator = (function() {
      function SampleTimeOperator2(period, scheduler) {
        this.period = period;
        this.scheduler = scheduler;
      }
      SampleTimeOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new SampleTimeSubscriber(subscriber, this.period, this.scheduler));
      };
      return SampleTimeOperator2;
    })();
    SampleTimeSubscriber = (function(_super) {
      __extends(SampleTimeSubscriber2, _super);
      function SampleTimeSubscriber2(destination, period, scheduler) {
        var _this = _super.call(this, destination) || this;
        _this.period = period;
        _this.scheduler = scheduler;
        _this.hasValue = false;
        _this.add(scheduler.schedule(dispatchNotification, period, { subscriber: _this, period }));
        return _this;
      }
      SampleTimeSubscriber2.prototype._next = function(value) {
        this.lastValue = value;
        this.hasValue = true;
      };
      SampleTimeSubscriber2.prototype.notifyNext = function() {
        if (this.hasValue) {
          this.hasValue = false;
          this.destination.next(this.lastValue);
        }
      };
      return SampleTimeSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/sequenceEqual.js
function sequenceEqual(compareTo, comparator) {
  return function(source) {
    return source.lift(new SequenceEqualOperator(compareTo, comparator));
  };
}
var SequenceEqualOperator, SequenceEqualSubscriber, SequenceEqualCompareToSubscriber;
var init_sequenceEqual = __esm({
  "node_modules/rxjs/_esm5/internal/operators/sequenceEqual.js"() {
    init_tslib_es6();
    init_Subscriber();
    SequenceEqualOperator = (function() {
      function SequenceEqualOperator2(compareTo, comparator) {
        this.compareTo = compareTo;
        this.comparator = comparator;
      }
      SequenceEqualOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new SequenceEqualSubscriber(subscriber, this.compareTo, this.comparator));
      };
      return SequenceEqualOperator2;
    })();
    SequenceEqualSubscriber = (function(_super) {
      __extends(SequenceEqualSubscriber2, _super);
      function SequenceEqualSubscriber2(destination, compareTo, comparator) {
        var _this = _super.call(this, destination) || this;
        _this.compareTo = compareTo;
        _this.comparator = comparator;
        _this._a = [];
        _this._b = [];
        _this._oneComplete = false;
        _this.destination.add(compareTo.subscribe(new SequenceEqualCompareToSubscriber(destination, _this)));
        return _this;
      }
      SequenceEqualSubscriber2.prototype._next = function(value) {
        if (this._oneComplete && this._b.length === 0) {
          this.emit(false);
        } else {
          this._a.push(value);
          this.checkValues();
        }
      };
      SequenceEqualSubscriber2.prototype._complete = function() {
        if (this._oneComplete) {
          this.emit(this._a.length === 0 && this._b.length === 0);
        } else {
          this._oneComplete = true;
        }
        this.unsubscribe();
      };
      SequenceEqualSubscriber2.prototype.checkValues = function() {
        var _c = this, _a = _c._a, _b = _c._b, comparator = _c.comparator;
        while (_a.length > 0 && _b.length > 0) {
          var a = _a.shift();
          var b = _b.shift();
          var areEqual = false;
          try {
            areEqual = comparator ? comparator(a, b) : a === b;
          } catch (e) {
            this.destination.error(e);
          }
          if (!areEqual) {
            this.emit(false);
          }
        }
      };
      SequenceEqualSubscriber2.prototype.emit = function(value) {
        var destination = this.destination;
        destination.next(value);
        destination.complete();
      };
      SequenceEqualSubscriber2.prototype.nextB = function(value) {
        if (this._oneComplete && this._a.length === 0) {
          this.emit(false);
        } else {
          this._b.push(value);
          this.checkValues();
        }
      };
      SequenceEqualSubscriber2.prototype.completeB = function() {
        if (this._oneComplete) {
          this.emit(this._a.length === 0 && this._b.length === 0);
        } else {
          this._oneComplete = true;
        }
      };
      return SequenceEqualSubscriber2;
    })(Subscriber);
    SequenceEqualCompareToSubscriber = (function(_super) {
      __extends(SequenceEqualCompareToSubscriber2, _super);
      function SequenceEqualCompareToSubscriber2(destination, parent) {
        var _this = _super.call(this, destination) || this;
        _this.parent = parent;
        return _this;
      }
      SequenceEqualCompareToSubscriber2.prototype._next = function(value) {
        this.parent.nextB(value);
      };
      SequenceEqualCompareToSubscriber2.prototype._error = function(err) {
        this.parent.error(err);
        this.unsubscribe();
      };
      SequenceEqualCompareToSubscriber2.prototype._complete = function() {
        this.parent.completeB();
        this.unsubscribe();
      };
      return SequenceEqualCompareToSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/share.js
function shareSubjectFactory() {
  return new Subject();
}
function share() {
  return function(source) {
    return refCount()(multicast(shareSubjectFactory)(source));
  };
}
var init_share = __esm({
  "node_modules/rxjs/_esm5/internal/operators/share.js"() {
    init_multicast();
    init_refCount();
    init_Subject();
  }
});

// node_modules/rxjs/_esm5/internal/operators/shareReplay.js
function shareReplay(configOrBufferSize, windowTime2, scheduler) {
  var config;
  if (configOrBufferSize && typeof configOrBufferSize === "object") {
    config = configOrBufferSize;
  } else {
    config = {
      bufferSize: configOrBufferSize,
      windowTime: windowTime2,
      refCount: false,
      scheduler
    };
  }
  return function(source) {
    return source.lift(shareReplayOperator(config));
  };
}
function shareReplayOperator(_a) {
  var _b = _a.bufferSize, bufferSize = _b === void 0 ? Number.POSITIVE_INFINITY : _b, _c = _a.windowTime, windowTime2 = _c === void 0 ? Number.POSITIVE_INFINITY : _c, useRefCount = _a.refCount, scheduler = _a.scheduler;
  var subject;
  var refCount2 = 0;
  var subscription;
  var hasError = false;
  var isComplete = false;
  return function shareReplayOperation(source) {
    refCount2++;
    var innerSub;
    if (!subject || hasError) {
      hasError = false;
      subject = new ReplaySubject(bufferSize, windowTime2, scheduler);
      innerSub = subject.subscribe(this);
      subscription = source.subscribe({
        next: function(value) {
          subject.next(value);
        },
        error: function(err) {
          hasError = true;
          subject.error(err);
        },
        complete: function() {
          isComplete = true;
          subscription = void 0;
          subject.complete();
        }
      });
    } else {
      innerSub = subject.subscribe(this);
    }
    this.add(function() {
      refCount2--;
      innerSub.unsubscribe();
      if (subscription && !isComplete && useRefCount && refCount2 === 0) {
        subscription.unsubscribe();
        subscription = void 0;
        subject = void 0;
      }
    });
  };
}
var init_shareReplay = __esm({
  "node_modules/rxjs/_esm5/internal/operators/shareReplay.js"() {
    init_ReplaySubject();
  }
});

// node_modules/rxjs/_esm5/internal/operators/single.js
function single(predicate) {
  return function(source) {
    return source.lift(new SingleOperator(predicate, source));
  };
}
var SingleOperator, SingleSubscriber;
var init_single = __esm({
  "node_modules/rxjs/_esm5/internal/operators/single.js"() {
    init_tslib_es6();
    init_Subscriber();
    init_EmptyError();
    SingleOperator = (function() {
      function SingleOperator2(predicate, source) {
        this.predicate = predicate;
        this.source = source;
      }
      SingleOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new SingleSubscriber(subscriber, this.predicate, this.source));
      };
      return SingleOperator2;
    })();
    SingleSubscriber = (function(_super) {
      __extends(SingleSubscriber2, _super);
      function SingleSubscriber2(destination, predicate, source) {
        var _this = _super.call(this, destination) || this;
        _this.predicate = predicate;
        _this.source = source;
        _this.seenValue = false;
        _this.index = 0;
        return _this;
      }
      SingleSubscriber2.prototype.applySingleValue = function(value) {
        if (this.seenValue) {
          this.destination.error("Sequence contains more than one element");
        } else {
          this.seenValue = true;
          this.singleValue = value;
        }
      };
      SingleSubscriber2.prototype._next = function(value) {
        var index = this.index++;
        if (this.predicate) {
          this.tryNext(value, index);
        } else {
          this.applySingleValue(value);
        }
      };
      SingleSubscriber2.prototype.tryNext = function(value, index) {
        try {
          if (this.predicate(value, index, this.source)) {
            this.applySingleValue(value);
          }
        } catch (err) {
          this.destination.error(err);
        }
      };
      SingleSubscriber2.prototype._complete = function() {
        var destination = this.destination;
        if (this.index > 0) {
          destination.next(this.seenValue ? this.singleValue : void 0);
          destination.complete();
        } else {
          destination.error(new EmptyError());
        }
      };
      return SingleSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/skip.js
function skip(count2) {
  return function(source) {
    return source.lift(new SkipOperator(count2));
  };
}
var SkipOperator, SkipSubscriber;
var init_skip = __esm({
  "node_modules/rxjs/_esm5/internal/operators/skip.js"() {
    init_tslib_es6();
    init_Subscriber();
    SkipOperator = (function() {
      function SkipOperator2(total) {
        this.total = total;
      }
      SkipOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new SkipSubscriber(subscriber, this.total));
      };
      return SkipOperator2;
    })();
    SkipSubscriber = (function(_super) {
      __extends(SkipSubscriber2, _super);
      function SkipSubscriber2(destination, total) {
        var _this = _super.call(this, destination) || this;
        _this.total = total;
        _this.count = 0;
        return _this;
      }
      SkipSubscriber2.prototype._next = function(x) {
        if (++this.count > this.total) {
          this.destination.next(x);
        }
      };
      return SkipSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/skipLast.js
function skipLast(count2) {
  return function(source) {
    return source.lift(new SkipLastOperator(count2));
  };
}
var SkipLastOperator, SkipLastSubscriber;
var init_skipLast = __esm({
  "node_modules/rxjs/_esm5/internal/operators/skipLast.js"() {
    init_tslib_es6();
    init_Subscriber();
    init_ArgumentOutOfRangeError();
    SkipLastOperator = (function() {
      function SkipLastOperator2(_skipCount) {
        this._skipCount = _skipCount;
        if (this._skipCount < 0) {
          throw new ArgumentOutOfRangeError();
        }
      }
      SkipLastOperator2.prototype.call = function(subscriber, source) {
        if (this._skipCount === 0) {
          return source.subscribe(new Subscriber(subscriber));
        } else {
          return source.subscribe(new SkipLastSubscriber(subscriber, this._skipCount));
        }
      };
      return SkipLastOperator2;
    })();
    SkipLastSubscriber = (function(_super) {
      __extends(SkipLastSubscriber2, _super);
      function SkipLastSubscriber2(destination, _skipCount) {
        var _this = _super.call(this, destination) || this;
        _this._skipCount = _skipCount;
        _this._count = 0;
        _this._ring = new Array(_skipCount);
        return _this;
      }
      SkipLastSubscriber2.prototype._next = function(value) {
        var skipCount = this._skipCount;
        var count2 = this._count++;
        if (count2 < skipCount) {
          this._ring[count2] = value;
        } else {
          var currentIndex = count2 % skipCount;
          var ring = this._ring;
          var oldValue = ring[currentIndex];
          ring[currentIndex] = value;
          this.destination.next(oldValue);
        }
      };
      return SkipLastSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/skipUntil.js
function skipUntil(notifier) {
  return function(source) {
    return source.lift(new SkipUntilOperator(notifier));
  };
}
var SkipUntilOperator, SkipUntilSubscriber;
var init_skipUntil = __esm({
  "node_modules/rxjs/_esm5/internal/operators/skipUntil.js"() {
    init_tslib_es6();
    init_innerSubscribe();
    SkipUntilOperator = (function() {
      function SkipUntilOperator2(notifier) {
        this.notifier = notifier;
      }
      SkipUntilOperator2.prototype.call = function(destination, source) {
        return source.subscribe(new SkipUntilSubscriber(destination, this.notifier));
      };
      return SkipUntilOperator2;
    })();
    SkipUntilSubscriber = (function(_super) {
      __extends(SkipUntilSubscriber2, _super);
      function SkipUntilSubscriber2(destination, notifier) {
        var _this = _super.call(this, destination) || this;
        _this.hasValue = false;
        var innerSubscriber = new SimpleInnerSubscriber(_this);
        _this.add(innerSubscriber);
        _this.innerSubscription = innerSubscriber;
        var innerSubscription = innerSubscribe(notifier, innerSubscriber);
        if (innerSubscription !== innerSubscriber) {
          _this.add(innerSubscription);
          _this.innerSubscription = innerSubscription;
        }
        return _this;
      }
      SkipUntilSubscriber2.prototype._next = function(value) {
        if (this.hasValue) {
          _super.prototype._next.call(this, value);
        }
      };
      SkipUntilSubscriber2.prototype.notifyNext = function() {
        this.hasValue = true;
        if (this.innerSubscription) {
          this.innerSubscription.unsubscribe();
        }
      };
      SkipUntilSubscriber2.prototype.notifyComplete = function() {
      };
      return SkipUntilSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/skipWhile.js
function skipWhile(predicate) {
  return function(source) {
    return source.lift(new SkipWhileOperator(predicate));
  };
}
var SkipWhileOperator, SkipWhileSubscriber;
var init_skipWhile = __esm({
  "node_modules/rxjs/_esm5/internal/operators/skipWhile.js"() {
    init_tslib_es6();
    init_Subscriber();
    SkipWhileOperator = (function() {
      function SkipWhileOperator2(predicate) {
        this.predicate = predicate;
      }
      SkipWhileOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new SkipWhileSubscriber(subscriber, this.predicate));
      };
      return SkipWhileOperator2;
    })();
    SkipWhileSubscriber = (function(_super) {
      __extends(SkipWhileSubscriber2, _super);
      function SkipWhileSubscriber2(destination, predicate) {
        var _this = _super.call(this, destination) || this;
        _this.predicate = predicate;
        _this.skipping = true;
        _this.index = 0;
        return _this;
      }
      SkipWhileSubscriber2.prototype._next = function(value) {
        var destination = this.destination;
        if (this.skipping) {
          this.tryCallPredicate(value);
        }
        if (!this.skipping) {
          destination.next(value);
        }
      };
      SkipWhileSubscriber2.prototype.tryCallPredicate = function(value) {
        try {
          var result = this.predicate(value, this.index++);
          this.skipping = Boolean(result);
        } catch (err) {
          this.destination.error(err);
        }
      };
      return SkipWhileSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/startWith.js
function startWith() {
  var array = [];
  for (var _i = 0; _i < arguments.length; _i++) {
    array[_i] = arguments[_i];
  }
  var scheduler = array[array.length - 1];
  if (isScheduler(scheduler)) {
    array.pop();
    return function(source) {
      return concat(array, source, scheduler);
    };
  } else {
    return function(source) {
      return concat(array, source);
    };
  }
}
var init_startWith = __esm({
  "node_modules/rxjs/_esm5/internal/operators/startWith.js"() {
    init_concat();
    init_isScheduler();
  }
});

// node_modules/rxjs/_esm5/internal/observable/SubscribeOnObservable.js
var SubscribeOnObservable;
var init_SubscribeOnObservable = __esm({
  "node_modules/rxjs/_esm5/internal/observable/SubscribeOnObservable.js"() {
    init_tslib_es6();
    init_Observable();
    init_asap();
    init_isNumeric();
    SubscribeOnObservable = (function(_super) {
      __extends(SubscribeOnObservable2, _super);
      function SubscribeOnObservable2(source, delayTime, scheduler) {
        if (delayTime === void 0) {
          delayTime = 0;
        }
        if (scheduler === void 0) {
          scheduler = asap;
        }
        var _this = _super.call(this) || this;
        _this.source = source;
        _this.delayTime = delayTime;
        _this.scheduler = scheduler;
        if (!isNumeric(delayTime) || delayTime < 0) {
          _this.delayTime = 0;
        }
        if (!scheduler || typeof scheduler.schedule !== "function") {
          _this.scheduler = asap;
        }
        return _this;
      }
      SubscribeOnObservable2.create = function(source, delay2, scheduler) {
        if (delay2 === void 0) {
          delay2 = 0;
        }
        if (scheduler === void 0) {
          scheduler = asap;
        }
        return new SubscribeOnObservable2(source, delay2, scheduler);
      };
      SubscribeOnObservable2.dispatch = function(arg) {
        var source = arg.source, subscriber = arg.subscriber;
        return this.add(source.subscribe(subscriber));
      };
      SubscribeOnObservable2.prototype._subscribe = function(subscriber) {
        var delay2 = this.delayTime;
        var source = this.source;
        var scheduler = this.scheduler;
        return scheduler.schedule(SubscribeOnObservable2.dispatch, delay2, {
          source,
          subscriber
        });
      };
      return SubscribeOnObservable2;
    })(Observable);
  }
});

// node_modules/rxjs/_esm5/internal/operators/subscribeOn.js
function subscribeOn(scheduler, delay2) {
  if (delay2 === void 0) {
    delay2 = 0;
  }
  return function subscribeOnOperatorFunction(source) {
    return source.lift(new SubscribeOnOperator(scheduler, delay2));
  };
}
var SubscribeOnOperator;
var init_subscribeOn = __esm({
  "node_modules/rxjs/_esm5/internal/operators/subscribeOn.js"() {
    init_SubscribeOnObservable();
    SubscribeOnOperator = (function() {
      function SubscribeOnOperator2(scheduler, delay2) {
        this.scheduler = scheduler;
        this.delay = delay2;
      }
      SubscribeOnOperator2.prototype.call = function(subscriber, source) {
        return new SubscribeOnObservable(source, this.delay, this.scheduler).subscribe(subscriber);
      };
      return SubscribeOnOperator2;
    })();
  }
});

// node_modules/rxjs/_esm5/internal/operators/switchMap.js
function switchMap(project, resultSelector) {
  if (typeof resultSelector === "function") {
    return function(source) {
      return source.pipe(switchMap(function(a, i) {
        return from(project(a, i)).pipe(map(function(b, ii) {
          return resultSelector(a, b, i, ii);
        }));
      }));
    };
  }
  return function(source) {
    return source.lift(new SwitchMapOperator(project));
  };
}
var SwitchMapOperator, SwitchMapSubscriber;
var init_switchMap = __esm({
  "node_modules/rxjs/_esm5/internal/operators/switchMap.js"() {
    init_tslib_es6();
    init_map();
    init_from();
    init_innerSubscribe();
    SwitchMapOperator = (function() {
      function SwitchMapOperator2(project) {
        this.project = project;
      }
      SwitchMapOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new SwitchMapSubscriber(subscriber, this.project));
      };
      return SwitchMapOperator2;
    })();
    SwitchMapSubscriber = (function(_super) {
      __extends(SwitchMapSubscriber2, _super);
      function SwitchMapSubscriber2(destination, project) {
        var _this = _super.call(this, destination) || this;
        _this.project = project;
        _this.index = 0;
        return _this;
      }
      SwitchMapSubscriber2.prototype._next = function(value) {
        var result;
        var index = this.index++;
        try {
          result = this.project(value, index);
        } catch (error) {
          this.destination.error(error);
          return;
        }
        this._innerSub(result);
      };
      SwitchMapSubscriber2.prototype._innerSub = function(result) {
        var innerSubscription = this.innerSubscription;
        if (innerSubscription) {
          innerSubscription.unsubscribe();
        }
        var innerSubscriber = new SimpleInnerSubscriber(this);
        var destination = this.destination;
        destination.add(innerSubscriber);
        this.innerSubscription = innerSubscribe(result, innerSubscriber);
        if (this.innerSubscription !== innerSubscriber) {
          destination.add(this.innerSubscription);
        }
      };
      SwitchMapSubscriber2.prototype._complete = function() {
        var innerSubscription = this.innerSubscription;
        if (!innerSubscription || innerSubscription.closed) {
          _super.prototype._complete.call(this);
        }
        this.unsubscribe();
      };
      SwitchMapSubscriber2.prototype._unsubscribe = function() {
        this.innerSubscription = void 0;
      };
      SwitchMapSubscriber2.prototype.notifyComplete = function() {
        this.innerSubscription = void 0;
        if (this.isStopped) {
          _super.prototype._complete.call(this);
        }
      };
      SwitchMapSubscriber2.prototype.notifyNext = function(innerValue) {
        this.destination.next(innerValue);
      };
      return SwitchMapSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/switchAll.js
function switchAll() {
  return switchMap(identity);
}
var init_switchAll = __esm({
  "node_modules/rxjs/_esm5/internal/operators/switchAll.js"() {
    init_switchMap();
    init_identity();
  }
});

// node_modules/rxjs/_esm5/internal/operators/switchMapTo.js
function switchMapTo(innerObservable, resultSelector) {
  return resultSelector ? switchMap(function() {
    return innerObservable;
  }, resultSelector) : switchMap(function() {
    return innerObservable;
  });
}
var init_switchMapTo = __esm({
  "node_modules/rxjs/_esm5/internal/operators/switchMapTo.js"() {
    init_switchMap();
  }
});

// node_modules/rxjs/_esm5/internal/operators/takeUntil.js
function takeUntil(notifier) {
  return function(source) {
    return source.lift(new TakeUntilOperator(notifier));
  };
}
var TakeUntilOperator, TakeUntilSubscriber;
var init_takeUntil = __esm({
  "node_modules/rxjs/_esm5/internal/operators/takeUntil.js"() {
    init_tslib_es6();
    init_innerSubscribe();
    TakeUntilOperator = (function() {
      function TakeUntilOperator2(notifier) {
        this.notifier = notifier;
      }
      TakeUntilOperator2.prototype.call = function(subscriber, source) {
        var takeUntilSubscriber = new TakeUntilSubscriber(subscriber);
        var notifierSubscription = innerSubscribe(this.notifier, new SimpleInnerSubscriber(takeUntilSubscriber));
        if (notifierSubscription && !takeUntilSubscriber.seenValue) {
          takeUntilSubscriber.add(notifierSubscription);
          return source.subscribe(takeUntilSubscriber);
        }
        return takeUntilSubscriber;
      };
      return TakeUntilOperator2;
    })();
    TakeUntilSubscriber = (function(_super) {
      __extends(TakeUntilSubscriber2, _super);
      function TakeUntilSubscriber2(destination) {
        var _this = _super.call(this, destination) || this;
        _this.seenValue = false;
        return _this;
      }
      TakeUntilSubscriber2.prototype.notifyNext = function() {
        this.seenValue = true;
        this.complete();
      };
      TakeUntilSubscriber2.prototype.notifyComplete = function() {
      };
      return TakeUntilSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/takeWhile.js
function takeWhile(predicate, inclusive) {
  if (inclusive === void 0) {
    inclusive = false;
  }
  return function(source) {
    return source.lift(new TakeWhileOperator(predicate, inclusive));
  };
}
var TakeWhileOperator, TakeWhileSubscriber;
var init_takeWhile = __esm({
  "node_modules/rxjs/_esm5/internal/operators/takeWhile.js"() {
    init_tslib_es6();
    init_Subscriber();
    TakeWhileOperator = (function() {
      function TakeWhileOperator2(predicate, inclusive) {
        this.predicate = predicate;
        this.inclusive = inclusive;
      }
      TakeWhileOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new TakeWhileSubscriber(subscriber, this.predicate, this.inclusive));
      };
      return TakeWhileOperator2;
    })();
    TakeWhileSubscriber = (function(_super) {
      __extends(TakeWhileSubscriber2, _super);
      function TakeWhileSubscriber2(destination, predicate, inclusive) {
        var _this = _super.call(this, destination) || this;
        _this.predicate = predicate;
        _this.inclusive = inclusive;
        _this.index = 0;
        return _this;
      }
      TakeWhileSubscriber2.prototype._next = function(value) {
        var destination = this.destination;
        var result;
        try {
          result = this.predicate(value, this.index++);
        } catch (err) {
          destination.error(err);
          return;
        }
        this.nextOrComplete(value, result);
      };
      TakeWhileSubscriber2.prototype.nextOrComplete = function(value, predicateResult) {
        var destination = this.destination;
        if (Boolean(predicateResult)) {
          destination.next(value);
        } else {
          if (this.inclusive) {
            destination.next(value);
          }
          destination.complete();
        }
      };
      return TakeWhileSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/tap.js
function tap(nextOrObserver, error, complete) {
  return function tapOperatorFunction(source) {
    return source.lift(new DoOperator(nextOrObserver, error, complete));
  };
}
var DoOperator, TapSubscriber;
var init_tap = __esm({
  "node_modules/rxjs/_esm5/internal/operators/tap.js"() {
    init_tslib_es6();
    init_Subscriber();
    init_noop();
    init_isFunction();
    DoOperator = (function() {
      function DoOperator2(nextOrObserver, error, complete) {
        this.nextOrObserver = nextOrObserver;
        this.error = error;
        this.complete = complete;
      }
      DoOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new TapSubscriber(subscriber, this.nextOrObserver, this.error, this.complete));
      };
      return DoOperator2;
    })();
    TapSubscriber = (function(_super) {
      __extends(TapSubscriber2, _super);
      function TapSubscriber2(destination, observerOrNext, error, complete) {
        var _this = _super.call(this, destination) || this;
        _this._tapNext = noop;
        _this._tapError = noop;
        _this._tapComplete = noop;
        _this._tapError = error || noop;
        _this._tapComplete = complete || noop;
        if (isFunction(observerOrNext)) {
          _this._context = _this;
          _this._tapNext = observerOrNext;
        } else if (observerOrNext) {
          _this._context = observerOrNext;
          _this._tapNext = observerOrNext.next || noop;
          _this._tapError = observerOrNext.error || noop;
          _this._tapComplete = observerOrNext.complete || noop;
        }
        return _this;
      }
      TapSubscriber2.prototype._next = function(value) {
        try {
          this._tapNext.call(this._context, value);
        } catch (err) {
          this.destination.error(err);
          return;
        }
        this.destination.next(value);
      };
      TapSubscriber2.prototype._error = function(err) {
        try {
          this._tapError.call(this._context, err);
        } catch (err2) {
          this.destination.error(err2);
          return;
        }
        this.destination.error(err);
      };
      TapSubscriber2.prototype._complete = function() {
        try {
          this._tapComplete.call(this._context);
        } catch (err) {
          this.destination.error(err);
          return;
        }
        return this.destination.complete();
      };
      return TapSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/throttle.js
function throttle(durationSelector, config) {
  if (config === void 0) {
    config = defaultThrottleConfig;
  }
  return function(source) {
    return source.lift(new ThrottleOperator(durationSelector, !!config.leading, !!config.trailing));
  };
}
var defaultThrottleConfig, ThrottleOperator, ThrottleSubscriber;
var init_throttle = __esm({
  "node_modules/rxjs/_esm5/internal/operators/throttle.js"() {
    init_tslib_es6();
    init_innerSubscribe();
    defaultThrottleConfig = {
      leading: true,
      trailing: false
    };
    ThrottleOperator = (function() {
      function ThrottleOperator2(durationSelector, leading, trailing) {
        this.durationSelector = durationSelector;
        this.leading = leading;
        this.trailing = trailing;
      }
      ThrottleOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new ThrottleSubscriber(subscriber, this.durationSelector, this.leading, this.trailing));
      };
      return ThrottleOperator2;
    })();
    ThrottleSubscriber = (function(_super) {
      __extends(ThrottleSubscriber2, _super);
      function ThrottleSubscriber2(destination, durationSelector, _leading, _trailing) {
        var _this = _super.call(this, destination) || this;
        _this.destination = destination;
        _this.durationSelector = durationSelector;
        _this._leading = _leading;
        _this._trailing = _trailing;
        _this._hasValue = false;
        return _this;
      }
      ThrottleSubscriber2.prototype._next = function(value) {
        this._hasValue = true;
        this._sendValue = value;
        if (!this._throttled) {
          if (this._leading) {
            this.send();
          } else {
            this.throttle(value);
          }
        }
      };
      ThrottleSubscriber2.prototype.send = function() {
        var _a = this, _hasValue = _a._hasValue, _sendValue = _a._sendValue;
        if (_hasValue) {
          this.destination.next(_sendValue);
          this.throttle(_sendValue);
        }
        this._hasValue = false;
        this._sendValue = void 0;
      };
      ThrottleSubscriber2.prototype.throttle = function(value) {
        var duration = this.tryDurationSelector(value);
        if (!!duration) {
          this.add(this._throttled = innerSubscribe(duration, new SimpleInnerSubscriber(this)));
        }
      };
      ThrottleSubscriber2.prototype.tryDurationSelector = function(value) {
        try {
          return this.durationSelector(value);
        } catch (err) {
          this.destination.error(err);
          return null;
        }
      };
      ThrottleSubscriber2.prototype.throttlingDone = function() {
        var _a = this, _throttled = _a._throttled, _trailing = _a._trailing;
        if (_throttled) {
          _throttled.unsubscribe();
        }
        this._throttled = void 0;
        if (_trailing) {
          this.send();
        }
      };
      ThrottleSubscriber2.prototype.notifyNext = function() {
        this.throttlingDone();
      };
      ThrottleSubscriber2.prototype.notifyComplete = function() {
        this.throttlingDone();
      };
      return ThrottleSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/throttleTime.js
function throttleTime(duration, scheduler, config) {
  if (scheduler === void 0) {
    scheduler = async;
  }
  if (config === void 0) {
    config = defaultThrottleConfig;
  }
  return function(source) {
    return source.lift(new ThrottleTimeOperator(duration, scheduler, config.leading, config.trailing));
  };
}
function dispatchNext2(arg) {
  var subscriber = arg.subscriber;
  subscriber.clearThrottle();
}
var ThrottleTimeOperator, ThrottleTimeSubscriber;
var init_throttleTime = __esm({
  "node_modules/rxjs/_esm5/internal/operators/throttleTime.js"() {
    init_tslib_es6();
    init_Subscriber();
    init_async();
    init_throttle();
    ThrottleTimeOperator = (function() {
      function ThrottleTimeOperator2(duration, scheduler, leading, trailing) {
        this.duration = duration;
        this.scheduler = scheduler;
        this.leading = leading;
        this.trailing = trailing;
      }
      ThrottleTimeOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new ThrottleTimeSubscriber(subscriber, this.duration, this.scheduler, this.leading, this.trailing));
      };
      return ThrottleTimeOperator2;
    })();
    ThrottleTimeSubscriber = (function(_super) {
      __extends(ThrottleTimeSubscriber2, _super);
      function ThrottleTimeSubscriber2(destination, duration, scheduler, leading, trailing) {
        var _this = _super.call(this, destination) || this;
        _this.duration = duration;
        _this.scheduler = scheduler;
        _this.leading = leading;
        _this.trailing = trailing;
        _this._hasTrailingValue = false;
        _this._trailingValue = null;
        return _this;
      }
      ThrottleTimeSubscriber2.prototype._next = function(value) {
        if (this.throttled) {
          if (this.trailing) {
            this._trailingValue = value;
            this._hasTrailingValue = true;
          }
        } else {
          this.add(this.throttled = this.scheduler.schedule(dispatchNext2, this.duration, { subscriber: this }));
          if (this.leading) {
            this.destination.next(value);
          } else if (this.trailing) {
            this._trailingValue = value;
            this._hasTrailingValue = true;
          }
        }
      };
      ThrottleTimeSubscriber2.prototype._complete = function() {
        if (this._hasTrailingValue) {
          this.destination.next(this._trailingValue);
          this.destination.complete();
        } else {
          this.destination.complete();
        }
      };
      ThrottleTimeSubscriber2.prototype.clearThrottle = function() {
        var throttled = this.throttled;
        if (throttled) {
          if (this.trailing && this._hasTrailingValue) {
            this.destination.next(this._trailingValue);
            this._trailingValue = null;
            this._hasTrailingValue = false;
          }
          throttled.unsubscribe();
          this.remove(throttled);
          this.throttled = null;
        }
      };
      return ThrottleTimeSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/timeInterval.js
function timeInterval(scheduler) {
  if (scheduler === void 0) {
    scheduler = async;
  }
  return function(source) {
    return defer(function() {
      return source.pipe(scan(function(_a, value) {
        var current = _a.current;
        return { value, current: scheduler.now(), last: current };
      }, { current: scheduler.now(), value: void 0, last: void 0 }), map(function(_a) {
        var current = _a.current, last2 = _a.last, value = _a.value;
        return new TimeInterval(value, current - last2);
      }));
    });
  };
}
var TimeInterval;
var init_timeInterval = __esm({
  "node_modules/rxjs/_esm5/internal/operators/timeInterval.js"() {
    init_async();
    init_scan();
    init_defer();
    init_map();
    TimeInterval = /* @__PURE__ */ (function() {
      function TimeInterval2(value, interval) {
        this.value = value;
        this.interval = interval;
      }
      return TimeInterval2;
    })();
  }
});

// node_modules/rxjs/_esm5/internal/operators/timeoutWith.js
function timeoutWith(due, withObservable, scheduler) {
  if (scheduler === void 0) {
    scheduler = async;
  }
  return function(source) {
    var absoluteTimeout = isDate(due);
    var waitFor = absoluteTimeout ? +due - scheduler.now() : Math.abs(due);
    return source.lift(new TimeoutWithOperator(waitFor, absoluteTimeout, withObservable, scheduler));
  };
}
var TimeoutWithOperator, TimeoutWithSubscriber;
var init_timeoutWith = __esm({
  "node_modules/rxjs/_esm5/internal/operators/timeoutWith.js"() {
    init_tslib_es6();
    init_async();
    init_isDate();
    init_innerSubscribe();
    TimeoutWithOperator = (function() {
      function TimeoutWithOperator2(waitFor, absoluteTimeout, withObservable, scheduler) {
        this.waitFor = waitFor;
        this.absoluteTimeout = absoluteTimeout;
        this.withObservable = withObservable;
        this.scheduler = scheduler;
      }
      TimeoutWithOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new TimeoutWithSubscriber(subscriber, this.absoluteTimeout, this.waitFor, this.withObservable, this.scheduler));
      };
      return TimeoutWithOperator2;
    })();
    TimeoutWithSubscriber = (function(_super) {
      __extends(TimeoutWithSubscriber2, _super);
      function TimeoutWithSubscriber2(destination, absoluteTimeout, waitFor, withObservable, scheduler) {
        var _this = _super.call(this, destination) || this;
        _this.absoluteTimeout = absoluteTimeout;
        _this.waitFor = waitFor;
        _this.withObservable = withObservable;
        _this.scheduler = scheduler;
        _this.scheduleTimeout();
        return _this;
      }
      TimeoutWithSubscriber2.dispatchTimeout = function(subscriber) {
        var withObservable = subscriber.withObservable;
        subscriber._unsubscribeAndRecycle();
        subscriber.add(innerSubscribe(withObservable, new SimpleInnerSubscriber(subscriber)));
      };
      TimeoutWithSubscriber2.prototype.scheduleTimeout = function() {
        var action = this.action;
        if (action) {
          this.action = action.schedule(this, this.waitFor);
        } else {
          this.add(this.action = this.scheduler.schedule(TimeoutWithSubscriber2.dispatchTimeout, this.waitFor, this));
        }
      };
      TimeoutWithSubscriber2.prototype._next = function(value) {
        if (!this.absoluteTimeout) {
          this.scheduleTimeout();
        }
        _super.prototype._next.call(this, value);
      };
      TimeoutWithSubscriber2.prototype._unsubscribe = function() {
        this.action = void 0;
        this.scheduler = null;
        this.withObservable = null;
      };
      return TimeoutWithSubscriber2;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/timeout.js
function timeout(due, scheduler) {
  if (scheduler === void 0) {
    scheduler = async;
  }
  return timeoutWith(due, throwError(new TimeoutError()), scheduler);
}
var init_timeout = __esm({
  "node_modules/rxjs/_esm5/internal/operators/timeout.js"() {
    init_async();
    init_TimeoutError();
    init_timeoutWith();
    init_throwError();
  }
});

// node_modules/rxjs/_esm5/internal/operators/timestamp.js
function timestamp(scheduler) {
  if (scheduler === void 0) {
    scheduler = async;
  }
  return map(function(value) {
    return new Timestamp(value, scheduler.now());
  });
}
var Timestamp;
var init_timestamp = __esm({
  "node_modules/rxjs/_esm5/internal/operators/timestamp.js"() {
    init_async();
    init_map();
    Timestamp = /* @__PURE__ */ (function() {
      function Timestamp2(value, timestamp2) {
        this.value = value;
        this.timestamp = timestamp2;
      }
      return Timestamp2;
    })();
  }
});

// node_modules/rxjs/_esm5/internal/operators/toArray.js
function toArrayReducer(arr, item, index) {
  if (index === 0) {
    return [item];
  }
  arr.push(item);
  return arr;
}
function toArray() {
  return reduce(toArrayReducer, []);
}
var init_toArray = __esm({
  "node_modules/rxjs/_esm5/internal/operators/toArray.js"() {
    init_reduce();
  }
});

// node_modules/rxjs/_esm5/internal/operators/window.js
function window(windowBoundaries) {
  return function windowOperatorFunction(source) {
    return source.lift(new WindowOperator(windowBoundaries));
  };
}
var WindowOperator, WindowSubscriber;
var init_window = __esm({
  "node_modules/rxjs/_esm5/internal/operators/window.js"() {
    init_tslib_es6();
    init_Subject();
    init_innerSubscribe();
    WindowOperator = (function() {
      function WindowOperator3(windowBoundaries) {
        this.windowBoundaries = windowBoundaries;
      }
      WindowOperator3.prototype.call = function(subscriber, source) {
        var windowSubscriber = new WindowSubscriber(subscriber);
        var sourceSubscription = source.subscribe(windowSubscriber);
        if (!sourceSubscription.closed) {
          windowSubscriber.add(innerSubscribe(this.windowBoundaries, new SimpleInnerSubscriber(windowSubscriber)));
        }
        return sourceSubscription;
      };
      return WindowOperator3;
    })();
    WindowSubscriber = (function(_super) {
      __extends(WindowSubscriber3, _super);
      function WindowSubscriber3(destination) {
        var _this = _super.call(this, destination) || this;
        _this.window = new Subject();
        destination.next(_this.window);
        return _this;
      }
      WindowSubscriber3.prototype.notifyNext = function() {
        this.openWindow();
      };
      WindowSubscriber3.prototype.notifyError = function(error) {
        this._error(error);
      };
      WindowSubscriber3.prototype.notifyComplete = function() {
        this._complete();
      };
      WindowSubscriber3.prototype._next = function(value) {
        this.window.next(value);
      };
      WindowSubscriber3.prototype._error = function(err) {
        this.window.error(err);
        this.destination.error(err);
      };
      WindowSubscriber3.prototype._complete = function() {
        this.window.complete();
        this.destination.complete();
      };
      WindowSubscriber3.prototype._unsubscribe = function() {
        this.window = null;
      };
      WindowSubscriber3.prototype.openWindow = function() {
        var prevWindow = this.window;
        if (prevWindow) {
          prevWindow.complete();
        }
        var destination = this.destination;
        var newWindow = this.window = new Subject();
        destination.next(newWindow);
      };
      return WindowSubscriber3;
    })(SimpleOuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/windowCount.js
function windowCount(windowSize, startWindowEvery) {
  if (startWindowEvery === void 0) {
    startWindowEvery = 0;
  }
  return function windowCountOperatorFunction(source) {
    return source.lift(new WindowCountOperator(windowSize, startWindowEvery));
  };
}
var WindowCountOperator, WindowCountSubscriber;
var init_windowCount = __esm({
  "node_modules/rxjs/_esm5/internal/operators/windowCount.js"() {
    init_tslib_es6();
    init_Subscriber();
    init_Subject();
    WindowCountOperator = (function() {
      function WindowCountOperator2(windowSize, startWindowEvery) {
        this.windowSize = windowSize;
        this.startWindowEvery = startWindowEvery;
      }
      WindowCountOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new WindowCountSubscriber(subscriber, this.windowSize, this.startWindowEvery));
      };
      return WindowCountOperator2;
    })();
    WindowCountSubscriber = (function(_super) {
      __extends(WindowCountSubscriber2, _super);
      function WindowCountSubscriber2(destination, windowSize, startWindowEvery) {
        var _this = _super.call(this, destination) || this;
        _this.destination = destination;
        _this.windowSize = windowSize;
        _this.startWindowEvery = startWindowEvery;
        _this.windows = [new Subject()];
        _this.count = 0;
        destination.next(_this.windows[0]);
        return _this;
      }
      WindowCountSubscriber2.prototype._next = function(value) {
        var startWindowEvery = this.startWindowEvery > 0 ? this.startWindowEvery : this.windowSize;
        var destination = this.destination;
        var windowSize = this.windowSize;
        var windows = this.windows;
        var len = windows.length;
        for (var i = 0; i < len && !this.closed; i++) {
          windows[i].next(value);
        }
        var c = this.count - windowSize + 1;
        if (c >= 0 && c % startWindowEvery === 0 && !this.closed) {
          windows.shift().complete();
        }
        if (++this.count % startWindowEvery === 0 && !this.closed) {
          var window_1 = new Subject();
          windows.push(window_1);
          destination.next(window_1);
        }
      };
      WindowCountSubscriber2.prototype._error = function(err) {
        var windows = this.windows;
        if (windows) {
          while (windows.length > 0 && !this.closed) {
            windows.shift().error(err);
          }
        }
        this.destination.error(err);
      };
      WindowCountSubscriber2.prototype._complete = function() {
        var windows = this.windows;
        if (windows) {
          while (windows.length > 0 && !this.closed) {
            windows.shift().complete();
          }
        }
        this.destination.complete();
      };
      WindowCountSubscriber2.prototype._unsubscribe = function() {
        this.count = 0;
        this.windows = null;
      };
      return WindowCountSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/windowTime.js
function windowTime(windowTimeSpan) {
  var scheduler = async;
  var windowCreationInterval = null;
  var maxWindowSize = Number.POSITIVE_INFINITY;
  if (isScheduler(arguments[3])) {
    scheduler = arguments[3];
  }
  if (isScheduler(arguments[2])) {
    scheduler = arguments[2];
  } else if (isNumeric(arguments[2])) {
    maxWindowSize = Number(arguments[2]);
  }
  if (isScheduler(arguments[1])) {
    scheduler = arguments[1];
  } else if (isNumeric(arguments[1])) {
    windowCreationInterval = Number(arguments[1]);
  }
  return function windowTimeOperatorFunction(source) {
    return source.lift(new WindowTimeOperator(windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler));
  };
}
function dispatchWindowTimeSpanOnly(state) {
  var subscriber = state.subscriber, windowTimeSpan = state.windowTimeSpan, window2 = state.window;
  if (window2) {
    subscriber.closeWindow(window2);
  }
  state.window = subscriber.openWindow();
  this.schedule(state, windowTimeSpan);
}
function dispatchWindowCreation(state) {
  var windowTimeSpan = state.windowTimeSpan, subscriber = state.subscriber, scheduler = state.scheduler, windowCreationInterval = state.windowCreationInterval;
  var window2 = subscriber.openWindow();
  var action = this;
  var context = { action, subscription: null };
  var timeSpanState = { subscriber, window: window2, context };
  context.subscription = scheduler.schedule(dispatchWindowClose, windowTimeSpan, timeSpanState);
  action.add(context.subscription);
  action.schedule(state, windowCreationInterval);
}
function dispatchWindowClose(state) {
  var subscriber = state.subscriber, window2 = state.window, context = state.context;
  if (context && context.action && context.subscription) {
    context.action.remove(context.subscription);
  }
  subscriber.closeWindow(window2);
}
var WindowTimeOperator, CountedSubject, WindowTimeSubscriber;
var init_windowTime = __esm({
  "node_modules/rxjs/_esm5/internal/operators/windowTime.js"() {
    init_tslib_es6();
    init_Subject();
    init_async();
    init_Subscriber();
    init_isNumeric();
    init_isScheduler();
    WindowTimeOperator = (function() {
      function WindowTimeOperator2(windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler) {
        this.windowTimeSpan = windowTimeSpan;
        this.windowCreationInterval = windowCreationInterval;
        this.maxWindowSize = maxWindowSize;
        this.scheduler = scheduler;
      }
      WindowTimeOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new WindowTimeSubscriber(subscriber, this.windowTimeSpan, this.windowCreationInterval, this.maxWindowSize, this.scheduler));
      };
      return WindowTimeOperator2;
    })();
    CountedSubject = (function(_super) {
      __extends(CountedSubject2, _super);
      function CountedSubject2() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this._numberOfNextedValues = 0;
        return _this;
      }
      CountedSubject2.prototype.next = function(value) {
        this._numberOfNextedValues++;
        _super.prototype.next.call(this, value);
      };
      Object.defineProperty(CountedSubject2.prototype, "numberOfNextedValues", {
        get: function() {
          return this._numberOfNextedValues;
        },
        enumerable: true,
        configurable: true
      });
      return CountedSubject2;
    })(Subject);
    WindowTimeSubscriber = (function(_super) {
      __extends(WindowTimeSubscriber2, _super);
      function WindowTimeSubscriber2(destination, windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler) {
        var _this = _super.call(this, destination) || this;
        _this.destination = destination;
        _this.windowTimeSpan = windowTimeSpan;
        _this.windowCreationInterval = windowCreationInterval;
        _this.maxWindowSize = maxWindowSize;
        _this.scheduler = scheduler;
        _this.windows = [];
        var window2 = _this.openWindow();
        if (windowCreationInterval !== null && windowCreationInterval >= 0) {
          var closeState = { subscriber: _this, window: window2, context: null };
          var creationState = { windowTimeSpan, windowCreationInterval, subscriber: _this, scheduler };
          _this.add(scheduler.schedule(dispatchWindowClose, windowTimeSpan, closeState));
          _this.add(scheduler.schedule(dispatchWindowCreation, windowCreationInterval, creationState));
        } else {
          var timeSpanOnlyState = { subscriber: _this, window: window2, windowTimeSpan };
          _this.add(scheduler.schedule(dispatchWindowTimeSpanOnly, windowTimeSpan, timeSpanOnlyState));
        }
        return _this;
      }
      WindowTimeSubscriber2.prototype._next = function(value) {
        var windows = this.windows;
        var len = windows.length;
        for (var i = 0; i < len; i++) {
          var window_1 = windows[i];
          if (!window_1.closed) {
            window_1.next(value);
            if (window_1.numberOfNextedValues >= this.maxWindowSize) {
              this.closeWindow(window_1);
            }
          }
        }
      };
      WindowTimeSubscriber2.prototype._error = function(err) {
        var windows = this.windows;
        while (windows.length > 0) {
          windows.shift().error(err);
        }
        this.destination.error(err);
      };
      WindowTimeSubscriber2.prototype._complete = function() {
        var windows = this.windows;
        while (windows.length > 0) {
          var window_2 = windows.shift();
          if (!window_2.closed) {
            window_2.complete();
          }
        }
        this.destination.complete();
      };
      WindowTimeSubscriber2.prototype.openWindow = function() {
        var window2 = new CountedSubject();
        this.windows.push(window2);
        var destination = this.destination;
        destination.next(window2);
        return window2;
      };
      WindowTimeSubscriber2.prototype.closeWindow = function(window2) {
        window2.complete();
        var windows = this.windows;
        windows.splice(windows.indexOf(window2), 1);
      };
      return WindowTimeSubscriber2;
    })(Subscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/windowToggle.js
function windowToggle(openings, closingSelector) {
  return function(source) {
    return source.lift(new WindowToggleOperator(openings, closingSelector));
  };
}
var WindowToggleOperator, WindowToggleSubscriber;
var init_windowToggle = __esm({
  "node_modules/rxjs/_esm5/internal/operators/windowToggle.js"() {
    init_tslib_es6();
    init_Subject();
    init_Subscription();
    init_OuterSubscriber();
    init_subscribeToResult();
    WindowToggleOperator = (function() {
      function WindowToggleOperator2(openings, closingSelector) {
        this.openings = openings;
        this.closingSelector = closingSelector;
      }
      WindowToggleOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new WindowToggleSubscriber(subscriber, this.openings, this.closingSelector));
      };
      return WindowToggleOperator2;
    })();
    WindowToggleSubscriber = (function(_super) {
      __extends(WindowToggleSubscriber2, _super);
      function WindowToggleSubscriber2(destination, openings, closingSelector) {
        var _this = _super.call(this, destination) || this;
        _this.openings = openings;
        _this.closingSelector = closingSelector;
        _this.contexts = [];
        _this.add(_this.openSubscription = subscribeToResult(_this, openings, openings));
        return _this;
      }
      WindowToggleSubscriber2.prototype._next = function(value) {
        var contexts = this.contexts;
        if (contexts) {
          var len = contexts.length;
          for (var i = 0; i < len; i++) {
            contexts[i].window.next(value);
          }
        }
      };
      WindowToggleSubscriber2.prototype._error = function(err) {
        var contexts = this.contexts;
        this.contexts = null;
        if (contexts) {
          var len = contexts.length;
          var index = -1;
          while (++index < len) {
            var context_1 = contexts[index];
            context_1.window.error(err);
            context_1.subscription.unsubscribe();
          }
        }
        _super.prototype._error.call(this, err);
      };
      WindowToggleSubscriber2.prototype._complete = function() {
        var contexts = this.contexts;
        this.contexts = null;
        if (contexts) {
          var len = contexts.length;
          var index = -1;
          while (++index < len) {
            var context_2 = contexts[index];
            context_2.window.complete();
            context_2.subscription.unsubscribe();
          }
        }
        _super.prototype._complete.call(this);
      };
      WindowToggleSubscriber2.prototype._unsubscribe = function() {
        var contexts = this.contexts;
        this.contexts = null;
        if (contexts) {
          var len = contexts.length;
          var index = -1;
          while (++index < len) {
            var context_3 = contexts[index];
            context_3.window.unsubscribe();
            context_3.subscription.unsubscribe();
          }
        }
      };
      WindowToggleSubscriber2.prototype.notifyNext = function(outerValue, innerValue, outerIndex, innerIndex, innerSub) {
        if (outerValue === this.openings) {
          var closingNotifier = void 0;
          try {
            var closingSelector = this.closingSelector;
            closingNotifier = closingSelector(innerValue);
          } catch (e) {
            return this.error(e);
          }
          var window_1 = new Subject();
          var subscription = new Subscription();
          var context_4 = { window: window_1, subscription };
          this.contexts.push(context_4);
          var innerSubscription = subscribeToResult(this, closingNotifier, context_4);
          if (innerSubscription.closed) {
            this.closeWindow(this.contexts.length - 1);
          } else {
            innerSubscription.context = context_4;
            subscription.add(innerSubscription);
          }
          this.destination.next(window_1);
        } else {
          this.closeWindow(this.contexts.indexOf(outerValue));
        }
      };
      WindowToggleSubscriber2.prototype.notifyError = function(err) {
        this.error(err);
      };
      WindowToggleSubscriber2.prototype.notifyComplete = function(inner) {
        if (inner !== this.openSubscription) {
          this.closeWindow(this.contexts.indexOf(inner.context));
        }
      };
      WindowToggleSubscriber2.prototype.closeWindow = function(index) {
        if (index === -1) {
          return;
        }
        var contexts = this.contexts;
        var context = contexts[index];
        var window2 = context.window, subscription = context.subscription;
        contexts.splice(index, 1);
        window2.complete();
        subscription.unsubscribe();
      };
      return WindowToggleSubscriber2;
    })(OuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/windowWhen.js
function windowWhen(closingSelector) {
  return function windowWhenOperatorFunction(source) {
    return source.lift(new WindowOperator2(closingSelector));
  };
}
var WindowOperator2, WindowSubscriber2;
var init_windowWhen = __esm({
  "node_modules/rxjs/_esm5/internal/operators/windowWhen.js"() {
    init_tslib_es6();
    init_Subject();
    init_OuterSubscriber();
    init_subscribeToResult();
    WindowOperator2 = (function() {
      function WindowOperator3(closingSelector) {
        this.closingSelector = closingSelector;
      }
      WindowOperator3.prototype.call = function(subscriber, source) {
        return source.subscribe(new WindowSubscriber2(subscriber, this.closingSelector));
      };
      return WindowOperator3;
    })();
    WindowSubscriber2 = (function(_super) {
      __extends(WindowSubscriber3, _super);
      function WindowSubscriber3(destination, closingSelector) {
        var _this = _super.call(this, destination) || this;
        _this.destination = destination;
        _this.closingSelector = closingSelector;
        _this.openWindow();
        return _this;
      }
      WindowSubscriber3.prototype.notifyNext = function(_outerValue, _innerValue, _outerIndex, _innerIndex, innerSub) {
        this.openWindow(innerSub);
      };
      WindowSubscriber3.prototype.notifyError = function(error) {
        this._error(error);
      };
      WindowSubscriber3.prototype.notifyComplete = function(innerSub) {
        this.openWindow(innerSub);
      };
      WindowSubscriber3.prototype._next = function(value) {
        this.window.next(value);
      };
      WindowSubscriber3.prototype._error = function(err) {
        this.window.error(err);
        this.destination.error(err);
        this.unsubscribeClosingNotification();
      };
      WindowSubscriber3.prototype._complete = function() {
        this.window.complete();
        this.destination.complete();
        this.unsubscribeClosingNotification();
      };
      WindowSubscriber3.prototype.unsubscribeClosingNotification = function() {
        if (this.closingNotification) {
          this.closingNotification.unsubscribe();
        }
      };
      WindowSubscriber3.prototype.openWindow = function(innerSub) {
        if (innerSub === void 0) {
          innerSub = null;
        }
        if (innerSub) {
          this.remove(innerSub);
          innerSub.unsubscribe();
        }
        var prevWindow = this.window;
        if (prevWindow) {
          prevWindow.complete();
        }
        var window2 = this.window = new Subject();
        this.destination.next(window2);
        var closingNotifier;
        try {
          var closingSelector = this.closingSelector;
          closingNotifier = closingSelector();
        } catch (e) {
          this.destination.error(e);
          this.window.error(e);
          return;
        }
        this.add(this.closingNotification = subscribeToResult(this, closingNotifier));
      };
      return WindowSubscriber3;
    })(OuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/withLatestFrom.js
function withLatestFrom() {
  var args = [];
  for (var _i = 0; _i < arguments.length; _i++) {
    args[_i] = arguments[_i];
  }
  return function(source) {
    var project;
    if (typeof args[args.length - 1] === "function") {
      project = args.pop();
    }
    var observables = args;
    return source.lift(new WithLatestFromOperator(observables, project));
  };
}
var WithLatestFromOperator, WithLatestFromSubscriber;
var init_withLatestFrom = __esm({
  "node_modules/rxjs/_esm5/internal/operators/withLatestFrom.js"() {
    init_tslib_es6();
    init_OuterSubscriber();
    init_subscribeToResult();
    WithLatestFromOperator = (function() {
      function WithLatestFromOperator2(observables, project) {
        this.observables = observables;
        this.project = project;
      }
      WithLatestFromOperator2.prototype.call = function(subscriber, source) {
        return source.subscribe(new WithLatestFromSubscriber(subscriber, this.observables, this.project));
      };
      return WithLatestFromOperator2;
    })();
    WithLatestFromSubscriber = (function(_super) {
      __extends(WithLatestFromSubscriber2, _super);
      function WithLatestFromSubscriber2(destination, observables, project) {
        var _this = _super.call(this, destination) || this;
        _this.observables = observables;
        _this.project = project;
        _this.toRespond = [];
        var len = observables.length;
        _this.values = new Array(len);
        for (var i = 0; i < len; i++) {
          _this.toRespond.push(i);
        }
        for (var i = 0; i < len; i++) {
          var observable = observables[i];
          _this.add(subscribeToResult(_this, observable, void 0, i));
        }
        return _this;
      }
      WithLatestFromSubscriber2.prototype.notifyNext = function(_outerValue, innerValue, outerIndex) {
        this.values[outerIndex] = innerValue;
        var toRespond = this.toRespond;
        if (toRespond.length > 0) {
          var found = toRespond.indexOf(outerIndex);
          if (found !== -1) {
            toRespond.splice(found, 1);
          }
        }
      };
      WithLatestFromSubscriber2.prototype.notifyComplete = function() {
      };
      WithLatestFromSubscriber2.prototype._next = function(value) {
        if (this.toRespond.length === 0) {
          var args = [value].concat(this.values);
          if (this.project) {
            this._tryProject(args);
          } else {
            this.destination.next(args);
          }
        }
      };
      WithLatestFromSubscriber2.prototype._tryProject = function(args) {
        var result;
        try {
          result = this.project.apply(this, args);
        } catch (err) {
          this.destination.error(err);
          return;
        }
        this.destination.next(result);
      };
      return WithLatestFromSubscriber2;
    })(OuterSubscriber);
  }
});

// node_modules/rxjs/_esm5/internal/operators/zip.js
function zip2() {
  var observables = [];
  for (var _i = 0; _i < arguments.length; _i++) {
    observables[_i] = arguments[_i];
  }
  return function zipOperatorFunction(source) {
    return source.lift.call(zip.apply(void 0, [source].concat(observables)));
  };
}
var init_zip2 = __esm({
  "node_modules/rxjs/_esm5/internal/operators/zip.js"() {
    init_zip();
  }
});

// node_modules/rxjs/_esm5/internal/operators/zipAll.js
function zipAll(project) {
  return function(source) {
    return source.lift(new ZipOperator(project));
  };
}
var init_zipAll = __esm({
  "node_modules/rxjs/_esm5/internal/operators/zipAll.js"() {
    init_zip();
  }
});

// node_modules/rxjs/_esm5/operators/index.js
var init_operators = __esm({
  "node_modules/rxjs/_esm5/operators/index.js"() {
    init_audit();
    init_auditTime();
    init_buffer();
    init_bufferCount();
    init_bufferTime();
    init_bufferToggle();
    init_bufferWhen();
    init_catchError();
    init_combineAll();
    init_combineLatest2();
    init_concat2();
    init_concatAll();
    init_concatMap();
    init_concatMapTo();
    init_count();
    init_debounce();
    init_debounceTime();
    init_defaultIfEmpty();
    init_delay();
    init_delayWhen();
    init_dematerialize();
    init_distinct();
    init_distinctUntilChanged();
    init_distinctUntilKeyChanged();
    init_elementAt();
    init_endWith();
    init_every();
    init_exhaust();
    init_exhaustMap();
    init_expand();
    init_filter();
    init_finalize();
    init_find();
    init_findIndex();
    init_first();
    init_groupBy();
    init_ignoreElements();
    init_isEmpty();
    init_last();
    init_map();
    init_mapTo();
    init_materialize();
    init_max();
    init_merge2();
    init_mergeAll();
    init_mergeMap();
    init_mergeMapTo();
    init_mergeScan();
    init_min();
    init_multicast();
    init_observeOn();
    init_onErrorResumeNext();
    init_pairwise();
    init_partition();
    init_pluck();
    init_publish();
    init_publishBehavior();
    init_publishLast();
    init_publishReplay();
    init_race2();
    init_reduce();
    init_repeat();
    init_repeatWhen();
    init_retry();
    init_retryWhen();
    init_refCount();
    init_sample();
    init_sampleTime();
    init_scan();
    init_sequenceEqual();
    init_share();
    init_shareReplay();
    init_single();
    init_skip();
    init_skipLast();
    init_skipUntil();
    init_skipWhile();
    init_startWith();
    init_subscribeOn();
    init_switchAll();
    init_switchMap();
    init_switchMapTo();
    init_take();
    init_takeLast();
    init_takeUntil();
    init_takeWhile();
    init_tap();
    init_throttle();
    init_throttleTime();
    init_throwIfEmpty();
    init_timeInterval();
    init_timeout();
    init_timeoutWith();
    init_timestamp();
    init_toArray();
    init_window();
    init_windowCount();
    init_windowTime();
    init_windowToggle();
    init_windowWhen();
    init_withLatestFrom();
    init_zip2();
    init_zipAll();
  }
});

export {
  audit,
  auditTime,
  buffer,
  bufferCount,
  bufferTime,
  bufferToggle,
  bufferWhen,
  catchError,
  combineAll,
  combineLatest,
  concat2 as concat,
  concatMap,
  concatMapTo,
  count,
  debounce,
  debounceTime,
  defaultIfEmpty,
  delay,
  delayWhen,
  dematerialize,
  distinct,
  distinctUntilChanged,
  distinctUntilKeyChanged,
  throwIfEmpty,
  take,
  elementAt,
  endWith,
  every,
  exhaust,
  exhaustMap,
  expand,
  finalize,
  find,
  findIndex,
  first,
  ignoreElements,
  isEmpty,
  takeLast,
  last,
  mapTo,
  materialize,
  scan,
  reduce,
  max,
  merge2 as merge,
  mergeMapTo,
  mergeScan,
  min,
  multicast,
  onErrorResumeNext,
  pairwise,
  partition,
  pluck,
  publish,
  publishBehavior,
  publishLast,
  publishReplay,
  race2 as race,
  repeat,
  repeatWhen,
  retry,
  retryWhen,
  sample,
  sampleTime,
  sequenceEqual,
  share,
  shareReplay,
  single,
  skip,
  skipLast,
  skipUntil,
  skipWhile,
  startWith,
  subscribeOn,
  switchMap,
  switchAll,
  switchMapTo,
  takeUntil,
  takeWhile,
  tap,
  throttle,
  throttleTime,
  timeInterval,
  timeoutWith,
  timeout,
  timestamp,
  toArray,
  window,
  windowCount,
  windowTime,
  windowToggle,
  windowWhen,
  withLatestFrom,
  zip2 as zip,
  zipAll,
  init_operators
};
//# sourceMappingURL=chunk-H5IUTQT7.js.map

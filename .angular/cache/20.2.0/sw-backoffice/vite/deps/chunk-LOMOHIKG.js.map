{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/id-generator.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/keycodes2.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/typeahead.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/keycodes.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/list-key-manager.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/focus-key-manager.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/shadow-dom.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable } from '@angular/core';\n\n/**\n * Keeps track of the ID count per prefix. This helps us make the IDs a bit more deterministic\n * like they were before the service was introduced. Note that ideally we wouldn't have to do\n * this, but there are some internal tests that rely on the IDs.\n */\nconst counters = {};\n/** Service that generates unique IDs for DOM nodes. */\nclass _IdGenerator {\n  _appId = inject(APP_ID);\n  /**\n   * Generates a unique ID with a specific prefix.\n   * @param prefix Prefix to add to the ID.\n   */\n  getId(prefix) {\n    // Omit the app ID if it's the default `ng`. Since the vast majority of pages have one\n    // Angular app on them, we can reduce the amount of breakages by not adding it.\n    if (this._appId !== 'ng') {\n      prefix += this._appId;\n    }\n    if (!counters.hasOwnProperty(prefix)) {\n      counters[prefix] = 0;\n    }\n    return `${prefix}${counters[prefix]++}`;\n  }\n  static ɵfac = function _IdGenerator_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _IdGenerator)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: _IdGenerator,\n    factory: _IdGenerator.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_IdGenerator, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { _IdGenerator };\n", "const MAC_ENTER = 3;\nconst BACKSPACE = 8;\nconst TAB = 9;\nconst NUM_CENTER = 12;\nconst ENTER = 13;\nconst SHIFT = 16;\nconst CONTROL = 17;\nconst ALT = 18;\nconst PAUSE = 19;\nconst CAPS_LOCK = 20;\nconst ESCAPE = 27;\nconst SPACE = 32;\nconst PAGE_UP = 33;\nconst PAGE_DOWN = 34;\nconst END = 35;\nconst HOME = 36;\nconst LEFT_ARROW = 37;\nconst UP_ARROW = 38;\nconst RIGHT_ARROW = 39;\nconst DOWN_ARROW = 40;\nconst PLUS_SIGN = 43;\nconst PRINT_SCREEN = 44;\nconst INSERT = 45;\nconst DELETE = 46;\nconst ZERO = 48;\nconst ONE = 49;\nconst TWO = 50;\nconst THREE = 51;\nconst FOUR = 52;\nconst FIVE = 53;\nconst SIX = 54;\nconst SEVEN = 55;\nconst EIGHT = 56;\nconst NINE = 57;\nconst FF_SEMICOLON = 59; // Firefox (Gecko) fires this for semicolon instead of 186\nconst FF_EQUALS = 61; // Firefox (Gecko) fires this for equals instead of 187\nconst QUESTION_MARK = 63;\nconst AT_SIGN = 64;\nconst A = 65;\nconst B = 66;\nconst C = 67;\nconst D = 68;\nconst E = 69;\nconst F = 70;\nconst G = 71;\nconst H = 72;\nconst I = 73;\nconst J = 74;\nconst K = 75;\nconst L = 76;\nconst M = 77;\nconst N = 78;\nconst O = 79;\nconst P = 80;\nconst Q = 81;\nconst R = 82;\nconst S = 83;\nconst T = 84;\nconst U = 85;\nconst V = 86;\nconst W = 87;\nconst X = 88;\nconst Y = 89;\nconst Z = 90;\nconst META = 91; // WIN_KEY_LEFT\nconst MAC_WK_CMD_LEFT = 91;\nconst MAC_WK_CMD_RIGHT = 93;\nconst CONTEXT_MENU = 93;\nconst NUMPAD_ZERO = 96;\nconst NUMPAD_ONE = 97;\nconst NUMPAD_TWO = 98;\nconst NUMPAD_THREE = 99;\nconst NUMPAD_FOUR = 100;\nconst NUMPAD_FIVE = 101;\nconst NUMPAD_SIX = 102;\nconst NUMPAD_SEVEN = 103;\nconst NUMPAD_EIGHT = 104;\nconst NUMPAD_NINE = 105;\nconst NUMPAD_MULTIPLY = 106;\nconst NUMPAD_PLUS = 107;\nconst NUMPAD_MINUS = 109;\nconst NUMPAD_PERIOD = 110;\nconst NUMPAD_DIVIDE = 111;\nconst F1 = 112;\nconst F2 = 113;\nconst F3 = 114;\nconst F4 = 115;\nconst F5 = 116;\nconst F6 = 117;\nconst F7 = 118;\nconst F8 = 119;\nconst F9 = 120;\nconst F10 = 121;\nconst F11 = 122;\nconst F12 = 123;\nconst NUM_LOCK = 144;\nconst SCROLL_LOCK = 145;\nconst FIRST_MEDIA = 166;\nconst FF_MINUS = 173;\nconst MUTE = 173; // Firefox (Gecko) fires 181 for MUTE\nconst VOLUME_DOWN = 174; // Firefox (Gecko) fires 182 for VOLUME_DOWN\nconst VOLUME_UP = 175; // Firefox (Gecko) fires 183 for VOLUME_UP\nconst FF_MUTE = 181;\nconst FF_VOLUME_DOWN = 182;\nconst LAST_MEDIA = 183;\nconst FF_VOLUME_UP = 183;\nconst SEMICOLON = 186; // Firefox (Gecko) fires 59 for SEMICOLON\nconst EQUALS = 187; // Firefox (Gecko) fires 61 for EQUALS\nconst COMMA = 188;\nconst DASH = 189; // Firefox (Gecko) fires 173 for DASH/MINUS\nconst PERIOD = 190;\nconst SLASH = 191;\nconst APOSTROPHE = 192;\nconst TILDE = 192;\nconst OPEN_SQUARE_BRACKET = 219;\nconst BACKSLASH = 220;\nconst CLOSE_SQUARE_BRACKET = 221;\nconst SINGLE_QUOTE = 222;\nconst MAC_META = 224;\n\nexport { A, ALT, APOSTROPHE, AT_SIGN, B, BACKSLASH, BACKSPACE, C, CAPS_LOCK, CLOSE_SQUARE_BRACKET, COMMA, CONTEXT_MENU, CONTROL, D, DASH, DELETE, DOWN_ARROW, E, EIGHT, END, ENTER, EQUALS, ESCAPE, F, F1, F10, F11, F12, F2, F3, F4, F5, F6, F7, F8, F9, FF_EQUALS, FF_MINUS, FF_MUTE, FF_SEMICOLON, FF_VOLUME_DOWN, FF_VOLUME_UP, FIRST_MEDIA, FIVE, FOUR, G, H, HOME, I, INSERT, J, K, L, LAST_MEDIA, LEFT_ARROW, M, MAC_ENTER, MAC_META, MAC_WK_CMD_LEFT, MAC_WK_CMD_RIGHT, META, MUTE, N, NINE, NUMPAD_DIVIDE, NUMPAD_EIGHT, NUMPAD_FIVE, NUMPAD_FOUR, NUMPAD_MINUS, NUMPAD_MULTIPLY, NUMPAD_NINE, NUMPAD_ONE, NUMPAD_PERIOD, NUMPAD_PLUS, NUMPAD_SEVEN, NUMPAD_SIX, NUMPAD_THREE, NUMPAD_TWO, NUMPAD_ZERO, NUM_CENTER, NUM_LOCK, O, ONE, OPEN_SQUARE_BRACKET, P, PAGE_DOWN, PAGE_UP, PAUSE, PERIOD, PLUS_SIGN, PRINT_SCREEN, Q, QUESTION_MARK, R, RIGHT_ARROW, S, SCROLL_LOCK, SEMICOLON, SEVEN, SHIFT, SINGLE_QUOTE, SIX, SLASH, SPACE, T, TAB, THREE, TILDE, TWO, U, UP_ARROW, V, VOLUME_DOWN, VOLUME_UP, W, X, Y, Z, ZERO };\n\n", "import { Subject } from 'rxjs';\nimport { tap, debounceTime, filter, map } from 'rxjs/operators';\nimport { A, Z, ZERO, NINE } from './keycodes2.mjs';\n\nconst DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS = 200;\n/**\n * Selects items based on keyboard inputs. Implements the typeahead functionality of\n * `role=\"listbox\"` or `role=\"tree\"` and other related roles.\n */\nclass Typeahead {\n    _letterKeyStream = new Subject();\n    _items = [];\n    _selectedItemIndex = -1;\n    /** Buffer for the letters that the user has pressed */\n    _pressedLetters = [];\n    _skipPredicateFn;\n    _selectedItem = new Subject();\n    selectedItem = this._selectedItem;\n    constructor(initialItems, config) {\n        const typeAheadInterval = typeof config?.debounceInterval === 'number'\n            ? config.debounceInterval\n            : DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS;\n        if (config?.skipPredicate) {\n            this._skipPredicateFn = config.skipPredicate;\n        }\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n            initialItems.length &&\n            initialItems.some(item => typeof item.getLabel !== 'function')) {\n            throw new Error('KeyManager items in typeahead mode must implement the `getLabel` method.');\n        }\n        this.setItems(initialItems);\n        this._setupKeyHandler(typeAheadInterval);\n    }\n    destroy() {\n        this._pressedLetters = [];\n        this._letterKeyStream.complete();\n        this._selectedItem.complete();\n    }\n    setCurrentSelectedItemIndex(index) {\n        this._selectedItemIndex = index;\n    }\n    setItems(items) {\n        this._items = items;\n    }\n    handleKey(event) {\n        const keyCode = event.keyCode;\n        // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n        // otherwise fall back to resolving alphanumeric characters via the keyCode.\n        if (event.key && event.key.length === 1) {\n            this._letterKeyStream.next(event.key.toLocaleUpperCase());\n        }\n        else if ((keyCode >= A && keyCode <= Z) || (keyCode >= ZERO && keyCode <= NINE)) {\n            this._letterKeyStream.next(String.fromCharCode(keyCode));\n        }\n    }\n    /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n    isTyping() {\n        return this._pressedLetters.length > 0;\n    }\n    /** Resets the currently stored sequence of typed letters. */\n    reset() {\n        this._pressedLetters = [];\n    }\n    _setupKeyHandler(typeAheadInterval) {\n        // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n        // and convert those letters back into a string. Afterwards find the first item that starts\n        // with that string and select it.\n        this._letterKeyStream\n            .pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(typeAheadInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join('').toLocaleUpperCase()))\n            .subscribe(inputString => {\n            // Start at 1 because we want to start searching at the item immediately\n            // following the current active item.\n            for (let i = 1; i < this._items.length + 1; i++) {\n                const index = (this._selectedItemIndex + i) % this._items.length;\n                const item = this._items[index];\n                if (!this._skipPredicateFn?.(item) &&\n                    item.getLabel?.().toLocaleUpperCase().trim().indexOf(inputString) === 0) {\n                    this._selectedItem.next(item);\n                    break;\n                }\n            }\n            this._pressedLetters = [];\n        });\n    }\n}\n\nexport { Typeahead };\n\n", "export { A, ALT, APOSTROPHE, AT_SIGN, B, <PERSON>C<PERSON>LASH, BACKSPACE, C, CAPS_LOCK, CLOSE_SQUARE_BRACKET, COMMA, CONTEXT_MENU, CONTROL, D, DASH, DELETE, DOWN_ARROW, E, EIGHT, END, ENTER, EQUALS, ESCAPE, F, F1, F10, F11, F12, F2, F3, F4, F5, F6, F7, F8, F9, FF_EQUALS, FF_MINUS, FF_MUTE, FF_SEMICOLON, FF_VOLUME_DOWN, FF_VOLUME_UP, FIRST_MEDIA, FIVE, FOUR, G, H, HOME, I, INSERT, J, K, L, LAST_MEDIA, LEFT_ARROW, M, MAC_ENTER, MAC_META, MAC_WK_CMD_LEFT, MAC_WK_CMD_RIGHT, META, MUTE, N, NINE, NUMPAD_DIVIDE, NUMPAD_EIGHT, NUMPAD_FIVE, NUMPAD_FOUR, NUMPAD_MINUS, NUMPAD_MULTIPLY, NUMPAD_NINE, NUMPAD_ONE, NUMPAD_PERIOD, NUMPAD_PLUS, NUMPAD_SEVEN, NUMPAD_SIX, NUMPAD_THREE, NUMPAD_TWO, NUMPAD_ZERO, NUM_CENTER, NUM_LOCK, O, ONE, OPEN_SQUARE_BRACKET, P, PAGE_DOWN, PAGE_UP, PAUSE, PERIOD, PLUS_SIGN, PRINT_SCREEN, Q, QUESTION_MARK, R, RIGHT_ARROW, S, SCROLL_LOCK, SEMICOLON, SEVEN, SHIFT, SINGLE_QUOTE, SIX, SLASH, SPACE, T, TAB, THREE, TILDE, TWO, U, UP_ARROW, V, VOLUME_DOWN, VOLUME_UP, W, X, Y, Z, ZERO } from './keycodes2.mjs';\n\n/**\n * Checks whether a modifier key is pressed.\n * @param event Event to be checked.\n */\nfunction hasModifierKey(event, ...modifiers) {\n    if (modifiers.length) {\n        return modifiers.some(modifier => event[modifier]);\n    }\n    return event.altKey || event.shiftKey || event.ctrlKey || event.metaKey;\n}\n\nexport { hasModifierKey };\n\n", "import { signal, QueryList, isSignal, effect } from '@angular/core';\nimport { Subscription, Subject } from 'rxjs';\nimport { Typeahead } from './typeahead.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { PAGE_DOWN, PAGE_UP, END, HOME, LEFT_ARROW, RIGHT_ARROW, UP_ARROW, DOWN_ARROW, TAB } from './keycodes2.mjs';\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nclass ListKeyManager {\n    _items;\n    _activeItemIndex = signal(-1, ...(ngDevMode ? [{ debugName: \"_activeItemIndex\" }] : []));\n    _activeItem = signal(null, ...(ngDevMode ? [{ debugName: \"_activeItem\" }] : []));\n    _wrap = false;\n    _typeaheadSubscription = Subscription.EMPTY;\n    _itemChangesSubscription;\n    _vertical = true;\n    _horizontal;\n    _allowedModifierKeys = [];\n    _homeAndEnd = false;\n    _pageUpAndDown = { enabled: false, delta: 10 };\n    _effectRef;\n    _typeahead;\n    /**\n     * Predicate function that can be used to check whether an item should be skipped\n     * by the key manager. By default, disabled items are skipped.\n     */\n    _skipPredicateFn = (item) => item.disabled;\n    constructor(_items, injector) {\n        this._items = _items;\n        // We allow for the items to be an array because, in some cases, the consumer may\n        // not have access to a QueryList of the items they want to manage (e.g. when the\n        // items aren't being collected via `ViewChildren` or `ContentChildren`).\n        if (_items instanceof QueryList) {\n            this._itemChangesSubscription = _items.changes.subscribe((newItems) => this._itemsChanged(newItems.toArray()));\n        }\n        else if (isSignal(_items)) {\n            if (!injector && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw new Error('ListKeyManager constructed with a signal must receive an injector');\n            }\n            this._effectRef = effect(() => this._itemsChanged(_items()), ...(ngDevMode ? [{ debugName: \"_effectRef\", injector }] : [{ injector }]));\n        }\n    }\n    /**\n     * Stream that emits any time the TAB key is pressed, so components can react\n     * when focus is shifted off of the list.\n     */\n    tabOut = new Subject();\n    /** Stream that emits whenever the active item of the list manager changes. */\n    change = new Subject();\n    /**\n     * Sets the predicate function that determines which items should be skipped by the\n     * list key manager.\n     * @param predicate Function that determines whether the given item should be skipped.\n     */\n    skipPredicate(predicate) {\n        this._skipPredicateFn = predicate;\n        return this;\n    }\n    /**\n     * Configures wrapping mode, which determines whether the active item will wrap to\n     * the other end of list when there are no more items in the given direction.\n     * @param shouldWrap Whether the list should wrap when reaching the end.\n     */\n    withWrap(shouldWrap = true) {\n        this._wrap = shouldWrap;\n        return this;\n    }\n    /**\n     * Configures whether the key manager should be able to move the selection vertically.\n     * @param enabled Whether vertical selection should be enabled.\n     */\n    withVerticalOrientation(enabled = true) {\n        this._vertical = enabled;\n        return this;\n    }\n    /**\n     * Configures the key manager to move the selection horizontally.\n     * Passing in `null` will disable horizontal movement.\n     * @param direction Direction in which the selection can be moved.\n     */\n    withHorizontalOrientation(direction) {\n        this._horizontal = direction;\n        return this;\n    }\n    /**\n     * Modifier keys which are allowed to be held down and whose default actions will be prevented\n     * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n     */\n    withAllowedModifierKeys(keys) {\n        this._allowedModifierKeys = keys;\n        return this;\n    }\n    /**\n     * Turns on typeahead mode which allows users to set the active item by typing.\n     * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n     */\n    withTypeAhead(debounceInterval = 200) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const items = this._getItemsArray();\n            if (items.length > 0 && items.some(item => typeof item.getLabel !== 'function')) {\n                throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n            }\n        }\n        this._typeaheadSubscription.unsubscribe();\n        const items = this._getItemsArray();\n        this._typeahead = new Typeahead(items, {\n            debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n            skipPredicate: item => this._skipPredicateFn(item),\n        });\n        this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n            this.setActiveItem(item);\n        });\n        return this;\n    }\n    /** Cancels the current typeahead sequence. */\n    cancelTypeahead() {\n        this._typeahead?.reset();\n        return this;\n    }\n    /**\n     * Configures the key manager to activate the first and last items\n     * respectively when the Home or End key is pressed.\n     * @param enabled Whether pressing the Home or End key activates the first/last item.\n     */\n    withHomeAndEnd(enabled = true) {\n        this._homeAndEnd = enabled;\n        return this;\n    }\n    /**\n     * Configures the key manager to activate every 10th, configured or first/last element in up/down direction\n     * respectively when the Page-Up or Page-Down key is pressed.\n     * @param enabled Whether pressing the Page-Up or Page-Down key activates the first/last item.\n     * @param delta Whether pressing the Home or End key activates the first/last item.\n     */\n    withPageUpDown(enabled = true, delta = 10) {\n        this._pageUpAndDown = { enabled, delta };\n        return this;\n    }\n    setActiveItem(item) {\n        const previousActiveItem = this._activeItem();\n        this.updateActiveItem(item);\n        if (this._activeItem() !== previousActiveItem) {\n            this.change.next(this._activeItemIndex());\n        }\n    }\n    /**\n     * Sets the active item depending on the key event passed in.\n     * @param event Keyboard event to be used for determining which element should be active.\n     */\n    onKeydown(event) {\n        const keyCode = event.keyCode;\n        const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n        const isModifierAllowed = modifiers.every(modifier => {\n            return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n        });\n        switch (keyCode) {\n            case TAB:\n                this.tabOut.next();\n                return;\n            case DOWN_ARROW:\n                if (this._vertical && isModifierAllowed) {\n                    this.setNextItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case UP_ARROW:\n                if (this._vertical && isModifierAllowed) {\n                    this.setPreviousItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case RIGHT_ARROW:\n                if (this._horizontal && isModifierAllowed) {\n                    this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case LEFT_ARROW:\n                if (this._horizontal && isModifierAllowed) {\n                    this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case HOME:\n                if (this._homeAndEnd && isModifierAllowed) {\n                    this.setFirstItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case END:\n                if (this._homeAndEnd && isModifierAllowed) {\n                    this.setLastItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case PAGE_UP:\n                if (this._pageUpAndDown.enabled && isModifierAllowed) {\n                    const targetIndex = this._activeItemIndex() - this._pageUpAndDown.delta;\n                    this._setActiveItemByIndex(targetIndex > 0 ? targetIndex : 0, 1);\n                    break;\n                }\n                else {\n                    return;\n                }\n            case PAGE_DOWN:\n                if (this._pageUpAndDown.enabled && isModifierAllowed) {\n                    const targetIndex = this._activeItemIndex() + this._pageUpAndDown.delta;\n                    const itemsLength = this._getItemsArray().length;\n                    this._setActiveItemByIndex(targetIndex < itemsLength ? targetIndex : itemsLength - 1, -1);\n                    break;\n                }\n                else {\n                    return;\n                }\n            default:\n                if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n                    this._typeahead?.handleKey(event);\n                }\n                // Note that we return here, in order to avoid preventing\n                // the default action of non-navigational keys.\n                return;\n        }\n        this._typeahead?.reset();\n        event.preventDefault();\n    }\n    /** Index of the currently active item. */\n    get activeItemIndex() {\n        return this._activeItemIndex();\n    }\n    /** The active item. */\n    get activeItem() {\n        return this._activeItem();\n    }\n    /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n    isTyping() {\n        return !!this._typeahead && this._typeahead.isTyping();\n    }\n    /** Sets the active item to the first enabled item in the list. */\n    setFirstItemActive() {\n        this._setActiveItemByIndex(0, 1);\n    }\n    /** Sets the active item to the last enabled item in the list. */\n    setLastItemActive() {\n        this._setActiveItemByIndex(this._getItemsArray().length - 1, -1);\n    }\n    /** Sets the active item to the next enabled item in the list. */\n    setNextItemActive() {\n        this._activeItemIndex() < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n    }\n    /** Sets the active item to a previous enabled item in the list. */\n    setPreviousItemActive() {\n        this._activeItemIndex() < 0 && this._wrap\n            ? this.setLastItemActive()\n            : this._setActiveItemByDelta(-1);\n    }\n    updateActiveItem(item) {\n        const itemArray = this._getItemsArray();\n        const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n        const activeItem = itemArray[index];\n        // Explicitly check for `null` and `undefined` because other falsy values are valid.\n        this._activeItem.set(activeItem == null ? null : activeItem);\n        this._activeItemIndex.set(index);\n        this._typeahead?.setCurrentSelectedItemIndex(index);\n    }\n    /** Cleans up the key manager. */\n    destroy() {\n        this._typeaheadSubscription.unsubscribe();\n        this._itemChangesSubscription?.unsubscribe();\n        this._effectRef?.destroy();\n        this._typeahead?.destroy();\n        this.tabOut.complete();\n        this.change.complete();\n    }\n    /**\n     * This method sets the active item, given a list of items and the delta between the\n     * currently active item and the new active item. It will calculate differently\n     * depending on whether wrap mode is turned on.\n     */\n    _setActiveItemByDelta(delta) {\n        this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n    }\n    /**\n     * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n     * down the list until it finds an item that is not disabled, and it will wrap if it\n     * encounters either end of the list.\n     */\n    _setActiveInWrapMode(delta) {\n        const items = this._getItemsArray();\n        for (let i = 1; i <= items.length; i++) {\n            const index = (this._activeItemIndex() + delta * i + items.length) % items.length;\n            const item = items[index];\n            if (!this._skipPredicateFn(item)) {\n                this.setActiveItem(index);\n                return;\n            }\n        }\n    }\n    /**\n     * Sets the active item properly given the default mode. In other words, it will\n     * continue to move down the list until it finds an item that is not disabled. If\n     * it encounters either end of the list, it will stop and not wrap.\n     */\n    _setActiveInDefaultMode(delta) {\n        this._setActiveItemByIndex(this._activeItemIndex() + delta, delta);\n    }\n    /**\n     * Sets the active item to the first enabled item starting at the index specified. If the\n     * item is disabled, it will move in the fallbackDelta direction until it either\n     * finds an enabled item or encounters the end of the list.\n     */\n    _setActiveItemByIndex(index, fallbackDelta) {\n        const items = this._getItemsArray();\n        if (!items[index]) {\n            return;\n        }\n        while (this._skipPredicateFn(items[index])) {\n            index += fallbackDelta;\n            if (!items[index]) {\n                return;\n            }\n        }\n        this.setActiveItem(index);\n    }\n    /** Returns the items as an array. */\n    _getItemsArray() {\n        if (isSignal(this._items)) {\n            return this._items();\n        }\n        return this._items instanceof QueryList ? this._items.toArray() : this._items;\n    }\n    /** Callback for when the items have changed. */\n    _itemsChanged(newItems) {\n        this._typeahead?.setItems(newItems);\n        const activeItem = this._activeItem();\n        if (activeItem) {\n            const newIndex = newItems.indexOf(activeItem);\n            if (newIndex > -1 && newIndex !== this._activeItemIndex()) {\n                this._activeItemIndex.set(newIndex);\n                this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n            }\n        }\n    }\n}\n\nexport { ListKeyManager };\n\n", "import { ListKeyManager } from './list-key-manager.mjs';\n\nclass FocusKeyManager extends ListKeyManager {\n    _origin = 'program';\n    /**\n     * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n     * @param origin Focus origin to be used when focusing items.\n     */\n    setFocusOrigin(origin) {\n        this._origin = origin;\n        return this;\n    }\n    setActiveItem(item) {\n        super.setActiveItem(item);\n        if (this.activeItem) {\n            this.activeItem.focus(this._origin);\n        }\n    }\n}\n\nexport { FocusKeyManager };\n\n", "let shadowDomIsSupported;\n/** Checks whether the user's browser support Shadow DOM. */\nfunction _supportsShadowDom() {\n    if (shadowDomIsSupported == null) {\n        const head = typeof document !== 'undefined' ? document.head : null;\n        shadowDomIsSupported = !!(head && (head.createShadowRoot || head.attachShadow));\n    }\n    return shadowDomIsSupported;\n}\n/** Gets the shadow root of an element, if supported and the element is inside the Shadow DOM. */\nfunction _getShadowRoot(element) {\n    if (_supportsShadowDom()) {\n        const rootNode = element.getRootNode ? element.getRootNode() : null;\n        // Note that this should be caught by `_supportsShadowDom`, but some\n        // teams have been able to hit this code path on unsupported browsers.\n        if (typeof ShadowRoot !== 'undefined' && ShadowRoot && rootNode instanceof ShadowRoot) {\n            return rootNode;\n        }\n    }\n    return null;\n}\n/**\n * Gets the currently-focused element on the page while\n * also piercing through Shadow DOM boundaries.\n */\nfunction _getFocusedElementPierceShadowDom() {\n    let activeElement = typeof document !== 'undefined' && document\n        ? document.activeElement\n        : null;\n    while (activeElement && activeElement.shadowRoot) {\n        const newActiveElement = activeElement.shadowRoot.activeElement;\n        if (newActiveElement === activeElement) {\n            break;\n        }\n        else {\n            activeElement = newActiveElement;\n        }\n    }\n    return activeElement;\n}\n/** Gets the target of an event while accounting for Shadow DOM. */\nfunction _getEventTarget(event) {\n    // If an event is bound outside the Shadow DOM, the `event.target` will\n    // point to the shadow root so we have to use `composedPath` instead.\n    return (event.composedPath ? event.composedPath()[0] : event.target);\n}\n\nexport { _getEventTarget, _getFocusedElementPierceShadowDom, _getShadowRoot, _supportsShadowDom };\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAOA,IAAM,WAAW,CAAC;AAElB,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,SAAS,OAAO,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,MAAM,QAAQ;AAGZ,QAAI,KAAK,WAAW,MAAM;AACxB,gBAAU,KAAK;AAAA,IACjB;AACA,QAAI,CAAC,SAAS,eAAe,MAAM,GAAG;AACpC,eAAS,MAAM,IAAI;AAAA,IACrB;AACA,WAAO,GAAG,MAAM,GAAG,SAAS,MAAM,GAAG;AAAA,EACvC;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,IACtB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC1CH,IAAM,YAAY;AAClB,IAAM,MAAM;AAEZ,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,MAAM;AAGZ,IAAM,SAAS;AACf,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,YAAY;AAClB,IAAM,MAAM;AACZ,IAAM,OAAO;AACb,IAAM,aAAa;AACnB,IAAM,WAAW;AACjB,IAAM,cAAc;AACpB,IAAM,aAAa;AAInB,IAAM,SAAS;AACf,IAAM,OAAO;AASb,IAAM,OAAO;AAKb,IAAM,IAAI;AAyBV,IAAM,IAAI;AACV,IAAM,OAAO;AA4Cb,IAAM,QAAQ;AAUd,IAAM,WAAW;;;ACtHjB;AACA;AAGA,IAAM,yCAAyC;AAK/C,IAAM,YAAN,MAAgB;AAAA,EACZ,mBAAmB,IAAI,QAAQ;AAAA,EAC/B,SAAS,CAAC;AAAA,EACV,qBAAqB;AAAA;AAAA,EAErB,kBAAkB,CAAC;AAAA,EACnB;AAAA,EACA,gBAAgB,IAAI,QAAQ;AAAA,EAC5B,eAAe,KAAK;AAAA,EACpB,YAAY,cAAc,QAAQ;AAC9B,UAAM,oBAAoB,OAAO,QAAQ,qBAAqB,WACxD,OAAO,mBACP;AACN,QAAI,QAAQ,eAAe;AACvB,WAAK,mBAAmB,OAAO;AAAA,IACnC;AACA,SAAK,OAAO,cAAc,eAAe,cACrC,aAAa,UACb,aAAa,KAAK,UAAQ,OAAO,KAAK,aAAa,UAAU,GAAG;AAChE,YAAM,IAAI,MAAM,0EAA0E;AAAA,IAC9F;AACA,SAAK,SAAS,YAAY;AAC1B,SAAK,iBAAiB,iBAAiB;AAAA,EAC3C;AAAA,EACA,UAAU;AACN,SAAK,kBAAkB,CAAC;AACxB,SAAK,iBAAiB,SAAS;AAC/B,SAAK,cAAc,SAAS;AAAA,EAChC;AAAA,EACA,4BAA4B,OAAO;AAC/B,SAAK,qBAAqB;AAAA,EAC9B;AAAA,EACA,SAAS,OAAO;AACZ,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,UAAU,OAAO;AACb,UAAM,UAAU,MAAM;AAGtB,QAAI,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG;AACrC,WAAK,iBAAiB,KAAK,MAAM,IAAI,kBAAkB,CAAC;AAAA,IAC5D,WACU,WAAW,KAAK,WAAW,KAAO,WAAW,QAAQ,WAAW,MAAO;AAC7E,WAAK,iBAAiB,KAAK,OAAO,aAAa,OAAO,CAAC;AAAA,IAC3D;AAAA,EACJ;AAAA;AAAA,EAEA,WAAW;AACP,WAAO,KAAK,gBAAgB,SAAS;AAAA,EACzC;AAAA;AAAA,EAEA,QAAQ;AACJ,SAAK,kBAAkB,CAAC;AAAA,EAC5B;AAAA,EACA,iBAAiB,mBAAmB;AAIhC,SAAK,iBACA,KAAK,IAAI,YAAU,KAAK,gBAAgB,KAAK,MAAM,CAAC,GAAG,aAAa,iBAAiB,GAAG,OAAO,MAAM,KAAK,gBAAgB,SAAS,CAAC,GAAG,IAAI,MAAM,KAAK,gBAAgB,KAAK,EAAE,EAAE,kBAAkB,CAAC,CAAC,EACnM,UAAU,iBAAe;AAG1B,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,SAAS,GAAG,KAAK;AAC7C,cAAM,SAAS,KAAK,qBAAqB,KAAK,KAAK,OAAO;AAC1D,cAAM,OAAO,KAAK,OAAO,KAAK;AAC9B,YAAI,CAAC,KAAK,mBAAmB,IAAI,KAC7B,KAAK,WAAW,EAAE,kBAAkB,EAAE,KAAK,EAAE,QAAQ,WAAW,MAAM,GAAG;AACzE,eAAK,cAAc,KAAK,IAAI;AAC5B;AAAA,QACJ;AAAA,MACJ;AACA,WAAK,kBAAkB,CAAC;AAAA,IAC5B,CAAC;AAAA,EACL;AACJ;;;AC9EA,SAAS,eAAe,UAAU,WAAW;AACzC,MAAI,UAAU,QAAQ;AAClB,WAAO,UAAU,KAAK,cAAY,MAAM,QAAQ,CAAC;AAAA,EACrD;AACA,SAAO,MAAM,UAAU,MAAM,YAAY,MAAM,WAAW,MAAM;AACpE;;;ACXA;AACA;AASA,IAAM,iBAAN,MAAqB;AAAA,EACjB;AAAA,EACA,mBAAmB,OAAO,IAAI,GAAI,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,IAAI,CAAC,CAAE;AAAA,EACvF,cAAc,OAAO,MAAM,GAAI,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,IAAI,CAAC,CAAE;AAAA,EAC/E,QAAQ;AAAA,EACR,yBAAyB,aAAa;AAAA,EACtC;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA,uBAAuB,CAAC;AAAA,EACxB,cAAc;AAAA,EACd,iBAAiB,EAAE,SAAS,OAAO,OAAO,GAAG;AAAA,EAC7C;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,CAAC,SAAS,KAAK;AAAA,EAClC,YAAY,QAAQ,UAAU;AAC1B,SAAK,SAAS;AAId,QAAI,kBAAkB,WAAW;AAC7B,WAAK,2BAA2B,OAAO,QAAQ,UAAU,CAAC,aAAa,KAAK,cAAc,SAAS,QAAQ,CAAC,CAAC;AAAA,IACjH,WACS,SAAS,MAAM,GAAG;AACvB,UAAI,CAAC,aAAa,OAAO,cAAc,eAAe,YAAY;AAC9D,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACvF;AACA,WAAK,aAAa,OAAO,MAAM,KAAK,cAAc,OAAO,CAAC,GAAG,GAAI,YAAY,CAAC,EAAE,WAAW,cAAc,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,CAAE;AAAA,IAC1I;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,IAAI,QAAQ;AAAA;AAAA,EAErB,SAAS,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,cAAc,WAAW;AACrB,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,aAAa,MAAM;AACxB,SAAK,QAAQ;AACb,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,UAAU,MAAM;AACpC,SAAK,YAAY;AACjB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,0BAA0B,WAAW;AACjC,SAAK,cAAc;AACnB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,MAAM;AAC1B,SAAK,uBAAuB;AAC5B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,mBAAmB,KAAK;AAClC,QAAI,OAAO,cAAc,eAAe,WAAW;AAC/C,YAAMA,SAAQ,KAAK,eAAe;AAClC,UAAIA,OAAM,SAAS,KAAKA,OAAM,KAAK,UAAQ,OAAO,KAAK,aAAa,UAAU,GAAG;AAC7E,cAAM,MAAM,8EAA8E;AAAA,MAC9F;AAAA,IACJ;AACA,SAAK,uBAAuB,YAAY;AACxC,UAAM,QAAQ,KAAK,eAAe;AAClC,SAAK,aAAa,IAAI,UAAU,OAAO;AAAA,MACnC,kBAAkB,OAAO,qBAAqB,WAAW,mBAAmB;AAAA,MAC5E,eAAe,UAAQ,KAAK,iBAAiB,IAAI;AAAA,IACrD,CAAC;AACD,SAAK,yBAAyB,KAAK,WAAW,aAAa,UAAU,UAAQ;AACzE,WAAK,cAAc,IAAI;AAAA,IAC3B,CAAC;AACD,WAAO;AAAA,EACX;AAAA;AAAA,EAEA,kBAAkB;AACd,SAAK,YAAY,MAAM;AACvB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,UAAU,MAAM;AAC3B,SAAK,cAAc;AACnB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,UAAU,MAAM,QAAQ,IAAI;AACvC,SAAK,iBAAiB,EAAE,SAAS,MAAM;AACvC,WAAO;AAAA,EACX;AAAA,EACA,cAAc,MAAM;AAChB,UAAM,qBAAqB,KAAK,YAAY;AAC5C,SAAK,iBAAiB,IAAI;AAC1B,QAAI,KAAK,YAAY,MAAM,oBAAoB;AAC3C,WAAK,OAAO,KAAK,KAAK,iBAAiB,CAAC;AAAA,IAC5C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO;AACb,UAAM,UAAU,MAAM;AACtB,UAAM,YAAY,CAAC,UAAU,WAAW,WAAW,UAAU;AAC7D,UAAM,oBAAoB,UAAU,MAAM,cAAY;AAClD,aAAO,CAAC,MAAM,QAAQ,KAAK,KAAK,qBAAqB,QAAQ,QAAQ,IAAI;AAAA,IAC7E,CAAC;AACD,YAAQ,SAAS;AAAA,MACb,KAAK;AACD,aAAK,OAAO,KAAK;AACjB;AAAA,MACJ,KAAK;AACD,YAAI,KAAK,aAAa,mBAAmB;AACrC,eAAK,kBAAkB;AACvB;AAAA,QACJ,OACK;AACD;AAAA,QACJ;AAAA,MACJ,KAAK;AACD,YAAI,KAAK,aAAa,mBAAmB;AACrC,eAAK,sBAAsB;AAC3B;AAAA,QACJ,OACK;AACD;AAAA,QACJ;AAAA,MACJ,KAAK;AACD,YAAI,KAAK,eAAe,mBAAmB;AACvC,eAAK,gBAAgB,QAAQ,KAAK,sBAAsB,IAAI,KAAK,kBAAkB;AACnF;AAAA,QACJ,OACK;AACD;AAAA,QACJ;AAAA,MACJ,KAAK;AACD,YAAI,KAAK,eAAe,mBAAmB;AACvC,eAAK,gBAAgB,QAAQ,KAAK,kBAAkB,IAAI,KAAK,sBAAsB;AACnF;AAAA,QACJ,OACK;AACD;AAAA,QACJ;AAAA,MACJ,KAAK;AACD,YAAI,KAAK,eAAe,mBAAmB;AACvC,eAAK,mBAAmB;AACxB;AAAA,QACJ,OACK;AACD;AAAA,QACJ;AAAA,MACJ,KAAK;AACD,YAAI,KAAK,eAAe,mBAAmB;AACvC,eAAK,kBAAkB;AACvB;AAAA,QACJ,OACK;AACD;AAAA,QACJ;AAAA,MACJ,KAAK;AACD,YAAI,KAAK,eAAe,WAAW,mBAAmB;AAClD,gBAAM,cAAc,KAAK,iBAAiB,IAAI,KAAK,eAAe;AAClE,eAAK,sBAAsB,cAAc,IAAI,cAAc,GAAG,CAAC;AAC/D;AAAA,QACJ,OACK;AACD;AAAA,QACJ;AAAA,MACJ,KAAK;AACD,YAAI,KAAK,eAAe,WAAW,mBAAmB;AAClD,gBAAM,cAAc,KAAK,iBAAiB,IAAI,KAAK,eAAe;AAClE,gBAAM,cAAc,KAAK,eAAe,EAAE;AAC1C,eAAK,sBAAsB,cAAc,cAAc,cAAc,cAAc,GAAG,EAAE;AACxF;AAAA,QACJ,OACK;AACD;AAAA,QACJ;AAAA,MACJ;AACI,YAAI,qBAAqB,eAAe,OAAO,UAAU,GAAG;AACxD,eAAK,YAAY,UAAU,KAAK;AAAA,QACpC;AAGA;AAAA,IACR;AACA,SAAK,YAAY,MAAM;AACvB,UAAM,eAAe;AAAA,EACzB;AAAA;AAAA,EAEA,IAAI,kBAAkB;AAClB,WAAO,KAAK,iBAAiB;AAAA,EACjC;AAAA;AAAA,EAEA,IAAI,aAAa;AACb,WAAO,KAAK,YAAY;AAAA,EAC5B;AAAA;AAAA,EAEA,WAAW;AACP,WAAO,CAAC,CAAC,KAAK,cAAc,KAAK,WAAW,SAAS;AAAA,EACzD;AAAA;AAAA,EAEA,qBAAqB;AACjB,SAAK,sBAAsB,GAAG,CAAC;AAAA,EACnC;AAAA;AAAA,EAEA,oBAAoB;AAChB,SAAK,sBAAsB,KAAK,eAAe,EAAE,SAAS,GAAG,EAAE;AAAA,EACnE;AAAA;AAAA,EAEA,oBAAoB;AAChB,SAAK,iBAAiB,IAAI,IAAI,KAAK,mBAAmB,IAAI,KAAK,sBAAsB,CAAC;AAAA,EAC1F;AAAA;AAAA,EAEA,wBAAwB;AACpB,SAAK,iBAAiB,IAAI,KAAK,KAAK,QAC9B,KAAK,kBAAkB,IACvB,KAAK,sBAAsB,EAAE;AAAA,EACvC;AAAA,EACA,iBAAiB,MAAM;AACnB,UAAM,YAAY,KAAK,eAAe;AACtC,UAAM,QAAQ,OAAO,SAAS,WAAW,OAAO,UAAU,QAAQ,IAAI;AACtE,UAAM,aAAa,UAAU,KAAK;AAElC,SAAK,YAAY,IAAI,cAAc,OAAO,OAAO,UAAU;AAC3D,SAAK,iBAAiB,IAAI,KAAK;AAC/B,SAAK,YAAY,4BAA4B,KAAK;AAAA,EACtD;AAAA;AAAA,EAEA,UAAU;AACN,SAAK,uBAAuB,YAAY;AACxC,SAAK,0BAA0B,YAAY;AAC3C,SAAK,YAAY,QAAQ;AACzB,SAAK,YAAY,QAAQ;AACzB,SAAK,OAAO,SAAS;AACrB,SAAK,OAAO,SAAS;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,OAAO;AACzB,SAAK,QAAQ,KAAK,qBAAqB,KAAK,IAAI,KAAK,wBAAwB,KAAK;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,OAAO;AACxB,UAAM,QAAQ,KAAK,eAAe;AAClC,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,KAAK;AACpC,YAAM,SAAS,KAAK,iBAAiB,IAAI,QAAQ,IAAI,MAAM,UAAU,MAAM;AAC3E,YAAM,OAAO,MAAM,KAAK;AACxB,UAAI,CAAC,KAAK,iBAAiB,IAAI,GAAG;AAC9B,aAAK,cAAc,KAAK;AACxB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB,OAAO;AAC3B,SAAK,sBAAsB,KAAK,iBAAiB,IAAI,OAAO,KAAK;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,OAAO,eAAe;AACxC,UAAM,QAAQ,KAAK,eAAe;AAClC,QAAI,CAAC,MAAM,KAAK,GAAG;AACf;AAAA,IACJ;AACA,WAAO,KAAK,iBAAiB,MAAM,KAAK,CAAC,GAAG;AACxC,eAAS;AACT,UAAI,CAAC,MAAM,KAAK,GAAG;AACf;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,cAAc,KAAK;AAAA,EAC5B;AAAA;AAAA,EAEA,iBAAiB;AACb,QAAI,SAAS,KAAK,MAAM,GAAG;AACvB,aAAO,KAAK,OAAO;AAAA,IACvB;AACA,WAAO,KAAK,kBAAkB,YAAY,KAAK,OAAO,QAAQ,IAAI,KAAK;AAAA,EAC3E;AAAA;AAAA,EAEA,cAAc,UAAU;AACpB,SAAK,YAAY,SAAS,QAAQ;AAClC,UAAM,aAAa,KAAK,YAAY;AACpC,QAAI,YAAY;AACZ,YAAM,WAAW,SAAS,QAAQ,UAAU;AAC5C,UAAI,WAAW,MAAM,aAAa,KAAK,iBAAiB,GAAG;AACvD,aAAK,iBAAiB,IAAI,QAAQ;AAClC,aAAK,YAAY,4BAA4B,QAAQ;AAAA,MACzD;AAAA,IACJ;AAAA,EACJ;AACJ;;;AClWA,IAAM,kBAAN,cAA8B,eAAe;AAAA,EACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKV,eAAe,QAAQ;AACnB,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AAAA,EACA,cAAc,MAAM;AAChB,UAAM,cAAc,IAAI;AACxB,QAAI,KAAK,YAAY;AACjB,WAAK,WAAW,MAAM,KAAK,OAAO;AAAA,IACtC;AAAA,EACJ;AACJ;;;AClBA,IAAI;AAEJ,SAAS,qBAAqB;AAC1B,MAAI,wBAAwB,MAAM;AAC9B,UAAM,OAAO,OAAO,aAAa,cAAc,SAAS,OAAO;AAC/D,2BAAuB,CAAC,EAAE,SAAS,KAAK,oBAAoB,KAAK;AAAA,EACrE;AACA,SAAO;AACX;AAEA,SAAS,eAAe,SAAS;AAC7B,MAAI,mBAAmB,GAAG;AACtB,UAAM,WAAW,QAAQ,cAAc,QAAQ,YAAY,IAAI;AAG/D,QAAI,OAAO,eAAe,eAAe,cAAc,oBAAoB,YAAY;AACnF,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAKA,SAAS,oCAAoC;AACzC,MAAI,gBAAgB,OAAO,aAAa,eAAe,WACjD,SAAS,gBACT;AACN,SAAO,iBAAiB,cAAc,YAAY;AAC9C,UAAM,mBAAmB,cAAc,WAAW;AAClD,QAAI,qBAAqB,eAAe;AACpC;AAAA,IACJ,OACK;AACD,sBAAgB;AAAA,IACpB;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,gBAAgB,OAAO;AAG5B,SAAQ,MAAM,eAAe,MAAM,aAAa,EAAE,CAAC,IAAI,MAAM;AACjE;", "names": ["items"]}
{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/tree.mjs", "../../../../../../node_modules/@angular/material/fesm2022/tree.mjs"], "sourcesContent": ["import { SelectionModel } from './selection-model.mjs';\nimport { isObservable, Subject, BehaviorSubject, of, combineLatest, EMPTY, concat } from 'rxjs';\nimport { take, filter, takeUntil, startWith, tap, switchMap, map, reduce, concatMap, distinctUntilChanged } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ViewContainerRef, Directive, TemplateRef, IterableDiffers, ChangeDetectorRef, ElementRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, ViewChild, ContentChildren, EventEmitter, booleanAttribute, Output, numberAttribute, NgModule } from '@angular/core';\nimport { TREE_KEY_MANAGER } from './tree-key-manager.mjs';\nimport { Directionality } from './directionality.mjs';\nimport { isDataSource } from './data-source.mjs';\nimport { coerceObservable } from './coercion/private.mjs';\nimport './typeahead.mjs';\nimport './keycodes2.mjs';\n\n/**\n * Base tree control. It has basic toggle/expand/collapse operations on a single data node.\n *\n * @deprecated Use one of levelAccessor or childrenAccessor. To be removed in a future version.\n * @breaking-change 21.0.0\n */\nclass BaseTreeControl {\n  /** Saved data node for `expandAll` action. */\n  dataNodes;\n  /** A selection model with multi-selection to track expansion status. */\n  expansionModel = new SelectionModel(true);\n  /**\n   * Returns the identifier by which a dataNode should be tracked, should its\n   * reference change.\n   *\n   * Similar to trackBy for *ngFor\n   */\n  trackBy;\n  /** Get depth of a given data node, return the level number. This is for flat tree node. */\n  getLevel;\n  /**\n   * Whether the data node is expandable. Returns true if expandable.\n   * This is for flat tree node.\n   */\n  isExpandable;\n  /** Gets a stream that emits whenever the given data node's children change. */\n  getChildren;\n  /** Toggles one single data node's expanded/collapsed state. */\n  toggle(dataNode) {\n    this.expansionModel.toggle(this._trackByValue(dataNode));\n  }\n  /** Expands one single data node. */\n  expand(dataNode) {\n    this.expansionModel.select(this._trackByValue(dataNode));\n  }\n  /** Collapses one single data node. */\n  collapse(dataNode) {\n    this.expansionModel.deselect(this._trackByValue(dataNode));\n  }\n  /** Whether a given data node is expanded or not. Returns true if the data node is expanded. */\n  isExpanded(dataNode) {\n    return this.expansionModel.isSelected(this._trackByValue(dataNode));\n  }\n  /** Toggles a subtree rooted at `node` recursively. */\n  toggleDescendants(dataNode) {\n    this.expansionModel.isSelected(this._trackByValue(dataNode)) ? this.collapseDescendants(dataNode) : this.expandDescendants(dataNode);\n  }\n  /** Collapse all dataNodes in the tree. */\n  collapseAll() {\n    this.expansionModel.clear();\n  }\n  /** Expands a subtree rooted at given data node recursively. */\n  expandDescendants(dataNode) {\n    let toBeProcessed = [dataNode];\n    toBeProcessed.push(...this.getDescendants(dataNode));\n    this.expansionModel.select(...toBeProcessed.map(value => this._trackByValue(value)));\n  }\n  /** Collapses a subtree rooted at given data node recursively. */\n  collapseDescendants(dataNode) {\n    let toBeProcessed = [dataNode];\n    toBeProcessed.push(...this.getDescendants(dataNode));\n    this.expansionModel.deselect(...toBeProcessed.map(value => this._trackByValue(value)));\n  }\n  _trackByValue(value) {\n    return this.trackBy ? this.trackBy(value) : value;\n  }\n}\n\n/**\n * Flat tree control. Able to expand/collapse a subtree recursively for flattened tree.\n *\n * @deprecated Use one of levelAccessor or childrenAccessor instead. To be removed in a future\n * version.\n * @breaking-change 21.0.0\n */\nclass FlatTreeControl extends BaseTreeControl {\n  getLevel;\n  isExpandable;\n  options;\n  /** Construct with flat tree data node functions getLevel and isExpandable. */\n  constructor(getLevel, isExpandable, options) {\n    super();\n    this.getLevel = getLevel;\n    this.isExpandable = isExpandable;\n    this.options = options;\n    if (this.options) {\n      this.trackBy = this.options.trackBy;\n    }\n  }\n  /**\n   * Gets a list of the data node's subtree of descendent data nodes.\n   *\n   * To make this working, the `dataNodes` of the TreeControl must be flattened tree nodes\n   * with correct levels.\n   */\n  getDescendants(dataNode) {\n    const startIndex = this.dataNodes.indexOf(dataNode);\n    const results = [];\n    // Goes through flattened tree nodes in the `dataNodes` array, and get all descendants.\n    // The level of descendants of a tree node must be greater than the level of the given\n    // tree node.\n    // If we reach a node whose level is equal to the level of the tree node, we hit a sibling.\n    // If we reach a node whose level is greater than the level of the tree node, we hit a\n    // sibling of an ancestor.\n    for (let i = startIndex + 1; i < this.dataNodes.length && this.getLevel(dataNode) < this.getLevel(this.dataNodes[i]); i++) {\n      results.push(this.dataNodes[i]);\n    }\n    return results;\n  }\n  /**\n   * Expands all data nodes in the tree.\n   *\n   * To make this working, the `dataNodes` variable of the TreeControl must be set to all flattened\n   * data nodes of the tree.\n   */\n  expandAll() {\n    this.expansionModel.select(...this.dataNodes.map(node => this._trackByValue(node)));\n  }\n}\n\n/**\n * Nested tree control. Able to expand/collapse a subtree recursively for NestedNode type.\n *\n * @deprecated Use one of levelAccessor or childrenAccessor instead. To be removed in a future\n * version.\n * @breaking-change 21.0.0\n */\nclass NestedTreeControl extends BaseTreeControl {\n  getChildren;\n  options;\n  /** Construct with nested tree function getChildren. */\n  constructor(getChildren, options) {\n    super();\n    this.getChildren = getChildren;\n    this.options = options;\n    if (this.options) {\n      this.trackBy = this.options.trackBy;\n    }\n    if (this.options?.isExpandable) {\n      this.isExpandable = this.options.isExpandable;\n    }\n  }\n  /**\n   * Expands all dataNodes in the tree.\n   *\n   * To make this working, the `dataNodes` variable of the TreeControl must be set to all root level\n   * data nodes of the tree.\n   */\n  expandAll() {\n    this.expansionModel.clear();\n    const allNodes = this.dataNodes.reduce((accumulator, dataNode) => [...accumulator, ...this.getDescendants(dataNode), dataNode], []);\n    this.expansionModel.select(...allNodes.map(node => this._trackByValue(node)));\n  }\n  /** Gets a list of descendant dataNodes of a subtree rooted at given data node recursively. */\n  getDescendants(dataNode) {\n    const descendants = [];\n    this._getDescendants(descendants, dataNode);\n    // Remove the node itself\n    return descendants.splice(1);\n  }\n  /** A helper function to get descendants recursively. */\n  _getDescendants(descendants, dataNode) {\n    descendants.push(dataNode);\n    const childrenNodes = this.getChildren(dataNode);\n    if (Array.isArray(childrenNodes)) {\n      childrenNodes.forEach(child => this._getDescendants(descendants, child));\n    } else if (isObservable(childrenNodes)) {\n      // TypeScript as of version 3.5 doesn't seem to treat `Boolean` like a function that\n      // returns a `boolean` specifically in the context of `filter`, so we manually clarify that.\n      childrenNodes.pipe(take(1), filter(Boolean)).subscribe(children => {\n        for (const child of children) {\n          this._getDescendants(descendants, child);\n        }\n      });\n    }\n  }\n}\n\n/**\n * Injection token used to provide a `CdkTreeNode` to its outlet.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst CDK_TREE_NODE_OUTLET_NODE = new InjectionToken('CDK_TREE_NODE_OUTLET_NODE');\n/**\n * Outlet for nested CdkNode. Put `[cdkTreeNodeOutlet]` on a tag to place children dataNodes\n * inside the outlet.\n */\nclass CdkTreeNodeOutlet {\n  viewContainer = inject(ViewContainerRef);\n  _node = inject(CDK_TREE_NODE_OUTLET_NODE, {\n    optional: true\n  });\n  constructor() {}\n  static ɵfac = function CdkTreeNodeOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTreeNodeOutlet)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkTreeNodeOutlet,\n    selectors: [[\"\", \"cdkTreeNodeOutlet\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNodeOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTreeNodeOutlet]'\n    }]\n  }], () => [], null);\n})();\n\n/** Context provided to the tree node component. */\nclass CdkTreeNodeOutletContext {\n  /** Data for the node. */\n  $implicit;\n  /** Depth of the node. */\n  level;\n  /** Index location of the node. */\n  index;\n  /** Length of the number of total dataNodes. */\n  count;\n  constructor(data) {\n    this.$implicit = data;\n  }\n}\n/**\n * Data node definition for the CdkTree.\n * Captures the node's template and a when predicate that describes when this node should be used.\n */\nclass CdkTreeNodeDef {\n  /** @docs-private */\n  template = inject(TemplateRef);\n  /**\n   * Function that should return true if this node template should be used for the provided node\n   * data and index. If left undefined, this node will be considered the default node template to\n   * use when no other when functions return true for the data.\n   * For every node, there must be at least one when function that passes or an undefined to\n   * default.\n   */\n  when;\n  constructor() {}\n  static ɵfac = function CdkTreeNodeDef_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTreeNodeDef)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkTreeNodeDef,\n    selectors: [[\"\", \"cdkTreeNodeDef\", \"\"]],\n    inputs: {\n      when: [0, \"cdkTreeNodeDefWhen\", \"when\"]\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNodeDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTreeNodeDef]',\n      inputs: [{\n        name: 'when',\n        alias: 'cdkTreeNodeDefWhen'\n      }]\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Returns an error to be thrown when there is no usable data.\n * @docs-private\n */\nfunction getTreeNoValidDataSourceError() {\n  return Error(`A valid data source must be provided.`);\n}\n/**\n * Returns an error to be thrown when there are multiple nodes that are missing a when function.\n * @docs-private\n */\nfunction getTreeMultipleDefaultNodeDefsError() {\n  return Error(`There can only be one default row without a when predicate function.`);\n}\n/**\n * Returns an error to be thrown when there are no matching node defs for a particular set of data.\n * @docs-private\n */\nfunction getTreeMissingMatchingNodeDefError() {\n  return Error(`Could not find a matching node definition for the provided node data.`);\n}\n/**\n * Returns an error to be thrown when there is no tree control.\n * @docs-private\n */\nfunction getTreeControlMissingError() {\n  return Error(`Could not find a tree control, levelAccessor, or childrenAccessor for the tree.`);\n}\n/**\n * Returns an error to be thrown when there are multiple ways of specifying children or level\n * provided to the tree.\n * @docs-private\n */\nfunction getMultipleTreeControlsError() {\n  return Error(`More than one of tree control, levelAccessor, or childrenAccessor were provided.`);\n}\n\n/**\n * CDK tree component that connects with a data source to retrieve data of type `T` and renders\n * dataNodes with hierarchy. Updates the dataNodes when new data is provided by the data source.\n */\nclass CdkTree {\n  _differs = inject(IterableDiffers);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _elementRef = inject(ElementRef);\n  _dir = inject(Directionality);\n  /** Subject that emits when the component has been destroyed. */\n  _onDestroy = new Subject();\n  /** Differ used to find the changes in the data provided by the data source. */\n  _dataDiffer;\n  /** Stores the node definition that does not have a when predicate. */\n  _defaultNodeDef;\n  /** Data subscription */\n  _dataSubscription;\n  /** Level of nodes */\n  _levels = new Map();\n  /** The immediate parents for a node. This is `null` if there is no parent. */\n  _parents = new Map();\n  /**\n   * Nodes grouped into each set, which is a list of nodes displayed together in the DOM.\n   *\n   * Lookup key is the parent of a set. Root nodes have key of null.\n   *\n   * Values is a 'set' of tree nodes. Each tree node maps to a treeitem element. Sets are in the\n   * order that it is rendered. Each set maps directly to aria-posinset and aria-setsize attributes.\n   */\n  _ariaSets = new Map();\n  /**\n   * Provides a stream containing the latest data array to render. Influenced by the tree's\n   * stream of view window (what dataNodes are currently on screen).\n   * Data source can be an observable of data array, or a data array to render.\n   */\n  get dataSource() {\n    return this._dataSource;\n  }\n  set dataSource(dataSource) {\n    if (this._dataSource !== dataSource) {\n      this._switchDataSource(dataSource);\n    }\n  }\n  _dataSource;\n  /**\n   * The tree controller\n   *\n   * @deprecated Use one of `levelAccessor` or `childrenAccessor` instead. To be removed in a\n   * future version.\n   * @breaking-change 21.0.0\n   */\n  treeControl;\n  /**\n   * Given a data node, determines what tree level the node is at.\n   *\n   * One of levelAccessor or childrenAccessor must be specified, not both.\n   * This is enforced at run-time.\n   */\n  levelAccessor;\n  /**\n   * Given a data node, determines what the children of that node are.\n   *\n   * One of levelAccessor or childrenAccessor must be specified, not both.\n   * This is enforced at run-time.\n   */\n  childrenAccessor;\n  /**\n   * Tracking function that will be used to check the differences in data changes. Used similarly\n   * to `ngFor` `trackBy` function. Optimize node operations by identifying a node based on its data\n   * relative to the function to know if a node should be added/removed/moved.\n   * Accepts a function that takes two parameters, `index` and `item`.\n   */\n  trackBy;\n  /**\n   * Given a data node, determines the key by which we determine whether or not this node is expanded.\n   */\n  expansionKey;\n  // Outlets within the tree's template where the dataNodes will be inserted.\n  _nodeOutlet;\n  /** The tree node template for the tree */\n  _nodeDefs;\n  // TODO(tinayuangao): Setup a listener for scrolling, emit the calculated view to viewChange.\n  //     Remove the MAX_VALUE in viewChange\n  /**\n   * Stream containing the latest information on what rows are being displayed on screen.\n   * Can be used by the data source to as a heuristic of what data should be provided.\n   */\n  viewChange = new BehaviorSubject({\n    start: 0,\n    end: Number.MAX_VALUE\n  });\n  /** Keep track of which nodes are expanded. */\n  _expansionModel;\n  /**\n   * Maintain a synchronous cache of flattened data nodes. This will only be\n   * populated after initial render, and in certain cases, will be delayed due to\n   * relying on Observable `getChildren` calls.\n   */\n  _flattenedNodes = new BehaviorSubject([]);\n  /** The automatically determined node type for the tree. */\n  _nodeType = new BehaviorSubject(null);\n  /** The mapping between data and the node that is rendered. */\n  _nodes = new BehaviorSubject(new Map());\n  /**\n   * Synchronous cache of nodes for the `TreeKeyManager`. This is separate\n   * from `_flattenedNodes` so they can be independently updated at different\n   * times.\n   */\n  _keyManagerNodes = new BehaviorSubject([]);\n  _keyManagerFactory = inject(TREE_KEY_MANAGER);\n  /** The key manager for this tree. Handles focus and activation based on user keyboard input. */\n  _keyManager;\n  _viewInit = false;\n  constructor() {}\n  ngAfterContentInit() {\n    this._initializeKeyManager();\n  }\n  ngAfterContentChecked() {\n    this._updateDefaultNodeDefinition();\n    this._subscribeToDataChanges();\n  }\n  ngOnDestroy() {\n    this._nodeOutlet.viewContainer.clear();\n    this.viewChange.complete();\n    this._onDestroy.next();\n    this._onDestroy.complete();\n    if (this._dataSource && typeof this._dataSource.disconnect === 'function') {\n      this.dataSource.disconnect(this);\n    }\n    if (this._dataSubscription) {\n      this._dataSubscription.unsubscribe();\n      this._dataSubscription = null;\n    }\n    // In certain tests, the tree might be destroyed before this is initialized\n    // in `ngAfterContentInit`.\n    this._keyManager?.destroy();\n  }\n  ngOnInit() {\n    this._checkTreeControlUsage();\n    this._initializeDataDiffer();\n  }\n  ngAfterViewInit() {\n    this._viewInit = true;\n  }\n  _updateDefaultNodeDefinition() {\n    const defaultNodeDefs = this._nodeDefs.filter(def => !def.when);\n    if (defaultNodeDefs.length > 1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeMultipleDefaultNodeDefsError();\n    }\n    this._defaultNodeDef = defaultNodeDefs[0];\n  }\n  /**\n   * Sets the node type for the tree, if it hasn't been set yet.\n   *\n   * This will be called by the first node that's rendered in order for the tree\n   * to determine what data transformations are required.\n   */\n  _setNodeTypeIfUnset(newType) {\n    const currentType = this._nodeType.value;\n    if (currentType === null) {\n      this._nodeType.next(newType);\n    } else if ((typeof ngDevMode === 'undefined' || ngDevMode) && currentType !== newType) {\n      console.warn(`Tree is using conflicting node types which can cause unexpected behavior. ` + `Please use tree nodes of the same type (e.g. only flat or only nested). ` + `Current node type: \"${currentType}\", new node type \"${newType}\".`);\n    }\n  }\n  /**\n   * Switch to the provided data source by resetting the data and unsubscribing from the current\n   * render change subscription if one exists. If the data source is null, interpret this by\n   * clearing the node outlet. Otherwise start listening for new data.\n   */\n  _switchDataSource(dataSource) {\n    if (this._dataSource && typeof this._dataSource.disconnect === 'function') {\n      this.dataSource.disconnect(this);\n    }\n    if (this._dataSubscription) {\n      this._dataSubscription.unsubscribe();\n      this._dataSubscription = null;\n    }\n    // Remove the all dataNodes if there is now no data source\n    if (!dataSource) {\n      this._nodeOutlet.viewContainer.clear();\n    }\n    this._dataSource = dataSource;\n    if (this._nodeDefs) {\n      this._subscribeToDataChanges();\n    }\n  }\n  _getExpansionModel() {\n    if (!this.treeControl) {\n      this._expansionModel ??= new SelectionModel(true);\n      return this._expansionModel;\n    }\n    return this.treeControl.expansionModel;\n  }\n  /** Set up a subscription for the data provided by the data source. */\n  _subscribeToDataChanges() {\n    if (this._dataSubscription) {\n      return;\n    }\n    let dataStream;\n    if (isDataSource(this._dataSource)) {\n      dataStream = this._dataSource.connect(this);\n    } else if (isObservable(this._dataSource)) {\n      dataStream = this._dataSource;\n    } else if (Array.isArray(this._dataSource)) {\n      dataStream = of(this._dataSource);\n    }\n    if (!dataStream) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        throw getTreeNoValidDataSourceError();\n      }\n      return;\n    }\n    this._dataSubscription = this._getRenderData(dataStream).pipe(takeUntil(this._onDestroy)).subscribe(renderingData => {\n      this._renderDataChanges(renderingData);\n    });\n  }\n  /** Given an Observable containing a stream of the raw data, returns an Observable containing the RenderingData */\n  _getRenderData(dataStream) {\n    const expansionModel = this._getExpansionModel();\n    return combineLatest([dataStream, this._nodeType,\n    // We don't use the expansion data directly, however we add it here to essentially\n    // trigger data rendering when expansion changes occur.\n    expansionModel.changed.pipe(startWith(null), tap(expansionChanges => {\n      this._emitExpansionChanges(expansionChanges);\n    }))]).pipe(switchMap(([data, nodeType]) => {\n      if (nodeType === null) {\n        return of({\n          renderNodes: data,\n          flattenedNodes: null,\n          nodeType\n        });\n      }\n      // If we're here, then we know what our node type is, and therefore can\n      // perform our usual rendering pipeline, which necessitates converting the data\n      return this._computeRenderingData(data, nodeType).pipe(map(convertedData => ({\n        ...convertedData,\n        nodeType\n      })));\n    }));\n  }\n  _renderDataChanges(data) {\n    if (data.nodeType === null) {\n      this.renderNodeChanges(data.renderNodes);\n      return;\n    }\n    // If we're here, then we know what our node type is, and therefore can\n    // perform our usual rendering pipeline.\n    this._updateCachedData(data.flattenedNodes);\n    this.renderNodeChanges(data.renderNodes);\n    this._updateKeyManagerItems(data.flattenedNodes);\n  }\n  _emitExpansionChanges(expansionChanges) {\n    if (!expansionChanges) {\n      return;\n    }\n    const nodes = this._nodes.value;\n    for (const added of expansionChanges.added) {\n      const node = nodes.get(added);\n      node?._emitExpansionState(true);\n    }\n    for (const removed of expansionChanges.removed) {\n      const node = nodes.get(removed);\n      node?._emitExpansionState(false);\n    }\n  }\n  _initializeKeyManager() {\n    const items = combineLatest([this._keyManagerNodes, this._nodes]).pipe(map(([keyManagerNodes, renderNodes]) => keyManagerNodes.reduce((items, data) => {\n      const node = renderNodes.get(this._getExpansionKey(data));\n      if (node) {\n        items.push(node);\n      }\n      return items;\n    }, [])));\n    const keyManagerOptions = {\n      trackBy: node => this._getExpansionKey(node.data),\n      skipPredicate: node => !!node.isDisabled,\n      typeAheadDebounceInterval: true,\n      horizontalOrientation: this._dir.value\n    };\n    this._keyManager = this._keyManagerFactory(items, keyManagerOptions);\n  }\n  _initializeDataDiffer() {\n    // Provide a default trackBy based on `_getExpansionKey` if one isn't provided.\n    const trackBy = this.trackBy ?? ((_index, item) => this._getExpansionKey(item));\n    this._dataDiffer = this._differs.find([]).create(trackBy);\n  }\n  _checkTreeControlUsage() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // Verify that Tree follows API contract of using one of TreeControl, levelAccessor or\n      // childrenAccessor. Throw an appropriate error if contract is not met.\n      let numTreeControls = 0;\n      if (this.treeControl) {\n        numTreeControls++;\n      }\n      if (this.levelAccessor) {\n        numTreeControls++;\n      }\n      if (this.childrenAccessor) {\n        numTreeControls++;\n      }\n      if (!numTreeControls) {\n        throw getTreeControlMissingError();\n      } else if (numTreeControls > 1) {\n        throw getMultipleTreeControlsError();\n      }\n    }\n  }\n  /** Check for changes made in the data and render each change (node added/removed/moved). */\n  renderNodeChanges(data, dataDiffer = this._dataDiffer, viewContainer = this._nodeOutlet.viewContainer, parentData) {\n    const changes = dataDiffer.diff(data);\n    // Some tree consumers expect change detection to propagate to nodes\n    // even when the array itself hasn't changed; we explicitly detect changes\n    // anyways in order for nodes to update their data.\n    //\n    // However, if change detection is called while the component's view is\n    // still initing, then the order of child views initing will be incorrect;\n    // to prevent this, we only exit early if the view hasn't initialized yet.\n    if (!changes && !this._viewInit) {\n      return;\n    }\n    changes?.forEachOperation((item, adjustedPreviousIndex, currentIndex) => {\n      if (item.previousIndex == null) {\n        this.insertNode(data[currentIndex], currentIndex, viewContainer, parentData);\n      } else if (currentIndex == null) {\n        viewContainer.remove(adjustedPreviousIndex);\n      } else {\n        const view = viewContainer.get(adjustedPreviousIndex);\n        viewContainer.move(view, currentIndex);\n      }\n    });\n    // If the data itself changes, but keeps the same trackBy, we need to update the templates'\n    // context to reflect the new object.\n    changes?.forEachIdentityChange(record => {\n      const newData = record.item;\n      if (record.currentIndex != undefined) {\n        const view = viewContainer.get(record.currentIndex);\n        view.context.$implicit = newData;\n      }\n    });\n    // Note: we only `detectChanges` from a top-level call, otherwise we risk overflowing\n    // the call stack since this method is called recursively (see #29733.)\n    // TODO: change to `this._changeDetectorRef.markForCheck()`,\n    // or just switch this component to use signals.\n    if (parentData) {\n      this._changeDetectorRef.markForCheck();\n    } else {\n      this._changeDetectorRef.detectChanges();\n    }\n  }\n  /**\n   * Finds the matching node definition that should be used for this node data. If there is only\n   * one node definition, it is returned. Otherwise, find the node definition that has a when\n   * predicate that returns true with the data. If none return true, return the default node\n   * definition.\n   */\n  _getNodeDef(data, i) {\n    if (this._nodeDefs.length === 1) {\n      return this._nodeDefs.first;\n    }\n    const nodeDef = this._nodeDefs.find(def => def.when && def.when(i, data)) || this._defaultNodeDef;\n    if (!nodeDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeMissingMatchingNodeDefError();\n    }\n    return nodeDef;\n  }\n  /**\n   * Create the embedded view for the data node template and place it in the correct index location\n   * within the data node view container.\n   */\n  insertNode(nodeData, index, viewContainer, parentData) {\n    const levelAccessor = this._getLevelAccessor();\n    const node = this._getNodeDef(nodeData, index);\n    const key = this._getExpansionKey(nodeData);\n    // Node context that will be provided to created embedded view\n    const context = new CdkTreeNodeOutletContext(nodeData);\n    context.index = index;\n    parentData ??= this._parents.get(key) ?? undefined;\n    // If the tree is flat tree, then use the `getLevel` function in flat tree control\n    // Otherwise, use the level of parent node.\n    if (levelAccessor) {\n      context.level = levelAccessor(nodeData);\n    } else if (parentData !== undefined && this._levels.has(this._getExpansionKey(parentData))) {\n      context.level = this._levels.get(this._getExpansionKey(parentData)) + 1;\n    } else {\n      context.level = 0;\n    }\n    this._levels.set(key, context.level);\n    // Use default tree nodeOutlet, or nested node's nodeOutlet\n    const container = viewContainer ? viewContainer : this._nodeOutlet.viewContainer;\n    container.createEmbeddedView(node.template, context, index);\n    // Set the data to just created `CdkTreeNode`.\n    // The `CdkTreeNode` created from `createEmbeddedView` will be saved in static variable\n    //     `mostRecentTreeNode`. We get it from static variable and pass the node data to it.\n    if (CdkTreeNode.mostRecentTreeNode) {\n      CdkTreeNode.mostRecentTreeNode.data = nodeData;\n    }\n  }\n  /** Whether the data node is expanded or collapsed. Returns true if it's expanded. */\n  isExpanded(dataNode) {\n    return !!(this.treeControl?.isExpanded(dataNode) || this._expansionModel?.isSelected(this._getExpansionKey(dataNode)));\n  }\n  /** If the data node is currently expanded, collapse it. Otherwise, expand it. */\n  toggle(dataNode) {\n    if (this.treeControl) {\n      this.treeControl.toggle(dataNode);\n    } else if (this._expansionModel) {\n      this._expansionModel.toggle(this._getExpansionKey(dataNode));\n    }\n  }\n  /** Expand the data node. If it is already expanded, does nothing. */\n  expand(dataNode) {\n    if (this.treeControl) {\n      this.treeControl.expand(dataNode);\n    } else if (this._expansionModel) {\n      this._expansionModel.select(this._getExpansionKey(dataNode));\n    }\n  }\n  /** Collapse the data node. If it is already collapsed, does nothing. */\n  collapse(dataNode) {\n    if (this.treeControl) {\n      this.treeControl.collapse(dataNode);\n    } else if (this._expansionModel) {\n      this._expansionModel.deselect(this._getExpansionKey(dataNode));\n    }\n  }\n  /**\n   * If the data node is currently expanded, collapse it and all its descendants.\n   * Otherwise, expand it and all its descendants.\n   */\n  toggleDescendants(dataNode) {\n    if (this.treeControl) {\n      this.treeControl.toggleDescendants(dataNode);\n    } else if (this._expansionModel) {\n      if (this.isExpanded(dataNode)) {\n        this.collapseDescendants(dataNode);\n      } else {\n        this.expandDescendants(dataNode);\n      }\n    }\n  }\n  /**\n   * Expand the data node and all its descendants. If they are already expanded, does nothing.\n   */\n  expandDescendants(dataNode) {\n    if (this.treeControl) {\n      this.treeControl.expandDescendants(dataNode);\n    } else if (this._expansionModel) {\n      const expansionModel = this._expansionModel;\n      expansionModel.select(this._getExpansionKey(dataNode));\n      this._getDescendants(dataNode).pipe(take(1), takeUntil(this._onDestroy)).subscribe(children => {\n        expansionModel.select(...children.map(child => this._getExpansionKey(child)));\n      });\n    }\n  }\n  /** Collapse the data node and all its descendants. If it is already collapsed, does nothing. */\n  collapseDescendants(dataNode) {\n    if (this.treeControl) {\n      this.treeControl.collapseDescendants(dataNode);\n    } else if (this._expansionModel) {\n      const expansionModel = this._expansionModel;\n      expansionModel.deselect(this._getExpansionKey(dataNode));\n      this._getDescendants(dataNode).pipe(take(1), takeUntil(this._onDestroy)).subscribe(children => {\n        expansionModel.deselect(...children.map(child => this._getExpansionKey(child)));\n      });\n    }\n  }\n  /** Expands all data nodes in the tree. */\n  expandAll() {\n    if (this.treeControl) {\n      this.treeControl.expandAll();\n    } else if (this._expansionModel) {\n      this._forEachExpansionKey(keys => this._expansionModel?.select(...keys));\n    }\n  }\n  /** Collapse all data nodes in the tree. */\n  collapseAll() {\n    if (this.treeControl) {\n      this.treeControl.collapseAll();\n    } else if (this._expansionModel) {\n      this._forEachExpansionKey(keys => this._expansionModel?.deselect(...keys));\n    }\n  }\n  /** Level accessor, used for compatibility between the old Tree and new Tree */\n  _getLevelAccessor() {\n    return this.treeControl?.getLevel?.bind(this.treeControl) ?? this.levelAccessor;\n  }\n  /** Children accessor, used for compatibility between the old Tree and new Tree */\n  _getChildrenAccessor() {\n    return this.treeControl?.getChildren?.bind(this.treeControl) ?? this.childrenAccessor;\n  }\n  /**\n   * Gets the direct children of a node; used for compatibility between the old tree and the\n   * new tree.\n   */\n  _getDirectChildren(dataNode) {\n    const levelAccessor = this._getLevelAccessor();\n    const expansionModel = this._expansionModel ?? this.treeControl?.expansionModel;\n    if (!expansionModel) {\n      return of([]);\n    }\n    const key = this._getExpansionKey(dataNode);\n    const isExpanded = expansionModel.changed.pipe(switchMap(changes => {\n      if (changes.added.includes(key)) {\n        return of(true);\n      } else if (changes.removed.includes(key)) {\n        return of(false);\n      }\n      return EMPTY;\n    }), startWith(this.isExpanded(dataNode)));\n    if (levelAccessor) {\n      return combineLatest([isExpanded, this._flattenedNodes]).pipe(map(([expanded, flattenedNodes]) => {\n        if (!expanded) {\n          return [];\n        }\n        return this._findChildrenByLevel(levelAccessor, flattenedNodes, dataNode, 1);\n      }));\n    }\n    const childrenAccessor = this._getChildrenAccessor();\n    if (childrenAccessor) {\n      return coerceObservable(childrenAccessor(dataNode) ?? []);\n    }\n    throw getTreeControlMissingError();\n  }\n  /**\n   * Given the list of flattened nodes, the level accessor, and the level range within\n   * which to consider children, finds the children for a given node.\n   *\n   * For example, for direct children, `levelDelta` would be 1. For all descendants,\n   * `levelDelta` would be Infinity.\n   */\n  _findChildrenByLevel(levelAccessor, flattenedNodes, dataNode, levelDelta) {\n    const key = this._getExpansionKey(dataNode);\n    const startIndex = flattenedNodes.findIndex(node => this._getExpansionKey(node) === key);\n    const dataNodeLevel = levelAccessor(dataNode);\n    const expectedLevel = dataNodeLevel + levelDelta;\n    const results = [];\n    // Goes through flattened tree nodes in the `flattenedNodes` array, and get all\n    // descendants within a certain level range.\n    //\n    // If we reach a node whose level is equal to or less than the level of the tree node,\n    // we hit a sibling or parent's sibling, and should stop.\n    for (let i = startIndex + 1; i < flattenedNodes.length; i++) {\n      const currentLevel = levelAccessor(flattenedNodes[i]);\n      if (currentLevel <= dataNodeLevel) {\n        break;\n      }\n      if (currentLevel <= expectedLevel) {\n        results.push(flattenedNodes[i]);\n      }\n    }\n    return results;\n  }\n  /**\n   * Adds the specified node component to the tree's internal registry.\n   *\n   * This primarily facilitates keyboard navigation.\n   */\n  _registerNode(node) {\n    this._nodes.value.set(this._getExpansionKey(node.data), node);\n    this._nodes.next(this._nodes.value);\n  }\n  /** Removes the specified node component from the tree's internal registry. */\n  _unregisterNode(node) {\n    this._nodes.value.delete(this._getExpansionKey(node.data));\n    this._nodes.next(this._nodes.value);\n  }\n  /**\n   * For the given node, determine the level where this node appears in the tree.\n   *\n   * This is intended to be used for `aria-level` but is 0-indexed.\n   */\n  _getLevel(node) {\n    return this._levels.get(this._getExpansionKey(node));\n  }\n  /**\n   * For the given node, determine the size of the parent's child set.\n   *\n   * This is intended to be used for `aria-setsize`.\n   */\n  _getSetSize(dataNode) {\n    const set = this._getAriaSet(dataNode);\n    return set.length;\n  }\n  /**\n   * For the given node, determine the index (starting from 1) of the node in its parent's child set.\n   *\n   * This is intended to be used for `aria-posinset`.\n   */\n  _getPositionInSet(dataNode) {\n    const set = this._getAriaSet(dataNode);\n    const key = this._getExpansionKey(dataNode);\n    return set.findIndex(node => this._getExpansionKey(node) === key) + 1;\n  }\n  /** Given a CdkTreeNode, gets the node that renders that node's parent's data. */\n  _getNodeParent(node) {\n    const parent = this._parents.get(this._getExpansionKey(node.data));\n    return parent && this._nodes.value.get(this._getExpansionKey(parent));\n  }\n  /** Given a CdkTreeNode, gets the nodes that renders that node's child data. */\n  _getNodeChildren(node) {\n    return this._getDirectChildren(node.data).pipe(map(children => children.reduce((nodes, child) => {\n      const value = this._nodes.value.get(this._getExpansionKey(child));\n      if (value) {\n        nodes.push(value);\n      }\n      return nodes;\n    }, [])));\n  }\n  /** `keydown` event handler; this just passes the event to the `TreeKeyManager`. */\n  _sendKeydownToKeyManager(event) {\n    // Only handle events directly on the tree or directly on one of the nodes, otherwise\n    // we risk interfering with events in the projected content (see #29828).\n    if (event.target === this._elementRef.nativeElement) {\n      this._keyManager.onKeydown(event);\n    } else {\n      const nodes = this._nodes.getValue();\n      for (const [, node] of nodes) {\n        if (event.target === node._elementRef.nativeElement) {\n          this._keyManager.onKeydown(event);\n          break;\n        }\n      }\n    }\n  }\n  /** Gets all nested descendants of a given node. */\n  _getDescendants(dataNode) {\n    if (this.treeControl) {\n      return of(this.treeControl.getDescendants(dataNode));\n    }\n    if (this.levelAccessor) {\n      const results = this._findChildrenByLevel(this.levelAccessor, this._flattenedNodes.value, dataNode, Infinity);\n      return of(results);\n    }\n    if (this.childrenAccessor) {\n      return this._getAllChildrenRecursively(dataNode).pipe(reduce((allChildren, nextChildren) => {\n        allChildren.push(...nextChildren);\n        return allChildren;\n      }, []));\n    }\n    throw getTreeControlMissingError();\n  }\n  /**\n   * Gets all children and sub-children of the provided node.\n   *\n   * This will emit multiple times, in the order that the children will appear\n   * in the tree, and can be combined with a `reduce` operator.\n   */\n  _getAllChildrenRecursively(dataNode) {\n    if (!this.childrenAccessor) {\n      return of([]);\n    }\n    return coerceObservable(this.childrenAccessor(dataNode)).pipe(take(1), switchMap(children => {\n      // Here, we cache the parents of a particular child so that we can compute the levels.\n      for (const child of children) {\n        this._parents.set(this._getExpansionKey(child), dataNode);\n      }\n      return of(...children).pipe(concatMap(child => concat(of([child]), this._getAllChildrenRecursively(child))));\n    }));\n  }\n  _getExpansionKey(dataNode) {\n    // In the case that a key accessor function was not provided by the\n    // tree user, we'll default to using the node object itself as the key.\n    //\n    // This cast is safe since:\n    // - if an expansionKey is provided, TS will infer the type of K to be\n    //   the return type.\n    // - if it's not, then K will be defaulted to T.\n    return this.expansionKey?.(dataNode) ?? dataNode;\n  }\n  _getAriaSet(node) {\n    const key = this._getExpansionKey(node);\n    const parent = this._parents.get(key);\n    const parentKey = parent ? this._getExpansionKey(parent) : null;\n    const set = this._ariaSets.get(parentKey);\n    return set ?? [node];\n  }\n  /**\n   * Finds the parent for the given node. If this is a root node, this\n   * returns null. If we're unable to determine the parent, for example,\n   * if we don't have cached node data, this returns undefined.\n   */\n  _findParentForNode(node, index, cachedNodes) {\n    // In all cases, we have a mapping from node to level; all we need to do here is backtrack in\n    // our flattened list of nodes to determine the first node that's of a level lower than the\n    // provided node.\n    if (!cachedNodes.length) {\n      return null;\n    }\n    const currentLevel = this._levels.get(this._getExpansionKey(node)) ?? 0;\n    for (let parentIndex = index - 1; parentIndex >= 0; parentIndex--) {\n      const parentNode = cachedNodes[parentIndex];\n      const parentLevel = this._levels.get(this._getExpansionKey(parentNode)) ?? 0;\n      if (parentLevel < currentLevel) {\n        return parentNode;\n      }\n    }\n    return null;\n  }\n  /**\n   * Given a set of root nodes and the current node level, flattens any nested\n   * nodes into a single array.\n   *\n   * If any nodes are not expanded, then their children will not be added into the array.\n   * This will still traverse all nested children in order to build up our internal data\n   * models, but will not include them in the returned array.\n   */\n  _flattenNestedNodesWithExpansion(nodes, level = 0) {\n    const childrenAccessor = this._getChildrenAccessor();\n    // If we're using a level accessor, we don't need to flatten anything.\n    if (!childrenAccessor) {\n      return of([...nodes]);\n    }\n    return of(...nodes).pipe(concatMap(node => {\n      const parentKey = this._getExpansionKey(node);\n      if (!this._parents.has(parentKey)) {\n        this._parents.set(parentKey, null);\n      }\n      this._levels.set(parentKey, level);\n      const children = coerceObservable(childrenAccessor(node));\n      return concat(of([node]), children.pipe(take(1), tap(childNodes => {\n        this._ariaSets.set(parentKey, [...(childNodes ?? [])]);\n        for (const child of childNodes ?? []) {\n          const childKey = this._getExpansionKey(child);\n          this._parents.set(childKey, node);\n          this._levels.set(childKey, level + 1);\n        }\n      }), switchMap(childNodes => {\n        if (!childNodes) {\n          return of([]);\n        }\n        return this._flattenNestedNodesWithExpansion(childNodes, level + 1).pipe(map(nestedNodes => this.isExpanded(node) ? nestedNodes : []));\n      })));\n    }), reduce((results, children) => {\n      results.push(...children);\n      return results;\n    }, []));\n  }\n  /**\n   * Converts children for certain tree configurations.\n   *\n   * This also computes parent, level, and group data.\n   */\n  _computeRenderingData(nodes, nodeType) {\n    // The only situations where we have to convert children types is when\n    // they're mismatched; i.e. if the tree is using a childrenAccessor and the\n    // nodes are flat, or if the tree is using a levelAccessor and the nodes are\n    // nested.\n    if (this.childrenAccessor && nodeType === 'flat') {\n      // clear previously generated data so we don't keep end up retaining data overtime causing\n      // memory leaks.\n      this._clearPreviousCache();\n      // This flattens children into a single array.\n      this._ariaSets.set(null, [...nodes]);\n      return this._flattenNestedNodesWithExpansion(nodes).pipe(map(flattenedNodes => ({\n        renderNodes: flattenedNodes,\n        flattenedNodes\n      })));\n    } else if (this.levelAccessor && nodeType === 'nested') {\n      // In the nested case, we only look for root nodes. The CdkNestedNode\n      // itself will handle rendering each individual node's children.\n      const levelAccessor = this.levelAccessor;\n      return of(nodes.filter(node => levelAccessor(node) === 0)).pipe(map(rootNodes => ({\n        renderNodes: rootNodes,\n        flattenedNodes: nodes\n      })), tap(({\n        flattenedNodes\n      }) => {\n        this._calculateParents(flattenedNodes);\n      }));\n    } else if (nodeType === 'flat') {\n      // In the case of a TreeControl, we know that the node type matches up\n      // with the TreeControl, and so no conversions are necessary. Otherwise,\n      // we've already confirmed that the data model matches up with the\n      // desired node type here.\n      return of({\n        renderNodes: nodes,\n        flattenedNodes: nodes\n      }).pipe(tap(({\n        flattenedNodes\n      }) => {\n        this._calculateParents(flattenedNodes);\n      }));\n    } else {\n      // clear previously generated data so we don't keep end up retaining data overtime causing\n      // memory leaks.\n      this._clearPreviousCache();\n      // For nested nodes, we still need to perform the node flattening in order\n      // to maintain our caches for various tree operations.\n      this._ariaSets.set(null, [...nodes]);\n      return this._flattenNestedNodesWithExpansion(nodes).pipe(map(flattenedNodes => ({\n        renderNodes: nodes,\n        flattenedNodes\n      })));\n    }\n  }\n  _updateCachedData(flattenedNodes) {\n    this._flattenedNodes.next(flattenedNodes);\n  }\n  _updateKeyManagerItems(flattenedNodes) {\n    this._keyManagerNodes.next(flattenedNodes);\n  }\n  /** Traverse the flattened node data and compute parents, levels, and group data. */\n  _calculateParents(flattenedNodes) {\n    const levelAccessor = this._getLevelAccessor();\n    if (!levelAccessor) {\n      return;\n    }\n    // clear previously generated data so we don't keep end up retaining data overtime causing\n    // memory leaks.\n    this._clearPreviousCache();\n    for (let index = 0; index < flattenedNodes.length; index++) {\n      const dataNode = flattenedNodes[index];\n      const key = this._getExpansionKey(dataNode);\n      this._levels.set(key, levelAccessor(dataNode));\n      const parent = this._findParentForNode(dataNode, index, flattenedNodes);\n      this._parents.set(key, parent);\n      const parentKey = parent ? this._getExpansionKey(parent) : null;\n      const group = this._ariaSets.get(parentKey) ?? [];\n      group.splice(index, 0, dataNode);\n      this._ariaSets.set(parentKey, group);\n    }\n  }\n  /** Invokes a callback with all node expansion keys. */\n  _forEachExpansionKey(callback) {\n    const toToggle = [];\n    const observables = [];\n    this._nodes.value.forEach(node => {\n      toToggle.push(this._getExpansionKey(node.data));\n      observables.push(this._getDescendants(node.data));\n    });\n    if (observables.length > 0) {\n      combineLatest(observables).pipe(take(1), takeUntil(this._onDestroy)).subscribe(results => {\n        results.forEach(inner => inner.forEach(r => toToggle.push(this._getExpansionKey(r))));\n        callback(toToggle);\n      });\n    } else {\n      callback(toToggle);\n    }\n  }\n  /** Clears the maps we use to store parents, level & aria-sets in. */\n  _clearPreviousCache() {\n    this._parents.clear();\n    this._levels.clear();\n    this._ariaSets.clear();\n  }\n  static ɵfac = function CdkTree_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTree)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CdkTree,\n    selectors: [[\"cdk-tree\"]],\n    contentQueries: function CdkTree_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, CdkTreeNodeDef, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nodeDefs = _t);\n      }\n    },\n    viewQuery: function CdkTree_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(CdkTreeNodeOutlet, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nodeOutlet = _t.first);\n      }\n    },\n    hostAttrs: [\"role\", \"tree\", 1, \"cdk-tree\"],\n    hostBindings: function CdkTree_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown\", function CdkTree_keydown_HostBindingHandler($event) {\n          return ctx._sendKeydownToKeyManager($event);\n        });\n      }\n    },\n    inputs: {\n      dataSource: \"dataSource\",\n      treeControl: \"treeControl\",\n      levelAccessor: \"levelAccessor\",\n      childrenAccessor: \"childrenAccessor\",\n      trackBy: \"trackBy\",\n      expansionKey: \"expansionKey\"\n    },\n    exportAs: [\"cdkTree\"],\n    decls: 1,\n    vars: 0,\n    consts: [[\"cdkTreeNodeOutlet\", \"\"]],\n    template: function CdkTree_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainer(0, 0);\n      }\n    },\n    dependencies: [CdkTreeNodeOutlet],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTree, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-tree',\n      exportAs: 'cdkTree',\n      template: `<ng-container cdkTreeNodeOutlet></ng-container>`,\n      host: {\n        'class': 'cdk-tree',\n        'role': 'tree',\n        '(keydown)': '_sendKeydownToKeyManager($event)'\n      },\n      encapsulation: ViewEncapsulation.None,\n      // The \"OnPush\" status for the `CdkTree` component is effectively a noop, so we are removing it.\n      // The view for `CdkTree` consists entirely of templates declared in other views. As they are\n      // declared elsewhere, they are checked when their declaration points are checked.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [CdkTreeNodeOutlet]\n    }]\n  }], () => [], {\n    dataSource: [{\n      type: Input\n    }],\n    treeControl: [{\n      type: Input\n    }],\n    levelAccessor: [{\n      type: Input\n    }],\n    childrenAccessor: [{\n      type: Input\n    }],\n    trackBy: [{\n      type: Input\n    }],\n    expansionKey: [{\n      type: Input\n    }],\n    _nodeOutlet: [{\n      type: ViewChild,\n      args: [CdkTreeNodeOutlet, {\n        static: true\n      }]\n    }],\n    _nodeDefs: [{\n      type: ContentChildren,\n      args: [CdkTreeNodeDef, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }]\n  });\n})();\n/**\n * Tree node for CdkTree. It contains the data in the tree node.\n */\nclass CdkTreeNode {\n  _elementRef = inject(ElementRef);\n  _tree = inject(CdkTree);\n  _tabindex = -1;\n  _type = 'flat';\n  /**\n   * The role of the tree node.\n   *\n   * @deprecated This will be ignored; the tree will automatically determine the appropriate role\n   * for tree node. This input will be removed in a future version.\n   * @breaking-change 21.0.0\n   */\n  get role() {\n    return 'treeitem';\n  }\n  set role(_role) {\n    // ignore any role setting, we handle this internally.\n  }\n  /**\n   * Whether or not this node is expandable.\n   *\n   * If not using `FlatTreeControl`, or if `isExpandable` is not provided to\n   * `NestedTreeControl`, this should be provided for correct node a11y.\n   */\n  get isExpandable() {\n    return this._isExpandable();\n  }\n  set isExpandable(isExpandable) {\n    this._inputIsExpandable = isExpandable;\n    if (this.data && !this._isExpandable || !this._inputIsExpandable) {\n      return;\n    }\n    // If the node is being set to expandable, ensure that the status of the\n    // node is propagated\n    if (this._inputIsExpanded) {\n      this.expand();\n    } else if (this._inputIsExpanded === false) {\n      this.collapse();\n    }\n  }\n  get isExpanded() {\n    return this._tree.isExpanded(this._data);\n  }\n  set isExpanded(isExpanded) {\n    this._inputIsExpanded = isExpanded;\n    if (isExpanded) {\n      this.expand();\n    } else {\n      this.collapse();\n    }\n  }\n  /**\n   * Whether or not this node is disabled. If it's disabled, then the user won't be able to focus\n   * or activate this node.\n   */\n  isDisabled;\n  /**\n   * The text used to locate this item during typeahead. If not specified, the `textContent` will\n   * will be used.\n   */\n  typeaheadLabel;\n  getLabel() {\n    return this.typeaheadLabel || this._elementRef.nativeElement.textContent?.trim() || '';\n  }\n  /** This emits when the node has been programatically activated or activated by keyboard. */\n  activation = new EventEmitter();\n  /** This emits when the node's expansion status has been changed. */\n  expandedChange = new EventEmitter();\n  /**\n   * The most recently created `CdkTreeNode`. We save it in static variable so we can retrieve it\n   * in `CdkTree` and set the data to it.\n   */\n  static mostRecentTreeNode = null;\n  /** Subject that emits when the component has been destroyed. */\n  _destroyed = new Subject();\n  /** Emits when the node's data has changed. */\n  _dataChanges = new Subject();\n  _inputIsExpandable = false;\n  _inputIsExpanded = undefined;\n  /**\n   * Flag used to determine whether or not we should be focusing the actual element based on\n   * some user interaction (click or focus). On click, we don't forcibly focus the element\n   * since the click could trigger some other component that wants to grab its own focus\n   * (e.g. menu, dialog).\n   */\n  _shouldFocus = true;\n  _parentNodeAriaLevel;\n  /** The tree node's data. */\n  get data() {\n    return this._data;\n  }\n  set data(value) {\n    if (value !== this._data) {\n      this._data = value;\n      this._dataChanges.next();\n    }\n  }\n  _data;\n  /* If leaf node, return true to not assign aria-expanded attribute */\n  get isLeafNode() {\n    // If flat tree node data returns false for expandable property, it's a leaf node\n    if (this._tree.treeControl?.isExpandable !== undefined && !this._tree.treeControl.isExpandable(this._data)) {\n      return true;\n      // If nested tree node data returns 0 descendants, it's a leaf node\n    } else if (this._tree.treeControl?.isExpandable === undefined && this._tree.treeControl?.getDescendants(this._data).length === 0) {\n      return true;\n    }\n    return false;\n  }\n  get level() {\n    // If the tree has a levelAccessor, use it to get the level. Otherwise read the\n    // aria-level off the parent node and use it as the level for this node (note aria-level is\n    // 1-indexed, while this property is 0-indexed, so we don't need to increment).\n    return this._tree._getLevel(this._data) ?? this._parentNodeAriaLevel;\n  }\n  /** Determines if the tree node is expandable. */\n  _isExpandable() {\n    if (this._tree.treeControl) {\n      if (this.isLeafNode) {\n        return false;\n      }\n      // For compatibility with trees created using TreeControl before we added\n      // CdkTreeNode#isExpandable.\n      return true;\n    }\n    return this._inputIsExpandable;\n  }\n  /**\n   * Determines the value for `aria-expanded`.\n   *\n   * For non-expandable nodes, this is `null`.\n   */\n  _getAriaExpanded() {\n    if (!this._isExpandable()) {\n      return null;\n    }\n    return String(this.isExpanded);\n  }\n  /**\n   * Determines the size of this node's parent's child set.\n   *\n   * This is intended to be used for `aria-setsize`.\n   */\n  _getSetSize() {\n    return this._tree._getSetSize(this._data);\n  }\n  /**\n   * Determines the index (starting from 1) of this node in its parent's child set.\n   *\n   * This is intended to be used for `aria-posinset`.\n   */\n  _getPositionInSet() {\n    return this._tree._getPositionInSet(this._data);\n  }\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  constructor() {\n    CdkTreeNode.mostRecentTreeNode = this;\n  }\n  ngOnInit() {\n    this._parentNodeAriaLevel = getParentNodeAriaLevel(this._elementRef.nativeElement);\n    this._tree._getExpansionModel().changed.pipe(map(() => this.isExpanded), distinctUntilChanged(), takeUntil(this._destroyed)).subscribe(() => this._changeDetectorRef.markForCheck());\n    this._tree._setNodeTypeIfUnset(this._type);\n    this._tree._registerNode(this);\n  }\n  ngOnDestroy() {\n    // If this is the last tree node being destroyed,\n    // clear out the reference to avoid leaking memory.\n    if (CdkTreeNode.mostRecentTreeNode === this) {\n      CdkTreeNode.mostRecentTreeNode = null;\n    }\n    this._dataChanges.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  getParent() {\n    return this._tree._getNodeParent(this) ?? null;\n  }\n  getChildren() {\n    return this._tree._getNodeChildren(this);\n  }\n  /** Focuses this data node. Implemented for TreeKeyManagerItem. */\n  focus() {\n    this._tabindex = 0;\n    if (this._shouldFocus) {\n      this._elementRef.nativeElement.focus();\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Defocus this data node. */\n  unfocus() {\n    this._tabindex = -1;\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Emits an activation event. Implemented for TreeKeyManagerItem. */\n  activate() {\n    if (this.isDisabled) {\n      return;\n    }\n    this.activation.next(this._data);\n  }\n  /** Collapses this data node. Implemented for TreeKeyManagerItem. */\n  collapse() {\n    if (this.isExpandable) {\n      this._tree.collapse(this._data);\n    }\n  }\n  /** Expands this data node. Implemented for TreeKeyManagerItem. */\n  expand() {\n    if (this.isExpandable) {\n      this._tree.expand(this._data);\n    }\n  }\n  /** Makes the node focusable. Implemented for TreeKeyManagerItem. */\n  makeFocusable() {\n    this._tabindex = 0;\n    this._changeDetectorRef.markForCheck();\n  }\n  _focusItem() {\n    if (this.isDisabled) {\n      return;\n    }\n    this._tree._keyManager.focusItem(this);\n  }\n  _setActiveItem() {\n    if (this.isDisabled) {\n      return;\n    }\n    this._shouldFocus = false;\n    this._tree._keyManager.focusItem(this);\n    this._shouldFocus = true;\n  }\n  _emitExpansionState(expanded) {\n    this.expandedChange.emit(expanded);\n  }\n  static ɵfac = function CdkTreeNode_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTreeNode)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkTreeNode,\n    selectors: [[\"cdk-tree-node\"]],\n    hostAttrs: [\"role\", \"treeitem\", 1, \"cdk-tree-node\"],\n    hostVars: 5,\n    hostBindings: function CdkTreeNode_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function CdkTreeNode_click_HostBindingHandler() {\n          return ctx._setActiveItem();\n        })(\"focus\", function CdkTreeNode_focus_HostBindingHandler() {\n          return ctx._focusItem();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵdomProperty(\"tabIndex\", ctx._tabindex);\n        i0.ɵɵattribute(\"aria-expanded\", ctx._getAriaExpanded())(\"aria-level\", ctx.level + 1)(\"aria-posinset\", ctx._getPositionInSet())(\"aria-setsize\", ctx._getSetSize());\n      }\n    },\n    inputs: {\n      role: \"role\",\n      isExpandable: [2, \"isExpandable\", \"isExpandable\", booleanAttribute],\n      isExpanded: \"isExpanded\",\n      isDisabled: [2, \"isDisabled\", \"isDisabled\", booleanAttribute],\n      typeaheadLabel: [0, \"cdkTreeNodeTypeaheadLabel\", \"typeaheadLabel\"]\n    },\n    outputs: {\n      activation: \"activation\",\n      expandedChange: \"expandedChange\"\n    },\n    exportAs: [\"cdkTreeNode\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNode, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-tree-node',\n      exportAs: 'cdkTreeNode',\n      host: {\n        'class': 'cdk-tree-node',\n        '[attr.aria-expanded]': '_getAriaExpanded()',\n        '[attr.aria-level]': 'level + 1',\n        '[attr.aria-posinset]': '_getPositionInSet()',\n        '[attr.aria-setsize]': '_getSetSize()',\n        '[tabindex]': '_tabindex',\n        'role': 'treeitem',\n        '(click)': '_setActiveItem()',\n        '(focus)': '_focusItem()'\n      }\n    }]\n  }], () => [], {\n    role: [{\n      type: Input\n    }],\n    isExpandable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    isExpanded: [{\n      type: Input\n    }],\n    isDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    typeaheadLabel: [{\n      type: Input,\n      args: ['cdkTreeNodeTypeaheadLabel']\n    }],\n    activation: [{\n      type: Output\n    }],\n    expandedChange: [{\n      type: Output\n    }]\n  });\n})();\nfunction getParentNodeAriaLevel(nodeElement) {\n  let parent = nodeElement.parentElement;\n  while (parent && !isNodeElement(parent)) {\n    parent = parent.parentElement;\n  }\n  if (!parent) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw Error('Incorrect tree structure containing detached node.');\n    } else {\n      return -1;\n    }\n  } else if (parent.classList.contains('cdk-nested-tree-node')) {\n    return numberAttribute(parent.getAttribute('aria-level'));\n  } else {\n    // The ancestor element is the cdk-tree itself\n    return 0;\n  }\n}\nfunction isNodeElement(element) {\n  const classList = element.classList;\n  return !!(classList?.contains('cdk-nested-tree-node') || classList?.contains('cdk-tree'));\n}\n\n/**\n * Nested node is a child of `<cdk-tree>`. It works with nested tree.\n * By using `cdk-nested-tree-node` component in tree node template, children of the parent node will\n * be added in the `cdkTreeNodeOutlet` in tree node template.\n * The children of node will be automatically added to `cdkTreeNodeOutlet`.\n */\nclass CdkNestedTreeNode extends CdkTreeNode {\n  _type = 'nested';\n  _differs = inject(IterableDiffers);\n  /** Differ used to find the changes in the data provided by the data source. */\n  _dataDiffer;\n  /** The children data dataNodes of current node. They will be placed in `CdkTreeNodeOutlet`. */\n  _children;\n  /** The children node placeholder. */\n  nodeOutlet;\n  constructor() {\n    super();\n  }\n  ngAfterContentInit() {\n    this._dataDiffer = this._differs.find([]).create(this._tree.trackBy);\n    this._tree._getDirectChildren(this.data).pipe(takeUntil(this._destroyed)).subscribe(result => this.updateChildrenNodes(result));\n    this.nodeOutlet.changes.pipe(takeUntil(this._destroyed)).subscribe(() => this.updateChildrenNodes());\n  }\n  ngOnDestroy() {\n    this._clear();\n    super.ngOnDestroy();\n  }\n  /** Add children dataNodes to the NodeOutlet */\n  updateChildrenNodes(children) {\n    const outlet = this._getNodeOutlet();\n    if (children) {\n      this._children = children;\n    }\n    if (outlet && this._children) {\n      const viewContainer = outlet.viewContainer;\n      this._tree.renderNodeChanges(this._children, this._dataDiffer, viewContainer, this._data);\n    } else {\n      // Reset the data differ if there's no children nodes displayed\n      this._dataDiffer.diff([]);\n    }\n  }\n  /** Clear the children dataNodes. */\n  _clear() {\n    const outlet = this._getNodeOutlet();\n    if (outlet) {\n      outlet.viewContainer.clear();\n      this._dataDiffer.diff([]);\n    }\n  }\n  /** Gets the outlet for the current node. */\n  _getNodeOutlet() {\n    const outlets = this.nodeOutlet;\n    // Note that since we use `descendants: true` on the query, we have to ensure\n    // that we don't pick up the outlet of a child node by accident.\n    return outlets && outlets.find(outlet => !outlet._node || outlet._node === this);\n  }\n  static ɵfac = function CdkNestedTreeNode_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkNestedTreeNode)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkNestedTreeNode,\n    selectors: [[\"cdk-nested-tree-node\"]],\n    contentQueries: function CdkNestedTreeNode_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, CdkTreeNodeOutlet, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nodeOutlet = _t);\n      }\n    },\n    hostAttrs: [1, \"cdk-nested-tree-node\"],\n    exportAs: [\"cdkNestedTreeNode\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkTreeNode,\n      useExisting: CdkNestedTreeNode\n    }, {\n      provide: CDK_TREE_NODE_OUTLET_NODE,\n      useExisting: CdkNestedTreeNode\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkNestedTreeNode, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-nested-tree-node',\n      exportAs: 'cdkNestedTreeNode',\n      providers: [{\n        provide: CdkTreeNode,\n        useExisting: CdkNestedTreeNode\n      }, {\n        provide: CDK_TREE_NODE_OUTLET_NODE,\n        useExisting: CdkNestedTreeNode\n      }],\n      host: {\n        'class': 'cdk-nested-tree-node'\n      }\n    }]\n  }], () => [], {\n    nodeOutlet: [{\n      type: ContentChildren,\n      args: [CdkTreeNodeOutlet, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }]\n  });\n})();\n\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * Indent for the children tree dataNodes.\n * This directive will add left-padding to the node to show hierarchy.\n */\nclass CdkTreeNodePadding {\n  _treeNode = inject(CdkTreeNode);\n  _tree = inject(CdkTree);\n  _element = inject(ElementRef);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  /** Current padding value applied to the element. Used to avoid unnecessarily hitting the DOM. */\n  _currentPadding;\n  /** Subject that emits when the component has been destroyed. */\n  _destroyed = new Subject();\n  /** CSS units used for the indentation value. */\n  indentUnits = 'px';\n  /** The level of depth of the tree node. The padding will be `level * indent` pixels. */\n  get level() {\n    return this._level;\n  }\n  set level(value) {\n    this._setLevelInput(value);\n  }\n  _level;\n  /**\n   * The indent for each level. Can be a number or a CSS string.\n   * Default number 40px from material design menu sub-menu spec.\n   */\n  get indent() {\n    return this._indent;\n  }\n  set indent(indent) {\n    this._setIndentInput(indent);\n  }\n  _indent = 40;\n  constructor() {\n    this._setPadding();\n    this._dir?.change.pipe(takeUntil(this._destroyed)).subscribe(() => this._setPadding(true));\n    // In Ivy the indentation binding might be set before the tree node's data has been added,\n    // which means that we'll miss the first render. We have to subscribe to changes in the\n    // data to ensure that everything is up to date.\n    this._treeNode._dataChanges.subscribe(() => this._setPadding());\n  }\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** The padding indent value for the tree node. Returns a string with px numbers if not null. */\n  _paddingIndent() {\n    const nodeLevel = (this._treeNode.data && this._tree._getLevel(this._treeNode.data)) ?? null;\n    const level = this._level == null ? nodeLevel : this._level;\n    return typeof level === 'number' ? `${level * this._indent}${this.indentUnits}` : null;\n  }\n  _setPadding(forceChange = false) {\n    const padding = this._paddingIndent();\n    if (padding !== this._currentPadding || forceChange) {\n      const element = this._element.nativeElement;\n      const paddingProp = this._dir && this._dir.value === 'rtl' ? 'paddingRight' : 'paddingLeft';\n      const resetProp = paddingProp === 'paddingLeft' ? 'paddingRight' : 'paddingLeft';\n      element.style[paddingProp] = padding || '';\n      element.style[resetProp] = '';\n      this._currentPadding = padding;\n    }\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setLevelInput(value) {\n    // Set to null as the fallback value so that _setPadding can fall back to the node level if the\n    // consumer set the directive as `cdkTreeNodePadding=\"\"`. We still want to take this value if\n    // they set 0 explicitly.\n    this._level = isNaN(value) ? null : value;\n    this._setPadding();\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setIndentInput(indent) {\n    let value = indent;\n    let units = 'px';\n    if (typeof indent === 'string') {\n      const parts = indent.split(cssUnitPattern);\n      value = parts[0];\n      units = parts[1] || units;\n    }\n    this.indentUnits = units;\n    this._indent = numberAttribute(value);\n    this._setPadding();\n  }\n  static ɵfac = function CdkTreeNodePadding_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTreeNodePadding)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkTreeNodePadding,\n    selectors: [[\"\", \"cdkTreeNodePadding\", \"\"]],\n    inputs: {\n      level: [2, \"cdkTreeNodePadding\", \"level\", numberAttribute],\n      indent: [0, \"cdkTreeNodePaddingIndent\", \"indent\"]\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNodePadding, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTreeNodePadding]'\n    }]\n  }], () => [], {\n    level: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTreeNodePadding',\n        transform: numberAttribute\n      }]\n    }],\n    indent: [{\n      type: Input,\n      args: ['cdkTreeNodePaddingIndent']\n    }]\n  });\n})();\n\n/**\n * Node toggle to expand and collapse the node.\n */\nclass CdkTreeNodeToggle {\n  _tree = inject(CdkTree);\n  _treeNode = inject(CdkTreeNode);\n  /** Whether expand/collapse the node recursively. */\n  recursive = false;\n  constructor() {}\n  // Toggle the expanded or collapsed state of this node.\n  //\n  // Focus this node with expanding or collapsing it. This ensures that the active node will always\n  // be visible when expanding and collapsing.\n  _toggle() {\n    this.recursive ? this._tree.toggleDescendants(this._treeNode.data) : this._tree.toggle(this._treeNode.data);\n    this._tree._keyManager.focusItem(this._treeNode);\n  }\n  static ɵfac = function CdkTreeNodeToggle_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTreeNodeToggle)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkTreeNodeToggle,\n    selectors: [[\"\", \"cdkTreeNodeToggle\", \"\"]],\n    hostAttrs: [\"tabindex\", \"-1\"],\n    hostBindings: function CdkTreeNodeToggle_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function CdkTreeNodeToggle_click_HostBindingHandler($event) {\n          ctx._toggle();\n          return $event.stopPropagation();\n        })(\"keydown.Enter\", function CdkTreeNodeToggle_keydown_Enter_HostBindingHandler($event) {\n          ctx._toggle();\n          return $event.preventDefault();\n        })(\"keydown.Space\", function CdkTreeNodeToggle_keydown_Space_HostBindingHandler($event) {\n          ctx._toggle();\n          return $event.preventDefault();\n        });\n      }\n    },\n    inputs: {\n      recursive: [2, \"cdkTreeNodeToggleRecursive\", \"recursive\", booleanAttribute]\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNodeToggle, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTreeNodeToggle]',\n      host: {\n        '(click)': '_toggle(); $event.stopPropagation();',\n        '(keydown.Enter)': '_toggle(); $event.preventDefault();',\n        '(keydown.Space)': '_toggle(); $event.preventDefault();',\n        'tabindex': '-1'\n      }\n    }]\n  }], () => [], {\n    recursive: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTreeNodeToggleRecursive',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nconst EXPORTED_DECLARATIONS = [CdkNestedTreeNode, CdkTreeNodeDef, CdkTreeNodePadding, CdkTreeNodeToggle, CdkTree, CdkTreeNode, CdkTreeNodeOutlet];\nclass CdkTreeModule {\n  static ɵfac = function CdkTreeModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTreeModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CdkTreeModule,\n    imports: [CdkNestedTreeNode, CdkTreeNodeDef, CdkTreeNodePadding, CdkTreeNodeToggle, CdkTree, CdkTreeNode, CdkTreeNodeOutlet],\n    exports: [CdkNestedTreeNode, CdkTreeNodeDef, CdkTreeNodePadding, CdkTreeNodeToggle, CdkTree, CdkTreeNode, CdkTreeNodeOutlet]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeModule, [{\n    type: NgModule,\n    args: [{\n      imports: EXPORTED_DECLARATIONS,\n      exports: EXPORTED_DECLARATIONS\n    }]\n  }], null, null);\n})();\nexport { BaseTreeControl, CDK_TREE_NODE_OUTLET_NODE, CdkNestedTreeNode, CdkTree, CdkTreeModule, CdkTreeNode, CdkTreeNodeDef, CdkTreeNodeOutlet, CdkTreeNodeOutletContext, CdkTreeNodePadding, CdkTreeNodeToggle, FlatTreeControl, NestedTreeControl, getMultipleTreeControlsError, getTreeControlMissingError, getTreeMissingMatchingNodeDefError, getTreeMultipleDefaultNodeDefsError, getTreeNoValidDataSourceError };\n", "import { CdkTreeNode, CdkTreeNodeDef, CdkNestedTreeNode, CDK_TREE_NODE_OUTLET_NODE, CdkTreeNodePadding, CdkTreeNodeOutlet, CdkTree, CdkTreeNodeToggle, CdkTreeModule } from '@angular/cdk/tree';\nimport * as i0 from '@angular/core';\nimport { inject, HostAttributeToken, numberAttribute, booleanAttribute, Directive, Input, ViewContainerRef, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { MatCommonModule } from './common-module.mjs';\nimport { DataSource } from '@angular/cdk/collections';\nimport { BehaviorSubject, merge } from 'rxjs';\nimport { take, map } from 'rxjs/operators';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/**\n * Determinte if argument TreeKeyManager is the NoopTreeKeyManager. This function is safe to use with SSR.\n */\nfunction isNoopTreeKeyManager(keyManager) {\n  return !!keyManager._isNoopTreeKeyManager;\n}\n/**\n * Wrapper for the CdkTree node with Material design styles.\n */\nclass MatTreeNode extends CdkTreeNode {\n  /**\n   * The tabindex of the tree node.\n   *\n   * @deprecated By default MatTreeNode manages focus using TreeKeyManager instead of tabIndex.\n   *   Recommend to avoid setting tabIndex directly to prevent TreeKeyManager form getting into\n   *   an unexpected state. Tabindex to be removed in a future version.\n   * @breaking-change 21.0.0 Remove this attribute.\n   */\n  get tabIndexInputBinding() {\n    return this._tabIndexInputBinding;\n  }\n  set tabIndexInputBinding(value) {\n    // If the specified tabIndex value is null or undefined, fall back to the default value.\n    this._tabIndexInputBinding = value;\n  }\n  _tabIndexInputBinding;\n  /**\n   * The default tabindex of the tree node.\n   *\n   * @deprecated By default MatTreeNode manages focus using TreeKeyManager instead of tabIndex.\n   *   Recommend to avoid setting tabIndex directly to prevent TreeKeyManager form getting into\n   *   an unexpected state. Tabindex to be removed in a future version.\n   * @breaking-change 21.0.0 Remove this attribute.\n   */\n  defaultTabIndex = 0;\n  _getTabindexAttribute() {\n    if (isNoopTreeKeyManager(this._tree._keyManager)) {\n      return this.tabIndexInputBinding;\n    }\n    return this._tabindex;\n  }\n  /**\n   * Whether the component is disabled.\n   *\n   * @deprecated This is an alias for `isDisabled`.\n   * @breaking-change 21.0.0 Remove this input\n   */\n  get disabled() {\n    return this.isDisabled;\n  }\n  set disabled(value) {\n    this.isDisabled = value;\n  }\n  constructor() {\n    super();\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    });\n    this.tabIndexInputBinding = Number(tabIndex) || this.defaultTabIndex;\n  }\n  // This is a workaround for https://github.com/angular/angular/issues/23091\n  // In aot mode, the lifecycle hooks from parent class are not called.\n  ngOnInit() {\n    super.ngOnInit();\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n  }\n  static ɵfac = function MatTreeNode_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTreeNode)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTreeNode,\n    selectors: [[\"mat-tree-node\"]],\n    hostAttrs: [1, \"mat-tree-node\"],\n    hostVars: 5,\n    hostBindings: function MatTreeNode_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatTreeNode_click_HostBindingHandler() {\n          return ctx._focusItem();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵdomProperty(\"tabIndex\", ctx._getTabindexAttribute());\n        i0.ɵɵattribute(\"aria-expanded\", ctx._getAriaExpanded())(\"aria-level\", ctx.level + 1)(\"aria-posinset\", ctx._getPositionInSet())(\"aria-setsize\", ctx._getSetSize());\n      }\n    },\n    inputs: {\n      tabIndexInputBinding: [2, \"tabIndex\", \"tabIndexInputBinding\", value => value == null ? 0 : numberAttribute(value)],\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n    },\n    outputs: {\n      activation: \"activation\",\n      expandedChange: \"expandedChange\"\n    },\n    exportAs: [\"matTreeNode\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkTreeNode,\n      useExisting: MatTreeNode\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeNode, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-tree-node',\n      exportAs: 'matTreeNode',\n      outputs: ['activation', 'expandedChange'],\n      providers: [{\n        provide: CdkTreeNode,\n        useExisting: MatTreeNode\n      }],\n      host: {\n        'class': 'mat-tree-node',\n        '[attr.aria-expanded]': '_getAriaExpanded()',\n        '[attr.aria-level]': 'level + 1',\n        '[attr.aria-posinset]': '_getPositionInSet()',\n        '[attr.aria-setsize]': '_getSetSize()',\n        '(click)': '_focusItem()',\n        '[tabindex]': '_getTabindexAttribute()'\n      }\n    }]\n  }], () => [], {\n    tabIndexInputBinding: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value),\n        alias: 'tabIndex'\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/**\n * Wrapper for the CdkTree node definition with Material design styles.\n * Captures the node's template and a when predicate that describes when this node should be used.\n */\nclass MatTreeNodeDef extends CdkTreeNodeDef {\n  data;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatTreeNodeDef_BaseFactory;\n    return function MatTreeNodeDef_Factory(__ngFactoryType__) {\n      return (ɵMatTreeNodeDef_BaseFactory || (ɵMatTreeNodeDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatTreeNodeDef)))(__ngFactoryType__ || MatTreeNodeDef);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTreeNodeDef,\n    selectors: [[\"\", \"matTreeNodeDef\", \"\"]],\n    inputs: {\n      when: [0, \"matTreeNodeDefWhen\", \"when\"],\n      data: [0, \"matTreeNode\", \"data\"]\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkTreeNodeDef,\n      useExisting: MatTreeNodeDef\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeNodeDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matTreeNodeDef]',\n      inputs: [{\n        name: 'when',\n        alias: 'matTreeNodeDefWhen'\n      }],\n      providers: [{\n        provide: CdkTreeNodeDef,\n        useExisting: MatTreeNodeDef\n      }]\n    }]\n  }], null, {\n    data: [{\n      type: Input,\n      args: ['matTreeNode']\n    }]\n  });\n})();\n/**\n * Wrapper for the CdkTree nested node with Material design styles.\n */\nclass MatNestedTreeNode extends CdkNestedTreeNode {\n  node;\n  /**\n   * Whether the node is disabled.\n   *\n   * @deprecated This is an alias for `isDisabled`.\n   * @breaking-change 21.0.0 Remove this input\n   */\n  get disabled() {\n    return this.isDisabled;\n  }\n  set disabled(value) {\n    this.isDisabled = value;\n  }\n  /** Tabindex of the node. */\n  get tabIndex() {\n    return this.isDisabled ? -1 : this._tabIndex;\n  }\n  set tabIndex(value) {\n    // If the specified tabIndex value is null or undefined, fall back to the default value.\n    this._tabIndex = value;\n  }\n  _tabIndex;\n  // This is a workaround for https://github.com/angular/angular/issues/19145\n  // In aot mode, the lifecycle hooks from parent class are not called.\n  // TODO(tinayuangao): Remove when the angular issue #19145 is fixed\n  ngOnInit() {\n    super.ngOnInit();\n  }\n  ngAfterContentInit() {\n    super.ngAfterContentInit();\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatNestedTreeNode_BaseFactory;\n    return function MatNestedTreeNode_Factory(__ngFactoryType__) {\n      return (ɵMatNestedTreeNode_BaseFactory || (ɵMatNestedTreeNode_BaseFactory = i0.ɵɵgetInheritedFactory(MatNestedTreeNode)))(__ngFactoryType__ || MatNestedTreeNode);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatNestedTreeNode,\n    selectors: [[\"mat-nested-tree-node\"]],\n    hostAttrs: [1, \"mat-nested-tree-node\"],\n    inputs: {\n      node: [0, \"matNestedTreeNode\", \"node\"],\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)]\n    },\n    outputs: {\n      activation: \"activation\",\n      expandedChange: \"expandedChange\"\n    },\n    exportAs: [\"matNestedTreeNode\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkNestedTreeNode,\n      useExisting: MatNestedTreeNode\n    }, {\n      provide: CdkTreeNode,\n      useExisting: MatNestedTreeNode\n    }, {\n      provide: CDK_TREE_NODE_OUTLET_NODE,\n      useExisting: MatNestedTreeNode\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatNestedTreeNode, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-nested-tree-node',\n      exportAs: 'matNestedTreeNode',\n      outputs: ['activation', 'expandedChange'],\n      providers: [{\n        provide: CdkNestedTreeNode,\n        useExisting: MatNestedTreeNode\n      }, {\n        provide: CdkTreeNode,\n        useExisting: MatNestedTreeNode\n      }, {\n        provide: CDK_TREE_NODE_OUTLET_NODE,\n        useExisting: MatNestedTreeNode\n      }],\n      host: {\n        'class': 'mat-nested-tree-node'\n      }\n    }]\n  }], null, {\n    node: [{\n      type: Input,\n      args: ['matNestedTreeNode']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }]\n  });\n})();\n\n/**\n * Wrapper for the CdkTree padding with Material design styles.\n */\nclass MatTreeNodePadding extends CdkTreeNodePadding {\n  /** The level of depth of the tree node. The padding will be `level * indent` pixels. */\n  get level() {\n    return this._level;\n  }\n  set level(value) {\n    this._setLevelInput(value);\n  }\n  /** The indent for each level. Default number 40px from material design menu sub-menu spec. */\n  get indent() {\n    return this._indent;\n  }\n  set indent(indent) {\n    this._setIndentInput(indent);\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatTreeNodePadding_BaseFactory;\n    return function MatTreeNodePadding_Factory(__ngFactoryType__) {\n      return (ɵMatTreeNodePadding_BaseFactory || (ɵMatTreeNodePadding_BaseFactory = i0.ɵɵgetInheritedFactory(MatTreeNodePadding)))(__ngFactoryType__ || MatTreeNodePadding);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTreeNodePadding,\n    selectors: [[\"\", \"matTreeNodePadding\", \"\"]],\n    inputs: {\n      level: [2, \"matTreeNodePadding\", \"level\", numberAttribute],\n      indent: [0, \"matTreeNodePaddingIndent\", \"indent\"]\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkTreeNodePadding,\n      useExisting: MatTreeNodePadding\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeNodePadding, [{\n    type: Directive,\n    args: [{\n      selector: '[matTreeNodePadding]',\n      providers: [{\n        provide: CdkTreeNodePadding,\n        useExisting: MatTreeNodePadding\n      }]\n    }]\n  }], null, {\n    level: [{\n      type: Input,\n      args: [{\n        alias: 'matTreeNodePadding',\n        transform: numberAttribute\n      }]\n    }],\n    indent: [{\n      type: Input,\n      args: ['matTreeNodePaddingIndent']\n    }]\n  });\n})();\n\n/**\n * Outlet for nested CdkNode. Put `[matTreeNodeOutlet]` on a tag to place children dataNodes\n * inside the outlet.\n */\nclass MatTreeNodeOutlet {\n  viewContainer = inject(ViewContainerRef);\n  _node = inject(CDK_TREE_NODE_OUTLET_NODE, {\n    optional: true\n  });\n  static ɵfac = function MatTreeNodeOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTreeNodeOutlet)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTreeNodeOutlet,\n    selectors: [[\"\", \"matTreeNodeOutlet\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkTreeNodeOutlet,\n      useExisting: MatTreeNodeOutlet\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeNodeOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[matTreeNodeOutlet]',\n      providers: [{\n        provide: CdkTreeNodeOutlet,\n        useExisting: MatTreeNodeOutlet\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * Wrapper for the CdkTable with Material design styles.\n */\nclass MatTree extends CdkTree {\n  // Outlets within the tree's template where the dataNodes will be inserted.\n  // We need an initializer here to avoid a TS error. The value will be set in `ngAfterViewInit`.\n  _nodeOutlet = undefined;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatTree_BaseFactory;\n    return function MatTree_Factory(__ngFactoryType__) {\n      return (ɵMatTree_BaseFactory || (ɵMatTree_BaseFactory = i0.ɵɵgetInheritedFactory(MatTree)))(__ngFactoryType__ || MatTree);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTree,\n    selectors: [[\"mat-tree\"]],\n    viewQuery: function MatTree_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatTreeNodeOutlet, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nodeOutlet = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-tree\"],\n    exportAs: [\"matTree\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkTree,\n      useExisting: MatTree\n    }]), i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 0,\n    consts: [[\"matTreeNodeOutlet\", \"\"]],\n    template: function MatTree_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainer(0, 0);\n      }\n    },\n    dependencies: [MatTreeNodeOutlet],\n    styles: [\".mat-tree{display:block;background-color:var(--mat-tree-container-background-color, var(--mat-sys-surface))}.mat-tree-node,.mat-nested-tree-node{color:var(--mat-tree-node-text-color, var(--mat-sys-on-surface));font-family:var(--mat-tree-node-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-tree-node-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-tree-node-text-weight, var(--mat-sys-body-large-weight))}.mat-tree-node{display:flex;align-items:center;flex:1;word-wrap:break-word;min-height:var(--mat-tree-node-min-height, 48px)}.mat-nested-tree-node{border-bottom-width:0}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTree, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tree',\n      exportAs: 'matTree',\n      template: `<ng-container matTreeNodeOutlet></ng-container>`,\n      host: {\n        'class': 'mat-tree'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      providers: [{\n        provide: CdkTree,\n        useExisting: MatTree\n      }],\n      imports: [MatTreeNodeOutlet],\n      styles: [\".mat-tree{display:block;background-color:var(--mat-tree-container-background-color, var(--mat-sys-surface))}.mat-tree-node,.mat-nested-tree-node{color:var(--mat-tree-node-text-color, var(--mat-sys-on-surface));font-family:var(--mat-tree-node-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-tree-node-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-tree-node-text-weight, var(--mat-sys-body-large-weight))}.mat-tree-node{display:flex;align-items:center;flex:1;word-wrap:break-word;min-height:var(--mat-tree-node-min-height, 48px)}.mat-nested-tree-node{border-bottom-width:0}\\n\"]\n    }]\n  }], null, {\n    _nodeOutlet: [{\n      type: ViewChild,\n      args: [MatTreeNodeOutlet, {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Wrapper for the CdkTree's toggle with Material design styles.\n */\nclass MatTreeNodeToggle extends CdkTreeNodeToggle {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatTreeNodeToggle_BaseFactory;\n    return function MatTreeNodeToggle_Factory(__ngFactoryType__) {\n      return (ɵMatTreeNodeToggle_BaseFactory || (ɵMatTreeNodeToggle_BaseFactory = i0.ɵɵgetInheritedFactory(MatTreeNodeToggle)))(__ngFactoryType__ || MatTreeNodeToggle);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTreeNodeToggle,\n    selectors: [[\"\", \"matTreeNodeToggle\", \"\"]],\n    inputs: {\n      recursive: [0, \"matTreeNodeToggleRecursive\", \"recursive\"]\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkTreeNodeToggle,\n      useExisting: MatTreeNodeToggle\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeNodeToggle, [{\n    type: Directive,\n    args: [{\n      selector: '[matTreeNodeToggle]',\n      providers: [{\n        provide: CdkTreeNodeToggle,\n        useExisting: MatTreeNodeToggle\n      }],\n      inputs: [{\n        name: 'recursive',\n        alias: 'matTreeNodeToggleRecursive'\n      }]\n    }]\n  }], null, null);\n})();\nconst MAT_TREE_DIRECTIVES = [MatNestedTreeNode, MatTreeNodeDef, MatTreeNodePadding, MatTreeNodeToggle, MatTree, MatTreeNode, MatTreeNodeOutlet];\nclass MatTreeModule {\n  static ɵfac = function MatTreeModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTreeModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatTreeModule,\n    imports: [CdkTreeModule, MatCommonModule, MatNestedTreeNode, MatTreeNodeDef, MatTreeNodePadding, MatTreeNodeToggle, MatTree, MatTreeNode, MatTreeNodeOutlet],\n    exports: [MatCommonModule, MatNestedTreeNode, MatTreeNodeDef, MatTreeNodePadding, MatTreeNodeToggle, MatTree, MatTreeNode, MatTreeNodeOutlet]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CdkTreeModule, MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkTreeModule, MatCommonModule, ...MAT_TREE_DIRECTIVES],\n      exports: [MatCommonModule, MAT_TREE_DIRECTIVES]\n    }]\n  }], null, null);\n})();\n\n/**\n * Tree flattener to convert a normal type of node to node with children & level information.\n * Transform nested nodes of type `T` to flattened nodes of type `F`.\n *\n * For example, the input data of type `T` is nested, and contains its children data:\n *   SomeNode: {\n *     key: 'Fruits',\n *     children: [\n *       NodeOne: {\n *         key: 'Apple',\n *       },\n *       NodeTwo: {\n *        key: 'Pear',\n *      }\n *    ]\n *  }\n *  After flattener flatten the tree, the structure will become\n *  SomeNode: {\n *    key: 'Fruits',\n *    expandable: true,\n *    level: 1\n *  },\n *  NodeOne: {\n *    key: 'Apple',\n *    expandable: false,\n *    level: 2\n *  },\n *  NodeTwo: {\n *   key: 'Pear',\n *   expandable: false,\n *   level: 2\n * }\n * and the output flattened type is `F` with additional information.\n *\n * @deprecated Use MatTree#childrenAccessor and MatTreeNode#isExpandable\n * instead. To be removed in a future version.\n * @breaking-change 21.0.0\n */\nclass MatTreeFlattener {\n  transformFunction;\n  getLevel;\n  isExpandable;\n  getChildren;\n  constructor(transformFunction, getLevel, isExpandable, getChildren) {\n    this.transformFunction = transformFunction;\n    this.getLevel = getLevel;\n    this.isExpandable = isExpandable;\n    this.getChildren = getChildren;\n  }\n  _flattenNode(node, level, resultNodes, parentMap) {\n    const flatNode = this.transformFunction(node, level);\n    resultNodes.push(flatNode);\n    if (this.isExpandable(flatNode)) {\n      const childrenNodes = this.getChildren(node);\n      if (childrenNodes) {\n        if (Array.isArray(childrenNodes)) {\n          this._flattenChildren(childrenNodes, level, resultNodes, parentMap);\n        } else {\n          childrenNodes.pipe(take(1)).subscribe(children => {\n            this._flattenChildren(children, level, resultNodes, parentMap);\n          });\n        }\n      }\n    }\n    return resultNodes;\n  }\n  _flattenChildren(children, level, resultNodes, parentMap) {\n    children.forEach((child, index) => {\n      let childParentMap = parentMap.slice();\n      childParentMap.push(index != children.length - 1);\n      this._flattenNode(child, level + 1, resultNodes, childParentMap);\n    });\n  }\n  /**\n   * Flatten a list of node type T to flattened version of node F.\n   * Please note that type T may be nested, and the length of `structuredData` may be different\n   * from that of returned list `F[]`.\n   */\n  flattenNodes(structuredData) {\n    let resultNodes = [];\n    structuredData.forEach(node => this._flattenNode(node, 0, resultNodes, []));\n    return resultNodes;\n  }\n  /**\n   * Expand flattened node with current expansion status.\n   * The returned list may have different length.\n   */\n  expandFlattenedNodes(nodes, treeControl) {\n    let results = [];\n    let currentExpand = [];\n    currentExpand[0] = true;\n    nodes.forEach(node => {\n      let expand = true;\n      for (let i = 0; i <= this.getLevel(node); i++) {\n        expand = expand && currentExpand[i];\n      }\n      if (expand) {\n        results.push(node);\n      }\n      if (this.isExpandable(node)) {\n        currentExpand[this.getLevel(node) + 1] = treeControl.isExpanded(node);\n      }\n    });\n    return results;\n  }\n}\n/**\n * Data source for flat tree.\n * The data source need to handle expansion/collapsion of the tree node and change the data feed\n * to `MatTree`.\n * The nested tree nodes of type `T` are flattened through `MatTreeFlattener`, and converted\n * to type `F` for `MatTree` to consume.\n *\n * @deprecated Use one of levelAccessor or childrenAccessor instead. To be removed in a future\n * version.\n * @breaking-change 21.0.0\n */\nclass MatTreeFlatDataSource extends DataSource {\n  _treeControl;\n  _treeFlattener;\n  _flattenedData = new BehaviorSubject([]);\n  _expandedData = new BehaviorSubject([]);\n  get data() {\n    return this._data.value;\n  }\n  set data(value) {\n    this._data.next(value);\n    this._flattenedData.next(this._treeFlattener.flattenNodes(this.data));\n    this._treeControl.dataNodes = this._flattenedData.value;\n  }\n  _data = new BehaviorSubject([]);\n  constructor(_treeControl, _treeFlattener, initialData) {\n    super();\n    this._treeControl = _treeControl;\n    this._treeFlattener = _treeFlattener;\n    if (initialData) {\n      // Assign the data through the constructor to ensure that all of the logic is executed.\n      this.data = initialData;\n    }\n  }\n  connect(collectionViewer) {\n    return merge(collectionViewer.viewChange, this._treeControl.expansionModel.changed, this._flattenedData).pipe(map(() => {\n      this._expandedData.next(this._treeFlattener.expandFlattenedNodes(this._flattenedData.value, this._treeControl));\n      return this._expandedData.value;\n    }));\n  }\n  disconnect() {\n    // no op\n  }\n}\n\n/**\n * Data source for nested tree.\n *\n * The data source for nested tree doesn't have to consider node flattener, or the way to expand\n * or collapse. The expansion/collapsion will be handled by TreeControl and each non-leaf node.\n */\nclass MatTreeNestedDataSource extends DataSource {\n  /**\n   * Data for the nested tree\n   */\n  get data() {\n    return this._data.value;\n  }\n  set data(value) {\n    this._data.next(value);\n  }\n  _data = new BehaviorSubject([]);\n  connect(collectionViewer) {\n    return merge(...[collectionViewer.viewChange, this._data]).pipe(map(() => this.data));\n  }\n  disconnect() {\n    // no op\n  }\n}\nexport { MatNestedTreeNode, MatTree, MatTreeFlatDataSource, MatTreeFlattener, MatTreeModule, MatTreeNestedDataSource, MatTreeNode, MatTreeNodeDef, MatTreeNodeOutlet, MatTreeNodePadding, MatTreeNodeToggle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AAcA,IAAM,kBAAN,MAAsB;AAAA;AAAA,EAEpB;AAAA;AAAA,EAEA,iBAAiB,IAAI,eAAe,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxC;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,OAAO,UAAU;AACf,SAAK,eAAe,OAAO,KAAK,cAAc,QAAQ,CAAC;AAAA,EACzD;AAAA;AAAA,EAEA,OAAO,UAAU;AACf,SAAK,eAAe,OAAO,KAAK,cAAc,QAAQ,CAAC;AAAA,EACzD;AAAA;AAAA,EAEA,SAAS,UAAU;AACjB,SAAK,eAAe,SAAS,KAAK,cAAc,QAAQ,CAAC;AAAA,EAC3D;AAAA;AAAA,EAEA,WAAW,UAAU;AACnB,WAAO,KAAK,eAAe,WAAW,KAAK,cAAc,QAAQ,CAAC;AAAA,EACpE;AAAA;AAAA,EAEA,kBAAkB,UAAU;AAC1B,SAAK,eAAe,WAAW,KAAK,cAAc,QAAQ,CAAC,IAAI,KAAK,oBAAoB,QAAQ,IAAI,KAAK,kBAAkB,QAAQ;AAAA,EACrI;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,eAAe,MAAM;AAAA,EAC5B;AAAA;AAAA,EAEA,kBAAkB,UAAU;AAC1B,QAAI,gBAAgB,CAAC,QAAQ;AAC7B,kBAAc,KAAK,GAAG,KAAK,eAAe,QAAQ,CAAC;AACnD,SAAK,eAAe,OAAO,GAAG,cAAc,IAAI,WAAS,KAAK,cAAc,KAAK,CAAC,CAAC;AAAA,EACrF;AAAA;AAAA,EAEA,oBAAoB,UAAU;AAC5B,QAAI,gBAAgB,CAAC,QAAQ;AAC7B,kBAAc,KAAK,GAAG,KAAK,eAAe,QAAQ,CAAC;AACnD,SAAK,eAAe,SAAS,GAAG,cAAc,IAAI,WAAS,KAAK,cAAc,KAAK,CAAC,CAAC;AAAA,EACvF;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,KAAK,UAAU,KAAK,QAAQ,KAAK,IAAI;AAAA,EAC9C;AACF;AA6DA,IAAM,oBAAN,cAAgC,gBAAgB;AAAA,EAC9C;AAAA,EACA;AAAA;AAAA,EAEA,YAAY,aAAa,SAAS;AAChC,UAAM;AACN,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,QAAI,KAAK,SAAS;AAChB,WAAK,UAAU,KAAK,QAAQ;AAAA,IAC9B;AACA,QAAI,KAAK,SAAS,cAAc;AAC9B,WAAK,eAAe,KAAK,QAAQ;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,SAAK,eAAe,MAAM;AAC1B,UAAM,WAAW,KAAK,UAAU,OAAO,CAAC,aAAa,aAAa,CAAC,GAAG,aAAa,GAAG,KAAK,eAAe,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC;AAClI,SAAK,eAAe,OAAO,GAAG,SAAS,IAAI,UAAQ,KAAK,cAAc,IAAI,CAAC,CAAC;AAAA,EAC9E;AAAA;AAAA,EAEA,eAAe,UAAU;AACvB,UAAM,cAAc,CAAC;AACrB,SAAK,gBAAgB,aAAa,QAAQ;AAE1C,WAAO,YAAY,OAAO,CAAC;AAAA,EAC7B;AAAA;AAAA,EAEA,gBAAgB,aAAa,UAAU;AACrC,gBAAY,KAAK,QAAQ;AACzB,UAAM,gBAAgB,KAAK,YAAY,QAAQ;AAC/C,QAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,oBAAc,QAAQ,WAAS,KAAK,gBAAgB,aAAa,KAAK,CAAC;AAAA,IACzE,WAAW,aAAa,aAAa,GAAG;AAGtC,oBAAc,KAAK,KAAK,CAAC,GAAG,OAAO,OAAO,CAAC,EAAE,UAAU,cAAY;AACjE,mBAAW,SAAS,UAAU;AAC5B,eAAK,gBAAgB,aAAa,KAAK;AAAA,QACzC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAOA,IAAM,4BAA4B,IAAI,eAAe,2BAA2B;AAKhF,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,gBAAgB,OAAO,gBAAgB;AAAA,EACvC,QAAQ,OAAO,2BAA2B;AAAA,IACxC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,EAC3C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAGH,IAAM,2BAAN,MAA+B;AAAA;AAAA,EAE7B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EACA,YAAY,MAAM;AAChB,SAAK,YAAY;AAAA,EACnB;AACF;AAKA,IAAM,iBAAN,MAAM,gBAAe;AAAA;AAAA,EAEnB,WAAW,OAAO,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ7B;AAAA,EACA,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC;AAAA,IACtC,QAAQ;AAAA,MACN,MAAM,CAAC,GAAG,sBAAsB,MAAM;AAAA,IACxC;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,QAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,SAAS,gCAAgC;AACvC,SAAO,MAAM,uCAAuC;AACtD;AAKA,SAAS,sCAAsC;AAC7C,SAAO,MAAM,sEAAsE;AACrF;AAKA,SAAS,qCAAqC;AAC5C,SAAO,MAAM,uEAAuE;AACtF;AAKA,SAAS,6BAA6B;AACpC,SAAO,MAAM,iFAAiF;AAChG;AAMA,SAAS,+BAA+B;AACtC,SAAO,MAAM,kFAAkF;AACjG;AAMA,IAAM,UAAN,MAAM,SAAQ;AAAA,EACZ,WAAW,OAAO,eAAe;AAAA,EACjC,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,cAAc,OAAO,UAAU;AAAA,EAC/B,OAAO,OAAO,cAAc;AAAA;AAAA,EAE5B,aAAa,IAAI,QAAQ;AAAA;AAAA,EAEzB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,UAAU,oBAAI,IAAI;AAAA;AAAA,EAElB,WAAW,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASnB,YAAY,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,YAAY;AACzB,QAAI,KAAK,gBAAgB,YAAY;AACnC,WAAK,kBAAkB,UAAU;AAAA,IACnC;AAAA,EACF;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,IAAI,gBAAgB;AAAA,IAC/B,OAAO;AAAA,IACP,KAAK,OAAO;AAAA,EACd,CAAC;AAAA;AAAA,EAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,IAAI,gBAAgB,CAAC,CAAC;AAAA;AAAA,EAExC,YAAY,IAAI,gBAAgB,IAAI;AAAA;AAAA,EAEpC,SAAS,IAAI,gBAAgB,oBAAI,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtC,mBAAmB,IAAI,gBAAgB,CAAC,CAAC;AAAA,EACzC,qBAAqB,OAAO,gBAAgB;AAAA;AAAA,EAE5C;AAAA,EACA,YAAY;AAAA,EACZ,cAAc;AAAA,EAAC;AAAA,EACf,qBAAqB;AACnB,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,wBAAwB;AACtB,SAAK,6BAA6B;AAClC,SAAK,wBAAwB;AAAA,EAC/B;AAAA,EACA,cAAc;AACZ,SAAK,YAAY,cAAc,MAAM;AACrC,SAAK,WAAW,SAAS;AACzB,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,QAAI,KAAK,eAAe,OAAO,KAAK,YAAY,eAAe,YAAY;AACzE,WAAK,WAAW,WAAW,IAAI;AAAA,IACjC;AACA,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,YAAY;AACnC,WAAK,oBAAoB;AAAA,IAC3B;AAGA,SAAK,aAAa,QAAQ;AAAA,EAC5B;AAAA,EACA,WAAW;AACT,SAAK,uBAAuB;AAC5B,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,kBAAkB;AAChB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,+BAA+B;AAC7B,UAAM,kBAAkB,KAAK,UAAU,OAAO,SAAO,CAAC,IAAI,IAAI;AAC9D,QAAI,gBAAgB,SAAS,MAAM,OAAO,cAAc,eAAe,YAAY;AACjF,YAAM,oCAAoC;AAAA,IAC5C;AACA,SAAK,kBAAkB,gBAAgB,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,SAAS;AAC3B,UAAM,cAAc,KAAK,UAAU;AACnC,QAAI,gBAAgB,MAAM;AACxB,WAAK,UAAU,KAAK,OAAO;AAAA,IAC7B,YAAY,OAAO,cAAc,eAAe,cAAc,gBAAgB,SAAS;AACrF,cAAQ,KAAK,yKAAmL,WAAW,qBAAqB,OAAO,IAAI;AAAA,IAC7O;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,YAAY;AAC5B,QAAI,KAAK,eAAe,OAAO,KAAK,YAAY,eAAe,YAAY;AACzE,WAAK,WAAW,WAAW,IAAI;AAAA,IACjC;AACA,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,YAAY;AACnC,WAAK,oBAAoB;AAAA,IAC3B;AAEA,QAAI,CAAC,YAAY;AACf,WAAK,YAAY,cAAc,MAAM;AAAA,IACvC;AACA,SAAK,cAAc;AACnB,QAAI,KAAK,WAAW;AAClB,WAAK,wBAAwB;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,oBAAoB,IAAI,eAAe,IAAI;AAChD,aAAO,KAAK;AAAA,IACd;AACA,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA,EAEA,0BAA0B;AACxB,QAAI,KAAK,mBAAmB;AAC1B;AAAA,IACF;AACA,QAAI;AACJ,QAAI,aAAa,KAAK,WAAW,GAAG;AAClC,mBAAa,KAAK,YAAY,QAAQ,IAAI;AAAA,IAC5C,WAAW,aAAa,KAAK,WAAW,GAAG;AACzC,mBAAa,KAAK;AAAA,IACpB,WAAW,MAAM,QAAQ,KAAK,WAAW,GAAG;AAC1C,mBAAa,GAAG,KAAK,WAAW;AAAA,IAClC;AACA,QAAI,CAAC,YAAY;AACf,UAAI,OAAO,cAAc,eAAe,WAAW;AACjD,cAAM,8BAA8B;AAAA,MACtC;AACA;AAAA,IACF;AACA,SAAK,oBAAoB,KAAK,eAAe,UAAU,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,mBAAiB;AACnH,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,eAAe,YAAY;AACzB,UAAM,iBAAiB,KAAK,mBAAmB;AAC/C,WAAO,cAAc;AAAA,MAAC;AAAA,MAAY,KAAK;AAAA;AAAA;AAAA,MAGvC,eAAe,QAAQ,KAAK,UAAU,IAAI,GAAG,IAAI,sBAAoB;AACnE,aAAK,sBAAsB,gBAAgB;AAAA,MAC7C,CAAC,CAAC;AAAA,IAAC,CAAC,EAAE,KAAK,UAAU,CAAC,CAAC,MAAM,QAAQ,MAAM;AACzC,UAAI,aAAa,MAAM;AACrB,eAAO,GAAG;AAAA,UACR,aAAa;AAAA,UACb,gBAAgB;AAAA,UAChB;AAAA,QACF,CAAC;AAAA,MACH;AAGA,aAAO,KAAK,sBAAsB,MAAM,QAAQ,EAAE,KAAK,IAAI,mBAAkB,iCACxE,gBADwE;AAAA,QAE3E;AAAA,MACF,EAAE,CAAC;AAAA,IACL,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,mBAAmB,MAAM;AACvB,QAAI,KAAK,aAAa,MAAM;AAC1B,WAAK,kBAAkB,KAAK,WAAW;AACvC;AAAA,IACF;AAGA,SAAK,kBAAkB,KAAK,cAAc;AAC1C,SAAK,kBAAkB,KAAK,WAAW;AACvC,SAAK,uBAAuB,KAAK,cAAc;AAAA,EACjD;AAAA,EACA,sBAAsB,kBAAkB;AACtC,QAAI,CAAC,kBAAkB;AACrB;AAAA,IACF;AACA,UAAM,QAAQ,KAAK,OAAO;AAC1B,eAAW,SAAS,iBAAiB,OAAO;AAC1C,YAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,YAAM,oBAAoB,IAAI;AAAA,IAChC;AACA,eAAW,WAAW,iBAAiB,SAAS;AAC9C,YAAM,OAAO,MAAM,IAAI,OAAO;AAC9B,YAAM,oBAAoB,KAAK;AAAA,IACjC;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,UAAM,QAAQ,cAAc,CAAC,KAAK,kBAAkB,KAAK,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,iBAAiB,WAAW,MAAM,gBAAgB,OAAO,CAACA,QAAO,SAAS;AACrJ,YAAM,OAAO,YAAY,IAAI,KAAK,iBAAiB,IAAI,CAAC;AACxD,UAAI,MAAM;AACR,QAAAA,OAAM,KAAK,IAAI;AAAA,MACjB;AACA,aAAOA;AAAA,IACT,GAAG,CAAC,CAAC,CAAC,CAAC;AACP,UAAM,oBAAoB;AAAA,MACxB,SAAS,UAAQ,KAAK,iBAAiB,KAAK,IAAI;AAAA,MAChD,eAAe,UAAQ,CAAC,CAAC,KAAK;AAAA,MAC9B,2BAA2B;AAAA,MAC3B,uBAAuB,KAAK,KAAK;AAAA,IACnC;AACA,SAAK,cAAc,KAAK,mBAAmB,OAAO,iBAAiB;AAAA,EACrE;AAAA,EACA,wBAAwB;AAEtB,UAAM,UAAU,KAAK,YAAY,CAAC,QAAQ,SAAS,KAAK,iBAAiB,IAAI;AAC7E,SAAK,cAAc,KAAK,SAAS,KAAK,CAAC,CAAC,EAAE,OAAO,OAAO;AAAA,EAC1D;AAAA,EACA,yBAAyB;AACvB,QAAI,OAAO,cAAc,eAAe,WAAW;AAGjD,UAAI,kBAAkB;AACtB,UAAI,KAAK,aAAa;AACpB;AAAA,MACF;AACA,UAAI,KAAK,eAAe;AACtB;AAAA,MACF;AACA,UAAI,KAAK,kBAAkB;AACzB;AAAA,MACF;AACA,UAAI,CAAC,iBAAiB;AACpB,cAAM,2BAA2B;AAAA,MACnC,WAAW,kBAAkB,GAAG;AAC9B,cAAM,6BAA6B;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB,MAAM,aAAa,KAAK,aAAa,gBAAgB,KAAK,YAAY,eAAe,YAAY;AACjH,UAAM,UAAU,WAAW,KAAK,IAAI;AAQpC,QAAI,CAAC,WAAW,CAAC,KAAK,WAAW;AAC/B;AAAA,IACF;AACA,aAAS,iBAAiB,CAAC,MAAM,uBAAuB,iBAAiB;AACvE,UAAI,KAAK,iBAAiB,MAAM;AAC9B,aAAK,WAAW,KAAK,YAAY,GAAG,cAAc,eAAe,UAAU;AAAA,MAC7E,WAAW,gBAAgB,MAAM;AAC/B,sBAAc,OAAO,qBAAqB;AAAA,MAC5C,OAAO;AACL,cAAM,OAAO,cAAc,IAAI,qBAAqB;AACpD,sBAAc,KAAK,MAAM,YAAY;AAAA,MACvC;AAAA,IACF,CAAC;AAGD,aAAS,sBAAsB,YAAU;AACvC,YAAM,UAAU,OAAO;AACvB,UAAI,OAAO,gBAAgB,QAAW;AACpC,cAAM,OAAO,cAAc,IAAI,OAAO,YAAY;AAClD,aAAK,QAAQ,YAAY;AAAA,MAC3B;AAAA,IACF,CAAC;AAKD,QAAI,YAAY;AACd,WAAK,mBAAmB,aAAa;AAAA,IACvC,OAAO;AACL,WAAK,mBAAmB,cAAc;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,MAAM,GAAG;AACnB,QAAI,KAAK,UAAU,WAAW,GAAG;AAC/B,aAAO,KAAK,UAAU;AAAA,IACxB;AACA,UAAM,UAAU,KAAK,UAAU,KAAK,SAAO,IAAI,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,KAAK;AAClF,QAAI,CAAC,YAAY,OAAO,cAAc,eAAe,YAAY;AAC/D,YAAM,mCAAmC;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,UAAU,OAAO,eAAe,YAAY;AACrD,UAAM,gBAAgB,KAAK,kBAAkB;AAC7C,UAAM,OAAO,KAAK,YAAY,UAAU,KAAK;AAC7C,UAAM,MAAM,KAAK,iBAAiB,QAAQ;AAE1C,UAAM,UAAU,IAAI,yBAAyB,QAAQ;AACrD,YAAQ,QAAQ;AAChB,mBAAe,KAAK,SAAS,IAAI,GAAG,KAAK;AAGzC,QAAI,eAAe;AACjB,cAAQ,QAAQ,cAAc,QAAQ;AAAA,IACxC,WAAW,eAAe,UAAa,KAAK,QAAQ,IAAI,KAAK,iBAAiB,UAAU,CAAC,GAAG;AAC1F,cAAQ,QAAQ,KAAK,QAAQ,IAAI,KAAK,iBAAiB,UAAU,CAAC,IAAI;AAAA,IACxE,OAAO;AACL,cAAQ,QAAQ;AAAA,IAClB;AACA,SAAK,QAAQ,IAAI,KAAK,QAAQ,KAAK;AAEnC,UAAM,YAAY,gBAAgB,gBAAgB,KAAK,YAAY;AACnE,cAAU,mBAAmB,KAAK,UAAU,SAAS,KAAK;AAI1D,QAAI,YAAY,oBAAoB;AAClC,kBAAY,mBAAmB,OAAO;AAAA,IACxC;AAAA,EACF;AAAA;AAAA,EAEA,WAAW,UAAU;AACnB,WAAO,CAAC,EAAE,KAAK,aAAa,WAAW,QAAQ,KAAK,KAAK,iBAAiB,WAAW,KAAK,iBAAiB,QAAQ,CAAC;AAAA,EACtH;AAAA;AAAA,EAEA,OAAO,UAAU;AACf,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,OAAO,QAAQ;AAAA,IAClC,WAAW,KAAK,iBAAiB;AAC/B,WAAK,gBAAgB,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAAA,IAC7D;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,UAAU;AACf,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,OAAO,QAAQ;AAAA,IAClC,WAAW,KAAK,iBAAiB;AAC/B,WAAK,gBAAgB,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AAAA,IAC7D;AAAA,EACF;AAAA;AAAA,EAEA,SAAS,UAAU;AACjB,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,SAAS,QAAQ;AAAA,IACpC,WAAW,KAAK,iBAAiB;AAC/B,WAAK,gBAAgB,SAAS,KAAK,iBAAiB,QAAQ,CAAC;AAAA,IAC/D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,UAAU;AAC1B,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,kBAAkB,QAAQ;AAAA,IAC7C,WAAW,KAAK,iBAAiB;AAC/B,UAAI,KAAK,WAAW,QAAQ,GAAG;AAC7B,aAAK,oBAAoB,QAAQ;AAAA,MACnC,OAAO;AACL,aAAK,kBAAkB,QAAQ;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,UAAU;AAC1B,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,kBAAkB,QAAQ;AAAA,IAC7C,WAAW,KAAK,iBAAiB;AAC/B,YAAM,iBAAiB,KAAK;AAC5B,qBAAe,OAAO,KAAK,iBAAiB,QAAQ,CAAC;AACrD,WAAK,gBAAgB,QAAQ,EAAE,KAAK,KAAK,CAAC,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,cAAY;AAC7F,uBAAe,OAAO,GAAG,SAAS,IAAI,WAAS,KAAK,iBAAiB,KAAK,CAAC,CAAC;AAAA,MAC9E,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB,UAAU;AAC5B,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,oBAAoB,QAAQ;AAAA,IAC/C,WAAW,KAAK,iBAAiB;AAC/B,YAAM,iBAAiB,KAAK;AAC5B,qBAAe,SAAS,KAAK,iBAAiB,QAAQ,CAAC;AACvD,WAAK,gBAAgB,QAAQ,EAAE,KAAK,KAAK,CAAC,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,cAAY;AAC7F,uBAAe,SAAS,GAAG,SAAS,IAAI,WAAS,KAAK,iBAAiB,KAAK,CAAC,CAAC;AAAA,MAChF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,YAAY;AACV,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,UAAU;AAAA,IAC7B,WAAW,KAAK,iBAAiB;AAC/B,WAAK,qBAAqB,UAAQ,KAAK,iBAAiB,OAAO,GAAG,IAAI,CAAC;AAAA,IACzE;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,YAAY;AAAA,IAC/B,WAAW,KAAK,iBAAiB;AAC/B,WAAK,qBAAqB,UAAQ,KAAK,iBAAiB,SAAS,GAAG,IAAI,CAAC;AAAA,IAC3E;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,KAAK,aAAa,UAAU,KAAK,KAAK,WAAW,KAAK,KAAK;AAAA,EACpE;AAAA;AAAA,EAEA,uBAAuB;AACrB,WAAO,KAAK,aAAa,aAAa,KAAK,KAAK,WAAW,KAAK,KAAK;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,UAAU;AAC3B,UAAM,gBAAgB,KAAK,kBAAkB;AAC7C,UAAM,iBAAiB,KAAK,mBAAmB,KAAK,aAAa;AACjE,QAAI,CAAC,gBAAgB;AACnB,aAAO,GAAG,CAAC,CAAC;AAAA,IACd;AACA,UAAM,MAAM,KAAK,iBAAiB,QAAQ;AAC1C,UAAM,aAAa,eAAe,QAAQ,KAAK,UAAU,aAAW;AAClE,UAAI,QAAQ,MAAM,SAAS,GAAG,GAAG;AAC/B,eAAO,GAAG,IAAI;AAAA,MAChB,WAAW,QAAQ,QAAQ,SAAS,GAAG,GAAG;AACxC,eAAO,GAAG,KAAK;AAAA,MACjB;AACA,aAAO;AAAA,IACT,CAAC,GAAG,UAAU,KAAK,WAAW,QAAQ,CAAC,CAAC;AACxC,QAAI,eAAe;AACjB,aAAO,cAAc,CAAC,YAAY,KAAK,eAAe,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,UAAU,cAAc,MAAM;AAChG,YAAI,CAAC,UAAU;AACb,iBAAO,CAAC;AAAA,QACV;AACA,eAAO,KAAK,qBAAqB,eAAe,gBAAgB,UAAU,CAAC;AAAA,MAC7E,CAAC,CAAC;AAAA,IACJ;AACA,UAAM,mBAAmB,KAAK,qBAAqB;AACnD,QAAI,kBAAkB;AACpB,aAAO,iBAAiB,iBAAiB,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC1D;AACA,UAAM,2BAA2B;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,eAAe,gBAAgB,UAAU,YAAY;AACxE,UAAM,MAAM,KAAK,iBAAiB,QAAQ;AAC1C,UAAM,aAAa,eAAe,UAAU,UAAQ,KAAK,iBAAiB,IAAI,MAAM,GAAG;AACvF,UAAM,gBAAgB,cAAc,QAAQ;AAC5C,UAAM,gBAAgB,gBAAgB;AACtC,UAAM,UAAU,CAAC;AAMjB,aAAS,IAAI,aAAa,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC3D,YAAM,eAAe,cAAc,eAAe,CAAC,CAAC;AACpD,UAAI,gBAAgB,eAAe;AACjC;AAAA,MACF;AACA,UAAI,gBAAgB,eAAe;AACjC,gBAAQ,KAAK,eAAe,CAAC,CAAC;AAAA,MAChC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,MAAM;AAClB,SAAK,OAAO,MAAM,IAAI,KAAK,iBAAiB,KAAK,IAAI,GAAG,IAAI;AAC5D,SAAK,OAAO,KAAK,KAAK,OAAO,KAAK;AAAA,EACpC;AAAA;AAAA,EAEA,gBAAgB,MAAM;AACpB,SAAK,OAAO,MAAM,OAAO,KAAK,iBAAiB,KAAK,IAAI,CAAC;AACzD,SAAK,OAAO,KAAK,KAAK,OAAO,KAAK;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,MAAM;AACd,WAAO,KAAK,QAAQ,IAAI,KAAK,iBAAiB,IAAI,CAAC;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,UAAU;AACpB,UAAM,MAAM,KAAK,YAAY,QAAQ;AACrC,WAAO,IAAI;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,UAAU;AAC1B,UAAM,MAAM,KAAK,YAAY,QAAQ;AACrC,UAAM,MAAM,KAAK,iBAAiB,QAAQ;AAC1C,WAAO,IAAI,UAAU,UAAQ,KAAK,iBAAiB,IAAI,MAAM,GAAG,IAAI;AAAA,EACtE;AAAA;AAAA,EAEA,eAAe,MAAM;AACnB,UAAM,SAAS,KAAK,SAAS,IAAI,KAAK,iBAAiB,KAAK,IAAI,CAAC;AACjE,WAAO,UAAU,KAAK,OAAO,MAAM,IAAI,KAAK,iBAAiB,MAAM,CAAC;AAAA,EACtE;AAAA;AAAA,EAEA,iBAAiB,MAAM;AACrB,WAAO,KAAK,mBAAmB,KAAK,IAAI,EAAE,KAAK,IAAI,cAAY,SAAS,OAAO,CAAC,OAAO,UAAU;AAC/F,YAAM,QAAQ,KAAK,OAAO,MAAM,IAAI,KAAK,iBAAiB,KAAK,CAAC;AAChE,UAAI,OAAO;AACT,cAAM,KAAK,KAAK;AAAA,MAClB;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC,CAAC,CAAC;AAAA,EACT;AAAA;AAAA,EAEA,yBAAyB,OAAO;AAG9B,QAAI,MAAM,WAAW,KAAK,YAAY,eAAe;AACnD,WAAK,YAAY,UAAU,KAAK;AAAA,IAClC,OAAO;AACL,YAAM,QAAQ,KAAK,OAAO,SAAS;AACnC,iBAAW,CAAC,EAAE,IAAI,KAAK,OAAO;AAC5B,YAAI,MAAM,WAAW,KAAK,YAAY,eAAe;AACnD,eAAK,YAAY,UAAU,KAAK;AAChC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB,UAAU;AACxB,QAAI,KAAK,aAAa;AACpB,aAAO,GAAG,KAAK,YAAY,eAAe,QAAQ,CAAC;AAAA,IACrD;AACA,QAAI,KAAK,eAAe;AACtB,YAAM,UAAU,KAAK,qBAAqB,KAAK,eAAe,KAAK,gBAAgB,OAAO,UAAU,QAAQ;AAC5G,aAAO,GAAG,OAAO;AAAA,IACnB;AACA,QAAI,KAAK,kBAAkB;AACzB,aAAO,KAAK,2BAA2B,QAAQ,EAAE,KAAK,OAAO,CAAC,aAAa,iBAAiB;AAC1F,oBAAY,KAAK,GAAG,YAAY;AAChC,eAAO;AAAA,MACT,GAAG,CAAC,CAAC,CAAC;AAAA,IACR;AACA,UAAM,2BAA2B;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,2BAA2B,UAAU;AACnC,QAAI,CAAC,KAAK,kBAAkB;AAC1B,aAAO,GAAG,CAAC,CAAC;AAAA,IACd;AACA,WAAO,iBAAiB,KAAK,iBAAiB,QAAQ,CAAC,EAAE,KAAK,KAAK,CAAC,GAAG,UAAU,cAAY;AAE3F,iBAAW,SAAS,UAAU;AAC5B,aAAK,SAAS,IAAI,KAAK,iBAAiB,KAAK,GAAG,QAAQ;AAAA,MAC1D;AACA,aAAO,GAAG,GAAG,QAAQ,EAAE,KAAK,UAAU,WAAS,OAAO,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,2BAA2B,KAAK,CAAC,CAAC,CAAC;AAAA,IAC7G,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,iBAAiB,UAAU;AAQzB,WAAO,KAAK,eAAe,QAAQ,KAAK;AAAA,EAC1C;AAAA,EACA,YAAY,MAAM;AAChB,UAAM,MAAM,KAAK,iBAAiB,IAAI;AACtC,UAAM,SAAS,KAAK,SAAS,IAAI,GAAG;AACpC,UAAM,YAAY,SAAS,KAAK,iBAAiB,MAAM,IAAI;AAC3D,UAAM,MAAM,KAAK,UAAU,IAAI,SAAS;AACxC,WAAO,OAAO,CAAC,IAAI;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,MAAM,OAAO,aAAa;AAI3C,QAAI,CAAC,YAAY,QAAQ;AACvB,aAAO;AAAA,IACT;AACA,UAAM,eAAe,KAAK,QAAQ,IAAI,KAAK,iBAAiB,IAAI,CAAC,KAAK;AACtE,aAAS,cAAc,QAAQ,GAAG,eAAe,GAAG,eAAe;AACjE,YAAM,aAAa,YAAY,WAAW;AAC1C,YAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,iBAAiB,UAAU,CAAC,KAAK;AAC3E,UAAI,cAAc,cAAc;AAC9B,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iCAAiC,OAAO,QAAQ,GAAG;AACjD,UAAM,mBAAmB,KAAK,qBAAqB;AAEnD,QAAI,CAAC,kBAAkB;AACrB,aAAO,GAAG,CAAC,GAAG,KAAK,CAAC;AAAA,IACtB;AACA,WAAO,GAAG,GAAG,KAAK,EAAE,KAAK,UAAU,UAAQ;AACzC,YAAM,YAAY,KAAK,iBAAiB,IAAI;AAC5C,UAAI,CAAC,KAAK,SAAS,IAAI,SAAS,GAAG;AACjC,aAAK,SAAS,IAAI,WAAW,IAAI;AAAA,MACnC;AACA,WAAK,QAAQ,IAAI,WAAW,KAAK;AACjC,YAAM,WAAW,iBAAiB,iBAAiB,IAAI,CAAC;AACxD,aAAO,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,SAAS,KAAK,KAAK,CAAC,GAAG,IAAI,gBAAc;AACjE,aAAK,UAAU,IAAI,WAAW,CAAC,GAAI,cAAc,CAAC,CAAE,CAAC;AACrD,mBAAW,SAAS,cAAc,CAAC,GAAG;AACpC,gBAAM,WAAW,KAAK,iBAAiB,KAAK;AAC5C,eAAK,SAAS,IAAI,UAAU,IAAI;AAChC,eAAK,QAAQ,IAAI,UAAU,QAAQ,CAAC;AAAA,QACtC;AAAA,MACF,CAAC,GAAG,UAAU,gBAAc;AAC1B,YAAI,CAAC,YAAY;AACf,iBAAO,GAAG,CAAC,CAAC;AAAA,QACd;AACA,eAAO,KAAK,iCAAiC,YAAY,QAAQ,CAAC,EAAE,KAAK,IAAI,iBAAe,KAAK,WAAW,IAAI,IAAI,cAAc,CAAC,CAAC,CAAC;AAAA,MACvI,CAAC,CAAC,CAAC;AAAA,IACL,CAAC,GAAG,OAAO,CAAC,SAAS,aAAa;AAChC,cAAQ,KAAK,GAAG,QAAQ;AACxB,aAAO;AAAA,IACT,GAAG,CAAC,CAAC,CAAC;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,OAAO,UAAU;AAKrC,QAAI,KAAK,oBAAoB,aAAa,QAAQ;AAGhD,WAAK,oBAAoB;AAEzB,WAAK,UAAU,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC;AACnC,aAAO,KAAK,iCAAiC,KAAK,EAAE,KAAK,IAAI,qBAAmB;AAAA,QAC9E,aAAa;AAAA,QACb;AAAA,MACF,EAAE,CAAC;AAAA,IACL,WAAW,KAAK,iBAAiB,aAAa,UAAU;AAGtD,YAAM,gBAAgB,KAAK;AAC3B,aAAO,GAAG,MAAM,OAAO,UAAQ,cAAc,IAAI,MAAM,CAAC,CAAC,EAAE,KAAK,IAAI,gBAAc;AAAA,QAChF,aAAa;AAAA,QACb,gBAAgB;AAAA,MAClB,EAAE,GAAG,IAAI,CAAC;AAAA,QACR;AAAA,MACF,MAAM;AACJ,aAAK,kBAAkB,cAAc;AAAA,MACvC,CAAC,CAAC;AAAA,IACJ,WAAW,aAAa,QAAQ;AAK9B,aAAO,GAAG;AAAA,QACR,aAAa;AAAA,QACb,gBAAgB;AAAA,MAClB,CAAC,EAAE,KAAK,IAAI,CAAC;AAAA,QACX;AAAA,MACF,MAAM;AACJ,aAAK,kBAAkB,cAAc;AAAA,MACvC,CAAC,CAAC;AAAA,IACJ,OAAO;AAGL,WAAK,oBAAoB;AAGzB,WAAK,UAAU,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC;AACnC,aAAO,KAAK,iCAAiC,KAAK,EAAE,KAAK,IAAI,qBAAmB;AAAA,QAC9E,aAAa;AAAA,QACb;AAAA,MACF,EAAE,CAAC;AAAA,IACL;AAAA,EACF;AAAA,EACA,kBAAkB,gBAAgB;AAChC,SAAK,gBAAgB,KAAK,cAAc;AAAA,EAC1C;AAAA,EACA,uBAAuB,gBAAgB;AACrC,SAAK,iBAAiB,KAAK,cAAc;AAAA,EAC3C;AAAA;AAAA,EAEA,kBAAkB,gBAAgB;AAChC,UAAM,gBAAgB,KAAK,kBAAkB;AAC7C,QAAI,CAAC,eAAe;AAClB;AAAA,IACF;AAGA,SAAK,oBAAoB;AACzB,aAAS,QAAQ,GAAG,QAAQ,eAAe,QAAQ,SAAS;AAC1D,YAAM,WAAW,eAAe,KAAK;AACrC,YAAM,MAAM,KAAK,iBAAiB,QAAQ;AAC1C,WAAK,QAAQ,IAAI,KAAK,cAAc,QAAQ,CAAC;AAC7C,YAAM,SAAS,KAAK,mBAAmB,UAAU,OAAO,cAAc;AACtE,WAAK,SAAS,IAAI,KAAK,MAAM;AAC7B,YAAM,YAAY,SAAS,KAAK,iBAAiB,MAAM,IAAI;AAC3D,YAAM,QAAQ,KAAK,UAAU,IAAI,SAAS,KAAK,CAAC;AAChD,YAAM,OAAO,OAAO,GAAG,QAAQ;AAC/B,WAAK,UAAU,IAAI,WAAW,KAAK;AAAA,IACrC;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB,UAAU;AAC7B,UAAM,WAAW,CAAC;AAClB,UAAM,cAAc,CAAC;AACrB,SAAK,OAAO,MAAM,QAAQ,UAAQ;AAChC,eAAS,KAAK,KAAK,iBAAiB,KAAK,IAAI,CAAC;AAC9C,kBAAY,KAAK,KAAK,gBAAgB,KAAK,IAAI,CAAC;AAAA,IAClD,CAAC;AACD,QAAI,YAAY,SAAS,GAAG;AAC1B,oBAAc,WAAW,EAAE,KAAK,KAAK,CAAC,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,aAAW;AACxF,gBAAQ,QAAQ,WAAS,MAAM,QAAQ,OAAK,SAAS,KAAK,KAAK,iBAAiB,CAAC,CAAC,CAAC,CAAC;AACpF,iBAAS,QAAQ;AAAA,MACnB,CAAC;AAAA,IACH,OAAO;AACL,eAAS,QAAQ;AAAA,IACnB;AAAA,EACF;AAAA;AAAA,EAEA,sBAAsB;AACpB,SAAK,SAAS,MAAM;AACpB,SAAK,QAAQ,MAAM;AACnB,SAAK,UAAU,MAAM;AAAA,EACvB;AAAA,EACA,OAAO,OAAO,SAAS,gBAAgB,mBAAmB;AACxD,WAAO,KAAK,qBAAqB,UAAS;AAAA,EAC5C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,IACxB,gBAAgB,SAAS,uBAAuB,IAAI,KAAK,UAAU;AACjE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,gBAAgB,CAAC;AAAA,MAC/C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,cAAc,IAAI,KAAK;AACzC,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,mBAAmB,CAAC;AAAA,MACrC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,QAAQ,QAAQ,GAAG,UAAU;AAAA,IACzC,cAAc,SAAS,qBAAqB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,WAAW,SAAS,mCAAmC,QAAQ;AAC3E,iBAAO,IAAI,yBAAyB,MAAM;AAAA,QAC5C,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,SAAS;AAAA,MACT,cAAc;AAAA,IAChB;AAAA,IACA,UAAU,CAAC,SAAS;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,qBAAqB,EAAE,CAAC;AAAA,IAClC,UAAU,SAAS,iBAAiB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,QAAG,mBAAmB,GAAG,CAAC;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,cAAc,CAAC,iBAAiB;AAAA,IAChC,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,aAAa;AAAA,MACf;AAAA,MACA,eAAe,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,iBAAiB;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,QACxB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA;AAAA;AAAA,QAGrB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAIH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,cAAc,OAAO,UAAU;AAAA,EAC/B,QAAQ,OAAO,OAAO;AAAA,EACtB,YAAY;AAAA,EACZ,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQR,IAAI,OAAO;AACT,WAAO;AAAA,EACT;AAAA,EACA,IAAI,KAAK,OAAO;AAAA,EAEhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,eAAe;AACjB,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EACA,IAAI,aAAa,cAAc;AAC7B,SAAK,qBAAqB;AAC1B,QAAI,KAAK,QAAQ,CAAC,KAAK,iBAAiB,CAAC,KAAK,oBAAoB;AAChE;AAAA,IACF;AAGA,QAAI,KAAK,kBAAkB;AACzB,WAAK,OAAO;AAAA,IACd,WAAW,KAAK,qBAAqB,OAAO;AAC1C,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,MAAM,WAAW,KAAK,KAAK;AAAA,EACzC;AAAA,EACA,IAAI,WAAW,YAAY;AACzB,SAAK,mBAAmB;AACxB,QAAI,YAAY;AACd,WAAK,OAAO;AAAA,IACd,OAAO;AACL,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA,WAAW;AACT,WAAO,KAAK,kBAAkB,KAAK,YAAY,cAAc,aAAa,KAAK,KAAK;AAAA,EACtF;AAAA;AAAA,EAEA,aAAa,IAAI,aAAa;AAAA;AAAA,EAE9B,iBAAiB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC,OAAO,qBAAqB;AAAA;AAAA,EAE5B,aAAa,IAAI,QAAQ;AAAA;AAAA,EAEzB,eAAe,IAAI,QAAQ;AAAA,EAC3B,qBAAqB;AAAA,EACrB,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOnB,eAAe;AAAA,EACf;AAAA;AAAA,EAEA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,OAAO;AACd,QAAI,UAAU,KAAK,OAAO;AACxB,WAAK,QAAQ;AACb,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,aAAa;AAEf,QAAI,KAAK,MAAM,aAAa,iBAAiB,UAAa,CAAC,KAAK,MAAM,YAAY,aAAa,KAAK,KAAK,GAAG;AAC1G,aAAO;AAAA,IAET,WAAW,KAAK,MAAM,aAAa,iBAAiB,UAAa,KAAK,MAAM,aAAa,eAAe,KAAK,KAAK,EAAE,WAAW,GAAG;AAChI,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAI,QAAQ;AAIV,WAAO,KAAK,MAAM,UAAU,KAAK,KAAK,KAAK,KAAK;AAAA,EAClD;AAAA;AAAA,EAEA,gBAAgB;AACd,QAAI,KAAK,MAAM,aAAa;AAC1B,UAAI,KAAK,YAAY;AACnB,eAAO;AAAA,MACT;AAGA,aAAO;AAAA,IACT;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB;AACjB,QAAI,CAAC,KAAK,cAAc,GAAG;AACzB,aAAO;AAAA,IACT;AACA,WAAO,OAAO,KAAK,UAAU;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACZ,WAAO,KAAK,MAAM,YAAY,KAAK,KAAK;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,WAAO,KAAK,MAAM,kBAAkB,KAAK,KAAK;AAAA,EAChD;AAAA,EACA,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,cAAc;AACZ,iBAAY,qBAAqB;AAAA,EACnC;AAAA,EACA,WAAW;AACT,SAAK,uBAAuB,uBAAuB,KAAK,YAAY,aAAa;AACjF,SAAK,MAAM,mBAAmB,EAAE,QAAQ,KAAK,IAAI,MAAM,KAAK,UAAU,GAAG,qBAAqB,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,mBAAmB,aAAa,CAAC;AACnL,SAAK,MAAM,oBAAoB,KAAK,KAAK;AACzC,SAAK,MAAM,cAAc,IAAI;AAAA,EAC/B;AAAA,EACA,cAAc;AAGZ,QAAI,aAAY,uBAAuB,MAAM;AAC3C,mBAAY,qBAAqB;AAAA,IACnC;AACA,SAAK,aAAa,SAAS;AAC3B,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA,EACA,YAAY;AACV,WAAO,KAAK,MAAM,eAAe,IAAI,KAAK;AAAA,EAC5C;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,MAAM,iBAAiB,IAAI;AAAA,EACzC;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,YAAY;AACjB,QAAI,KAAK,cAAc;AACrB,WAAK,YAAY,cAAc,MAAM;AAAA,IACvC;AACA,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,YAAY;AACjB,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,WAAW;AACT,QAAI,KAAK,YAAY;AACnB;AAAA,IACF;AACA,SAAK,WAAW,KAAK,KAAK,KAAK;AAAA,EACjC;AAAA;AAAA,EAEA,WAAW;AACT,QAAI,KAAK,cAAc;AACrB,WAAK,MAAM,SAAS,KAAK,KAAK;AAAA,IAChC;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AACP,QAAI,KAAK,cAAc;AACrB,WAAK,MAAM,OAAO,KAAK,KAAK;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB;AACd,SAAK,YAAY;AACjB,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA,EACA,aAAa;AACX,QAAI,KAAK,YAAY;AACnB;AAAA,IACF;AACA,SAAK,MAAM,YAAY,UAAU,IAAI;AAAA,EACvC;AAAA,EACA,iBAAiB;AACf,QAAI,KAAK,YAAY;AACnB;AAAA,IACF;AACA,SAAK,eAAe;AACpB,SAAK,MAAM,YAAY,UAAU,IAAI;AACrC,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,oBAAoB,UAAU;AAC5B,SAAK,eAAe,KAAK,QAAQ;AAAA,EACnC;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,IAC7B,WAAW,CAAC,QAAQ,YAAY,GAAG,eAAe;AAAA,IAClD,UAAU;AAAA,IACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,uCAAuC;AACrE,iBAAO,IAAI,eAAe;AAAA,QAC5B,CAAC,EAAE,SAAS,SAAS,uCAAuC;AAC1D,iBAAO,IAAI,WAAW;AAAA,QACxB,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,YAAY,IAAI,SAAS;AAC1C,QAAG,YAAY,iBAAiB,IAAI,iBAAiB,CAAC,EAAE,cAAc,IAAI,QAAQ,CAAC,EAAE,iBAAiB,IAAI,kBAAkB,CAAC,EAAE,gBAAgB,IAAI,YAAY,CAAC;AAAA,MAClK;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,MAClE,YAAY;AAAA,MACZ,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,gBAAgB,CAAC,GAAG,6BAA6B,gBAAgB;AAAA,IACnE;AAAA,IACA,SAAS;AAAA,MACP,YAAY;AAAA,MACZ,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,EAC1B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,wBAAwB;AAAA,QACxB,qBAAqB;AAAA,QACrB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,cAAc;AAAA,QACd,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,uBAAuB,aAAa;AAC3C,MAAI,SAAS,YAAY;AACzB,SAAO,UAAU,CAAC,cAAc,MAAM,GAAG;AACvC,aAAS,OAAO;AAAA,EAClB;AACA,MAAI,CAAC,QAAQ;AACX,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,YAAM,MAAM,oDAAoD;AAAA,IAClE,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,WAAW,OAAO,UAAU,SAAS,sBAAsB,GAAG;AAC5D,WAAO,gBAAgB,OAAO,aAAa,YAAY,CAAC;AAAA,EAC1D,OAAO;AAEL,WAAO;AAAA,EACT;AACF;AACA,SAAS,cAAc,SAAS;AAC9B,QAAM,YAAY,QAAQ;AAC1B,SAAO,CAAC,EAAE,WAAW,SAAS,sBAAsB,KAAK,WAAW,SAAS,UAAU;AACzF;AAQA,IAAM,oBAAN,MAAM,2BAA0B,YAAY;AAAA,EAC1C,QAAQ;AAAA,EACR,WAAW,OAAO,eAAe;AAAA;AAAA,EAEjC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EACA,cAAc;AACZ,UAAM;AAAA,EACR;AAAA,EACA,qBAAqB;AACnB,SAAK,cAAc,KAAK,SAAS,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK,MAAM,OAAO;AACnE,SAAK,MAAM,mBAAmB,KAAK,IAAI,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU,KAAK,oBAAoB,MAAM,CAAC;AAC9H,SAAK,WAAW,QAAQ,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,oBAAoB,CAAC;AAAA,EACrG;AAAA,EACA,cAAc;AACZ,SAAK,OAAO;AACZ,UAAM,YAAY;AAAA,EACpB;AAAA;AAAA,EAEA,oBAAoB,UAAU;AAC5B,UAAM,SAAS,KAAK,eAAe;AACnC,QAAI,UAAU;AACZ,WAAK,YAAY;AAAA,IACnB;AACA,QAAI,UAAU,KAAK,WAAW;AAC5B,YAAM,gBAAgB,OAAO;AAC7B,WAAK,MAAM,kBAAkB,KAAK,WAAW,KAAK,aAAa,eAAe,KAAK,KAAK;AAAA,IAC1F,OAAO;AAEL,WAAK,YAAY,KAAK,CAAC,CAAC;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AACP,UAAM,SAAS,KAAK,eAAe;AACnC,QAAI,QAAQ;AACV,aAAO,cAAc,MAAM;AAC3B,WAAK,YAAY,KAAK,CAAC,CAAC;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,UAAM,UAAU,KAAK;AAGrB,WAAO,WAAW,QAAQ,KAAK,YAAU,CAAC,OAAO,SAAS,OAAO,UAAU,IAAI;AAAA,EACjF;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,IACpC,gBAAgB,SAAS,iCAAiC,IAAI,KAAK,UAAU;AAC3E,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,mBAAmB,CAAC;AAAA,MAClD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa;AAAA,MAChE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,sBAAsB;AAAA,IACrC,UAAU,CAAC,mBAAmB;AAAA,IAC9B,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA;AAAA;AAAA,QAGxB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,iBAAiB;AAKvB,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,YAAY,OAAO,WAAW;AAAA,EAC9B,QAAQ,OAAO,OAAO;AAAA,EACtB,WAAW,OAAO,UAAU;AAAA,EAC5B,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED;AAAA;AAAA,EAEA,aAAa,IAAI,QAAQ;AAAA;AAAA,EAEzB,cAAc;AAAA;AAAA,EAEd,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,eAAe,KAAK;AAAA,EAC3B;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,QAAQ;AACjB,SAAK,gBAAgB,MAAM;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,EACV,cAAc;AACZ,SAAK,YAAY;AACjB,SAAK,MAAM,OAAO,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,YAAY,IAAI,CAAC;AAIzF,SAAK,UAAU,aAAa,UAAU,MAAM,KAAK,YAAY,CAAC;AAAA,EAChE;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA,EAEA,iBAAiB;AACf,UAAM,aAAa,KAAK,UAAU,QAAQ,KAAK,MAAM,UAAU,KAAK,UAAU,IAAI,MAAM;AACxF,UAAM,QAAQ,KAAK,UAAU,OAAO,YAAY,KAAK;AACrD,WAAO,OAAO,UAAU,WAAW,GAAG,QAAQ,KAAK,OAAO,GAAG,KAAK,WAAW,KAAK;AAAA,EACpF;AAAA,EACA,YAAY,cAAc,OAAO;AAC/B,UAAM,UAAU,KAAK,eAAe;AACpC,QAAI,YAAY,KAAK,mBAAmB,aAAa;AACnD,YAAM,UAAU,KAAK,SAAS;AAC9B,YAAM,cAAc,KAAK,QAAQ,KAAK,KAAK,UAAU,QAAQ,iBAAiB;AAC9E,YAAM,YAAY,gBAAgB,gBAAgB,iBAAiB;AACnE,cAAQ,MAAM,WAAW,IAAI,WAAW;AACxC,cAAQ,MAAM,SAAS,IAAI;AAC3B,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,OAAO;AAIpB,SAAK,SAAS,MAAM,KAAK,IAAI,OAAO;AACpC,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,QAAQ;AACtB,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,OAAO,WAAW,UAAU;AAC9B,YAAM,QAAQ,OAAO,MAAM,cAAc;AACzC,cAAQ,MAAM,CAAC;AACf,cAAQ,MAAM,CAAC,KAAK;AAAA,IACtB;AACA,SAAK,cAAc;AACnB,SAAK,UAAU,gBAAgB,KAAK;AACpC,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,IAC1C,QAAQ;AAAA,MACN,OAAO,CAAC,GAAG,sBAAsB,SAAS,eAAe;AAAA,MACzD,QAAQ,CAAC,GAAG,4BAA4B,QAAQ;AAAA,IAClD;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,QAAQ,OAAO,OAAO;AAAA,EACtB,YAAY,OAAO,WAAW;AAAA;AAAA,EAE9B,YAAY;AAAA,EACZ,cAAc;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,UAAU;AACR,SAAK,YAAY,KAAK,MAAM,kBAAkB,KAAK,UAAU,IAAI,IAAI,KAAK,MAAM,OAAO,KAAK,UAAU,IAAI;AAC1G,SAAK,MAAM,YAAY,UAAU,KAAK,SAAS;AAAA,EACjD;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,IACzC,WAAW,CAAC,YAAY,IAAI;AAAA,IAC5B,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,2CAA2C,QAAQ;AACjF,cAAI,QAAQ;AACZ,iBAAO,OAAO,gBAAgB;AAAA,QAChC,CAAC,EAAE,iBAAiB,SAAS,mDAAmD,QAAQ;AACtF,cAAI,QAAQ;AACZ,iBAAO,OAAO,eAAe;AAAA,QAC/B,CAAC,EAAE,iBAAiB,SAAS,mDAAmD,QAAQ;AACtF,cAAI,QAAQ;AACZ,iBAAO,OAAO,eAAe;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,WAAW,CAAC,GAAG,8BAA8B,aAAa,gBAAgB;AAAA,IAC5E;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,WAAW;AAAA,QACX,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,YAAY;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAwB,CAAC,mBAAmB,gBAAgB,oBAAoB,mBAAmB,SAAS,aAAa,iBAAiB;AAChJ,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,mBAAmB,gBAAgB,oBAAoB,mBAAmB,SAAS,aAAa,iBAAiB;AAAA,IAC3H,SAAS,CAAC,mBAAmB,gBAAgB,oBAAoB,mBAAmB,SAAS,aAAa,iBAAiB;AAAA,EAC7H,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACl5DH;AACA;AAGA;AACA;AAOA,SAAS,qBAAqB,YAAY;AACxC,SAAO,CAAC,CAAC,WAAW;AACtB;AAIA,IAAM,cAAN,MAAM,qBAAoB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASpC,IAAI,uBAAuB;AACzB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,qBAAqB,OAAO;AAE9B,SAAK,wBAAwB;AAAA,EAC/B;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,kBAAkB;AAAA,EAClB,wBAAwB;AACtB,QAAI,qBAAqB,KAAK,MAAM,WAAW,GAAG;AAChD,aAAO,KAAK;AAAA,IACd;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,cAAc;AACZ,UAAM;AACN,UAAM,WAAW,OAAO,IAAI,mBAAmB,UAAU,GAAG;AAAA,MAC1D,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,uBAAuB,OAAO,QAAQ,KAAK,KAAK;AAAA,EACvD;AAAA;AAAA;AAAA,EAGA,WAAW;AACT,UAAM,SAAS;AAAA,EACjB;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAAA,EACpB;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,IAC7B,WAAW,CAAC,GAAG,eAAe;AAAA,IAC9B,UAAU;AAAA,IACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,uCAAuC;AACrE,iBAAO,IAAI,WAAW;AAAA,QACxB,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,YAAY,IAAI,sBAAsB,CAAC;AACxD,QAAG,YAAY,iBAAiB,IAAI,iBAAiB,CAAC,EAAE,cAAc,IAAI,QAAQ,CAAC,EAAE,iBAAiB,IAAI,kBAAkB,CAAC,EAAE,gBAAgB,IAAI,YAAY,CAAC;AAAA,MAClK;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,sBAAsB,CAAC,GAAG,YAAY,wBAAwB,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK,CAAC;AAAA,MACjH,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACxD;AAAA,IACA,SAAS;AAAA,MACP,YAAY;AAAA,MACZ,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,IACxB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS,CAAC,cAAc,gBAAgB;AAAA,MACxC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,wBAAwB;AAAA,QACxB,qBAAqB;AAAA,QACrB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,WAAW;AAAA,QACX,cAAc;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK;AAAA,QAC7D,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,iBAAN,MAAM,wBAAuB,eAAe;AAAA,EAC1C;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,uBAAuB,mBAAmB;AACxD,cAAQ,gCAAgC,8BAAiC,sBAAsB,eAAc,IAAI,qBAAqB,eAAc;AAAA,IACtJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC;AAAA,IACtC,QAAQ;AAAA,MACN,MAAM,CAAC,GAAG,sBAAsB,MAAM;AAAA,MACtC,MAAM,CAAC,GAAG,eAAe,MAAM;AAAA,IACjC;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,QAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,MACD,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAIH,IAAM,oBAAN,MAAM,2BAA0B,kBAAkB;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,KAAK,KAAK;AAAA,EACrC;AAAA,EACA,IAAI,SAAS,OAAO;AAElB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,UAAM,SAAS;AAAA,EACjB;AAAA,EACA,qBAAqB;AACnB,UAAM,mBAAmB;AAAA,EAC3B;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAAA,EACpB;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,0BAA0B,mBAAmB;AAC3D,cAAQ,mCAAmC,iCAAoC,sBAAsB,kBAAiB,IAAI,qBAAqB,kBAAiB;AAAA,IAClK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,IACpC,WAAW,CAAC,GAAG,sBAAsB;AAAA,IACrC,QAAQ;AAAA,MACN,MAAM,CAAC,GAAG,qBAAqB,MAAM;AAAA,MACrC,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,UAAU,CAAC,GAAG,YAAY,YAAY,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK,CAAC;AAAA,IAC3F;AAAA,IACA,SAAS;AAAA,MACP,YAAY;AAAA,MACZ,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU,CAAC,mBAAmB;AAAA,IAC9B,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,IACf,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS,CAAC,cAAc,gBAAgB;AAAA,MACxC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK;AAAA,MAC/D,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,qBAAN,MAAM,4BAA2B,mBAAmB;AAAA;AAAA,EAElD,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,eAAe,KAAK;AAAA,EAC3B;AAAA;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,QAAQ;AACjB,SAAK,gBAAgB,MAAM;AAAA,EAC7B;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,2BAA2B,mBAAmB;AAC5D,cAAQ,oCAAoC,kCAAqC,sBAAsB,mBAAkB,IAAI,qBAAqB,mBAAkB;AAAA,IACtK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,IAC1C,QAAQ;AAAA,MACN,OAAO,CAAC,GAAG,sBAAsB,SAAS,eAAe;AAAA,MACzD,QAAQ,CAAC,GAAG,4BAA4B,QAAQ;AAAA,IAClD;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,gBAAgB,OAAO,gBAAgB;AAAA,EACvC,QAAQ,OAAO,2BAA2B;AAAA,IACxC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,IACzC,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,UAAN,MAAM,iBAAgB,QAAQ;AAAA;AAAA;AAAA,EAG5B,cAAc;AAAA,EACd,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,gBAAgB,mBAAmB;AACjD,cAAQ,yBAAyB,uBAA0B,sBAAsB,QAAO,IAAI,qBAAqB,QAAO;AAAA,IAC1H;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,IACxB,WAAW,SAAS,cAAc,IAAI,KAAK;AACzC,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,mBAAmB,CAAC;AAAA,MACrC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,UAAU;AAAA,IACzB,UAAU,CAAC,SAAS;AAAA,IACpB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,qBAAqB,EAAE,CAAC;AAAA,IAClC,UAAU,SAAS,iBAAiB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,QAAG,mBAAmB,GAAG,CAAC;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,cAAc,CAAC,iBAAiB;AAAA,IAChC,QAAQ,CAAC,imBAAimB;AAAA,IAC1mB,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,SAAS,CAAC,iBAAiB;AAAA,MAC3B,QAAQ,CAAC,imBAAimB;AAAA,IAC5mB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,QACxB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,oBAAN,MAAM,2BAA0B,kBAAkB;AAAA,EAChD,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,0BAA0B,mBAAmB;AAC3D,cAAQ,mCAAmC,iCAAoC,sBAAsB,kBAAiB,IAAI,qBAAqB,kBAAiB;AAAA,IAClK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,IACzC,QAAQ;AAAA,MACN,WAAW,CAAC,GAAG,8BAA8B,WAAW;AAAA,IAC1D;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,QAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,sBAAsB,CAAC,mBAAmB,gBAAgB,oBAAoB,mBAAmB,SAAS,aAAa,iBAAiB;AAC9I,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,eAAe,iBAAiB,mBAAmB,gBAAgB,oBAAoB,mBAAmB,SAAS,aAAa,iBAAiB;AAAA,IAC3J,SAAS,CAAC,iBAAiB,mBAAmB,gBAAgB,oBAAoB,mBAAmB,SAAS,aAAa,iBAAiB;AAAA,EAC9I,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,eAAe,iBAAiB,eAAe;AAAA,EAC3D,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,eAAe,iBAAiB,GAAG,mBAAmB;AAAA,MAChE,SAAS,CAAC,iBAAiB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAwCH,IAAM,mBAAN,MAAuB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,mBAAmB,UAAU,cAAc,aAAa;AAClE,SAAK,oBAAoB;AACzB,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,aAAa,MAAM,OAAO,aAAa,WAAW;AAChD,UAAM,WAAW,KAAK,kBAAkB,MAAM,KAAK;AACnD,gBAAY,KAAK,QAAQ;AACzB,QAAI,KAAK,aAAa,QAAQ,GAAG;AAC/B,YAAM,gBAAgB,KAAK,YAAY,IAAI;AAC3C,UAAI,eAAe;AACjB,YAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,eAAK,iBAAiB,eAAe,OAAO,aAAa,SAAS;AAAA,QACpE,OAAO;AACL,wBAAc,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,cAAY;AAChD,iBAAK,iBAAiB,UAAU,OAAO,aAAa,SAAS;AAAA,UAC/D,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,UAAU,OAAO,aAAa,WAAW;AACxD,aAAS,QAAQ,CAAC,OAAO,UAAU;AACjC,UAAI,iBAAiB,UAAU,MAAM;AACrC,qBAAe,KAAK,SAAS,SAAS,SAAS,CAAC;AAChD,WAAK,aAAa,OAAO,QAAQ,GAAG,aAAa,cAAc;AAAA,IACjE,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,gBAAgB;AAC3B,QAAI,cAAc,CAAC;AACnB,mBAAe,QAAQ,UAAQ,KAAK,aAAa,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC;AAC1E,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,OAAO,aAAa;AACvC,QAAI,UAAU,CAAC;AACf,QAAI,gBAAgB,CAAC;AACrB,kBAAc,CAAC,IAAI;AACnB,UAAM,QAAQ,UAAQ;AACpB,UAAI,SAAS;AACb,eAAS,IAAI,GAAG,KAAK,KAAK,SAAS,IAAI,GAAG,KAAK;AAC7C,iBAAS,UAAU,cAAc,CAAC;AAAA,MACpC;AACA,UAAI,QAAQ;AACV,gBAAQ,KAAK,IAAI;AAAA,MACnB;AACA,UAAI,KAAK,aAAa,IAAI,GAAG;AAC3B,sBAAc,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,YAAY,WAAW,IAAI;AAAA,MACtE;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAYA,IAAM,wBAAN,cAAoC,WAAW;AAAA,EAC7C;AAAA,EACA;AAAA,EACA,iBAAiB,IAAI,gBAAgB,CAAC,CAAC;AAAA,EACvC,gBAAgB,IAAI,gBAAgB,CAAC,CAAC;AAAA,EACtC,IAAI,OAAO;AACT,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,KAAK,OAAO;AACd,SAAK,MAAM,KAAK,KAAK;AACrB,SAAK,eAAe,KAAK,KAAK,eAAe,aAAa,KAAK,IAAI,CAAC;AACpE,SAAK,aAAa,YAAY,KAAK,eAAe;AAAA,EACpD;AAAA,EACA,QAAQ,IAAI,gBAAgB,CAAC,CAAC;AAAA,EAC9B,YAAY,cAAc,gBAAgB,aAAa;AACrD,UAAM;AACN,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,QAAI,aAAa;AAEf,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,QAAQ,kBAAkB;AACxB,WAAO,MAAM,iBAAiB,YAAY,KAAK,aAAa,eAAe,SAAS,KAAK,cAAc,EAAE,KAAK,IAAI,MAAM;AACtH,WAAK,cAAc,KAAK,KAAK,eAAe,qBAAqB,KAAK,eAAe,OAAO,KAAK,YAAY,CAAC;AAC9G,aAAO,KAAK,cAAc;AAAA,IAC5B,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,aAAa;AAAA,EAEb;AACF;AAQA,IAAM,0BAAN,cAAsC,WAAW;AAAA;AAAA;AAAA;AAAA,EAI/C,IAAI,OAAO;AACT,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,KAAK,OAAO;AACd,SAAK,MAAM,KAAK,KAAK;AAAA,EACvB;AAAA,EACA,QAAQ,IAAI,gBAAgB,CAAC,CAAC;AAAA,EAC9B,QAAQ,kBAAkB;AACxB,WAAO,MAAM,GAAG,CAAC,iBAAiB,YAAY,KAAK,KAAK,CAAC,EAAE,KAAK,IAAI,MAAM,KAAK,IAAI,CAAC;AAAA,EACtF;AAAA,EACA,aAAa;AAAA,EAEb;AACF;", "names": ["items"]}
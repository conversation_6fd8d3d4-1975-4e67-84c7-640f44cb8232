{"version": 3, "sources": ["../../../../../../node_modules/ngx-bootstrap/utils/fesm2022/ngx-bootstrap-utils.mjs", "../../../../../../node_modules/ngx-bootstrap/positioning/fesm2022/ngx-bootstrap-positioning.mjs", "../../../../../../node_modules/ngx-bootstrap/component-loader/fesm2022/ngx-bootstrap-component-loader.mjs"], "sourcesContent": ["import { isDevMode } from '@angular/core';\n\n/**\n * @copyright Valor Software\n * @copyright Angular ng-bootstrap team\n */\nclass Trigger {\n    constructor(open, close) {\n        this.open = open;\n        this.close = close || open;\n    }\n    isManual() {\n        return this.open === 'manual' || this.close === 'manual';\n    }\n}\n\nconst DEFAULT_ALIASES = {\n    hover: ['mouseover', 'mouseout'],\n    focus: ['focusin', 'focusout']\n};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction parseTriggers(triggers, aliases = DEFAULT_ALIASES) {\n    const trimmedTriggers = (triggers || '').trim();\n    if (trimmedTriggers.length === 0) {\n        return [];\n    }\n    const parsedTriggers = trimmedTriggers\n        .split(/\\s+/)\n        .map((trigger) => trigger.split(':'))\n        .map((triggerPair) => {\n        const alias = aliases[triggerPair[0]] || triggerPair;\n        return new Trigger(alias[0], alias[1]);\n    });\n    const manualTriggers = parsedTriggers.filter((triggerPair) => triggerPair.isManual());\n    if (manualTriggers.length > 1) {\n        throw new Error('Triggers parse error: only one manual trigger is allowed');\n    }\n    if (manualTriggers.length === 1 && parsedTriggers.length > 1) {\n        throw new Error('Triggers parse error: manual trigger can\\'t be mixed with other triggers');\n    }\n    return parsedTriggers;\n}\nfunction listenToTriggers(renderer, \n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntarget, triggers, showFn, hideFn, toggleFn) {\n    const parsedTriggers = parseTriggers(triggers);\n    const listeners = [];\n    if (parsedTriggers.length === 1 && parsedTriggers[0].isManual()) {\n        return Function.prototype;\n    }\n    parsedTriggers.forEach((trigger) => {\n        if (trigger.open === trigger.close) {\n            listeners.push(renderer.listen(target, trigger.open, toggleFn));\n            return;\n        }\n        listeners.push(renderer.listen(target, trigger.open, showFn));\n        if (trigger.close) {\n            listeners.push(renderer.listen(target, trigger.close, hideFn));\n        }\n    });\n    return () => {\n        listeners.forEach((unsubscribeFn) => unsubscribeFn());\n    };\n}\nfunction listenToTriggersV2(renderer, options) {\n    const parsedTriggers = parseTriggers(options.triggers);\n    const target = options.target;\n    // do nothing\n    if (parsedTriggers.length === 1 && parsedTriggers[0].isManual()) {\n        return Function.prototype;\n    }\n    // all listeners\n    const listeners = [];\n    // lazy listeners registration\n    const _registerHide = [];\n    const registerHide = () => {\n        // add hide listeners to unregister array\n        _registerHide.forEach((fn) => listeners.push(fn()));\n        // register hide events only once\n        _registerHide.length = 0;\n    };\n    // register open\\close\\toggle listeners\n    parsedTriggers.forEach((trigger) => {\n        const useToggle = trigger.open === trigger.close;\n        const showFn = useToggle ? options.toggle : options.show;\n        if (!useToggle && trigger.close && options.hide) {\n            const triggerClose = trigger.close;\n            const optionsHide = options.hide;\n            const _hide = () => renderer.listen(target, triggerClose, optionsHide);\n            _registerHide.push(_hide);\n        }\n        if (showFn) {\n            listeners.push(renderer.listen(target, trigger.open, () => showFn(registerHide)));\n        }\n    });\n    return () => {\n        listeners.forEach((unsubscribeFn) => unsubscribeFn());\n    };\n}\nfunction registerOutsideClick(renderer, options) {\n    if (!options.outsideClick) {\n        return Function.prototype;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return renderer.listen('document', 'click', (event) => {\n        if (options.target && options.target.contains(event.target)) {\n            return;\n        }\n        if (options.targets &&\n            options.targets.some(target => target.contains(event.target))) {\n            return;\n        }\n        if (options.hide) {\n            options.hide();\n        }\n    });\n}\nfunction registerEscClick(renderer, options) {\n    if (!options.outsideEsc) {\n        return Function.prototype;\n    }\n    return renderer.listen('document', 'keyup.esc', (event) => {\n        if (options.target && options.target.contains(event.target)) {\n            return;\n        }\n        if (options.targets &&\n            options.targets.some(target => target.contains(event.target))) {\n            return;\n        }\n        if (options.hide) {\n            options.hide();\n        }\n    });\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * JS version of browser APIs. This library can only run in the browser.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst win = (typeof window !== 'undefined' && window) || {};\nconst document = win.document;\nconst location = win.location;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst gc = win.gc ? () => win.gc() : () => null;\nconst performance = win.performance ? win.performance : null;\nconst Event = win.Event;\nconst MouseEvent = win.MouseEvent;\nconst KeyboardEvent = win.KeyboardEvent;\nconst EventTarget = win.EventTarget;\nconst History = win.History;\nconst Location = win.Location;\nconst EventListener = win.EventListener;\n\nvar BsVerions;\n(function (BsVerions) {\n    BsVerions[\"isBs4\"] = \"bs4\";\n    BsVerions[\"isBs5\"] = \"bs5\";\n})(BsVerions || (BsVerions = {}));\nlet guessedVersion;\nfunction _guessBsVersion() {\n    const spanEl = win.document.createElement('span');\n    spanEl.innerText = 'testing bs version';\n    spanEl.classList.add('d-none');\n    spanEl.classList.add('pl-1');\n    win.document.head.appendChild(spanEl);\n    const checkPadding = win.getComputedStyle(spanEl).paddingLeft;\n    if (checkPadding && parseFloat(checkPadding)) {\n        win.document.head.removeChild(spanEl);\n        return 'bs4';\n    }\n    win.document.head.removeChild(spanEl);\n    return 'bs5';\n}\nfunction setTheme(theme) {\n    guessedVersion = theme;\n}\nfunction isBs4() {\n    if (guessedVersion)\n        return guessedVersion === 'bs4';\n    guessedVersion = _guessBsVersion();\n    return guessedVersion === 'bs4';\n}\nfunction isBs5() {\n    if (guessedVersion)\n        return guessedVersion === 'bs5';\n    guessedVersion = _guessBsVersion();\n    return guessedVersion === 'bs5';\n}\nfunction getBsVer() {\n    return {\n        isBs4: isBs4(),\n        isBs5: isBs5()\n    };\n}\nfunction currentBsVersion() {\n    const bsVer = getBsVer();\n    const resVersion = Object.keys(bsVer).find(key => bsVer[key]);\n    return BsVerions[resVersion];\n}\n\nclass LinkedList {\n    constructor() {\n        this.length = 0;\n        this.asArray = [];\n        // Array methods overriding END\n    }\n    get(position) {\n        if (this.length === 0 || position < 0 || position >= this.length) {\n            return void 0;\n        }\n        let current = this.head;\n        for (let index = 0; index < position; index++) {\n            current = current?.next;\n        }\n        return current?.value;\n    }\n    add(value, position = this.length) {\n        if (position < 0 || position > this.length) {\n            throw new Error('Position is out of the list');\n        }\n        const node = {\n            value,\n            next: undefined,\n            previous: undefined\n        };\n        if (this.length === 0) {\n            this.head = node;\n            this.tail = node;\n            this.current = node;\n        }\n        else {\n            if (position === 0 && this.head) {\n                // first node\n                node.next = this.head;\n                this.head.previous = node;\n                this.head = node;\n            }\n            else if (position === this.length && this.tail) {\n                // last node\n                this.tail.next = node;\n                node.previous = this.tail;\n                this.tail = node;\n            }\n            else {\n                // node in middle\n                const currentPreviousNode = this.getNode(position - 1);\n                const currentNextNode = currentPreviousNode?.next;\n                if (currentPreviousNode && currentNextNode) {\n                    currentPreviousNode.next = node;\n                    currentNextNode.previous = node;\n                    node.previous = currentPreviousNode;\n                    node.next = currentNextNode;\n                }\n            }\n        }\n        this.length++;\n        this.createInternalArrayRepresentation();\n    }\n    remove(position = 0) {\n        if (this.length === 0 || position < 0 || position >= this.length) {\n            throw new Error('Position is out of the list');\n        }\n        if (position === 0 && this.head) {\n            // first node\n            this.head = this.head.next;\n            if (this.head) {\n                // there is no second node\n                this.head.previous = undefined;\n            }\n            else {\n                // there is no second node\n                this.tail = undefined;\n            }\n        }\n        else if (position === this.length - 1 && this.tail?.previous) {\n            // last node\n            this.tail = this.tail.previous;\n            this.tail.next = undefined;\n        }\n        else {\n            // middle node\n            const removedNode = this.getNode(position);\n            if (removedNode?.next && removedNode.previous) {\n                removedNode.next.previous = removedNode.previous;\n                removedNode.previous.next = removedNode.next;\n            }\n        }\n        this.length--;\n        this.createInternalArrayRepresentation();\n    }\n    set(position, value) {\n        if (this.length === 0 || position < 0 || position >= this.length) {\n            throw new Error('Position is out of the list');\n        }\n        const node = this.getNode(position);\n        if (node) {\n            node.value = value;\n            this.createInternalArrayRepresentation();\n        }\n    }\n    toArray() {\n        return this.asArray;\n    }\n    findAll(fn) {\n        let current = this.head;\n        const result = [];\n        if (!current) {\n            return result;\n        }\n        for (let index = 0; index < this.length; index++) {\n            if (!current) {\n                return result;\n            }\n            if (fn(current.value, index)) {\n                result.push({ index, value: current.value });\n            }\n            current = current.next;\n        }\n        return result;\n    }\n    // Array methods overriding start\n    push(...args) {\n        args.forEach((arg) => {\n            this.add(arg);\n        });\n        return this.length;\n    }\n    pop() {\n        if (this.length === 0) {\n            return undefined;\n        }\n        const last = this.tail;\n        this.remove(this.length - 1);\n        return last?.value;\n    }\n    unshift(...args) {\n        args.reverse();\n        args.forEach((arg) => {\n            this.add(arg, 0);\n        });\n        return this.length;\n    }\n    shift() {\n        if (this.length === 0) {\n            return undefined;\n        }\n        const lastItem = this.head?.value;\n        this.remove();\n        return lastItem;\n    }\n    forEach(fn) {\n        let current = this.head;\n        for (let index = 0; index < this.length; index++) {\n            if (!current) {\n                return;\n            }\n            fn(current.value, index);\n            current = current.next;\n        }\n    }\n    indexOf(value) {\n        let current = this.head;\n        let position = -1;\n        for (let index = 0; index < this.length; index++) {\n            if (!current) {\n                return position;\n            }\n            if (current.value === value) {\n                position = index;\n                break;\n            }\n            current = current.next;\n        }\n        return position;\n    }\n    some(fn) {\n        let current = this.head;\n        let result = false;\n        while (current && !result) {\n            if (fn(current.value)) {\n                result = true;\n                break;\n            }\n            current = current.next;\n        }\n        return result;\n    }\n    every(fn) {\n        let current = this.head;\n        let result = true;\n        while (current && result) {\n            if (!fn(current.value)) {\n                result = false;\n            }\n            current = current.next;\n        }\n        return result;\n    }\n    toString() {\n        return '[Linked List]';\n    }\n    find(fn) {\n        let current = this.head;\n        for (let index = 0; index < this.length; index++) {\n            if (!current) {\n                return;\n            }\n            if (fn(current.value, index)) {\n                return current.value;\n            }\n            current = current.next;\n        }\n    }\n    findIndex(fn) {\n        let current = this.head;\n        for (let index = 0; index < this.length; index++) {\n            if (!current) {\n                return -1;\n            }\n            if (fn(current.value, index)) {\n                return index;\n            }\n            current = current.next;\n        }\n        return -1;\n    }\n    getNode(position) {\n        if (this.length === 0 || position < 0 || position >= this.length) {\n            throw new Error('Position is out of the list');\n        }\n        let current = this.head;\n        for (let index = 0; index < position; index++) {\n            current = current?.next;\n        }\n        return current;\n    }\n    createInternalArrayRepresentation() {\n        const outArray = [];\n        let current = this.head;\n        while (current) {\n            outArray.push(current.value);\n            current = current.next;\n        }\n        this.asArray = outArray;\n    }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction OnChange() {\n    const sufix = 'Change';\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return function OnChangeHandler(target, propertyKey) {\n        const _key = ` __${propertyKey}Value`;\n        Object.defineProperty(target, propertyKey, {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            get() {\n                return this[_key];\n            },\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            set(value) {\n                const prevValue = this[_key];\n                this[_key] = value;\n                if (prevValue !== value && this[propertyKey + sufix]) {\n                    this[propertyKey + sufix].emit(value);\n                }\n            }\n        });\n    };\n}\n\nclass Utils {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    static reflow(element) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        ((bs) => bs)(element.offsetHeight);\n    }\n    // source: https://github.com/jquery/jquery/blob/master/src/css/var/getStyles.js\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    static getStyles(elem) {\n        // Support: IE <=11 only, Firefox <=30 (#15098, #14150)\n        // IE throws on elements created in popups\n        // FF meanwhile throws on frame elements through \"defaultView.getComputedStyle\"\n        let view = elem.ownerDocument.defaultView;\n        if (!view || !view.opener) {\n            view = win;\n        }\n        return view.getComputedStyle(elem);\n    }\n    static stackOverflowConfig() {\n        const bsVer = currentBsVersion();\n        return {\n            crossorigin: \"anonymous\",\n            integrity: bsVer === 'bs5' ? 'sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65' : 'sha384-TX8t27EcRE3e/ihU7zmQxVncDAy5uIKz4rEkgIXeMed4M0jlfIDPvg6uqKI2xXr2',\n            cdnLink: bsVer === 'bs5' ? 'https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css' : 'https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/css/bootstrap.min.css',\n        };\n    }\n}\n\nconst _messagesHash = {};\nconst _hideMsg = typeof console === 'undefined' || !('warn' in console);\nfunction warnOnce(msg) {\n    if (!isDevMode() || _hideMsg || msg in _messagesHash) {\n        return;\n    }\n    _messagesHash[msg] = true;\n    console.warn(msg);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BsVerions, LinkedList, OnChange, Trigger, Utils, currentBsVersion, document, getBsVer, listenToTriggers, listenToTriggersV2, parseTriggers, registerEscClick, registerOutsideClick, setTheme, warnOnce, win as window };\n\n", "import { getBsVer } from 'ngx-bootstrap/utils';\nimport * as i0 from '@angular/core';\nimport { PLATFORM_ID, Injectable, Inject, ElementRef } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { Subject, merge, fromEvent, of, animationFrameScheduler } from 'rxjs';\nvar MapPlacementInToRL;\n(function (MapPlacementInToRL) {\n  MapPlacementInToRL[\"top\"] = \"top\";\n  MapPlacementInToRL[\"bottom\"] = \"bottom\";\n  MapPlacementInToRL[\"left\"] = \"left\";\n  MapPlacementInToRL[\"right\"] = \"right\";\n  MapPlacementInToRL[\"auto\"] = \"auto\";\n  MapPlacementInToRL[\"end\"] = \"right\";\n  MapPlacementInToRL[\"start\"] = \"left\";\n  MapPlacementInToRL[\"top left\"] = \"top left\";\n  MapPlacementInToRL[\"top right\"] = \"top right\";\n  MapPlacementInToRL[\"right top\"] = \"right top\";\n  MapPlacementInToRL[\"right bottom\"] = \"right bottom\";\n  MapPlacementInToRL[\"bottom right\"] = \"bottom right\";\n  MapPlacementInToRL[\"bottom left\"] = \"bottom left\";\n  MapPlacementInToRL[\"left bottom\"] = \"left bottom\";\n  MapPlacementInToRL[\"left top\"] = \"left top\";\n  MapPlacementInToRL[\"top start\"] = \"top left\";\n  MapPlacementInToRL[\"top end\"] = \"top right\";\n  MapPlacementInToRL[\"end top\"] = \"right top\";\n  MapPlacementInToRL[\"end bottom\"] = \"right bottom\";\n  MapPlacementInToRL[\"bottom end\"] = \"bottom right\";\n  MapPlacementInToRL[\"bottom start\"] = \"bottom left\";\n  MapPlacementInToRL[\"start bottom\"] = \"start bottom\";\n  MapPlacementInToRL[\"start top\"] = \"left top\";\n})(MapPlacementInToRL || (MapPlacementInToRL = {}));\nvar PlacementForBs5;\n(function (PlacementForBs5) {\n  PlacementForBs5[\"top\"] = \"top\";\n  PlacementForBs5[\"bottom\"] = \"bottom\";\n  PlacementForBs5[\"left\"] = \"start\";\n  PlacementForBs5[\"right\"] = \"end\";\n  PlacementForBs5[\"auto\"] = \"auto\";\n  PlacementForBs5[\"end\"] = \"end\";\n  PlacementForBs5[\"start\"] = \"start\";\n  PlacementForBs5[\"top left\"] = \"top start\";\n  PlacementForBs5[\"top right\"] = \"top end\";\n  PlacementForBs5[\"right top\"] = \"end top\";\n  PlacementForBs5[\"right bottom\"] = \"end bottom\";\n  PlacementForBs5[\"bottom right\"] = \"bottom end\";\n  PlacementForBs5[\"bottom left\"] = \"bottom start\";\n  PlacementForBs5[\"left bottom\"] = \"start bottom\";\n  PlacementForBs5[\"left top\"] = \"start top\";\n  PlacementForBs5[\"top start\"] = \"top start\";\n  PlacementForBs5[\"top end\"] = \"top end\";\n  PlacementForBs5[\"end top\"] = \"end top\";\n  PlacementForBs5[\"end bottom\"] = \"end bottom\";\n  PlacementForBs5[\"bottom end\"] = \"bottom end\";\n  PlacementForBs5[\"bottom start\"] = \"bottom start\";\n  PlacementForBs5[\"start bottom\"] = \"start bottom\";\n  PlacementForBs5[\"start top\"] = \"start top\";\n})(PlacementForBs5 || (PlacementForBs5 = {}));\nfunction getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  const window = element.ownerDocument.defaultView;\n  const css = window?.getComputedStyle(element, null);\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  return property ? css && css[property] : css;\n}\n\n/**\n * Returns the offset parent of the given element\n */\nfunction getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n  const noOffsetParent = null;\n  // NOTE: 1 DOM access here\n  let offsetParent = element?.offsetParent;\n  // Skip hidden elements which don't have an offsetParent\n  let sibling = void 0;\n  while (offsetParent === noOffsetParent && element.nextElementSibling && sibling !== element.nextElementSibling) {\n    // todo: valorkin fix\n    sibling = element.nextElementSibling;\n    offsetParent = sibling.offsetParent;\n  }\n  const nodeName = offsetParent && offsetParent.nodeName;\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return sibling ? sibling.ownerDocument.documentElement : document.documentElement;\n  }\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  if (offsetParent && ['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 && getStyleComputedProperty(offsetParent, 'position') === 'static') {\n    return getOffsetParent(offsetParent);\n  }\n  return offsetParent;\n}\n\n// todo: valorkin fix\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction isOffsetContainer(element) {\n  const {\n    nodeName\n  } = element;\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element;\n}\n\n/**\n * Finds the root node (document, shadowDOM root) of the given element\n */\nfunction getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n  return node;\n}\n\n/**\n * Finds the offset parent common to the two provided nodes\n */\nfunction findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  const order = element1.compareDocumentPosition(element2) & Node.DOCUMENT_POSITION_FOLLOWING;\n  const start = order ? element1 : element2;\n  const end = order ? element2 : element1;\n  // Get common ancestor container\n  const range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  // todo: valorkin fix\n  const commonAncestorContainer = range.commonAncestorContainer;\n  // Both nodes are inside #document\n  if (element1 !== commonAncestorContainer && element2 !== commonAncestorContainer || start.contains(end)) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n    return getOffsetParent(commonAncestorContainer);\n  }\n  // one of the nodes is inside shadowDOM, find which one\n  const element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n\n/**\n * Finds the first parent of an element that has a transformed property defined\n */\nfunction getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element || !element.parentElement) {\n    return document.documentElement;\n  }\n  let el = element.parentElement;\n  while (el?.parentElement && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n}\n\n/**\n * Helper to detect borders of a given element\n */\nfunction getBordersSize(styles, axis) {\n  const sideA = axis === 'x' ? 'Left' : 'Top';\n  const sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n  return parseFloat(styles[`border${sideA}Width`]) + parseFloat(styles[`border${sideB}Width`]);\n}\nfunction getSize(axis, body, html) {\n  const _body = body;\n  const _html = html;\n  return Math.max(_body[`offset${axis}`], _body[`scroll${axis}`], _html[`client${axis}`], _html[`offset${axis}`], _html[`scroll${axis}`], 0);\n}\nfunction getWindowSizes(document) {\n  const body = document.body;\n  const html = document.documentElement;\n  return {\n    height: getSize('Height', body, html),\n    width: getSize('Width', body, html)\n  };\n}\nfunction getClientRect(offsets) {\n  return {\n    ...offsets,\n    right: (offsets.left || 0) + offsets.width,\n    bottom: (offsets.top || 0) + offsets.height\n  };\n}\n\n/**\n * Tells if a given input is a number\n */\nfunction isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(Number(n));\n}\nfunction isNumber(value) {\n  return typeof value === 'number' || Object.prototype.toString.call(value) === '[object Number]';\n}\n\n/**\n * Get bounding client rect of given element\n */\nfunction getBoundingClientRect(element) {\n  const rect = element.getBoundingClientRect();\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  // try {\n  //   if (isIE(10)) {\n  //     const scrollTop = getScroll(element, 'top');\n  //     const scrollLeft = getScroll(element, 'left');\n  //     if (rect && isNumber(rect.top) && isNumber(rect.left) && isNumber(rect.bottom) && isNumber(rect.right)) {\n  //       rect.top += scrollTop;\n  //       rect.left += scrollLeft;\n  //       rect.bottom += scrollTop;\n  //       rect.right += scrollLeft;\n  //     }\n  //   }\n  // } catch (e) {\n  //   return rect;\n  // }\n  if (!(rect && isNumber(rect.top) && isNumber(rect.left) && isNumber(rect.bottom) && isNumber(rect.right))) {\n    return rect;\n  }\n  const result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  };\n  // subtract scrollbar size from sizes\n  const sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : undefined;\n  const width = sizes?.width || element.clientWidth || isNumber(rect.right) && isNumber(result.left) && rect.right - result.left || 0;\n  const height = sizes?.height || element.clientHeight || isNumber(rect.bottom) && isNumber(result.top) && rect.bottom - result.top || 0;\n  let horizScrollbar = element.offsetWidth - width;\n  let vertScrollbar = element.offsetHeight - height;\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    const styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n  return getClientRect(result);\n}\nfunction getOffsetRectRelativeToArbitraryNode(children, parent, fixedPosition = false) {\n  const isHTML = parent.nodeName === 'HTML';\n  const childrenRect = getBoundingClientRect(children);\n  const parentRect = getBoundingClientRect(parent);\n  const styles = getStyleComputedProperty(parent);\n  const borderTopWidth = parseFloat(styles.borderTopWidth);\n  const borderLeftWidth = parseFloat(styles.borderLeftWidth);\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if (fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top ?? 0, 0);\n    parentRect.left = Math.max(parentRect.left ?? 0, 0);\n  }\n  const offsets = getClientRect({\n    top: (childrenRect.top ?? 0) - (parentRect.top ?? 0) - borderTopWidth,\n    left: (childrenRect.left ?? 0) - (parentRect.left ?? 0) - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (isHTML) {\n    const marginTop = parseFloat(styles.marginTop);\n    const marginLeft = parseFloat(styles.marginLeft);\n    if (isNumber(offsets.top)) {\n      offsets.top -= borderTopWidth - marginTop;\n    }\n    if (isNumber(offsets.bottom)) {\n      offsets.bottom -= borderTopWidth - marginTop;\n    }\n    if (isNumber(offsets.left)) {\n      offsets.left -= borderLeftWidth - marginLeft;\n    }\n    if (isNumber(offsets.right)) {\n      offsets.right -= borderLeftWidth - marginLeft;\n    }\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n  return offsets;\n}\n\n/**\n * Returns the parentNode or the host of the element\n */\n// todo: valorkin fix\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n\n/**\n * Returns the scrolling parent of the given element\n */\n// todo: valorkin fix\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body;\n  }\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body;\n    case '#document':\n      return element.body;\n    default:\n  }\n  // Firefox want us to check `-x` and `-y` variations as well\n  const {\n    overflow,\n    overflowX,\n    overflowY\n  } = getStyleComputedProperty(element);\n  if (/(auto|scroll|overlay)/.test(String(overflow) + String(overflowY) + String(overflowX))) {\n    return element;\n  }\n  return getScrollParent(getParentNode(element));\n}\n\n/**\n * Gets the scroll value of the given element in the given side (top and left)\n */\nfunction getScroll(element, side = 'top') {\n  const upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  const nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    const html = element.ownerDocument.documentElement;\n    const scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n  return element[upperSide];\n}\nfunction getViewportOffsetRectRelativeToArtbitraryNode(element, excludeScroll = false) {\n  const html = element.ownerDocument.documentElement;\n  const relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  const width = Math.max(html.clientWidth, window.innerWidth || 0);\n  const height = Math.max(html.clientHeight, window.innerHeight || 0);\n  const scrollTop = !excludeScroll ? getScroll(html) : 0;\n  const scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n  const offset = {\n    top: scrollTop - Number(relativeOffset?.top) + Number(relativeOffset?.marginTop),\n    left: scrollLeft - Number(relativeOffset?.left) + Number(relativeOffset?.marginLeft),\n    width,\n    height\n  };\n  return getClientRect(offset);\n}\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n */\nfunction isFixed(element) {\n  const nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  return isFixed(getParentNode(element));\n}\nfunction getBoundaries(target, host, padding = 0, boundariesElement, fixedPosition = false) {\n  // NOTE: 1 DOM access here\n  let boundaries = {\n    top: 0,\n    left: 0\n  };\n  const offsetParent = fixedPosition ? getFixedPositionOffsetParent(target) : findCommonOffsetParent(target, host);\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    let boundariesNode;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(host));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = target.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = target.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n    const offsets = getOffsetRectRelativeToArbitraryNode(boundariesNode, offsetParent, fixedPosition);\n    // In case of HTML, we need a different computation\n    if (offsets && boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      const {\n        height,\n        width\n      } = getWindowSizes(target.ownerDocument);\n      if (isNumber(boundaries.top) && isNumber(offsets.top) && isNumber(offsets.marginTop)) {\n        boundaries.top += offsets.top - offsets.marginTop;\n      }\n      if (isNumber(boundaries.top)) {\n        boundaries.bottom = Number(height) + Number(offsets.top);\n      }\n      if (isNumber(boundaries.left) && isNumber(offsets.left) && isNumber(offsets.marginLeft)) {\n        boundaries.left += offsets.left - offsets.marginLeft;\n      }\n      if (isNumber(boundaries.top)) {\n        boundaries.right = Number(width) + Number(offsets.left);\n      }\n    } else if (offsets) {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n  // Add paddings\n  if (isNumber(boundaries.left)) {\n    boundaries.left += padding;\n  }\n  if (isNumber(boundaries.top)) {\n    boundaries.top += padding;\n  }\n  if (isNumber(boundaries.right)) {\n    boundaries.right -= padding;\n  }\n  if (isNumber(boundaries.bottom)) {\n    boundaries.bottom -= padding;\n  }\n  return boundaries;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n */\nfunction getArea({\n  width,\n  height\n}) {\n  return width * height;\n}\nfunction computeAutoPlacement(placement, refRect, target, host, allowedPositions = ['top', 'bottom', 'right', 'left'], boundariesElement = 'viewport', padding = 0) {\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n  const boundaries = getBoundaries(target, host, padding, boundariesElement);\n  const rects = {\n    top: {\n      width: boundaries?.width ?? 0,\n      height: (refRect?.top ?? 0) - (boundaries?.top ?? 0)\n    },\n    right: {\n      width: (boundaries?.right ?? 0) - (refRect?.right ?? 0),\n      height: boundaries?.height ?? 0\n    },\n    bottom: {\n      width: boundaries?.width ?? 0,\n      height: (boundaries?.bottom ?? 0) - (refRect?.bottom ?? 0)\n    },\n    left: {\n      width: (refRect.left ?? 0) - (boundaries?.left ?? 0),\n      height: boundaries?.height ?? 0\n    }\n  };\n  const sortedAreas = Object.keys(rects).map(key => ({\n    position: key,\n    ...rects[key],\n    area: getArea(rects[key])\n  })).sort((a, b) => b.area - a.area);\n  let filteredAreas = sortedAreas.filter(({\n    width,\n    height\n  }) => {\n    return width >= target.clientWidth && height >= target.clientHeight;\n  });\n  filteredAreas = filteredAreas.filter(({\n    position\n  }) => {\n    return allowedPositions.some(allowedPosition => {\n      return allowedPosition === position;\n    });\n  });\n  const computedPlacement = filteredAreas.length > 0 ? filteredAreas[0].position : sortedAreas[0].position;\n  const variation = placement.split(' ')[1];\n  // for tooltip on auto position\n  target.className = target.className.replace(/bs-tooltip-auto/g, `bs-tooltip-${getBsVer().isBs5 ? PlacementForBs5[computedPlacement] : computedPlacement}`);\n  return computedPlacement + (variation ? `-${variation}` : '');\n}\nfunction getOffsets(data) {\n  return {\n    width: data.offsets.target.width,\n    height: data.offsets.target.height,\n    left: Math.floor(data.offsets.target.left ?? 0),\n    top: Math.round(data.offsets.target.top ?? 0),\n    bottom: Math.round(data.offsets.target.bottom ?? 0),\n    right: Math.floor(data.offsets.target.right ?? 0)\n  };\n}\n\n/**\n * Get the opposite placement of the given one\n */\nfunction getOppositePlacement(placement) {\n  const hash = {\n    left: 'right',\n    right: 'left',\n    bottom: 'top',\n    top: 'bottom'\n  };\n  return placement.replace(/left|right|bottom|top/g, matched => hash[matched]);\n}\n\n/**\n * Get the opposite placement variation of the given one\n */\nfunction getOppositeVariation(variation) {\n  if (variation === 'right') {\n    return 'left';\n  } else if (variation === 'left') {\n    return 'right';\n  }\n  return variation;\n}\nconst parse = (value, def = 0) => value ? parseFloat(value) : def;\nfunction getOuterSizes(element) {\n  const window = element.ownerDocument.defaultView;\n  const styles = window?.getComputedStyle(element);\n  const x = parse(styles?.marginTop) + parse(styles?.marginBottom);\n  const y = parse(styles?.marginLeft) + parse(styles?.marginRight);\n  return {\n    width: Number(element.offsetWidth) + y,\n    height: Number(element.offsetHeight) + x\n  };\n}\n\n/**\n * Get offsets to the reference element\n */\nfunction getReferenceOffsets(target, host, fixedPosition) {\n  const commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(target) : findCommonOffsetParent(target, host);\n  return getOffsetRectRelativeToArbitraryNode(host, commonOffsetParent, fixedPosition);\n}\n\n/**\n * Get offsets to the target\n */\nfunction getTargetOffsets(target, hostOffsets, position) {\n  const placement = position.split(' ')[0];\n  // Get target node sizes\n  const targetRect = getOuterSizes(target);\n  // Add position, width and height to our offsets object\n  const targetOffsets = {\n    width: targetRect.width,\n    height: targetRect.height\n  };\n  // depending by the target placement we have to compute its offsets slightly differently\n  const isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  const mainSide = isHoriz ? 'top' : 'left';\n  const secondarySide = isHoriz ? 'left' : 'top';\n  const measurement = isHoriz ? 'height' : 'width';\n  const secondaryMeasurement = !isHoriz ? 'height' : 'width';\n  targetOffsets[mainSide] = (hostOffsets[mainSide] ?? 0) + hostOffsets[measurement] / 2 - targetRect[measurement] / 2;\n  targetOffsets[secondarySide] = placement === secondarySide ? (hostOffsets[secondarySide] ?? 0) - targetRect[secondaryMeasurement] : hostOffsets[getOppositePlacement(secondarySide)] ?? 0;\n  return targetOffsets;\n}\nfunction isModifierEnabled(options, modifierName) {\n  return !!options.modifiers[modifierName]?.enabled;\n}\nconst availablePositions = {\n  top: ['top', 'top start', 'top end'],\n  bottom: ['bottom', 'bottom start', 'bottom end'],\n  start: ['start', 'start top', 'start bottom'],\n  end: ['end', 'end top', 'end bottom']\n};\nfunction checkPopoverMargin(placement, checkPosition) {\n  if (!getBsVer().isBs5) {\n    return false;\n  }\n  return availablePositions[checkPosition].includes(placement);\n}\nfunction checkMargins(placement) {\n  if (!getBsVer().isBs5) {\n    return '';\n  }\n  if (checkPopoverMargin(placement, 'end')) {\n    return 'ms-2';\n  }\n  if (checkPopoverMargin(placement, 'start')) {\n    return 'me-2';\n  }\n  if (checkPopoverMargin(placement, 'top')) {\n    return 'mb-2';\n  }\n  if (checkPopoverMargin(placement, 'bottom')) {\n    return 'mt-2';\n  }\n  return '';\n}\nfunction updateContainerClass(data, renderer) {\n  const target = data.instance.target;\n  let containerClass = target.className;\n  const dataPlacement = getBsVer().isBs5 ? PlacementForBs5[data.placement] : data.placement;\n  if (data.placementAuto) {\n    containerClass = containerClass.replace(/bs-popover-auto/g, `bs-popover-${dataPlacement}`);\n    containerClass = containerClass.replace(/ms-2|me-2|mb-2|mt-2/g, '');\n    containerClass = containerClass.replace(/bs-tooltip-auto/g, `bs-tooltip-${dataPlacement}`);\n    containerClass = containerClass.replace(/\\sauto/g, ` ${dataPlacement}`);\n    if (containerClass.indexOf('popover') !== -1) {\n      containerClass = containerClass + ' ' + checkMargins(dataPlacement);\n    }\n    if (containerClass.indexOf('popover') !== -1 && containerClass.indexOf('popover-auto') === -1) {\n      containerClass += ' popover-auto';\n    }\n    if (containerClass.indexOf('tooltip') !== -1 && containerClass.indexOf('tooltip-auto') === -1) {\n      containerClass += ' tooltip-auto';\n    }\n  }\n  containerClass = containerClass.replace(/left|right|top|bottom|end|start/g, `${dataPlacement.split(' ')[0]}`);\n  if (renderer) {\n    renderer.setAttribute(target, 'class', containerClass);\n    return;\n  }\n  target.className = containerClass;\n}\nfunction setStyles(element, styles, renderer) {\n  if (!element || !styles) {\n    return;\n  }\n  Object.keys(styles).forEach(prop => {\n    let unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && isNumeric(styles[prop])) {\n      unit = 'px';\n    }\n    if (renderer) {\n      renderer.setStyle(element, prop, `${String(styles[prop])}${unit}`);\n      return;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    element.style[prop] = String(styles[prop]) + unit;\n  });\n}\nfunction arrow(data) {\n  let targetOffsets = data.offsets.target;\n  // if arrowElement is a string, suppose it's a CSS selector\n  const arrowElement = data.instance.target.querySelector('.arrow');\n  // if arrowElement is not found, don't run the modifier\n  if (!arrowElement) {\n    return data;\n  }\n  const isVertical = ['left', 'right'].indexOf(data.placement.split(' ')[0]) !== -1;\n  const len = isVertical ? 'height' : 'width';\n  const sideCapitalized = isVertical ? 'Top' : 'Left';\n  const side = sideCapitalized.toLowerCase();\n  const altSide = isVertical ? 'left' : 'top';\n  const opSide = isVertical ? 'bottom' : 'right';\n  const arrowElementSize = getOuterSizes(arrowElement)[len];\n  const placementVariation = data.placement.split(' ')[1];\n  // top/left side\n  if ((data.offsets.host[opSide] ?? 0) - arrowElementSize < (targetOffsets[side] ?? 0)) {\n    targetOffsets[side] -= (targetOffsets[side] ?? 0) - ((data.offsets.host[opSide] ?? 0) - arrowElementSize);\n  }\n  // bottom/right side\n  if (Number(data.offsets.host[side]) + Number(arrowElementSize) > (targetOffsets[opSide] ?? 0)) {\n    targetOffsets[side] += Number(data.offsets.host[side]) + Number(arrowElementSize) - Number(targetOffsets[opSide]);\n  }\n  targetOffsets = getClientRect(targetOffsets);\n  // Compute the sideValue using the updated target offsets\n  // take target margin in account because we don't have this info available\n  const css = getStyleComputedProperty(data.instance.target);\n  const targetMarginSide = parseFloat(css[`margin${sideCapitalized}`]) || 0;\n  const targetBorderSide = parseFloat(css[`border${sideCapitalized}Width`]) || 0;\n  // compute center of the target\n  let center;\n  if (!placementVariation) {\n    center = Number(data.offsets.host[side]) + Number(data.offsets.host[len] / 2 - arrowElementSize / 2);\n  } else {\n    const targetBorderRadius = parseFloat(css[\"borderRadius\"]) || 0;\n    const targetSideArrowOffset = Number(targetMarginSide + targetBorderSide + targetBorderRadius);\n    center = side === placementVariation ? Number(data.offsets.host[side]) + targetSideArrowOffset : Number(data.offsets.host[side]) + Number(data.offsets.host[len] - targetSideArrowOffset);\n  }\n  let sideValue = center - (targetOffsets[side] ?? 0) - targetMarginSide - targetBorderSide;\n  // prevent arrowElement from being placed not contiguously to its target\n  sideValue = Math.max(Math.min(targetOffsets[len] - (arrowElementSize + 5), sideValue), 0);\n  data.offsets.arrow = {\n    [side]: Math.round(sideValue),\n    [altSide]: '' // make sure to unset any eventual altSide value from the DOM node\n  };\n  data.instance.arrow = arrowElement;\n  return data;\n}\nfunction flip(data) {\n  data.offsets.target = getClientRect(data.offsets.target);\n  if (!isModifierEnabled(data.options, 'flip')) {\n    data.offsets.target = {\n      ...data.offsets.target,\n      ...getTargetOffsets(data.instance.target, data.offsets.host, data.placement)\n    };\n    return data;\n  }\n  const boundaries = getBoundaries(data.instance.target, data.instance.host, 0,\n  // padding\n  'viewport', false // positionFixed\n  );\n  let placement = data.placement.split(' ')[0];\n  let variation = data.placement.split(' ')[1] || '';\n  const offsetsHost = data.offsets.host;\n  const target = data.instance.target;\n  const host = data.instance.host;\n  const adaptivePosition = computeAutoPlacement('auto', offsetsHost, target, host, data.options.allowedPositions);\n  const flipOrder = [placement, adaptivePosition];\n  flipOrder.forEach((step, index) => {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return;\n    }\n    placement = data.placement.split(' ')[0];\n    // using floor because the host offsets may contain decimals we are not going to consider here\n    const overlapsRef = placement === 'left' && Math.floor(data.offsets.target.right ?? 0) > Math.floor(data.offsets.host.left ?? 0) || placement === 'right' && Math.floor(data.offsets.target.left ?? 0) < Math.floor(data.offsets.host.right ?? 0) || placement === 'top' && Math.floor(data.offsets.target.bottom ?? 0) > Math.floor(data.offsets.host.top ?? 0) || placement === 'bottom' && Math.floor(data.offsets.target.top ?? 0) < Math.floor(data.offsets.host.bottom ?? 0);\n    const overflowsLeft = Math.floor(data.offsets.target.left ?? 0) < Math.floor(boundaries.left ?? 0);\n    const overflowsRight = Math.floor(data.offsets.target.right ?? 0) > Math.floor(boundaries.right ?? 0);\n    const overflowsTop = Math.floor(data.offsets.target.top ?? 0) < Math.floor(boundaries.top ?? 0);\n    const overflowsBottom = Math.floor(data.offsets.target.bottom ?? 0) > Math.floor(boundaries.bottom ?? 0);\n    const overflowsBoundaries = placement === 'left' && overflowsLeft || placement === 'right' && overflowsRight || placement === 'top' && overflowsTop || placement === 'bottom' && overflowsBottom;\n    // flip the variation if required\n    const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n    const flippedVariation = isVertical && variation === 'left' && overflowsLeft || isVertical && variation === 'right' && overflowsRight || !isVertical && variation === 'left' && overflowsTop || !isVertical && variation === 'right' && overflowsBottom;\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n      data.placement = placement + (variation ? ` ${variation}` : '');\n      data.offsets.target = {\n        ...data.offsets.target,\n        ...getTargetOffsets(data.instance.target, data.offsets.host, data.placement)\n      };\n    }\n  });\n  return data;\n}\nfunction initData(targetElement, hostElement, position, options) {\n  if (!targetElement || !hostElement) {\n    return;\n  }\n  const hostElPosition = getReferenceOffsets(targetElement, hostElement);\n  if (!position.match(/^(auto)*\\s*(left|right|top|bottom|start|end)*$/) && !position.match(/^(left|right|top|bottom|start|end)*(?: (left|right|top|bottom|start|end))*$/)) {\n    position = 'auto';\n  }\n  const placementAuto = !!position.match(/auto/g);\n  // support old placements 'auto left|right|top|bottom'\n  let placement = position.match(/auto\\s(left|right|top|bottom|start|end)/) ? position.split(' ')[1] || 'auto' : position;\n  // Normalize placements that have identical main placement and variation (\"right right\" => \"right\").\n  const matches = placement.match(/^(left|right|top|bottom|start|end)* ?(?!\\1)(left|right|top|bottom|start|end)?/);\n  if (matches) {\n    placement = matches[1] + (matches[2] ? ` ${matches[2]}` : '');\n  }\n  // \"left right\", \"top bottom\" etc. placements also considered incorrect.\n  if (['left right', 'right left', 'top bottom', 'bottom top'].indexOf(placement) !== -1) {\n    placement = 'auto';\n  }\n  placement = computeAutoPlacement(placement, hostElPosition, targetElement, hostElement, options ? options.allowedPositions : undefined);\n  const targetOffset = getTargetOffsets(targetElement, hostElPosition, placement);\n  return {\n    options: options || {\n      modifiers: {}\n    },\n    instance: {\n      target: targetElement,\n      host: hostElement,\n      arrow: void 0\n    },\n    offsets: {\n      target: targetOffset,\n      host: hostElPosition,\n      arrow: void 0\n    },\n    positionFixed: false,\n    placement,\n    placementAuto\n  };\n}\nfunction preventOverflow(data) {\n  if (!isModifierEnabled(data.options, 'preventOverflow')) {\n    return data;\n  }\n  // NOTE: DOM access here\n  // resets the target Offsets's position so that the document size can be calculated excluding\n  // the size of the targetOffsets element itself\n  const transformProp = 'transform';\n  const targetStyles = data.instance.target.style; // assignment to help minification\n  const {\n    top,\n    left,\n    [transformProp]: transform\n  } = targetStyles;\n  targetStyles.top = '';\n  targetStyles.left = '';\n  targetStyles[transformProp] = '';\n  const boundaries = getBoundaries(data.instance.target, data.instance.host, 0,\n  // padding\n  data.options.modifiers.preventOverflow?.boundariesElement || 'scrollParent', false // positionFixed\n  );\n  // NOTE: DOM access here\n  // restores the original style properties after the offsets have been computed\n  targetStyles.top = top;\n  targetStyles.left = left;\n  targetStyles[transformProp] = transform;\n  const order = ['left', 'right', 'top', 'bottom'];\n  const check = {\n    primary(placement) {\n      let value = data.offsets.target[placement];\n      // options.escapeWithReference\n      if ((data.offsets.target[placement] ?? 0) < (boundaries[placement] ?? 0)) {\n        value = Math.max(data.offsets.target[placement] ?? 0, boundaries[placement] ?? 0);\n      }\n      return {\n        [placement]: value\n      };\n    },\n    secondary(placement) {\n      const isPlacementHorizontal = placement === 'right';\n      const mainSide = isPlacementHorizontal ? 'left' : 'top';\n      const measurement = isPlacementHorizontal ? 'width' : 'height';\n      let value = data.offsets.target[mainSide];\n      // escapeWithReference\n      if ((data.offsets.target[placement] ?? 0) > (boundaries[placement] ?? 0)) {\n        value = Math.min(data.offsets.target[mainSide] ?? 0, (boundaries[placement] ?? 0) - data.offsets.target[measurement]);\n      }\n      return {\n        [mainSide]: value\n      };\n    }\n  };\n  order.forEach(placement => {\n    const side = ['left', 'top', 'start'].indexOf(placement) !== -1 ? check['primary'] : check['secondary'];\n    data.offsets.target = {\n      ...data.offsets.target,\n      ...side(placement)\n    };\n  });\n  return data;\n}\nfunction shift(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split(' ')[0];\n  const shiftVariation = placement.split(' ')[1];\n  if (shiftVariation) {\n    const {\n      host,\n      target\n    } = data.offsets;\n    const isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    const side = isVertical ? 'left' : 'top';\n    const measurement = isVertical ? 'width' : 'height';\n    const shiftOffsets = {\n      start: {\n        [side]: host[side]\n      },\n      end: {\n        [side]: (host[side] ?? 0) + host[measurement] - target[measurement]\n      }\n    };\n    data.offsets.target = {\n      ...target,\n      ...{\n        [side]: side === shiftVariation ? shiftOffsets.start[side] : shiftOffsets.end[side]\n      }\n    };\n  }\n  return data;\n}\nclass Positioning {\n  position(hostElement, targetElement /*, round = true*/) {\n    return this.offset(hostElement, targetElement /*, false*/);\n  }\n  offset(hostElement, targetElement /*, round = true*/) {\n    return getReferenceOffsets(targetElement, hostElement);\n  }\n  positionElements(hostElement, targetElement, position, appendToBody, options) {\n    const chainOfModifiers = [flip, shift, preventOverflow, arrow];\n    const _position = MapPlacementInToRL[position];\n    const data = initData(targetElement, hostElement, _position, options);\n    if (!data) {\n      return;\n    }\n    return chainOfModifiers.reduce((modifiedData, modifier) => modifier(modifiedData), data);\n  }\n}\nconst positionService = new Positioning();\nfunction positionElements(hostElement, targetElement, placement, appendToBody, options, renderer) {\n  const data = positionService.positionElements(hostElement, targetElement, placement, appendToBody, options);\n  if (!data) {\n    return;\n  }\n  const offsets = getOffsets(data);\n  setStyles(targetElement, {\n    'will-change': 'transform',\n    top: '0px',\n    left: '0px',\n    transform: `translate3d(${offsets.left}px, ${offsets.top}px, 0px)`\n  }, renderer);\n  if (data.instance.arrow) {\n    setStyles(data.instance.arrow, data.offsets.arrow, renderer);\n  }\n  updateContainerClass(data, renderer);\n}\nclass PositioningService {\n  constructor(ngZone, rendererFactory, platformId) {\n    this.update$$ = new Subject();\n    this.positionElements = new Map();\n    this.isDisabled = false;\n    if (isPlatformBrowser(platformId)) {\n      ngZone.runOutsideAngular(() => {\n        this.triggerEvent$ = merge(fromEvent(window, 'scroll', {\n          passive: true\n        }), fromEvent(window, 'resize', {\n          passive: true\n        }), of(0, animationFrameScheduler), this.update$$);\n        this.triggerEvent$.subscribe(() => {\n          if (this.isDisabled) {\n            return;\n          }\n          this.positionElements\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          .forEach(positionElement => {\n            positionElements(_getHtmlElement(positionElement.target), _getHtmlElement(positionElement.element), positionElement.attachment, positionElement.appendToBody, this.options, rendererFactory.createRenderer(null, null));\n          });\n        });\n      });\n    }\n  }\n  position(options) {\n    this.addPositionElement(options);\n  }\n  get event$() {\n    return this.triggerEvent$;\n  }\n  disable() {\n    this.isDisabled = true;\n  }\n  enable() {\n    this.isDisabled = false;\n  }\n  addPositionElement(options) {\n    this.positionElements.set(_getHtmlElement(options.element), options);\n  }\n  calcPosition() {\n    this.update$$.next(null);\n  }\n  deletePositionElement(elRef) {\n    this.positionElements.delete(_getHtmlElement(elRef));\n  }\n  setOptions(options) {\n    this.options = options;\n  }\n  static {\n    this.ɵfac = function PositioningService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PositioningService)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(PLATFORM_ID));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PositioningService,\n      factory: PositioningService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PositioningService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.RendererFactory2\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }], null);\n})();\nfunction _getHtmlElement(element) {\n  // it means that we got a selector\n  if (typeof element === 'string') {\n    return document.querySelector(element);\n  }\n  if (element instanceof ElementRef) {\n    return element.nativeElement;\n  }\n  return element ?? null;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { PlacementForBs5, Positioning, PositioningService, checkMargins, positionElements };\n", "import * as i0 from '@angular/core';\nimport { EventEmitter, Injector, ElementRef, TemplateRef, Injectable, Inject } from '@angular/core';\nimport { listenToTriggersV2, registerOutsideClick, registerEscClick } from 'ngx-bootstrap/utils';\nimport * as i1 from 'ngx-bootstrap/positioning';\nimport { DOCUMENT } from '@angular/common';\nclass BsComponentRef {}\n\n/**\n * @copyright Valor Software\n * @copyright Angular ng-bootstrap team\n */\nclass ContentRef {\n  constructor(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  nodes, viewRef,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  componentRef) {\n    this.nodes = nodes;\n    this.viewRef = viewRef;\n    this.componentRef = componentRef;\n  }\n}\n\n// todo: add delay support\n// todo: merge events onShow, onShown, etc...\n// todo: add global positioning configuration?\nclass ComponentLoader {\n  /**\n   * Do not use this directly, it should be instanced via\n   * `ComponentLoadFactory.attach`\n   * @internal\n   */\n  constructor(_viewContainerRef, _renderer, _elementRef, _injector, _componentFactoryResolver, _ngZone, _applicationRef, _posService, _document) {\n    this._viewContainerRef = _viewContainerRef;\n    this._renderer = _renderer;\n    this._elementRef = _elementRef;\n    this._injector = _injector;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._ngZone = _ngZone;\n    this._applicationRef = _applicationRef;\n    this._posService = _posService;\n    this._document = _document;\n    this.onBeforeShow = new EventEmitter();\n    this.onShown = new EventEmitter();\n    this.onBeforeHide = new EventEmitter();\n    this.onHidden = new EventEmitter();\n    this._providers = [];\n    this._isHiding = false;\n    /**\n     * A selector used if container element was not found\n     */\n    this.containerDefaultSelector = 'body';\n    this._listenOpts = {};\n    this._globalListener = Function.prototype;\n  }\n  get isShown() {\n    if (this._isHiding) {\n      return false;\n    }\n    return !!this._componentRef;\n  }\n  attach(compType) {\n    this._componentFactory = this._componentFactoryResolver.resolveComponentFactory(compType);\n    return this;\n  }\n  // todo: add behaviour: to target element, `body`, custom element\n  to(container) {\n    this.container = container || this.container;\n    return this;\n  }\n  position(opts) {\n    if (!opts) {\n      return this;\n    }\n    this.attachment = opts.attachment || this.attachment;\n    this._elementRef = opts.target || this._elementRef;\n    return this;\n  }\n  provide(provider) {\n    this._providers.push(provider);\n    return this;\n  }\n  // todo: appendChild to element or document.querySelector(this.container)\n  show(opts = {}) {\n    this._subscribePositioning();\n    this._innerComponent = void 0;\n    if (!this._componentRef) {\n      this.onBeforeShow.emit();\n      this._contentRef = this._getContentRef(opts.content, opts.context, opts.initialState);\n      const injector = Injector.create({\n        providers: this._providers,\n        parent: this._injector\n      });\n      if (!this._componentFactory) {\n        return;\n      }\n      this._componentRef = this._componentFactory.create(injector, this._contentRef.nodes);\n      this._applicationRef.attachView(this._componentRef.hostView);\n      // this._componentRef = this._viewContainerRef\n      //   .createComponent(this._componentFactory, 0, injector, this._contentRef.nodes);\n      this.instance = this._componentRef.instance;\n      Object.assign(this._componentRef.instance, opts);\n      if (this.container instanceof ElementRef) {\n        this.container.nativeElement.appendChild(this._componentRef.location.nativeElement);\n      }\n      if (typeof this.container === 'string' && typeof this._document !== 'undefined') {\n        const selectedElement = this._document.querySelector(this.container) || this._document.querySelector(this.containerDefaultSelector);\n        if (!selectedElement) {\n          return;\n        }\n        selectedElement.appendChild(this._componentRef.location.nativeElement);\n      }\n      if (!this.container && this._elementRef && this._elementRef.nativeElement.parentElement) {\n        this._elementRef.nativeElement.parentElement.appendChild(this._componentRef.location.nativeElement);\n      }\n      // we need to manually invoke change detection since events registered\n      // via\n      // Renderer::listen() are not picked up by change detection with the\n      // OnPush strategy\n      if (this._contentRef.componentRef) {\n        this._innerComponent = this._contentRef.componentRef.instance;\n        this._contentRef.componentRef.changeDetectorRef.markForCheck();\n        this._contentRef.componentRef.changeDetectorRef.detectChanges();\n      }\n      this._componentRef.changeDetectorRef.markForCheck();\n      this._componentRef.changeDetectorRef.detectChanges();\n      this.onShown.emit(opts.id ? {\n        id: opts.id\n      } : this._componentRef.instance);\n    }\n    this._registerOutsideClick();\n    return this._componentRef;\n  }\n  hide(id) {\n    if (!this._componentRef) {\n      return this;\n    }\n    this._posService.deletePositionElement(this._componentRef.location);\n    this.onBeforeHide.emit(this._componentRef.instance);\n    const componentEl = this._componentRef.location.nativeElement;\n    componentEl.parentNode?.removeChild(componentEl);\n    this._contentRef?.componentRef?.destroy();\n    if (this._viewContainerRef && this._contentRef?.viewRef) {\n      this._viewContainerRef.remove(this._viewContainerRef.indexOf(this._contentRef.viewRef));\n    }\n    this._contentRef?.viewRef?.destroy();\n    this._componentRef?.destroy();\n    this._contentRef = void 0;\n    this._componentRef = void 0;\n    this._removeGlobalListener();\n    this.onHidden.emit(id ? {\n      id\n    } : null);\n    return this;\n  }\n  toggle() {\n    if (this.isShown) {\n      this.hide();\n      return;\n    }\n    this.show();\n  }\n  dispose() {\n    if (this.isShown) {\n      this.hide();\n    }\n    this._unsubscribePositioning();\n    if (this._unregisterListenersFn) {\n      this._unregisterListenersFn();\n    }\n  }\n  listen(listenOpts) {\n    this.triggers = listenOpts.triggers || this.triggers;\n    this._listenOpts.outsideClick = listenOpts.outsideClick;\n    this._listenOpts.outsideEsc = listenOpts.outsideEsc;\n    listenOpts.target = listenOpts.target || this._elementRef?.nativeElement;\n    const hide = this._listenOpts.hide = () => listenOpts.hide ? listenOpts.hide() : void this.hide();\n    const show = this._listenOpts.show = registerHide => {\n      listenOpts.show ? listenOpts.show(registerHide) : this.show(registerHide);\n      registerHide();\n    };\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const toggle = registerHide => {\n      this.isShown ? hide() : show(registerHide);\n    };\n    if (this._renderer) {\n      this._unregisterListenersFn = listenToTriggersV2(this._renderer, {\n        target: listenOpts.target,\n        triggers: listenOpts.triggers,\n        show,\n        hide,\n        toggle\n      });\n    }\n    return this;\n  }\n  _removeGlobalListener() {\n    if (this._globalListener) {\n      this._globalListener();\n      this._globalListener = Function.prototype;\n    }\n  }\n  attachInline(vRef,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  template) {\n    if (vRef && template) {\n      this._inlineViewRef = vRef.createEmbeddedView(template);\n    }\n    return this;\n  }\n  _registerOutsideClick() {\n    if (!this._componentRef || !this._componentRef.location) {\n      return;\n    }\n    let unsubscribeOutsideClick = Function.prototype;\n    let unsubscribeEscClick = Function.prototype;\n    // why: should run after first event bubble\n    if (this._listenOpts.outsideClick) {\n      const target = this._componentRef.location.nativeElement;\n      setTimeout(() => {\n        if (this._renderer && this._elementRef) {\n          unsubscribeOutsideClick = registerOutsideClick(this._renderer, {\n            targets: [target, this._elementRef.nativeElement],\n            outsideClick: this._listenOpts.outsideClick,\n            hide: () => this._listenOpts.hide && this._listenOpts.hide()\n          });\n        }\n      });\n    }\n    if (this._listenOpts.outsideEsc && this._renderer && this._elementRef) {\n      const target = this._componentRef.location.nativeElement;\n      unsubscribeEscClick = registerEscClick(this._renderer, {\n        targets: [target, this._elementRef.nativeElement],\n        outsideEsc: this._listenOpts.outsideEsc,\n        hide: () => this._listenOpts.hide && this._listenOpts.hide()\n      });\n    }\n    this._globalListener = () => {\n      unsubscribeOutsideClick();\n      unsubscribeEscClick();\n    };\n  }\n  getInnerComponent() {\n    return this._innerComponent;\n  }\n  _subscribePositioning() {\n    if (this._zoneSubscription || !this.attachment) {\n      return;\n    }\n    this.onShown.subscribe(() => {\n      this._posService.position({\n        element: this._componentRef?.location,\n        target: this._elementRef,\n        attachment: this.attachment,\n        appendToBody: this.container === 'body'\n      });\n    });\n    this._zoneSubscription = this._ngZone.onStable.subscribe(() => {\n      if (!this._componentRef) {\n        return;\n      }\n      this._posService.calcPosition();\n    });\n  }\n  _unsubscribePositioning() {\n    if (!this._zoneSubscription) {\n      return;\n    }\n    this._zoneSubscription.unsubscribe();\n    this._zoneSubscription = void 0;\n  }\n  _getContentRef(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  content,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  context,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  initialState) {\n    if (!content) {\n      return new ContentRef([]);\n    }\n    if (content instanceof TemplateRef) {\n      if (this._viewContainerRef) {\n        const _viewRef = this._viewContainerRef.createEmbeddedView(content, context);\n        _viewRef.markForCheck();\n        return new ContentRef([_viewRef.rootNodes], _viewRef);\n      }\n      const viewRef = content.createEmbeddedView({});\n      this._applicationRef.attachView(viewRef);\n      return new ContentRef([viewRef.rootNodes], viewRef);\n    }\n    if (typeof content === 'function') {\n      const contentCmptFactory = this._componentFactoryResolver.resolveComponentFactory(content);\n      const modalContentInjector = Injector.create({\n        providers: this._providers,\n        parent: this._injector\n      });\n      const componentRef = contentCmptFactory.create(modalContentInjector);\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      Object.assign(componentRef.instance, initialState);\n      this._applicationRef.attachView(componentRef.hostView);\n      return new ContentRef([[componentRef.location.nativeElement]], componentRef.hostView, componentRef);\n    }\n    const nodes = this._renderer ? [this._renderer.createText(`${content}`)] : [];\n    return new ContentRef([nodes]);\n  }\n}\nclass ComponentLoaderFactory {\n  constructor(_componentFactoryResolver, _ngZone, _injector, _posService, _applicationRef, _document) {\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._ngZone = _ngZone;\n    this._injector = _injector;\n    this._posService = _posService;\n    this._applicationRef = _applicationRef;\n    this._document = _document;\n  }\n  /**\n   *\n   * @param _elementRef\n   * @param _viewContainerRef\n   * @param _renderer\n   */\n  createLoader(_elementRef, _viewContainerRef, _renderer) {\n    return new ComponentLoader(_viewContainerRef, _renderer, _elementRef, this._injector, this._componentFactoryResolver, this._ngZone, this._applicationRef, this._posService, this._document);\n  }\n  static {\n    this.ɵfac = function ComponentLoaderFactory_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ComponentLoaderFactory)(i0.ɵɵinject(i0.ComponentFactoryResolver), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i1.PositioningService), i0.ɵɵinject(i0.ApplicationRef), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ComponentLoaderFactory,\n      factory: ComponentLoaderFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ComponentLoaderFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.ComponentFactoryResolver\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i0.Injector\n  }, {\n    type: i1.PositioningService\n  }, {\n    type: i0.ApplicationRef\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BsComponentRef, ComponentLoader, ComponentLoaderFactory, ContentRef };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAMA,IAAM,UAAN,MAAc;AAAA,EACV,YAAY,MAAM,OAAO;AACrB,SAAK,OAAO;AACZ,SAAK,QAAQ,SAAS;AAAA,EAC1B;AAAA,EACA,WAAW;AACP,WAAO,KAAK,SAAS,YAAY,KAAK,UAAU;AAAA,EACpD;AACJ;AAEA,IAAM,kBAAkB;AAAA,EACpB,OAAO,CAAC,aAAa,UAAU;AAAA,EAC/B,OAAO,CAAC,WAAW,UAAU;AACjC;AAEA,SAAS,cAAc,UAAU,UAAU,iBAAiB;AACxD,QAAM,mBAAmB,YAAY,IAAI,KAAK;AAC9C,MAAI,gBAAgB,WAAW,GAAG;AAC9B,WAAO,CAAC;AAAA,EACZ;AACA,QAAM,iBAAiB,gBAClB,MAAM,KAAK,EACX,IAAI,CAAC,YAAY,QAAQ,MAAM,GAAG,CAAC,EACnC,IAAI,CAAC,gBAAgB;AACtB,UAAM,QAAQ,QAAQ,YAAY,CAAC,CAAC,KAAK;AACzC,WAAO,IAAI,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,EACzC,CAAC;AACD,QAAM,iBAAiB,eAAe,OAAO,CAAC,gBAAgB,YAAY,SAAS,CAAC;AACpF,MAAI,eAAe,SAAS,GAAG;AAC3B,UAAM,IAAI,MAAM,0DAA0D;AAAA,EAC9E;AACA,MAAI,eAAe,WAAW,KAAK,eAAe,SAAS,GAAG;AAC1D,UAAM,IAAI,MAAM,yEAA0E;AAAA,EAC9F;AACA,SAAO;AACX;AAuBA,SAAS,mBAAmB,UAAU,SAAS;AAC3C,QAAM,iBAAiB,cAAc,QAAQ,QAAQ;AACrD,QAAM,SAAS,QAAQ;AAEvB,MAAI,eAAe,WAAW,KAAK,eAAe,CAAC,EAAE,SAAS,GAAG;AAC7D,WAAO,SAAS;AAAA,EACpB;AAEA,QAAM,YAAY,CAAC;AAEnB,QAAM,gBAAgB,CAAC;AACvB,QAAM,eAAe,MAAM;AAEvB,kBAAc,QAAQ,CAAC,OAAO,UAAU,KAAK,GAAG,CAAC,CAAC;AAElD,kBAAc,SAAS;AAAA,EAC3B;AAEA,iBAAe,QAAQ,CAAC,YAAY;AAChC,UAAM,YAAY,QAAQ,SAAS,QAAQ;AAC3C,UAAM,SAAS,YAAY,QAAQ,SAAS,QAAQ;AACpD,QAAI,CAAC,aAAa,QAAQ,SAAS,QAAQ,MAAM;AAC7C,YAAM,eAAe,QAAQ;AAC7B,YAAM,cAAc,QAAQ;AAC5B,YAAM,QAAQ,MAAM,SAAS,OAAO,QAAQ,cAAc,WAAW;AACrE,oBAAc,KAAK,KAAK;AAAA,IAC5B;AACA,QAAI,QAAQ;AACR,gBAAU,KAAK,SAAS,OAAO,QAAQ,QAAQ,MAAM,MAAM,OAAO,YAAY,CAAC,CAAC;AAAA,IACpF;AAAA,EACJ,CAAC;AACD,SAAO,MAAM;AACT,cAAU,QAAQ,CAAC,kBAAkB,cAAc,CAAC;AAAA,EACxD;AACJ;AACA,SAAS,qBAAqB,UAAU,SAAS;AAC7C,MAAI,CAAC,QAAQ,cAAc;AACvB,WAAO,SAAS;AAAA,EACpB;AAEA,SAAO,SAAS,OAAO,YAAY,SAAS,CAAC,UAAU;AACnD,QAAI,QAAQ,UAAU,QAAQ,OAAO,SAAS,MAAM,MAAM,GAAG;AACzD;AAAA,IACJ;AACA,QAAI,QAAQ,WACR,QAAQ,QAAQ,KAAK,YAAU,OAAO,SAAS,MAAM,MAAM,CAAC,GAAG;AAC/D;AAAA,IACJ;AACA,QAAI,QAAQ,MAAM;AACd,cAAQ,KAAK;AAAA,IACjB;AAAA,EACJ,CAAC;AACL;AACA,SAAS,iBAAiB,UAAU,SAAS;AACzC,MAAI,CAAC,QAAQ,YAAY;AACrB,WAAO,SAAS;AAAA,EACpB;AACA,SAAO,SAAS,OAAO,YAAY,aAAa,CAAC,UAAU;AACvD,QAAI,QAAQ,UAAU,QAAQ,OAAO,SAAS,MAAM,MAAM,GAAG;AACzD;AAAA,IACJ;AACA,QAAI,QAAQ,WACR,QAAQ,QAAQ,KAAK,YAAU,OAAO,SAAS,MAAM,MAAM,CAAC,GAAG;AAC/D;AAAA,IACJ;AACA,QAAI,QAAQ,MAAM;AACd,cAAQ,KAAK;AAAA,IACjB;AAAA,EACJ,CAAC;AACL;AAaA,IAAM,MAAO,OAAO,WAAW,eAAe,UAAW,CAAC;AAC1D,IAAMA,YAAW,IAAI;AACrB,IAAM,WAAW,IAAI;AAErB,IAAM,KAAK,IAAI,KAAK,MAAM,IAAI,GAAG,IAAI,MAAM;AAC3C,IAAM,cAAc,IAAI,cAAc,IAAI,cAAc;AACxD,IAAM,QAAQ,IAAI;AAClB,IAAM,aAAa,IAAI;AACvB,IAAM,gBAAgB,IAAI;AAC1B,IAAM,cAAc,IAAI;AACxB,IAAM,UAAU,IAAI;AACpB,IAAM,WAAW,IAAI;AACrB,IAAM,gBAAgB,IAAI;AAE1B,IAAI;AAAA,CACH,SAAUC,YAAW;AAClB,EAAAA,WAAU,OAAO,IAAI;AACrB,EAAAA,WAAU,OAAO,IAAI;AACzB,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,IAAI;AACJ,SAAS,kBAAkB;AACvB,QAAM,SAAS,IAAI,SAAS,cAAc,MAAM;AAChD,SAAO,YAAY;AACnB,SAAO,UAAU,IAAI,QAAQ;AAC7B,SAAO,UAAU,IAAI,MAAM;AAC3B,MAAI,SAAS,KAAK,YAAY,MAAM;AACpC,QAAM,eAAe,IAAI,iBAAiB,MAAM,EAAE;AAClD,MAAI,gBAAgB,WAAW,YAAY,GAAG;AAC1C,QAAI,SAAS,KAAK,YAAY,MAAM;AACpC,WAAO;AAAA,EACX;AACA,MAAI,SAAS,KAAK,YAAY,MAAM;AACpC,SAAO;AACX;AAIA,SAAS,QAAQ;AACb,MAAI;AACA,WAAO,mBAAmB;AAC9B,mBAAiB,gBAAgB;AACjC,SAAO,mBAAmB;AAC9B;AACA,SAAS,QAAQ;AACb,MAAI;AACA,WAAO,mBAAmB;AAC9B,mBAAiB,gBAAgB;AACjC,SAAO,mBAAmB;AAC9B;AACA,SAAS,WAAW;AAChB,SAAO;AAAA,IACH,OAAO,MAAM;AAAA,IACb,OAAO,MAAM;AAAA,EACjB;AACJ;AA+PA,SAAS,WAAW;AAChB,QAAM,QAAQ;AAEd,SAAO,SAAS,gBAAgB,QAAQ,aAAa;AACjD,UAAM,OAAO,MAAM,WAAW;AAC9B,WAAO,eAAe,QAAQ,aAAa;AAAA;AAAA,MAEvC,MAAM;AACF,eAAO,KAAK,IAAI;AAAA,MACpB;AAAA;AAAA,MAEA,IAAI,OAAO;AACP,cAAM,YAAY,KAAK,IAAI;AAC3B,aAAK,IAAI,IAAI;AACb,YAAI,cAAc,SAAS,KAAK,cAAc,KAAK,GAAG;AAClD,eAAK,cAAc,KAAK,EAAE,KAAK,KAAK;AAAA,QACxC;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AA8BA,IAAM,gBAAgB,CAAC;AACvB,IAAM,WAAW,OAAO,YAAY,eAAe,EAAE,UAAU;AAC/D,SAAS,SAAS,KAAK;AACnB,MAAI,CAAC,UAAU,KAAK,YAAY,OAAO,eAAe;AAClD;AAAA,EACJ;AACA,gBAAc,GAAG,IAAI;AACrB,UAAQ,KAAK,GAAG;AACpB;;;AChgBA;AACA;AACA;AACA;AACA,IAAI;AAAA,CACH,SAAUC,qBAAoB;AAC7B,EAAAA,oBAAmB,KAAK,IAAI;AAC5B,EAAAA,oBAAmB,QAAQ,IAAI;AAC/B,EAAAA,oBAAmB,MAAM,IAAI;AAC7B,EAAAA,oBAAmB,OAAO,IAAI;AAC9B,EAAAA,oBAAmB,MAAM,IAAI;AAC7B,EAAAA,oBAAmB,KAAK,IAAI;AAC5B,EAAAA,oBAAmB,OAAO,IAAI;AAC9B,EAAAA,oBAAmB,UAAU,IAAI;AACjC,EAAAA,oBAAmB,WAAW,IAAI;AAClC,EAAAA,oBAAmB,WAAW,IAAI;AAClC,EAAAA,oBAAmB,cAAc,IAAI;AACrC,EAAAA,oBAAmB,cAAc,IAAI;AACrC,EAAAA,oBAAmB,aAAa,IAAI;AACpC,EAAAA,oBAAmB,aAAa,IAAI;AACpC,EAAAA,oBAAmB,UAAU,IAAI;AACjC,EAAAA,oBAAmB,WAAW,IAAI;AAClC,EAAAA,oBAAmB,SAAS,IAAI;AAChC,EAAAA,oBAAmB,SAAS,IAAI;AAChC,EAAAA,oBAAmB,YAAY,IAAI;AACnC,EAAAA,oBAAmB,YAAY,IAAI;AACnC,EAAAA,oBAAmB,cAAc,IAAI;AACrC,EAAAA,oBAAmB,cAAc,IAAI;AACrC,EAAAA,oBAAmB,WAAW,IAAI;AACpC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAClD,IAAI;AAAA,CACH,SAAUC,kBAAiB;AAC1B,EAAAA,iBAAgB,KAAK,IAAI;AACzB,EAAAA,iBAAgB,QAAQ,IAAI;AAC5B,EAAAA,iBAAgB,MAAM,IAAI;AAC1B,EAAAA,iBAAgB,OAAO,IAAI;AAC3B,EAAAA,iBAAgB,MAAM,IAAI;AAC1B,EAAAA,iBAAgB,KAAK,IAAI;AACzB,EAAAA,iBAAgB,OAAO,IAAI;AAC3B,EAAAA,iBAAgB,UAAU,IAAI;AAC9B,EAAAA,iBAAgB,WAAW,IAAI;AAC/B,EAAAA,iBAAgB,WAAW,IAAI;AAC/B,EAAAA,iBAAgB,cAAc,IAAI;AAClC,EAAAA,iBAAgB,cAAc,IAAI;AAClC,EAAAA,iBAAgB,aAAa,IAAI;AACjC,EAAAA,iBAAgB,aAAa,IAAI;AACjC,EAAAA,iBAAgB,UAAU,IAAI;AAC9B,EAAAA,iBAAgB,WAAW,IAAI;AAC/B,EAAAA,iBAAgB,SAAS,IAAI;AAC7B,EAAAA,iBAAgB,SAAS,IAAI;AAC7B,EAAAA,iBAAgB,YAAY,IAAI;AAChC,EAAAA,iBAAgB,YAAY,IAAI;AAChC,EAAAA,iBAAgB,cAAc,IAAI;AAClC,EAAAA,iBAAgB,cAAc,IAAI;AAClC,EAAAA,iBAAgB,WAAW,IAAI;AACjC,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,SAAS,yBAAyB,SAAS,UAAU;AACnD,MAAI,QAAQ,aAAa,GAAG;AAC1B,WAAO,CAAC;AAAA,EACV;AAEA,QAAMC,UAAS,QAAQ,cAAc;AACrC,QAAM,MAAMA,SAAQ,iBAAiB,SAAS,IAAI;AAGlD,SAAO,WAAW,OAAO,IAAI,QAAQ,IAAI;AAC3C;AAKA,SAAS,gBAAgB,SAAS;AAChC,MAAI,CAAC,SAAS;AACZ,WAAO,SAAS;AAAA,EAClB;AACA,QAAM,iBAAiB;AAEvB,MAAI,eAAe,SAAS;AAE5B,MAAI,UAAU;AACd,SAAO,iBAAiB,kBAAkB,QAAQ,sBAAsB,YAAY,QAAQ,oBAAoB;AAE9G,cAAU,QAAQ;AAClB,mBAAe,QAAQ;AAAA,EACzB;AACA,QAAM,WAAW,gBAAgB,aAAa;AAC9C,MAAI,CAAC,YAAY,aAAa,UAAU,aAAa,QAAQ;AAC3D,WAAO,UAAU,QAAQ,cAAc,kBAAkB,SAAS;AAAA,EACpE;AAEA,MAAI,gBAAgB,CAAC,MAAM,MAAM,OAAO,EAAE,QAAQ,aAAa,QAAQ,MAAM,MAAM,yBAAyB,cAAc,UAAU,MAAM,UAAU;AAClJ,WAAO,gBAAgB,YAAY;AAAA,EACrC;AACA,SAAO;AACT;AAIA,SAAS,kBAAkB,SAAS;AAClC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,MAAI,aAAa,QAAQ;AACvB,WAAO;AAAA,EACT;AACA,SAAO,aAAa,UAAU,gBAAgB,QAAQ,iBAAiB,MAAM;AAC/E;AAKA,SAAS,QAAQ,MAAM;AACrB,MAAI,KAAK,eAAe,MAAM;AAC5B,WAAO,QAAQ,KAAK,UAAU;AAAA,EAChC;AACA,SAAO;AACT;AAKA,SAAS,uBAAuB,UAAU,UAAU;AAElD,MAAI,CAAC,YAAY,CAAC,SAAS,YAAY,CAAC,YAAY,CAAC,SAAS,UAAU;AACtE,WAAO,SAAS;AAAA,EAClB;AAEA,QAAM,QAAQ,SAAS,wBAAwB,QAAQ,IAAI,KAAK;AAChE,QAAM,QAAQ,QAAQ,WAAW;AACjC,QAAM,MAAM,QAAQ,WAAW;AAE/B,QAAM,QAAQ,SAAS,YAAY;AACnC,QAAM,SAAS,OAAO,CAAC;AACvB,QAAM,OAAO,KAAK,CAAC;AAEnB,QAAM,0BAA0B,MAAM;AAEtC,MAAI,aAAa,2BAA2B,aAAa,2BAA2B,MAAM,SAAS,GAAG,GAAG;AACvG,QAAI,kBAAkB,uBAAuB,GAAG;AAC9C,aAAO;AAAA,IACT;AACA,WAAO,gBAAgB,uBAAuB;AAAA,EAChD;AAEA,QAAM,eAAe,QAAQ,QAAQ;AACrC,MAAI,aAAa,MAAM;AACrB,WAAO,uBAAuB,aAAa,MAAM,QAAQ;AAAA,EAC3D,OAAO;AACL,WAAO,uBAAuB,UAAU,QAAQ,QAAQ,EAAE,IAAI;AAAA,EAChE;AACF;AAKA,SAAS,6BAA6B,SAAS;AAE7C,MAAI,CAAC,WAAW,CAAC,QAAQ,eAAe;AACtC,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,KAAK,QAAQ;AACjB,SAAO,IAAI,iBAAiB,yBAAyB,IAAI,WAAW,MAAM,QAAQ;AAChF,SAAK,GAAG;AAAA,EACV;AACA,SAAO,MAAM,SAAS;AACxB;AAKA,SAAS,eAAe,QAAQ,MAAM;AACpC,QAAM,QAAQ,SAAS,MAAM,SAAS;AACtC,QAAM,QAAQ,UAAU,SAAS,UAAU;AAC3C,SAAO,WAAW,OAAO,SAAS,KAAK,OAAO,CAAC,IAAI,WAAW,OAAO,SAAS,KAAK,OAAO,CAAC;AAC7F;AACA,SAAS,QAAQ,MAAM,MAAM,MAAM;AACjC,QAAM,QAAQ;AACd,QAAM,QAAQ;AACd,SAAO,KAAK,IAAI,MAAM,SAAS,IAAI,EAAE,GAAG,MAAM,SAAS,IAAI,EAAE,GAAG,MAAM,SAAS,IAAI,EAAE,GAAG,MAAM,SAAS,IAAI,EAAE,GAAG,MAAM,SAAS,IAAI,EAAE,GAAG,CAAC;AAC3I;AACA,SAAS,eAAeC,WAAU;AAChC,QAAM,OAAOA,UAAS;AACtB,QAAM,OAAOA,UAAS;AACtB,SAAO;AAAA,IACL,QAAQ,QAAQ,UAAU,MAAM,IAAI;AAAA,IACpC,OAAO,QAAQ,SAAS,MAAM,IAAI;AAAA,EACpC;AACF;AACA,SAAS,cAAc,SAAS;AAC9B,SAAO,iCACF,UADE;AAAA,IAEL,QAAQ,QAAQ,QAAQ,KAAK,QAAQ;AAAA,IACrC,SAAS,QAAQ,OAAO,KAAK,QAAQ;AAAA,EACvC;AACF;AAKA,SAAS,UAAU,GAAG;AACpB,SAAO,MAAM,MAAM,CAAC,MAAM,WAAW,CAAC,CAAC,KAAK,SAAS,OAAO,CAAC,CAAC;AAChE;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU,YAAY,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAChF;AAKA,SAAS,sBAAsB,SAAS;AACtC,QAAM,OAAO,QAAQ,sBAAsB;AAkB3C,MAAI,EAAE,QAAQ,SAAS,KAAK,GAAG,KAAK,SAAS,KAAK,IAAI,KAAK,SAAS,KAAK,MAAM,KAAK,SAAS,KAAK,KAAK,IAAI;AACzG,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AAAA,IACb,MAAM,KAAK;AAAA,IACX,KAAK,KAAK;AAAA,IACV,OAAO,KAAK,QAAQ,KAAK;AAAA,IACzB,QAAQ,KAAK,SAAS,KAAK;AAAA,EAC7B;AAEA,QAAM,QAAQ,QAAQ,aAAa,SAAS,eAAe,QAAQ,aAAa,IAAI;AACpF,QAAM,QAAQ,OAAO,SAAS,QAAQ,eAAe,SAAS,KAAK,KAAK,KAAK,SAAS,OAAO,IAAI,KAAK,KAAK,QAAQ,OAAO,QAAQ;AAClI,QAAM,SAAS,OAAO,UAAU,QAAQ,gBAAgB,SAAS,KAAK,MAAM,KAAK,SAAS,OAAO,GAAG,KAAK,KAAK,SAAS,OAAO,OAAO;AACrI,MAAI,iBAAiB,QAAQ,cAAc;AAC3C,MAAI,gBAAgB,QAAQ,eAAe;AAG3C,MAAI,kBAAkB,eAAe;AACnC,UAAM,SAAS,yBAAyB,OAAO;AAC/C,sBAAkB,eAAe,QAAQ,GAAG;AAC5C,qBAAiB,eAAe,QAAQ,GAAG;AAC3C,WAAO,SAAS;AAChB,WAAO,UAAU;AAAA,EACnB;AACA,SAAO,cAAc,MAAM;AAC7B;AACA,SAAS,qCAAqC,UAAU,QAAQ,gBAAgB,OAAO;AACrF,QAAM,SAAS,OAAO,aAAa;AACnC,QAAM,eAAe,sBAAsB,QAAQ;AACnD,QAAM,aAAa,sBAAsB,MAAM;AAC/C,QAAM,SAAS,yBAAyB,MAAM;AAC9C,QAAM,iBAAiB,WAAW,OAAO,cAAc;AACvD,QAAM,kBAAkB,WAAW,OAAO,eAAe;AAEzD,MAAI,iBAAiB,QAAQ;AAC3B,eAAW,MAAM,KAAK,IAAI,WAAW,OAAO,GAAG,CAAC;AAChD,eAAW,OAAO,KAAK,IAAI,WAAW,QAAQ,GAAG,CAAC;AAAA,EACpD;AACA,QAAM,UAAU,cAAc;AAAA,IAC5B,MAAM,aAAa,OAAO,MAAM,WAAW,OAAO,KAAK;AAAA,IACvD,OAAO,aAAa,QAAQ,MAAM,WAAW,QAAQ,KAAK;AAAA,IAC1D,OAAO,aAAa;AAAA,IACpB,QAAQ,aAAa;AAAA,EACvB,CAAC;AACD,UAAQ,YAAY;AACpB,UAAQ,aAAa;AAKrB,MAAI,QAAQ;AACV,UAAM,YAAY,WAAW,OAAO,SAAS;AAC7C,UAAM,aAAa,WAAW,OAAO,UAAU;AAC/C,QAAI,SAAS,QAAQ,GAAG,GAAG;AACzB,cAAQ,OAAO,iBAAiB;AAAA,IAClC;AACA,QAAI,SAAS,QAAQ,MAAM,GAAG;AAC5B,cAAQ,UAAU,iBAAiB;AAAA,IACrC;AACA,QAAI,SAAS,QAAQ,IAAI,GAAG;AAC1B,cAAQ,QAAQ,kBAAkB;AAAA,IACpC;AACA,QAAI,SAAS,QAAQ,KAAK,GAAG;AAC3B,cAAQ,SAAS,kBAAkB;AAAA,IACrC;AAEA,YAAQ,YAAY;AACpB,YAAQ,aAAa;AAAA,EACvB;AACA,SAAO;AACT;AAOA,SAAS,cAAc,SAAS;AAC9B,MAAI,QAAQ,aAAa,QAAQ;AAC/B,WAAO;AAAA,EACT;AACA,SAAO,QAAQ,cAAc,QAAQ;AACvC;AAOA,SAAS,gBAAgB,SAAS;AAEhC,MAAI,CAAC,SAAS;AACZ,WAAO,SAAS;AAAA,EAClB;AACA,UAAQ,QAAQ,UAAU;AAAA,IACxB,KAAK;AAAA,IACL,KAAK;AACH,aAAO,QAAQ,cAAc;AAAA,IAC/B,KAAK;AACH,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AAEA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,yBAAyB,OAAO;AACpC,MAAI,wBAAwB,KAAK,OAAO,QAAQ,IAAI,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,GAAG;AAC1F,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,cAAc,OAAO,CAAC;AAC/C;AAKA,SAAS,UAAU,SAAS,OAAO,OAAO;AACxC,QAAM,YAAY,SAAS,QAAQ,cAAc;AACjD,QAAM,WAAW,QAAQ;AACzB,MAAI,aAAa,UAAU,aAAa,QAAQ;AAC9C,UAAM,OAAO,QAAQ,cAAc;AACnC,UAAM,mBAAmB,QAAQ,cAAc,oBAAoB;AACnE,WAAO,iBAAiB,SAAS;AAAA,EACnC;AACA,SAAO,QAAQ,SAAS;AAC1B;AACA,SAAS,8CAA8C,SAAS,gBAAgB,OAAO;AACrF,QAAM,OAAO,QAAQ,cAAc;AACnC,QAAM,iBAAiB,qCAAqC,SAAS,IAAI;AACzE,QAAM,QAAQ,KAAK,IAAI,KAAK,aAAa,OAAO,cAAc,CAAC;AAC/D,QAAM,SAAS,KAAK,IAAI,KAAK,cAAc,OAAO,eAAe,CAAC;AAClE,QAAM,YAAY,CAAC,gBAAgB,UAAU,IAAI,IAAI;AACrD,QAAM,aAAa,CAAC,gBAAgB,UAAU,MAAM,MAAM,IAAI;AAC9D,QAAM,SAAS;AAAA,IACb,KAAK,YAAY,OAAO,gBAAgB,GAAG,IAAI,OAAO,gBAAgB,SAAS;AAAA,IAC/E,MAAM,aAAa,OAAO,gBAAgB,IAAI,IAAI,OAAO,gBAAgB,UAAU;AAAA,IACnF;AAAA,IACA;AAAA,EACF;AACA,SAAO,cAAc,MAAM;AAC7B;AAKA,SAAS,QAAQ,SAAS;AACxB,QAAM,WAAW,QAAQ;AACzB,MAAI,aAAa,UAAU,aAAa,QAAQ;AAC9C,WAAO;AAAA,EACT;AACA,MAAI,yBAAyB,SAAS,UAAU,MAAM,SAAS;AAC7D,WAAO;AAAA,EACT;AACA,SAAO,QAAQ,cAAc,OAAO,CAAC;AACvC;AACA,SAAS,cAAc,QAAQ,MAAM,UAAU,GAAG,mBAAmB,gBAAgB,OAAO;AAE1F,MAAI,aAAa;AAAA,IACf,KAAK;AAAA,IACL,MAAM;AAAA,EACR;AACA,QAAM,eAAe,gBAAgB,6BAA6B,MAAM,IAAI,uBAAuB,QAAQ,IAAI;AAE/G,MAAI,sBAAsB,YAAY;AACpC,iBAAa,8CAA8C,cAAc,aAAa;AAAA,EACxF,OAAO;AAEL,QAAI;AACJ,QAAI,sBAAsB,gBAAgB;AACxC,uBAAiB,gBAAgB,cAAc,IAAI,CAAC;AACpD,UAAI,eAAe,aAAa,QAAQ;AACtC,yBAAiB,OAAO,cAAc;AAAA,MACxC;AAAA,IACF,WAAW,sBAAsB,UAAU;AACzC,uBAAiB,OAAO,cAAc;AAAA,IACxC,OAAO;AACL,uBAAiB;AAAA,IACnB;AACA,UAAM,UAAU,qCAAqC,gBAAgB,cAAc,aAAa;AAEhG,QAAI,WAAW,eAAe,aAAa,UAAU,CAAC,QAAQ,YAAY,GAAG;AAC3E,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,eAAe,OAAO,aAAa;AACvC,UAAI,SAAS,WAAW,GAAG,KAAK,SAAS,QAAQ,GAAG,KAAK,SAAS,QAAQ,SAAS,GAAG;AACpF,mBAAW,OAAO,QAAQ,MAAM,QAAQ;AAAA,MAC1C;AACA,UAAI,SAAS,WAAW,GAAG,GAAG;AAC5B,mBAAW,SAAS,OAAO,MAAM,IAAI,OAAO,QAAQ,GAAG;AAAA,MACzD;AACA,UAAI,SAAS,WAAW,IAAI,KAAK,SAAS,QAAQ,IAAI,KAAK,SAAS,QAAQ,UAAU,GAAG;AACvF,mBAAW,QAAQ,QAAQ,OAAO,QAAQ;AAAA,MAC5C;AACA,UAAI,SAAS,WAAW,GAAG,GAAG;AAC5B,mBAAW,QAAQ,OAAO,KAAK,IAAI,OAAO,QAAQ,IAAI;AAAA,MACxD;AAAA,IACF,WAAW,SAAS;AAElB,mBAAa;AAAA,IACf;AAAA,EACF;AAEA,MAAI,SAAS,WAAW,IAAI,GAAG;AAC7B,eAAW,QAAQ;AAAA,EACrB;AACA,MAAI,SAAS,WAAW,GAAG,GAAG;AAC5B,eAAW,OAAO;AAAA,EACpB;AACA,MAAI,SAAS,WAAW,KAAK,GAAG;AAC9B,eAAW,SAAS;AAAA,EACtB;AACA,MAAI,SAAS,WAAW,MAAM,GAAG;AAC/B,eAAW,UAAU;AAAA,EACvB;AACA,SAAO;AACT;AAMA,SAAS,QAAQ;AAAA,EACf;AAAA,EACA;AACF,GAAG;AACD,SAAO,QAAQ;AACjB;AACA,SAAS,qBAAqB,WAAW,SAAS,QAAQ,MAAM,mBAAmB,CAAC,OAAO,UAAU,SAAS,MAAM,GAAG,oBAAoB,YAAY,UAAU,GAAG;AAClK,MAAI,UAAU,QAAQ,MAAM,MAAM,IAAI;AACpC,WAAO;AAAA,EACT;AACA,QAAM,aAAa,cAAc,QAAQ,MAAM,SAAS,iBAAiB;AACzE,QAAM,QAAQ;AAAA,IACZ,KAAK;AAAA,MACH,OAAO,YAAY,SAAS;AAAA,MAC5B,SAAS,SAAS,OAAO,MAAM,YAAY,OAAO;AAAA,IACpD;AAAA,IACA,OAAO;AAAA,MACL,QAAQ,YAAY,SAAS,MAAM,SAAS,SAAS;AAAA,MACrD,QAAQ,YAAY,UAAU;AAAA,IAChC;AAAA,IACA,QAAQ;AAAA,MACN,OAAO,YAAY,SAAS;AAAA,MAC5B,SAAS,YAAY,UAAU,MAAM,SAAS,UAAU;AAAA,IAC1D;AAAA,IACA,MAAM;AAAA,MACJ,QAAQ,QAAQ,QAAQ,MAAM,YAAY,QAAQ;AAAA,MAClD,QAAQ,YAAY,UAAU;AAAA,IAChC;AAAA,EACF;AACA,QAAM,cAAc,OAAO,KAAK,KAAK,EAAE,IAAI,SAAQ;AAAA,IACjD,UAAU;AAAA,KACP,MAAM,GAAG,IAFqC;AAAA,IAGjD,MAAM,QAAQ,MAAM,GAAG,CAAC;AAAA,EAC1B,EAAE,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,OAAO,EAAE,IAAI;AAClC,MAAI,gBAAgB,YAAY,OAAO,CAAC;AAAA,IACtC;AAAA,IACA;AAAA,EACF,MAAM;AACJ,WAAO,SAAS,OAAO,eAAe,UAAU,OAAO;AAAA,EACzD,CAAC;AACD,kBAAgB,cAAc,OAAO,CAAC;AAAA,IACpC;AAAA,EACF,MAAM;AACJ,WAAO,iBAAiB,KAAK,qBAAmB;AAC9C,aAAO,oBAAoB;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC;AACD,QAAM,oBAAoB,cAAc,SAAS,IAAI,cAAc,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE;AAChG,QAAM,YAAY,UAAU,MAAM,GAAG,EAAE,CAAC;AAExC,SAAO,YAAY,OAAO,UAAU,QAAQ,oBAAoB,cAAc,SAAS,EAAE,QAAQ,gBAAgB,iBAAiB,IAAI,iBAAiB,EAAE;AACzJ,SAAO,qBAAqB,YAAY,IAAI,SAAS,KAAK;AAC5D;AACA,SAAS,WAAW,MAAM;AACxB,SAAO;AAAA,IACL,OAAO,KAAK,QAAQ,OAAO;AAAA,IAC3B,QAAQ,KAAK,QAAQ,OAAO;AAAA,IAC5B,MAAM,KAAK,MAAM,KAAK,QAAQ,OAAO,QAAQ,CAAC;AAAA,IAC9C,KAAK,KAAK,MAAM,KAAK,QAAQ,OAAO,OAAO,CAAC;AAAA,IAC5C,QAAQ,KAAK,MAAM,KAAK,QAAQ,OAAO,UAAU,CAAC;AAAA,IAClD,OAAO,KAAK,MAAM,KAAK,QAAQ,OAAO,SAAS,CAAC;AAAA,EAClD;AACF;AAKA,SAAS,qBAAqB,WAAW;AACvC,QAAM,OAAO;AAAA,IACX,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,KAAK;AAAA,EACP;AACA,SAAO,UAAU,QAAQ,0BAA0B,aAAW,KAAK,OAAO,CAAC;AAC7E;AAKA,SAAS,qBAAqB,WAAW;AACvC,MAAI,cAAc,SAAS;AACzB,WAAO;AAAA,EACT,WAAW,cAAc,QAAQ;AAC/B,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,QAAQ,CAAC,OAAO,MAAM,MAAM,QAAQ,WAAW,KAAK,IAAI;AAC9D,SAAS,cAAc,SAAS;AAC9B,QAAMD,UAAS,QAAQ,cAAc;AACrC,QAAM,SAASA,SAAQ,iBAAiB,OAAO;AAC/C,QAAM,IAAI,MAAM,QAAQ,SAAS,IAAI,MAAM,QAAQ,YAAY;AAC/D,QAAM,IAAI,MAAM,QAAQ,UAAU,IAAI,MAAM,QAAQ,WAAW;AAC/D,SAAO;AAAA,IACL,OAAO,OAAO,QAAQ,WAAW,IAAI;AAAA,IACrC,QAAQ,OAAO,QAAQ,YAAY,IAAI;AAAA,EACzC;AACF;AAKA,SAAS,oBAAoB,QAAQ,MAAM,eAAe;AACxD,QAAM,qBAAqB,gBAAgB,6BAA6B,MAAM,IAAI,uBAAuB,QAAQ,IAAI;AACrH,SAAO,qCAAqC,MAAM,oBAAoB,aAAa;AACrF;AAKA,SAAS,iBAAiB,QAAQ,aAAa,UAAU;AACvD,QAAM,YAAY,SAAS,MAAM,GAAG,EAAE,CAAC;AAEvC,QAAM,aAAa,cAAc,MAAM;AAEvC,QAAM,gBAAgB;AAAA,IACpB,OAAO,WAAW;AAAA,IAClB,QAAQ,WAAW;AAAA,EACrB;AAEA,QAAM,UAAU,CAAC,SAAS,MAAM,EAAE,QAAQ,SAAS,MAAM;AACzD,QAAM,WAAW,UAAU,QAAQ;AACnC,QAAM,gBAAgB,UAAU,SAAS;AACzC,QAAM,cAAc,UAAU,WAAW;AACzC,QAAM,uBAAuB,CAAC,UAAU,WAAW;AACnD,gBAAc,QAAQ,KAAK,YAAY,QAAQ,KAAK,KAAK,YAAY,WAAW,IAAI,IAAI,WAAW,WAAW,IAAI;AAClH,gBAAc,aAAa,IAAI,cAAc,iBAAiB,YAAY,aAAa,KAAK,KAAK,WAAW,oBAAoB,IAAI,YAAY,qBAAqB,aAAa,CAAC,KAAK;AACxL,SAAO;AACT;AACA,SAAS,kBAAkB,SAAS,cAAc;AAChD,SAAO,CAAC,CAAC,QAAQ,UAAU,YAAY,GAAG;AAC5C;AACA,IAAM,qBAAqB;AAAA,EACzB,KAAK,CAAC,OAAO,aAAa,SAAS;AAAA,EACnC,QAAQ,CAAC,UAAU,gBAAgB,YAAY;AAAA,EAC/C,OAAO,CAAC,SAAS,aAAa,cAAc;AAAA,EAC5C,KAAK,CAAC,OAAO,WAAW,YAAY;AACtC;AACA,SAAS,mBAAmB,WAAW,eAAe;AACpD,MAAI,CAAC,SAAS,EAAE,OAAO;AACrB,WAAO;AAAA,EACT;AACA,SAAO,mBAAmB,aAAa,EAAE,SAAS,SAAS;AAC7D;AACA,SAAS,aAAa,WAAW;AAC/B,MAAI,CAAC,SAAS,EAAE,OAAO;AACrB,WAAO;AAAA,EACT;AACA,MAAI,mBAAmB,WAAW,KAAK,GAAG;AACxC,WAAO;AAAA,EACT;AACA,MAAI,mBAAmB,WAAW,OAAO,GAAG;AAC1C,WAAO;AAAA,EACT;AACA,MAAI,mBAAmB,WAAW,KAAK,GAAG;AACxC,WAAO;AAAA,EACT;AACA,MAAI,mBAAmB,WAAW,QAAQ,GAAG;AAC3C,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,MAAM,UAAU;AAC5C,QAAM,SAAS,KAAK,SAAS;AAC7B,MAAI,iBAAiB,OAAO;AAC5B,QAAM,gBAAgB,SAAS,EAAE,QAAQ,gBAAgB,KAAK,SAAS,IAAI,KAAK;AAChF,MAAI,KAAK,eAAe;AACtB,qBAAiB,eAAe,QAAQ,oBAAoB,cAAc,aAAa,EAAE;AACzF,qBAAiB,eAAe,QAAQ,wBAAwB,EAAE;AAClE,qBAAiB,eAAe,QAAQ,oBAAoB,cAAc,aAAa,EAAE;AACzF,qBAAiB,eAAe,QAAQ,WAAW,IAAI,aAAa,EAAE;AACtE,QAAI,eAAe,QAAQ,SAAS,MAAM,IAAI;AAC5C,uBAAiB,iBAAiB,MAAM,aAAa,aAAa;AAAA,IACpE;AACA,QAAI,eAAe,QAAQ,SAAS,MAAM,MAAM,eAAe,QAAQ,cAAc,MAAM,IAAI;AAC7F,wBAAkB;AAAA,IACpB;AACA,QAAI,eAAe,QAAQ,SAAS,MAAM,MAAM,eAAe,QAAQ,cAAc,MAAM,IAAI;AAC7F,wBAAkB;AAAA,IACpB;AAAA,EACF;AACA,mBAAiB,eAAe,QAAQ,oCAAoC,GAAG,cAAc,MAAM,GAAG,EAAE,CAAC,CAAC,EAAE;AAC5G,MAAI,UAAU;AACZ,aAAS,aAAa,QAAQ,SAAS,cAAc;AACrD;AAAA,EACF;AACA,SAAO,YAAY;AACrB;AACA,SAAS,UAAU,SAAS,QAAQ,UAAU;AAC5C,MAAI,CAAC,WAAW,CAAC,QAAQ;AACvB;AAAA,EACF;AACA,SAAO,KAAK,MAAM,EAAE,QAAQ,UAAQ;AAClC,QAAI,OAAO;AAEX,QAAI,CAAC,SAAS,UAAU,OAAO,SAAS,UAAU,MAAM,EAAE,QAAQ,IAAI,MAAM,MAAM,UAAU,OAAO,IAAI,CAAC,GAAG;AACzG,aAAO;AAAA,IACT;AACA,QAAI,UAAU;AACZ,eAAS,SAAS,SAAS,MAAM,GAAG,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE;AACjE;AAAA,IACF;AAEA,YAAQ,MAAM,IAAI,IAAI,OAAO,OAAO,IAAI,CAAC,IAAI;AAAA,EAC/C,CAAC;AACH;AACA,SAAS,MAAM,MAAM;AACnB,MAAI,gBAAgB,KAAK,QAAQ;AAEjC,QAAM,eAAe,KAAK,SAAS,OAAO,cAAc,QAAQ;AAEhE,MAAI,CAAC,cAAc;AACjB,WAAO;AAAA,EACT;AACA,QAAM,aAAa,CAAC,QAAQ,OAAO,EAAE,QAAQ,KAAK,UAAU,MAAM,GAAG,EAAE,CAAC,CAAC,MAAM;AAC/E,QAAM,MAAM,aAAa,WAAW;AACpC,QAAM,kBAAkB,aAAa,QAAQ;AAC7C,QAAM,OAAO,gBAAgB,YAAY;AACzC,QAAM,UAAU,aAAa,SAAS;AACtC,QAAM,SAAS,aAAa,WAAW;AACvC,QAAM,mBAAmB,cAAc,YAAY,EAAE,GAAG;AACxD,QAAM,qBAAqB,KAAK,UAAU,MAAM,GAAG,EAAE,CAAC;AAEtD,OAAK,KAAK,QAAQ,KAAK,MAAM,KAAK,KAAK,oBAAoB,cAAc,IAAI,KAAK,IAAI;AACpF,kBAAc,IAAI,MAAM,cAAc,IAAI,KAAK,OAAO,KAAK,QAAQ,KAAK,MAAM,KAAK,KAAK;AAAA,EAC1F;AAEA,MAAI,OAAO,KAAK,QAAQ,KAAK,IAAI,CAAC,IAAI,OAAO,gBAAgB,KAAK,cAAc,MAAM,KAAK,IAAI;AAC7F,kBAAc,IAAI,KAAK,OAAO,KAAK,QAAQ,KAAK,IAAI,CAAC,IAAI,OAAO,gBAAgB,IAAI,OAAO,cAAc,MAAM,CAAC;AAAA,EAClH;AACA,kBAAgB,cAAc,aAAa;AAG3C,QAAM,MAAM,yBAAyB,KAAK,SAAS,MAAM;AACzD,QAAM,mBAAmB,WAAW,IAAI,SAAS,eAAe,EAAE,CAAC,KAAK;AACxE,QAAM,mBAAmB,WAAW,IAAI,SAAS,eAAe,OAAO,CAAC,KAAK;AAE7E,MAAI;AACJ,MAAI,CAAC,oBAAoB;AACvB,aAAS,OAAO,KAAK,QAAQ,KAAK,IAAI,CAAC,IAAI,OAAO,KAAK,QAAQ,KAAK,GAAG,IAAI,IAAI,mBAAmB,CAAC;AAAA,EACrG,OAAO;AACL,UAAM,qBAAqB,WAAW,IAAI,cAAc,CAAC,KAAK;AAC9D,UAAM,wBAAwB,OAAO,mBAAmB,mBAAmB,kBAAkB;AAC7F,aAAS,SAAS,qBAAqB,OAAO,KAAK,QAAQ,KAAK,IAAI,CAAC,IAAI,wBAAwB,OAAO,KAAK,QAAQ,KAAK,IAAI,CAAC,IAAI,OAAO,KAAK,QAAQ,KAAK,GAAG,IAAI,qBAAqB;AAAA,EAC1L;AACA,MAAI,YAAY,UAAU,cAAc,IAAI,KAAK,KAAK,mBAAmB;AAEzE,cAAY,KAAK,IAAI,KAAK,IAAI,cAAc,GAAG,KAAK,mBAAmB,IAAI,SAAS,GAAG,CAAC;AACxF,OAAK,QAAQ,QAAQ;AAAA,IACnB,CAAC,IAAI,GAAG,KAAK,MAAM,SAAS;AAAA,IAC5B,CAAC,OAAO,GAAG;AAAA;AAAA,EACb;AACA,OAAK,SAAS,QAAQ;AACtB,SAAO;AACT;AACA,SAAS,KAAK,MAAM;AAClB,OAAK,QAAQ,SAAS,cAAc,KAAK,QAAQ,MAAM;AACvD,MAAI,CAAC,kBAAkB,KAAK,SAAS,MAAM,GAAG;AAC5C,SAAK,QAAQ,SAAS,kCACjB,KAAK,QAAQ,SACb,iBAAiB,KAAK,SAAS,QAAQ,KAAK,QAAQ,MAAM,KAAK,SAAS;AAE7E,WAAO;AAAA,EACT;AACA,QAAM,aAAa;AAAA,IAAc,KAAK,SAAS;AAAA,IAAQ,KAAK,SAAS;AAAA,IAAM;AAAA;AAAA,IAE3E;AAAA,IAAY;AAAA;AAAA,EACZ;AACA,MAAI,YAAY,KAAK,UAAU,MAAM,GAAG,EAAE,CAAC;AAC3C,MAAI,YAAY,KAAK,UAAU,MAAM,GAAG,EAAE,CAAC,KAAK;AAChD,QAAM,cAAc,KAAK,QAAQ;AACjC,QAAM,SAAS,KAAK,SAAS;AAC7B,QAAM,OAAO,KAAK,SAAS;AAC3B,QAAM,mBAAmB,qBAAqB,QAAQ,aAAa,QAAQ,MAAM,KAAK,QAAQ,gBAAgB;AAC9G,QAAM,YAAY,CAAC,WAAW,gBAAgB;AAC9C,YAAU,QAAQ,CAAC,MAAM,UAAU;AACjC,QAAI,cAAc,QAAQ,UAAU,WAAW,QAAQ,GAAG;AACxD;AAAA,IACF;AACA,gBAAY,KAAK,UAAU,MAAM,GAAG,EAAE,CAAC;AAEvC,UAAM,cAAc,cAAc,UAAU,KAAK,MAAM,KAAK,QAAQ,OAAO,SAAS,CAAC,IAAI,KAAK,MAAM,KAAK,QAAQ,KAAK,QAAQ,CAAC,KAAK,cAAc,WAAW,KAAK,MAAM,KAAK,QAAQ,OAAO,QAAQ,CAAC,IAAI,KAAK,MAAM,KAAK,QAAQ,KAAK,SAAS,CAAC,KAAK,cAAc,SAAS,KAAK,MAAM,KAAK,QAAQ,OAAO,UAAU,CAAC,IAAI,KAAK,MAAM,KAAK,QAAQ,KAAK,OAAO,CAAC,KAAK,cAAc,YAAY,KAAK,MAAM,KAAK,QAAQ,OAAO,OAAO,CAAC,IAAI,KAAK,MAAM,KAAK,QAAQ,KAAK,UAAU,CAAC;AACjd,UAAM,gBAAgB,KAAK,MAAM,KAAK,QAAQ,OAAO,QAAQ,CAAC,IAAI,KAAK,MAAM,WAAW,QAAQ,CAAC;AACjG,UAAM,iBAAiB,KAAK,MAAM,KAAK,QAAQ,OAAO,SAAS,CAAC,IAAI,KAAK,MAAM,WAAW,SAAS,CAAC;AACpG,UAAM,eAAe,KAAK,MAAM,KAAK,QAAQ,OAAO,OAAO,CAAC,IAAI,KAAK,MAAM,WAAW,OAAO,CAAC;AAC9F,UAAM,kBAAkB,KAAK,MAAM,KAAK,QAAQ,OAAO,UAAU,CAAC,IAAI,KAAK,MAAM,WAAW,UAAU,CAAC;AACvG,UAAM,sBAAsB,cAAc,UAAU,iBAAiB,cAAc,WAAW,kBAAkB,cAAc,SAAS,gBAAgB,cAAc,YAAY;AAEjL,UAAM,aAAa,CAAC,OAAO,QAAQ,EAAE,QAAQ,SAAS,MAAM;AAC5D,UAAM,mBAAmB,cAAc,cAAc,UAAU,iBAAiB,cAAc,cAAc,WAAW,kBAAkB,CAAC,cAAc,cAAc,UAAU,gBAAgB,CAAC,cAAc,cAAc,WAAW;AACxO,QAAI,eAAe,uBAAuB,kBAAkB;AAC1D,UAAI,eAAe,qBAAqB;AACtC,oBAAY,UAAU,QAAQ,CAAC;AAAA,MACjC;AACA,UAAI,kBAAkB;AACpB,oBAAY,qBAAqB,SAAS;AAAA,MAC5C;AACA,WAAK,YAAY,aAAa,YAAY,IAAI,SAAS,KAAK;AAC5D,WAAK,QAAQ,SAAS,kCACjB,KAAK,QAAQ,SACb,iBAAiB,KAAK,SAAS,QAAQ,KAAK,QAAQ,MAAM,KAAK,SAAS;AAAA,IAE/E;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,SAAS,eAAe,aAAa,UAAU,SAAS;AAC/D,MAAI,CAAC,iBAAiB,CAAC,aAAa;AAClC;AAAA,EACF;AACA,QAAM,iBAAiB,oBAAoB,eAAe,WAAW;AACrE,MAAI,CAAC,SAAS,MAAM,gDAAgD,KAAK,CAAC,SAAS,MAAM,6EAA6E,GAAG;AACvK,eAAW;AAAA,EACb;AACA,QAAM,gBAAgB,CAAC,CAAC,SAAS,MAAM,OAAO;AAE9C,MAAI,YAAY,SAAS,MAAM,yCAAyC,IAAI,SAAS,MAAM,GAAG,EAAE,CAAC,KAAK,SAAS;AAE/G,QAAM,UAAU,UAAU,MAAM,+EAA+E;AAC/G,MAAI,SAAS;AACX,gBAAY,QAAQ,CAAC,KAAK,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,CAAC,KAAK;AAAA,EAC5D;AAEA,MAAI,CAAC,cAAc,cAAc,cAAc,YAAY,EAAE,QAAQ,SAAS,MAAM,IAAI;AACtF,gBAAY;AAAA,EACd;AACA,cAAY,qBAAqB,WAAW,gBAAgB,eAAe,aAAa,UAAU,QAAQ,mBAAmB,MAAS;AACtI,QAAM,eAAe,iBAAiB,eAAe,gBAAgB,SAAS;AAC9E,SAAO;AAAA,IACL,SAAS,WAAW;AAAA,MAClB,WAAW,CAAC;AAAA,IACd;AAAA,IACA,UAAU;AAAA,MACR,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,IACf;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,CAAC,kBAAkB,KAAK,SAAS,iBAAiB,GAAG;AACvD,WAAO;AAAA,EACT;AAIA,QAAM,gBAAgB;AACtB,QAAM,eAAe,KAAK,SAAS,OAAO;AAC1C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,CAAC,aAAa,GAAG;AAAA,EACnB,IAAI;AACJ,eAAa,MAAM;AACnB,eAAa,OAAO;AACpB,eAAa,aAAa,IAAI;AAC9B,QAAM,aAAa;AAAA,IAAc,KAAK,SAAS;AAAA,IAAQ,KAAK,SAAS;AAAA,IAAM;AAAA;AAAA,IAE3E,KAAK,QAAQ,UAAU,iBAAiB,qBAAqB;AAAA,IAAgB;AAAA;AAAA,EAC7E;AAGA,eAAa,MAAM;AACnB,eAAa,OAAO;AACpB,eAAa,aAAa,IAAI;AAC9B,QAAM,QAAQ,CAAC,QAAQ,SAAS,OAAO,QAAQ;AAC/C,QAAM,QAAQ;AAAA,IACZ,QAAQ,WAAW;AACjB,UAAI,QAAQ,KAAK,QAAQ,OAAO,SAAS;AAEzC,WAAK,KAAK,QAAQ,OAAO,SAAS,KAAK,MAAM,WAAW,SAAS,KAAK,IAAI;AACxE,gBAAQ,KAAK,IAAI,KAAK,QAAQ,OAAO,SAAS,KAAK,GAAG,WAAW,SAAS,KAAK,CAAC;AAAA,MAClF;AACA,aAAO;AAAA,QACL,CAAC,SAAS,GAAG;AAAA,MACf;AAAA,IACF;AAAA,IACA,UAAU,WAAW;AACnB,YAAM,wBAAwB,cAAc;AAC5C,YAAM,WAAW,wBAAwB,SAAS;AAClD,YAAM,cAAc,wBAAwB,UAAU;AACtD,UAAI,QAAQ,KAAK,QAAQ,OAAO,QAAQ;AAExC,WAAK,KAAK,QAAQ,OAAO,SAAS,KAAK,MAAM,WAAW,SAAS,KAAK,IAAI;AACxE,gBAAQ,KAAK,IAAI,KAAK,QAAQ,OAAO,QAAQ,KAAK,IAAI,WAAW,SAAS,KAAK,KAAK,KAAK,QAAQ,OAAO,WAAW,CAAC;AAAA,MACtH;AACA,aAAO;AAAA,QACL,CAAC,QAAQ,GAAG;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACA,QAAM,QAAQ,eAAa;AACzB,UAAM,OAAO,CAAC,QAAQ,OAAO,OAAO,EAAE,QAAQ,SAAS,MAAM,KAAK,MAAM,SAAS,IAAI,MAAM,WAAW;AACtG,SAAK,QAAQ,SAAS,kCACjB,KAAK,QAAQ,SACb,KAAK,SAAS;AAAA,EAErB,CAAC;AACD,SAAO;AACT;AACA,SAAS,MAAM,MAAM;AACnB,QAAM,YAAY,KAAK;AACvB,QAAM,gBAAgB,UAAU,MAAM,GAAG,EAAE,CAAC;AAC5C,QAAM,iBAAiB,UAAU,MAAM,GAAG,EAAE,CAAC;AAC7C,MAAI,gBAAgB;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,UAAM,aAAa,CAAC,UAAU,KAAK,EAAE,QAAQ,aAAa,MAAM;AAChE,UAAM,OAAO,aAAa,SAAS;AACnC,UAAM,cAAc,aAAa,UAAU;AAC3C,UAAM,eAAe;AAAA,MACnB,OAAO;AAAA,QACL,CAAC,IAAI,GAAG,KAAK,IAAI;AAAA,MACnB;AAAA,MACA,KAAK;AAAA,QACH,CAAC,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,WAAW,IAAI,OAAO,WAAW;AAAA,MACpE;AAAA,IACF;AACA,SAAK,QAAQ,SAAS,kCACjB,SACA;AAAA,MACD,CAAC,IAAI,GAAG,SAAS,iBAAiB,aAAa,MAAM,IAAI,IAAI,aAAa,IAAI,IAAI;AAAA,IACpF;AAAA,EAEJ;AACA,SAAO;AACT;AACA,IAAM,cAAN,MAAkB;AAAA,EAChB,SAAS,aAAa,eAAkC;AACtD,WAAO,KAAK;AAAA,MAAO;AAAA,MAAa;AAAA;AAAA,IAAyB;AAAA,EAC3D;AAAA,EACA,OAAO,aAAa,eAAkC;AACpD,WAAO,oBAAoB,eAAe,WAAW;AAAA,EACvD;AAAA,EACA,iBAAiB,aAAa,eAAe,UAAU,cAAc,SAAS;AAC5E,UAAM,mBAAmB,CAAC,MAAM,OAAO,iBAAiB,KAAK;AAC7D,UAAM,YAAY,mBAAmB,QAAQ;AAC7C,UAAM,OAAO,SAAS,eAAe,aAAa,WAAW,OAAO;AACpE,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AACA,WAAO,iBAAiB,OAAO,CAAC,cAAc,aAAa,SAAS,YAAY,GAAG,IAAI;AAAA,EACzF;AACF;AACA,IAAM,kBAAkB,IAAI,YAAY;AACxC,SAAS,iBAAiB,aAAa,eAAe,WAAW,cAAc,SAAS,UAAU;AAChG,QAAM,OAAO,gBAAgB,iBAAiB,aAAa,eAAe,WAAW,cAAc,OAAO;AAC1G,MAAI,CAAC,MAAM;AACT;AAAA,EACF;AACA,QAAM,UAAU,WAAW,IAAI;AAC/B,YAAU,eAAe;AAAA,IACvB,eAAe;AAAA,IACf,KAAK;AAAA,IACL,MAAM;AAAA,IACN,WAAW,eAAe,QAAQ,IAAI,OAAO,QAAQ,GAAG;AAAA,EAC1D,GAAG,QAAQ;AACX,MAAI,KAAK,SAAS,OAAO;AACvB,cAAU,KAAK,SAAS,OAAO,KAAK,QAAQ,OAAO,QAAQ;AAAA,EAC7D;AACA,uBAAqB,MAAM,QAAQ;AACrC;AACA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,YAAY,QAAQ,iBAAiB,YAAY;AAC/C,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,mBAAmB,oBAAI,IAAI;AAChC,SAAK,aAAa;AAClB,QAAI,kBAAkB,UAAU,GAAG;AACjC,aAAO,kBAAkB,MAAM;AAC7B,aAAK,gBAAgB,MAAM,UAAU,QAAQ,UAAU;AAAA,UACrD,SAAS;AAAA,QACX,CAAC,GAAG,UAAU,QAAQ,UAAU;AAAA,UAC9B,SAAS;AAAA,QACX,CAAC,GAAG,GAAG,GAAG,uBAAuB,GAAG,KAAK,QAAQ;AACjD,aAAK,cAAc,UAAU,MAAM;AACjC,cAAI,KAAK,YAAY;AACnB;AAAA,UACF;AACA,eAAK,iBAEJ,QAAQ,qBAAmB;AAC1B,6BAAiB,gBAAgB,gBAAgB,MAAM,GAAG,gBAAgB,gBAAgB,OAAO,GAAG,gBAAgB,YAAY,gBAAgB,cAAc,KAAK,SAAS,gBAAgB,eAAe,MAAM,IAAI,CAAC;AAAA,UACxN,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,SAAS,SAAS;AAChB,SAAK,mBAAmB,OAAO;AAAA,EACjC;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,UAAU;AACR,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,SAAS;AACP,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,mBAAmB,SAAS;AAC1B,SAAK,iBAAiB,IAAI,gBAAgB,QAAQ,OAAO,GAAG,OAAO;AAAA,EACrE;AAAA,EACA,eAAe;AACb,SAAK,SAAS,KAAK,IAAI;AAAA,EACzB;AAAA,EACA,sBAAsB,OAAO;AAC3B,SAAK,iBAAiB,OAAO,gBAAgB,KAAK,CAAC;AAAA,EACrD;AAAA,EACA,WAAW,SAAS;AAClB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAuB,SAAY,MAAM,GAAM,SAAY,gBAAgB,GAAM,SAAS,WAAW,CAAC;AAAA,IACzI;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,oBAAmB;AAAA,MAC5B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,gBAAgB,SAAS;AAEhC,MAAI,OAAO,YAAY,UAAU;AAC/B,WAAO,SAAS,cAAc,OAAO;AAAA,EACvC;AACA,MAAI,mBAAmB,YAAY;AACjC,WAAO,QAAQ;AAAA,EACjB;AACA,SAAO,WAAW;AACpB;;;ACt/BA;AACA;AAGA;AAOA,IAAM,aAAN,MAAiB;AAAA,EACf,YAEA,OAAO,SAEP,cAAc;AACZ,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,eAAe;AAAA,EACtB;AACF;AAKA,IAAM,kBAAN,MAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,YAAY,mBAAmB,WAAW,aAAa,WAAW,2BAA2B,SAAS,iBAAiB,aAAa,WAAW;AAC7I,SAAK,oBAAoB;AACzB,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,4BAA4B;AACjC,SAAK,UAAU;AACf,SAAK,kBAAkB;AACvB,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,UAAU,IAAI,aAAa;AAChC,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,aAAa,CAAC;AACnB,SAAK,YAAY;AAIjB,SAAK,2BAA2B;AAChC,SAAK,cAAc,CAAC;AACpB,SAAK,kBAAkB,SAAS;AAAA,EAClC;AAAA,EACA,IAAI,UAAU;AACZ,QAAI,KAAK,WAAW;AAClB,aAAO;AAAA,IACT;AACA,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA,EACA,OAAO,UAAU;AACf,SAAK,oBAAoB,KAAK,0BAA0B,wBAAwB,QAAQ;AACxF,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,GAAG,WAAW;AACZ,SAAK,YAAY,aAAa,KAAK;AACnC,WAAO;AAAA,EACT;AAAA,EACA,SAAS,MAAM;AACb,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,SAAK,aAAa,KAAK,cAAc,KAAK;AAC1C,SAAK,cAAc,KAAK,UAAU,KAAK;AACvC,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,UAAU;AAChB,SAAK,WAAW,KAAK,QAAQ;AAC7B,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,KAAK,OAAO,CAAC,GAAG;AACd,SAAK,sBAAsB;AAC3B,SAAK,kBAAkB;AACvB,QAAI,CAAC,KAAK,eAAe;AACvB,WAAK,aAAa,KAAK;AACvB,WAAK,cAAc,KAAK,eAAe,KAAK,SAAS,KAAK,SAAS,KAAK,YAAY;AACpF,YAAM,WAAW,SAAS,OAAO;AAAA,QAC/B,WAAW,KAAK;AAAA,QAChB,QAAQ,KAAK;AAAA,MACf,CAAC;AACD,UAAI,CAAC,KAAK,mBAAmB;AAC3B;AAAA,MACF;AACA,WAAK,gBAAgB,KAAK,kBAAkB,OAAO,UAAU,KAAK,YAAY,KAAK;AACnF,WAAK,gBAAgB,WAAW,KAAK,cAAc,QAAQ;AAG3D,WAAK,WAAW,KAAK,cAAc;AACnC,aAAO,OAAO,KAAK,cAAc,UAAU,IAAI;AAC/C,UAAI,KAAK,qBAAqB,YAAY;AACxC,aAAK,UAAU,cAAc,YAAY,KAAK,cAAc,SAAS,aAAa;AAAA,MACpF;AACA,UAAI,OAAO,KAAK,cAAc,YAAY,OAAO,KAAK,cAAc,aAAa;AAC/E,cAAM,kBAAkB,KAAK,UAAU,cAAc,KAAK,SAAS,KAAK,KAAK,UAAU,cAAc,KAAK,wBAAwB;AAClI,YAAI,CAAC,iBAAiB;AACpB;AAAA,QACF;AACA,wBAAgB,YAAY,KAAK,cAAc,SAAS,aAAa;AAAA,MACvE;AACA,UAAI,CAAC,KAAK,aAAa,KAAK,eAAe,KAAK,YAAY,cAAc,eAAe;AACvF,aAAK,YAAY,cAAc,cAAc,YAAY,KAAK,cAAc,SAAS,aAAa;AAAA,MACpG;AAKA,UAAI,KAAK,YAAY,cAAc;AACjC,aAAK,kBAAkB,KAAK,YAAY,aAAa;AACrD,aAAK,YAAY,aAAa,kBAAkB,aAAa;AAC7D,aAAK,YAAY,aAAa,kBAAkB,cAAc;AAAA,MAChE;AACA,WAAK,cAAc,kBAAkB,aAAa;AAClD,WAAK,cAAc,kBAAkB,cAAc;AACnD,WAAK,QAAQ,KAAK,KAAK,KAAK;AAAA,QAC1B,IAAI,KAAK;AAAA,MACX,IAAI,KAAK,cAAc,QAAQ;AAAA,IACjC;AACA,SAAK,sBAAsB;AAC3B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,KAAK,IAAI;AACP,QAAI,CAAC,KAAK,eAAe;AACvB,aAAO;AAAA,IACT;AACA,SAAK,YAAY,sBAAsB,KAAK,cAAc,QAAQ;AAClE,SAAK,aAAa,KAAK,KAAK,cAAc,QAAQ;AAClD,UAAM,cAAc,KAAK,cAAc,SAAS;AAChD,gBAAY,YAAY,YAAY,WAAW;AAC/C,SAAK,aAAa,cAAc,QAAQ;AACxC,QAAI,KAAK,qBAAqB,KAAK,aAAa,SAAS;AACvD,WAAK,kBAAkB,OAAO,KAAK,kBAAkB,QAAQ,KAAK,YAAY,OAAO,CAAC;AAAA,IACxF;AACA,SAAK,aAAa,SAAS,QAAQ;AACnC,SAAK,eAAe,QAAQ;AAC5B,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,sBAAsB;AAC3B,SAAK,SAAS,KAAK,KAAK;AAAA,MACtB;AAAA,IACF,IAAI,IAAI;AACR,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,QAAI,KAAK,SAAS;AAChB,WAAK,KAAK;AACV;AAAA,IACF;AACA,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,UAAU;AACR,QAAI,KAAK,SAAS;AAChB,WAAK,KAAK;AAAA,IACZ;AACA,SAAK,wBAAwB;AAC7B,QAAI,KAAK,wBAAwB;AAC/B,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,OAAO,YAAY;AACjB,SAAK,WAAW,WAAW,YAAY,KAAK;AAC5C,SAAK,YAAY,eAAe,WAAW;AAC3C,SAAK,YAAY,aAAa,WAAW;AACzC,eAAW,SAAS,WAAW,UAAU,KAAK,aAAa;AAC3D,UAAM,OAAO,KAAK,YAAY,OAAO,MAAM,WAAW,OAAO,WAAW,KAAK,IAAI,KAAK,KAAK,KAAK;AAChG,UAAM,OAAO,KAAK,YAAY,OAAO,kBAAgB;AACnD,iBAAW,OAAO,WAAW,KAAK,YAAY,IAAI,KAAK,KAAK,YAAY;AACxE,mBAAa;AAAA,IACf;AAEA,UAAM,SAAS,kBAAgB;AAC7B,WAAK,UAAU,KAAK,IAAI,KAAK,YAAY;AAAA,IAC3C;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,yBAAyB,mBAAmB,KAAK,WAAW;AAAA,QAC/D,QAAQ,WAAW;AAAA,QACnB,UAAU,WAAW;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,wBAAwB;AACtB,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB;AACrB,WAAK,kBAAkB,SAAS;AAAA,IAClC;AAAA,EACF;AAAA,EACA,aAAa,MAEb,UAAU;AACR,QAAI,QAAQ,UAAU;AACpB,WAAK,iBAAiB,KAAK,mBAAmB,QAAQ;AAAA,IACxD;AACA,WAAO;AAAA,EACT;AAAA,EACA,wBAAwB;AACtB,QAAI,CAAC,KAAK,iBAAiB,CAAC,KAAK,cAAc,UAAU;AACvD;AAAA,IACF;AACA,QAAI,0BAA0B,SAAS;AACvC,QAAI,sBAAsB,SAAS;AAEnC,QAAI,KAAK,YAAY,cAAc;AACjC,YAAM,SAAS,KAAK,cAAc,SAAS;AAC3C,iBAAW,MAAM;AACf,YAAI,KAAK,aAAa,KAAK,aAAa;AACtC,oCAA0B,qBAAqB,KAAK,WAAW;AAAA,YAC7D,SAAS,CAAC,QAAQ,KAAK,YAAY,aAAa;AAAA,YAChD,cAAc,KAAK,YAAY;AAAA,YAC/B,MAAM,MAAM,KAAK,YAAY,QAAQ,KAAK,YAAY,KAAK;AAAA,UAC7D,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,KAAK,YAAY,cAAc,KAAK,aAAa,KAAK,aAAa;AACrE,YAAM,SAAS,KAAK,cAAc,SAAS;AAC3C,4BAAsB,iBAAiB,KAAK,WAAW;AAAA,QACrD,SAAS,CAAC,QAAQ,KAAK,YAAY,aAAa;AAAA,QAChD,YAAY,KAAK,YAAY;AAAA,QAC7B,MAAM,MAAM,KAAK,YAAY,QAAQ,KAAK,YAAY,KAAK;AAAA,MAC7D,CAAC;AAAA,IACH;AACA,SAAK,kBAAkB,MAAM;AAC3B,8BAAwB;AACxB,0BAAoB;AAAA,IACtB;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,wBAAwB;AACtB,QAAI,KAAK,qBAAqB,CAAC,KAAK,YAAY;AAC9C;AAAA,IACF;AACA,SAAK,QAAQ,UAAU,MAAM;AAC3B,WAAK,YAAY,SAAS;AAAA,QACxB,SAAS,KAAK,eAAe;AAAA,QAC7B,QAAQ,KAAK;AAAA,QACb,YAAY,KAAK;AAAA,QACjB,cAAc,KAAK,cAAc;AAAA,MACnC,CAAC;AAAA,IACH,CAAC;AACD,SAAK,oBAAoB,KAAK,QAAQ,SAAS,UAAU,MAAM;AAC7D,UAAI,CAAC,KAAK,eAAe;AACvB;AAAA,MACF;AACA,WAAK,YAAY,aAAa;AAAA,IAChC,CAAC;AAAA,EACH;AAAA,EACA,0BAA0B;AACxB,QAAI,CAAC,KAAK,mBAAmB;AAC3B;AAAA,IACF;AACA,SAAK,kBAAkB,YAAY;AACnC,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,eAEA,SAEA,SAEA,cAAc;AACZ,QAAI,CAAC,SAAS;AACZ,aAAO,IAAI,WAAW,CAAC,CAAC;AAAA,IAC1B;AACA,QAAI,mBAAmB,aAAa;AAClC,UAAI,KAAK,mBAAmB;AAC1B,cAAM,WAAW,KAAK,kBAAkB,mBAAmB,SAAS,OAAO;AAC3E,iBAAS,aAAa;AACtB,eAAO,IAAI,WAAW,CAAC,SAAS,SAAS,GAAG,QAAQ;AAAA,MACtD;AACA,YAAM,UAAU,QAAQ,mBAAmB,CAAC,CAAC;AAC7C,WAAK,gBAAgB,WAAW,OAAO;AACvC,aAAO,IAAI,WAAW,CAAC,QAAQ,SAAS,GAAG,OAAO;AAAA,IACpD;AACA,QAAI,OAAO,YAAY,YAAY;AACjC,YAAM,qBAAqB,KAAK,0BAA0B,wBAAwB,OAAO;AACzF,YAAM,uBAAuB,SAAS,OAAO;AAAA,QAC3C,WAAW,KAAK;AAAA,QAChB,QAAQ,KAAK;AAAA,MACf,CAAC;AACD,YAAM,eAAe,mBAAmB,OAAO,oBAAoB;AAGnE,aAAO,OAAO,aAAa,UAAU,YAAY;AACjD,WAAK,gBAAgB,WAAW,aAAa,QAAQ;AACrD,aAAO,IAAI,WAAW,CAAC,CAAC,aAAa,SAAS,aAAa,CAAC,GAAG,aAAa,UAAU,YAAY;AAAA,IACpG;AACA,UAAM,QAAQ,KAAK,YAAY,CAAC,KAAK,UAAU,WAAW,GAAG,OAAO,EAAE,CAAC,IAAI,CAAC;AAC5E,WAAO,IAAI,WAAW,CAAC,KAAK,CAAC;AAAA,EAC/B;AACF;AACA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,YAAY,2BAA2B,SAAS,WAAW,aAAa,iBAAiB,WAAW;AAClG,SAAK,4BAA4B;AACjC,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,kBAAkB;AACvB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,aAAa,mBAAmB,WAAW;AACtD,WAAO,IAAI,gBAAgB,mBAAmB,WAAW,aAAa,KAAK,WAAW,KAAK,2BAA2B,KAAK,SAAS,KAAK,iBAAiB,KAAK,aAAa,KAAK,SAAS;AAAA,EAC5L;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAA2B,SAAY,0BAAwB,GAAM,SAAY,MAAM,GAAM,SAAY,QAAQ,GAAM,SAAY,kBAAkB,GAAM,SAAY,cAAc,GAAM,SAAS,QAAQ,CAAC;AAAA,IAChP;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,wBAAuB;AAAA,MAChC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;", "names": ["document", "BsVerions", "MapPlacementInToRL", "PlacementForBs5", "window", "document"]}
{"version": 3, "sources": ["../../../../../../node_modules/clipboard/dist/clipboard.js"], "sourcesContent": ["/*!\n * clipboard.js v2.0.1\n * https://zenorocha.github.io/clipboard.js\n * \n * Licensed MIT © Zeno Rocha\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ClipboardJS\"] = factory();\n\telse\n\t\troot[\"ClipboardJS\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// identity function for calling harmony imports with the correct context\n/******/ \t__webpack_require__.i = function(value) { return value; };\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 3);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;(function (global, factory) {\n    if (true) {\n        !(__WEBPACK_AMD_DEFINE_ARRAY__ = [module, __webpack_require__(7)], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n    } else if (typeof exports !== \"undefined\") {\n        factory(module, require('select'));\n    } else {\n        var mod = {\n            exports: {}\n        };\n        factory(mod, global.select);\n        global.clipboardAction = mod.exports;\n    }\n})(this, function (module, _select) {\n    'use strict';\n\n    var _select2 = _interopRequireDefault(_select);\n\n    function _interopRequireDefault(obj) {\n        return obj && obj.__esModule ? obj : {\n            default: obj\n        };\n    }\n\n    var _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n        return typeof obj;\n    } : function (obj) {\n        return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n\n    function _classCallCheck(instance, Constructor) {\n        if (!(instance instanceof Constructor)) {\n            throw new TypeError(\"Cannot call a class as a function\");\n        }\n    }\n\n    var _createClass = function () {\n        function defineProperties(target, props) {\n            for (var i = 0; i < props.length; i++) {\n                var descriptor = props[i];\n                descriptor.enumerable = descriptor.enumerable || false;\n                descriptor.configurable = true;\n                if (\"value\" in descriptor) descriptor.writable = true;\n                Object.defineProperty(target, descriptor.key, descriptor);\n            }\n        }\n\n        return function (Constructor, protoProps, staticProps) {\n            if (protoProps) defineProperties(Constructor.prototype, protoProps);\n            if (staticProps) defineProperties(Constructor, staticProps);\n            return Constructor;\n        };\n    }();\n\n    var ClipboardAction = function () {\n        /**\n         * @param {Object} options\n         */\n        function ClipboardAction(options) {\n            _classCallCheck(this, ClipboardAction);\n\n            this.resolveOptions(options);\n            this.initSelection();\n        }\n\n        /**\n         * Defines base properties passed from constructor.\n         * @param {Object} options\n         */\n\n\n        _createClass(ClipboardAction, [{\n            key: 'resolveOptions',\n            value: function resolveOptions() {\n                var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n                this.action = options.action;\n                this.container = options.container;\n                this.emitter = options.emitter;\n                this.target = options.target;\n                this.text = options.text;\n                this.trigger = options.trigger;\n\n                this.selectedText = '';\n            }\n        }, {\n            key: 'initSelection',\n            value: function initSelection() {\n                if (this.text) {\n                    this.selectFake();\n                } else if (this.target) {\n                    this.selectTarget();\n                }\n            }\n        }, {\n            key: 'selectFake',\n            value: function selectFake() {\n                var _this = this;\n\n                var isRTL = document.documentElement.getAttribute('dir') == 'rtl';\n\n                this.removeFake();\n\n                this.fakeHandlerCallback = function () {\n                    return _this.removeFake();\n                };\n                this.fakeHandler = this.container.addEventListener('click', this.fakeHandlerCallback) || true;\n\n                this.fakeElem = document.createElement('textarea');\n                // Prevent zooming on iOS\n                this.fakeElem.style.fontSize = '12pt';\n                // Reset box model\n                this.fakeElem.style.border = '0';\n                this.fakeElem.style.padding = '0';\n                this.fakeElem.style.margin = '0';\n                // Move element out of screen horizontally\n                this.fakeElem.style.position = 'absolute';\n                this.fakeElem.style[isRTL ? 'right' : 'left'] = '-9999px';\n                // Move element to the same position vertically\n                var yPosition = window.pageYOffset || document.documentElement.scrollTop;\n                this.fakeElem.style.top = yPosition + 'px';\n\n                this.fakeElem.setAttribute('readonly', '');\n                this.fakeElem.value = this.text;\n\n                this.container.appendChild(this.fakeElem);\n\n                this.selectedText = (0, _select2.default)(this.fakeElem);\n                this.copyText();\n            }\n        }, {\n            key: 'removeFake',\n            value: function removeFake() {\n                if (this.fakeHandler) {\n                    this.container.removeEventListener('click', this.fakeHandlerCallback);\n                    this.fakeHandler = null;\n                    this.fakeHandlerCallback = null;\n                }\n\n                if (this.fakeElem) {\n                    this.container.removeChild(this.fakeElem);\n                    this.fakeElem = null;\n                }\n            }\n        }, {\n            key: 'selectTarget',\n            value: function selectTarget() {\n                this.selectedText = (0, _select2.default)(this.target);\n                this.copyText();\n            }\n        }, {\n            key: 'copyText',\n            value: function copyText() {\n                var succeeded = void 0;\n\n                try {\n                    succeeded = document.execCommand(this.action);\n                } catch (err) {\n                    succeeded = false;\n                }\n\n                this.handleResult(succeeded);\n            }\n        }, {\n            key: 'handleResult',\n            value: function handleResult(succeeded) {\n                this.emitter.emit(succeeded ? 'success' : 'error', {\n                    action: this.action,\n                    text: this.selectedText,\n                    trigger: this.trigger,\n                    clearSelection: this.clearSelection.bind(this)\n                });\n            }\n        }, {\n            key: 'clearSelection',\n            value: function clearSelection() {\n                if (this.trigger) {\n                    this.trigger.focus();\n                }\n\n                window.getSelection().removeAllRanges();\n            }\n        }, {\n            key: 'destroy',\n            value: function destroy() {\n                this.removeFake();\n            }\n        }, {\n            key: 'action',\n            set: function set() {\n                var action = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'copy';\n\n                this._action = action;\n\n                if (this._action !== 'copy' && this._action !== 'cut') {\n                    throw new Error('Invalid \"action\" value, use either \"copy\" or \"cut\"');\n                }\n            },\n            get: function get() {\n                return this._action;\n            }\n        }, {\n            key: 'target',\n            set: function set(target) {\n                if (target !== undefined) {\n                    if (target && (typeof target === 'undefined' ? 'undefined' : _typeof(target)) === 'object' && target.nodeType === 1) {\n                        if (this.action === 'copy' && target.hasAttribute('disabled')) {\n                            throw new Error('Invalid \"target\" attribute. Please use \"readonly\" instead of \"disabled\" attribute');\n                        }\n\n                        if (this.action === 'cut' && (target.hasAttribute('readonly') || target.hasAttribute('disabled'))) {\n                            throw new Error('Invalid \"target\" attribute. You can\\'t cut text from elements with \"readonly\" or \"disabled\" attributes');\n                        }\n\n                        this._target = target;\n                    } else {\n                        throw new Error('Invalid \"target\" value, use a valid Element');\n                    }\n                }\n            },\n            get: function get() {\n                return this._target;\n            }\n        }]);\n\n        return ClipboardAction;\n    }();\n\n    module.exports = ClipboardAction;\n});\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar is = __webpack_require__(6);\nvar delegate = __webpack_require__(5);\n\n/**\n * Validates all params and calls the right\n * listener function based on its target type.\n *\n * @param {String|HTMLElement|HTMLCollection|NodeList} target\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listen(target, type, callback) {\n    if (!target && !type && !callback) {\n        throw new Error('Missing required arguments');\n    }\n\n    if (!is.string(type)) {\n        throw new TypeError('Second argument must be a String');\n    }\n\n    if (!is.fn(callback)) {\n        throw new TypeError('Third argument must be a Function');\n    }\n\n    if (is.node(target)) {\n        return listenNode(target, type, callback);\n    }\n    else if (is.nodeList(target)) {\n        return listenNodeList(target, type, callback);\n    }\n    else if (is.string(target)) {\n        return listenSelector(target, type, callback);\n    }\n    else {\n        throw new TypeError('First argument must be a String, HTMLElement, HTMLCollection, or NodeList');\n    }\n}\n\n/**\n * Adds an event listener to a HTML element\n * and returns a remove listener function.\n *\n * @param {HTMLElement} node\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenNode(node, type, callback) {\n    node.addEventListener(type, callback);\n\n    return {\n        destroy: function() {\n            node.removeEventListener(type, callback);\n        }\n    }\n}\n\n/**\n * Add an event listener to a list of HTML elements\n * and returns a remove listener function.\n *\n * @param {NodeList|HTMLCollection} nodeList\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenNodeList(nodeList, type, callback) {\n    Array.prototype.forEach.call(nodeList, function(node) {\n        node.addEventListener(type, callback);\n    });\n\n    return {\n        destroy: function() {\n            Array.prototype.forEach.call(nodeList, function(node) {\n                node.removeEventListener(type, callback);\n            });\n        }\n    }\n}\n\n/**\n * Add an event listener to a selector\n * and returns a remove listener function.\n *\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenSelector(selector, type, callback) {\n    return delegate(document.body, selector, type, callback);\n}\n\nmodule.exports = listen;\n\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports) {\n\nfunction E () {\n  // Keep this empty so it's easier to inherit from\n  // (via https://github.com/lipsmack from https://github.com/scottcorgan/tiny-emitter/issues/3)\n}\n\nE.prototype = {\n  on: function (name, callback, ctx) {\n    var e = this.e || (this.e = {});\n\n    (e[name] || (e[name] = [])).push({\n      fn: callback,\n      ctx: ctx\n    });\n\n    return this;\n  },\n\n  once: function (name, callback, ctx) {\n    var self = this;\n    function listener () {\n      self.off(name, listener);\n      callback.apply(ctx, arguments);\n    };\n\n    listener._ = callback\n    return this.on(name, listener, ctx);\n  },\n\n  emit: function (name) {\n    var data = [].slice.call(arguments, 1);\n    var evtArr = ((this.e || (this.e = {}))[name] || []).slice();\n    var i = 0;\n    var len = evtArr.length;\n\n    for (i; i < len; i++) {\n      evtArr[i].fn.apply(evtArr[i].ctx, data);\n    }\n\n    return this;\n  },\n\n  off: function (name, callback) {\n    var e = this.e || (this.e = {});\n    var evts = e[name];\n    var liveEvents = [];\n\n    if (evts && callback) {\n      for (var i = 0, len = evts.length; i < len; i++) {\n        if (evts[i].fn !== callback && evts[i].fn._ !== callback)\n          liveEvents.push(evts[i]);\n      }\n    }\n\n    // Remove event from queue to prevent memory leak\n    // Suggested by https://github.com/lazd\n    // Ref: https://github.com/scottcorgan/tiny-emitter/commit/c6ebfaa9bc973b33d110a84a307742b7cf94c953#commitcomment-5024910\n\n    (liveEvents.length)\n      ? e[name] = liveEvents\n      : delete e[name];\n\n    return this;\n  }\n};\n\nmodule.exports = E;\n\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;(function (global, factory) {\n    if (true) {\n        !(__WEBPACK_AMD_DEFINE_ARRAY__ = [module, __webpack_require__(0), __webpack_require__(2), __webpack_require__(1)], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n    } else if (typeof exports !== \"undefined\") {\n        factory(module, require('./clipboard-action'), require('tiny-emitter'), require('good-listener'));\n    } else {\n        var mod = {\n            exports: {}\n        };\n        factory(mod, global.clipboardAction, global.tinyEmitter, global.goodListener);\n        global.clipboard = mod.exports;\n    }\n})(this, function (module, _clipboardAction, _tinyEmitter, _goodListener) {\n    'use strict';\n\n    var _clipboardAction2 = _interopRequireDefault(_clipboardAction);\n\n    var _tinyEmitter2 = _interopRequireDefault(_tinyEmitter);\n\n    var _goodListener2 = _interopRequireDefault(_goodListener);\n\n    function _interopRequireDefault(obj) {\n        return obj && obj.__esModule ? obj : {\n            default: obj\n        };\n    }\n\n    var _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n        return typeof obj;\n    } : function (obj) {\n        return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n\n    function _classCallCheck(instance, Constructor) {\n        if (!(instance instanceof Constructor)) {\n            throw new TypeError(\"Cannot call a class as a function\");\n        }\n    }\n\n    var _createClass = function () {\n        function defineProperties(target, props) {\n            for (var i = 0; i < props.length; i++) {\n                var descriptor = props[i];\n                descriptor.enumerable = descriptor.enumerable || false;\n                descriptor.configurable = true;\n                if (\"value\" in descriptor) descriptor.writable = true;\n                Object.defineProperty(target, descriptor.key, descriptor);\n            }\n        }\n\n        return function (Constructor, protoProps, staticProps) {\n            if (protoProps) defineProperties(Constructor.prototype, protoProps);\n            if (staticProps) defineProperties(Constructor, staticProps);\n            return Constructor;\n        };\n    }();\n\n    function _possibleConstructorReturn(self, call) {\n        if (!self) {\n            throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n        }\n\n        return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n    }\n\n    function _inherits(subClass, superClass) {\n        if (typeof superClass !== \"function\" && superClass !== null) {\n            throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n        }\n\n        subClass.prototype = Object.create(superClass && superClass.prototype, {\n            constructor: {\n                value: subClass,\n                enumerable: false,\n                writable: true,\n                configurable: true\n            }\n        });\n        if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n    }\n\n    var Clipboard = function (_Emitter) {\n        _inherits(Clipboard, _Emitter);\n\n        /**\n         * @param {String|HTMLElement|HTMLCollection|NodeList} trigger\n         * @param {Object} options\n         */\n        function Clipboard(trigger, options) {\n            _classCallCheck(this, Clipboard);\n\n            var _this = _possibleConstructorReturn(this, (Clipboard.__proto__ || Object.getPrototypeOf(Clipboard)).call(this));\n\n            _this.resolveOptions(options);\n            _this.listenClick(trigger);\n            return _this;\n        }\n\n        /**\n         * Defines if attributes would be resolved using internal setter functions\n         * or custom functions that were passed in the constructor.\n         * @param {Object} options\n         */\n\n\n        _createClass(Clipboard, [{\n            key: 'resolveOptions',\n            value: function resolveOptions() {\n                var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n                this.action = typeof options.action === 'function' ? options.action : this.defaultAction;\n                this.target = typeof options.target === 'function' ? options.target : this.defaultTarget;\n                this.text = typeof options.text === 'function' ? options.text : this.defaultText;\n                this.container = _typeof(options.container) === 'object' ? options.container : document.body;\n            }\n        }, {\n            key: 'listenClick',\n            value: function listenClick(trigger) {\n                var _this2 = this;\n\n                this.listener = (0, _goodListener2.default)(trigger, 'click', function (e) {\n                    return _this2.onClick(e);\n                });\n            }\n        }, {\n            key: 'onClick',\n            value: function onClick(e) {\n                var trigger = e.delegateTarget || e.currentTarget;\n\n                if (this.clipboardAction) {\n                    this.clipboardAction = null;\n                }\n\n                this.clipboardAction = new _clipboardAction2.default({\n                    action: this.action(trigger),\n                    target: this.target(trigger),\n                    text: this.text(trigger),\n                    container: this.container,\n                    trigger: trigger,\n                    emitter: this\n                });\n            }\n        }, {\n            key: 'defaultAction',\n            value: function defaultAction(trigger) {\n                return getAttributeValue('action', trigger);\n            }\n        }, {\n            key: 'defaultTarget',\n            value: function defaultTarget(trigger) {\n                var selector = getAttributeValue('target', trigger);\n\n                if (selector) {\n                    return document.querySelector(selector);\n                }\n            }\n        }, {\n            key: 'defaultText',\n            value: function defaultText(trigger) {\n                return getAttributeValue('text', trigger);\n            }\n        }, {\n            key: 'destroy',\n            value: function destroy() {\n                this.listener.destroy();\n\n                if (this.clipboardAction) {\n                    this.clipboardAction.destroy();\n                    this.clipboardAction = null;\n                }\n            }\n        }], [{\n            key: 'isSupported',\n            value: function isSupported() {\n                var action = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ['copy', 'cut'];\n\n                var actions = typeof action === 'string' ? [action] : action;\n                var support = !!document.queryCommandSupported;\n\n                actions.forEach(function (action) {\n                    support = support && !!document.queryCommandSupported(action);\n                });\n\n                return support;\n            }\n        }]);\n\n        return Clipboard;\n    }(_tinyEmitter2.default);\n\n    /**\n     * Helper function to retrieve attribute value.\n     * @param {String} suffix\n     * @param {Element} element\n     */\n    function getAttributeValue(suffix, element) {\n        var attribute = 'data-clipboard-' + suffix;\n\n        if (!element.hasAttribute(attribute)) {\n            return;\n        }\n\n        return element.getAttribute(attribute);\n    }\n\n    module.exports = Clipboard;\n});\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports) {\n\nvar DOCUMENT_NODE_TYPE = 9;\n\n/**\n * A polyfill for Element.matches()\n */\nif (typeof Element !== 'undefined' && !Element.prototype.matches) {\n    var proto = Element.prototype;\n\n    proto.matches = proto.matchesSelector ||\n                    proto.mozMatchesSelector ||\n                    proto.msMatchesSelector ||\n                    proto.oMatchesSelector ||\n                    proto.webkitMatchesSelector;\n}\n\n/**\n * Finds the closest parent that matches a selector.\n *\n * @param {Element} element\n * @param {String} selector\n * @return {Function}\n */\nfunction closest (element, selector) {\n    while (element && element.nodeType !== DOCUMENT_NODE_TYPE) {\n        if (typeof element.matches === 'function' &&\n            element.matches(selector)) {\n          return element;\n        }\n        element = element.parentNode;\n    }\n}\n\nmodule.exports = closest;\n\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar closest = __webpack_require__(4);\n\n/**\n * Delegates event to a selector.\n *\n * @param {Element} element\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @param {Boolean} useCapture\n * @return {Object}\n */\nfunction _delegate(element, selector, type, callback, useCapture) {\n    var listenerFn = listener.apply(this, arguments);\n\n    element.addEventListener(type, listenerFn, useCapture);\n\n    return {\n        destroy: function() {\n            element.removeEventListener(type, listenerFn, useCapture);\n        }\n    }\n}\n\n/**\n * Delegates event to a selector.\n *\n * @param {Element|String|Array} [elements]\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @param {Boolean} useCapture\n * @return {Object}\n */\nfunction delegate(elements, selector, type, callback, useCapture) {\n    // Handle the regular Element usage\n    if (typeof elements.addEventListener === 'function') {\n        return _delegate.apply(null, arguments);\n    }\n\n    // Handle Element-less usage, it defaults to global delegation\n    if (typeof type === 'function') {\n        // Use `document` as the first parameter, then apply arguments\n        // This is a short way to .unshift `arguments` without running into deoptimizations\n        return _delegate.bind(null, document).apply(null, arguments);\n    }\n\n    // Handle Selector-based usage\n    if (typeof elements === 'string') {\n        elements = document.querySelectorAll(elements);\n    }\n\n    // Handle Array-like based usage\n    return Array.prototype.map.call(elements, function (element) {\n        return _delegate(element, selector, type, callback, useCapture);\n    });\n}\n\n/**\n * Finds closest match and invokes callback.\n *\n * @param {Element} element\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @return {Function}\n */\nfunction listener(element, selector, type, callback) {\n    return function(e) {\n        e.delegateTarget = closest(e.target, selector);\n\n        if (e.delegateTarget) {\n            callback.call(element, e);\n        }\n    }\n}\n\nmodule.exports = delegate;\n\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports) {\n\n/**\n * Check if argument is a HTML element.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.node = function(value) {\n    return value !== undefined\n        && value instanceof HTMLElement\n        && value.nodeType === 1;\n};\n\n/**\n * Check if argument is a list of HTML elements.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.nodeList = function(value) {\n    var type = Object.prototype.toString.call(value);\n\n    return value !== undefined\n        && (type === '[object NodeList]' || type === '[object HTMLCollection]')\n        && ('length' in value)\n        && (value.length === 0 || exports.node(value[0]));\n};\n\n/**\n * Check if argument is a string.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.string = function(value) {\n    return typeof value === 'string'\n        || value instanceof String;\n};\n\n/**\n * Check if argument is a function.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.fn = function(value) {\n    var type = Object.prototype.toString.call(value);\n\n    return type === '[object Function]';\n};\n\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports) {\n\nfunction select(element) {\n    var selectedText;\n\n    if (element.nodeName === 'SELECT') {\n        element.focus();\n\n        selectedText = element.value;\n    }\n    else if (element.nodeName === 'INPUT' || element.nodeName === 'TEXTAREA') {\n        var isReadOnly = element.hasAttribute('readonly');\n\n        if (!isReadOnly) {\n            element.setAttribute('readonly', '');\n        }\n\n        element.select();\n        element.setSelectionRange(0, element.value.length);\n\n        if (!isReadOnly) {\n            element.removeAttribute('readonly');\n        }\n\n        selectedText = element.value;\n    }\n    else {\n        if (element.hasAttribute('contenteditable')) {\n            element.focus();\n        }\n\n        var selection = window.getSelection();\n        var range = document.createRange();\n\n        range.selectNodeContents(element);\n        selection.removeAllRanges();\n        selection.addRange(range);\n\n        selectedText = selection.toString();\n    }\n\n    return selectedText;\n}\n\nmodule.exports = select;\n\n\n/***/ })\n/******/ ]);\n});"], "mappings": ";;;;;AAAA;AAAA;AAMA,KAAC,SAAS,iCAAiC,MAAM,SAAS;AACzD,UAAG,OAAO,YAAY,YAAY,OAAO,WAAW;AACnD,eAAO,UAAU,QAAQ;AAAA,eAClB,OAAO,WAAW,cAAc,OAAO;AAC9C,eAAO,CAAC,GAAG,OAAO;AAAA,eACX,OAAO,YAAY;AAC1B,gBAAQ,aAAa,IAAI,QAAQ;AAAA;AAEjC,aAAK,aAAa,IAAI,QAAQ;AAAA,IAChC,GAAG,SAAM,WAAW;AACpB;AAAA;AAAA,SAAiB,SAAS,SAAS;AAEzB,cAAI,mBAAmB,CAAC;AAGxB,mBAAS,oBAAoB,UAAU;AAGtC,gBAAG,iBAAiB,QAAQ,GAAG;AAC9B,qBAAO,iBAAiB,QAAQ,EAAE;AAAA,YACnC;AAEA,gBAAIA,UAAS,iBAAiB,QAAQ,IAAI;AAAA;AAAA,cACzC,GAAG;AAAA;AAAA,cACH,GAAG;AAAA;AAAA,cACH,SAAS,CAAC;AAAA;AAAA,YACX;AAGA,oBAAQ,QAAQ,EAAE,KAAKA,QAAO,SAASA,SAAQA,QAAO,SAAS,mBAAmB;AAGlF,YAAAA,QAAO,IAAI;AAGX,mBAAOA,QAAO;AAAA,UACf;AAIA,8BAAoB,IAAI;AAGxB,8BAAoB,IAAI;AAGxB,8BAAoB,IAAI,SAAS,OAAO;AAAE,mBAAO;AAAA,UAAO;AAGxD,8BAAoB,IAAI,SAASC,UAAS,MAAM,QAAQ;AACvD,gBAAG,CAAC,oBAAoB,EAAEA,UAAS,IAAI,GAAG;AACzC,qBAAO,eAAeA,UAAS,MAAM;AAAA;AAAA,gBACpC,cAAc;AAAA;AAAA,gBACd,YAAY;AAAA;AAAA,gBACZ,KAAK;AAAA;AAAA,cACN,CAAC;AAAA,YACF;AAAA,UACD;AAGA,8BAAoB,IAAI,SAASD,SAAQ;AACxC,gBAAI,SAASA,WAAUA,QAAO;AAAA;AAAA,cAC7B,SAAS,aAAa;AAAE,uBAAOA,QAAO,SAAS;AAAA,cAAG;AAAA;AAAA;AAAA,cAClD,SAAS,mBAAmB;AAAE,uBAAOA;AAAA,cAAQ;AAAA;AAC9C,gCAAoB,EAAE,QAAQ,KAAK,MAAM;AACzC,mBAAO;AAAA,UACR;AAGA,8BAAoB,IAAI,SAAS,QAAQ,UAAU;AAAE,mBAAO,OAAO,UAAU,eAAe,KAAK,QAAQ,QAAQ;AAAA,UAAG;AAGpH,8BAAoB,IAAI;AAGxB,iBAAO,oBAAoB,oBAAoB,IAAI,CAAC;AAAA,QACrD,GAEC;AAAA;AAAA;AAAA,WAEH,SAASA,SAAQC,UAAS,qBAAqB;AAEtD,gBAAI,gCAAgC,8BAA8B;AAA8B,aAAC,SAAU,QAAQ,SAAS;AACxH,kBAAI,MAAM;AACN,kBAAE,+BAA+B,CAACD,SAAQ,oBAAoB,CAAC,CAAC,GAAG,iCAAkC,SACzG,gCAAiC,OAAO,mCAAmC,aAC1E,+BAA+B,MAAMC,UAAS,4BAA4B,IAAK,gCAChF,kCAAkC,WAAcD,QAAO,UAAU;AAAA,cACjE,WAAW,OAAOC,aAAY,aAAa;AACvC,wBAAQD,SAAQ,IAAiB;AAAA,cACrC,OAAO;AACH,oBAAI,MAAM;AAAA,kBACN,SAAS,CAAC;AAAA,gBACd;AACA,wBAAQ,KAAK,OAAO,MAAM;AAC1B,uBAAO,kBAAkB,IAAI;AAAA,cACjC;AAAA,YACJ,GAAG,MAAM,SAAUA,SAAQ,SAAS;AAChC;AAEA,kBAAI,WAAW,uBAAuB,OAAO;AAE7C,uBAAS,uBAAuB,KAAK;AACjC,uBAAO,OAAO,IAAI,aAAa,MAAM;AAAA,kBACjC,SAAS;AAAA,gBACb;AAAA,cACJ;AAEA,kBAAI,UAAU,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,WAAW,SAAU,KAAK;AAC/F,uBAAO,OAAO;AAAA,cAClB,IAAI,SAAU,KAAK;AACf,uBAAO,OAAO,OAAO,WAAW,cAAc,IAAI,gBAAgB,UAAU,QAAQ,OAAO,YAAY,WAAW,OAAO;AAAA,cAC7H;AAEA,uBAAS,gBAAgB,UAAU,aAAa;AAC5C,oBAAI,EAAE,oBAAoB,cAAc;AACpC,wBAAM,IAAI,UAAU,mCAAmC;AAAA,gBAC3D;AAAA,cACJ;AAEA,kBAAI,eAAe,4BAAY;AAC3B,yBAAS,iBAAiB,QAAQ,OAAO;AACrC,2BAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,wBAAI,aAAa,MAAM,CAAC;AACxB,+BAAW,aAAa,WAAW,cAAc;AACjD,+BAAW,eAAe;AAC1B,wBAAI,WAAW,WAAY,YAAW,WAAW;AACjD,2BAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,kBAC5D;AAAA,gBACJ;AAEA,uBAAO,SAAU,aAAa,YAAY,aAAa;AACnD,sBAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAClE,sBAAI,YAAa,kBAAiB,aAAa,WAAW;AAC1D,yBAAO;AAAA,gBACX;AAAA,cACJ,GAAE;AAEF,kBAAI,mBAAkB,WAAY;AAI9B,yBAASE,iBAAgB,SAAS;AAC9B,kCAAgB,MAAMA,gBAAe;AAErC,uBAAK,eAAe,OAAO;AAC3B,uBAAK,cAAc;AAAA,gBACvB;AAQA,6BAAaA,kBAAiB,CAAC;AAAA,kBAC3B,KAAK;AAAA,kBACL,OAAO,SAAS,iBAAiB;AAC7B,wBAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEnF,yBAAK,SAAS,QAAQ;AACtB,yBAAK,YAAY,QAAQ;AACzB,yBAAK,UAAU,QAAQ;AACvB,yBAAK,SAAS,QAAQ;AACtB,yBAAK,OAAO,QAAQ;AACpB,yBAAK,UAAU,QAAQ;AAEvB,yBAAK,eAAe;AAAA,kBACxB;AAAA,gBACJ,GAAG;AAAA,kBACC,KAAK;AAAA,kBACL,OAAO,SAAS,gBAAgB;AAC5B,wBAAI,KAAK,MAAM;AACX,2BAAK,WAAW;AAAA,oBACpB,WAAW,KAAK,QAAQ;AACpB,2BAAK,aAAa;AAAA,oBACtB;AAAA,kBACJ;AAAA,gBACJ,GAAG;AAAA,kBACC,KAAK;AAAA,kBACL,OAAO,SAAS,aAAa;AACzB,wBAAI,QAAQ;AAEZ,wBAAI,QAAQ,SAAS,gBAAgB,aAAa,KAAK,KAAK;AAE5D,yBAAK,WAAW;AAEhB,yBAAK,sBAAsB,WAAY;AACnC,6BAAO,MAAM,WAAW;AAAA,oBAC5B;AACA,yBAAK,cAAc,KAAK,UAAU,iBAAiB,SAAS,KAAK,mBAAmB,KAAK;AAEzF,yBAAK,WAAW,SAAS,cAAc,UAAU;AAEjD,yBAAK,SAAS,MAAM,WAAW;AAE/B,yBAAK,SAAS,MAAM,SAAS;AAC7B,yBAAK,SAAS,MAAM,UAAU;AAC9B,yBAAK,SAAS,MAAM,SAAS;AAE7B,yBAAK,SAAS,MAAM,WAAW;AAC/B,yBAAK,SAAS,MAAM,QAAQ,UAAU,MAAM,IAAI;AAEhD,wBAAI,YAAY,OAAO,eAAe,SAAS,gBAAgB;AAC/D,yBAAK,SAAS,MAAM,MAAM,YAAY;AAEtC,yBAAK,SAAS,aAAa,YAAY,EAAE;AACzC,yBAAK,SAAS,QAAQ,KAAK;AAE3B,yBAAK,UAAU,YAAY,KAAK,QAAQ;AAExC,yBAAK,gBAAgB,GAAG,SAAS,SAAS,KAAK,QAAQ;AACvD,yBAAK,SAAS;AAAA,kBAClB;AAAA,gBACJ,GAAG;AAAA,kBACC,KAAK;AAAA,kBACL,OAAO,SAAS,aAAa;AACzB,wBAAI,KAAK,aAAa;AAClB,2BAAK,UAAU,oBAAoB,SAAS,KAAK,mBAAmB;AACpE,2BAAK,cAAc;AACnB,2BAAK,sBAAsB;AAAA,oBAC/B;AAEA,wBAAI,KAAK,UAAU;AACf,2BAAK,UAAU,YAAY,KAAK,QAAQ;AACxC,2BAAK,WAAW;AAAA,oBACpB;AAAA,kBACJ;AAAA,gBACJ,GAAG;AAAA,kBACC,KAAK;AAAA,kBACL,OAAO,SAAS,eAAe;AAC3B,yBAAK,gBAAgB,GAAG,SAAS,SAAS,KAAK,MAAM;AACrD,yBAAK,SAAS;AAAA,kBAClB;AAAA,gBACJ,GAAG;AAAA,kBACC,KAAK;AAAA,kBACL,OAAO,SAAS,WAAW;AACvB,wBAAI,YAAY;AAEhB,wBAAI;AACA,kCAAY,SAAS,YAAY,KAAK,MAAM;AAAA,oBAChD,SAAS,KAAK;AACV,kCAAY;AAAA,oBAChB;AAEA,yBAAK,aAAa,SAAS;AAAA,kBAC/B;AAAA,gBACJ,GAAG;AAAA,kBACC,KAAK;AAAA,kBACL,OAAO,SAAS,aAAa,WAAW;AACpC,yBAAK,QAAQ,KAAK,YAAY,YAAY,SAAS;AAAA,sBAC/C,QAAQ,KAAK;AAAA,sBACb,MAAM,KAAK;AAAA,sBACX,SAAS,KAAK;AAAA,sBACd,gBAAgB,KAAK,eAAe,KAAK,IAAI;AAAA,oBACjD,CAAC;AAAA,kBACL;AAAA,gBACJ,GAAG;AAAA,kBACC,KAAK;AAAA,kBACL,OAAO,SAAS,iBAAiB;AAC7B,wBAAI,KAAK,SAAS;AACd,2BAAK,QAAQ,MAAM;AAAA,oBACvB;AAEA,2BAAO,aAAa,EAAE,gBAAgB;AAAA,kBAC1C;AAAA,gBACJ,GAAG;AAAA,kBACC,KAAK;AAAA,kBACL,OAAO,SAAS,UAAU;AACtB,yBAAK,WAAW;AAAA,kBACpB;AAAA,gBACJ,GAAG;AAAA,kBACC,KAAK;AAAA,kBACL,KAAK,SAAS,MAAM;AAChB,wBAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAEjF,yBAAK,UAAU;AAEf,wBAAI,KAAK,YAAY,UAAU,KAAK,YAAY,OAAO;AACnD,4BAAM,IAAI,MAAM,oDAAoD;AAAA,oBACxE;AAAA,kBACJ;AAAA,kBACA,KAAK,SAAS,MAAM;AAChB,2BAAO,KAAK;AAAA,kBAChB;AAAA,gBACJ,GAAG;AAAA,kBACC,KAAK;AAAA,kBACL,KAAK,SAAS,IAAI,QAAQ;AACtB,wBAAI,WAAW,QAAW;AACtB,0BAAI,WAAW,OAAO,WAAW,cAAc,cAAc,QAAQ,MAAM,OAAO,YAAY,OAAO,aAAa,GAAG;AACjH,4BAAI,KAAK,WAAW,UAAU,OAAO,aAAa,UAAU,GAAG;AAC3D,gCAAM,IAAI,MAAM,mFAAmF;AAAA,wBACvG;AAEA,4BAAI,KAAK,WAAW,UAAU,OAAO,aAAa,UAAU,KAAK,OAAO,aAAa,UAAU,IAAI;AAC/F,gCAAM,IAAI,MAAM,uGAAwG;AAAA,wBAC5H;AAEA,6BAAK,UAAU;AAAA,sBACnB,OAAO;AACH,8BAAM,IAAI,MAAM,6CAA6C;AAAA,sBACjE;AAAA,oBACJ;AAAA,kBACJ;AAAA,kBACA,KAAK,SAAS,MAAM;AAChB,2BAAO,KAAK;AAAA,kBAChB;AAAA,gBACJ,CAAC,CAAC;AAEF,uBAAOA;AAAA,cACX,GAAE;AAEF,cAAAF,QAAO,UAAU;AAAA,YACrB,CAAC;AAAA,UAEK;AAAA;AAAA;AAAA,WAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD,gBAAI,KAAK,oBAAoB,CAAC;AAC9B,gBAAI,WAAW,oBAAoB,CAAC;AAWpC,qBAAS,OAAO,QAAQ,MAAM,UAAU;AACpC,kBAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU;AAC/B,sBAAM,IAAI,MAAM,4BAA4B;AAAA,cAChD;AAEA,kBAAI,CAAC,GAAG,OAAO,IAAI,GAAG;AAClB,sBAAM,IAAI,UAAU,kCAAkC;AAAA,cAC1D;AAEA,kBAAI,CAAC,GAAG,GAAG,QAAQ,GAAG;AAClB,sBAAM,IAAI,UAAU,mCAAmC;AAAA,cAC3D;AAEA,kBAAI,GAAG,KAAK,MAAM,GAAG;AACjB,uBAAO,WAAW,QAAQ,MAAM,QAAQ;AAAA,cAC5C,WACS,GAAG,SAAS,MAAM,GAAG;AAC1B,uBAAO,eAAe,QAAQ,MAAM,QAAQ;AAAA,cAChD,WACS,GAAG,OAAO,MAAM,GAAG;AACxB,uBAAO,eAAe,QAAQ,MAAM,QAAQ;AAAA,cAChD,OACK;AACD,sBAAM,IAAI,UAAU,2EAA2E;AAAA,cACnG;AAAA,YACJ;AAWA,qBAAS,WAAW,MAAM,MAAM,UAAU;AACtC,mBAAK,iBAAiB,MAAM,QAAQ;AAEpC,qBAAO;AAAA,gBACH,SAAS,WAAW;AAChB,uBAAK,oBAAoB,MAAM,QAAQ;AAAA,gBAC3C;AAAA,cACJ;AAAA,YACJ;AAWA,qBAAS,eAAe,UAAU,MAAM,UAAU;AAC9C,oBAAM,UAAU,QAAQ,KAAK,UAAU,SAAS,MAAM;AAClD,qBAAK,iBAAiB,MAAM,QAAQ;AAAA,cACxC,CAAC;AAED,qBAAO;AAAA,gBACH,SAAS,WAAW;AAChB,wBAAM,UAAU,QAAQ,KAAK,UAAU,SAAS,MAAM;AAClD,yBAAK,oBAAoB,MAAM,QAAQ;AAAA,kBAC3C,CAAC;AAAA,gBACL;AAAA,cACJ;AAAA,YACJ;AAWA,qBAAS,eAAe,UAAU,MAAM,UAAU;AAC9C,qBAAO,SAAS,SAAS,MAAM,UAAU,MAAM,QAAQ;AAAA,YAC3D;AAEA,YAAAD,QAAO,UAAU;AAAA,UAGX;AAAA;AAAA;AAAA,WAEC,SAASA,SAAQC,UAAS;AAEjC,qBAAS,IAAK;AAAA,YAGd;AAEA,cAAE,YAAY;AAAA,cACZ,IAAI,SAAU,MAAM,UAAU,KAAK;AACjC,oBAAI,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC;AAE7B,iBAAC,EAAE,IAAI,MAAM,EAAE,IAAI,IAAI,CAAC,IAAI,KAAK;AAAA,kBAC/B,IAAI;AAAA,kBACJ;AAAA,gBACF,CAAC;AAED,uBAAO;AAAA,cACT;AAAA,cAEA,MAAM,SAAU,MAAM,UAAU,KAAK;AACnC,oBAAI,OAAO;AACX,yBAAS,WAAY;AACnB,uBAAK,IAAI,MAAM,QAAQ;AACvB,2BAAS,MAAM,KAAK,SAAS;AAAA,gBAC/B;AAAC;AAED,yBAAS,IAAI;AACb,uBAAO,KAAK,GAAG,MAAM,UAAU,GAAG;AAAA,cACpC;AAAA,cAEA,MAAM,SAAU,MAAM;AACpB,oBAAI,OAAO,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC;AACrC,oBAAI,WAAW,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,MAAM;AAC3D,oBAAI,IAAI;AACR,oBAAI,MAAM,OAAO;AAEjB,qBAAK,GAAG,IAAI,KAAK,KAAK;AACpB,yBAAO,CAAC,EAAE,GAAG,MAAM,OAAO,CAAC,EAAE,KAAK,IAAI;AAAA,gBACxC;AAEA,uBAAO;AAAA,cACT;AAAA,cAEA,KAAK,SAAU,MAAM,UAAU;AAC7B,oBAAI,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC;AAC7B,oBAAI,OAAO,EAAE,IAAI;AACjB,oBAAI,aAAa,CAAC;AAElB,oBAAI,QAAQ,UAAU;AACpB,2BAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;AAC/C,wBAAI,KAAK,CAAC,EAAE,OAAO,YAAY,KAAK,CAAC,EAAE,GAAG,MAAM;AAC9C,iCAAW,KAAK,KAAK,CAAC,CAAC;AAAA,kBAC3B;AAAA,gBACF;AAMA,gBAAC,WAAW,SACR,EAAE,IAAI,IAAI,aACV,OAAO,EAAE,IAAI;AAEjB,uBAAO;AAAA,cACT;AAAA,YACF;AAEA,YAAAD,QAAO,UAAU;AAAA,UAGX;AAAA;AAAA;AAAA,WAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD,gBAAI,gCAAgC,8BAA8B;AAA8B,aAAC,SAAU,QAAQ,SAAS;AACxH,kBAAI,MAAM;AACN,kBAAE,+BAA+B,CAACD,SAAQ,oBAAoB,CAAC,GAAG,oBAAoB,CAAC,GAAG,oBAAoB,CAAC,CAAC,GAAG,iCAAkC,SACzJ,gCAAiC,OAAO,mCAAmC,aAC1E,+BAA+B,MAAMC,UAAS,4BAA4B,IAAK,gCAChF,kCAAkC,WAAcD,QAAO,UAAU;AAAA,cACjE,WAAW,OAAOC,aAAY,aAAa;AACvC,wBAAQD,SAAQ,MAA+B,MAAyB,IAAwB;AAAA,cACpG,OAAO;AACH,oBAAI,MAAM;AAAA,kBACN,SAAS,CAAC;AAAA,gBACd;AACA,wBAAQ,KAAK,OAAO,iBAAiB,OAAO,aAAa,OAAO,YAAY;AAC5E,uBAAO,YAAY,IAAI;AAAA,cAC3B;AAAA,YACJ,GAAG,MAAM,SAAUA,SAAQ,kBAAkB,cAAc,eAAe;AACtE;AAEA,kBAAI,oBAAoB,uBAAuB,gBAAgB;AAE/D,kBAAI,gBAAgB,uBAAuB,YAAY;AAEvD,kBAAI,iBAAiB,uBAAuB,aAAa;AAEzD,uBAAS,uBAAuB,KAAK;AACjC,uBAAO,OAAO,IAAI,aAAa,MAAM;AAAA,kBACjC,SAAS;AAAA,gBACb;AAAA,cACJ;AAEA,kBAAI,UAAU,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,WAAW,SAAU,KAAK;AAC/F,uBAAO,OAAO;AAAA,cAClB,IAAI,SAAU,KAAK;AACf,uBAAO,OAAO,OAAO,WAAW,cAAc,IAAI,gBAAgB,UAAU,QAAQ,OAAO,YAAY,WAAW,OAAO;AAAA,cAC7H;AAEA,uBAAS,gBAAgB,UAAU,aAAa;AAC5C,oBAAI,EAAE,oBAAoB,cAAc;AACpC,wBAAM,IAAI,UAAU,mCAAmC;AAAA,gBAC3D;AAAA,cACJ;AAEA,kBAAI,eAAe,4BAAY;AAC3B,yBAAS,iBAAiB,QAAQ,OAAO;AACrC,2BAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,wBAAI,aAAa,MAAM,CAAC;AACxB,+BAAW,aAAa,WAAW,cAAc;AACjD,+BAAW,eAAe;AAC1B,wBAAI,WAAW,WAAY,YAAW,WAAW;AACjD,2BAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,kBAC5D;AAAA,gBACJ;AAEA,uBAAO,SAAU,aAAa,YAAY,aAAa;AACnD,sBAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAClE,sBAAI,YAAa,kBAAiB,aAAa,WAAW;AAC1D,yBAAO;AAAA,gBACX;AAAA,cACJ,GAAE;AAEF,uBAAS,2BAA2B,MAAM,MAAM;AAC5C,oBAAI,CAAC,MAAM;AACP,wBAAM,IAAI,eAAe,2DAA2D;AAAA,gBACxF;AAEA,uBAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,cACrF;AAEA,uBAAS,UAAU,UAAU,YAAY;AACrC,oBAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AACzD,wBAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,gBACtG;AAEA,yBAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW;AAAA,kBACnE,aAAa;AAAA,oBACT,OAAO;AAAA,oBACP,YAAY;AAAA,oBACZ,UAAU;AAAA,oBACV,cAAc;AAAA,kBAClB;AAAA,gBACJ,CAAC;AACD,oBAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,cAC/G;AAEA,kBAAI,aAAY,SAAU,UAAU;AAChC,0BAAUG,YAAW,QAAQ;AAM7B,yBAASA,WAAU,SAAS,SAAS;AACjC,kCAAgB,MAAMA,UAAS;AAE/B,sBAAI,QAAQ,2BAA2B,OAAOA,WAAU,aAAa,OAAO,eAAeA,UAAS,GAAG,KAAK,IAAI,CAAC;AAEjH,wBAAM,eAAe,OAAO;AAC5B,wBAAM,YAAY,OAAO;AACzB,yBAAO;AAAA,gBACX;AASA,6BAAaA,YAAW,CAAC;AAAA,kBACrB,KAAK;AAAA,kBACL,OAAO,SAAS,iBAAiB;AAC7B,wBAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEnF,yBAAK,SAAS,OAAO,QAAQ,WAAW,aAAa,QAAQ,SAAS,KAAK;AAC3E,yBAAK,SAAS,OAAO,QAAQ,WAAW,aAAa,QAAQ,SAAS,KAAK;AAC3E,yBAAK,OAAO,OAAO,QAAQ,SAAS,aAAa,QAAQ,OAAO,KAAK;AACrE,yBAAK,YAAY,QAAQ,QAAQ,SAAS,MAAM,WAAW,QAAQ,YAAY,SAAS;AAAA,kBAC5F;AAAA,gBACJ,GAAG;AAAA,kBACC,KAAK;AAAA,kBACL,OAAO,SAAS,YAAY,SAAS;AACjC,wBAAI,SAAS;AAEb,yBAAK,YAAY,GAAG,eAAe,SAAS,SAAS,SAAS,SAAU,GAAG;AACvE,6BAAO,OAAO,QAAQ,CAAC;AAAA,oBAC3B,CAAC;AAAA,kBACL;AAAA,gBACJ,GAAG;AAAA,kBACC,KAAK;AAAA,kBACL,OAAO,SAAS,QAAQ,GAAG;AACvB,wBAAI,UAAU,EAAE,kBAAkB,EAAE;AAEpC,wBAAI,KAAK,iBAAiB;AACtB,2BAAK,kBAAkB;AAAA,oBAC3B;AAEA,yBAAK,kBAAkB,IAAI,kBAAkB,QAAQ;AAAA,sBACjD,QAAQ,KAAK,OAAO,OAAO;AAAA,sBAC3B,QAAQ,KAAK,OAAO,OAAO;AAAA,sBAC3B,MAAM,KAAK,KAAK,OAAO;AAAA,sBACvB,WAAW,KAAK;AAAA,sBAChB;AAAA,sBACA,SAAS;AAAA,oBACb,CAAC;AAAA,kBACL;AAAA,gBACJ,GAAG;AAAA,kBACC,KAAK;AAAA,kBACL,OAAO,SAAS,cAAc,SAAS;AACnC,2BAAO,kBAAkB,UAAU,OAAO;AAAA,kBAC9C;AAAA,gBACJ,GAAG;AAAA,kBACC,KAAK;AAAA,kBACL,OAAO,SAAS,cAAc,SAAS;AACnC,wBAAI,WAAW,kBAAkB,UAAU,OAAO;AAElD,wBAAI,UAAU;AACV,6BAAO,SAAS,cAAc,QAAQ;AAAA,oBAC1C;AAAA,kBACJ;AAAA,gBACJ,GAAG;AAAA,kBACC,KAAK;AAAA,kBACL,OAAO,SAAS,YAAY,SAAS;AACjC,2BAAO,kBAAkB,QAAQ,OAAO;AAAA,kBAC5C;AAAA,gBACJ,GAAG;AAAA,kBACC,KAAK;AAAA,kBACL,OAAO,SAAS,UAAU;AACtB,yBAAK,SAAS,QAAQ;AAEtB,wBAAI,KAAK,iBAAiB;AACtB,2BAAK,gBAAgB,QAAQ;AAC7B,2BAAK,kBAAkB;AAAA,oBAC3B;AAAA,kBACJ;AAAA,gBACJ,CAAC,GAAG,CAAC;AAAA,kBACD,KAAK;AAAA,kBACL,OAAO,SAAS,cAAc;AAC1B,wBAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC,QAAQ,KAAK;AAE/F,wBAAI,UAAU,OAAO,WAAW,WAAW,CAAC,MAAM,IAAI;AACtD,wBAAI,UAAU,CAAC,CAAC,SAAS;AAEzB,4BAAQ,QAAQ,SAAUC,SAAQ;AAC9B,gCAAU,WAAW,CAAC,CAAC,SAAS,sBAAsBA,OAAM;AAAA,oBAChE,CAAC;AAED,2BAAO;AAAA,kBACX;AAAA,gBACJ,CAAC,CAAC;AAEF,uBAAOD;AAAA,cACX,GAAE,cAAc,OAAO;AAOvB,uBAAS,kBAAkB,QAAQ,SAAS;AACxC,oBAAI,YAAY,oBAAoB;AAEpC,oBAAI,CAAC,QAAQ,aAAa,SAAS,GAAG;AAClC;AAAA,gBACJ;AAEA,uBAAO,QAAQ,aAAa,SAAS;AAAA,cACzC;AAEA,cAAAH,QAAO,UAAU;AAAA,YACrB,CAAC;AAAA,UAEK;AAAA;AAAA;AAAA,WAEC,SAASA,SAAQC,UAAS;AAEjC,gBAAI,qBAAqB;AAKzB,gBAAI,OAAO,YAAY,eAAe,CAAC,QAAQ,UAAU,SAAS;AAC9D,kBAAI,QAAQ,QAAQ;AAEpB,oBAAM,UAAU,MAAM,mBACN,MAAM,sBACN,MAAM,qBACN,MAAM,oBACN,MAAM;AAAA,YAC1B;AASA,qBAAS,QAAS,SAAS,UAAU;AACjC,qBAAO,WAAW,QAAQ,aAAa,oBAAoB;AACvD,oBAAI,OAAO,QAAQ,YAAY,cAC3B,QAAQ,QAAQ,QAAQ,GAAG;AAC7B,yBAAO;AAAA,gBACT;AACA,0BAAU,QAAQ;AAAA,cACtB;AAAA,YACJ;AAEA,YAAAD,QAAO,UAAU;AAAA,UAGX;AAAA;AAAA;AAAA,WAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD,gBAAI,UAAU,oBAAoB,CAAC;AAYnC,qBAAS,UAAU,SAAS,UAAU,MAAM,UAAU,YAAY;AAC9D,kBAAI,aAAa,SAAS,MAAM,MAAM,SAAS;AAE/C,sBAAQ,iBAAiB,MAAM,YAAY,UAAU;AAErD,qBAAO;AAAA,gBACH,SAAS,WAAW;AAChB,0BAAQ,oBAAoB,MAAM,YAAY,UAAU;AAAA,gBAC5D;AAAA,cACJ;AAAA,YACJ;AAYA,qBAAS,SAAS,UAAU,UAAU,MAAM,UAAU,YAAY;AAE9D,kBAAI,OAAO,SAAS,qBAAqB,YAAY;AACjD,uBAAO,UAAU,MAAM,MAAM,SAAS;AAAA,cAC1C;AAGA,kBAAI,OAAO,SAAS,YAAY;AAG5B,uBAAO,UAAU,KAAK,MAAM,QAAQ,EAAE,MAAM,MAAM,SAAS;AAAA,cAC/D;AAGA,kBAAI,OAAO,aAAa,UAAU;AAC9B,2BAAW,SAAS,iBAAiB,QAAQ;AAAA,cACjD;AAGA,qBAAO,MAAM,UAAU,IAAI,KAAK,UAAU,SAAU,SAAS;AACzD,uBAAO,UAAU,SAAS,UAAU,MAAM,UAAU,UAAU;AAAA,cAClE,CAAC;AAAA,YACL;AAWA,qBAAS,SAAS,SAAS,UAAU,MAAM,UAAU;AACjD,qBAAO,SAAS,GAAG;AACf,kBAAE,iBAAiB,QAAQ,EAAE,QAAQ,QAAQ;AAE7C,oBAAI,EAAE,gBAAgB;AAClB,2BAAS,KAAK,SAAS,CAAC;AAAA,gBAC5B;AAAA,cACJ;AAAA,YACJ;AAEA,YAAAD,QAAO,UAAU;AAAA,UAGX;AAAA;AAAA;AAAA,WAEC,SAASA,SAAQC,UAAS;AAQjC,YAAAA,SAAQ,OAAO,SAAS,OAAO;AAC3B,qBAAO,UAAU,UACV,iBAAiB,eACjB,MAAM,aAAa;AAAA,YAC9B;AAQA,YAAAA,SAAQ,WAAW,SAAS,OAAO;AAC/B,kBAAI,OAAO,OAAO,UAAU,SAAS,KAAK,KAAK;AAE/C,qBAAO,UAAU,WACT,SAAS,uBAAuB,SAAS,8BACzC,YAAY,UACZ,MAAM,WAAW,KAAKA,SAAQ,KAAK,MAAM,CAAC,CAAC;AAAA,YACvD;AAQA,YAAAA,SAAQ,SAAS,SAAS,OAAO;AAC7B,qBAAO,OAAO,UAAU,YACjB,iBAAiB;AAAA,YAC5B;AAQA,YAAAA,SAAQ,KAAK,SAAS,OAAO;AACzB,kBAAI,OAAO,OAAO,UAAU,SAAS,KAAK,KAAK;AAE/C,qBAAO,SAAS;AAAA,YACpB;AAAA,UAGM;AAAA;AAAA;AAAA,WAEC,SAASD,SAAQC,UAAS;AAEjC,qBAAS,OAAO,SAAS;AACrB,kBAAI;AAEJ,kBAAI,QAAQ,aAAa,UAAU;AAC/B,wBAAQ,MAAM;AAEd,+BAAe,QAAQ;AAAA,cAC3B,WACS,QAAQ,aAAa,WAAW,QAAQ,aAAa,YAAY;AACtE,oBAAI,aAAa,QAAQ,aAAa,UAAU;AAEhD,oBAAI,CAAC,YAAY;AACb,0BAAQ,aAAa,YAAY,EAAE;AAAA,gBACvC;AAEA,wBAAQ,OAAO;AACf,wBAAQ,kBAAkB,GAAG,QAAQ,MAAM,MAAM;AAEjD,oBAAI,CAAC,YAAY;AACb,0BAAQ,gBAAgB,UAAU;AAAA,gBACtC;AAEA,+BAAe,QAAQ;AAAA,cAC3B,OACK;AACD,oBAAI,QAAQ,aAAa,iBAAiB,GAAG;AACzC,0BAAQ,MAAM;AAAA,gBAClB;AAEA,oBAAI,YAAY,OAAO,aAAa;AACpC,oBAAI,QAAQ,SAAS,YAAY;AAEjC,sBAAM,mBAAmB,OAAO;AAChC,0BAAU,gBAAgB;AAC1B,0BAAU,SAAS,KAAK;AAExB,+BAAe,UAAU,SAAS;AAAA,cACtC;AAEA,qBAAO;AAAA,YACX;AAEA,YAAAD,QAAO,UAAU;AAAA,UAGX;AAAA;AAAA,QACG,CAAC;AAAA;AAAA,IACV,CAAC;AAAA;AAAA;", "names": ["module", "exports", "ClipboardAction", "Clipboard", "action"]}
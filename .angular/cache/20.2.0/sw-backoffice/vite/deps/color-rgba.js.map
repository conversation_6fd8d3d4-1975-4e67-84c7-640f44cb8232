{"version": 3, "sources": ["../../../../../../node_modules/color-name/index.js", "../../../../../../node_modules/color-space/rgb.js", "../../../../../../node_modules/color-space/hsl.js", "../../../../../../node_modules/color-parse/index.mjs", "../../../../../../node_modules/color-rgba/index.mjs"], "sourcesContent": ["'use strict'\r\n\r\nmodule.exports = {\r\n\t\"aliceblue\": [240, 248, 255],\r\n\t\"antiquewhite\": [250, 235, 215],\r\n\t\"aqua\": [0, 255, 255],\r\n\t\"aquamarine\": [127, 255, 212],\r\n\t\"azure\": [240, 255, 255],\r\n\t\"beige\": [245, 245, 220],\r\n\t\"bisque\": [255, 228, 196],\r\n\t\"black\": [0, 0, 0],\r\n\t\"blanchedalmond\": [255, 235, 205],\r\n\t\"blue\": [0, 0, 255],\r\n\t\"blueviolet\": [138, 43, 226],\r\n\t\"brown\": [165, 42, 42],\r\n\t\"burlywood\": [222, 184, 135],\r\n\t\"cadetblue\": [95, 158, 160],\r\n\t\"chartreuse\": [127, 255, 0],\r\n\t\"chocolate\": [210, 105, 30],\r\n\t\"coral\": [255, 127, 80],\r\n\t\"cornflowerblue\": [100, 149, 237],\r\n\t\"cornsilk\": [255, 248, 220],\r\n\t\"crimson\": [220, 20, 60],\r\n\t\"cyan\": [0, 255, 255],\r\n\t\"darkblue\": [0, 0, 139],\r\n\t\"darkcyan\": [0, 139, 139],\r\n\t\"darkgoldenrod\": [184, 134, 11],\r\n\t\"darkgray\": [169, 169, 169],\r\n\t\"darkgreen\": [0, 100, 0],\r\n\t\"darkgrey\": [169, 169, 169],\r\n\t\"darkkhaki\": [189, 183, 107],\r\n\t\"darkmagenta\": [139, 0, 139],\r\n\t\"darkolivegreen\": [85, 107, 47],\r\n\t\"darkorange\": [255, 140, 0],\r\n\t\"darkorchid\": [153, 50, 204],\r\n\t\"darkred\": [139, 0, 0],\r\n\t\"darksalmon\": [233, 150, 122],\r\n\t\"darkseagreen\": [143, 188, 143],\r\n\t\"darkslateblue\": [72, 61, 139],\r\n\t\"darkslategray\": [47, 79, 79],\r\n\t\"darkslategrey\": [47, 79, 79],\r\n\t\"darkturquoise\": [0, 206, 209],\r\n\t\"darkviolet\": [148, 0, 211],\r\n\t\"deeppink\": [255, 20, 147],\r\n\t\"deepskyblue\": [0, 191, 255],\r\n\t\"dimgray\": [105, 105, 105],\r\n\t\"dimgrey\": [105, 105, 105],\r\n\t\"dodgerblue\": [30, 144, 255],\r\n\t\"firebrick\": [178, 34, 34],\r\n\t\"floralwhite\": [255, 250, 240],\r\n\t\"forestgreen\": [34, 139, 34],\r\n\t\"fuchsia\": [255, 0, 255],\r\n\t\"gainsboro\": [220, 220, 220],\r\n\t\"ghostwhite\": [248, 248, 255],\r\n\t\"gold\": [255, 215, 0],\r\n\t\"goldenrod\": [218, 165, 32],\r\n\t\"gray\": [128, 128, 128],\r\n\t\"green\": [0, 128, 0],\r\n\t\"greenyellow\": [173, 255, 47],\r\n\t\"grey\": [128, 128, 128],\r\n\t\"honeydew\": [240, 255, 240],\r\n\t\"hotpink\": [255, 105, 180],\r\n\t\"indianred\": [205, 92, 92],\r\n\t\"indigo\": [75, 0, 130],\r\n\t\"ivory\": [255, 255, 240],\r\n\t\"khaki\": [240, 230, 140],\r\n\t\"lavender\": [230, 230, 250],\r\n\t\"lavenderblush\": [255, 240, 245],\r\n\t\"lawngreen\": [124, 252, 0],\r\n\t\"lemonchiffon\": [255, 250, 205],\r\n\t\"lightblue\": [173, 216, 230],\r\n\t\"lightcoral\": [240, 128, 128],\r\n\t\"lightcyan\": [224, 255, 255],\r\n\t\"lightgoldenrodyellow\": [250, 250, 210],\r\n\t\"lightgray\": [211, 211, 211],\r\n\t\"lightgreen\": [144, 238, 144],\r\n\t\"lightgrey\": [211, 211, 211],\r\n\t\"lightpink\": [255, 182, 193],\r\n\t\"lightsalmon\": [255, 160, 122],\r\n\t\"lightseagreen\": [32, 178, 170],\r\n\t\"lightskyblue\": [135, 206, 250],\r\n\t\"lightslategray\": [119, 136, 153],\r\n\t\"lightslategrey\": [119, 136, 153],\r\n\t\"lightsteelblue\": [176, 196, 222],\r\n\t\"lightyellow\": [255, 255, 224],\r\n\t\"lime\": [0, 255, 0],\r\n\t\"limegreen\": [50, 205, 50],\r\n\t\"linen\": [250, 240, 230],\r\n\t\"magenta\": [255, 0, 255],\r\n\t\"maroon\": [128, 0, 0],\r\n\t\"mediumaquamarine\": [102, 205, 170],\r\n\t\"mediumblue\": [0, 0, 205],\r\n\t\"mediumorchid\": [186, 85, 211],\r\n\t\"mediumpurple\": [147, 112, 219],\r\n\t\"mediumseagreen\": [60, 179, 113],\r\n\t\"mediumslateblue\": [123, 104, 238],\r\n\t\"mediumspringgreen\": [0, 250, 154],\r\n\t\"mediumturquoise\": [72, 209, 204],\r\n\t\"mediumvioletred\": [199, 21, 133],\r\n\t\"midnightblue\": [25, 25, 112],\r\n\t\"mintcream\": [245, 255, 250],\r\n\t\"mistyrose\": [255, 228, 225],\r\n\t\"moccasin\": [255, 228, 181],\r\n\t\"navajowhite\": [255, 222, 173],\r\n\t\"navy\": [0, 0, 128],\r\n\t\"oldlace\": [253, 245, 230],\r\n\t\"olive\": [128, 128, 0],\r\n\t\"olivedrab\": [107, 142, 35],\r\n\t\"orange\": [255, 165, 0],\r\n\t\"orangered\": [255, 69, 0],\r\n\t\"orchid\": [218, 112, 214],\r\n\t\"palegoldenrod\": [238, 232, 170],\r\n\t\"palegreen\": [152, 251, 152],\r\n\t\"paleturquoise\": [175, 238, 238],\r\n\t\"palevioletred\": [219, 112, 147],\r\n\t\"papayawhip\": [255, 239, 213],\r\n\t\"peachpuff\": [255, 218, 185],\r\n\t\"peru\": [205, 133, 63],\r\n\t\"pink\": [255, 192, 203],\r\n\t\"plum\": [221, 160, 221],\r\n\t\"powderblue\": [176, 224, 230],\r\n\t\"purple\": [128, 0, 128],\r\n\t\"rebeccapurple\": [102, 51, 153],\r\n\t\"red\": [255, 0, 0],\r\n\t\"rosybrown\": [188, 143, 143],\r\n\t\"royalblue\": [65, 105, 225],\r\n\t\"saddlebrown\": [139, 69, 19],\r\n\t\"salmon\": [250, 128, 114],\r\n\t\"sandybrown\": [244, 164, 96],\r\n\t\"seagreen\": [46, 139, 87],\r\n\t\"seashell\": [255, 245, 238],\r\n\t\"sienna\": [160, 82, 45],\r\n\t\"silver\": [192, 192, 192],\r\n\t\"skyblue\": [135, 206, 235],\r\n\t\"slateblue\": [106, 90, 205],\r\n\t\"slategray\": [112, 128, 144],\r\n\t\"slategrey\": [112, 128, 144],\r\n\t\"snow\": [255, 250, 250],\r\n\t\"springgreen\": [0, 255, 127],\r\n\t\"steelblue\": [70, 130, 180],\r\n\t\"tan\": [210, 180, 140],\r\n\t\"teal\": [0, 128, 128],\r\n\t\"thistle\": [216, 191, 216],\r\n\t\"tomato\": [255, 99, 71],\r\n\t\"turquoise\": [64, 224, 208],\r\n\t\"violet\": [238, 130, 238],\r\n\t\"wheat\": [245, 222, 179],\r\n\t\"white\": [255, 255, 255],\r\n\t\"whitesmoke\": [245, 245, 245],\r\n\t\"yellow\": [255, 255, 0],\r\n\t\"yellowgreen\": [154, 205, 50]\r\n};\r\n", "/**\n * RGB space.\n *\n * @module  color-space/rgb\n */\n'use strict'\n\nmodule.exports = {\n\tname: 'rgb',\n\tmin: [0,0,0],\n\tmax: [255,255,255],\n\tchannel: ['red', 'green', 'blue'],\n\talias: ['RGB']\n};\n", "/**\n * @module color-space/hsl\n */\n'use strict'\n\nvar rgb = require('./rgb');\n\nmodule.exports = {\n\tname: 'hsl',\n\tmin: [0,0,0],\n\tmax: [360,100,100],\n\tchannel: ['hue', 'saturation', 'lightness'],\n\talias: ['HSL'],\n\n\trgb: function(hsl) {\n\t\tvar h = hsl[0] / 360,\n\t\t\t\ts = hsl[1] / 100,\n\t\t\t\tl = hsl[2] / 100,\n\t\t\t\tt1, t2, t3, rgb, val;\n\n\t\tif (s === 0) {\n\t\t\tval = l * 255;\n\t\t\treturn [val, val, val];\n\t\t}\n\n\t\tif (l < 0.5) {\n\t\t\tt2 = l * (1 + s);\n\t\t}\n\t\telse {\n\t\t\tt2 = l + s - l * s;\n\t\t}\n\t\tt1 = 2 * l - t2;\n\n\t\trgb = [0, 0, 0];\n\t\tfor (var i = 0; i < 3; i++) {\n\t\t\tt3 = h + 1 / 3 * - (i - 1);\n\t\t\tif (t3 < 0) {\n\t\t\t\tt3++;\n\t\t\t}\n\t\t\telse if (t3 > 1) {\n\t\t\t\tt3--;\n\t\t\t}\n\n\t\t\tif (6 * t3 < 1) {\n\t\t\t\tval = t1 + (t2 - t1) * 6 * t3;\n\t\t\t}\n\t\t\telse if (2 * t3 < 1) {\n\t\t\t\tval = t2;\n\t\t\t}\n\t\t\telse if (3 * t3 < 2) {\n\t\t\t\tval = t1 + (t2 - t1) * (2 / 3 - t3) * 6;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tval = t1;\n\t\t\t}\n\n\t\t\trgb[i] = val * 255;\n\t\t}\n\n\t\treturn rgb;\n\t}\n};\n\n\n//extend rgb\nrgb.hsl = function(rgb) {\n\tvar r = rgb[0]/255,\n\t\t\tg = rgb[1]/255,\n\t\t\tb = rgb[2]/255,\n\t\t\tmin = Math.min(r, g, b),\n\t\t\tmax = Math.max(r, g, b),\n\t\t\tdelta = max - min,\n\t\t\th, s, l;\n\n\tif (max === min) {\n\t\th = 0;\n\t}\n\telse if (r === max) {\n\t\th = (g - b) / delta;\n\t}\n\telse if (g === max) {\n\t\th = 2 + (b - r) / delta;\n\t}\n\telse if (b === max) {\n\t\th = 4 + (r - g)/ delta;\n\t}\n\n\th = Math.min(h * 60, 360);\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tl = (min + max) / 2;\n\n\tif (max === min) {\n\t\ts = 0;\n\t}\n\telse if (l <= 0.5) {\n\t\ts = delta / (max + min);\n\t}\n\telse {\n\t\ts = delta / (2 - max - min);\n\t}\n\n\treturn [h, s * 100, l * 100];\n};\n", "/**\n * @module color-parse\n */\nimport names from 'color-name'\n\nexport default parse\n\n/**\n * Base hues\n * http://dev.w3.org/csswg/css-color/#typedef-named-hue\n */\n//FIXME: use external hue detector\nvar baseHues = {\n\tred: 0,\n\torange: 60,\n\tyellow: 120,\n\tgreen: 180,\n\tblue: 240,\n\tpurple: 300\n}\n\n/**\n * Parse color from the string passed\n *\n * @return {Object} A space indicator `space`, an array `values` and `alpha`\n */\nfunction parse(cstr) {\n\tvar m, parts = [], alpha = 1, space\n\n\tif (typeof cstr === 'string') {\n\t\tcstr = cstr.toLowerCase();\n\n\t\t//keyword\n\t\tif (names[cstr]) {\n\t\t\tparts = names[cstr].slice()\n\t\t\tspace = 'rgb'\n\t\t}\n\n\t\t//reserved words\n\t\telse if (cstr === 'transparent') {\n\t\t\talpha = 0\n\t\t\tspace = 'rgb'\n\t\t\tparts = [0, 0, 0]\n\t\t}\n\n\t\t//hex\n\t\telse if (/^#[A-Fa-f0-9]+$/.test(cstr)) {\n\t\t\tvar base = cstr.slice(1)\n\t\t\tvar size = base.length\n\t\t\tvar isShort = size <= 4\n\t\t\talpha = 1\n\n\t\t\tif (isShort) {\n\t\t\t\tparts = [\n\t\t\t\t\tparseInt(base[0] + base[0], 16),\n\t\t\t\t\tparseInt(base[1] + base[1], 16),\n\t\t\t\t\tparseInt(base[2] + base[2], 16)\n\t\t\t\t]\n\t\t\t\tif (size === 4) {\n\t\t\t\t\talpha = parseInt(base[3] + base[3], 16) / 255\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tparts = [\n\t\t\t\t\tparseInt(base[0] + base[1], 16),\n\t\t\t\t\tparseInt(base[2] + base[3], 16),\n\t\t\t\t\tparseInt(base[4] + base[5], 16)\n\t\t\t\t]\n\t\t\t\tif (size === 8) {\n\t\t\t\t\talpha = parseInt(base[6] + base[7], 16) / 255\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (!parts[0]) parts[0] = 0\n\t\t\tif (!parts[1]) parts[1] = 0\n\t\t\tif (!parts[2]) parts[2] = 0\n\n\t\t\tspace = 'rgb'\n\t\t}\n\n\t\t//color space\n\t\telse if (m = /^((?:rgb|hs[lvb]|hwb|cmyk?|xy[zy]|gray|lab|lchu?v?|[ly]uv|lms)a?)\\s*\\(([^\\)]*)\\)/.exec(cstr)) {\n\t\t\tvar name = m[1]\n\t\t\tvar isRGB = name === 'rgb'\n\t\t\tvar base = name.replace(/a$/, '')\n\t\t\tspace = base\n\t\t\tvar size = base === 'cmyk' ? 4 : base === 'gray' ? 1 : 3\n\t\t\tparts = m[2].trim()\n\t\t\t\t.split(/\\s*[,\\/]\\s*|\\s+/)\n\t\t\t\t.map(function (x, i) {\n\t\t\t\t\t//<percentage>\n\t\t\t\t\tif (/%$/.test(x)) {\n\t\t\t\t\t\t//alpha\n\t\t\t\t\t\tif (i === size) return parseFloat(x) / 100\n\t\t\t\t\t\t//rgb\n\t\t\t\t\t\tif (base === 'rgb') return parseFloat(x) * 255 / 100\n\t\t\t\t\t\treturn parseFloat(x)\n\t\t\t\t\t}\n\t\t\t\t\t//hue\n\t\t\t\t\telse if (base[i] === 'h') {\n\t\t\t\t\t\t//<deg>\n\t\t\t\t\t\tif (/deg$/.test(x)) {\n\t\t\t\t\t\t\treturn parseFloat(x)\n\t\t\t\t\t\t}\n\t\t\t\t\t\t//<base-hue>\n\t\t\t\t\t\telse if (baseHues[x] !== undefined) {\n\t\t\t\t\t\t\treturn baseHues[x]\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn parseFloat(x)\n\t\t\t\t})\n\n\t\t\tif (name === base) parts.push(1)\n\t\t\talpha = (isRGB) ? 1 : (parts[size] === undefined) ? 1 : parts[size]\n\t\t\tparts = parts.slice(0, size)\n\t\t}\n\n\t\t//named channels case\n\t\telse if (cstr.length > 10 && /[0-9](?:\\s|\\/)/.test(cstr)) {\n\t\t\tparts = cstr.match(/([0-9]+)/g).map(function (value) {\n\t\t\t\treturn parseFloat(value)\n\t\t\t})\n\n\t\t\tspace = cstr.match(/([a-z])/ig).join('').toLowerCase()\n\t\t}\n\t}\n\n\t//numeric case\n\telse if (!isNaN(cstr)) {\n\t\tspace = 'rgb'\n\t\tparts = [cstr >>> 16, (cstr & 0x00ff00) >>> 8, cstr & 0x0000ff]\n\t}\n\n\t//array-like\n\telse if (Array.isArray(cstr) || cstr.length) {\n\t\tparts = [cstr[0], cstr[1], cstr[2]]\n\t\tspace = 'rgb'\n\t\talpha = cstr.length === 4 ? cstr[3] : 1\n\t}\n\n\t//object case - detects css cases of rgb and hsl\n\telse if (cstr instanceof Object) {\n\t\tif (cstr.r != null || cstr.red != null || cstr.R != null) {\n\t\t\tspace = 'rgb'\n\t\t\tparts = [\n\t\t\t\tcstr.r || cstr.red || cstr.R || 0,\n\t\t\t\tcstr.g || cstr.green || cstr.G || 0,\n\t\t\t\tcstr.b || cstr.blue || cstr.B || 0\n\t\t\t]\n\t\t}\n\t\telse {\n\t\t\tspace = 'hsl'\n\t\t\tparts = [\n\t\t\t\tcstr.h || cstr.hue || cstr.H || 0,\n\t\t\t\tcstr.s || cstr.saturation || cstr.S || 0,\n\t\t\t\tcstr.l || cstr.lightness || cstr.L || cstr.b || cstr.brightness\n\t\t\t]\n\t\t}\n\n\t\talpha = cstr.a || cstr.alpha || cstr.opacity || 1\n\n\t\tif (cstr.opacity != null) alpha /= 100\n\t}\n\n\treturn {\n\t\tspace: space,\n\t\tvalues: parts,\n\t\talpha: alpha\n\t}\n}\n", "/** @module  color-rgba */\r\nimport parse from 'color-parse'\r\nimport hsl from 'color-space/hsl.js'\r\n\r\nexport default function rgba (color) {\r\n\t// template literals\r\n\tif (Array.isArray(color) && color.raw) color = String.raw(...arguments)\r\n\r\n\tvar values, i, l\r\n\r\n\t//attempt to parse non-array arguments\r\n\tvar parsed = parse(color)\r\n\r\n\tif (!parsed.space) return []\r\n\r\n\tvalues = Array(3)\r\n\tvalues[0] = Math.min(Math.max(parsed.values[0], 0), 255)\r\n\tvalues[1] = Math.min(Math.max(parsed.values[1], 0), 255)\r\n\tvalues[2] = Math.min(Math.max(parsed.values[2], 0), 255)\r\n\r\n\tif (parsed.space[0] === 'h') {\r\n\t\tvalues = hsl.rgb(values)\r\n\t}\r\n\r\n\tvalues.push(Math.min(Math.max(parsed.alpha, 0), 1))\r\n\r\n\treturn values\r\n}\r\n"], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAEA,WAAO,UAAU;AAAA,MAChB,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,QAAQ,CAAC,GAAG,KAAK,GAAG;AAAA,MACpB,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,SAAS,CAAC,GAAG,GAAG,CAAC;AAAA,MACjB,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,QAAQ,CAAC,GAAG,GAAG,GAAG;AAAA,MAClB,cAAc,CAAC,KAAK,IAAI,GAAG;AAAA,MAC3B,SAAS,CAAC,KAAK,IAAI,EAAE;AAAA,MACrB,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,IAAI,KAAK,GAAG;AAAA,MAC1B,cAAc,CAAC,KAAK,KAAK,CAAC;AAAA,MAC1B,aAAa,CAAC,KAAK,KAAK,EAAE;AAAA,MAC1B,SAAS,CAAC,KAAK,KAAK,EAAE;AAAA,MACtB,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,WAAW,CAAC,KAAK,IAAI,EAAE;AAAA,MACvB,QAAQ,CAAC,GAAG,KAAK,GAAG;AAAA,MACpB,YAAY,CAAC,GAAG,GAAG,GAAG;AAAA,MACtB,YAAY,CAAC,GAAG,KAAK,GAAG;AAAA,MACxB,iBAAiB,CAAC,KAAK,KAAK,EAAE;AAAA,MAC9B,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,aAAa,CAAC,GAAG,KAAK,CAAC;AAAA,MACvB,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,eAAe,CAAC,KAAK,GAAG,GAAG;AAAA,MAC3B,kBAAkB,CAAC,IAAI,KAAK,EAAE;AAAA,MAC9B,cAAc,CAAC,KAAK,KAAK,CAAC;AAAA,MAC1B,cAAc,CAAC,KAAK,IAAI,GAAG;AAAA,MAC3B,WAAW,CAAC,KAAK,GAAG,CAAC;AAAA,MACrB,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,iBAAiB,CAAC,IAAI,IAAI,GAAG;AAAA,MAC7B,iBAAiB,CAAC,IAAI,IAAI,EAAE;AAAA,MAC5B,iBAAiB,CAAC,IAAI,IAAI,EAAE;AAAA,MAC5B,iBAAiB,CAAC,GAAG,KAAK,GAAG;AAAA,MAC7B,cAAc,CAAC,KAAK,GAAG,GAAG;AAAA,MAC1B,YAAY,CAAC,KAAK,IAAI,GAAG;AAAA,MACzB,eAAe,CAAC,GAAG,KAAK,GAAG;AAAA,MAC3B,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,cAAc,CAAC,IAAI,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,KAAK,IAAI,EAAE;AAAA,MACzB,eAAe,CAAC,KAAK,KAAK,GAAG;AAAA,MAC7B,eAAe,CAAC,IAAI,KAAK,EAAE;AAAA,MAC3B,WAAW,CAAC,KAAK,GAAG,GAAG;AAAA,MACvB,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,QAAQ,CAAC,KAAK,KAAK,CAAC;AAAA,MACpB,aAAa,CAAC,KAAK,KAAK,EAAE;AAAA,MAC1B,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,SAAS,CAAC,GAAG,KAAK,CAAC;AAAA,MACnB,eAAe,CAAC,KAAK,KAAK,EAAE;AAAA,MAC5B,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,aAAa,CAAC,KAAK,IAAI,EAAE;AAAA,MACzB,UAAU,CAAC,IAAI,GAAG,GAAG;AAAA,MACrB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,iBAAiB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC/B,aAAa,CAAC,KAAK,KAAK,CAAC;AAAA,MACzB,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,wBAAwB,CAAC,KAAK,KAAK,GAAG;AAAA,MACtC,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,eAAe,CAAC,KAAK,KAAK,GAAG;AAAA,MAC7B,iBAAiB,CAAC,IAAI,KAAK,GAAG;AAAA,MAC9B,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,eAAe,CAAC,KAAK,KAAK,GAAG;AAAA,MAC7B,QAAQ,CAAC,GAAG,KAAK,CAAC;AAAA,MAClB,aAAa,CAAC,IAAI,KAAK,EAAE;AAAA,MACzB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,WAAW,CAAC,KAAK,GAAG,GAAG;AAAA,MACvB,UAAU,CAAC,KAAK,GAAG,CAAC;AAAA,MACpB,oBAAoB,CAAC,KAAK,KAAK,GAAG;AAAA,MAClC,cAAc,CAAC,GAAG,GAAG,GAAG;AAAA,MACxB,gBAAgB,CAAC,KAAK,IAAI,GAAG;AAAA,MAC7B,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,kBAAkB,CAAC,IAAI,KAAK,GAAG;AAAA,MAC/B,mBAAmB,CAAC,KAAK,KAAK,GAAG;AAAA,MACjC,qBAAqB,CAAC,GAAG,KAAK,GAAG;AAAA,MACjC,mBAAmB,CAAC,IAAI,KAAK,GAAG;AAAA,MAChC,mBAAmB,CAAC,KAAK,IAAI,GAAG;AAAA,MAChC,gBAAgB,CAAC,IAAI,IAAI,GAAG;AAAA,MAC5B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,eAAe,CAAC,KAAK,KAAK,GAAG;AAAA,MAC7B,QAAQ,CAAC,GAAG,GAAG,GAAG;AAAA,MAClB,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,SAAS,CAAC,KAAK,KAAK,CAAC;AAAA,MACrB,aAAa,CAAC,KAAK,KAAK,EAAE;AAAA,MAC1B,UAAU,CAAC,KAAK,KAAK,CAAC;AAAA,MACtB,aAAa,CAAC,KAAK,IAAI,CAAC;AAAA,MACxB,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,iBAAiB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC/B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,iBAAiB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC/B,iBAAiB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC/B,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,QAAQ,CAAC,KAAK,KAAK,EAAE;AAAA,MACrB,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,UAAU,CAAC,KAAK,GAAG,GAAG;AAAA,MACtB,iBAAiB,CAAC,KAAK,IAAI,GAAG;AAAA,MAC9B,OAAO,CAAC,KAAK,GAAG,CAAC;AAAA,MACjB,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,IAAI,KAAK,GAAG;AAAA,MAC1B,eAAe,CAAC,KAAK,IAAI,EAAE;AAAA,MAC3B,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,cAAc,CAAC,KAAK,KAAK,EAAE;AAAA,MAC3B,YAAY,CAAC,IAAI,KAAK,EAAE;AAAA,MACxB,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,UAAU,CAAC,KAAK,IAAI,EAAE;AAAA,MACtB,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,aAAa,CAAC,KAAK,IAAI,GAAG;AAAA,MAC1B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,eAAe,CAAC,GAAG,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,IAAI,KAAK,GAAG;AAAA,MAC1B,OAAO,CAAC,KAAK,KAAK,GAAG;AAAA,MACrB,QAAQ,CAAC,GAAG,KAAK,GAAG;AAAA,MACpB,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,UAAU,CAAC,KAAK,IAAI,EAAE;AAAA,MACtB,aAAa,CAAC,IAAI,KAAK,GAAG;AAAA,MAC1B,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,UAAU,CAAC,KAAK,KAAK,CAAC;AAAA,MACtB,eAAe,CAAC,KAAK,KAAK,EAAE;AAAA,IAC7B;AAAA;AAAA;;;ACvJA;AAAA;AAAA;AAOA,WAAO,UAAU;AAAA,MAChB,MAAM;AAAA,MACN,KAAK,CAAC,GAAE,GAAE,CAAC;AAAA,MACX,KAAK,CAAC,KAAI,KAAI,GAAG;AAAA,MACjB,SAAS,CAAC,OAAO,SAAS,MAAM;AAAA,MAChC,OAAO,CAAC,KAAK;AAAA,IACd;AAAA;AAAA;;;ACbA;AAAA;AAAA;AAKA,QAAI,MAAM;AAEV,WAAO,UAAU;AAAA,MAChB,MAAM;AAAA,MACN,KAAK,CAAC,GAAE,GAAE,CAAC;AAAA,MACX,KAAK,CAAC,KAAI,KAAI,GAAG;AAAA,MACjB,SAAS,CAAC,OAAO,cAAc,WAAW;AAAA,MAC1C,OAAO,CAAC,KAAK;AAAA,MAEb,KAAK,SAASA,MAAK;AAClB,YAAI,IAAIA,KAAI,CAAC,IAAI,KACf,IAAIA,KAAI,CAAC,IAAI,KACb,IAAIA,KAAI,CAAC,IAAI,KACb,IAAI,IAAI,IAAIC,MAAK;AAEnB,YAAI,MAAM,GAAG;AACZ,gBAAM,IAAI;AACV,iBAAO,CAAC,KAAK,KAAK,GAAG;AAAA,QACtB;AAEA,YAAI,IAAI,KAAK;AACZ,eAAK,KAAK,IAAI;AAAA,QACf,OACK;AACJ,eAAK,IAAI,IAAI,IAAI;AAAA,QAClB;AACA,aAAK,IAAI,IAAI;AAEb,QAAAA,OAAM,CAAC,GAAG,GAAG,CAAC;AACd,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC3B,eAAK,IAAI,IAAI,IAAI,EAAG,IAAI;AACxB,cAAI,KAAK,GAAG;AACX;AAAA,UACD,WACS,KAAK,GAAG;AAChB;AAAA,UACD;AAEA,cAAI,IAAI,KAAK,GAAG;AACf,kBAAM,MAAM,KAAK,MAAM,IAAI;AAAA,UAC5B,WACS,IAAI,KAAK,GAAG;AACpB,kBAAM;AAAA,UACP,WACS,IAAI,KAAK,GAAG;AACpB,kBAAM,MAAM,KAAK,OAAO,IAAI,IAAI,MAAM;AAAA,UACvC,OACK;AACJ,kBAAM;AAAA,UACP;AAEA,UAAAA,KAAI,CAAC,IAAI,MAAM;AAAA,QAChB;AAEA,eAAOA;AAAA,MACR;AAAA,IACD;AAIA,QAAI,MAAM,SAASA,MAAK;AACvB,UAAI,IAAIA,KAAI,CAAC,IAAE,KACb,IAAIA,KAAI,CAAC,IAAE,KACX,IAAIA,KAAI,CAAC,IAAE,KACX,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GACtB,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GACtB,QAAQ,MAAM,KACd,GAAG,GAAG;AAER,UAAI,QAAQ,KAAK;AAChB,YAAI;AAAA,MACL,WACS,MAAM,KAAK;AACnB,aAAK,IAAI,KAAK;AAAA,MACf,WACS,MAAM,KAAK;AACnB,YAAI,KAAK,IAAI,KAAK;AAAA,MACnB,WACS,MAAM,KAAK;AACnB,YAAI,KAAK,IAAI,KAAI;AAAA,MAClB;AAEA,UAAI,KAAK,IAAI,IAAI,IAAI,GAAG;AAExB,UAAI,IAAI,GAAG;AACV,aAAK;AAAA,MACN;AAEA,WAAK,MAAM,OAAO;AAElB,UAAI,QAAQ,KAAK;AAChB,YAAI;AAAA,MACL,WACS,KAAK,KAAK;AAClB,YAAI,SAAS,MAAM;AAAA,MACpB,OACK;AACJ,YAAI,SAAS,IAAI,MAAM;AAAA,MACxB;AAEA,aAAO,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IAC5B;AAAA;AAAA;;;ACvGA,wBAAkB;AAElB,IAAO,sBAAQ;AAOf,IAAI,WAAW;AAAA,EACd,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ;AACT;AAOA,SAAS,MAAM,MAAM;AACpB,MAAI,GAAG,QAAQ,CAAC,GAAG,QAAQ,GAAG;AAE9B,MAAI,OAAO,SAAS,UAAU;AAC7B,WAAO,KAAK,YAAY;AAGxB,QAAI,kBAAAC,QAAM,IAAI,GAAG;AAChB,cAAQ,kBAAAA,QAAM,IAAI,EAAE,MAAM;AAC1B,cAAQ;AAAA,IACT,WAGS,SAAS,eAAe;AAChC,cAAQ;AACR,cAAQ;AACR,cAAQ,CAAC,GAAG,GAAG,CAAC;AAAA,IACjB,WAGS,kBAAkB,KAAK,IAAI,GAAG;AACtC,UAAI,OAAO,KAAK,MAAM,CAAC;AACvB,UAAI,OAAO,KAAK;AAChB,UAAI,UAAU,QAAQ;AACtB,cAAQ;AAER,UAAI,SAAS;AACZ,gBAAQ;AAAA,UACP,SAAS,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,EAAE;AAAA,UAC9B,SAAS,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,EAAE;AAAA,UAC9B,SAAS,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,EAAE;AAAA,QAC/B;AACA,YAAI,SAAS,GAAG;AACf,kBAAQ,SAAS,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,EAAE,IAAI;AAAA,QAC3C;AAAA,MACD,OACK;AACJ,gBAAQ;AAAA,UACP,SAAS,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,EAAE;AAAA,UAC9B,SAAS,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,EAAE;AAAA,UAC9B,SAAS,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,EAAE;AAAA,QAC/B;AACA,YAAI,SAAS,GAAG;AACf,kBAAQ,SAAS,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,EAAE,IAAI;AAAA,QAC3C;AAAA,MACD;AAEA,UAAI,CAAC,MAAM,CAAC,EAAG,OAAM,CAAC,IAAI;AAC1B,UAAI,CAAC,MAAM,CAAC,EAAG,OAAM,CAAC,IAAI;AAC1B,UAAI,CAAC,MAAM,CAAC,EAAG,OAAM,CAAC,IAAI;AAE1B,cAAQ;AAAA,IACT,WAGS,IAAI,mFAAmF,KAAK,IAAI,GAAG;AAC3G,UAAI,OAAO,EAAE,CAAC;AACd,UAAI,QAAQ,SAAS;AACrB,UAAI,OAAO,KAAK,QAAQ,MAAM,EAAE;AAChC,cAAQ;AACR,UAAI,OAAO,SAAS,SAAS,IAAI,SAAS,SAAS,IAAI;AACvD,cAAQ,EAAE,CAAC,EAAE,KAAK,EAChB,MAAM,iBAAiB,EACvB,IAAI,SAAU,GAAG,GAAG;AAEpB,YAAI,KAAK,KAAK,CAAC,GAAG;AAEjB,cAAI,MAAM,KAAM,QAAO,WAAW,CAAC,IAAI;AAEvC,cAAI,SAAS,MAAO,QAAO,WAAW,CAAC,IAAI,MAAM;AACjD,iBAAO,WAAW,CAAC;AAAA,QACpB,WAES,KAAK,CAAC,MAAM,KAAK;AAEzB,cAAI,OAAO,KAAK,CAAC,GAAG;AACnB,mBAAO,WAAW,CAAC;AAAA,UACpB,WAES,SAAS,CAAC,MAAM,QAAW;AACnC,mBAAO,SAAS,CAAC;AAAA,UAClB;AAAA,QACD;AACA,eAAO,WAAW,CAAC;AAAA,MACpB,CAAC;AAEF,UAAI,SAAS,KAAM,OAAM,KAAK,CAAC;AAC/B,cAAS,QAAS,IAAK,MAAM,IAAI,MAAM,SAAa,IAAI,MAAM,IAAI;AAClE,cAAQ,MAAM,MAAM,GAAG,IAAI;AAAA,IAC5B,WAGS,KAAK,SAAS,MAAM,iBAAiB,KAAK,IAAI,GAAG;AACzD,cAAQ,KAAK,MAAM,WAAW,EAAE,IAAI,SAAU,OAAO;AACpD,eAAO,WAAW,KAAK;AAAA,MACxB,CAAC;AAED,cAAQ,KAAK,MAAM,WAAW,EAAE,KAAK,EAAE,EAAE,YAAY;AAAA,IACtD;AAAA,EACD,WAGS,CAAC,MAAM,IAAI,GAAG;AACtB,YAAQ;AACR,YAAQ,CAAC,SAAS,KAAK,OAAO,WAAc,GAAG,OAAO,GAAQ;AAAA,EAC/D,WAGS,MAAM,QAAQ,IAAI,KAAK,KAAK,QAAQ;AAC5C,YAAQ,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAClC,YAAQ;AACR,YAAQ,KAAK,WAAW,IAAI,KAAK,CAAC,IAAI;AAAA,EACvC,WAGS,gBAAgB,QAAQ;AAChC,QAAI,KAAK,KAAK,QAAQ,KAAK,OAAO,QAAQ,KAAK,KAAK,MAAM;AACzD,cAAQ;AACR,cAAQ;AAAA,QACP,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK;AAAA,QAChC,KAAK,KAAK,KAAK,SAAS,KAAK,KAAK;AAAA,QAClC,KAAK,KAAK,KAAK,QAAQ,KAAK,KAAK;AAAA,MAClC;AAAA,IACD,OACK;AACJ,cAAQ;AACR,cAAQ;AAAA,QACP,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK;AAAA,QAChC,KAAK,KAAK,KAAK,cAAc,KAAK,KAAK;AAAA,QACvC,KAAK,KAAK,KAAK,aAAa,KAAK,KAAK,KAAK,KAAK,KAAK;AAAA,MACtD;AAAA,IACD;AAEA,YAAQ,KAAK,KAAK,KAAK,SAAS,KAAK,WAAW;AAEhD,QAAI,KAAK,WAAW,KAAM,UAAS;AAAA,EACpC;AAEA,SAAO;AAAA,IACN;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,EACD;AACD;;;ACvKA,iBAAgB;AAED,SAAR,KAAuB,OAAO;AAEpC,MAAI,MAAM,QAAQ,KAAK,KAAK,MAAM,IAAK,SAAQ,OAAO,IAAI,GAAG,SAAS;AAEtE,MAAI,QAAQ,GAAG;AAGf,MAAI,SAAS,oBAAM,KAAK;AAExB,MAAI,CAAC,OAAO,MAAO,QAAO,CAAC;AAE3B,WAAS,MAAM,CAAC;AAChB,SAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG;AACvD,SAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG;AACvD,SAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG;AAEvD,MAAI,OAAO,MAAM,CAAC,MAAM,KAAK;AAC5B,aAAS,WAAAC,QAAI,IAAI,MAAM;AAAA,EACxB;AAEA,SAAO,KAAK,KAAK,IAAI,KAAK,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AAElD,SAAO;AACR;", "names": ["hsl", "rgb", "names", "hsl"]}
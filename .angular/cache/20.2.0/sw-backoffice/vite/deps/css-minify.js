import {
  __commonJS
} from "./chunk-WOR4A3D2.js";

// node_modules/css-minify/lib/minify.js
var require_minify = __commonJS({
  "node_modules/css-minify/lib/minify.js"(exports, module) {
    var minify = function(str) {
      str = str.replace(/\/\*(.|\n)*?\*\//g, "");
      str = str.replace(/\s*(\{|\}|\[|\]|\(|\)|\:|\;|\,)\s*/g, "$1");
      str = str.replace(/#([\da-fA-F])\1([\da-fA-F])\2([\da-fA-F])\3/g, "#$1$2$3");
      str = str.replace(/:[\+\-]?0(rem|em|ec|ex|px|pc|pt|vh|vw|vmin|vmax|%|mm|cm|in)/g, ":0");
      str = str.replace(/\n/g, "");
      str = str.replace(/;\}/g, "}");
      str = str.replace(/^\s+|\s+$/g, "");
      return str;
    };
    module.exports = minify;
  }
});

// node_modules/css-minify/index.js
var require_css_minify = __commonJS({
  "node_modules/css-minify/index.js"(exports, module) {
    var minify = require_minify();
    module.exports = minify;
  }
});
export default require_css_minify();
//# sourceMappingURL=css-minify.js.map

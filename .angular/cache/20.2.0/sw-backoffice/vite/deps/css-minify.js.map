{"version": 3, "sources": ["../../../../../../node_modules/css-minify/lib/minify.js", "../../../../../../node_modules/css-minify/index.js"], "sourcesContent": ["var minify=function(str){\n\tstr=str.replace(/\\/\\*(.|\\n)*?\\*\\//g,\"\");//删除注释\n\tstr=str.replace(/\\s*(\\{|\\}|\\[|\\]|\\(|\\)|\\:|\\;|\\,)\\s*/g,\"$1\");//删除小括号、中括号、大括号、冒号、逗号、分号两边的空格\n\tstr=str.replace(/#([\\da-fA-F])\\1([\\da-fA-F])\\2([\\da-fA-F])\\3/g,\"#$1$2$3\");//颜色值#aabbcc转换为#abc\n\tstr=str.replace(/:[\\+\\-]?0(rem|em|ec|ex|px|pc|pt|vh|vw|vmin|vmax|%|mm|cm|in)/g,\":0\");//删除值为0的单位\n\tstr=str.replace(/\\n/g,\"\");//删除换行符\n\tstr=str.replace(/;\\}/g,\"}\");//删除最后一行语句的分号\n\tstr=str.replace(/^\\s+|\\s+$/g,\"\");//删除首尾的空白符\n\treturn str;\n};\nmodule.exports=minify;", "var minify=require(\"./lib/minify.js\");\nmodule.exports=minify;"], "mappings": ";;;;;AAAA;AAAA;AAAA,QAAI,SAAO,SAAS,KAAI;AACvB,YAAI,IAAI,QAAQ,qBAAoB,EAAE;AACtC,YAAI,IAAI,QAAQ,uCAAsC,IAAI;AAC1D,YAAI,IAAI,QAAQ,gDAA+C,SAAS;AACxE,YAAI,IAAI,QAAQ,gEAA+D,IAAI;AACnF,YAAI,IAAI,QAAQ,OAAM,EAAE;AACxB,YAAI,IAAI,QAAQ,QAAO,GAAG;AAC1B,YAAI,IAAI,QAAQ,cAAa,EAAE;AAC/B,aAAO;AAAA,IACR;AACA,WAAO,UAAQ;AAAA;AAAA;;;ACVf;AAAA;AAAA,QAAI,SAAO;AACX,WAAO,UAAQ;AAAA;AAAA;", "names": []}
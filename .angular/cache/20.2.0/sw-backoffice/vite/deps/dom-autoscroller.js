import {
  __commonJS,
  __esm,
  __export,
  __toCommonJS
} from "./chunk-WOR4A3D2.js";

// node_modules/type-func/dist/bundle.js
var require_bundle = __commonJS({
  "node_modules/type-func/dist/bundle.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    function getDef(f, d) {
      if (typeof f === "undefined") {
        return typeof d === "undefined" ? f : d;
      }
      return f;
    }
    function boolean(func, def) {
      func = getDef(func, def);
      if (typeof func === "function") {
        return function f() {
          for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {
            args[_key] = arguments[_key];
          }
          return !!func.apply(this, args);
        };
      }
      return !!func ? function() {
        return true;
      } : function() {
        return false;
      };
    }
    function integer(func, def) {
      func = getDef(func, def);
      if (typeof func === "function") {
        return function f() {
          for (var _len2 = arguments.length, args = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
            args[_key2] = arguments[_key2];
          }
          var n = parseInt(func.apply(this, args), 10);
          return n != n ? 0 : n;
        };
      }
      func = parseInt(func, 10);
      return func != func ? function() {
        return 0;
      } : function() {
        return func;
      };
    }
    function string(func, def) {
      func = getDef(func, def);
      if (typeof func === "function") {
        return function f() {
          for (var _len3 = arguments.length, args = Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
            args[_key3] = arguments[_key3];
          }
          return "" + func.apply(this, args);
        };
      }
      func = "" + func;
      return function() {
        return func;
      };
    }
    exports.boolean = boolean;
    exports.integer = integer;
    exports.string = string;
  }
});

// node_modules/animation-frame-polyfill/lib/animation-frame-polyfill.module.js
var animation_frame_polyfill_module_exports = {};
__export(animation_frame_polyfill_module_exports, {
  cancelAnimationFrame: () => cancelAnimationFrame,
  requestAnimationFrame: () => requestAnimationFrame
});
var prefix, requestAnimationFrame, cancelAnimationFrame;
var init_animation_frame_polyfill_module = __esm({
  "node_modules/animation-frame-polyfill/lib/animation-frame-polyfill.module.js"() {
    prefix = ["webkit", "moz", "ms", "o"];
    requestAnimationFrame = (function() {
      for (var i = 0, limit = prefix.length; i < limit && !window.requestAnimationFrame; ++i) {
        window.requestAnimationFrame = window[prefix[i] + "RequestAnimationFrame"];
      }
      if (!window.requestAnimationFrame) {
        var lastTime = 0;
        window.requestAnimationFrame = function(callback) {
          var now = (/* @__PURE__ */ new Date()).getTime();
          var ttc = Math.max(0, 16 - now - lastTime);
          var timer = window.setTimeout(function() {
            return callback(now + ttc);
          }, ttc);
          lastTime = now + ttc;
          return timer;
        };
      }
      return window.requestAnimationFrame.bind(window);
    })();
    cancelAnimationFrame = (function() {
      for (var i = 0, limit = prefix.length; i < limit && !window.cancelAnimationFrame; ++i) {
        window.cancelAnimationFrame = window[prefix[i] + "CancelAnimationFrame"] || window[prefix[i] + "CancelRequestAnimationFrame"];
      }
      if (!window.cancelAnimationFrame) {
        window.cancelAnimationFrame = function(timer) {
          window.clearTimeout(timer);
        };
      }
      return window.cancelAnimationFrame.bind(window);
    })();
  }
});

// node_modules/array-from/polyfill.js
var require_polyfill = __commonJS({
  "node_modules/array-from/polyfill.js"(exports, module) {
    module.exports = (function() {
      var isCallable = function(fn) {
        return typeof fn === "function";
      };
      var toInteger = function(value) {
        var number = Number(value);
        if (isNaN(number)) {
          return 0;
        }
        if (number === 0 || !isFinite(number)) {
          return number;
        }
        return (number > 0 ? 1 : -1) * Math.floor(Math.abs(number));
      };
      var maxSafeInteger = Math.pow(2, 53) - 1;
      var toLength = function(value) {
        var len = toInteger(value);
        return Math.min(Math.max(len, 0), maxSafeInteger);
      };
      var iteratorProp = function(value) {
        if (value != null) {
          if (["string", "number", "boolean", "symbol"].indexOf(typeof value) > -1) {
            return Symbol.iterator;
          } else if (typeof Symbol !== "undefined" && "iterator" in Symbol && Symbol.iterator in value) {
            return Symbol.iterator;
          } else if ("@@iterator" in value) {
            return "@@iterator";
          }
        }
      };
      var getMethod = function(O, P) {
        if (O != null && P != null) {
          var func = O[P];
          if (func == null) {
            return void 0;
          }
          if (!isCallable(func)) {
            throw new TypeError(func + " is not a function");
          }
          return func;
        }
      };
      var iteratorStep = function(iterator) {
        var result = iterator.next();
        var done = Boolean(result.done);
        if (done) {
          return false;
        }
        return result;
      };
      return function from(items) {
        "use strict";
        var C = this;
        var mapFn = arguments.length > 1 ? arguments[1] : void 0;
        var T;
        if (typeof mapFn !== "undefined") {
          if (!isCallable(mapFn)) {
            throw new TypeError(
              "Array.from: when provided, the second argument must be a function"
            );
          }
          if (arguments.length > 2) {
            T = arguments[2];
          }
        }
        var A, k;
        var usingIterator = getMethod(items, iteratorProp(items));
        if (usingIterator !== void 0) {
          A = isCallable(C) ? Object(new C()) : [];
          var iterator = usingIterator.call(items);
          if (iterator == null) {
            throw new TypeError(
              "Array.from requires an array-like or iterable object"
            );
          }
          k = 0;
          var next, nextValue;
          while (true) {
            next = iteratorStep(iterator);
            if (!next) {
              A.length = k;
              return A;
            }
            nextValue = next.value;
            if (mapFn) {
              A[k] = mapFn.call(T, nextValue, k);
            } else {
              A[k] = nextValue;
            }
            k++;
          }
        } else {
          var arrayLike = Object(items);
          if (items == null) {
            throw new TypeError(
              "Array.from requires an array-like object - not null or undefined"
            );
          }
          var len = toLength(arrayLike.length);
          A = isCallable(C) ? Object(new C(len)) : new Array(len);
          k = 0;
          var kValue;
          while (k < len) {
            kValue = arrayLike[k];
            if (mapFn) {
              A[k] = mapFn.call(T, kValue, k);
            } else {
              A[k] = kValue;
            }
            k++;
          }
          A.length = len;
        }
        return A;
      };
    })();
  }
});

// node_modules/array-from/index.js
var require_array_from = __commonJS({
  "node_modules/array-from/index.js"(exports, module) {
    module.exports = typeof Array.from === "function" ? Array.from : require_polyfill();
  }
});

// node_modules/is-array/index.js
var require_is_array = __commonJS({
  "node_modules/is-array/index.js"(exports, module) {
    var isArray = Array.isArray;
    var str = Object.prototype.toString;
    module.exports = isArray || function(val) {
      return !!val && "[object Array]" == str.call(val);
    };
  }
});

// node_modules/iselement/module/index.js
var module_exports = {};
__export(module_exports, {
  default: () => module_default
});
function module_default(input) {
  return input != null && (typeof input === "undefined" ? "undefined" : _typeof(input)) === "object" && input.nodeType === 1 && _typeof(input.style) === "object" && _typeof(input.ownerDocument) === "object";
}
var _typeof;
var init_module = __esm({
  "node_modules/iselement/module/index.js"() {
    _typeof = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? function(obj) {
      return typeof obj;
    } : function(obj) {
      return obj && typeof Symbol === "function" && obj.constructor === Symbol ? "symbol" : typeof obj;
    };
  }
});

// node_modules/dom-set/dist/bundle.js
var require_bundle2 = __commonJS({
  "node_modules/dom-set/dist/bundle.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    function _interopDefault(ex) {
      return ex && typeof ex === "object" && "default" in ex ? ex["default"] : ex;
    }
    var arrayFrom = _interopDefault(require_array_from());
    var isArray = _interopDefault(require_is_array());
    var isElement = _interopDefault((init_module(), __toCommonJS(module_exports)));
    var _typeof2 = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? function(obj) {
      return typeof obj;
    } : function(obj) {
      return obj && typeof Symbol === "function" && obj.constructor === Symbol ? "symbol" : typeof obj;
    };
    var isElement$1 = function(input) {
      return input != null && (typeof input === "undefined" ? "undefined" : _typeof2(input)) === "object" && input.nodeType === 1 && _typeof2(input.style) === "object" && _typeof2(input.ownerDocument) === "object";
    };
    function select(selector) {
      if (typeof selector === "string") {
        try {
          return document.querySelector(selector);
        } catch (e) {
          throw e;
        }
      } else if (isElement(selector)) {
        return selector;
      }
    }
    function selectAll(selector) {
      if (typeof selector === "string") {
        return Array.prototype.slice.apply(
          document.querySelectorAll(selector)
        );
      } else if (isArray(selector)) {
        return selector.map(select);
      } else if ("length" in selector) {
        return arrayFrom(selector).map(select);
      }
    }
    function indexOfElement(elements, element) {
      element = resolveElement(element, true);
      if (!isElement$1(element)) {
        return -1;
      }
      for (var i = 0; i < elements.length; i++) {
        if (elements[i] === element) {
          return i;
        }
      }
      return -1;
    }
    function hasElement(elements, element) {
      return -1 !== indexOfElement(elements, element);
    }
    function domListOf(arr) {
      if (!arr) {
        return [];
      }
      try {
        if (typeof arr === "string") {
          return arrayFrom(document.querySelectorAll(arr));
        } else if (isArray(arr)) {
          return arr.map(resolveElement);
        } else {
          if (typeof arr.length === "undefined") {
            return [resolveElement(arr)];
          }
          return arrayFrom(arr, resolveElement);
        }
      } catch (e) {
        throw new Error(e);
      }
    }
    function concatElementLists() {
      var lists = [], len = arguments.length;
      while (len--) lists[len] = arguments[len];
      return lists.reduce(function(last, list) {
        return list.length ? last : last.concat(domListOf(list));
      }, []);
    }
    function pushElements(elements, toAdd) {
      for (var i = 0; i < toAdd.length; i++) {
        if (!hasElement(elements, toAdd[i])) {
          elements.push(toAdd[i]);
        }
      }
      return toAdd;
    }
    function addElements(elements) {
      var toAdd = [], len = arguments.length - 1;
      while (len-- > 0) toAdd[len] = arguments[len + 1];
      toAdd = toAdd.map(resolveElement);
      return pushElements(elements, toAdd);
    }
    function removeElements(elements) {
      var toRemove = [], len = arguments.length - 1;
      while (len-- > 0) toRemove[len] = arguments[len + 1];
      return toRemove.map(resolveElement).reduce(function(last, e) {
        var index = indexOfElement(elements, e);
        if (index !== -1) {
          return last.concat(elements.splice(index, 1));
        }
        return last;
      }, []);
    }
    function resolveElement(element, noThrow) {
      if (typeof element === "string") {
        try {
          return document.querySelector(element);
        } catch (e) {
          throw e;
        }
      }
      if (!isElement$1(element) && !noThrow) {
        throw new TypeError(element + " is not a DOM element.");
      }
      return element;
    }
    exports.indexOfElement = indexOfElement;
    exports.hasElement = hasElement;
    exports.domListOf = domListOf;
    exports.concatElementLists = concatElementLists;
    exports.addElements = addElements;
    exports.removeElements = removeElements;
    exports.resolveElement = resolveElement;
    exports.select = select;
    exports.selectAll = selectAll;
  }
});

// node_modules/create-point-cb/dist/bundle.js
var require_bundle3 = __commonJS({
  "node_modules/create-point-cb/dist/bundle.js"(exports, module) {
    "use strict";
    var typeFunc = require_bundle();
    function createPointCB(object, options) {
      options = options || {};
      var allowUpdate = typeFunc.boolean(options.allowUpdate, true);
      return function pointCB(event) {
        event = event || window.event;
        object.target = event.target || event.srcElement || event.originalTarget;
        object.element = this;
        object.type = event.type;
        if (!allowUpdate(event)) {
          return;
        }
        if (event.targetTouches) {
          object.x = event.targetTouches[0].clientX;
          object.y = event.targetTouches[0].clientY;
          object.pageX = event.targetTouches[0].pageX;
          object.pageY = event.targetTouches[0].pageY;
          object.screenX = event.targetTouches[0].screenX;
          object.screenY = event.targetTouches[0].screenY;
        } else {
          if (event.pageX === null && event.clientX !== null) {
            var eventDoc = event.target && event.target.ownerDocument || document;
            var doc = eventDoc.documentElement;
            var body = eventDoc.body;
            object.pageX = event.clientX + (doc && doc.scrollLeft || body && body.scrollLeft || 0) - (doc && doc.clientLeft || body && body.clientLeft || 0);
            object.pageY = event.clientY + (doc && doc.scrollTop || body && body.scrollTop || 0) - (doc && doc.clientTop || body && body.clientTop || 0);
          } else {
            object.pageX = event.pageX;
            object.pageY = event.pageY;
          }
          object.x = event.clientX;
          object.y = event.clientY;
          object.screenX = event.screenX;
          object.screenY = event.screenY;
        }
        object.clientX = object.x;
        object.clientY = object.y;
      };
    }
    module.exports = createPointCB;
  }
});

// node_modules/dom-plane/dist/bundle.js
var require_bundle4 = __commonJS({
  "node_modules/dom-plane/dist/bundle.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    function _interopDefault(ex) {
      return ex && typeof ex === "object" && "default" in ex ? ex["default"] : ex;
    }
    var createPointCb = _interopDefault(require_bundle3());
    function createWindowRect() {
      var props = {
        top: { value: 0, enumerable: true },
        left: { value: 0, enumerable: true },
        right: { value: window.innerWidth, enumerable: true },
        bottom: { value: window.innerHeight, enumerable: true },
        width: { value: window.innerWidth, enumerable: true },
        height: { value: window.innerHeight, enumerable: true },
        x: { value: 0, enumerable: true },
        y: { value: 0, enumerable: true }
      };
      if (Object.create) {
        return Object.create({}, props);
      } else {
        var rect = {};
        Object.defineProperties(rect, props);
        return rect;
      }
    }
    function getClientRect(el) {
      if (el === window) {
        return createWindowRect();
      } else {
        try {
          var rect = el.getBoundingClientRect();
          if (rect.x === void 0) {
            rect.x = rect.left;
            rect.y = rect.top;
          }
          return rect;
        } catch (e) {
          throw new TypeError("Can't call getBoundingClientRect on " + el);
        }
      }
    }
    function pointInside(point, el) {
      var rect = getClientRect(el);
      return point.y > rect.top && point.y < rect.bottom && point.x > rect.left && point.x < rect.right;
    }
    exports.createPointCB = createPointCb;
    exports.getClientRect = getClientRect;
    exports.pointInside = pointInside;
  }
});

// node_modules/dom-mousemove-dispatcher/dist/bundle.js
var require_bundle5 = __commonJS({
  "node_modules/dom-mousemove-dispatcher/dist/bundle.js"(exports, module) {
    "use strict";
    var objectCreate = void 0;
    if (typeof Object.create != "function") {
      objectCreate = /* @__PURE__ */ (function(undefined2) {
        var Temp = function Temp2() {
        };
        return function(prototype, propertiesObject) {
          if (prototype !== Object(prototype) && prototype !== null) {
            throw TypeError("Argument must be an object, or null");
          }
          Temp.prototype = prototype || {};
          var result = new Temp();
          Temp.prototype = null;
          if (propertiesObject !== undefined2) {
            Object.defineProperties(result, propertiesObject);
          }
          if (prototype === null) {
            result.__proto__ = null;
          }
          return result;
        };
      })();
    } else {
      objectCreate = Object.create;
    }
    var objectCreate$1 = objectCreate;
    var mouseEventProps = ["altKey", "button", "buttons", "clientX", "clientY", "ctrlKey", "metaKey", "movementX", "movementY", "offsetX", "offsetY", "pageX", "pageY", "region", "relatedTarget", "screenX", "screenY", "shiftKey", "which", "x", "y"];
    function createDispatcher(element) {
      var defaultSettings = {
        screenX: 0,
        screenY: 0,
        clientX: 0,
        clientY: 0,
        ctrlKey: false,
        shiftKey: false,
        altKey: false,
        metaKey: false,
        button: 0,
        buttons: 1,
        relatedTarget: null,
        region: null
      };
      if (element !== void 0) {
        element.addEventListener("mousemove", onMove);
      }
      function onMove(e) {
        for (var i = 0; i < mouseEventProps.length; i++) {
          defaultSettings[mouseEventProps[i]] = e[mouseEventProps[i]];
        }
      }
      var dispatch = (function() {
        if (MouseEvent) {
          return function m1(element2, initMove, data) {
            var evt = new MouseEvent("mousemove", createMoveInit(defaultSettings, initMove));
            setSpecial(evt, data);
            return element2.dispatchEvent(evt);
          };
        } else if (typeof document.createEvent === "function") {
          return function m2(element2, initMove, data) {
            var settings = createMoveInit(defaultSettings, initMove);
            var evt = document.createEvent("MouseEvents");
            evt.initMouseEvent(
              "mousemove",
              true,
              //can bubble
              true,
              //cancelable
              window,
              //view
              0,
              //detail
              settings.screenX,
              //0, //screenX
              settings.screenY,
              //0, //screenY
              settings.clientX,
              //80, //clientX
              settings.clientY,
              //20, //clientY
              settings.ctrlKey,
              //false, //ctrlKey
              settings.altKey,
              //false, //altKey
              settings.shiftKey,
              //false, //shiftKey
              settings.metaKey,
              //false, //metaKey
              settings.button,
              //0, //button
              settings.relatedTarget
              //null //relatedTarget
            );
            setSpecial(evt, data);
            return element2.dispatchEvent(evt);
          };
        } else if (typeof document.createEventObject === "function") {
          return function m3(element2, initMove, data) {
            var evt = document.createEventObject();
            var settings = createMoveInit(defaultSettings, initMove);
            for (var name in settings) {
              evt[name] = settings[name];
            }
            setSpecial(evt, data);
            return element2.dispatchEvent(evt);
          };
        }
      })();
      function destroy() {
        if (element) element.removeEventListener("mousemove", onMove, false);
        defaultSettings = null;
      }
      return {
        destroy,
        dispatch
      };
    }
    function createMoveInit(defaultSettings, initMove) {
      initMove = initMove || {};
      var settings = objectCreate$1(defaultSettings);
      for (var i = 0; i < mouseEventProps.length; i++) {
        if (initMove[mouseEventProps[i]] !== void 0) settings[mouseEventProps[i]] = initMove[mouseEventProps[i]];
      }
      return settings;
    }
    function setSpecial(e, data) {
      console.log("data ", data);
      e.data = data || {};
      e.dispatched = "mousemove";
    }
    module.exports = createDispatcher;
  }
});

// node_modules/dom-autoscroller/dist/bundle.js
var require_bundle6 = __commonJS({
  "node_modules/dom-autoscroller/dist/bundle.js"(exports, module) {
    function _interopDefault(ex) {
      return ex && typeof ex === "object" && "default" in ex ? ex["default"] : ex;
    }
    var typeFunc = require_bundle();
    var animationFramePolyfill = (init_animation_frame_polyfill_module(), __toCommonJS(animation_frame_polyfill_module_exports));
    var domSet = require_bundle2();
    var domPlane = require_bundle4();
    var mousemoveDispatcher = _interopDefault(require_bundle5());
    function AutoScroller(elements, options) {
      if (options === void 0) options = {};
      var self = this;
      var maxSpeed = 4, scrolling = false;
      this.margin = options.margin || -1;
      this.scrollWhenOutside = options.scrollWhenOutside || false;
      var point = {}, pointCB = domPlane.createPointCB(point), dispatcher = mousemoveDispatcher(), down = false;
      window.addEventListener("mousemove", pointCB, false);
      window.addEventListener("touchmove", pointCB, false);
      if (!isNaN(options.maxSpeed)) {
        maxSpeed = options.maxSpeed;
      }
      this.autoScroll = typeFunc.boolean(options.autoScroll);
      this.syncMove = typeFunc.boolean(options.syncMove, false);
      this.destroy = function(forceCleanAnimation) {
        window.removeEventListener("mousemove", pointCB, false);
        window.removeEventListener("touchmove", pointCB, false);
        window.removeEventListener("mousedown", onDown, false);
        window.removeEventListener("touchstart", onDown, false);
        window.removeEventListener("mouseup", onUp, false);
        window.removeEventListener("touchend", onUp, false);
        window.removeEventListener("pointerup", onUp, false);
        window.removeEventListener("mouseleave", onMouseOut, false);
        window.removeEventListener("mousemove", onMove, false);
        window.removeEventListener("touchmove", onMove, false);
        window.removeEventListener("scroll", setScroll, true);
        elements = [];
        if (forceCleanAnimation) {
          cleanAnimation();
        }
      };
      this.add = function() {
        var element = [], len = arguments.length;
        while (len--) element[len] = arguments[len];
        domSet.addElements.apply(void 0, [elements].concat(element));
        return this;
      };
      this.remove = function() {
        var element = [], len = arguments.length;
        while (len--) element[len] = arguments[len];
        return domSet.removeElements.apply(void 0, [elements].concat(element));
      };
      var hasWindow = null, windowAnimationFrame;
      if (Object.prototype.toString.call(elements) !== "[object Array]") {
        elements = [elements];
      }
      (function(temp) {
        elements = [];
        temp.forEach(function(element) {
          if (element === window) {
            hasWindow = window;
          } else {
            self.add(element);
          }
        });
      })(elements);
      Object.defineProperties(this, {
        down: {
          get: function() {
            return down;
          }
        },
        maxSpeed: {
          get: function() {
            return maxSpeed;
          }
        },
        point: {
          get: function() {
            return point;
          }
        },
        scrolling: {
          get: function() {
            return scrolling;
          }
        }
      });
      var n = 0, current = null, animationFrame;
      window.addEventListener("mousedown", onDown, false);
      window.addEventListener("touchstart", onDown, false);
      window.addEventListener("mouseup", onUp, false);
      window.addEventListener("touchend", onUp, false);
      window.addEventListener("pointerup", onUp, false);
      window.addEventListener("mousemove", onMove, false);
      window.addEventListener("touchmove", onMove, false);
      window.addEventListener("mouseleave", onMouseOut, false);
      window.addEventListener("scroll", setScroll, true);
      function setScroll(e) {
        for (var i = 0; i < elements.length; i++) {
          if (elements[i] === e.target) {
            scrolling = true;
            break;
          }
        }
        if (scrolling) {
          animationFramePolyfill.requestAnimationFrame(function() {
            return scrolling = false;
          });
        }
      }
      function onDown() {
        down = true;
      }
      function onUp() {
        down = false;
        cleanAnimation();
      }
      function cleanAnimation() {
        animationFramePolyfill.cancelAnimationFrame(animationFrame);
        animationFramePolyfill.cancelAnimationFrame(windowAnimationFrame);
      }
      function onMouseOut() {
        down = false;
      }
      function getTarget(target) {
        if (!target) {
          return null;
        }
        if (current === target) {
          return target;
        }
        if (domSet.hasElement(elements, target)) {
          return target;
        }
        while (target = target.parentNode) {
          if (domSet.hasElement(elements, target)) {
            return target;
          }
        }
        return null;
      }
      function getElementUnderPoint() {
        var underPoint = null;
        for (var i = 0; i < elements.length; i++) {
          if (inside(point, elements[i])) {
            underPoint = elements[i];
          }
        }
        return underPoint;
      }
      function onMove(event) {
        if (!self.autoScroll()) {
          return;
        }
        if (event["dispatched"]) {
          return;
        }
        var target = event.target, body = document.body;
        if (current && !inside(point, current)) {
          if (!self.scrollWhenOutside) {
            current = null;
          }
        }
        if (target && target.parentNode === body) {
          target = getElementUnderPoint();
        } else {
          target = getTarget(target);
          if (!target) {
            target = getElementUnderPoint();
          }
        }
        if (target && target !== current) {
          current = target;
        }
        if (hasWindow) {
          animationFramePolyfill.cancelAnimationFrame(windowAnimationFrame);
          windowAnimationFrame = animationFramePolyfill.requestAnimationFrame(scrollWindow);
        }
        if (!current) {
          return;
        }
        animationFramePolyfill.cancelAnimationFrame(animationFrame);
        animationFrame = animationFramePolyfill.requestAnimationFrame(scrollTick);
      }
      function scrollWindow() {
        autoScroll(hasWindow);
        animationFramePolyfill.cancelAnimationFrame(windowAnimationFrame);
        windowAnimationFrame = animationFramePolyfill.requestAnimationFrame(scrollWindow);
      }
      function scrollTick() {
        if (!current) {
          return;
        }
        autoScroll(current);
        animationFramePolyfill.cancelAnimationFrame(animationFrame);
        animationFrame = animationFramePolyfill.requestAnimationFrame(scrollTick);
      }
      function autoScroll(el) {
        var rect = domPlane.getClientRect(el), scrollx, scrolly;
        if (point.x < rect.left + self.margin) {
          scrollx = Math.floor(
            Math.max(-1, (point.x - rect.left) / self.margin - 1) * self.maxSpeed
          );
        } else if (point.x > rect.right - self.margin) {
          scrollx = Math.ceil(
            Math.min(1, (point.x - rect.right) / self.margin + 1) * self.maxSpeed
          );
        } else {
          scrollx = 0;
        }
        if (point.y < rect.top + self.margin) {
          scrolly = Math.floor(
            Math.max(-1, (point.y - rect.top) / self.margin - 1) * self.maxSpeed
          );
        } else if (point.y > rect.bottom - self.margin) {
          scrolly = Math.ceil(
            Math.min(1, (point.y - rect.bottom) / self.margin + 1) * self.maxSpeed
          );
        } else {
          scrolly = 0;
        }
        if (self.syncMove()) {
          dispatcher.dispatch(el, {
            pageX: point.pageX + scrollx,
            pageY: point.pageY + scrolly,
            clientX: point.x + scrollx,
            clientY: point.y + scrolly
          });
        }
        setTimeout(function() {
          if (scrolly) {
            scrollY(el, scrolly);
          }
          if (scrollx) {
            scrollX(el, scrollx);
          }
        });
      }
      function scrollY(el, amount) {
        if (el === window) {
          window.scrollTo(el.pageXOffset, el.pageYOffset + amount);
        } else {
          el.scrollTop += amount;
        }
      }
      function scrollX(el, amount) {
        if (el === window) {
          window.scrollTo(el.pageXOffset + amount, el.pageYOffset);
        } else {
          el.scrollLeft += amount;
        }
      }
    }
    function AutoScrollerFactory(element, options) {
      return new AutoScroller(element, options);
    }
    function inside(point, el, rect) {
      if (!rect) {
        return domPlane.pointInside(point, el);
      } else {
        return point.y > rect.top && point.y < rect.bottom && point.x > rect.left && point.x < rect.right;
      }
    }
    module.exports = AutoScrollerFactory;
  }
});
export default require_bundle6();
//# sourceMappingURL=dom-autoscroller.js.map

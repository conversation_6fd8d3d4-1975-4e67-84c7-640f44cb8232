{"version": 3, "sources": ["../../../../../../node_modules/type-func/dist/bundle.js", "../../../../../../node_modules/animation-frame-polyfill/lib/animation-frame-polyfill.module.js", "../../../../../../node_modules/array-from/polyfill.js", "../../../../../../node_modules/array-from/index.js", "../../../../../../node_modules/is-array/index.js", "../../../../../../node_modules/iselement/module/index.js", "../../../../../../node_modules/dom-set/dist/bundle.js", "../../../../../../node_modules/create-point-cb/dist/bundle.js", "../../../../../../node_modules/dom-plane/dist/bundle.js", "../../../../../../node_modules/dom-mousemove-dispatcher/dist/bundle.js", "../../../../../../node_modules/dom-autoscroller/dist/bundle.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nfunction getDef(f, d) {\n    if (typeof f === 'undefined') {\n        return typeof d === 'undefined' ? f : d;\n    }\n\n    return f;\n}\nfunction boolean(func, def) {\n\n    func = getDef(func, def);\n\n    if (typeof func === 'function') {\n        return function f() {\n            for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n                args[_key] = arguments[_key];\n            }\n\n            return !!func.apply(this, args);\n        };\n    }\n\n    return !!func ? function () {\n        return true;\n    } : function () {\n        return false;\n    };\n}\n\nfunction integer(func, def) {\n\n    func = getDef(func, def);\n\n    if (typeof func === 'function') {\n        return function f() {\n            for (var _len2 = arguments.length, args = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n                args[_key2] = arguments[_key2];\n            }\n\n            var n = parseInt(func.apply(this, args), 10);\n            return n != n ? 0 : n;\n        };\n    }\n\n    func = parseInt(func, 10);\n\n    return func != func ? function () {\n        return 0;\n    } : function () {\n        return func;\n    };\n}\n\nfunction string(func, def) {\n\n    func = getDef(func, def);\n\n    if (typeof func === 'function') {\n        return function f() {\n            for (var _len3 = arguments.length, args = Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n                args[_key3] = arguments[_key3];\n            }\n\n            return '' + func.apply(this, args);\n        };\n    }\n\n    func = '' + func;\n\n    return function () {\n        return func;\n    };\n}\n\nexports.boolean = boolean;\nexports.integer = integer;\nexports.string = string;\n\n", "var prefix = ['webkit', 'moz', 'ms', 'o'];\nexport var requestAnimationFrame = function () {\n  for (var i = 0, limit = prefix.length; i < limit && !window.requestAnimationFrame; ++i) {\n    window.requestAnimationFrame = window[prefix[i] + 'RequestAnimationFrame'];\n  }\n\n  if (!window.requestAnimationFrame) {\n    var lastTime = 0;\n\n    window.requestAnimationFrame = function (callback) {\n      var now = new Date().getTime();\n      var ttc = Math.max(0, 16 - now - lastTime);\n      var timer = window.setTimeout(function () {\n        return callback(now + ttc);\n      }, ttc);\n      lastTime = now + ttc;\n      return timer;\n    };\n  }\n\n  return window.requestAnimationFrame.bind(window);\n}();\nexport var cancelAnimationFrame = function () {\n  for (var i = 0, limit = prefix.length; i < limit && !window.cancelAnimationFrame; ++i) {\n    window.cancelAnimationFrame = window[prefix[i] + 'CancelAnimationFrame'] || window[prefix[i] + 'CancelRequestAnimationFrame'];\n  }\n\n  if (!window.cancelAnimationFrame) {\n    window.cancelAnimationFrame = function (timer) {\n      window.clearTimeout(timer);\n    };\n  }\n\n  return window.cancelAnimationFrame.bind(window);\n}();\n", "// Production steps of ECMA-262, Edition 6, 22.1.2.1\n// Reference: http://www.ecma-international.org/ecma-262/6.0/#sec-array.from\nmodule.exports = (function() {\n  var isCallable = function(fn) {\n    return typeof fn === 'function';\n  };\n  var toInteger = function (value) {\n    var number = Number(value);\n    if (isNaN(number)) { return 0; }\n    if (number === 0 || !isFinite(number)) { return number; }\n    return (number > 0 ? 1 : -1) * Math.floor(Math.abs(number));\n  };\n  var maxSafeInteger = Math.pow(2, 53) - 1;\n  var toLength = function (value) {\n    var len = toInteger(value);\n    return Math.min(Math.max(len, 0), maxSafeInteger);\n  };\n  var iteratorProp = function(value) {\n    if(value != null) {\n      if(['string','number','boolean','symbol'].indexOf(typeof value) > -1){\n        return Symbol.iterator;\n      } else if (\n        (typeof Symbol !== 'undefined') &&\n        ('iterator' in Symbol) &&\n        (Symbol.iterator in value)\n      ) {\n        return Symbol.iterator;\n      }\n      // Support \"@@iterator\" placeholder, Gecko 27 to Gecko 35\n      else if ('@@iterator' in value) {\n        return '@@iterator';\n      }\n    }\n  };\n  var getMethod = function(O, P) {\n    // Assert: IsPropertyKey(P) is true.\n    if (O != null && P != null) {\n      // Let func be GetV(O, P).\n      var func = O[P];\n      // ReturnIfAbrupt(func).\n      // If func is either undefined or null, return undefined.\n      if(func == null) {\n        return void 0;\n      }\n      // If IsCallable(func) is false, throw a TypeError exception.\n      if (!isCallable(func)) {\n        throw new TypeError(func + ' is not a function');\n      }\n      return func;\n    }\n  };\n  var iteratorStep = function(iterator) {\n    // Let result be IteratorNext(iterator).\n    // ReturnIfAbrupt(result).\n    var result = iterator.next();\n    // Let done be IteratorComplete(result).\n    // ReturnIfAbrupt(done).\n    var done = Boolean(result.done);\n    // If done is true, return false.\n    if(done) {\n      return false;\n    }\n    // Return result.\n    return result;\n  };\n\n  // The length property of the from method is 1.\n  return function from(items /*, mapFn, thisArg */ ) {\n    'use strict';\n\n    // 1. Let C be the this value.\n    var C = this;\n\n    // 2. If mapfn is undefined, let mapping be false.\n    var mapFn = arguments.length > 1 ? arguments[1] : void 0;\n\n    var T;\n    if (typeof mapFn !== 'undefined') {\n      // 3. else\n      //   a. If IsCallable(mapfn) is false, throw a TypeError exception.\n      if (!isCallable(mapFn)) {\n        throw new TypeError(\n          'Array.from: when provided, the second argument must be a function'\n        );\n      }\n\n      //   b. If thisArg was supplied, let T be thisArg; else let T\n      //      be undefined.\n      if (arguments.length > 2) {\n        T = arguments[2];\n      }\n      //   c. Let mapping be true (implied by mapFn)\n    }\n\n    var A, k;\n\n    // 4. Let usingIterator be GetMethod(items, @@iterator).\n    // 5. ReturnIfAbrupt(usingIterator).\n    var usingIterator = getMethod(items, iteratorProp(items));\n\n    // 6. If usingIterator is not undefined, then\n    if (usingIterator !== void 0) {\n      // a. If IsConstructor(C) is true, then\n      //   i. Let A be the result of calling the [[Construct]]\n      //      internal method of C with an empty argument list.\n      // b. Else,\n      //   i. Let A be the result of the abstract operation ArrayCreate\n      //      with argument 0.\n      // c. ReturnIfAbrupt(A).\n      A = isCallable(C) ? Object(new C()) : [];\n\n      // d. Let iterator be GetIterator(items, usingIterator).\n      var iterator = usingIterator.call(items);\n\n      // e. ReturnIfAbrupt(iterator).\n      if (iterator == null) {\n        throw new TypeError(\n          'Array.from requires an array-like or iterable object'\n        );\n      }\n\n      // f. Let k be 0.\n      k = 0;\n\n      // g. Repeat\n      var next, nextValue;\n      while (true) {\n        // i. Let Pk be ToString(k).\n        // ii. Let next be IteratorStep(iterator).\n        // iii. ReturnIfAbrupt(next).\n        next = iteratorStep(iterator);\n\n        // iv. If next is false, then\n        if (!next) {\n\n          // 1. Let setStatus be Set(A, \"length\", k, true).\n          // 2. ReturnIfAbrupt(setStatus).\n          A.length = k;\n\n          // 3. Return A.\n          return A;\n        }\n        // v. Let nextValue be IteratorValue(next).\n        // vi. ReturnIfAbrupt(nextValue)\n        nextValue = next.value;\n\n        // vii. If mapping is true, then\n        //   1. Let mappedValue be Call(mapfn, T, «nextValue, k»).\n        //   2. If mappedValue is an abrupt completion, return\n        //      IteratorClose(iterator, mappedValue).\n        //   3. Let mappedValue be mappedValue.[[value]].\n        // viii. Else, let mappedValue be nextValue.\n        // ix.  Let defineStatus be the result of\n        //      CreateDataPropertyOrThrow(A, Pk, mappedValue).\n        // x. [TODO] If defineStatus is an abrupt completion, return\n        //    IteratorClose(iterator, defineStatus).\n        if (mapFn) {\n          A[k] = mapFn.call(T, nextValue, k);\n        }\n        else {\n          A[k] = nextValue;\n        }\n        // xi. Increase k by 1.\n        k++;\n      }\n      // 7. Assert: items is not an Iterable so assume it is\n      //    an array-like object.\n    } else {\n\n      // 8. Let arrayLike be ToObject(items).\n      var arrayLike = Object(items);\n\n      // 9. ReturnIfAbrupt(items).\n      if (items == null) {\n        throw new TypeError(\n          'Array.from requires an array-like object - not null or undefined'\n        );\n      }\n\n      // 10. Let len be ToLength(Get(arrayLike, \"length\")).\n      // 11. ReturnIfAbrupt(len).\n      var len = toLength(arrayLike.length);\n\n      // 12. If IsConstructor(C) is true, then\n      //     a. Let A be Construct(C, «len»).\n      // 13. Else\n      //     a. Let A be ArrayCreate(len).\n      // 14. ReturnIfAbrupt(A).\n      A = isCallable(C) ? Object(new C(len)) : new Array(len);\n\n      // 15. Let k be 0.\n      k = 0;\n      // 16. Repeat, while k < len… (also steps a - h)\n      var kValue;\n      while (k < len) {\n        kValue = arrayLike[k];\n        if (mapFn) {\n          A[k] = mapFn.call(T, kValue, k);\n        }\n        else {\n          A[k] = kValue;\n        }\n        k++;\n      }\n      // 17. Let setStatus be Set(A, \"length\", len, true).\n      // 18. ReturnIfAbrupt(setStatus).\n      A.length = len;\n      // 19. Return A.\n    }\n    return A;\n  };\n})();\n", "module.exports = (typeof Array.from === 'function' ?\n  Array.from :\n  require('./polyfill')\n);\n", "\n/**\n * isArray\n */\n\nvar isArray = Array.isArray;\n\n/**\n * toString\n */\n\nvar str = Object.prototype.toString;\n\n/**\n * Whether or not the given `val`\n * is an array.\n *\n * example:\n *\n *        isArray([]);\n *        // > true\n *        isArray(arguments);\n *        // > false\n *        isArray('');\n *        // > false\n *\n * @param {mixed} val\n * @return {bool}\n */\n\nmodule.exports = isArray || function (val) {\n  return !! val && '[object Array]' == str.call(val);\n};\n", "var _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol ? \"symbol\" : typeof obj; };\n\n/**\n * Returns `true` if provided input is Element.\n * @name isElement\n * @param {*} [input]\n * @returns {boolean}\n */\nexport default function (input) {\n  return input != null && (typeof input === 'undefined' ? 'undefined' : _typeof(input)) === 'object' && input.nodeType === 1 && _typeof(input.style) === 'object' && _typeof(input.ownerDocument) === 'object';\n}", "'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nfunction _interopDefault (ex) { return (ex && (typeof ex === 'object') && 'default' in ex) ? ex['default'] : ex; }\n\nvar arrayFrom = _interopDefault(require('array-from'));\nvar isArray = _interopDefault(require('is-array'));\nvar isElement = _interopDefault(require('iselement'));\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol ? \"symbol\" : typeof obj; };\n\n/**\n * Returns `true` if provided input is Element.\n * @name isElement\n * @param {*} [input]\n * @returns {boolean}\n */\nvar isElement$1 = function (input) {\n  return input != null && (typeof input === 'undefined' ? 'undefined' : _typeof(input)) === 'object' && input.nodeType === 1 && _typeof(input.style) === 'object' && _typeof(input.ownerDocument) === 'object';\n};\n\nfunction select(selector){\n    if(typeof selector === 'string'){\n        try{\n            return document.querySelector(selector);\n        }catch(e){\n            throw e;\n        }\n    }else if(isElement(selector)){\n        return selector;\n    }\n}\n\nfunction selectAll(selector){\n    if(typeof selector === 'string'){\n        return Array.prototype.slice.apply(\n            document.querySelectorAll(selector)\n        );\n    }else if(isArray(selector)){\n        return selector.map(select);\n    }else if('length' in selector){\n        return arrayFrom(selector).map(select);\n    }\n}\n\nfunction indexOfElement(elements, element){\n    element = resolveElement(element, true);\n    if(!isElement$1(element)) { return -1; }\n    for(var i=0; i<elements.length; i++){\n        if(elements[i] === element){\n            return i;\n        }\n    }\n    return -1;\n}\n\nfunction hasElement(elements, element){\n    return -1 !== indexOfElement(elements, element);\n}\n\nfunction domListOf(arr){\n\n    if(!arr) { return []; }\n\n    try{\n        if(typeof arr === 'string'){\n            return arrayFrom(document.querySelectorAll(arr));\n        }else if(isArray(arr)){\n            return arr.map(resolveElement);\n        }else{\n            if(typeof arr.length === 'undefined'){\n                return [resolveElement(arr)];\n            }\n\n            return arrayFrom(arr, resolveElement);\n\n        }\n    }catch(e){\n        throw new Error(e);\n    }\n\n}\n\nfunction concatElementLists(){\n    var lists = [], len = arguments.length;\n    while ( len-- ) lists[ len ] = arguments[ len ];\n\n    return lists.reduce(function (last, list){\n        return list.length ? last : last.concat(domListOf(list));\n    }, []);\n}\n\nfunction pushElements(elements, toAdd){\n\n    for(var i=0; i<toAdd.length; i++){\n        if(!hasElement(elements, toAdd[i]))\n            { elements.push(toAdd[i]); }\n    }\n\n    return toAdd;\n}\n\nfunction addElements(elements){\n    var toAdd = [], len = arguments.length - 1;\n    while ( len-- > 0 ) toAdd[ len ] = arguments[ len + 1 ];\n\n    toAdd = toAdd.map(resolveElement);\n    return pushElements(elements, toAdd);\n}\n\nfunction removeElements(elements){\n    var toRemove = [], len = arguments.length - 1;\n    while ( len-- > 0 ) toRemove[ len ] = arguments[ len + 1 ];\n\n    return toRemove.map(resolveElement).reduce(function (last, e){\n\n        var index = indexOfElement(elements, e);\n\n        if(index !== -1)\n            { return last.concat(elements.splice(index, 1)); }\n        return last;\n    }, []);\n}\n\nfunction resolveElement(element, noThrow){\n    if(typeof element === 'string'){\n        try{\n            return document.querySelector(element);\n        }catch(e){\n            throw e;\n        }\n\n    }\n\n    if(!isElement$1(element) && !noThrow){\n        throw new TypeError((element + \" is not a DOM element.\"));\n    }\n    return element;\n}\n\nexports.indexOfElement = indexOfElement;\nexports.hasElement = hasElement;\nexports.domListOf = domListOf;\nexports.concatElementLists = concatElementLists;\nexports.addElements = addElements;\nexports.removeElements = removeElements;\nexports.resolveElement = resolveElement;\nexports.select = select;\nexports.selectAll = selectAll;\n\n", "'use strict';\n\nvar typeFunc = require('type-func');\n\nfunction createPointCB(object, options) {\n\n    // A persistent object (as opposed to returned object) is used to save memory\n    // This is good to prevent layout thrashing, or for games, and such\n\n    // NOTE\n    // This uses IE fixes which should be OK to remove some day. :)\n    // Some speed will be gained by removal of these.\n\n    // pointCB should be saved in a variable on return\n    // This allows the usage of element.removeEventListener\n\n    options = options || {};\n\n    var allowUpdate = typeFunc.boolean(options.allowUpdate, true);\n\n    /*if(typeof options.allowUpdate === 'function'){\n        allowUpdate = options.allowUpdate;\n    }else{\n        allowUpdate = function(){return true;};\n    }*/\n\n    return function pointCB(event) {\n\n        event = event || window.event; // IE-ism\n        object.target = event.target || event.srcElement || event.originalTarget;\n        object.element = this;\n        object.type = event.type;\n\n        if (!allowUpdate(event)) {\n            return;\n        }\n\n        // Support touch\n        // http://www.creativebloq.com/javascript/make-your-site-work-touch-devices-51411644\n\n        if (event.targetTouches) {\n            object.x = event.targetTouches[0].clientX;\n            object.y = event.targetTouches[0].clientY;\n            object.pageX = event.targetTouches[0].pageX;\n            object.pageY = event.targetTouches[0].pageY;\n            object.screenX = event.targetTouches[0].screenX;\n            object.screenY = event.targetTouches[0].screenY;\n        } else {\n\n            // If pageX/Y aren't available and clientX/Y are,\n            // calculate pageX/Y - logic taken from jQuery.\n            // (This is to support old IE)\n            // NOTE Hopefully this can be removed soon.\n\n            if (event.pageX === null && event.clientX !== null) {\n                var eventDoc = event.target && event.target.ownerDocument || document;\n                var doc = eventDoc.documentElement;\n                var body = eventDoc.body;\n\n                object.pageX = event.clientX + (doc && doc.scrollLeft || body && body.scrollLeft || 0) - (doc && doc.clientLeft || body && body.clientLeft || 0);\n                object.pageY = event.clientY + (doc && doc.scrollTop || body && body.scrollTop || 0) - (doc && doc.clientTop || body && body.clientTop || 0);\n            } else {\n                object.pageX = event.pageX;\n                object.pageY = event.pageY;\n            }\n\n            // pageX, and pageY change with page scroll\n            // so we're not going to use those for x, and y.\n            // NOTE Most browsers also alias clientX/Y with x/y\n            // so that's something to consider down the road.\n\n            object.x = event.clientX;\n            object.y = event.clientY;\n\n            object.screenX = event.screenX;\n            object.screenY = event.screenY;\n        }\n\n        object.clientX = object.x;\n        object.clientY = object.y;\n    };\n\n    //NOTE Remember accessibility, Aria roles, and labels.\n}\n\n/*\ngit remote add origin https://github.com/hollowdoor/create_point_cb.git\ngit push -u origin master\n*/\n\nmodule.exports = createPointCB;\n\n", "'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nfunction _interopDefault (ex) { return (ex && (typeof ex === 'object') && 'default' in ex) ? ex['default'] : ex; }\n\nvar createPointCb = _interopDefault(require('create-point-cb'));\n\nfunction createWindowRect() {\n    var props = {\n        top: { value: 0, enumerable: true },\n        left: { value: 0, enumerable: true },\n        right: { value: window.innerWidth, enumerable: true },\n        bottom: { value: window.innerHeight, enumerable: true },\n        width: { value: window.innerWidth, enumerable: true },\n        height: { value: window.innerHeight, enumerable: true },\n        x: { value: 0, enumerable: true },\n        y: { value: 0, enumerable: true }\n    };\n\n    if (Object.create) {\n        return Object.create({}, props);\n    } else {\n        var rect = {};\n        Object.defineProperties(rect, props);\n        return rect;\n    }\n}\n\nfunction getClientRect(el) {\n    if (el === window) {\n        return createWindowRect();\n    } else {\n        try {\n            var rect = el.getBoundingClientRect();\n            if (rect.x === undefined) {\n                rect.x = rect.left;\n                rect.y = rect.top;\n            }\n            return rect;\n        } catch (e) {\n            throw new TypeError(\"Can't call getBoundingClientRect on \" + el);\n        }\n    }\n}\n\nfunction pointInside(point, el) {\n    var rect = getClientRect(el);\n    return point.y > rect.top && point.y < rect.bottom && point.x > rect.left && point.x < rect.right;\n}\n\nexports.createPointCB = createPointCb;\nexports.getClientRect = getClientRect;\nexports.pointInside = pointInside;\n\n", "'use strict';\n\nvar objectCreate = void 0;\nif (typeof Object.create != 'function') {\n  objectCreate = function (undefined) {\n    var Temp = function Temp() {};\n    return function (prototype, propertiesObject) {\n      if (prototype !== Object(prototype) && prototype !== null) {\n        throw TypeError('Argument must be an object, or null');\n      }\n      Temp.prototype = prototype || {};\n      var result = new Temp();\n      Temp.prototype = null;\n      if (propertiesObject !== undefined) {\n        Object.defineProperties(result, propertiesObject);\n      }\n\n      // to imitate the case of Object.create(null)\n      if (prototype === null) {\n        result.__proto__ = null;\n      }\n      return result;\n    };\n  }();\n} else {\n  objectCreate = Object.create;\n}\n\nvar objectCreate$1 = objectCreate;\n\nvar mouseEventProps = ['altKey', 'button', 'buttons', 'clientX', 'clientY', 'ctrlKey', 'metaKey', 'movementX', 'movementY', 'offsetX', 'offsetY', 'pageX', 'pageY', 'region', 'relatedTarget', 'screenX', 'screenY', 'shiftKey', 'which', 'x', 'y'];\n\nfunction createDispatcher(element) {\n\n    var defaultSettings = {\n        screenX: 0,\n        screenY: 0,\n        clientX: 0,\n        clientY: 0,\n        ctrlKey: false,\n        shiftKey: false,\n        altKey: false,\n        metaKey: false,\n        button: 0,\n        buttons: 1,\n        relatedTarget: null,\n        region: null\n    };\n\n    if (element !== undefined) {\n        element.addEventListener('mousemove', onMove);\n    }\n\n    function onMove(e) {\n        for (var i = 0; i < mouseEventProps.length; i++) {\n            defaultSettings[mouseEventProps[i]] = e[mouseEventProps[i]];\n        }\n    }\n\n    var dispatch = function () {\n        if (MouseEvent) {\n            return function m1(element, initMove, data) {\n                var evt = new MouseEvent('mousemove', createMoveInit(defaultSettings, initMove));\n\n                //evt.dispatched = 'mousemove';\n                setSpecial(evt, data);\n\n                return element.dispatchEvent(evt);\n            };\n        } else if (typeof document.createEvent === 'function') {\n            return function m2(element, initMove, data) {\n                var settings = createMoveInit(defaultSettings, initMove);\n                var evt = document.createEvent('MouseEvents');\n\n                evt.initMouseEvent(\"mousemove\", true, //can bubble\n                true, //cancelable\n                window, //view\n                0, //detail\n                settings.screenX, //0, //screenX\n                settings.screenY, //0, //screenY\n                settings.clientX, //80, //clientX\n                settings.clientY, //20, //clientY\n                settings.ctrlKey, //false, //ctrlKey\n                settings.altKey, //false, //altKey\n                settings.shiftKey, //false, //shiftKey\n                settings.metaKey, //false, //metaKey\n                settings.button, //0, //button\n                settings.relatedTarget //null //relatedTarget\n                );\n\n                //evt.dispatched = 'mousemove';\n                setSpecial(evt, data);\n\n                return element.dispatchEvent(evt);\n            };\n        } else if (typeof document.createEventObject === 'function') {\n            return function m3(element, initMove, data) {\n                var evt = document.createEventObject();\n                var settings = createMoveInit(defaultSettings, initMove);\n                for (var name in settings) {\n                    evt[name] = settings[name];\n                }\n\n                //evt.dispatched = 'mousemove';\n                setSpecial(evt, data);\n\n                return element.dispatchEvent(evt);\n            };\n        }\n    }();\n\n    function destroy() {\n        if (element) element.removeEventListener('mousemove', onMove, false);\n        defaultSettings = null;\n    }\n\n    return {\n        destroy: destroy,\n        dispatch: dispatch\n    };\n}\n\nfunction createMoveInit(defaultSettings, initMove) {\n    initMove = initMove || {};\n    var settings = objectCreate$1(defaultSettings);\n    for (var i = 0; i < mouseEventProps.length; i++) {\n        if (initMove[mouseEventProps[i]] !== undefined) settings[mouseEventProps[i]] = initMove[mouseEventProps[i]];\n    }\n\n    return settings;\n}\n\nfunction setSpecial(e, data) {\n    console.log('data ', data);\n    e.data = data || {};\n    e.dispatched = 'mousemove';\n}\n\n/*\nhttp://marcgrabanski.com/simulating-mouse-click-events-in-javascript/\n*/\n\nmodule.exports = createDispatcher;\n\n", "'use strict';\n\nfunction _interopDefault (ex) { return (ex && (typeof ex === 'object') && 'default' in ex) ? ex['default'] : ex; }\n\nvar typeFunc = require('type-func');\nvar animationFramePolyfill = require('animation-frame-polyfill');\nvar domSet = require('dom-set');\nvar domPlane = require('dom-plane');\nvar mousemoveDispatcher = _interopDefault(require('dom-mousemove-dispatcher'));\n\nfunction AutoScroller(elements, options){\n    if ( options === void 0 ) options = {};\n\n    var self = this;\n    var maxSpeed = 4, scrolling = false;\n\n    this.margin = options.margin || -1;\n    //this.scrolling = false;\n    this.scrollWhenOutside = options.scrollWhenOutside || false;\n\n    var point = {},\n        pointCB = domPlane.createPointCB(point),\n        dispatcher = mousemoveDispatcher(),\n        down = false;\n\n    window.addEventListener('mousemove', pointCB, false);\n    window.addEventListener('touchmove', pointCB, false);\n\n    if(!isNaN(options.maxSpeed)){\n        maxSpeed = options.maxSpeed;\n    }\n\n    this.autoScroll = typeFunc.boolean(options.autoScroll);\n    this.syncMove = typeFunc.boolean(options.syncMove, false);\n\n    this.destroy = function(forceCleanAnimation) {\n        window.removeEventListener('mousemove', pointCB, false);\n        window.removeEventListener('touchmove', pointCB, false);\n        window.removeEventListener('mousedown', onDown, false);\n        window.removeEventListener('touchstart', onDown, false);\n        window.removeEventListener('mouseup', onUp, false);\n        window.removeEventListener('touchend', onUp, false);\n        window.removeEventListener('pointerup', onUp, false);\n        window.removeEventListener('mouseleave', onMouseOut, false);\n\n        window.removeEventListener('mousemove', onMove, false);\n        window.removeEventListener('touchmove', onMove, false);\n\n        window.removeEventListener('scroll', setScroll, true);\n        elements = [];\n        if(forceCleanAnimation){\n          cleanAnimation();\n        }\n    };\n\n    this.add = function(){\n        var element = [], len = arguments.length;\n        while ( len-- ) element[ len ] = arguments[ len ];\n\n        domSet.addElements.apply(void 0, [ elements ].concat( element ));\n        return this;\n    };\n\n    this.remove = function(){\n        var element = [], len = arguments.length;\n        while ( len-- ) element[ len ] = arguments[ len ];\n\n        return domSet.removeElements.apply(void 0, [ elements ].concat( element ));\n    };\n\n    var hasWindow = null, windowAnimationFrame;\n\n    if(Object.prototype.toString.call(elements) !== '[object Array]'){\n        elements = [elements];\n    }\n\n    (function(temp){\n        elements = [];\n        temp.forEach(function(element){\n            if(element === window){\n                hasWindow = window;\n            }else{\n                self.add(element);\n            }\n        });\n    }(elements));\n\n    Object.defineProperties(this, {\n        down: {\n            get: function(){ return down; }\n        },\n        maxSpeed: {\n            get: function(){ return maxSpeed; }\n        },\n        point: {\n            get: function(){ return point; }\n        },\n        scrolling: {\n            get: function(){ return scrolling; }\n        }\n    });\n\n    var n = 0, current = null, animationFrame;\n\n    window.addEventListener('mousedown', onDown, false);\n    window.addEventListener('touchstart', onDown, false);\n    window.addEventListener('mouseup', onUp, false);\n    window.addEventListener('touchend', onUp, false);\n\n    /*\n    IE does not trigger mouseup event when scrolling.\n    It is a known issue that Microsoft won't fix.\n    https://connect.microsoft.com/IE/feedback/details/783058/scrollbar-trigger-mousedown-but-not-mouseup\n    IE supports pointer events instead\n    */\n    window.addEventListener('pointerup', onUp, false);\n\n    window.addEventListener('mousemove', onMove, false);\n    window.addEventListener('touchmove', onMove, false);\n\n    window.addEventListener('mouseleave', onMouseOut, false);\n\n    window.addEventListener('scroll', setScroll, true);\n\n    function setScroll(e){\n\n        for(var i=0; i<elements.length; i++){\n            if(elements[i] === e.target){\n                scrolling = true;\n                break;\n            }\n        }\n\n        if(scrolling){\n            animationFramePolyfill.requestAnimationFrame(function (){ return scrolling = false; });\n        }\n    }\n\n    function onDown(){\n        down = true;\n    }\n\n    function onUp(){\n        down = false;\n        cleanAnimation();\n    }\n    function cleanAnimation(){\n      animationFramePolyfill.cancelAnimationFrame(animationFrame);\n      animationFramePolyfill.cancelAnimationFrame(windowAnimationFrame);\n    }\n    function onMouseOut(){\n        down = false;\n    }\n\n    function getTarget(target){\n        if(!target){\n            return null;\n        }\n\n        if(current === target){\n            return target;\n        }\n\n        if(domSet.hasElement(elements, target)){\n            return target;\n        }\n\n        while(target = target.parentNode){\n            if(domSet.hasElement(elements, target)){\n                return target;\n            }\n        }\n\n        return null;\n    }\n\n    function getElementUnderPoint(){\n        var underPoint = null;\n\n        for(var i=0; i<elements.length; i++){\n            if(inside(point, elements[i])){\n                underPoint = elements[i];\n            }\n        }\n\n        return underPoint;\n    }\n\n\n    function onMove(event){\n\n        if(!self.autoScroll()) { return; }\n\n        if(event['dispatched']){ return; }\n\n        var target = event.target, body = document.body;\n\n        if(current && !inside(point, current)){\n            if(!self.scrollWhenOutside){\n                current = null;\n            }\n        }\n\n        if(target && target.parentNode === body){\n            //The special condition to improve speed.\n            target = getElementUnderPoint();\n        }else{\n            target = getTarget(target);\n\n            if(!target){\n                target = getElementUnderPoint();\n            }\n        }\n\n\n        if(target && target !== current){\n            current = target;\n        }\n\n        if(hasWindow){\n            animationFramePolyfill.cancelAnimationFrame(windowAnimationFrame);\n            windowAnimationFrame = animationFramePolyfill.requestAnimationFrame(scrollWindow);\n        }\n\n\n        if(!current){\n            return;\n        }\n\n        animationFramePolyfill.cancelAnimationFrame(animationFrame);\n        animationFrame = animationFramePolyfill.requestAnimationFrame(scrollTick);\n    }\n\n    function scrollWindow(){\n        autoScroll(hasWindow);\n\n        animationFramePolyfill.cancelAnimationFrame(windowAnimationFrame);\n        windowAnimationFrame = animationFramePolyfill.requestAnimationFrame(scrollWindow);\n    }\n\n    function scrollTick(){\n\n        if(!current){\n            return;\n        }\n\n        autoScroll(current);\n\n        animationFramePolyfill.cancelAnimationFrame(animationFrame);\n        animationFrame = animationFramePolyfill.requestAnimationFrame(scrollTick);\n\n    }\n\n\n    function autoScroll(el){\n        var rect = domPlane.getClientRect(el), scrollx, scrolly;\n\n        if(point.x < rect.left + self.margin){\n            scrollx = Math.floor(\n                Math.max(-1, (point.x - rect.left) / self.margin - 1) * self.maxSpeed\n            );\n        }else if(point.x > rect.right - self.margin){\n            scrollx = Math.ceil(\n                Math.min(1, (point.x - rect.right) / self.margin + 1) * self.maxSpeed\n            );\n        }else{\n            scrollx = 0;\n        }\n\n        if(point.y < rect.top + self.margin){\n            scrolly = Math.floor(\n                Math.max(-1, (point.y - rect.top) / self.margin - 1) * self.maxSpeed\n            );\n        }else if(point.y > rect.bottom - self.margin){\n            scrolly = Math.ceil(\n                Math.min(1, (point.y - rect.bottom) / self.margin + 1) * self.maxSpeed\n            );\n        }else{\n            scrolly = 0;\n        }\n\n        if(self.syncMove()){\n            /*\n            Notes about mousemove event dispatch.\n            screen(X/Y) should need to be updated.\n            Some other properties might need to be set.\n            Keep the syncMove option default false until all inconsistencies are taken care of.\n            */\n            dispatcher.dispatch(el, {\n                pageX: point.pageX + scrollx,\n                pageY: point.pageY + scrolly,\n                clientX: point.x + scrollx,\n                clientY: point.y + scrolly\n            });\n        }\n\n        setTimeout(function (){\n\n            if(scrolly){\n                scrollY(el, scrolly);\n            }\n\n            if(scrollx){\n                scrollX(el, scrollx);\n            }\n\n        });\n    }\n\n    function scrollY(el, amount){\n        if(el === window){\n            window.scrollTo(el.pageXOffset, el.pageYOffset + amount);\n        }else{\n            el.scrollTop += amount;\n        }\n    }\n\n    function scrollX(el, amount){\n        if(el === window){\n            window.scrollTo(el.pageXOffset + amount, el.pageYOffset);\n        }else{\n            el.scrollLeft += amount;\n        }\n    }\n\n}\n\nfunction AutoScrollerFactory(element, options){\n    return new AutoScroller(element, options);\n}\n\nfunction inside(point, el, rect){\n    if(!rect){\n        return domPlane.pointInside(point, el);\n    }else{\n        return (point.y > rect.top && point.y < rect.bottom &&\n                point.x > rect.left && point.x < rect.right);\n    }\n}\n\n/*\ngit remote add origin https://github.com/hollowdoor/dom_autoscroller.git\ngit push -u origin master\n*/\n\nmodule.exports = AutoScrollerFactory;\n\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAE5D,aAAS,OAAO,GAAG,GAAG;AAClB,UAAI,OAAO,MAAM,aAAa;AAC1B,eAAO,OAAO,MAAM,cAAc,IAAI;AAAA,MAC1C;AAEA,aAAO;AAAA,IACX;AACA,aAAS,QAAQ,MAAM,KAAK;AAExB,aAAO,OAAO,MAAM,GAAG;AAEvB,UAAI,OAAO,SAAS,YAAY;AAC5B,eAAO,SAAS,IAAI;AAChB,mBAAS,OAAO,UAAU,QAAQ,OAAO,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACjF,iBAAK,IAAI,IAAI,UAAU,IAAI;AAAA,UAC/B;AAEA,iBAAO,CAAC,CAAC,KAAK,MAAM,MAAM,IAAI;AAAA,QAClC;AAAA,MACJ;AAEA,aAAO,CAAC,CAAC,OAAO,WAAY;AACxB,eAAO;AAAA,MACX,IAAI,WAAY;AACZ,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,aAAS,QAAQ,MAAM,KAAK;AAExB,aAAO,OAAO,MAAM,GAAG;AAEvB,UAAI,OAAO,SAAS,YAAY;AAC5B,eAAO,SAAS,IAAI;AAChB,mBAAS,QAAQ,UAAU,QAAQ,OAAO,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACvF,iBAAK,KAAK,IAAI,UAAU,KAAK;AAAA,UACjC;AAEA,cAAI,IAAI,SAAS,KAAK,MAAM,MAAM,IAAI,GAAG,EAAE;AAC3C,iBAAO,KAAK,IAAI,IAAI;AAAA,QACxB;AAAA,MACJ;AAEA,aAAO,SAAS,MAAM,EAAE;AAExB,aAAO,QAAQ,OAAO,WAAY;AAC9B,eAAO;AAAA,MACX,IAAI,WAAY;AACZ,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,aAAS,OAAO,MAAM,KAAK;AAEvB,aAAO,OAAO,MAAM,GAAG;AAEvB,UAAI,OAAO,SAAS,YAAY;AAC5B,eAAO,SAAS,IAAI;AAChB,mBAAS,QAAQ,UAAU,QAAQ,OAAO,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACvF,iBAAK,KAAK,IAAI,UAAU,KAAK;AAAA,UACjC;AAEA,iBAAO,KAAK,KAAK,MAAM,MAAM,IAAI;AAAA,QACrC;AAAA,MACJ;AAEA,aAAO,KAAK;AAEZ,aAAO,WAAY;AACf,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,YAAQ,UAAU;AAClB,YAAQ,UAAU;AAClB,YAAQ,SAAS;AAAA;AAAA;;;AC/EjB;AAAA;AAAA;AAAA;AAAA;AAAA,IAAI,QACO,uBAqBA;AAtBX;AAAA;AAAA,IAAI,SAAS,CAAC,UAAU,OAAO,MAAM,GAAG;AACjC,IAAI,yBAAwB,WAAY;AAC7C,eAAS,IAAI,GAAG,QAAQ,OAAO,QAAQ,IAAI,SAAS,CAAC,OAAO,uBAAuB,EAAE,GAAG;AACtF,eAAO,wBAAwB,OAAO,OAAO,CAAC,IAAI,uBAAuB;AAAA,MAC3E;AAEA,UAAI,CAAC,OAAO,uBAAuB;AACjC,YAAI,WAAW;AAEf,eAAO,wBAAwB,SAAU,UAAU;AACjD,cAAI,OAAM,oBAAI,KAAK,GAAE,QAAQ;AAC7B,cAAI,MAAM,KAAK,IAAI,GAAG,KAAK,MAAM,QAAQ;AACzC,cAAI,QAAQ,OAAO,WAAW,WAAY;AACxC,mBAAO,SAAS,MAAM,GAAG;AAAA,UAC3B,GAAG,GAAG;AACN,qBAAW,MAAM;AACjB,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO,OAAO,sBAAsB,KAAK,MAAM;AAAA,IACjD,GAAE;AACK,IAAI,wBAAuB,WAAY;AAC5C,eAAS,IAAI,GAAG,QAAQ,OAAO,QAAQ,IAAI,SAAS,CAAC,OAAO,sBAAsB,EAAE,GAAG;AACrF,eAAO,uBAAuB,OAAO,OAAO,CAAC,IAAI,sBAAsB,KAAK,OAAO,OAAO,CAAC,IAAI,6BAA6B;AAAA,MAC9H;AAEA,UAAI,CAAC,OAAO,sBAAsB;AAChC,eAAO,uBAAuB,SAAU,OAAO;AAC7C,iBAAO,aAAa,KAAK;AAAA,QAC3B;AAAA,MACF;AAEA,aAAO,OAAO,qBAAqB,KAAK,MAAM;AAAA,IAChD,GAAE;AAAA;AAAA;;;AClCF;AAAA;AAEA,WAAO,WAAW,WAAW;AAC3B,UAAI,aAAa,SAAS,IAAI;AAC5B,eAAO,OAAO,OAAO;AAAA,MACvB;AACA,UAAI,YAAY,SAAU,OAAO;AAC/B,YAAI,SAAS,OAAO,KAAK;AACzB,YAAI,MAAM,MAAM,GAAG;AAAE,iBAAO;AAAA,QAAG;AAC/B,YAAI,WAAW,KAAK,CAAC,SAAS,MAAM,GAAG;AAAE,iBAAO;AAAA,QAAQ;AACxD,gBAAQ,SAAS,IAAI,IAAI,MAAM,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC;AAAA,MAC5D;AACA,UAAI,iBAAiB,KAAK,IAAI,GAAG,EAAE,IAAI;AACvC,UAAI,WAAW,SAAU,OAAO;AAC9B,YAAI,MAAM,UAAU,KAAK;AACzB,eAAO,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG,cAAc;AAAA,MAClD;AACA,UAAI,eAAe,SAAS,OAAO;AACjC,YAAG,SAAS,MAAM;AAChB,cAAG,CAAC,UAAS,UAAS,WAAU,QAAQ,EAAE,QAAQ,OAAO,KAAK,IAAI,IAAG;AACnE,mBAAO,OAAO;AAAA,UAChB,WACG,OAAO,WAAW,eAClB,cAAc,UACd,OAAO,YAAY,OACpB;AACA,mBAAO,OAAO;AAAA,UAChB,WAES,gBAAgB,OAAO;AAC9B,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,UAAI,YAAY,SAAS,GAAG,GAAG;AAE7B,YAAI,KAAK,QAAQ,KAAK,MAAM;AAE1B,cAAI,OAAO,EAAE,CAAC;AAGd,cAAG,QAAQ,MAAM;AACf,mBAAO;AAAA,UACT;AAEA,cAAI,CAAC,WAAW,IAAI,GAAG;AACrB,kBAAM,IAAI,UAAU,OAAO,oBAAoB;AAAA,UACjD;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AACA,UAAI,eAAe,SAAS,UAAU;AAGpC,YAAI,SAAS,SAAS,KAAK;AAG3B,YAAI,OAAO,QAAQ,OAAO,IAAI;AAE9B,YAAG,MAAM;AACP,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAGA,aAAO,SAAS,KAAK,OAA8B;AACjD;AAGA,YAAI,IAAI;AAGR,YAAI,QAAQ,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AAElD,YAAI;AACJ,YAAI,OAAO,UAAU,aAAa;AAGhC,cAAI,CAAC,WAAW,KAAK,GAAG;AACtB,kBAAM,IAAI;AAAA,cACR;AAAA,YACF;AAAA,UACF;AAIA,cAAI,UAAU,SAAS,GAAG;AACxB,gBAAI,UAAU,CAAC;AAAA,UACjB;AAAA,QAEF;AAEA,YAAI,GAAG;AAIP,YAAI,gBAAgB,UAAU,OAAO,aAAa,KAAK,CAAC;AAGxD,YAAI,kBAAkB,QAAQ;AAQ5B,cAAI,WAAW,CAAC,IAAI,OAAO,IAAI,EAAE,CAAC,IAAI,CAAC;AAGvC,cAAI,WAAW,cAAc,KAAK,KAAK;AAGvC,cAAI,YAAY,MAAM;AACpB,kBAAM,IAAI;AAAA,cACR;AAAA,YACF;AAAA,UACF;AAGA,cAAI;AAGJ,cAAI,MAAM;AACV,iBAAO,MAAM;AAIX,mBAAO,aAAa,QAAQ;AAG5B,gBAAI,CAAC,MAAM;AAIT,gBAAE,SAAS;AAGX,qBAAO;AAAA,YACT;AAGA,wBAAY,KAAK;AAYjB,gBAAI,OAAO;AACT,gBAAE,CAAC,IAAI,MAAM,KAAK,GAAG,WAAW,CAAC;AAAA,YACnC,OACK;AACH,gBAAE,CAAC,IAAI;AAAA,YACT;AAEA;AAAA,UACF;AAAA,QAGF,OAAO;AAGL,cAAI,YAAY,OAAO,KAAK;AAG5B,cAAI,SAAS,MAAM;AACjB,kBAAM,IAAI;AAAA,cACR;AAAA,YACF;AAAA,UACF;AAIA,cAAI,MAAM,SAAS,UAAU,MAAM;AAOnC,cAAI,WAAW,CAAC,IAAI,OAAO,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,MAAM,GAAG;AAGtD,cAAI;AAEJ,cAAI;AACJ,iBAAO,IAAI,KAAK;AACd,qBAAS,UAAU,CAAC;AACpB,gBAAI,OAAO;AACT,gBAAE,CAAC,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC;AAAA,YAChC,OACK;AACH,gBAAE,CAAC,IAAI;AAAA,YACT;AACA;AAAA,UACF;AAGA,YAAE,SAAS;AAAA,QAEb;AACA,eAAO;AAAA,MACT;AAAA,IACF,GAAG;AAAA;AAAA;;;ACnNH;AAAA;AAAA,WAAO,UAAW,OAAO,MAAM,SAAS,aACtC,MAAM,OACN;AAAA;AAAA;;;ACFF;AAAA;AAKA,QAAI,UAAU,MAAM;AAMpB,QAAI,MAAM,OAAO,UAAU;AAmB3B,WAAO,UAAU,WAAW,SAAU,KAAK;AACzC,aAAO,CAAC,CAAE,OAAO,oBAAoB,IAAI,KAAK,GAAG;AAAA,IACnD;AAAA;AAAA;;;AChCA;AAAA;AAAA;AAAA;AAQe,SAAR,eAAkB,OAAO;AAC9B,SAAO,SAAS,SAAS,OAAO,UAAU,cAAc,cAAc,QAAQ,KAAK,OAAO,YAAY,MAAM,aAAa,KAAK,QAAQ,MAAM,KAAK,MAAM,YAAY,QAAQ,MAAM,aAAa,MAAM;AACtM;AAVA,IAAI;AAAJ;AAAA;AAAA,IAAI,UAAU,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,WAAW,SAAU,KAAK;AAAE,aAAO,OAAO;AAAA,IAAK,IAAI,SAAU,KAAK;AAAE,aAAO,OAAO,OAAO,WAAW,cAAc,IAAI,gBAAgB,SAAS,WAAW,OAAO;AAAA,IAAK;AAAA;AAAA;;;ACA/O,IAAAA,kBAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAE5D,aAAS,gBAAiB,IAAI;AAAE,aAAQ,MAAO,OAAO,OAAO,YAAa,aAAa,KAAM,GAAG,SAAS,IAAI;AAAA,IAAI;AAEjH,QAAI,YAAY,gBAAgB,oBAAqB;AACrD,QAAI,UAAU,gBAAgB,kBAAmB;AACjD,QAAI,YAAY,gBAAgB,6CAAoB;AAEpD,QAAIC,WAAU,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,WAAW,SAAU,KAAK;AAAE,aAAO,OAAO;AAAA,IAAK,IAAI,SAAU,KAAK;AAAE,aAAO,OAAO,OAAO,WAAW,cAAc,IAAI,gBAAgB,SAAS,WAAW,OAAO;AAAA,IAAK;AAQ/O,QAAI,cAAc,SAAU,OAAO;AACjC,aAAO,SAAS,SAAS,OAAO,UAAU,cAAc,cAAcA,SAAQ,KAAK,OAAO,YAAY,MAAM,aAAa,KAAKA,SAAQ,MAAM,KAAK,MAAM,YAAYA,SAAQ,MAAM,aAAa,MAAM;AAAA,IACtM;AAEA,aAAS,OAAO,UAAS;AACrB,UAAG,OAAO,aAAa,UAAS;AAC5B,YAAG;AACC,iBAAO,SAAS,cAAc,QAAQ;AAAA,QAC1C,SAAO,GAAE;AACL,gBAAM;AAAA,QACV;AAAA,MACJ,WAAS,UAAU,QAAQ,GAAE;AACzB,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,aAAS,UAAU,UAAS;AACxB,UAAG,OAAO,aAAa,UAAS;AAC5B,eAAO,MAAM,UAAU,MAAM;AAAA,UACzB,SAAS,iBAAiB,QAAQ;AAAA,QACtC;AAAA,MACJ,WAAS,QAAQ,QAAQ,GAAE;AACvB,eAAO,SAAS,IAAI,MAAM;AAAA,MAC9B,WAAS,YAAY,UAAS;AAC1B,eAAO,UAAU,QAAQ,EAAE,IAAI,MAAM;AAAA,MACzC;AAAA,IACJ;AAEA,aAAS,eAAe,UAAU,SAAQ;AACtC,gBAAU,eAAe,SAAS,IAAI;AACtC,UAAG,CAAC,YAAY,OAAO,GAAG;AAAE,eAAO;AAAA,MAAI;AACvC,eAAQ,IAAE,GAAG,IAAE,SAAS,QAAQ,KAAI;AAChC,YAAG,SAAS,CAAC,MAAM,SAAQ;AACvB,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAEA,aAAS,WAAW,UAAU,SAAQ;AAClC,aAAO,OAAO,eAAe,UAAU,OAAO;AAAA,IAClD;AAEA,aAAS,UAAU,KAAI;AAEnB,UAAG,CAAC,KAAK;AAAE,eAAO,CAAC;AAAA,MAAG;AAEtB,UAAG;AACC,YAAG,OAAO,QAAQ,UAAS;AACvB,iBAAO,UAAU,SAAS,iBAAiB,GAAG,CAAC;AAAA,QACnD,WAAS,QAAQ,GAAG,GAAE;AAClB,iBAAO,IAAI,IAAI,cAAc;AAAA,QACjC,OAAK;AACD,cAAG,OAAO,IAAI,WAAW,aAAY;AACjC,mBAAO,CAAC,eAAe,GAAG,CAAC;AAAA,UAC/B;AAEA,iBAAO,UAAU,KAAK,cAAc;AAAA,QAExC;AAAA,MACJ,SAAO,GAAE;AACL,cAAM,IAAI,MAAM,CAAC;AAAA,MACrB;AAAA,IAEJ;AAEA,aAAS,qBAAoB;AACzB,UAAI,QAAQ,CAAC,GAAG,MAAM,UAAU;AAChC,aAAQ,MAAQ,OAAO,GAAI,IAAI,UAAW,GAAI;AAE9C,aAAO,MAAM,OAAO,SAAU,MAAM,MAAK;AACrC,eAAO,KAAK,SAAS,OAAO,KAAK,OAAO,UAAU,IAAI,CAAC;AAAA,MAC3D,GAAG,CAAC,CAAC;AAAA,IACT;AAEA,aAAS,aAAa,UAAU,OAAM;AAElC,eAAQ,IAAE,GAAG,IAAE,MAAM,QAAQ,KAAI;AAC7B,YAAG,CAAC,WAAW,UAAU,MAAM,CAAC,CAAC,GAC7B;AAAE,mBAAS,KAAK,MAAM,CAAC,CAAC;AAAA,QAAG;AAAA,MACnC;AAEA,aAAO;AAAA,IACX;AAEA,aAAS,YAAY,UAAS;AAC1B,UAAI,QAAQ,CAAC,GAAG,MAAM,UAAU,SAAS;AACzC,aAAQ,QAAQ,EAAI,OAAO,GAAI,IAAI,UAAW,MAAM,CAAE;AAEtD,cAAQ,MAAM,IAAI,cAAc;AAChC,aAAO,aAAa,UAAU,KAAK;AAAA,IACvC;AAEA,aAAS,eAAe,UAAS;AAC7B,UAAI,WAAW,CAAC,GAAG,MAAM,UAAU,SAAS;AAC5C,aAAQ,QAAQ,EAAI,UAAU,GAAI,IAAI,UAAW,MAAM,CAAE;AAEzD,aAAO,SAAS,IAAI,cAAc,EAAE,OAAO,SAAU,MAAM,GAAE;AAEzD,YAAI,QAAQ,eAAe,UAAU,CAAC;AAEtC,YAAG,UAAU,IACT;AAAE,iBAAO,KAAK,OAAO,SAAS,OAAO,OAAO,CAAC,CAAC;AAAA,QAAG;AACrD,eAAO;AAAA,MACX,GAAG,CAAC,CAAC;AAAA,IACT;AAEA,aAAS,eAAe,SAAS,SAAQ;AACrC,UAAG,OAAO,YAAY,UAAS;AAC3B,YAAG;AACC,iBAAO,SAAS,cAAc,OAAO;AAAA,QACzC,SAAO,GAAE;AACL,gBAAM;AAAA,QACV;AAAA,MAEJ;AAEA,UAAG,CAAC,YAAY,OAAO,KAAK,CAAC,SAAQ;AACjC,cAAM,IAAI,UAAW,UAAU,wBAAyB;AAAA,MAC5D;AACA,aAAO;AAAA,IACX;AAEA,YAAQ,iBAAiB;AACzB,YAAQ,aAAa;AACrB,YAAQ,YAAY;AACpB,YAAQ,qBAAqB;AAC7B,YAAQ,cAAc;AACtB,YAAQ,iBAAiB;AACzB,YAAQ,iBAAiB;AACzB,YAAQ,SAAS;AACjB,YAAQ,YAAY;AAAA;AAAA;;;ACrJpB,IAAAC,kBAAA;AAAA;AAAA;AAEA,QAAI,WAAW;AAEf,aAAS,cAAc,QAAQ,SAAS;AAYpC,gBAAU,WAAW,CAAC;AAEtB,UAAI,cAAc,SAAS,QAAQ,QAAQ,aAAa,IAAI;AAQ5D,aAAO,SAAS,QAAQ,OAAO;AAE3B,gBAAQ,SAAS,OAAO;AACxB,eAAO,SAAS,MAAM,UAAU,MAAM,cAAc,MAAM;AAC1D,eAAO,UAAU;AACjB,eAAO,OAAO,MAAM;AAEpB,YAAI,CAAC,YAAY,KAAK,GAAG;AACrB;AAAA,QACJ;AAKA,YAAI,MAAM,eAAe;AACrB,iBAAO,IAAI,MAAM,cAAc,CAAC,EAAE;AAClC,iBAAO,IAAI,MAAM,cAAc,CAAC,EAAE;AAClC,iBAAO,QAAQ,MAAM,cAAc,CAAC,EAAE;AACtC,iBAAO,QAAQ,MAAM,cAAc,CAAC,EAAE;AACtC,iBAAO,UAAU,MAAM,cAAc,CAAC,EAAE;AACxC,iBAAO,UAAU,MAAM,cAAc,CAAC,EAAE;AAAA,QAC5C,OAAO;AAOH,cAAI,MAAM,UAAU,QAAQ,MAAM,YAAY,MAAM;AAChD,gBAAI,WAAW,MAAM,UAAU,MAAM,OAAO,iBAAiB;AAC7D,gBAAI,MAAM,SAAS;AACnB,gBAAI,OAAO,SAAS;AAEpB,mBAAO,QAAQ,MAAM,WAAW,OAAO,IAAI,cAAc,QAAQ,KAAK,cAAc,MAAM,OAAO,IAAI,cAAc,QAAQ,KAAK,cAAc;AAC9I,mBAAO,QAAQ,MAAM,WAAW,OAAO,IAAI,aAAa,QAAQ,KAAK,aAAa,MAAM,OAAO,IAAI,aAAa,QAAQ,KAAK,aAAa;AAAA,UAC9I,OAAO;AACH,mBAAO,QAAQ,MAAM;AACrB,mBAAO,QAAQ,MAAM;AAAA,UACzB;AAOA,iBAAO,IAAI,MAAM;AACjB,iBAAO,IAAI,MAAM;AAEjB,iBAAO,UAAU,MAAM;AACvB,iBAAO,UAAU,MAAM;AAAA,QAC3B;AAEA,eAAO,UAAU,OAAO;AACxB,eAAO,UAAU,OAAO;AAAA,MAC5B;AAAA,IAGJ;AAOA,WAAO,UAAU;AAAA;AAAA;;;AC1FjB,IAAAC,kBAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAE5D,aAAS,gBAAiB,IAAI;AAAE,aAAQ,MAAO,OAAO,OAAO,YAAa,aAAa,KAAM,GAAG,SAAS,IAAI;AAAA,IAAI;AAEjH,QAAI,gBAAgB,gBAAgB,iBAA0B;AAE9D,aAAS,mBAAmB;AACxB,UAAI,QAAQ;AAAA,QACR,KAAK,EAAE,OAAO,GAAG,YAAY,KAAK;AAAA,QAClC,MAAM,EAAE,OAAO,GAAG,YAAY,KAAK;AAAA,QACnC,OAAO,EAAE,OAAO,OAAO,YAAY,YAAY,KAAK;AAAA,QACpD,QAAQ,EAAE,OAAO,OAAO,aAAa,YAAY,KAAK;AAAA,QACtD,OAAO,EAAE,OAAO,OAAO,YAAY,YAAY,KAAK;AAAA,QACpD,QAAQ,EAAE,OAAO,OAAO,aAAa,YAAY,KAAK;AAAA,QACtD,GAAG,EAAE,OAAO,GAAG,YAAY,KAAK;AAAA,QAChC,GAAG,EAAE,OAAO,GAAG,YAAY,KAAK;AAAA,MACpC;AAEA,UAAI,OAAO,QAAQ;AACf,eAAO,OAAO,OAAO,CAAC,GAAG,KAAK;AAAA,MAClC,OAAO;AACH,YAAI,OAAO,CAAC;AACZ,eAAO,iBAAiB,MAAM,KAAK;AACnC,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,aAAS,cAAc,IAAI;AACvB,UAAI,OAAO,QAAQ;AACf,eAAO,iBAAiB;AAAA,MAC5B,OAAO;AACH,YAAI;AACA,cAAI,OAAO,GAAG,sBAAsB;AACpC,cAAI,KAAK,MAAM,QAAW;AACtB,iBAAK,IAAI,KAAK;AACd,iBAAK,IAAI,KAAK;AAAA,UAClB;AACA,iBAAO;AAAA,QACX,SAAS,GAAG;AACR,gBAAM,IAAI,UAAU,yCAAyC,EAAE;AAAA,QACnE;AAAA,MACJ;AAAA,IACJ;AAEA,aAAS,YAAY,OAAO,IAAI;AAC5B,UAAI,OAAO,cAAc,EAAE;AAC3B,aAAO,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,UAAU,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,KAAK;AAAA,IAChG;AAEA,YAAQ,gBAAgB;AACxB,YAAQ,gBAAgB;AACxB,YAAQ,cAAc;AAAA;AAAA;;;ACrDtB,IAAAC,kBAAA;AAAA;AAAA;AAEA,QAAI,eAAe;AACnB,QAAI,OAAO,OAAO,UAAU,YAAY;AACtC,qBAAe,0BAAUC,YAAW;AAClC,YAAI,OAAO,SAASC,QAAO;AAAA,QAAC;AAC5B,eAAO,SAAU,WAAW,kBAAkB;AAC5C,cAAI,cAAc,OAAO,SAAS,KAAK,cAAc,MAAM;AACzD,kBAAM,UAAU,qCAAqC;AAAA,UACvD;AACA,eAAK,YAAY,aAAa,CAAC;AAC/B,cAAI,SAAS,IAAI,KAAK;AACtB,eAAK,YAAY;AACjB,cAAI,qBAAqBD,YAAW;AAClC,mBAAO,iBAAiB,QAAQ,gBAAgB;AAAA,UAClD;AAGA,cAAI,cAAc,MAAM;AACtB,mBAAO,YAAY;AAAA,UACrB;AACA,iBAAO;AAAA,QACT;AAAA,MACF,GAAE;AAAA,IACJ,OAAO;AACL,qBAAe,OAAO;AAAA,IACxB;AAEA,QAAI,iBAAiB;AAErB,QAAI,kBAAkB,CAAC,UAAU,UAAU,WAAW,WAAW,WAAW,WAAW,WAAW,aAAa,aAAa,WAAW,WAAW,SAAS,SAAS,UAAU,iBAAiB,WAAW,WAAW,YAAY,SAAS,KAAK,GAAG;AAElP,aAAS,iBAAiB,SAAS;AAE/B,UAAI,kBAAkB;AAAA,QAClB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,eAAe;AAAA,QACf,QAAQ;AAAA,MACZ;AAEA,UAAI,YAAY,QAAW;AACvB,gBAAQ,iBAAiB,aAAa,MAAM;AAAA,MAChD;AAEA,eAAS,OAAO,GAAG;AACf,iBAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC7C,0BAAgB,gBAAgB,CAAC,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;AAAA,QAC9D;AAAA,MACJ;AAEA,UAAI,YAAW,WAAY;AACvB,YAAI,YAAY;AACZ,iBAAO,SAAS,GAAGE,UAAS,UAAU,MAAM;AACxC,gBAAI,MAAM,IAAI,WAAW,aAAa,eAAe,iBAAiB,QAAQ,CAAC;AAG/E,uBAAW,KAAK,IAAI;AAEpB,mBAAOA,SAAQ,cAAc,GAAG;AAAA,UACpC;AAAA,QACJ,WAAW,OAAO,SAAS,gBAAgB,YAAY;AACnD,iBAAO,SAAS,GAAGA,UAAS,UAAU,MAAM;AACxC,gBAAI,WAAW,eAAe,iBAAiB,QAAQ;AACvD,gBAAI,MAAM,SAAS,YAAY,aAAa;AAE5C,gBAAI;AAAA,cAAe;AAAA,cAAa;AAAA;AAAA,cAChC;AAAA;AAAA,cACA;AAAA;AAAA,cACA;AAAA;AAAA,cACA,SAAS;AAAA;AAAA,cACT,SAAS;AAAA;AAAA,cACT,SAAS;AAAA;AAAA,cACT,SAAS;AAAA;AAAA,cACT,SAAS;AAAA;AAAA,cACT,SAAS;AAAA;AAAA,cACT,SAAS;AAAA;AAAA,cACT,SAAS;AAAA;AAAA,cACT,SAAS;AAAA;AAAA,cACT,SAAS;AAAA;AAAA,YACT;AAGA,uBAAW,KAAK,IAAI;AAEpB,mBAAOA,SAAQ,cAAc,GAAG;AAAA,UACpC;AAAA,QACJ,WAAW,OAAO,SAAS,sBAAsB,YAAY;AACzD,iBAAO,SAAS,GAAGA,UAAS,UAAU,MAAM;AACxC,gBAAI,MAAM,SAAS,kBAAkB;AACrC,gBAAI,WAAW,eAAe,iBAAiB,QAAQ;AACvD,qBAAS,QAAQ,UAAU;AACvB,kBAAI,IAAI,IAAI,SAAS,IAAI;AAAA,YAC7B;AAGA,uBAAW,KAAK,IAAI;AAEpB,mBAAOA,SAAQ,cAAc,GAAG;AAAA,UACpC;AAAA,QACJ;AAAA,MACJ,GAAE;AAEF,eAAS,UAAU;AACf,YAAI,QAAS,SAAQ,oBAAoB,aAAa,QAAQ,KAAK;AACnE,0BAAkB;AAAA,MACtB;AAEA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAEA,aAAS,eAAe,iBAAiB,UAAU;AAC/C,iBAAW,YAAY,CAAC;AACxB,UAAI,WAAW,eAAe,eAAe;AAC7C,eAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC7C,YAAI,SAAS,gBAAgB,CAAC,CAAC,MAAM,OAAW,UAAS,gBAAgB,CAAC,CAAC,IAAI,SAAS,gBAAgB,CAAC,CAAC;AAAA,MAC9G;AAEA,aAAO;AAAA,IACX;AAEA,aAAS,WAAW,GAAG,MAAM;AACzB,cAAQ,IAAI,SAAS,IAAI;AACzB,QAAE,OAAO,QAAQ,CAAC;AAClB,QAAE,aAAa;AAAA,IACnB;AAMA,WAAO,UAAU;AAAA;AAAA;;;AC9IjB,IAAAC,kBAAA;AAAA;AAEA,aAAS,gBAAiB,IAAI;AAAE,aAAQ,MAAO,OAAO,OAAO,YAAa,aAAa,KAAM,GAAG,SAAS,IAAI;AAAA,IAAI;AAEjH,QAAI,WAAW;AACf,QAAI,yBAAyB;AAC7B,QAAI,SAAS;AACb,QAAI,WAAW;AACf,QAAI,sBAAsB,gBAAgB,iBAAmC;AAE7E,aAAS,aAAa,UAAU,SAAQ;AACpC,UAAK,YAAY,OAAS,WAAU,CAAC;AAErC,UAAI,OAAO;AACX,UAAI,WAAW,GAAG,YAAY;AAE9B,WAAK,SAAS,QAAQ,UAAU;AAEhC,WAAK,oBAAoB,QAAQ,qBAAqB;AAEtD,UAAI,QAAQ,CAAC,GACT,UAAU,SAAS,cAAc,KAAK,GACtC,aAAa,oBAAoB,GACjC,OAAO;AAEX,aAAO,iBAAiB,aAAa,SAAS,KAAK;AACnD,aAAO,iBAAiB,aAAa,SAAS,KAAK;AAEnD,UAAG,CAAC,MAAM,QAAQ,QAAQ,GAAE;AACxB,mBAAW,QAAQ;AAAA,MACvB;AAEA,WAAK,aAAa,SAAS,QAAQ,QAAQ,UAAU;AACrD,WAAK,WAAW,SAAS,QAAQ,QAAQ,UAAU,KAAK;AAExD,WAAK,UAAU,SAAS,qBAAqB;AACzC,eAAO,oBAAoB,aAAa,SAAS,KAAK;AACtD,eAAO,oBAAoB,aAAa,SAAS,KAAK;AACtD,eAAO,oBAAoB,aAAa,QAAQ,KAAK;AACrD,eAAO,oBAAoB,cAAc,QAAQ,KAAK;AACtD,eAAO,oBAAoB,WAAW,MAAM,KAAK;AACjD,eAAO,oBAAoB,YAAY,MAAM,KAAK;AAClD,eAAO,oBAAoB,aAAa,MAAM,KAAK;AACnD,eAAO,oBAAoB,cAAc,YAAY,KAAK;AAE1D,eAAO,oBAAoB,aAAa,QAAQ,KAAK;AACrD,eAAO,oBAAoB,aAAa,QAAQ,KAAK;AAErD,eAAO,oBAAoB,UAAU,WAAW,IAAI;AACpD,mBAAW,CAAC;AACZ,YAAG,qBAAoB;AACrB,yBAAe;AAAA,QACjB;AAAA,MACJ;AAEA,WAAK,MAAM,WAAU;AACjB,YAAI,UAAU,CAAC,GAAG,MAAM,UAAU;AAClC,eAAQ,MAAQ,SAAS,GAAI,IAAI,UAAW,GAAI;AAEhD,eAAO,YAAY,MAAM,QAAQ,CAAE,QAAS,EAAE,OAAQ,OAAQ,CAAC;AAC/D,eAAO;AAAA,MACX;AAEA,WAAK,SAAS,WAAU;AACpB,YAAI,UAAU,CAAC,GAAG,MAAM,UAAU;AAClC,eAAQ,MAAQ,SAAS,GAAI,IAAI,UAAW,GAAI;AAEhD,eAAO,OAAO,eAAe,MAAM,QAAQ,CAAE,QAAS,EAAE,OAAQ,OAAQ,CAAC;AAAA,MAC7E;AAEA,UAAI,YAAY,MAAM;AAEtB,UAAG,OAAO,UAAU,SAAS,KAAK,QAAQ,MAAM,kBAAiB;AAC7D,mBAAW,CAAC,QAAQ;AAAA,MACxB;AAEA,OAAC,SAAS,MAAK;AACX,mBAAW,CAAC;AACZ,aAAK,QAAQ,SAAS,SAAQ;AAC1B,cAAG,YAAY,QAAO;AAClB,wBAAY;AAAA,UAChB,OAAK;AACD,iBAAK,IAAI,OAAO;AAAA,UACpB;AAAA,QACJ,CAAC;AAAA,MACL,GAAE,QAAQ;AAEV,aAAO,iBAAiB,MAAM;AAAA,QAC1B,MAAM;AAAA,UACF,KAAK,WAAU;AAAE,mBAAO;AAAA,UAAM;AAAA,QAClC;AAAA,QACA,UAAU;AAAA,UACN,KAAK,WAAU;AAAE,mBAAO;AAAA,UAAU;AAAA,QACtC;AAAA,QACA,OAAO;AAAA,UACH,KAAK,WAAU;AAAE,mBAAO;AAAA,UAAO;AAAA,QACnC;AAAA,QACA,WAAW;AAAA,UACP,KAAK,WAAU;AAAE,mBAAO;AAAA,UAAW;AAAA,QACvC;AAAA,MACJ,CAAC;AAED,UAAI,IAAI,GAAG,UAAU,MAAM;AAE3B,aAAO,iBAAiB,aAAa,QAAQ,KAAK;AAClD,aAAO,iBAAiB,cAAc,QAAQ,KAAK;AACnD,aAAO,iBAAiB,WAAW,MAAM,KAAK;AAC9C,aAAO,iBAAiB,YAAY,MAAM,KAAK;AAQ/C,aAAO,iBAAiB,aAAa,MAAM,KAAK;AAEhD,aAAO,iBAAiB,aAAa,QAAQ,KAAK;AAClD,aAAO,iBAAiB,aAAa,QAAQ,KAAK;AAElD,aAAO,iBAAiB,cAAc,YAAY,KAAK;AAEvD,aAAO,iBAAiB,UAAU,WAAW,IAAI;AAEjD,eAAS,UAAU,GAAE;AAEjB,iBAAQ,IAAE,GAAG,IAAE,SAAS,QAAQ,KAAI;AAChC,cAAG,SAAS,CAAC,MAAM,EAAE,QAAO;AACxB,wBAAY;AACZ;AAAA,UACJ;AAAA,QACJ;AAEA,YAAG,WAAU;AACT,iCAAuB,sBAAsB,WAAW;AAAE,mBAAO,YAAY;AAAA,UAAO,CAAC;AAAA,QACzF;AAAA,MACJ;AAEA,eAAS,SAAQ;AACb,eAAO;AAAA,MACX;AAEA,eAAS,OAAM;AACX,eAAO;AACP,uBAAe;AAAA,MACnB;AACA,eAAS,iBAAgB;AACvB,+BAAuB,qBAAqB,cAAc;AAC1D,+BAAuB,qBAAqB,oBAAoB;AAAA,MAClE;AACA,eAAS,aAAY;AACjB,eAAO;AAAA,MACX;AAEA,eAAS,UAAU,QAAO;AACtB,YAAG,CAAC,QAAO;AACP,iBAAO;AAAA,QACX;AAEA,YAAG,YAAY,QAAO;AAClB,iBAAO;AAAA,QACX;AAEA,YAAG,OAAO,WAAW,UAAU,MAAM,GAAE;AACnC,iBAAO;AAAA,QACX;AAEA,eAAM,SAAS,OAAO,YAAW;AAC7B,cAAG,OAAO,WAAW,UAAU,MAAM,GAAE;AACnC,mBAAO;AAAA,UACX;AAAA,QACJ;AAEA,eAAO;AAAA,MACX;AAEA,eAAS,uBAAsB;AAC3B,YAAI,aAAa;AAEjB,iBAAQ,IAAE,GAAG,IAAE,SAAS,QAAQ,KAAI;AAChC,cAAG,OAAO,OAAO,SAAS,CAAC,CAAC,GAAE;AAC1B,yBAAa,SAAS,CAAC;AAAA,UAC3B;AAAA,QACJ;AAEA,eAAO;AAAA,MACX;AAGA,eAAS,OAAO,OAAM;AAElB,YAAG,CAAC,KAAK,WAAW,GAAG;AAAE;AAAA,QAAQ;AAEjC,YAAG,MAAM,YAAY,GAAE;AAAE;AAAA,QAAQ;AAEjC,YAAI,SAAS,MAAM,QAAQ,OAAO,SAAS;AAE3C,YAAG,WAAW,CAAC,OAAO,OAAO,OAAO,GAAE;AAClC,cAAG,CAAC,KAAK,mBAAkB;AACvB,sBAAU;AAAA,UACd;AAAA,QACJ;AAEA,YAAG,UAAU,OAAO,eAAe,MAAK;AAEpC,mBAAS,qBAAqB;AAAA,QAClC,OAAK;AACD,mBAAS,UAAU,MAAM;AAEzB,cAAG,CAAC,QAAO;AACP,qBAAS,qBAAqB;AAAA,UAClC;AAAA,QACJ;AAGA,YAAG,UAAU,WAAW,SAAQ;AAC5B,oBAAU;AAAA,QACd;AAEA,YAAG,WAAU;AACT,iCAAuB,qBAAqB,oBAAoB;AAChE,iCAAuB,uBAAuB,sBAAsB,YAAY;AAAA,QACpF;AAGA,YAAG,CAAC,SAAQ;AACR;AAAA,QACJ;AAEA,+BAAuB,qBAAqB,cAAc;AAC1D,yBAAiB,uBAAuB,sBAAsB,UAAU;AAAA,MAC5E;AAEA,eAAS,eAAc;AACnB,mBAAW,SAAS;AAEpB,+BAAuB,qBAAqB,oBAAoB;AAChE,+BAAuB,uBAAuB,sBAAsB,YAAY;AAAA,MACpF;AAEA,eAAS,aAAY;AAEjB,YAAG,CAAC,SAAQ;AACR;AAAA,QACJ;AAEA,mBAAW,OAAO;AAElB,+BAAuB,qBAAqB,cAAc;AAC1D,yBAAiB,uBAAuB,sBAAsB,UAAU;AAAA,MAE5E;AAGA,eAAS,WAAW,IAAG;AACnB,YAAI,OAAO,SAAS,cAAc,EAAE,GAAG,SAAS;AAEhD,YAAG,MAAM,IAAI,KAAK,OAAO,KAAK,QAAO;AACjC,oBAAU,KAAK;AAAA,YACX,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,QAAQ,KAAK,SAAS,CAAC,IAAI,KAAK;AAAA,UACjE;AAAA,QACJ,WAAS,MAAM,IAAI,KAAK,QAAQ,KAAK,QAAO;AACxC,oBAAU,KAAK;AAAA,YACX,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,SAAS,KAAK,SAAS,CAAC,IAAI,KAAK;AAAA,UACjE;AAAA,QACJ,OAAK;AACD,oBAAU;AAAA,QACd;AAEA,YAAG,MAAM,IAAI,KAAK,MAAM,KAAK,QAAO;AAChC,oBAAU,KAAK;AAAA,YACX,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,KAAK,SAAS,CAAC,IAAI,KAAK;AAAA,UAChE;AAAA,QACJ,WAAS,MAAM,IAAI,KAAK,SAAS,KAAK,QAAO;AACzC,oBAAU,KAAK;AAAA,YACX,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,UAAU,KAAK,SAAS,CAAC,IAAI,KAAK;AAAA,UAClE;AAAA,QACJ,OAAK;AACD,oBAAU;AAAA,QACd;AAEA,YAAG,KAAK,SAAS,GAAE;AAOf,qBAAW,SAAS,IAAI;AAAA,YACpB,OAAO,MAAM,QAAQ;AAAA,YACrB,OAAO,MAAM,QAAQ;AAAA,YACrB,SAAS,MAAM,IAAI;AAAA,YACnB,SAAS,MAAM,IAAI;AAAA,UACvB,CAAC;AAAA,QACL;AAEA,mBAAW,WAAW;AAElB,cAAG,SAAQ;AACP,oBAAQ,IAAI,OAAO;AAAA,UACvB;AAEA,cAAG,SAAQ;AACP,oBAAQ,IAAI,OAAO;AAAA,UACvB;AAAA,QAEJ,CAAC;AAAA,MACL;AAEA,eAAS,QAAQ,IAAI,QAAO;AACxB,YAAG,OAAO,QAAO;AACb,iBAAO,SAAS,GAAG,aAAa,GAAG,cAAc,MAAM;AAAA,QAC3D,OAAK;AACD,aAAG,aAAa;AAAA,QACpB;AAAA,MACJ;AAEA,eAAS,QAAQ,IAAI,QAAO;AACxB,YAAG,OAAO,QAAO;AACb,iBAAO,SAAS,GAAG,cAAc,QAAQ,GAAG,WAAW;AAAA,QAC3D,OAAK;AACD,aAAG,cAAc;AAAA,QACrB;AAAA,MACJ;AAAA,IAEJ;AAEA,aAAS,oBAAoB,SAAS,SAAQ;AAC1C,aAAO,IAAI,aAAa,SAAS,OAAO;AAAA,IAC5C;AAEA,aAAS,OAAO,OAAO,IAAI,MAAK;AAC5B,UAAG,CAAC,MAAK;AACL,eAAO,SAAS,YAAY,OAAO,EAAE;AAAA,MACzC,OAAK;AACD,eAAQ,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,UACrC,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,KAAK;AAAA,MAClD;AAAA,IACJ;AAOA,WAAO,UAAU;AAAA;AAAA;", "names": ["require_bundle", "_typeof", "require_bundle", "require_bundle", "require_bundle", "undefined", "Temp", "element", "require_bundle"]}
import {
  __commonJS
} from "./chunk-WOR4A3D2.js";

// node_modules/highcharts/highcharts.js
var require_highcharts = __commonJS({
  "node_modules/highcharts/highcharts.js"(exports, module) {
    (function(T, O) {
      "object" === typeof module && module.exports ? (O["default"] = O, module.exports = T.document ? O(T) : O) : "function" === typeof define && define.amd ? define("highcharts/highcharts", function() {
        return O(T);
      }) : (T.Highcharts && T.Highcharts.error(16, true), T.Highcharts = O(T));
    })("undefined" !== typeof window ? window : exports, function(T) {
      function O(f, a, S, y) {
        f.hasOwnProperty(a) || (f[a] = y.apply(null, S));
      }
      var n = {};
      O(n, "Core/Globals.js", [], function() {
        var f = "undefined" !== typeof T ? T : "undefined" !== typeof window ? window : {}, a = f.document, S = f.navigator && f.navigator.userAgent || "", y = a && a.createElementNS && !!a.createElementNS("http://www.w3.org/2000/svg", "svg").createSVGRect, n2 = /(edge|msie|trident)/i.test(S) && !f.opera, G = -1 !== S.indexOf("Firefox"), C = -1 !== S.indexOf("Chrome"), J = G && 4 > parseInt(S.split("Firefox/")[1], 10);
        return {
          product: "Highcharts",
          version: "8.2.0",
          deg2rad: 2 * Math.PI / 360,
          doc: a,
          hasBidiBug: J,
          hasTouch: !!f.TouchEvent,
          isMS: n2,
          isWebKit: -1 !== S.indexOf("AppleWebKit"),
          isFirefox: G,
          isChrome: C,
          isSafari: !C && -1 !== S.indexOf("Safari"),
          isTouchDevice: /(Mobile|Android|Windows Phone)/.test(S),
          SVG_NS: "http://www.w3.org/2000/svg",
          chartCount: 0,
          seriesTypes: {},
          symbolSizes: {},
          svg: y,
          win: f,
          marginNames: ["plotTop", "marginRight", "marginBottom", "plotLeft"],
          noop: function() {
          },
          charts: [],
          dateFormats: {}
        };
      });
      O(n, "Core/Utilities.js", [n["Core/Globals.js"]], function(f) {
        function a(b2, c2, e2, d2) {
          var z2 = c2 ? "Highcharts error" : "Highcharts warning";
          32 === b2 && (b2 = z2 + ": Deprecated member");
          var w2 = I(b2), g2 = w2 ? z2 + " #" + b2 + ": www.highcharts.com/errors/" + b2 + "/" : b2.toString();
          z2 = function() {
            if (c2) throw Error(g2);
            v.console && -1 === a.messages.indexOf(g2) && console.log(g2);
          };
          if ("undefined" !== typeof d2) {
            var k2 = "";
            w2 && (g2 += "?");
            U(d2, function(b3, c3) {
              k2 += "\n - " + c3 + ": " + b3;
              w2 && (g2 += encodeURI(c3) + "=" + encodeURI(b3));
            });
            g2 += k2;
          }
          e2 ? ea(e2, "displayError", { code: b2, message: g2, params: d2 }, z2) : z2();
          a.messages.push(g2);
        }
        function S() {
          var b2, c2 = arguments, e2 = {}, d2 = function(b3, c3) {
            "object" !== typeof b3 && (b3 = {});
            U(c3, function(e3, z3) {
              !y(e3, true) || t(e3) || p(e3) ? b3[z3] = c3[z3] : b3[z3] = d2(b3[z3] || {}, e3);
            });
            return b3;
          };
          true === c2[0] && (e2 = c2[1], c2 = Array.prototype.slice.call(c2, 2));
          var z2 = c2.length;
          for (b2 = 0; b2 < z2; b2++) e2 = d2(e2, c2[b2]);
          return e2;
        }
        function y(b2, c2) {
          return !!b2 && "object" === typeof b2 && (!c2 || !E(b2));
        }
        function n2(b2, c2, e2) {
          var d2;
          K(c2) ? m(e2) ? b2.setAttribute(c2, e2) : b2 && b2.getAttribute && ((d2 = b2.getAttribute(c2)) || "class" !== c2 || (d2 = b2.getAttribute(c2 + "Name"))) : U(c2, function(c3, e3) {
            b2.setAttribute(e3, c3);
          });
          return d2;
        }
        function G() {
          for (var b2 = arguments, c2 = b2.length, e2 = 0; e2 < c2; e2++) {
            var d2 = b2[e2];
            if ("undefined" !== typeof d2 && null !== d2) return d2;
          }
        }
        function C(b2, c2) {
          if (!b2) return c2;
          var e2 = b2.split(".").reverse();
          if (1 === e2.length) return c2[b2];
          for (b2 = e2.pop(); "undefined" !== typeof b2 && "undefined" !== typeof c2 && null !== c2; ) c2 = c2[b2], b2 = e2.pop();
          return c2;
        }
        f.timers = [];
        var J = f.charts, H = f.doc, v = f.win;
        (a || (a = {})).messages = [];
        f.error = a;
        var L = (function() {
          function b2(b3, c2, e2) {
            this.options = c2;
            this.elem = b3;
            this.prop = e2;
          }
          b2.prototype.dSetter = function() {
            var b3 = this.paths, c2 = b3 && b3[0];
            b3 = b3 && b3[1];
            var e2 = [], d2 = this.now || 0;
            if (1 !== d2 && c2 && b3) if (c2.length === b3.length && 1 > d2) for (var z2 = 0; z2 < b3.length; z2++) {
              for (var w2 = c2[z2], g2 = b3[z2], k2 = [], h2 = 0; h2 < g2.length; h2++) {
                var P2 = w2[h2], l2 = g2[h2];
                k2[h2] = "number" === typeof P2 && "number" === typeof l2 && ("A" !== g2[0] || 4 !== h2 && 5 !== h2) ? P2 + d2 * (l2 - P2) : l2;
              }
              e2.push(k2);
            }
            else e2 = b3;
            else e2 = this.toD || [];
            this.elem.attr("d", e2, void 0, true);
          };
          b2.prototype.update = function() {
            var b3 = this.elem, c2 = this.prop, e2 = this.now, d2 = this.options.step;
            if (this[c2 + "Setter"]) this[c2 + "Setter"]();
            else b3.attr ? b3.element && b3.attr(c2, e2, null, true) : b3.style[c2] = e2 + this.unit;
            d2 && d2.call(b3, e2, this);
          };
          b2.prototype.run = function(b3, c2, e2) {
            var d2 = this, z2 = d2.options, w2 = function(b4) {
              return w2.stopped ? false : d2.step(b4);
            }, g2 = v.requestAnimationFrame || function(b4) {
              setTimeout(b4, 13);
            }, h2 = function() {
              for (var b4 = 0; b4 < f.timers.length; b4++) f.timers[b4]() || f.timers.splice(
                b4--,
                1
              );
              f.timers.length && g2(h2);
            };
            b3 !== c2 || this.elem["forceAnimate:" + this.prop] ? (this.startTime = +/* @__PURE__ */ new Date(), this.start = b3, this.end = c2, this.unit = e2, this.now = this.start, this.pos = 0, w2.elem = this.elem, w2.prop = this.prop, w2() && 1 === f.timers.push(w2) && g2(h2)) : (delete z2.curAnim[this.prop], z2.complete && 0 === Object.keys(z2.curAnim).length && z2.complete.call(this.elem));
          };
          b2.prototype.step = function(b3) {
            var c2 = +/* @__PURE__ */ new Date(), e2 = this.options, d2 = this.elem, z2 = e2.complete, w2 = e2.duration, g2 = e2.curAnim;
            if (d2.attr && !d2.element) b3 = false;
            else if (b3 || c2 >= w2 + this.startTime) {
              this.now = this.end;
              this.pos = 1;
              this.update();
              var h2 = g2[this.prop] = true;
              U(g2, function(b4) {
                true !== b4 && (h2 = false);
              });
              h2 && z2 && z2.call(d2);
              b3 = false;
            } else this.pos = e2.easing((c2 - this.startTime) / w2), this.now = this.start + (this.end - this.start) * this.pos, this.update(), b3 = true;
            return b3;
          };
          b2.prototype.initPath = function(b3, c2, e2) {
            function d2(b4, c3) {
              for (; b4.length < r2; ) {
                var e3 = b4[0], d3 = c3[r2 - b4.length];
                d3 && "M" === e3[0] && (b4[0] = "C" === d3[0] ? ["C", e3[1], e3[2], e3[1], e3[2], e3[1], e3[2]] : ["L", e3[1], e3[2]]);
                b4.unshift(e3);
                h2 && b4.push(b4[b4.length - 1]);
              }
            }
            function z2(b4, c3) {
              for (; b4.length < r2; ) if (c3 = b4[b4.length / k2 - 1].slice(), "C" === c3[0] && (c3[1] = c3[5], c3[2] = c3[6]), h2) {
                var e3 = b4[b4.length / k2].slice();
                b4.splice(b4.length / 2, 0, c3, e3);
              } else b4.push(c3);
            }
            var w2 = b3.startX, g2 = b3.endX;
            c2 = c2 && c2.slice();
            e2 = e2.slice();
            var h2 = b3.isArea, k2 = h2 ? 2 : 1;
            if (!c2) return [e2, e2];
            if (w2 && g2) {
              for (b3 = 0; b3 < w2.length; b3++) if (w2[b3] === g2[0]) {
                var P2 = b3;
                break;
              } else if (w2[0] === g2[g2.length - w2.length + b3]) {
                P2 = b3;
                var l2 = true;
                break;
              } else if (w2[w2.length - 1] === g2[g2.length - w2.length + b3]) {
                P2 = w2.length - b3;
                break;
              }
              "undefined" === typeof P2 && (c2 = []);
            }
            if (c2.length && I(P2)) {
              var r2 = e2.length + P2 * k2;
              l2 ? (d2(c2, e2), z2(e2, c2)) : (d2(e2, c2), z2(c2, e2));
            }
            return [
              c2,
              e2
            ];
          };
          b2.prototype.fillSetter = function() {
            b2.prototype.strokeSetter.apply(this, arguments);
          };
          b2.prototype.strokeSetter = function() {
            this.elem.attr(this.prop, f.color(this.start).tweenTo(f.color(this.end), this.pos), null, true);
          };
          return b2;
        })();
        f.Fx = L;
        f.merge = S;
        var q = f.pInt = function(b2, c2) {
          return parseInt(b2, c2 || 10);
        }, K = f.isString = function(b2) {
          return "string" === typeof b2;
        }, E = f.isArray = function(b2) {
          b2 = Object.prototype.toString.call(b2);
          return "[object Array]" === b2 || "[object Array Iterator]" === b2;
        };
        f.isObject = y;
        var p = f.isDOMElement = function(b2) {
          return y(b2) && "number" === typeof b2.nodeType;
        }, t = f.isClass = function(b2) {
          var c2 = b2 && b2.constructor;
          return !(!y(b2, true) || p(b2) || !c2 || !c2.name || "Object" === c2.name);
        }, I = f.isNumber = function(b2) {
          return "number" === typeof b2 && !isNaN(b2) && Infinity > b2 && -Infinity < b2;
        }, u = f.erase = function(b2, c2) {
          for (var e2 = b2.length; e2--; ) if (b2[e2] === c2) {
            b2.splice(e2, 1);
            break;
          }
        }, m = f.defined = function(b2) {
          return "undefined" !== typeof b2 && null !== b2;
        };
        f.attr = n2;
        var h = f.splat = function(b2) {
          return E(b2) ? b2 : [b2];
        }, l = f.syncTimeout = function(b2, c2, e2) {
          if (0 < c2) return setTimeout(b2, c2, e2);
          b2.call(0, e2);
          return -1;
        }, k = f.clearTimeout = function(b2) {
          m(b2) && clearTimeout(b2);
        }, g = f.extend = function(b2, c2) {
          var e2;
          b2 || (b2 = {});
          for (e2 in c2) b2[e2] = c2[e2];
          return b2;
        };
        f.pick = G;
        var d = f.css = function(b2, c2) {
          f.isMS && !f.svg && c2 && "undefined" !== typeof c2.opacity && (c2.filter = "alpha(opacity=" + 100 * c2.opacity + ")");
          g(b2.style, c2);
        }, x = f.createElement = function(b2, c2, e2, z2, w2) {
          b2 = H.createElement(b2);
          c2 && g(b2, c2);
          w2 && d(b2, { padding: "0", border: "none", margin: "0" });
          e2 && d(b2, e2);
          z2 && z2.appendChild(b2);
          return b2;
        }, r = f.extendClass = function(b2, c2) {
          var e2 = function() {
          };
          e2.prototype = new b2();
          g(
            e2.prototype,
            c2
          );
          return e2;
        }, A = f.pad = function(b2, c2, e2) {
          return Array((c2 || 2) + 1 - String(b2).replace("-", "").length).join(e2 || "0") + b2;
        }, N = f.relativeLength = function(b2, c2, e2) {
          return /%$/.test(b2) ? c2 * parseFloat(b2) / 100 + (e2 || 0) : parseFloat(b2);
        }, B = f.wrap = function(b2, c2, e2) {
          var d2 = b2[c2];
          b2[c2] = function() {
            var b3 = Array.prototype.slice.call(arguments), c3 = arguments, z2 = this;
            z2.proceed = function() {
              d2.apply(z2, arguments.length ? arguments : c3);
            };
            b3.unshift(d2);
            b3 = e2.apply(this, b3);
            z2.proceed = null;
            return b3;
          };
        }, M = f.format = function(b2, c2, e2) {
          var d2 = "{", z2 = false, w2 = [], g2 = /f$/, h2 = /\.([0-9])/, k2 = f.defaultOptions.lang, P2 = e2 && e2.time || f.time;
          for (e2 = e2 && e2.numberFormatter || X; b2; ) {
            var l2 = b2.indexOf(d2);
            if (-1 === l2) break;
            var r2 = b2.slice(0, l2);
            if (z2) {
              r2 = r2.split(":");
              d2 = C(r2.shift() || "", c2);
              if (r2.length && "number" === typeof d2) if (r2 = r2.join(":"), g2.test(r2)) {
                var m2 = parseInt((r2.match(h2) || ["", "-1"])[1], 10);
                null !== d2 && (d2 = e2(d2, m2, k2.decimalPoint, -1 < r2.indexOf(",") ? k2.thousandsSep : ""));
              } else d2 = P2.dateFormat(r2, d2);
              w2.push(d2);
            } else w2.push(r2);
            b2 = b2.slice(l2 + 1);
            d2 = (z2 = !z2) ? "}" : "{";
          }
          w2.push(b2);
          return w2.join("");
        }, R = f.getMagnitude = function(b2) {
          return Math.pow(
            10,
            Math.floor(Math.log(b2) / Math.LN10)
          );
        }, F = f.normalizeTickInterval = function(b2, c2, e2, d2, z2) {
          var w2 = b2;
          e2 = G(e2, 1);
          var g2 = b2 / e2;
          c2 || (c2 = z2 ? [1, 1.2, 1.5, 2, 2.5, 3, 4, 5, 6, 8, 10] : [1, 2, 2.5, 5, 10], false === d2 && (1 === e2 ? c2 = c2.filter(function(b3) {
            return 0 === b3 % 1;
          }) : 0.1 >= e2 && (c2 = [1 / e2])));
          for (d2 = 0; d2 < c2.length && !(w2 = c2[d2], z2 && w2 * e2 >= b2 || !z2 && g2 <= (c2[d2] + (c2[d2 + 1] || c2[d2])) / 2); d2++) ;
          return w2 = P(w2 * e2, -Math.round(Math.log(1e-3) / Math.LN10));
        }, e = f.stableSort = function(b2, c2) {
          var e2 = b2.length, d2, z2;
          for (z2 = 0; z2 < e2; z2++) b2[z2].safeI = z2;
          b2.sort(function(b3, e3) {
            d2 = c2(b3, e3);
            return 0 === d2 ? b3.safeI - e3.safeI : d2;
          });
          for (z2 = 0; z2 < e2; z2++) delete b2[z2].safeI;
        }, c = f.arrayMin = function(b2) {
          for (var c2 = b2.length, e2 = b2[0]; c2--; ) b2[c2] < e2 && (e2 = b2[c2]);
          return e2;
        }, b = f.arrayMax = function(b2) {
          for (var c2 = b2.length, e2 = b2[0]; c2--; ) b2[c2] > e2 && (e2 = b2[c2]);
          return e2;
        }, z = f.destroyObjectProperties = function(b2, c2) {
          U(b2, function(e2, d2) {
            e2 && e2 !== c2 && e2.destroy && e2.destroy();
            delete b2[d2];
          });
        }, w = f.discardElement = function(b2) {
          var c2 = f.garbageBin;
          c2 || (c2 = x("div"));
          b2 && c2.appendChild(b2);
          c2.innerHTML = "";
        }, P = f.correctFloat = function(b2, c2) {
          return parseFloat(b2.toPrecision(c2 || 14));
        }, Z = f.setAnimation = function(b2, c2) {
          c2.renderer.globalAnimation = G(b2, c2.options.chart.animation, true);
        }, W = f.animObject = function(b2) {
          return y(b2) ? f.merge({ duration: 500, defer: 0 }, b2) : { duration: b2 ? 500 : 0, defer: 0 };
        }, aa = f.timeUnits = { millisecond: 1, second: 1e3, minute: 6e4, hour: 36e5, day: 864e5, week: 6048e5, month: 24192e5, year: 314496e5 }, X = f.numberFormat = function(b2, c2, e2, d2) {
          b2 = +b2 || 0;
          c2 = +c2;
          var z2 = f.defaultOptions.lang, w2 = (b2.toString().split(".")[1] || "").split("e")[0].length, g2 = b2.toString().split("e");
          if (-1 === c2) c2 = Math.min(w2, 20);
          else if (!I(c2)) c2 = 2;
          else if (c2 && g2[1] && 0 > g2[1]) {
            var h2 = c2 + +g2[1];
            0 <= h2 ? (g2[0] = (+g2[0]).toExponential(h2).split("e")[0], c2 = h2) : (g2[0] = g2[0].split(".")[0] || 0, b2 = 20 > c2 ? (g2[0] * Math.pow(10, g2[1])).toFixed(c2) : 0, g2[1] = 0);
          }
          var k2 = (Math.abs(g2[1] ? g2[0] : b2) + Math.pow(10, -Math.max(c2, w2) - 1)).toFixed(c2);
          w2 = String(q(k2));
          h2 = 3 < w2.length ? w2.length % 3 : 0;
          e2 = G(e2, z2.decimalPoint);
          d2 = G(d2, z2.thousandsSep);
          b2 = (0 > b2 ? "-" : "") + (h2 ? w2.substr(0, h2) + d2 : "");
          b2 += w2.substr(h2).replace(/(\d{3})(?=\d)/g, "$1" + d2);
          c2 && (b2 += e2 + k2.slice(-c2));
          g2[1] && 0 !== +b2 && (b2 += "e" + g2[1]);
          return b2;
        };
        Math.easeInOutSine = function(b2) {
          return -0.5 * (Math.cos(Math.PI * b2) - 1);
        };
        var ba = f.getStyle = function(b2, c2, e2) {
          if ("width" === c2) return c2 = Math.min(b2.offsetWidth, b2.scrollWidth), e2 = b2.getBoundingClientRect && b2.getBoundingClientRect().width, e2 < c2 && e2 >= c2 - 1 && (c2 = Math.floor(e2)), Math.max(0, c2 - f.getStyle(b2, "padding-left") - f.getStyle(b2, "padding-right"));
          if ("height" === c2) return Math.max(0, Math.min(b2.offsetHeight, b2.scrollHeight) - f.getStyle(b2, "padding-top") - f.getStyle(b2, "padding-bottom"));
          v.getComputedStyle || a(27, true);
          if (b2 = v.getComputedStyle(b2, void 0)) b2 = b2.getPropertyValue(c2), G(e2, "opacity" !== c2) && (b2 = q(b2));
          return b2;
        }, ca = f.getDeferredAnimation = function(b2, c2, e2) {
          var d2 = W(c2), z2 = 0, w2 = 0;
          (e2 ? [e2] : b2.series).forEach(function(b3) {
            b3 = W(b3.options.animation);
            z2 = c2 && m(c2.defer) ? d2.defer : Math.max(z2, b3.duration + b3.defer);
            w2 = Math.min(d2.duration, b3.duration);
          });
          b2.renderer.forExport && (z2 = 0);
          return { defer: Math.max(0, z2 - w2), duration: Math.min(z2, w2) };
        }, Y = f.inArray = function(b2, c2, e2) {
          a(32, false, void 0, { "Highcharts.inArray": "use Array.indexOf" });
          return c2.indexOf(b2, e2);
        }, V = f.find = Array.prototype.find ? function(b2, c2) {
          return b2.find(c2);
        } : function(b2, c2) {
          var e2, d2 = b2.length;
          for (e2 = 0; e2 < d2; e2++) if (c2(b2[e2], e2)) return b2[e2];
        };
        f.keys = function(b2) {
          a(32, false, void 0, { "Highcharts.keys": "use Object.keys" });
          return Object.keys(b2);
        };
        var Q = f.offset = function(b2) {
          var c2 = H.documentElement;
          b2 = b2.parentElement || b2.parentNode ? b2.getBoundingClientRect() : { top: 0, left: 0 };
          return { top: b2.top + (v.pageYOffset || c2.scrollTop) - (c2.clientTop || 0), left: b2.left + (v.pageXOffset || c2.scrollLeft) - (c2.clientLeft || 0) };
        }, fa = f.stop = function(b2, c2) {
          for (var e2 = f.timers.length; e2--; ) f.timers[e2].elem !== b2 || c2 && c2 !== f.timers[e2].prop || (f.timers[e2].stopped = true);
        }, U = f.objectEach = function(b2, c2, e2) {
          for (var d2 in b2) Object.hasOwnProperty.call(b2, d2) && c2.call(e2 || b2[d2], b2[d2], d2, b2);
        };
        U({ map: "map", each: "forEach", grep: "filter", reduce: "reduce", some: "some" }, function(b2, c2) {
          f[c2] = function(e2) {
            var d2;
            a(32, false, void 0, (d2 = {}, d2["Highcharts." + c2] = "use Array." + b2, d2));
            return Array.prototype[b2].apply(e2, [].slice.call(arguments, 1));
          };
        });
        var ja = f.addEvent = function(b2, c2, e2, d2) {
          void 0 === d2 && (d2 = {});
          var z2 = b2.addEventListener || f.addEventListenerPolyfill;
          var w2 = "function" === typeof b2 && b2.prototype ? b2.prototype.protoEvents = b2.prototype.protoEvents || {} : b2.hcEvents = b2.hcEvents || {};
          f.Point && b2 instanceof f.Point && b2.series && b2.series.chart && (b2.series.chart.runTrackerClick = true);
          z2 && z2.call(b2, c2, e2, false);
          w2[c2] || (w2[c2] = []);
          w2[c2].push({ fn: e2, order: "number" === typeof d2.order ? d2.order : Infinity });
          w2[c2].sort(function(b3, c3) {
            return b3.order - c3.order;
          });
          return function() {
            ha(b2, c2, e2);
          };
        }, ha = f.removeEvent = function(b2, c2, e2) {
          function d2(c3, e3) {
            var d3 = b2.removeEventListener || f.removeEventListenerPolyfill;
            d3 && d3.call(b2, c3, e3, false);
          }
          function z2(e3) {
            var z3;
            if (b2.nodeName) {
              if (c2) {
                var w3 = {};
                w3[c2] = true;
              } else w3 = e3;
              U(w3, function(b3, c3) {
                if (e3[c3]) for (z3 = e3[c3].length; z3--; ) d2(c3, e3[c3][z3].fn);
              });
            }
          }
          var w2;
          ["protoEvents", "hcEvents"].forEach(function(g2, h2) {
            var k2 = (h2 = h2 ? b2 : b2.prototype) && h2[g2];
            k2 && (c2 ? (w2 = k2[c2] || [], e2 ? (k2[c2] = w2.filter(function(b3) {
              return e2 !== b3.fn;
            }), d2(c2, e2)) : (z2(k2), k2[c2] = [])) : (z2(k2), h2[g2] = {}));
          });
        }, ea = f.fireEvent = function(b2, c2, e2, d2) {
          var z2;
          e2 = e2 || {};
          if (H.createEvent && (b2.dispatchEvent || b2.fireEvent)) {
            var w2 = H.createEvent("Events");
            w2.initEvent(c2, true, true);
            g(w2, e2);
            b2.dispatchEvent ? b2.dispatchEvent(w2) : b2.fireEvent(c2, w2);
          } else e2.target || g(e2, { preventDefault: function() {
            e2.defaultPrevented = true;
          }, target: b2, type: c2 }), (function(c3, d3) {
            void 0 === c3 && (c3 = []);
            void 0 === d3 && (d3 = []);
            var w3 = 0, g2 = 0, h2 = c3.length + d3.length;
            for (z2 = 0; z2 < h2; z2++) false === (c3[w3] ? d3[g2] ? c3[w3].order <= d3[g2].order ? c3[w3++] : d3[g2++] : c3[w3++] : d3[g2++]).fn.call(b2, e2) && e2.preventDefault();
          })(b2.protoEvents && b2.protoEvents[c2], b2.hcEvents && b2.hcEvents[c2]);
          d2 && !e2.defaultPrevented && d2.call(b2, e2);
        }, ka = f.animate = function(b2, c2, e2) {
          var d2, z2 = "", w2, g2;
          if (!y(e2)) {
            var h2 = arguments;
            e2 = {
              duration: h2[2],
              easing: h2[3],
              complete: h2[4]
            };
          }
          I(e2.duration) || (e2.duration = 400);
          e2.easing = "function" === typeof e2.easing ? e2.easing : Math[e2.easing] || Math.easeInOutSine;
          e2.curAnim = S(c2);
          U(c2, function(h3, k2) {
            fa(b2, k2);
            g2 = new L(b2, e2, k2);
            w2 = null;
            "d" === k2 && E(c2.d) ? (g2.paths = g2.initPath(b2, b2.pathArray, c2.d), g2.toD = c2.d, d2 = 0, w2 = 1) : b2.attr ? d2 = b2.attr(k2) : (d2 = parseFloat(ba(b2, k2)) || 0, "opacity" !== k2 && (z2 = "px"));
            w2 || (w2 = h3);
            w2 && w2.match && w2.match("px") && (w2 = w2.replace(/px/g, ""));
            g2.run(d2, w2, z2);
          });
        }, la = f.seriesType = function(b2, c2, e2, d2, z2) {
          var w2 = ia(), g2 = f.seriesTypes;
          w2.plotOptions[b2] = S(w2.plotOptions[c2], e2);
          g2[b2] = r(g2[c2] || function() {
          }, d2);
          g2[b2].prototype.type = b2;
          z2 && (g2[b2].prototype.pointClass = r(f.Point, z2));
          return g2[b2];
        }, da, ma = f.uniqueKey = (function() {
          var b2 = Math.random().toString(36).substring(2, 9) + "-", c2 = 0;
          return function() {
            return "highcharts-" + (da ? "" : b2) + c2++;
          };
        })(), O2 = f.useSerialIds = function(b2) {
          return da = G(b2, da);
        }, na = f.isFunction = function(b2) {
          return "function" === typeof b2;
        }, ia = f.getOptions = function() {
          return f.defaultOptions;
        }, oa = f.setOptions = function(b2) {
          f.defaultOptions = S(true, f.defaultOptions, b2);
          (b2.time || b2.global) && f.time.update(S(f.defaultOptions.global, f.defaultOptions.time, b2.global, b2.time));
          return f.defaultOptions;
        };
        v.jQuery && (v.jQuery.fn.highcharts = function() {
          var b2 = [].slice.call(arguments);
          if (this[0]) return b2[0] ? (new f[K(b2[0]) ? b2.shift() : "Chart"](this[0], b2[0], b2[1]), this) : J[n2(this[0], "data-highcharts-chart")];
        });
        return {
          Fx: f.Fx,
          addEvent: ja,
          animate: ka,
          animObject: W,
          arrayMax: b,
          arrayMin: c,
          attr: n2,
          clamp: function(b2, c2, e2) {
            return b2 > c2 ? b2 < e2 ? b2 : e2 : c2;
          },
          clearTimeout: k,
          correctFloat: P,
          createElement: x,
          css: d,
          defined: m,
          destroyObjectProperties: z,
          discardElement: w,
          erase: u,
          error: a,
          extend: g,
          extendClass: r,
          find: V,
          fireEvent: ea,
          format: M,
          getDeferredAnimation: ca,
          getMagnitude: R,
          getNestedProperty: C,
          getOptions: ia,
          getStyle: ba,
          inArray: Y,
          isArray: E,
          isClass: t,
          isDOMElement: p,
          isFunction: na,
          isNumber: I,
          isObject: y,
          isString: K,
          merge: S,
          normalizeTickInterval: F,
          numberFormat: X,
          objectEach: U,
          offset: Q,
          pad: A,
          pick: G,
          pInt: q,
          relativeLength: N,
          removeEvent: ha,
          seriesType: la,
          setAnimation: Z,
          setOptions: oa,
          splat: h,
          stableSort: e,
          stop: fa,
          syncTimeout: l,
          timeUnits: aa,
          uniqueKey: ma,
          useSerialIds: O2,
          wrap: B
        };
      });
      O(n, "Core/Color.js", [n["Core/Globals.js"], n["Core/Utilities.js"]], function(f, a) {
        var S = a.isNumber, y = a.merge, n2 = a.pInt;
        a = (function() {
          function a2(f2) {
            this.parsers = [{ regex: /rgba\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]?(?:\.[0-9]+)?)\s*\)/, parse: function(a3) {
              return [n2(a3[1]), n2(a3[2]), n2(a3[3]), parseFloat(a3[4], 10)];
            } }, { regex: /rgb\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*\)/, parse: function(a3) {
              return [n2(a3[1]), n2(a3[2]), n2(a3[3]), 1];
            } }];
            this.rgba = [];
            if (!(this instanceof a2)) return new a2(f2);
            this.init(f2);
          }
          a2.parse = function(f2) {
            return new a2(f2);
          };
          a2.prototype.init = function(f2) {
            var J, H;
            if ((this.input = f2 = a2.names[f2 && f2.toLowerCase ? f2.toLowerCase() : ""] || f2) && f2.stops) this.stops = f2.stops.map(function(q) {
              return new a2(q[1]);
            });
            else {
              if (f2 && f2.charAt && "#" === f2.charAt()) {
                var v = f2.length;
                f2 = parseInt(f2.substr(1), 16);
                7 === v ? J = [(f2 & 16711680) >> 16, (f2 & 65280) >> 8, f2 & 255, 1] : 4 === v && (J = [(f2 & 3840) >> 4 | (f2 & 3840) >> 8, (f2 & 240) >> 4 | f2 & 240, (f2 & 15) << 4 | f2 & 15, 1]);
              }
              if (!J) for (H = this.parsers.length; H-- && !J; ) {
                var L = this.parsers[H];
                (v = L.regex.exec(f2)) && (J = L.parse(v));
              }
            }
            this.rgba = J || [];
          };
          a2.prototype.get = function(a3) {
            var f2 = this.input, H = this.rgba;
            if ("undefined" !== typeof this.stops) {
              var v = y(f2);
              v.stops = [].concat(v.stops);
              this.stops.forEach(function(f3, q) {
                v.stops[q] = [v.stops[q][0], f3.get(a3)];
              });
            } else v = H && S(H[0]) ? "rgb" === a3 || !a3 && 1 === H[3] ? "rgb(" + H[0] + "," + H[1] + "," + H[2] + ")" : "a" === a3 ? H[3] : "rgba(" + H.join(",") + ")" : f2;
            return v;
          };
          a2.prototype.brighten = function(a3) {
            var f2, H = this.rgba;
            if (this.stops) this.stops.forEach(function(f3) {
              f3.brighten(a3);
            });
            else if (S(a3) && 0 !== a3) for (f2 = 0; 3 > f2; f2++) H[f2] += n2(255 * a3), 0 > H[f2] && (H[f2] = 0), 255 < H[f2] && (H[f2] = 255);
            return this;
          };
          a2.prototype.setOpacity = function(a3) {
            this.rgba[3] = a3;
            return this;
          };
          a2.prototype.tweenTo = function(a3, f2) {
            var H = this.rgba, v = a3.rgba;
            v.length && H && H.length ? (a3 = 1 !== v[3] || 1 !== H[3], f2 = (a3 ? "rgba(" : "rgb(") + Math.round(v[0] + (H[0] - v[0]) * (1 - f2)) + "," + Math.round(v[1] + (H[1] - v[1]) * (1 - f2)) + "," + Math.round(v[2] + (H[2] - v[2]) * (1 - f2)) + (a3 ? "," + (v[3] + (H[3] - v[3]) * (1 - f2)) : "") + ")") : f2 = a3.input || "none";
            return f2;
          };
          a2.names = { white: "#ffffff", black: "#000000" };
          return a2;
        })();
        f.Color = a;
        f.color = a.parse;
        return f.Color;
      });
      O(n, "Core/Renderer/SVG/SVGElement.js", [n["Core/Color.js"], n["Core/Globals.js"], n["Core/Utilities.js"]], function(f, a, n2) {
        var y = a.deg2rad, D = a.doc, G = a.hasTouch, C = a.isFirefox, J = a.noop, H = a.svg, v = a.SVG_NS, L = a.win, q = n2.animate, K = n2.animObject, E = n2.attr, p = n2.createElement, t = n2.css, I = n2.defined, u = n2.erase, m = n2.extend, h = n2.fireEvent, l = n2.isArray, k = n2.isFunction, g = n2.isNumber, d = n2.isString, x = n2.merge, r = n2.objectEach, A = n2.pick, N = n2.pInt, B = n2.stop, M = n2.syncTimeout, R = n2.uniqueKey;
        "";
        n2 = (function() {
          function F() {
            this.height = this.element = void 0;
            this.opacity = 1;
            this.renderer = void 0;
            this.SVG_NS = v;
            this.symbolCustomAttribs = "x y width height r start end innerR anchorX anchorY rounded".split(" ");
            this.width = void 0;
          }
          F.prototype._defaultGetter = function(e) {
            e = A(this[e + "Value"], this[e], this.element ? this.element.getAttribute(e) : null, 0);
            /^[\-0-9\.]+$/.test(e) && (e = parseFloat(e));
            return e;
          };
          F.prototype._defaultSetter = function(e, c, b) {
            b.setAttribute(c, e);
          };
          F.prototype.add = function(e) {
            var c = this.renderer, b = this.element;
            e && (this.parentGroup = e);
            this.parentInverted = e && e.inverted;
            "undefined" !== typeof this.textStr && "text" === this.element.nodeName && c.buildText(this);
            this.added = true;
            if (!e || e.handleZ || this.zIndex) var d2 = this.zIndexSetter();
            d2 || (e ? e.element : c.box).appendChild(b);
            if (this.onAdd) this.onAdd();
            return this;
          };
          F.prototype.addClass = function(e, c) {
            var b = c ? "" : this.attr("class") || "";
            e = (e || "").split(/ /g).reduce(function(c2, e2) {
              -1 === b.indexOf(e2) && c2.push(e2);
              return c2;
            }, b ? [b] : []).join(" ");
            e !== b && this.attr(
              "class",
              e
            );
            return this;
          };
          F.prototype.afterSetters = function() {
            this.doTransform && (this.updateTransform(), this.doTransform = false);
          };
          F.prototype.align = function(e, c, b) {
            var z, w = {};
            var g2 = this.renderer;
            var h2 = g2.alignedObjects;
            var k2, l2;
            if (e) {
              if (this.alignOptions = e, this.alignByTranslate = c, !b || d(b)) this.alignTo = z = b || "renderer", u(h2, this), h2.push(this), b = void 0;
            } else e = this.alignOptions, c = this.alignByTranslate, z = this.alignTo;
            b = A(b, g2[z], g2);
            z = e.align;
            g2 = e.verticalAlign;
            h2 = (b.x || 0) + (e.x || 0);
            var r2 = (b.y || 0) + (e.y || 0);
            "right" === z ? k2 = 1 : "center" === z && (k2 = 2);
            k2 && (h2 += (b.width - (e.width || 0)) / k2);
            w[c ? "translateX" : "x"] = Math.round(h2);
            "bottom" === g2 ? l2 = 1 : "middle" === g2 && (l2 = 2);
            l2 && (r2 += (b.height - (e.height || 0)) / l2);
            w[c ? "translateY" : "y"] = Math.round(r2);
            this[this.placed ? "animate" : "attr"](w);
            this.placed = true;
            this.alignAttr = w;
            return this;
          };
          F.prototype.alignSetter = function(e) {
            var c = { left: "start", center: "middle", right: "end" };
            c[e] && (this.alignValue = e, this.element.setAttribute("text-anchor", c[e]));
          };
          F.prototype.animate = function(e, c, b) {
            var d2 = this, w = K(A(
              c,
              this.renderer.globalAnimation,
              true
            ));
            c = w.defer;
            A(D.hidden, D.msHidden, D.webkitHidden, false) && (w.duration = 0);
            0 !== w.duration ? (b && (w.complete = b), M(function() {
              d2.element && q(d2, e, w);
            }, c)) : (this.attr(e, void 0, b), r(e, function(b2, c2) {
              w.step && w.step.call(this, b2, { prop: c2, pos: 1 });
            }, this));
            return this;
          };
          F.prototype.applyTextOutline = function(e) {
            var c = this.element, b;
            -1 !== e.indexOf("contrast") && (e = e.replace(/contrast/g, this.renderer.getContrast(c.style.fill)));
            e = e.split(" ");
            var d2 = e[e.length - 1];
            if ((b = e[0]) && "none" !== b && a.svg) {
              this.fakeTS = true;
              e = [].slice.call(c.getElementsByTagName("tspan"));
              this.ySetter = this.xSetter;
              b = b.replace(/(^[\d\.]+)(.*?)$/g, function(b2, c2, e2) {
                return 2 * c2 + e2;
              });
              this.removeTextOutline(e);
              var w = c.textContent ? /^[\u0591-\u065F\u066A-\u07FF\uFB1D-\uFDFD\uFE70-\uFEFC]/.test(c.textContent) : false;
              var g2 = c.firstChild;
              e.forEach(function(e2, z) {
                0 === z && (e2.setAttribute("x", c.getAttribute("x")), z = c.getAttribute("y"), e2.setAttribute("y", z || 0), null === z && c.setAttribute("y", 0));
                z = e2.cloneNode(true);
                E(w && !C ? e2 : z, { "class": "highcharts-text-outline", fill: d2, stroke: d2, "stroke-width": b, "stroke-linejoin": "round" });
                c.insertBefore(z, g2);
              });
              w && C && e[0] && (e = e[0].cloneNode(true), e.textContent = " ", c.insertBefore(e, g2));
            }
          };
          F.prototype.attr = function(e, c, b, d2) {
            var z = this.element, g2, h2 = this, k2, l2, m2 = this.symbolCustomAttribs;
            if ("string" === typeof e && "undefined" !== typeof c) {
              var x2 = e;
              e = {};
              e[x2] = c;
            }
            "string" === typeof e ? h2 = (this[e + "Getter"] || this._defaultGetter).call(this, e, z) : (r(e, function(b2, c2) {
              k2 = false;
              d2 || B(this, c2);
              this.symbolName && -1 !== m2.indexOf(c2) && (g2 || (this.symbolAttr(e), g2 = true), k2 = true);
              !this.rotation || "x" !== c2 && "y" !== c2 || (this.doTransform = true);
              k2 || (l2 = this[c2 + "Setter"] || this._defaultSetter, l2.call(this, b2, c2, z), !this.styledMode && this.shadows && /^(width|height|visibility|x|y|d|transform|cx|cy|r)$/.test(c2) && this.updateShadows(c2, b2, l2));
            }, this), this.afterSetters());
            b && b.call(this);
            return h2;
          };
          F.prototype.clip = function(e) {
            return this.attr("clip-path", e ? "url(" + this.renderer.url + "#" + e.id + ")" : "none");
          };
          F.prototype.crisp = function(e, c) {
            c = c || e.strokeWidth || 0;
            var b = Math.round(c) % 2 / 2;
            e.x = Math.floor(e.x || this.x || 0) + b;
            e.y = Math.floor(e.y || this.y || 0) + b;
            e.width = Math.floor((e.width || this.width || 0) - 2 * b);
            e.height = Math.floor((e.height || this.height || 0) - 2 * b);
            I(e.strokeWidth) && (e.strokeWidth = c);
            return e;
          };
          F.prototype.complexColor = function(e, c, b) {
            var d2 = this.renderer, w, g2, k2, m2, p2, B2, t2, A2, u2, M2, Q = [], F2;
            h(this.renderer, "complexColor", { args: arguments }, function() {
              e.radialGradient ? g2 = "radialGradient" : e.linearGradient && (g2 = "linearGradient");
              if (g2) {
                k2 = e[g2];
                p2 = d2.gradients;
                B2 = e.stops;
                u2 = b.radialReference;
                l(k2) && (e[g2] = k2 = { x1: k2[0], y1: k2[1], x2: k2[2], y2: k2[3], gradientUnits: "userSpaceOnUse" });
                "radialGradient" === g2 && u2 && !I(k2.gradientUnits) && (m2 = k2, k2 = x(k2, d2.getRadialAttr(u2, m2), { gradientUnits: "userSpaceOnUse" }));
                r(k2, function(b2, c2) {
                  "id" !== c2 && Q.push(c2, b2);
                });
                r(B2, function(b2) {
                  Q.push(b2);
                });
                Q = Q.join(",");
                if (p2[Q]) M2 = p2[Q].attr("id");
                else {
                  k2.id = M2 = R();
                  var z = p2[Q] = d2.createElement(g2).attr(k2).add(d2.defs);
                  z.radAttr = m2;
                  z.stops = [];
                  B2.forEach(function(b2) {
                    0 === b2[1].indexOf("rgba") ? (w = f.parse(b2[1]), t2 = w.get("rgb"), A2 = w.get("a")) : (t2 = b2[1], A2 = 1);
                    b2 = d2.createElement("stop").attr({ offset: b2[0], "stop-color": t2, "stop-opacity": A2 }).add(z);
                    z.stops.push(b2);
                  });
                }
                F2 = "url(" + d2.url + "#" + M2 + ")";
                b.setAttribute(c, F2);
                b.gradient = Q;
                e.toString = function() {
                  return F2;
                };
              }
            });
          };
          F.prototype.css = function(e) {
            var c = this.styles, b = {}, d2 = this.element, w = "", g2 = !c, k2 = ["textOutline", "textOverflow", "width"];
            e && e.color && (e.fill = e.color);
            c && r(e, function(e2, d3) {
              c && c[d3] !== e2 && (b[d3] = e2, g2 = true);
            });
            if (g2) {
              c && (e = m(c, b));
              if (e) {
                if (null === e.width || "auto" === e.width) delete this.textWidth;
                else if ("text" === d2.nodeName.toLowerCase() && e.width) var h2 = this.textWidth = N(e.width);
              }
              this.styles = e;
              h2 && !H && this.renderer.forExport && delete e.width;
              if (d2.namespaceURI === this.SVG_NS) {
                var l2 = function(b2, c2) {
                  return "-" + c2.toLowerCase();
                };
                r(e, function(b2, c2) {
                  -1 === k2.indexOf(c2) && (w += c2.replace(/([A-Z])/g, l2) + ":" + b2 + ";");
                });
                w && E(d2, "style", w);
              } else t(d2, e);
              this.added && ("text" === this.element.nodeName && this.renderer.buildText(this), e && e.textOutline && this.applyTextOutline(e.textOutline));
            }
            return this;
          };
          F.prototype.dashstyleSetter = function(e) {
            var c = this["stroke-width"];
            "inherit" === c && (c = 1);
            if (e = e && e.toLowerCase()) {
              var b = e.replace("shortdashdotdot", "3,1,1,1,1,1,").replace(
                "shortdashdot",
                "3,1,1,1"
              ).replace("shortdot", "1,1,").replace("shortdash", "3,1,").replace("longdash", "8,3,").replace(/dot/g, "1,3,").replace("dash", "4,3,").replace(/,$/, "").split(",");
              for (e = b.length; e--; ) b[e] = "" + N(b[e]) * A(c, NaN);
              e = b.join(",").replace(/NaN/g, "none");
              this.element.setAttribute("stroke-dasharray", e);
            }
          };
          F.prototype.destroy = function() {
            var e = this, c = e.element || {}, b = e.renderer, d2 = b.isSVG && "SPAN" === c.nodeName && e.parentGroup || void 0, w = c.ownerSVGElement;
            c.onclick = c.onmouseout = c.onmouseover = c.onmousemove = c.point = null;
            B(e);
            if (e.clipPath && w) {
              var g2 = e.clipPath;
              [].forEach.call(w.querySelectorAll("[clip-path],[CLIP-PATH]"), function(b2) {
                -1 < b2.getAttribute("clip-path").indexOf(g2.element.id) && b2.removeAttribute("clip-path");
              });
              e.clipPath = g2.destroy();
            }
            if (e.stops) {
              for (w = 0; w < e.stops.length; w++) e.stops[w].destroy();
              e.stops.length = 0;
              e.stops = void 0;
            }
            e.safeRemoveChild(c);
            for (b.styledMode || e.destroyShadows(); d2 && d2.div && 0 === d2.div.childNodes.length; ) c = d2.parentGroup, e.safeRemoveChild(d2.div), delete d2.div, d2 = c;
            e.alignTo && u(
              b.alignedObjects,
              e
            );
            r(e, function(b2, c2) {
              e[c2] && e[c2].parentGroup === e && e[c2].destroy && e[c2].destroy();
              delete e[c2];
            });
          };
          F.prototype.destroyShadows = function() {
            (this.shadows || []).forEach(function(e) {
              this.safeRemoveChild(e);
            }, this);
            this.shadows = void 0;
          };
          F.prototype.destroyTextPath = function(e, c) {
            var b = e.getElementsByTagName("text")[0];
            if (b) {
              if (b.removeAttribute("dx"), b.removeAttribute("dy"), c.element.setAttribute("id", ""), this.textPathWrapper && b.getElementsByTagName("textPath").length) {
                for (e = this.textPathWrapper.element.childNodes; e.length; ) b.appendChild(e[0]);
                b.removeChild(this.textPathWrapper.element);
              }
            } else if (e.getAttribute("dx") || e.getAttribute("dy")) e.removeAttribute("dx"), e.removeAttribute("dy");
            this.textPathWrapper && (this.textPathWrapper = this.textPathWrapper.destroy());
          };
          F.prototype.dSetter = function(e, c, b) {
            l(e) && ("string" === typeof e[0] && (e = this.renderer.pathToSegments(e)), this.pathArray = e, e = e.reduce(function(b2, c2, e2) {
              return c2 && c2.join ? (e2 ? b2 + " " : "") + c2.join(" ") : (c2 || "").toString();
            }, ""));
            /(NaN| {2}|^$)/.test(e) && (e = "M 0 0");
            this[c] !== e && (b.setAttribute(
              c,
              e
            ), this[c] = e);
          };
          F.prototype.fadeOut = function(e) {
            var c = this;
            c.animate({ opacity: 0 }, { duration: A(e, 150), complete: function() {
              c.attr({ y: -9999 }).hide();
            } });
          };
          F.prototype.fillSetter = function(e, c, b) {
            "string" === typeof e ? b.setAttribute(c, e) : e && this.complexColor(e, c, b);
          };
          F.prototype.getBBox = function(e, c) {
            var b, d2 = this.renderer, w = this.element, g2 = this.styles, h2 = this.textStr, l2 = d2.cache, r2 = d2.cacheKeys, x2 = w.namespaceURI === this.SVG_NS;
            c = A(c, this.rotation, 0);
            var p2 = d2.styledMode ? w && F.prototype.getStyle.call(w, "font-size") : g2 && g2.fontSize;
            if (I(h2)) {
              var B2 = h2.toString();
              -1 === B2.indexOf("<") && (B2 = B2.replace(/[0-9]/g, "0"));
              B2 += ["", c, p2, this.textWidth, g2 && g2.textOverflow, g2 && g2.fontWeight].join();
            }
            B2 && !e && (b = l2[B2]);
            if (!b) {
              if (x2 || d2.forExport) {
                try {
                  var t2 = this.fakeTS && function(b2) {
                    [].forEach.call(w.querySelectorAll(".highcharts-text-outline"), function(c2) {
                      c2.style.display = b2;
                    });
                  };
                  k(t2) && t2("none");
                  b = w.getBBox ? m({}, w.getBBox()) : { width: w.offsetWidth, height: w.offsetHeight };
                  k(t2) && t2("");
                } catch (V) {
                  "";
                }
                if (!b || 0 > b.width) b = { width: 0, height: 0 };
              } else b = this.htmlGetBBox();
              d2.isSVG && (e = b.width, d2 = b.height, x2 && (b.height = d2 = { "11px,17": 14, "13px,20": 16 }[g2 && g2.fontSize + "," + Math.round(d2)] || d2), c && (g2 = c * y, b.width = Math.abs(d2 * Math.sin(g2)) + Math.abs(e * Math.cos(g2)), b.height = Math.abs(d2 * Math.cos(g2)) + Math.abs(e * Math.sin(g2))));
              if (B2 && 0 < b.height) {
                for (; 250 < r2.length; ) delete l2[r2.shift()];
                l2[B2] || r2.push(B2);
                l2[B2] = b;
              }
            }
            return b;
          };
          F.prototype.getStyle = function(e) {
            return L.getComputedStyle(this.element || this, "").getPropertyValue(e);
          };
          F.prototype.hasClass = function(e) {
            return -1 !== ("" + this.attr("class")).split(" ").indexOf(e);
          };
          F.prototype.hide = function(e) {
            e ? this.attr({ y: -9999 }) : this.attr({ visibility: "hidden" });
            return this;
          };
          F.prototype.htmlGetBBox = function() {
            return { height: 0, width: 0, x: 0, y: 0 };
          };
          F.prototype.init = function(e, c) {
            this.element = "span" === c ? p(c) : D.createElementNS(this.SVG_NS, c);
            this.renderer = e;
            h(this, "afterInit");
          };
          F.prototype.invert = function(e) {
            this.inverted = e;
            this.updateTransform();
            return this;
          };
          F.prototype.on = function(e, c) {
            var b, d2, w = this.element, g2;
            G && "click" === e ? (w.ontouchstart = function(c2) {
              b = c2.touches[0].clientX;
              d2 = c2.touches[0].clientY;
            }, w.ontouchend = function(e2) {
              b && 4 <= Math.sqrt(Math.pow(b - e2.changedTouches[0].clientX, 2) + Math.pow(d2 - e2.changedTouches[0].clientY, 2)) || c.call(w, e2);
              g2 = true;
              e2.preventDefault();
            }, w.onclick = function(b2) {
              g2 || c.call(w, b2);
            }) : w["on" + e] = c;
            return this;
          };
          F.prototype.opacitySetter = function(e, c, b) {
            this[c] = e;
            b.setAttribute(c, e);
          };
          F.prototype.removeClass = function(e) {
            return this.attr("class", ("" + this.attr("class")).replace(d(e) ? new RegExp("(^| )" + e + "( |$)") : e, " ").replace(/ +/g, " ").trim());
          };
          F.prototype.removeTextOutline = function(e) {
            for (var c = e.length, b; c--; ) b = e[c], "highcharts-text-outline" === b.getAttribute("class") && u(e, this.element.removeChild(b));
          };
          F.prototype.safeRemoveChild = function(e) {
            var c = e.parentNode;
            c && c.removeChild(e);
          };
          F.prototype.setRadialReference = function(e) {
            var c = this.element.gradient && this.renderer.gradients[this.element.gradient];
            this.element.radialReference = e;
            c && c.radAttr && c.animate(this.renderer.getRadialAttr(e, c.radAttr));
            return this;
          };
          F.prototype.setTextPath = function(e, c) {
            var b = this.element, d2 = { textAnchor: "text-anchor" }, w = false, k2 = this.textPathWrapper, h2 = !k2;
            c = x(true, { enabled: true, attributes: { dy: -5, startOffset: "50%", textAnchor: "middle" } }, c);
            var l2 = c.attributes;
            if (e && c && c.enabled) {
              k2 && null === k2.element.parentNode ? (h2 = true, k2 = k2.destroy()) : k2 && this.removeTextOutline.call(k2.parentGroup, [].slice.call(b.getElementsByTagName("tspan")));
              this.options && this.options.padding && (l2.dx = -this.options.padding);
              k2 || (this.textPathWrapper = k2 = this.renderer.createElement("textPath"), w = true);
              var m2 = k2.element;
              (c = e.element.getAttribute("id")) || e.element.setAttribute(
                "id",
                c = R()
              );
              if (h2) for (e = b.getElementsByTagName("tspan"); e.length; ) e[0].setAttribute("y", 0), g(l2.dx) && e[0].setAttribute("x", -l2.dx), m2.appendChild(e[0]);
              w && k2 && k2.add({ element: this.text ? this.text.element : b });
              m2.setAttributeNS("http://www.w3.org/1999/xlink", "href", this.renderer.url + "#" + c);
              I(l2.dy) && (m2.parentNode.setAttribute("dy", l2.dy), delete l2.dy);
              I(l2.dx) && (m2.parentNode.setAttribute("dx", l2.dx), delete l2.dx);
              r(l2, function(b2, c2) {
                m2.setAttribute(d2[c2] || c2, b2);
              });
              b.removeAttribute("transform");
              this.removeTextOutline.call(
                k2,
                [].slice.call(b.getElementsByTagName("tspan"))
              );
              this.text && !this.renderer.styledMode && this.attr({ fill: "none", "stroke-width": 0 });
              this.applyTextOutline = this.updateTransform = J;
            } else k2 && (delete this.updateTransform, delete this.applyTextOutline, this.destroyTextPath(b, e), this.updateTransform(), this.options && this.options.rotation && this.applyTextOutline(this.options.style.textOutline));
            return this;
          };
          F.prototype.shadow = function(e, c, b) {
            var d2 = [], g2 = this.element, k2 = false, h2 = this.oldShadowOptions;
            var l2 = {
              color: "#000000",
              offsetX: 1,
              offsetY: 1,
              opacity: 0.15,
              width: 3
            };
            var x2;
            true === e ? x2 = l2 : "object" === typeof e && (x2 = m(l2, e));
            x2 && (x2 && h2 && r(x2, function(b2, c2) {
              b2 !== h2[c2] && (k2 = true);
            }), k2 && this.destroyShadows(), this.oldShadowOptions = x2);
            if (!x2) this.destroyShadows();
            else if (!this.shadows) {
              var p2 = x2.opacity / x2.width;
              var B2 = this.parentInverted ? "translate(-1,-1)" : "translate(" + x2.offsetX + ", " + x2.offsetY + ")";
              for (l2 = 1; l2 <= x2.width; l2++) {
                var t2 = g2.cloneNode(false);
                var A2 = 2 * x2.width + 1 - 2 * l2;
                E(t2, { stroke: e.color || "#000000", "stroke-opacity": p2 * l2, "stroke-width": A2, transform: B2, fill: "none" });
                t2.setAttribute("class", (t2.getAttribute("class") || "") + " highcharts-shadow");
                b && (E(t2, "height", Math.max(E(t2, "height") - A2, 0)), t2.cutHeight = A2);
                c ? c.element.appendChild(t2) : g2.parentNode && g2.parentNode.insertBefore(t2, g2);
                d2.push(t2);
              }
              this.shadows = d2;
            }
            return this;
          };
          F.prototype.show = function(e) {
            return this.attr({ visibility: e ? "inherit" : "visible" });
          };
          F.prototype.strokeSetter = function(e, c, b) {
            this[c] = e;
            this.stroke && this["stroke-width"] ? (F.prototype.fillSetter.call(this, this.stroke, "stroke", b), b.setAttribute(
              "stroke-width",
              this["stroke-width"]
            ), this.hasStroke = true) : "stroke-width" === c && 0 === e && this.hasStroke ? (b.removeAttribute("stroke"), this.hasStroke = false) : this.renderer.styledMode && this["stroke-width"] && (b.setAttribute("stroke-width", this["stroke-width"]), this.hasStroke = true);
          };
          F.prototype.strokeWidth = function() {
            if (!this.renderer.styledMode) return this["stroke-width"] || 0;
            var e = this.getStyle("stroke-width"), c = 0;
            if (e.indexOf("px") === e.length - 2) c = N(e);
            else if ("" !== e) {
              var b = D.createElementNS(v, "rect");
              E(b, { width: e, "stroke-width": 0 });
              this.element.parentNode.appendChild(b);
              c = b.getBBox().width;
              b.parentNode.removeChild(b);
            }
            return c;
          };
          F.prototype.symbolAttr = function(e) {
            var c = this;
            "x y r start end width height innerR anchorX anchorY clockwise".split(" ").forEach(function(b) {
              c[b] = A(e[b], c[b]);
            });
            c.attr({ d: c.renderer.symbols[c.symbolName](c.x, c.y, c.width, c.height, c) });
          };
          F.prototype.textSetter = function(e) {
            e !== this.textStr && (delete this.textPxLength, this.textStr = e, this.added && this.renderer.buildText(this));
          };
          F.prototype.titleSetter = function(e) {
            var c = this.element.getElementsByTagName("title")[0];
            c || (c = D.createElementNS(this.SVG_NS, "title"), this.element.appendChild(c));
            c.firstChild && c.removeChild(c.firstChild);
            c.appendChild(D.createTextNode(String(A(e, "")).replace(/<[^>]*>/g, "").replace(/&lt;/g, "<").replace(/&gt;/g, ">")));
          };
          F.prototype.toFront = function() {
            var e = this.element;
            e.parentNode.appendChild(e);
            return this;
          };
          F.prototype.translate = function(e, c) {
            return this.attr({ translateX: e, translateY: c });
          };
          F.prototype.updateShadows = function(e, c, b) {
            var d2 = this.shadows;
            if (d2) for (var g2 = d2.length; g2--; ) b.call(d2[g2], "height" === e ? Math.max(c - (d2[g2].cutHeight || 0), 0) : "d" === e ? this.d : c, e, d2[g2]);
          };
          F.prototype.updateTransform = function() {
            var e = this.translateX || 0, c = this.translateY || 0, b = this.scaleX, d2 = this.scaleY, g2 = this.inverted, k2 = this.rotation, h2 = this.matrix, l2 = this.element;
            g2 && (e += this.width, c += this.height);
            e = ["translate(" + e + "," + c + ")"];
            I(h2) && e.push("matrix(" + h2.join(",") + ")");
            g2 ? e.push("rotate(90) scale(-1,1)") : k2 && e.push("rotate(" + k2 + " " + A(this.rotationOriginX, l2.getAttribute("x"), 0) + " " + A(this.rotationOriginY, l2.getAttribute("y") || 0) + ")");
            (I(b) || I(d2)) && e.push("scale(" + A(b, 1) + " " + A(d2, 1) + ")");
            e.length && l2.setAttribute("transform", e.join(" "));
          };
          F.prototype.visibilitySetter = function(e, c, b) {
            "inherit" === e ? b.removeAttribute(c) : this[c] !== e && b.setAttribute(c, e);
            this[c] = e;
          };
          F.prototype.xGetter = function(e) {
            "circle" === this.element.nodeName && ("x" === e ? e = "cx" : "y" === e && (e = "cy"));
            return this._defaultGetter(e);
          };
          F.prototype.zIndexSetter = function(e, c) {
            var b = this.renderer, d2 = this.parentGroup, g2 = (d2 || b).element || b.box, k2 = this.element, h2 = false;
            b = g2 === b.box;
            var l2 = this.added;
            var r2;
            I(e) ? (k2.setAttribute("data-z-index", e), e = +e, this[c] === e && (l2 = false)) : I(this[c]) && k2.removeAttribute("data-z-index");
            this[c] = e;
            if (l2) {
              (e = this.zIndex) && d2 && (d2.handleZ = true);
              c = g2.childNodes;
              for (r2 = c.length - 1; 0 <= r2 && !h2; r2--) {
                d2 = c[r2];
                l2 = d2.getAttribute("data-z-index");
                var m2 = !I(l2);
                if (d2 !== k2) {
                  if (0 > e && m2 && !b && !r2) g2.insertBefore(k2, c[r2]), h2 = true;
                  else if (N(l2) <= e || m2 && (!I(e) || 0 <= e)) g2.insertBefore(k2, c[r2 + 1] || null), h2 = true;
                }
              }
              h2 || (g2.insertBefore(k2, c[b ? 3 : 0] || null), h2 = true);
            }
            return h2;
          };
          return F;
        })();
        n2.prototype["stroke-widthSetter"] = n2.prototype.strokeSetter;
        n2.prototype.yGetter = n2.prototype.xGetter;
        n2.prototype.matrixSetter = n2.prototype.rotationOriginXSetter = n2.prototype.rotationOriginYSetter = n2.prototype.rotationSetter = n2.prototype.scaleXSetter = n2.prototype.scaleYSetter = n2.prototype.translateXSetter = n2.prototype.translateYSetter = n2.prototype.verticalAlignSetter = function(d2, e) {
          this[e] = d2;
          this.doTransform = true;
        };
        a.SVGElement = n2;
        return a.SVGElement;
      });
      O(n, "Core/Renderer/SVG/SVGLabel.js", [
        n["Core/Renderer/SVG/SVGElement.js"],
        n["Core/Utilities.js"]
      ], function(f, a) {
        var n2 = this && this.__extends || /* @__PURE__ */ (function() {
          var a2 = function(f2, L) {
            a2 = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(a3, f3) {
              a3.__proto__ = f3;
            } || function(a3, f3) {
              for (var q in f3) f3.hasOwnProperty(q) && (a3[q] = f3[q]);
            };
            return a2(f2, L);
          };
          return function(f2, L) {
            function q() {
              this.constructor = f2;
            }
            a2(f2, L);
            f2.prototype = null === L ? Object.create(L) : (q.prototype = L.prototype, new q());
          };
        })(), y = a.defined, D = a.extend, G = a.isNumber, C = a.merge, J = a.removeEvent;
        return (function(a2) {
          function v(f2, q, H, E, p, t, I, u, m, h) {
            var l = a2.call(this) || this;
            l.init(f2, "g");
            l.textStr = q;
            l.x = H;
            l.y = E;
            l.anchorX = t;
            l.anchorY = I;
            l.baseline = m;
            l.className = h;
            "button" !== h && l.addClass("highcharts-label");
            h && l.addClass("highcharts-" + h);
            l.text = f2.text("", 0, 0, u).attr({ zIndex: 1 });
            if ("string" === typeof p) {
              var k = /^url\((.*?)\)$/.test(p);
              if (l.renderer.symbols[p] || k) l.symbolKey = p;
            }
            l.bBox = v.emptyBBox;
            l.padding = 3;
            l.paddingLeft = 0;
            l.baselineOffset = 0;
            l.needsBox = f2.styledMode || k;
            l.deferredAttr = {};
            l.alignFactor = 0;
            return l;
          }
          n2(v, a2);
          v.prototype.alignSetter = function(a3) {
            a3 = { left: 0, center: 0.5, right: 1 }[a3];
            a3 !== this.alignFactor && (this.alignFactor = a3, this.bBox && G(this.xSetting) && this.attr({ x: this.xSetting }));
          };
          v.prototype.anchorXSetter = function(a3, f2) {
            this.anchorX = a3;
            this.boxAttr(f2, Math.round(a3) - this.getCrispAdjust() - this.xSetting);
          };
          v.prototype.anchorYSetter = function(a3, f2) {
            this.anchorY = a3;
            this.boxAttr(f2, a3 - this.ySetting);
          };
          v.prototype.boxAttr = function(a3, f2) {
            this.box ? this.box.attr(a3, f2) : this.deferredAttr[a3] = f2;
          };
          v.prototype.css = function(a3) {
            if (a3) {
              var q = {};
              a3 = C(a3);
              v.textProps.forEach(function(f2) {
                "undefined" !== typeof a3[f2] && (q[f2] = a3[f2], delete a3[f2]);
              });
              this.text.css(q);
              var L = "fontSize" in q || "fontWeight" in q;
              if ("width" in q || L) this.updateBoxSize(), L && this.updateTextPadding();
            }
            return f.prototype.css.call(this, a3);
          };
          v.prototype.destroy = function() {
            J(this.element, "mouseenter");
            J(this.element, "mouseleave");
            this.text && this.text.destroy();
            this.box && (this.box = this.box.destroy());
            f.prototype.destroy.call(this);
          };
          v.prototype.fillSetter = function(a3, f2) {
            a3 && (this.needsBox = true);
            this.fill = a3;
            this.boxAttr(f2, a3);
          };
          v.prototype.getBBox = function() {
            var a3 = this.bBox, f2 = this.padding;
            return { width: a3.width + 2 * f2, height: a3.height + 2 * f2, x: a3.x - f2, y: a3.y - f2 };
          };
          v.prototype.getCrispAdjust = function() {
            return this.renderer.styledMode && this.box ? this.box.strokeWidth() % 2 / 2 : (this["stroke-width"] ? parseInt(this["stroke-width"], 10) : 0) % 2 / 2;
          };
          v.prototype.heightSetter = function(a3) {
            this.heightSetting = a3;
          };
          v.prototype.on = function(a3, q) {
            var v2 = this, E = v2.text, p = E && "SPAN" === E.element.tagName ? E : void 0;
            if (p) {
              var t = function(t2) {
                ("mouseenter" === a3 || "mouseleave" === a3) && t2.relatedTarget instanceof Element && (v2.element.contains(t2.relatedTarget) || p.element.contains(t2.relatedTarget)) || q.call(v2.element, t2);
              };
              p.on(a3, t);
            }
            f.prototype.on.call(v2, a3, t || q);
            return v2;
          };
          v.prototype.onAdd = function() {
            var a3 = this.textStr;
            this.text.add(this);
            this.attr({ text: y(a3) ? a3 : "", x: this.x, y: this.y });
            this.box && y(this.anchorX) && this.attr({ anchorX: this.anchorX, anchorY: this.anchorY });
          };
          v.prototype.paddingSetter = function(a3) {
            y(a3) && a3 !== this.padding && (this.padding = a3, this.updateTextPadding());
          };
          v.prototype.paddingLeftSetter = function(a3) {
            y(a3) && a3 !== this.paddingLeft && (this.paddingLeft = a3, this.updateTextPadding());
          };
          v.prototype.rSetter = function(a3, f2) {
            this.boxAttr(f2, a3);
          };
          v.prototype.shadow = function(a3) {
            a3 && !this.renderer.styledMode && (this.updateBoxSize(), this.box && this.box.shadow(a3));
            return this;
          };
          v.prototype.strokeSetter = function(a3, f2) {
            this.stroke = a3;
            this.boxAttr(f2, a3);
          };
          v.prototype["stroke-widthSetter"] = function(a3, f2) {
            a3 && (this.needsBox = true);
            this["stroke-width"] = a3;
            this.boxAttr(f2, a3);
          };
          v.prototype["text-alignSetter"] = function(a3) {
            this.textAlign = a3;
          };
          v.prototype.textSetter = function(a3) {
            "undefined" !== typeof a3 && this.text.attr({ text: a3 });
            this.updateBoxSize();
            this.updateTextPadding();
          };
          v.prototype.updateBoxSize = function() {
            var a3 = this.text.element.style, f2 = {}, H = this.padding, E = this.paddingLeft, p = G(this.widthSetting) && G(this.heightSetting) && !this.textAlign || !y(this.text.textStr) ? v.emptyBBox : this.text.getBBox();
            this.width = (this.widthSetting || p.width || 0) + 2 * H + E;
            this.height = (this.heightSetting || p.height || 0) + 2 * H;
            this.baselineOffset = H + Math.min(this.renderer.fontMetrics(
              a3 && a3.fontSize,
              this.text
            ).b, p.height || Infinity);
            this.needsBox && (this.box || (a3 = this.box = this.symbolKey ? this.renderer.symbol(this.symbolKey) : this.renderer.rect(), a3.addClass(("button" === this.className ? "" : "highcharts-label-box") + (this.className ? " highcharts-" + this.className + "-box" : "")), a3.add(this), a3 = this.getCrispAdjust(), f2.x = a3, f2.y = (this.baseline ? -this.baselineOffset : 0) + a3), f2.width = Math.round(this.width), f2.height = Math.round(this.height), this.box.attr(D(f2, this.deferredAttr)), this.deferredAttr = {});
            this.bBox = p;
          };
          v.prototype.updateTextPadding = function() {
            var a3 = this.text, f2 = this.baseline ? 0 : this.baselineOffset, v2 = this.paddingLeft + this.padding;
            y(this.widthSetting) && this.bBox && ("center" === this.textAlign || "right" === this.textAlign) && (v2 += { center: 0.5, right: 1 }[this.textAlign] * (this.widthSetting - this.bBox.width));
            if (v2 !== a3.x || f2 !== a3.y) a3.attr("x", v2), a3.hasBoxWidthChanged && (this.bBox = a3.getBBox(true), this.updateBoxSize()), "undefined" !== typeof f2 && a3.attr("y", f2);
            a3.x = v2;
            a3.y = f2;
          };
          v.prototype.widthSetter = function(a3) {
            this.widthSetting = G(a3) ? a3 : void 0;
          };
          v.prototype.xSetter = function(a3) {
            this.x = a3;
            this.alignFactor && (a3 -= this.alignFactor * ((this.widthSetting || this.bBox.width) + 2 * this.padding), this["forceAnimate:x"] = true);
            this.xSetting = Math.round(a3);
            this.attr("translateX", this.xSetting);
          };
          v.prototype.ySetter = function(a3) {
            this.ySetting = this.y = Math.round(a3);
            this.attr("translateY", this.ySetting);
          };
          v.emptyBBox = { width: 0, height: 0, x: 0, y: 0 };
          v.textProps = "color cursor direction fontFamily fontSize fontStyle fontWeight lineHeight textAlign textDecoration textOutline textOverflow width".split(" ");
          return v;
        })(f);
      });
      O(n, "Core/Renderer/SVG/SVGRenderer.js", [n["Core/Color.js"], n["Core/Globals.js"], n["Core/Renderer/SVG/SVGElement.js"], n["Core/Renderer/SVG/SVGLabel.js"], n["Core/Utilities.js"]], function(f, a, n2, y, D) {
        var G = D.addEvent, C = D.attr, J = D.createElement, H = D.css, v = D.defined, L = D.destroyObjectProperties, q = D.extend, K = D.isArray, E = D.isNumber, p = D.isObject, t = D.isString, I = D.merge, u = D.objectEach, m = D.pick, h = D.pInt, l = D.splat, k = D.uniqueKey, g = a.charts, d = a.deg2rad, x = a.doc, r = a.isFirefox, A = a.isMS, N = a.isWebKit;
        D = a.noop;
        var B = a.svg, M = a.SVG_NS, R = a.symbolSizes, F = a.win, e = (function() {
          function c(b, c2, e2, d2, g2, k2, h2) {
            this.width = this.url = this.style = this.isSVG = this.imgCount = this.height = this.gradients = this.globalAnimation = this.defs = this.chartIndex = this.cacheKeys = this.cache = this.boxWrapper = this.box = this.alignedObjects = void 0;
            this.init(b, c2, e2, d2, g2, k2, h2);
          }
          c.prototype.init = function(b, c2, e2, d2, g2, k2, h2) {
            var w = this.createElement("svg").attr({ version: "1.1", "class": "highcharts-root" });
            h2 || w.css(this.getStyle(d2));
            d2 = w.element;
            b.appendChild(d2);
            C(b, "dir", "ltr");
            -1 === b.innerHTML.indexOf("xmlns") && C(d2, "xmlns", this.SVG_NS);
            this.isSVG = true;
            this.box = d2;
            this.boxWrapper = w;
            this.alignedObjects = [];
            this.url = (r || N) && x.getElementsByTagName("base").length ? F.location.href.split("#")[0].replace(/<[^>]*>/g, "").replace(/([\('\)])/g, "\\$1").replace(/ /g, "%20") : "";
            this.createElement("desc").add().element.appendChild(x.createTextNode("Created with Highcharts 8.2.0"));
            this.defs = this.createElement("defs").add();
            this.allowHTML = k2;
            this.forExport = g2;
            this.styledMode = h2;
            this.gradients = {};
            this.cache = {};
            this.cacheKeys = [];
            this.imgCount = 0;
            this.setSize(c2, e2, false);
            var z;
            r && b.getBoundingClientRect && (c2 = function() {
              H(b, { left: 0, top: 0 });
              z = b.getBoundingClientRect();
              H(b, { left: Math.ceil(z.left) - z.left + "px", top: Math.ceil(z.top) - z.top + "px" });
            }, c2(), this.unSubPixelFix = G(F, "resize", c2));
          };
          c.prototype.definition = function(b) {
            function c2(b2, d2) {
              var g2;
              l(b2).forEach(function(b3) {
                var w = e2.createElement(b3.tagName), z = {};
                u(b3, function(b4, c3) {
                  "tagName" !== c3 && "children" !== c3 && "textContent" !== c3 && (z[c3] = b4);
                });
                w.attr(z);
                w.add(d2 || e2.defs);
                b3.textContent && w.element.appendChild(x.createTextNode(b3.textContent));
                c2(b3.children || [], w);
                g2 = w;
              });
              return g2;
            }
            var e2 = this;
            return c2(b);
          };
          c.prototype.getStyle = function(b) {
            return this.style = q({ fontFamily: '"Lucida Grande", "Lucida Sans Unicode", Arial, Helvetica, sans-serif', fontSize: "12px" }, b);
          };
          c.prototype.setStyle = function(b) {
            this.boxWrapper.css(this.getStyle(b));
          };
          c.prototype.isHidden = function() {
            return !this.boxWrapper.getBBox().width;
          };
          c.prototype.destroy = function() {
            var b = this.defs;
            this.box = null;
            this.boxWrapper = this.boxWrapper.destroy();
            L(this.gradients || {});
            this.gradients = null;
            b && (this.defs = b.destroy());
            this.unSubPixelFix && this.unSubPixelFix();
            return this.alignedObjects = null;
          };
          c.prototype.createElement = function(b) {
            var c2 = new this.Element();
            c2.init(this, b);
            return c2;
          };
          c.prototype.getRadialAttr = function(b, c2) {
            return { cx: b[0] - b[2] / 2 + c2.cx * b[2], cy: b[1] - b[2] / 2 + c2.cy * b[2], r: c2.r * b[2] };
          };
          c.prototype.truncate = function(b, c2, e2, d2, g2, k2, h2) {
            var w = this, z = b.rotation, l2, r2 = d2 ? 1 : 0, m2 = (e2 || d2).length, P = m2, p2 = [], t2 = function(b2) {
              c2.firstChild && c2.removeChild(c2.firstChild);
              b2 && c2.appendChild(x.createTextNode(b2));
            }, B2 = function(z2, k3) {
              k3 = k3 || z2;
              if ("undefined" === typeof p2[k3]) if (c2.getSubStringLength) try {
                p2[k3] = g2 + c2.getSubStringLength(0, d2 ? k3 + 1 : k3);
              } catch (da) {
                "";
              }
              else w.getSpanWidth && (t2(h2(e2 || d2, z2)), p2[k3] = g2 + w.getSpanWidth(b, c2));
              return p2[k3];
            }, a2;
            b.rotation = 0;
            var A2 = B2(c2.textContent.length);
            if (a2 = g2 + A2 > k2) {
              for (; r2 <= m2; ) P = Math.ceil((r2 + m2) / 2), d2 && (l2 = h2(d2, P)), A2 = B2(P, l2 && l2.length - 1), r2 === m2 ? r2 = m2 + 1 : A2 > k2 ? m2 = P - 1 : r2 = P;
              0 === m2 ? t2("") : e2 && m2 === e2.length - 1 || t2(l2 || h2(e2 || d2, P));
            }
            d2 && d2.splice(0, P);
            b.actualWidth = A2;
            b.rotation = z;
            return a2;
          };
          c.prototype.buildText = function(b) {
            var c2 = b.element, e2 = this, d2 = e2.forExport, g2 = m(b.textStr, "").toString(), k2 = -1 !== g2.indexOf("<"), l2 = c2.childNodes, r2, p2 = C(c2, "x"), a2 = b.styles, A2 = b.textWidth, I2 = a2 && a2.lineHeight, Q = a2 && a2.textOutline, f2 = a2 && "ellipsis" === a2.textOverflow, F2 = a2 && "nowrap" === a2.whiteSpace, N2 = a2 && a2.fontSize, q2, E2 = l2.length;
            a2 = A2 && !b.added && this.box;
            var v2 = function(b2) {
              var d3;
              e2.styledMode || (d3 = /(px|em)$/.test(b2 && b2.style.fontSize) ? b2.style.fontSize : N2 || e2.style.fontSize || 12);
              return I2 ? h(I2) : e2.fontMetrics(
                d3,
                b2.getAttribute("style") ? b2 : c2
              ).h;
            }, R2 = function(b2, c3) {
              u(e2.escapes, function(e3, d3) {
                c3 && -1 !== c3.indexOf(e3) || (b2 = b2.toString().replace(new RegExp(e3, "g"), d3));
              });
              return b2;
            }, n3 = function(b2, c3) {
              var e3 = b2.indexOf("<");
              b2 = b2.substring(e3, b2.indexOf(">") - e3);
              e3 = b2.indexOf(c3 + "=");
              if (-1 !== e3 && (e3 = e3 + c3.length + 1, c3 = b2.charAt(e3), '"' === c3 || "'" === c3)) return b2 = b2.substring(e3 + 1), b2.substring(0, b2.indexOf(c3));
            }, K2 = /<br.*?>/g;
            var J2 = [g2, f2, F2, I2, Q, N2, A2].join();
            if (J2 !== b.textCache) {
              for (b.textCache = J2; E2--; ) c2.removeChild(l2[E2]);
              k2 || Q || f2 || A2 || -1 !== g2.indexOf(" ") && (!F2 || K2.test(g2)) ? (a2 && a2.appendChild(c2), k2 ? (g2 = e2.styledMode ? g2.replace(/<(b|strong)>/g, '<span class="highcharts-strong">').replace(/<(i|em)>/g, '<span class="highcharts-emphasized">') : g2.replace(/<(b|strong)>/g, '<span style="font-weight:bold">').replace(/<(i|em)>/g, '<span style="font-style:italic">'), g2 = g2.replace(/<a/g, "<span").replace(/<\/(b|strong|i|em|a)>/g, "</span>").split(K2)) : g2 = [g2], g2 = g2.filter(function(b2) {
                return "" !== b2;
              }), g2.forEach(function(g3, w) {
                var k3 = 0, z = 0;
                g3 = g3.replace(/^\s+|\s+$/g, "").replace(/<span/g, "|||<span").replace(
                  /<\/span>/g,
                  "</span>|||"
                );
                var h2 = g3.split("|||");
                h2.forEach(function(g4) {
                  if ("" !== g4 || 1 === h2.length) {
                    var l3 = {}, m2 = x.createElementNS(e2.SVG_NS, "tspan"), P, a3;
                    (P = n3(g4, "class")) && C(m2, "class", P);
                    if (P = n3(g4, "style")) P = P.replace(/(;| |^)color([ :])/, "$1fill$2"), C(m2, "style", P);
                    if ((a3 = n3(g4, "href")) && !d2 && -1 === a3.split(":")[0].toLowerCase().indexOf("javascript")) {
                      var t2 = x.createElementNS(e2.SVG_NS, "a");
                      C(t2, "href", a3);
                      C(m2, "class", "highcharts-anchor");
                      t2.appendChild(m2);
                      e2.styledMode || H(m2, { cursor: "pointer" });
                    }
                    g4 = R2(g4.replace(
                      /<[a-zA-Z\/](.|\n)*?>/g,
                      ""
                    ) || " ");
                    if (" " !== g4) {
                      m2.appendChild(x.createTextNode(g4));
                      k3 ? l3.dx = 0 : w && null !== p2 && (l3.x = p2);
                      C(m2, l3);
                      c2.appendChild(t2 || m2);
                      !k3 && q2 && (!B && d2 && H(m2, { display: "block" }), C(m2, "dy", v2(m2)));
                      if (A2) {
                        var Q2 = g4.replace(/([^\^])-/g, "$1- ").split(" ");
                        l3 = !F2 && (1 < h2.length || w || 1 < Q2.length);
                        t2 = 0;
                        a3 = v2(m2);
                        if (f2) r2 = e2.truncate(b, m2, g4, void 0, 0, Math.max(0, A2 - parseInt(N2 || 12, 10)), function(b2, c3) {
                          return b2.substring(0, c3) + "…";
                        });
                        else if (l3) for (; Q2.length; ) Q2.length && !F2 && 0 < t2 && (m2 = x.createElementNS(M, "tspan"), C(m2, { dy: a3, x: p2 }), P && C(m2, "style", P), m2.appendChild(x.createTextNode(Q2.join(" ").replace(
                          /- /g,
                          "-"
                        ))), c2.appendChild(m2)), e2.truncate(b, m2, null, Q2, 0 === t2 ? z : 0, A2, function(b2, c3) {
                          return Q2.slice(0, c3).join(" ").replace(/- /g, "-");
                        }), z = b.actualWidth, t2++;
                      }
                      k3++;
                    }
                  }
                });
                q2 = q2 || c2.childNodes.length;
              }), f2 && r2 && b.attr("title", R2(b.textStr || "", ["&lt;", "&gt;"])), a2 && a2.removeChild(c2), t(Q) && b.applyTextOutline && b.applyTextOutline(Q)) : c2.appendChild(x.createTextNode(R2(g2)));
            }
          };
          c.prototype.getContrast = function(b) {
            b = f.parse(b).rgba;
            b[0] *= 1;
            b[1] *= 1.2;
            b[2] *= 0.5;
            return 459 < b[0] + b[1] + b[2] ? "#000000" : "#FFFFFF";
          };
          c.prototype.button = function(b, c2, e2, d2, g2, k2, h2, l2, r2, m2) {
            var w = this.label(b, c2, e2, r2, void 0, void 0, m2, void 0, "button"), z = 0, x2 = this.styledMode;
            b = (g2 = g2 ? I(g2) : g2) && g2.style || {};
            g2 && g2.style && delete g2.style;
            w.attr(I({ padding: 8, r: 2 }, g2));
            if (!x2) {
              g2 = I({ fill: "#f7f7f7", stroke: "#cccccc", "stroke-width": 1, style: { color: "#333333", cursor: "pointer", fontWeight: "normal" } }, { style: b }, g2);
              var P = g2.style;
              delete g2.style;
              k2 = I(g2, { fill: "#e6e6e6" }, k2);
              var t2 = k2.style;
              delete k2.style;
              h2 = I(g2, { fill: "#e6ebf5", style: { color: "#000000", fontWeight: "bold" } }, h2);
              var a2 = h2.style;
              delete h2.style;
              l2 = I(g2, { style: { color: "#cccccc" } }, l2);
              var p2 = l2.style;
              delete l2.style;
            }
            G(w.element, A ? "mouseover" : "mouseenter", function() {
              3 !== z && w.setState(1);
            });
            G(w.element, A ? "mouseout" : "mouseleave", function() {
              3 !== z && w.setState(z);
            });
            w.setState = function(b2) {
              1 !== b2 && (w.state = z = b2);
              w.removeClass(/highcharts-button-(normal|hover|pressed|disabled)/).addClass("highcharts-button-" + ["normal", "hover", "pressed", "disabled"][b2 || 0]);
              x2 || w.attr([g2, k2, h2, l2][b2 || 0]).css([P, t2, a2, p2][b2 || 0]);
            };
            x2 || w.attr(g2).css(q({ cursor: "default" }, P));
            return w.on(
              "click",
              function(b2) {
                3 !== z && d2.call(w, b2);
              }
            );
          };
          c.prototype.crispLine = function(b, c2, e2) {
            void 0 === e2 && (e2 = "round");
            var d2 = b[0], g2 = b[1];
            d2[1] === g2[1] && (d2[1] = g2[1] = Math[e2](d2[1]) - c2 % 2 / 2);
            d2[2] === g2[2] && (d2[2] = g2[2] = Math[e2](d2[2]) + c2 % 2 / 2);
            return b;
          };
          c.prototype.path = function(b) {
            var c2 = this.styledMode ? {} : { fill: "none" };
            K(b) ? c2.d = b : p(b) && q(c2, b);
            return this.createElement("path").attr(c2);
          };
          c.prototype.circle = function(b, c2, e2) {
            b = p(b) ? b : "undefined" === typeof b ? {} : { x: b, y: c2, r: e2 };
            c2 = this.createElement("circle");
            c2.xSetter = c2.ySetter = function(b2, c3, e3) {
              e3.setAttribute("c" + c3, b2);
            };
            return c2.attr(b);
          };
          c.prototype.arc = function(b, c2, e2, d2, g2, k2) {
            p(b) ? (d2 = b, c2 = d2.y, e2 = d2.r, b = d2.x) : d2 = { innerR: d2, start: g2, end: k2 };
            b = this.symbol("arc", b, c2, e2, e2, d2);
            b.r = e2;
            return b;
          };
          c.prototype.rect = function(b, c2, e2, d2, g2, k2) {
            g2 = p(b) ? b.r : g2;
            var w = this.createElement("rect");
            b = p(b) ? b : "undefined" === typeof b ? {} : { x: b, y: c2, width: Math.max(e2, 0), height: Math.max(d2, 0) };
            this.styledMode || ("undefined" !== typeof k2 && (b.strokeWidth = k2, b = w.crisp(b)), b.fill = "none");
            g2 && (b.r = g2);
            w.rSetter = function(b2, c3, e3) {
              w.r = b2;
              C(e3, { rx: b2, ry: b2 });
            };
            w.rGetter = function() {
              return w.r;
            };
            return w.attr(b);
          };
          c.prototype.setSize = function(b, c2, e2) {
            var d2 = this.alignedObjects, g2 = d2.length;
            this.width = b;
            this.height = c2;
            for (this.boxWrapper.animate({ width: b, height: c2 }, { step: function() {
              this.attr({ viewBox: "0 0 " + this.attr("width") + " " + this.attr("height") });
            }, duration: m(e2, true) ? void 0 : 0 }); g2--; ) d2[g2].align();
          };
          c.prototype.g = function(b) {
            var c2 = this.createElement("g");
            return b ? c2.attr({ "class": "highcharts-" + b }) : c2;
          };
          c.prototype.image = function(b, c2, e2, d2, g2, k2) {
            var w = { preserveAspectRatio: "none" }, h2 = function(b2, c3) {
              b2.setAttributeNS ? b2.setAttributeNS("http://www.w3.org/1999/xlink", "href", c3) : b2.setAttribute("hc-svg-href", c3);
            }, z = function(c3) {
              h2(l2.element, b);
              k2.call(l2, c3);
            };
            1 < arguments.length && q(w, { x: c2, y: e2, width: d2, height: g2 });
            var l2 = this.createElement("image").attr(w);
            k2 ? (h2(l2.element, "data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="), w = new F.Image(), G(w, "load", z), w.src = b, w.complete && z({})) : h2(l2.element, b);
            return l2;
          };
          c.prototype.symbol = function(b, c2, e2, d2, k2, h2) {
            var w = this, z = /^url\((.*?)\)$/, l2 = z.test(b), r2 = !l2 && (this.symbols[b] ? b : "circle"), P = r2 && this.symbols[r2], t2;
            if (P) {
              "number" === typeof c2 && (t2 = P.call(this.symbols, Math.round(c2 || 0), Math.round(e2 || 0), d2 || 0, k2 || 0, h2));
              var a2 = this.path(t2);
              w.styledMode || a2.attr("fill", "none");
              q(a2, { symbolName: r2, x: c2, y: e2, width: d2, height: k2 });
              h2 && q(a2, h2);
            } else if (l2) {
              var p2 = b.match(z)[1];
              a2 = this.image(p2);
              a2.imgwidth = m(R[p2] && R[p2].width, h2 && h2.width);
              a2.imgheight = m(R[p2] && R[p2].height, h2 && h2.height);
              var B2 = function() {
                a2.attr({ width: a2.width, height: a2.height });
              };
              ["width", "height"].forEach(function(b2) {
                a2[b2 + "Setter"] = function(b3, c3) {
                  var e3 = {}, d3 = this["img" + c3], g2 = "width" === c3 ? "translateX" : "translateY";
                  this[c3] = b3;
                  v(d3) && (h2 && "within" === h2.backgroundSize && this.width && this.height && (d3 = Math.round(d3 * Math.min(this.width / this.imgwidth, this.height / this.imgheight))), this.element && this.element.setAttribute(c3, d3), this.alignByTranslate || (e3[g2] = ((this[c3] || 0) - d3) / 2, this.attr(e3)));
                };
              });
              v(c2) && a2.attr({ x: c2, y: e2 });
              a2.isImg = true;
              v(a2.imgwidth) && v(a2.imgheight) ? B2() : (a2.attr({ width: 0, height: 0 }), J("img", { onload: function() {
                var b2 = g[w.chartIndex];
                0 === this.width && (H(this, {
                  position: "absolute",
                  top: "-999em"
                }), x.body.appendChild(this));
                R[p2] = { width: this.width, height: this.height };
                a2.imgwidth = this.width;
                a2.imgheight = this.height;
                a2.element && B2();
                this.parentNode && this.parentNode.removeChild(this);
                w.imgCount--;
                if (!w.imgCount && b2 && !b2.hasLoaded) b2.onload();
              }, src: p2 }), this.imgCount++);
            }
            return a2;
          };
          c.prototype.clipRect = function(b, c2, e2, d2) {
            var g2 = k() + "-", w = this.createElement("clipPath").attr({ id: g2 }).add(this.defs);
            b = this.rect(b, c2, e2, d2, 0).add(w);
            b.id = g2;
            b.clipPath = w;
            b.count = 0;
            return b;
          };
          c.prototype.text = function(b, c2, e2, d2) {
            var g2 = {};
            if (d2 && (this.allowHTML || !this.forExport)) return this.html(b, c2, e2);
            g2.x = Math.round(c2 || 0);
            e2 && (g2.y = Math.round(e2));
            v(b) && (g2.text = b);
            b = this.createElement("text").attr(g2);
            d2 || (b.xSetter = function(b2, c3, e3) {
              var d3 = e3.getElementsByTagName("tspan"), g3 = e3.getAttribute(c3), k2;
              for (k2 = 0; k2 < d3.length; k2++) {
                var w = d3[k2];
                w.getAttribute(c3) === g3 && w.setAttribute(c3, b2);
              }
              e3.setAttribute(c3, b2);
            });
            return b;
          };
          c.prototype.fontMetrics = function(b, c2) {
            b = !this.styledMode && /px/.test(b) || !F.getComputedStyle ? b || c2 && c2.style && c2.style.fontSize || this.style && this.style.fontSize : c2 && n2.prototype.getStyle.call(c2, "font-size");
            b = /px/.test(b) ? h(b) : 12;
            c2 = 24 > b ? b + 3 : Math.round(1.2 * b);
            return { h: c2, b: Math.round(0.8 * c2), f: b };
          };
          c.prototype.rotCorr = function(b, c2, e2) {
            var g2 = b;
            c2 && e2 && (g2 = Math.max(g2 * Math.cos(c2 * d), 4));
            return { x: -b / 3 * Math.sin(c2 * d), y: g2 };
          };
          c.prototype.pathToSegments = function(b) {
            for (var c2 = [], e2 = [], d2 = { A: 8, C: 7, H: 2, L: 3, M: 3, Q: 5, S: 5, T: 3, V: 2 }, g2 = 0; g2 < b.length; g2++) t(e2[0]) && E(b[g2]) && e2.length === d2[e2[0].toUpperCase()] && b.splice(g2, 0, e2[0].replace("M", "L").replace("m", "l")), "string" === typeof b[g2] && (e2.length && c2.push(e2.slice(0)), e2.length = 0), e2.push(b[g2]);
            c2.push(e2.slice(0));
            return c2;
          };
          c.prototype.label = function(b, c2, e2, d2, g2, k2, h2, l2, r2) {
            return new y(this, b, c2, e2, d2, g2, k2, h2, l2, r2);
          };
          return c;
        })();
        e.prototype.Element = n2;
        e.prototype.SVG_NS = M;
        e.prototype.draw = D;
        e.prototype.escapes = { "&": "&amp;", "<": "&lt;", ">": "&gt;", "'": "&#39;", '"': "&quot;" };
        e.prototype.symbols = { circle: function(c, b, e2, d2) {
          return this.arc(c + e2 / 2, b + d2 / 2, e2 / 2, d2 / 2, { start: 0.5 * Math.PI, end: 2.5 * Math.PI, open: false });
        }, square: function(c, b, e2, d2) {
          return [[
            "M",
            c,
            b
          ], ["L", c + e2, b], ["L", c + e2, b + d2], ["L", c, b + d2], ["Z"]];
        }, triangle: function(c, b, e2, d2) {
          return [["M", c + e2 / 2, b], ["L", c + e2, b + d2], ["L", c, b + d2], ["Z"]];
        }, "triangle-down": function(c, b, e2, d2) {
          return [["M", c, b], ["L", c + e2, b], ["L", c + e2 / 2, b + d2], ["Z"]];
        }, diamond: function(c, b, e2, d2) {
          return [["M", c + e2 / 2, b], ["L", c + e2, b + d2 / 2], ["L", c + e2 / 2, b + d2], ["L", c, b + d2 / 2], ["Z"]];
        }, arc: function(c, b, e2, d2, g2) {
          var k2 = [];
          if (g2) {
            var h2 = g2.start || 0, w = g2.end || 0, z = g2.r || e2;
            e2 = g2.r || d2 || e2;
            var l2 = 1e-3 > Math.abs(w - h2 - 2 * Math.PI);
            w -= 1e-3;
            d2 = g2.innerR;
            l2 = m(g2.open, l2);
            var r2 = Math.cos(h2), x2 = Math.sin(h2), a2 = Math.cos(w), P = Math.sin(w);
            h2 = m(g2.longArc, 1e-3 > w - h2 - Math.PI ? 0 : 1);
            k2.push(["M", c + z * r2, b + e2 * x2], ["A", z, e2, 0, h2, m(g2.clockwise, 1), c + z * a2, b + e2 * P]);
            v(d2) && k2.push(l2 ? ["M", c + d2 * a2, b + d2 * P] : ["L", c + d2 * a2, b + d2 * P], ["A", d2, d2, 0, h2, v(g2.clockwise) ? 1 - g2.clockwise : 0, c + d2 * r2, b + d2 * x2]);
            l2 || k2.push(["Z"]);
          }
          return k2;
        }, callout: function(c, b, e2, d2, g2) {
          var k2 = Math.min(g2 && g2.r || 0, e2, d2), h2 = k2 + 6, w = g2 && g2.anchorX || 0;
          g2 = g2 && g2.anchorY || 0;
          var l2 = [["M", c + k2, b], ["L", c + e2 - k2, b], ["C", c + e2, b, c + e2, b, c + e2, b + k2], ["L", c + e2, b + d2 - k2], ["C", c + e2, b + d2, c + e2, b + d2, c + e2 - k2, b + d2], ["L", c + k2, b + d2], [
            "C",
            c,
            b + d2,
            c,
            b + d2,
            c,
            b + d2 - k2
          ], ["L", c, b + k2], ["C", c, b, c, b, c + k2, b]];
          w && w > e2 ? g2 > b + h2 && g2 < b + d2 - h2 ? l2.splice(3, 1, ["L", c + e2, g2 - 6], ["L", c + e2 + 6, g2], ["L", c + e2, g2 + 6], ["L", c + e2, b + d2 - k2]) : l2.splice(3, 1, ["L", c + e2, d2 / 2], ["L", w, g2], ["L", c + e2, d2 / 2], ["L", c + e2, b + d2 - k2]) : w && 0 > w ? g2 > b + h2 && g2 < b + d2 - h2 ? l2.splice(7, 1, ["L", c, g2 + 6], ["L", c - 6, g2], ["L", c, g2 - 6], ["L", c, b + k2]) : l2.splice(7, 1, ["L", c, d2 / 2], ["L", w, g2], ["L", c, d2 / 2], ["L", c, b + k2]) : g2 && g2 > d2 && w > c + h2 && w < c + e2 - h2 ? l2.splice(5, 1, ["L", w + 6, b + d2], ["L", w, b + d2 + 6], ["L", w - 6, b + d2], ["L", c + k2, b + d2]) : g2 && 0 > g2 && w > c + h2 && w < c + e2 - h2 && l2.splice(1, 1, ["L", w - 6, b], [
            "L",
            w,
            b - 6
          ], ["L", w + 6, b], ["L", e2 - k2, b]);
          return l2;
        } };
        a.SVGRenderer = e;
        a.Renderer = a.SVGRenderer;
        return a.Renderer;
      });
      O(n, "Core/Renderer/HTML/HTML.js", [n["Core/Globals.js"], n["Core/Renderer/SVG/SVGElement.js"], n["Core/Renderer/SVG/SVGRenderer.js"], n["Core/Utilities.js"]], function(f, a, n2, y) {
        var D = y.attr, G = y.createElement, C = y.css, J = y.defined, H = y.extend, v = y.pick, L = y.pInt, q = f.isFirefox, K = f.isMS, E = f.isWebKit, p = f.win;
        H(a.prototype, { htmlCss: function(a2) {
          var p2 = "SPAN" === this.element.tagName && a2 && "width" in a2, t = v(
            p2 && a2.width,
            void 0
          );
          if (p2) {
            delete a2.width;
            this.textWidth = t;
            var m = true;
          }
          a2 && "ellipsis" === a2.textOverflow && (a2.whiteSpace = "nowrap", a2.overflow = "hidden");
          this.styles = H(this.styles, a2);
          C(this.element, a2);
          m && this.htmlUpdateTransform();
          return this;
        }, htmlGetBBox: function() {
          var a2 = this.element;
          return { x: a2.offsetLeft, y: a2.offsetTop, width: a2.offsetWidth, height: a2.offsetHeight };
        }, htmlUpdateTransform: function() {
          if (this.added) {
            var a2 = this.renderer, p2 = this.element, u = this.translateX || 0, m = this.translateY || 0, h = this.x || 0, l = this.y || 0, k = this.textAlign || "left", g = { left: 0, center: 0.5, right: 1 }[k], d = this.styles, x = d && d.whiteSpace;
            C(p2, { marginLeft: u, marginTop: m });
            !a2.styledMode && this.shadows && this.shadows.forEach(function(d2) {
              C(d2, { marginLeft: u + 1, marginTop: m + 1 });
            });
            this.inverted && [].forEach.call(p2.childNodes, function(d2) {
              a2.invertChild(d2, p2);
            });
            if ("SPAN" === p2.tagName) {
              d = this.rotation;
              var r = this.textWidth && L(this.textWidth), A = [d, k, p2.innerHTML, this.textWidth, this.textAlign].join(), f2;
              (f2 = r !== this.oldTextWidth) && !(f2 = r > this.oldTextWidth) && ((f2 = this.textPxLength) || (C(p2, {
                width: "",
                whiteSpace: x || "nowrap"
              }), f2 = p2.offsetWidth), f2 = f2 > r);
              f2 && (/[ \-]/.test(p2.textContent || p2.innerText) || "ellipsis" === p2.style.textOverflow) ? (C(p2, { width: r + "px", display: "block", whiteSpace: x || "normal" }), this.oldTextWidth = r, this.hasBoxWidthChanged = true) : this.hasBoxWidthChanged = false;
              A !== this.cTT && (x = a2.fontMetrics(p2.style.fontSize, p2).b, !J(d) || d === (this.oldRotation || 0) && k === this.oldAlign || this.setSpanRotation(d, g, x), this.getSpanCorrection(!J(d) && this.textPxLength || p2.offsetWidth, x, g, d, k));
              C(p2, {
                left: h + (this.xCorr || 0) + "px",
                top: l + (this.yCorr || 0) + "px"
              });
              this.cTT = A;
              this.oldRotation = d;
              this.oldAlign = k;
            }
          } else this.alignOnAdd = true;
        }, setSpanRotation: function(a2, p2, u) {
          var m = {}, h = this.renderer.getTransformKey();
          m[h] = m.transform = "rotate(" + a2 + "deg)";
          m[h + (q ? "Origin" : "-origin")] = m.transformOrigin = 100 * p2 + "% " + u + "px";
          C(this.element, m);
        }, getSpanCorrection: function(a2, p2, u) {
          this.xCorr = -a2 * u;
          this.yCorr = -p2;
        } });
        H(n2.prototype, { getTransformKey: function() {
          return K && !/Edge/.test(p.navigator.userAgent) ? "-ms-transform" : E ? "-webkit-transform" : q ? "MozTransform" : p.opera ? "-o-transform" : "";
        }, html: function(p2, f2, u) {
          var m = this.createElement("span"), h = m.element, l = m.renderer, k = l.isSVG, g = function(d, g2) {
            ["opacity", "visibility"].forEach(function(k2) {
              d[k2 + "Setter"] = function(h2, l2, r) {
                var m2 = d.div ? d.div.style : g2;
                a.prototype[k2 + "Setter"].call(this, h2, l2, r);
                m2 && (m2[l2] = h2);
              };
            });
            d.addedSetters = true;
          };
          m.textSetter = function(d) {
            d !== h.innerHTML && (delete this.bBox, delete this.oldTextWidth);
            this.textStr = d;
            h.innerHTML = v(d, "");
            m.doTransform = true;
          };
          k && g(m, m.element.style);
          m.xSetter = m.ySetter = m.alignSetter = m.rotationSetter = function(d, g2) {
            "align" === g2 ? m.alignValue = m.textAlign = d : m[g2] = d;
            m.doTransform = true;
          };
          m.afterSetters = function() {
            this.doTransform && (this.htmlUpdateTransform(), this.doTransform = false);
          };
          m.attr({ text: p2, x: Math.round(f2), y: Math.round(u) }).css({ position: "absolute" });
          l.styledMode || m.css({ fontFamily: this.style.fontFamily, fontSize: this.style.fontSize });
          h.style.whiteSpace = "nowrap";
          m.css = m.htmlCss;
          k && (m.add = function(d) {
            var k2 = l.box.parentNode, r = [];
            if (this.parentGroup = d) {
              var a2 = d.div;
              if (!a2) {
                for (; d; ) r.push(d), d = d.parentGroup;
                r.reverse().forEach(function(d2) {
                  function h2(g2, e) {
                    d2[e] = g2;
                    "translateX" === e ? x.left = g2 + "px" : x.top = g2 + "px";
                    d2.doTransform = true;
                  }
                  var l2 = D(d2.element, "class");
                  a2 = d2.div = d2.div || G("div", l2 ? { className: l2 } : void 0, { position: "absolute", left: (d2.translateX || 0) + "px", top: (d2.translateY || 0) + "px", display: d2.display, opacity: d2.opacity, pointerEvents: d2.styles && d2.styles.pointerEvents }, a2 || k2);
                  var x = a2.style;
                  H(d2, { classSetter: /* @__PURE__ */ (function(d3) {
                    return function(e) {
                      this.element.setAttribute("class", e);
                      d3.className = e;
                    };
                  })(a2), on: function() {
                    r[0].div && m.on.apply({ element: r[0].div }, arguments);
                    return d2;
                  }, translateXSetter: h2, translateYSetter: h2 });
                  d2.addedSetters || g(d2);
                });
              }
            } else a2 = k2;
            a2.appendChild(h);
            m.added = true;
            m.alignOnAdd && m.htmlUpdateTransform();
            return m;
          });
          return m;
        } });
      });
      O(n, "Core/Axis/Tick.js", [n["Core/Globals.js"], n["Core/Utilities.js"]], function(f, a) {
        var n2 = a.clamp, y = a.correctFloat, D = a.defined, G = a.destroyObjectProperties, C = a.extend, J = a.fireEvent, H = a.isNumber, v = a.merge, L = a.objectEach, q = a.pick, K = f.deg2rad;
        a = (function() {
          function a2(a3, t, f2, u, m) {
            this.isNewLabel = this.isNew = true;
            this.axis = a3;
            this.pos = t;
            this.type = f2 || "";
            this.parameters = m || {};
            this.tickmarkOffset = this.parameters.tickmarkOffset;
            this.options = this.parameters.options;
            J(this, "init");
            f2 || u || this.addLabel();
          }
          a2.prototype.addLabel = function() {
            var a3 = this, t = a3.axis, f2 = t.options, u = t.chart, m = t.categories, h = t.logarithmic, l = t.names, k = a3.pos, g = q(a3.options && a3.options.labels, f2.labels), d = t.tickPositions, x = k === d[0], r = k === d[d.length - 1];
            l = this.parameters.category || (m ? q(m[k], l[k], k) : k);
            var A = a3.label;
            m = (!g.step || 1 === g.step) && 1 === t.tickInterval;
            d = d.info;
            var N, B;
            if (t.dateTime && d) {
              var M = u.time.resolveDTLFormat(f2.dateTimeLabelFormats[!f2.grid && d.higherRanks[k] || d.unitName]);
              var v2 = M.main;
            }
            a3.isFirst = x;
            a3.isLast = r;
            a3.formatCtx = { axis: t, chart: u, isFirst: x, isLast: r, dateTimeLabelFormat: v2, tickPositionInfo: d, value: h ? y(h.lin2log(l)) : l, pos: k };
            f2 = t.labelFormatter.call(a3.formatCtx, this.formatCtx);
            if (B = M && M.list) a3.shortenLabel = function() {
              for (N = 0; N < B.length; N++) if (A.attr({ text: t.labelFormatter.call(C(a3.formatCtx, { dateTimeLabelFormat: B[N] })) }), A.getBBox().width < t.getSlotWidth(a3) - 2 * q(g.padding, 5)) return;
              A.attr({ text: "" });
            };
            m && t._addedPlotLB && a3.moveLabel(f2, g);
            D(A) || a3.movedLabel ? A && A.textStr !== f2 && !m && (!A.textWidth || g.style && g.style.width || A.styles.width || A.css({ width: null }), A.attr({ text: f2 }), A.textPxLength = A.getBBox().width) : (a3.label = A = a3.createLabel({ x: 0, y: 0 }, f2, g), a3.rotation = 0);
          };
          a2.prototype.createLabel = function(a3, t, f2) {
            var p = this.axis, m = p.chart;
            if (a3 = D(t) && f2.enabled ? m.renderer.text(t, a3.x, a3.y, f2.useHTML).add(p.labelGroup) : null) m.styledMode || a3.css(v(f2.style)), a3.textPxLength = a3.getBBox().width;
            return a3;
          };
          a2.prototype.destroy = function() {
            G(this, this.axis);
          };
          a2.prototype.getPosition = function(a3, t, f2, u) {
            var m = this.axis, h = m.chart, l = u && h.oldChartHeight || h.chartHeight;
            a3 = { x: a3 ? y(m.translate(t + f2, null, null, u) + m.transB) : m.left + m.offset + (m.opposite ? (u && h.oldChartWidth || h.chartWidth) - m.right - m.left : 0), y: a3 ? l - m.bottom + m.offset - (m.opposite ? m.height : 0) : y(l - m.translate(t + f2, null, null, u) - m.transB) };
            a3.y = n2(a3.y, -1e5, 1e5);
            J(this, "afterGetPosition", { pos: a3 });
            return a3;
          };
          a2.prototype.getLabelPosition = function(a3, t, f2, u, m, h, l, k) {
            var g = this.axis, d = g.transA, x = g.isLinked && g.linkedParent ? g.linkedParent.reversed : g.reversed, r = g.staggerLines, p = g.tickRotCorr || { x: 0, y: 0 }, I = m.y, B = u || g.reserveSpaceDefault ? 0 : -g.labelOffset * ("center" === g.labelAlign ? 0.5 : 1), M = {};
            D(I) || (I = 0 === g.side ? f2.rotation ? -8 : -f2.getBBox().height : 2 === g.side ? p.y + 8 : Math.cos(f2.rotation * K) * (p.y - f2.getBBox(false, 0).height / 2));
            a3 = a3 + m.x + B + p.x - (h && u ? h * d * (x ? -1 : 1) : 0);
            t = t + I - (h && !u ? h * d * (x ? 1 : -1) : 0);
            r && (f2 = l / (k || 1) % r, g.opposite && (f2 = r - f2 - 1), t += g.labelOffset / r * f2);
            M.x = a3;
            M.y = Math.round(t);
            J(this, "afterGetLabelPosition", { pos: M, tickmarkOffset: h, index: l });
            return M;
          };
          a2.prototype.getLabelSize = function() {
            return this.label ? this.label.getBBox()[this.axis.horiz ? "height" : "width"] : 0;
          };
          a2.prototype.getMarkPath = function(a3, t, f2, u, m, h) {
            return h.crispLine([["M", a3, t], ["L", a3 + (m ? 0 : -f2), t + (m ? f2 : 0)]], u);
          };
          a2.prototype.handleOverflow = function(a3) {
            var p = this.axis, f2 = p.options.labels, u = a3.x, m = p.chart.chartWidth, h = p.chart.spacing, l = q(p.labelLeft, Math.min(p.pos, h[3]));
            h = q(p.labelRight, Math.max(p.isRadial ? 0 : p.pos + p.len, m - h[1]));
            var k = this.label, g = this.rotation, d = { left: 0, center: 0.5, right: 1 }[p.labelAlign || k.attr("align")], x = k.getBBox().width, r = p.getSlotWidth(this), A = r, N = 1, B, M = {};
            if (g || "justify" !== q(f2.overflow, "justify")) 0 > g && u - d * x < l ? B = Math.round(u / Math.cos(g * K) - l) : 0 < g && u + d * x > h && (B = Math.round((m - u) / Math.cos(g * K)));
            else if (m = u + (1 - d) * x, u - d * x < l ? A = a3.x + A * (1 - d) - l : m > h && (A = h - a3.x + A * d, N = -1), A = Math.min(r, A), A < r && "center" === p.labelAlign && (a3.x += N * (r - A - d * (r - Math.min(x, A)))), x > A || p.autoRotation && (k.styles || {}).width) B = A;
            B && (this.shortenLabel ? this.shortenLabel() : (M.width = Math.floor(B) + "px", (f2.style || {}).textOverflow || (M.textOverflow = "ellipsis"), k.css(M)));
          };
          a2.prototype.moveLabel = function(a3, t) {
            var p = this, f2 = p.label, m = false, h = p.axis, l = h.reversed;
            f2 && f2.textStr === a3 ? (p.movedLabel = f2, m = true, delete p.label) : L(h.ticks, function(g) {
              m || g.isNew || g === p || !g.label || g.label.textStr !== a3 || (p.movedLabel = g.label, m = true, g.labelPos = p.movedLabel.xy, delete g.label);
            });
            if (!m && (p.labelPos || f2)) {
              var k = p.labelPos || f2.xy;
              f2 = h.horiz ? l ? 0 : h.width + h.left : k.x;
              h = h.horiz ? k.y : l ? h.width + h.left : 0;
              p.movedLabel = p.createLabel({ x: f2, y: h }, a3, t);
              p.movedLabel && p.movedLabel.attr({ opacity: 0 });
            }
          };
          a2.prototype.render = function(a3, t, f2) {
            var p = this.axis, m = p.horiz, h = this.pos, l = q(this.tickmarkOffset, p.tickmarkOffset);
            h = this.getPosition(m, h, l, t);
            l = h.x;
            var k = h.y;
            p = m && l === p.pos + p.len || !m && k === p.pos ? -1 : 1;
            f2 = q(f2, 1);
            this.isActive = true;
            this.renderGridLine(t, f2, p);
            this.renderMark(h, f2, p);
            this.renderLabel(h, t, f2, a3);
            this.isNew = false;
            J(this, "afterRender");
          };
          a2.prototype.renderGridLine = function(a3, t, f2) {
            var p = this.axis, m = p.options, h = this.gridLine, l = {}, k = this.pos, g = this.type, d = q(this.tickmarkOffset, p.tickmarkOffset), x = p.chart.renderer, r = g ? g + "Grid" : "grid", A = m[r + "LineWidth"], N = m[r + "LineColor"];
            m = m[r + "LineDashStyle"];
            h || (p.chart.styledMode || (l.stroke = N, l["stroke-width"] = A, m && (l.dashstyle = m)), g || (l.zIndex = 1), a3 && (t = 0), this.gridLine = h = x.path().attr(l).addClass("highcharts-" + (g ? g + "-" : "") + "grid-line").add(p.gridGroup));
            if (h && (f2 = p.getPlotLinePath({ value: k + d, lineWidth: h.strokeWidth() * f2, force: "pass", old: a3 }))) h[a3 || this.isNew ? "attr" : "animate"]({ d: f2, opacity: t });
          };
          a2.prototype.renderMark = function(a3, t, f2) {
            var p = this.axis, m = p.options, h = p.chart.renderer, l = this.type, k = l ? l + "Tick" : "tick", g = p.tickSize(k), d = this.mark, x = !d, r = a3.x;
            a3 = a3.y;
            var A = q(m[k + "Width"], !l && p.isXAxis ? 1 : 0);
            m = m[k + "Color"];
            g && (p.opposite && (g[0] = -g[0]), x && (this.mark = d = h.path().addClass("highcharts-" + (l ? l + "-" : "") + "tick").add(p.axisGroup), p.chart.styledMode || d.attr({ stroke: m, "stroke-width": A })), d[x ? "attr" : "animate"]({ d: this.getMarkPath(r, a3, g[0], d.strokeWidth() * f2, p.horiz, h), opacity: t }));
          };
          a2.prototype.renderLabel = function(a3, f2, I, u) {
            var m = this.axis, h = m.horiz, l = m.options, k = this.label, g = l.labels, d = g.step;
            m = q(this.tickmarkOffset, m.tickmarkOffset);
            var x = true, r = a3.x;
            a3 = a3.y;
            k && H(r) && (k.xy = a3 = this.getLabelPosition(r, a3, k, h, g, m, u, d), this.isFirst && !this.isLast && !q(l.showFirstLabel, 1) || this.isLast && !this.isFirst && !q(l.showLastLabel, 1) ? x = false : !h || g.step || g.rotation || f2 || 0 === I || this.handleOverflow(a3), d && u % d && (x = false), x && H(a3.y) ? (a3.opacity = I, k[this.isNewLabel ? "attr" : "animate"](a3), this.isNewLabel = false) : (k.attr("y", -9999), this.isNewLabel = true));
          };
          a2.prototype.replaceMovedLabel = function() {
            var a3 = this.label, f2 = this.axis, q2 = f2.reversed;
            if (a3 && !this.isNew) {
              var u = f2.horiz ? q2 ? f2.left : f2.width + f2.left : a3.xy.x;
              q2 = f2.horiz ? a3.xy.y : q2 ? f2.width + f2.top : f2.top;
              a3.animate({ x: u, y: q2, opacity: 0 }, void 0, a3.destroy);
              delete this.label;
            }
            f2.isDirty = true;
            this.label = this.movedLabel;
            delete this.movedLabel;
          };
          return a2;
        })();
        f.Tick = a;
        return f.Tick;
      });
      O(n, "Core/Time.js", [n["Core/Globals.js"], n["Core/Utilities.js"]], function(f, a) {
        var n2 = a.defined, y = a.error, D = a.extend, G = a.isObject, C = a.merge, J = a.objectEach, H = a.pad, v = a.pick, L = a.splat, q = a.timeUnits, K = f.win;
        a = (function() {
          function a2(a3) {
            this.options = {};
            this.variableTimezone = this.useUTC = false;
            this.Date = K.Date;
            this.getTimezoneOffset = this.timezoneOffsetFunction();
            this.update(a3);
          }
          a2.prototype.get = function(a3, f2) {
            if (this.variableTimezone || this.timezoneOffset) {
              var p = f2.getTime(), t = p - this.getTimezoneOffset(f2);
              f2.setTime(t);
              a3 = f2["getUTC" + a3]();
              f2.setTime(p);
              return a3;
            }
            return this.useUTC ? f2["getUTC" + a3]() : f2["get" + a3]();
          };
          a2.prototype.set = function(a3, f2, q2) {
            if (this.variableTimezone || this.timezoneOffset) {
              if ("Milliseconds" === a3 || "Seconds" === a3 || "Minutes" === a3) return f2["setUTC" + a3](q2);
              var p = this.getTimezoneOffset(f2);
              p = f2.getTime() - p;
              f2.setTime(p);
              f2["setUTC" + a3](q2);
              a3 = this.getTimezoneOffset(f2);
              p = f2.getTime() + a3;
              return f2.setTime(p);
            }
            return this.useUTC ? f2["setUTC" + a3](q2) : f2["set" + a3](q2);
          };
          a2.prototype.update = function(a3) {
            var f2 = v(a3 && a3.useUTC, true);
            this.options = a3 = C(true, this.options || {}, a3);
            this.Date = a3.Date || K.Date || Date;
            this.timezoneOffset = (this.useUTC = f2) && a3.timezoneOffset;
            this.getTimezoneOffset = this.timezoneOffsetFunction();
            this.variableTimezone = !(f2 && !a3.getTimezoneOffset && !a3.timezone);
          };
          a2.prototype.makeTime = function(a3, t, q2, u, m, h) {
            if (this.useUTC) {
              var l = this.Date.UTC.apply(0, arguments);
              var k = this.getTimezoneOffset(l);
              l += k;
              var g = this.getTimezoneOffset(l);
              k !== g ? l += g - k : k - 36e5 !== this.getTimezoneOffset(l - 36e5) || f.isSafari || (l -= 36e5);
            } else l = new this.Date(a3, t, v(q2, 1), v(u, 0), v(m, 0), v(h, 0)).getTime();
            return l;
          };
          a2.prototype.timezoneOffsetFunction = function() {
            var a3 = this, f2 = this.options, q2 = f2.moment || K.moment;
            if (!this.useUTC) return function(a4) {
              return 6e4 * new Date(a4.toString()).getTimezoneOffset();
            };
            if (f2.timezone) {
              if (q2) return function(a4) {
                return 6e4 * -q2.tz(a4, f2.timezone).utcOffset();
              };
              y(25);
            }
            return this.useUTC && f2.getTimezoneOffset ? function(a4) {
              return 6e4 * f2.getTimezoneOffset(a4.valueOf());
            } : function() {
              return 6e4 * (a3.timezoneOffset || 0);
            };
          };
          a2.prototype.dateFormat = function(a3, t, q2) {
            var p;
            if (!n2(t) || isNaN(t)) return (null === (p = f.defaultOptions.lang) || void 0 === p ? void 0 : p.invalidDate) || "";
            a3 = v(a3, "%Y-%m-%d %H:%M:%S");
            var m = this;
            p = new this.Date(t);
            var h = this.get("Hours", p), l = this.get("Day", p), k = this.get("Date", p), g = this.get("Month", p), d = this.get("FullYear", p), x = f.defaultOptions.lang, r = null === x || void 0 === x ? void 0 : x.weekdays, A = null === x || void 0 === x ? void 0 : x.shortWeekdays;
            p = D({
              a: A ? A[l] : r[l].substr(0, 3),
              A: r[l],
              d: H(k),
              e: H(k, 2, " "),
              w: l,
              b: x.shortMonths[g],
              B: x.months[g],
              m: H(g + 1),
              o: g + 1,
              y: d.toString().substr(2, 2),
              Y: d,
              H: H(h),
              k: h,
              I: H(h % 12 || 12),
              l: h % 12 || 12,
              M: H(this.get("Minutes", p)),
              p: 12 > h ? "AM" : "PM",
              P: 12 > h ? "am" : "pm",
              S: H(p.getSeconds()),
              L: H(Math.floor(t % 1e3), 3)
            }, f.dateFormats);
            J(p, function(d2, g2) {
              for (; -1 !== a3.indexOf("%" + g2); ) a3 = a3.replace("%" + g2, "function" === typeof d2 ? d2.call(m, t) : d2);
            });
            return q2 ? a3.substr(0, 1).toUpperCase() + a3.substr(1) : a3;
          };
          a2.prototype.resolveDTLFormat = function(a3) {
            return G(a3, true) ? a3 : (a3 = L(a3), { main: a3[0], from: a3[1], to: a3[2] });
          };
          a2.prototype.getTimeTicks = function(a3, f2, I, u) {
            var m = this, h = [], l = {};
            var k = new m.Date(f2);
            var g = a3.unitRange, d = a3.count || 1, x;
            u = v(u, 1);
            if (n2(f2)) {
              m.set("Milliseconds", k, g >= q.second ? 0 : d * Math.floor(m.get("Milliseconds", k) / d));
              g >= q.second && m.set("Seconds", k, g >= q.minute ? 0 : d * Math.floor(m.get("Seconds", k) / d));
              g >= q.minute && m.set("Minutes", k, g >= q.hour ? 0 : d * Math.floor(m.get("Minutes", k) / d));
              g >= q.hour && m.set("Hours", k, g >= q.day ? 0 : d * Math.floor(m.get("Hours", k) / d));
              g >= q.day && m.set("Date", k, g >= q.month ? 1 : Math.max(1, d * Math.floor(m.get("Date", k) / d)));
              if (g >= q.month) {
                m.set("Month", k, g >= q.year ? 0 : d * Math.floor(m.get("Month", k) / d));
                var r = m.get("FullYear", k);
              }
              g >= q.year && m.set("FullYear", k, r - r % d);
              g === q.week && (r = m.get("Day", k), m.set(
                "Date",
                k,
                m.get("Date", k) - r + u + (r < u ? -7 : 0)
              ));
              r = m.get("FullYear", k);
              u = m.get("Month", k);
              var p = m.get("Date", k), t = m.get("Hours", k);
              f2 = k.getTime();
              m.variableTimezone && (x = I - f2 > 4 * q.month || m.getTimezoneOffset(f2) !== m.getTimezoneOffset(I));
              f2 = k.getTime();
              for (k = 1; f2 < I; ) h.push(f2), f2 = g === q.year ? m.makeTime(r + k * d, 0) : g === q.month ? m.makeTime(r, u + k * d) : !x || g !== q.day && g !== q.week ? x && g === q.hour && 1 < d ? m.makeTime(r, u, p, t + k * d) : f2 + g * d : m.makeTime(r, u, p + k * d * (g === q.day ? 1 : 7)), k++;
              h.push(f2);
              g <= q.hour && 1e4 > h.length && h.forEach(function(d2) {
                0 === d2 % 18e5 && "000000000" === m.dateFormat("%H%M%S%L", d2) && (l[d2] = "day");
              });
            }
            h.info = D(a3, { higherRanks: l, totalRange: g * d });
            return h;
          };
          return a2;
        })();
        f.Time = a;
        return f.Time;
      });
      O(n, "Core/Options.js", [n["Core/Globals.js"], n["Core/Time.js"], n["Core/Color.js"], n["Core/Utilities.js"]], function(f, a, n2, y) {
        n2 = n2.parse;
        y = y.merge;
        f.defaultOptions = { colors: "#7cb5ec #434348 #90ed7d #f7a35c #8085e9 #f15c80 #e4d354 #2b908f #f45b5b #91e8e1".split(" "), symbols: ["circle", "diamond", "square", "triangle", "triangle-down"], lang: {
          loading: "Loading...",
          months: "January February March April May June July August September October November December".split(" "),
          shortMonths: "Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),
          weekdays: "Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),
          decimalPoint: ".",
          numericSymbols: "kMGTPE".split(""),
          resetZoom: "Reset zoom",
          resetZoomTitle: "Reset zoom level 1:1",
          thousandsSep: " "
        }, global: {}, time: { Date: void 0, getTimezoneOffset: void 0, timezone: void 0, timezoneOffset: 0, useUTC: true }, chart: { styledMode: false, borderRadius: 0, colorCount: 10, defaultSeriesType: "line", ignoreHiddenSeries: true, spacing: [10, 10, 15, 10], resetZoomButton: {
          theme: { zIndex: 6 },
          position: { align: "right", x: -10, y: 10 }
        }, width: null, height: null, borderColor: "#335cad", backgroundColor: "#ffffff", plotBorderColor: "#cccccc" }, title: { text: "Chart title", align: "center", margin: 15, widthAdjust: -44 }, subtitle: { text: "", align: "center", widthAdjust: -44 }, caption: { margin: 15, text: "", align: "left", verticalAlign: "bottom" }, plotOptions: {}, labels: { style: { position: "absolute", color: "#333333" } }, legend: {
          enabled: true,
          align: "center",
          alignColumns: true,
          layout: "horizontal",
          labelFormatter: function() {
            return this.name;
          },
          borderColor: "#999999",
          borderRadius: 0,
          navigation: { activeColor: "#003399", inactiveColor: "#cccccc" },
          itemStyle: { color: "#333333", cursor: "pointer", fontSize: "12px", fontWeight: "bold", textOverflow: "ellipsis" },
          itemHoverStyle: { color: "#000000" },
          itemHiddenStyle: { color: "#cccccc" },
          shadow: false,
          itemCheckboxStyle: { position: "absolute", width: "13px", height: "13px" },
          squareSymbol: true,
          symbolPadding: 5,
          verticalAlign: "bottom",
          x: 0,
          y: 0,
          title: { style: { fontWeight: "bold" } }
        }, loading: { labelStyle: { fontWeight: "bold", position: "relative", top: "45%" }, style: {
          position: "absolute",
          backgroundColor: "#ffffff",
          opacity: 0.5,
          textAlign: "center"
        } }, tooltip: {
          enabled: true,
          animation: f.svg,
          borderRadius: 3,
          dateTimeLabelFormats: { millisecond: "%A, %b %e, %H:%M:%S.%L", second: "%A, %b %e, %H:%M:%S", minute: "%A, %b %e, %H:%M", hour: "%A, %b %e, %H:%M", day: "%A, %b %e, %Y", week: "Week from %A, %b %e, %Y", month: "%B %Y", year: "%Y" },
          footerFormat: "",
          padding: 8,
          snap: f.isTouchDevice ? 25 : 10,
          headerFormat: '<span style="font-size: 10px">{point.key}</span><br/>',
          pointFormat: '<span style="color:{point.color}">●</span> {series.name}: <b>{point.y}</b><br/>',
          backgroundColor: n2("#f7f7f7").setOpacity(0.85).get(),
          borderWidth: 1,
          shadow: true,
          style: { color: "#333333", cursor: "default", fontSize: "12px", whiteSpace: "nowrap" }
        }, credits: { enabled: true, href: "https://www.highcharts.com?credits", position: { align: "right", x: -10, verticalAlign: "bottom", y: -5 }, style: { cursor: "pointer", color: "#999999", fontSize: "9px" }, text: "Highcharts.com" } };
        "";
        f.time = new a(y(f.defaultOptions.global, f.defaultOptions.time));
        f.dateFormat = function(a2, n3, C) {
          return f.time.dateFormat(a2, n3, C);
        };
        return {
          dateFormat: f.dateFormat,
          defaultOptions: f.defaultOptions,
          time: f.time
        };
      });
      O(n, "Core/Axis/Axis.js", [n["Core/Color.js"], n["Core/Globals.js"], n["Core/Axis/Tick.js"], n["Core/Utilities.js"], n["Core/Options.js"]], function(f, a, n2, y, D) {
        var G = y.addEvent, C = y.animObject, J = y.arrayMax, H = y.arrayMin, v = y.clamp, L = y.correctFloat, q = y.defined, K = y.destroyObjectProperties, E = y.error, p = y.extend, t = y.fireEvent, I = y.format, u = y.getMagnitude, m = y.isArray, h = y.isFunction, l = y.isNumber, k = y.isString, g = y.merge, d = y.normalizeTickInterval, x = y.objectEach, r = y.pick, A = y.relativeLength, N = y.removeEvent, B = y.splat, M = y.syncTimeout, R = D.defaultOptions, F = a.deg2rad;
        y = (function() {
          function e(c, b) {
            this.zoomEnabled = this.width = this.visible = this.userOptions = this.translationSlope = this.transB = this.transA = this.top = this.ticks = this.tickRotCorr = this.tickPositions = this.tickmarkOffset = this.tickInterval = this.tickAmount = this.side = this.series = this.right = this.positiveValuesOnly = this.pos = this.pointRangePadding = this.pointRange = this.plotLinesAndBandsGroups = this.plotLinesAndBands = this.paddedTicks = this.overlap = this.options = this.oldMin = this.oldMax = this.offset = this.names = this.minPixelPadding = this.minorTicks = this.minorTickInterval = this.min = this.maxLabelLength = this.max = this.len = this.left = this.labelFormatter = this.labelEdge = this.isLinked = this.height = this.hasVisibleSeries = this.hasNames = this.coll = this.closestPointRange = this.chart = this.categories = this.bottom = this.alternateBands = void 0;
            this.init(c, b);
          }
          e.prototype.init = function(c, b) {
            var e2 = b.isX, d2 = this;
            d2.chart = c;
            d2.horiz = c.inverted && !d2.isZAxis ? !e2 : e2;
            d2.isXAxis = e2;
            d2.coll = d2.coll || (e2 ? "xAxis" : "yAxis");
            t(this, "init", { userOptions: b });
            d2.opposite = b.opposite;
            d2.side = b.side || (d2.horiz ? d2.opposite ? 0 : 2 : d2.opposite ? 1 : 3);
            d2.setOptions(b);
            var g2 = this.options, k2 = g2.type;
            d2.labelFormatter = g2.labels.formatter || d2.defaultLabelFormatter;
            d2.userOptions = b;
            d2.minPixelPadding = 0;
            d2.reversed = g2.reversed;
            d2.visible = false !== g2.visible;
            d2.zoomEnabled = false !== g2.zoomEnabled;
            d2.hasNames = "category" === k2 || true === g2.categories;
            d2.categories = g2.categories || d2.hasNames;
            d2.names || (d2.names = [], d2.names.keys = {});
            d2.plotLinesAndBandsGroups = {};
            d2.positiveValuesOnly = !!d2.logarithmic;
            d2.isLinked = q(g2.linkedTo);
            d2.ticks = {};
            d2.labelEdge = [];
            d2.minorTicks = {};
            d2.plotLinesAndBands = [];
            d2.alternateBands = {};
            d2.len = 0;
            d2.minRange = d2.userMinRange = g2.minRange || g2.maxZoom;
            d2.range = g2.range;
            d2.offset = g2.offset || 0;
            d2.max = null;
            d2.min = null;
            d2.crosshair = r(g2.crosshair, B(c.options.tooltip.crosshairs)[e2 ? 0 : 1], false);
            b = d2.options.events;
            -1 === c.axes.indexOf(d2) && (e2 ? c.axes.splice(c.xAxis.length, 0, d2) : c.axes.push(d2), c[d2.coll].push(d2));
            d2.series = d2.series || [];
            c.inverted && !d2.isZAxis && e2 && "undefined" === typeof d2.reversed && (d2.reversed = true);
            d2.labelRotation = d2.options.labels.rotation;
            x(b, function(b2, c2) {
              h(b2) && G(d2, c2, b2);
            });
            t(this, "afterInit");
          };
          e.prototype.setOptions = function(c) {
            this.options = g(e.defaultOptions, "yAxis" === this.coll && e.defaultYAxisOptions, [e.defaultTopAxisOptions, e.defaultRightAxisOptions, e.defaultBottomAxisOptions, e.defaultLeftAxisOptions][this.side], g(R[this.coll], c));
            t(this, "afterSetOptions", { userOptions: c });
          };
          e.prototype.defaultLabelFormatter = function() {
            var c = this.axis, b = l(this.value) ? this.value : NaN, e2 = c.chart.time, d2 = c.categories, g2 = this.dateTimeLabelFormat, k2 = R.lang, h2 = k2.numericSymbols;
            k2 = k2.numericSymbolMagnitude || 1e3;
            var a2 = h2 && h2.length, r2 = c.options.labels.format;
            c = c.logarithmic ? Math.abs(b) : c.tickInterval;
            var m2 = this.chart, x2 = m2.numberFormatter;
            if (r2) var f2 = I(r2, this, m2);
            else if (d2) f2 = "" + this.value;
            else if (g2) f2 = e2.dateFormat(g2, b);
            else if (a2 && 1e3 <= c) for (; a2-- && "undefined" === typeof f2; ) e2 = Math.pow(k2, a2 + 1), c >= e2 && 0 === 10 * b % e2 && null !== h2[a2] && 0 !== b && (f2 = x2(b / e2, -1) + h2[a2]);
            "undefined" === typeof f2 && (f2 = 1e4 <= Math.abs(b) ? x2(b, -1) : x2(b, -1, void 0, ""));
            return f2;
          };
          e.prototype.getSeriesExtremes = function() {
            var c = this, b = c.chart, e2;
            t(this, "getSeriesExtremes", null, function() {
              c.hasVisibleSeries = false;
              c.dataMin = c.dataMax = c.threshold = null;
              c.softThreshold = !c.isXAxis;
              c.stacking && c.stacking.buildStacks();
              c.series.forEach(function(d2) {
                if (d2.visible || !b.options.chart.ignoreHiddenSeries) {
                  var g2 = d2.options, k2 = g2.threshold;
                  c.hasVisibleSeries = true;
                  c.positiveValuesOnly && 0 >= k2 && (k2 = null);
                  if (c.isXAxis) {
                    if (g2 = d2.xData, g2.length) {
                      g2 = c.logarithmic ? g2.filter(c.validatePositiveValue) : g2;
                      e2 = d2.getXExtremes(g2);
                      var h2 = e2.min;
                      var a2 = e2.max;
                      l(h2) || h2 instanceof Date || (g2 = g2.filter(l), e2 = d2.getXExtremes(g2), h2 = e2.min, a2 = e2.max);
                      g2.length && (c.dataMin = Math.min(r(c.dataMin, h2), h2), c.dataMax = Math.max(r(c.dataMax, a2), a2));
                    }
                  } else if (d2 = d2.applyExtremes(), l(d2.dataMin) && (h2 = d2.dataMin, c.dataMin = Math.min(r(c.dataMin, h2), h2)), l(d2.dataMax) && (a2 = d2.dataMax, c.dataMax = Math.max(r(c.dataMax, a2), a2)), q(k2) && (c.threshold = k2), !g2.softThreshold || c.positiveValuesOnly) c.softThreshold = false;
                }
              });
            });
            t(this, "afterGetSeriesExtremes");
          };
          e.prototype.translate = function(c, b, e2, d2, g2, k2) {
            var h2 = this.linkedParent || this, a2 = 1, w = 0, z = d2 ? h2.oldTransA : h2.transA;
            d2 = d2 ? h2.oldMin : h2.min;
            var r2 = h2.minPixelPadding;
            g2 = (h2.isOrdinal || h2.brokenAxis && h2.brokenAxis.hasBreaks || h2.logarithmic && g2) && h2.lin2val;
            z || (z = h2.transA);
            e2 && (a2 *= -1, w = h2.len);
            h2.reversed && (a2 *= -1, w -= a2 * (h2.sector || h2.len));
            b ? (c = (c * a2 + w - r2) / z + d2, g2 && (c = h2.lin2val(c))) : (g2 && (c = h2.val2lin(c)), c = l(d2) ? a2 * (c - d2) * z + w + a2 * r2 + (l(k2) ? z * k2 : 0) : void 0);
            return c;
          };
          e.prototype.toPixels = function(c, b) {
            return this.translate(c, false, !this.horiz, null, true) + (b ? 0 : this.pos);
          };
          e.prototype.toValue = function(c, b) {
            return this.translate(c - (b ? 0 : this.pos), true, !this.horiz, null, true);
          };
          e.prototype.getPlotLinePath = function(c) {
            function b(b2, c2, e3) {
              if ("pass" !== f2 && b2 < c2 || b2 > e3) f2 ? b2 = v(b2, c2, e3) : q2 = true;
              return b2;
            }
            var e2 = this, d2 = e2.chart, g2 = e2.left, k2 = e2.top, h2 = c.old, a2 = c.value, m2 = c.translatedValue, x2 = c.lineWidth, f2 = c.force, p2, B2, A2, M2, F2 = h2 && d2.oldChartHeight || d2.chartHeight, u2 = h2 && d2.oldChartWidth || d2.chartWidth, q2, N2 = e2.transB;
            c = { value: a2, lineWidth: x2, old: h2, force: f2, acrossPanes: c.acrossPanes, translatedValue: m2 };
            t(
              this,
              "getPlotLinePath",
              c,
              function(c2) {
                m2 = r(m2, e2.translate(a2, null, null, h2));
                m2 = v(m2, -1e5, 1e5);
                p2 = A2 = Math.round(m2 + N2);
                B2 = M2 = Math.round(F2 - m2 - N2);
                l(m2) ? e2.horiz ? (B2 = k2, M2 = F2 - e2.bottom, p2 = A2 = b(p2, g2, g2 + e2.width)) : (p2 = g2, A2 = u2 - e2.right, B2 = M2 = b(B2, k2, k2 + e2.height)) : (q2 = true, f2 = false);
                c2.path = q2 && !f2 ? null : d2.renderer.crispLine([["M", p2, B2], ["L", A2, M2]], x2 || 1);
              }
            );
            return c.path;
          };
          e.prototype.getLinearTickPositions = function(c, b, e2) {
            var d2 = L(Math.floor(b / c) * c);
            e2 = L(Math.ceil(e2 / c) * c);
            var g2 = [], k2;
            L(d2 + c) === d2 && (k2 = 20);
            if (this.single) return [b];
            for (b = d2; b <= e2; ) {
              g2.push(b);
              b = L(b + c, k2);
              if (b === h2) break;
              var h2 = b;
            }
            return g2;
          };
          e.prototype.getMinorTickInterval = function() {
            var c = this.options;
            return true === c.minorTicks ? r(c.minorTickInterval, "auto") : false === c.minorTicks ? null : c.minorTickInterval;
          };
          e.prototype.getMinorTickPositions = function() {
            var c = this.options, b = this.tickPositions, e2 = this.minorTickInterval, d2 = [], g2 = this.pointRangePadding || 0, k2 = this.min - g2;
            g2 = this.max + g2;
            var h2 = g2 - k2;
            if (h2 && h2 / e2 < this.len / 3) {
              var a2 = this.logarithmic;
              if (a2) this.paddedTicks.forEach(function(b2, c2, g3) {
                c2 && d2.push.apply(d2, a2.getLogTickPositions(
                  e2,
                  g3[c2 - 1],
                  g3[c2],
                  true
                ));
              });
              else if (this.dateTime && "auto" === this.getMinorTickInterval()) d2 = d2.concat(this.getTimeTicks(this.dateTime.normalizeTimeTickInterval(e2), k2, g2, c.startOfWeek));
              else for (c = k2 + (b[0] - k2) % e2; c <= g2 && c !== d2[0]; c += e2) d2.push(c);
            }
            0 !== d2.length && this.trimTicks(d2);
            return d2;
          };
          e.prototype.adjustForMinRange = function() {
            var c = this.options, b = this.min, e2 = this.max, d2 = this.logarithmic, g2, k2, h2, a2, l2;
            this.isXAxis && "undefined" === typeof this.minRange && !d2 && (q(c.min) || q(c.max) ? this.minRange = null : (this.series.forEach(function(b2) {
              a2 = b2.xData;
              for (k2 = l2 = b2.xIncrement ? 1 : a2.length - 1; 0 < k2; k2--) if (h2 = a2[k2] - a2[k2 - 1], "undefined" === typeof g2 || h2 < g2) g2 = h2;
            }), this.minRange = Math.min(5 * g2, this.dataMax - this.dataMin)));
            if (e2 - b < this.minRange) {
              var m2 = this.dataMax - this.dataMin >= this.minRange;
              var x2 = this.minRange;
              var f2 = (x2 - e2 + b) / 2;
              f2 = [b - f2, r(c.min, b - f2)];
              m2 && (f2[2] = this.logarithmic ? this.logarithmic.log2lin(this.dataMin) : this.dataMin);
              b = J(f2);
              e2 = [b + x2, r(c.max, b + x2)];
              m2 && (e2[2] = d2 ? d2.log2lin(this.dataMax) : this.dataMax);
              e2 = H(e2);
              e2 - b < x2 && (f2[0] = e2 - x2, f2[1] = r(c.min, e2 - x2), b = J(f2));
            }
            this.min = b;
            this.max = e2;
          };
          e.prototype.getClosest = function() {
            var c;
            this.categories ? c = 1 : this.series.forEach(function(b) {
              var e2 = b.closestPointRange, d2 = b.visible || !b.chart.options.chart.ignoreHiddenSeries;
              !b.noSharedTooltip && q(e2) && d2 && (c = q(c) ? Math.min(c, e2) : e2);
            });
            return c;
          };
          e.prototype.nameToX = function(c) {
            var b = m(this.categories), e2 = b ? this.categories : this.names, d2 = c.options.x;
            c.series.requireSorting = false;
            q(d2) || (d2 = false === this.options.uniqueNames ? c.series.autoIncrement() : b ? e2.indexOf(c.name) : r(e2.keys[c.name], -1));
            if (-1 === d2) {
              if (!b) var g2 = e2.length;
            } else g2 = d2;
            "undefined" !== typeof g2 && (this.names[g2] = c.name, this.names.keys[c.name] = g2);
            return g2;
          };
          e.prototype.updateNames = function() {
            var c = this, b = this.names;
            0 < b.length && (Object.keys(b.keys).forEach(function(c2) {
              delete b.keys[c2];
            }), b.length = 0, this.minRange = this.userMinRange, (this.series || []).forEach(function(b2) {
              b2.xIncrement = null;
              if (!b2.points || b2.isDirtyData) c.max = Math.max(c.max, b2.xData.length - 1), b2.processData(), b2.generatePoints();
              b2.data.forEach(function(e2, d2) {
                if (e2 && e2.options && "undefined" !== typeof e2.name) {
                  var g2 = c.nameToX(e2);
                  "undefined" !== typeof g2 && g2 !== e2.x && (e2.x = g2, b2.xData[d2] = g2);
                }
              });
            }));
          };
          e.prototype.setAxisTranslation = function(c) {
            var b = this, e2 = b.max - b.min, d2 = b.axisPointRange || 0, g2 = 0, h2 = 0, a2 = b.linkedParent, l2 = !!b.categories, m2 = b.transA, x2 = b.isXAxis;
            if (x2 || l2 || d2) {
              var f2 = b.getClosest();
              a2 ? (g2 = a2.minPointOffset, h2 = a2.pointRangePadding) : b.series.forEach(function(c2) {
                var e3 = l2 ? 1 : x2 ? r(c2.options.pointRange, f2, 0) : b.axisPointRange || 0, a3 = c2.options.pointPlacement;
                d2 = Math.max(d2, e3);
                if (!b.single || l2) c2 = c2.is("xrange") ? !x2 : x2, g2 = Math.max(g2, c2 && k(a3) ? 0 : e3 / 2), h2 = Math.max(
                  h2,
                  c2 && "on" === a3 ? 0 : e3
                );
              });
              a2 = b.ordinal && b.ordinal.slope && f2 ? b.ordinal.slope / f2 : 1;
              b.minPointOffset = g2 *= a2;
              b.pointRangePadding = h2 *= a2;
              b.pointRange = Math.min(d2, b.single && l2 ? 1 : e2);
              x2 && (b.closestPointRange = f2);
            }
            c && (b.oldTransA = m2);
            b.translationSlope = b.transA = m2 = b.staticScale || b.len / (e2 + h2 || 1);
            b.transB = b.horiz ? b.left : b.bottom;
            b.minPixelPadding = m2 * g2;
            t(this, "afterSetAxisTranslation");
          };
          e.prototype.minFromRange = function() {
            return this.max - this.range;
          };
          e.prototype.setTickInterval = function(c) {
            var b = this, e2 = b.chart, g2 = b.logarithmic, k2 = b.options, h2 = b.isXAxis, a2 = b.isLinked, m2 = k2.maxPadding, x2 = k2.minPadding, f2 = k2.tickInterval, p2 = k2.tickPixelInterval, B2 = b.categories, A2 = l(b.threshold) ? b.threshold : null, Q = b.softThreshold;
            b.dateTime || B2 || a2 || this.getTickAmount();
            var M2 = r(b.userMin, k2.min);
            var F2 = r(b.userMax, k2.max);
            if (a2) {
              b.linkedParent = e2[b.coll][k2.linkedTo];
              var N2 = b.linkedParent.getExtremes();
              b.min = r(N2.min, N2.dataMin);
              b.max = r(N2.max, N2.dataMax);
              k2.type !== b.linkedParent.options.type && E(11, 1, e2);
            } else {
              if (Q && q(A2)) {
                if (b.dataMin >= A2) N2 = A2, x2 = 0;
                else if (b.dataMax <= A2) {
                  var v2 = A2;
                  m2 = 0;
                }
              }
              b.min = r(M2, N2, b.dataMin);
              b.max = r(F2, v2, b.dataMax);
            }
            g2 && (b.positiveValuesOnly && !c && 0 >= Math.min(b.min, r(b.dataMin, b.min)) && E(10, 1, e2), b.min = L(g2.log2lin(b.min), 16), b.max = L(g2.log2lin(b.max), 16));
            b.range && q(b.max) && (b.userMin = b.min = M2 = Math.max(b.dataMin, b.minFromRange()), b.userMax = F2 = b.max, b.range = null);
            t(b, "foundExtremes");
            b.beforePadding && b.beforePadding();
            b.adjustForMinRange();
            !(B2 || b.axisPointRange || b.stacking && b.stacking.usePercentage || a2) && q(b.min) && q(b.max) && (e2 = b.max - b.min) && (!q(M2) && x2 && (b.min -= e2 * x2), !q(F2) && m2 && (b.max += e2 * m2));
            l(b.userMin) || (l(k2.softMin) && k2.softMin < b.min && (b.min = M2 = k2.softMin), l(k2.floor) && (b.min = Math.max(b.min, k2.floor)));
            l(b.userMax) || (l(k2.softMax) && k2.softMax > b.max && (b.max = F2 = k2.softMax), l(k2.ceiling) && (b.max = Math.min(b.max, k2.ceiling)));
            Q && q(b.dataMin) && (A2 = A2 || 0, !q(M2) && b.min < A2 && b.dataMin >= A2 ? b.min = b.options.minRange ? Math.min(A2, b.max - b.minRange) : A2 : !q(F2) && b.max > A2 && b.dataMax <= A2 && (b.max = b.options.minRange ? Math.max(A2, b.min + b.minRange) : A2));
            b.tickInterval = b.min === b.max || "undefined" === typeof b.min || "undefined" === typeof b.max ? 1 : a2 && !f2 && p2 === b.linkedParent.options.tickPixelInterval ? f2 = b.linkedParent.tickInterval : r(f2, this.tickAmount ? (b.max - b.min) / Math.max(this.tickAmount - 1, 1) : void 0, B2 ? 1 : (b.max - b.min) * p2 / Math.max(b.len, p2));
            h2 && !c && b.series.forEach(function(c2) {
              c2.processData(b.min !== b.oldMin || b.max !== b.oldMax);
            });
            b.setAxisTranslation(true);
            t(this, "initialAxisTranslation");
            b.pointRange && !f2 && (b.tickInterval = Math.max(b.pointRange, b.tickInterval));
            c = r(k2.minTickInterval, b.dateTime && !b.series.some(function(b2) {
              return b2.noSharedTooltip;
            }) ? b.closestPointRange : 0);
            !f2 && b.tickInterval < c && (b.tickInterval = c);
            b.dateTime || b.logarithmic || f2 || (b.tickInterval = d(b.tickInterval, void 0, u(b.tickInterval), r(k2.allowDecimals, 0.5 > b.tickInterval || void 0 !== this.tickAmount), !!this.tickAmount));
            this.tickAmount || (b.tickInterval = b.unsquish());
            this.setTickPositions();
          };
          e.prototype.setTickPositions = function() {
            var c = this.options, b = c.tickPositions;
            var e2 = this.getMinorTickInterval();
            var d2 = c.tickPositioner, g2 = this.hasVerticalPanning(), k2 = "colorAxis" === this.coll, h2 = (k2 || !g2) && c.startOnTick;
            g2 = (k2 || !g2) && c.endOnTick;
            this.tickmarkOffset = this.categories && "between" === c.tickmarkPlacement && 1 === this.tickInterval ? 0.5 : 0;
            this.minorTickInterval = "auto" === e2 && this.tickInterval ? this.tickInterval / 5 : e2;
            this.single = this.min === this.max && q(this.min) && !this.tickAmount && (parseInt(this.min, 10) === this.min || false !== c.allowDecimals);
            this.tickPositions = e2 = b && b.slice();
            !e2 && (this.ordinal && this.ordinal.positions || !((this.max - this.min) / this.tickInterval > Math.max(2 * this.len, 200)) ? e2 = this.dateTime ? this.getTimeTicks(this.dateTime.normalizeTimeTickInterval(
              this.tickInterval,
              c.units
            ), this.min, this.max, c.startOfWeek, this.ordinal && this.ordinal.positions, this.closestPointRange, true) : this.logarithmic ? this.logarithmic.getLogTickPositions(this.tickInterval, this.min, this.max) : this.getLinearTickPositions(this.tickInterval, this.min, this.max) : (e2 = [this.min, this.max], E(19, false, this.chart)), e2.length > this.len && (e2 = [e2[0], e2.pop()], e2[0] === e2[1] && (e2.length = 1)), this.tickPositions = e2, d2 && (d2 = d2.apply(this, [this.min, this.max]))) && (this.tickPositions = e2 = d2);
            this.paddedTicks = e2.slice(0);
            this.trimTicks(
              e2,
              h2,
              g2
            );
            this.isLinked || (this.single && 2 > e2.length && !this.categories && !this.series.some(function(b2) {
              return b2.is("heatmap") && "between" === b2.options.pointPlacement;
            }) && (this.min -= 0.5, this.max += 0.5), b || d2 || this.adjustTickAmount());
            t(this, "afterSetTickPositions");
          };
          e.prototype.trimTicks = function(c, b, e2) {
            var d2 = c[0], g2 = c[c.length - 1], k2 = !this.isOrdinal && this.minPointOffset || 0;
            t(this, "trimTicks");
            if (!this.isLinked) {
              if (b && -Infinity !== d2) this.min = d2;
              else for (; this.min - k2 > c[0]; ) c.shift();
              if (e2) this.max = g2;
              else for (; this.max + k2 < c[c.length - 1]; ) c.pop();
              0 === c.length && q(d2) && !this.options.tickPositions && c.push((g2 + d2) / 2);
            }
          };
          e.prototype.alignToOthers = function() {
            var c = {}, b, e2 = this.options;
            false === this.chart.options.chart.alignTicks || false === e2.alignTicks || false === e2.startOnTick || false === e2.endOnTick || this.logarithmic || this.chart[this.coll].forEach(function(e3) {
              var d2 = e3.options;
              d2 = [e3.horiz ? d2.left : d2.top, d2.width, d2.height, d2.pane].join();
              e3.series.length && (c[d2] ? b = true : c[d2] = 1);
            });
            return b;
          };
          e.prototype.getTickAmount = function() {
            var c = this.options, b = c.tickAmount, e2 = c.tickPixelInterval;
            !q(c.tickInterval) && !b && this.len < e2 && !this.isRadial && !this.logarithmic && c.startOnTick && c.endOnTick && (b = 2);
            !b && this.alignToOthers() && (b = Math.ceil(this.len / e2) + 1);
            4 > b && (this.finalTickAmt = b, b = 5);
            this.tickAmount = b;
          };
          e.prototype.adjustTickAmount = function() {
            var c = this.options, b = this.tickInterval, e2 = this.tickPositions, d2 = this.tickAmount, g2 = this.finalTickAmt, k2 = e2 && e2.length, h2 = r(this.threshold, this.softThreshold ? 0 : null), a2;
            if (this.hasData()) {
              if (k2 < d2) {
                for (a2 = this.min; e2.length < d2; ) e2.length % 2 || a2 === h2 ? e2.push(L(e2[e2.length - 1] + b)) : e2.unshift(L(e2[0] - b));
                this.transA *= (k2 - 1) / (d2 - 1);
                this.min = c.startOnTick ? e2[0] : Math.min(this.min, e2[0]);
                this.max = c.endOnTick ? e2[e2.length - 1] : Math.max(this.max, e2[e2.length - 1]);
              } else k2 > d2 && (this.tickInterval *= 2, this.setTickPositions());
              if (q(g2)) {
                for (b = c = e2.length; b--; ) (3 === g2 && 1 === b % 2 || 2 >= g2 && 0 < b && b < c - 1) && e2.splice(b, 1);
                this.finalTickAmt = void 0;
              }
            }
          };
          e.prototype.setScale = function() {
            var c, b = false, e2 = false;
            this.series.forEach(function(c2) {
              var d2;
              b = b || c2.isDirtyData || c2.isDirty;
              e2 = e2 || (null === (d2 = c2.xAxis) || void 0 === d2 ? void 0 : d2.isDirty) || false;
            });
            this.oldMin = this.min;
            this.oldMax = this.max;
            this.oldAxisLength = this.len;
            this.setAxisSize();
            (c = this.len !== this.oldAxisLength) || b || e2 || this.isLinked || this.forceRedraw || this.userMin !== this.oldUserMin || this.userMax !== this.oldUserMax || this.alignToOthers() ? (this.stacking && this.stacking.resetStacks(), this.forceRedraw = false, this.getSeriesExtremes(), this.setTickInterval(), this.oldUserMin = this.userMin, this.oldUserMax = this.userMax, this.isDirty || (this.isDirty = c || this.min !== this.oldMin || this.max !== this.oldMax)) : this.stacking && this.stacking.cleanStacks();
            b && this.panningState && (this.panningState.isDirty = true);
            t(this, "afterSetScale");
          };
          e.prototype.setExtremes = function(c, b, e2, d2, g2) {
            var k2 = this, h2 = k2.chart;
            e2 = r(e2, true);
            k2.series.forEach(function(b2) {
              delete b2.kdTree;
            });
            g2 = p(g2, { min: c, max: b });
            t(k2, "setExtremes", g2, function() {
              k2.userMin = c;
              k2.userMax = b;
              k2.eventArgs = g2;
              e2 && h2.redraw(d2);
            });
          };
          e.prototype.zoom = function(c, b) {
            var e2 = this, d2 = this.dataMin, g2 = this.dataMax, k2 = this.options, h2 = Math.min(d2, r(k2.min, d2)), a2 = Math.max(
              g2,
              r(k2.max, g2)
            );
            c = { newMin: c, newMax: b };
            t(this, "zoom", c, function(b2) {
              var c2 = b2.newMin, k3 = b2.newMax;
              if (c2 !== e2.min || k3 !== e2.max) e2.allowZoomOutside || (q(d2) && (c2 < h2 && (c2 = h2), c2 > a2 && (c2 = a2)), q(g2) && (k3 < h2 && (k3 = h2), k3 > a2 && (k3 = a2))), e2.displayBtn = "undefined" !== typeof c2 || "undefined" !== typeof k3, e2.setExtremes(c2, k3, false, void 0, { trigger: "zoom" });
              b2.zoomed = true;
            });
            return c.zoomed;
          };
          e.prototype.setAxisSize = function() {
            var c = this.chart, b = this.options, e2 = b.offsets || [0, 0, 0, 0], d2 = this.horiz, g2 = this.width = Math.round(A(r(b.width, c.plotWidth - e2[3] + e2[1]), c.plotWidth)), k2 = this.height = Math.round(A(r(b.height, c.plotHeight - e2[0] + e2[2]), c.plotHeight)), h2 = this.top = Math.round(A(r(b.top, c.plotTop + e2[0]), c.plotHeight, c.plotTop));
            b = this.left = Math.round(A(r(b.left, c.plotLeft + e2[3]), c.plotWidth, c.plotLeft));
            this.bottom = c.chartHeight - k2 - h2;
            this.right = c.chartWidth - g2 - b;
            this.len = Math.max(d2 ? g2 : k2, 0);
            this.pos = d2 ? b : h2;
          };
          e.prototype.getExtremes = function() {
            var c = this.logarithmic;
            return {
              min: c ? L(c.lin2log(this.min)) : this.min,
              max: c ? L(c.lin2log(this.max)) : this.max,
              dataMin: this.dataMin,
              dataMax: this.dataMax,
              userMin: this.userMin,
              userMax: this.userMax
            };
          };
          e.prototype.getThreshold = function(c) {
            var b = this.logarithmic, e2 = b ? b.lin2log(this.min) : this.min;
            b = b ? b.lin2log(this.max) : this.max;
            null === c || -Infinity === c ? c = e2 : Infinity === c ? c = b : e2 > c ? c = e2 : b < c && (c = b);
            return this.translate(c, 0, 1, 0, 1);
          };
          e.prototype.autoLabelAlign = function(c) {
            var b = (r(c, 0) - 90 * this.side + 720) % 360;
            c = { align: "center" };
            t(this, "autoLabelAlign", c, function(c2) {
              15 < b && 165 > b ? c2.align = "right" : 195 < b && 345 > b && (c2.align = "left");
            });
            return c.align;
          };
          e.prototype.tickSize = function(c) {
            var b = this.options, e2 = b["tick" === c ? "tickLength" : "minorTickLength"], d2 = r(b["tick" === c ? "tickWidth" : "minorTickWidth"], "tick" === c && this.isXAxis && !this.categories ? 1 : 0);
            if (d2 && e2) {
              "inside" === b[c + "Position"] && (e2 = -e2);
              var g2 = [e2, d2];
            }
            c = { tickSize: g2 };
            t(this, "afterTickSize", c);
            return c.tickSize;
          };
          e.prototype.labelMetrics = function() {
            var c = this.tickPositions && this.tickPositions[0] || 0;
            return this.chart.renderer.fontMetrics(this.options.labels.style && this.options.labels.style.fontSize, this.ticks[c] && this.ticks[c].label);
          };
          e.prototype.unsquish = function() {
            var c = this.options.labels, b = this.horiz, e2 = this.tickInterval, d2 = e2, g2 = this.len / (((this.categories ? 1 : 0) + this.max - this.min) / e2), k2, h2 = c.rotation, a2 = this.labelMetrics(), l2, m2 = Number.MAX_VALUE, x2, f2 = this.max - this.min, p2 = function(b2) {
              var c2 = b2 / (g2 || 1);
              c2 = 1 < c2 ? Math.ceil(c2) : 1;
              c2 * e2 > f2 && Infinity !== b2 && Infinity !== g2 && f2 && (c2 = Math.ceil(f2 / e2));
              return L(c2 * e2);
            };
            b ? (x2 = !c.staggerLines && !c.step && (q(h2) ? [h2] : g2 < r(c.autoRotationLimit, 80) && c.autoRotation)) && x2.forEach(function(b2) {
              if (b2 === h2 || b2 && -90 <= b2 && 90 >= b2) {
                l2 = p2(Math.abs(a2.h / Math.sin(F * b2)));
                var c2 = l2 + Math.abs(b2 / 360);
                c2 < m2 && (m2 = c2, k2 = b2, d2 = l2);
              }
            }) : c.step || (d2 = p2(a2.h));
            this.autoRotation = x2;
            this.labelRotation = r(k2, h2);
            return d2;
          };
          e.prototype.getSlotWidth = function(c) {
            var b, e2 = this.chart, d2 = this.horiz, g2 = this.options.labels, k2 = Math.max(this.tickPositions.length - (this.categories ? 0 : 1), 1), h2 = e2.margin[3];
            if (c && l(c.slotWidth)) return c.slotWidth;
            if (d2 && g2 && 2 > (g2.step || 0)) return g2.rotation ? 0 : (this.staggerLines || 1) * this.len / k2;
            if (!d2) {
              c = null === (b = null === g2 || void 0 === g2 ? void 0 : g2.style) || void 0 === b ? void 0 : b.width;
              if (void 0 !== c) return parseInt(
                c,
                10
              );
              if (h2) return h2 - e2.spacing[3];
            }
            return 0.33 * e2.chartWidth;
          };
          e.prototype.renderUnsquish = function() {
            var c = this.chart, b = c.renderer, e2 = this.tickPositions, d2 = this.ticks, g2 = this.options.labels, h2 = g2 && g2.style || {}, a2 = this.horiz, l2 = this.getSlotWidth(), m2 = Math.max(1, Math.round(l2 - 2 * (g2.padding || 5))), r2 = {}, x2 = this.labelMetrics(), f2 = g2.style && g2.style.textOverflow, p2 = 0;
            k(g2.rotation) || (r2.rotation = g2.rotation || 0);
            e2.forEach(function(b2) {
              b2 = d2[b2];
              b2.movedLabel && b2.replaceMovedLabel();
              b2 && b2.label && b2.label.textPxLength > p2 && (p2 = b2.label.textPxLength);
            });
            this.maxLabelLength = p2;
            if (this.autoRotation) p2 > m2 && p2 > x2.h ? r2.rotation = this.labelRotation : this.labelRotation = 0;
            else if (l2) {
              var B2 = m2;
              if (!f2) {
                var A2 = "clip";
                for (m2 = e2.length; !a2 && m2--; ) {
                  var t2 = e2[m2];
                  if (t2 = d2[t2].label) t2.styles && "ellipsis" === t2.styles.textOverflow ? t2.css({ textOverflow: "clip" }) : t2.textPxLength > l2 && t2.css({ width: l2 + "px" }), t2.getBBox().height > this.len / e2.length - (x2.h - x2.f) && (t2.specificTextOverflow = "ellipsis");
                }
              }
            }
            r2.rotation && (B2 = p2 > 0.5 * c.chartHeight ? 0.33 * c.chartHeight : p2, f2 || (A2 = "ellipsis"));
            if (this.labelAlign = g2.align || this.autoLabelAlign(this.labelRotation)) r2.align = this.labelAlign;
            e2.forEach(function(b2) {
              var c2 = (b2 = d2[b2]) && b2.label, e3 = h2.width, g3 = {};
              c2 && (c2.attr(r2), b2.shortenLabel ? b2.shortenLabel() : B2 && !e3 && "nowrap" !== h2.whiteSpace && (B2 < c2.textPxLength || "SPAN" === c2.element.tagName) ? (g3.width = B2 + "px", f2 || (g3.textOverflow = c2.specificTextOverflow || A2), c2.css(g3)) : c2.styles && c2.styles.width && !g3.width && !e3 && c2.css({ width: null }), delete c2.specificTextOverflow, b2.rotation = r2.rotation);
            }, this);
            this.tickRotCorr = b.rotCorr(x2.b, this.labelRotation || 0, 0 !== this.side);
          };
          e.prototype.hasData = function() {
            return this.series.some(function(c) {
              return c.hasData();
            }) || this.options.showEmpty && q(this.min) && q(this.max);
          };
          e.prototype.addTitle = function(c) {
            var b = this.chart.renderer, e2 = this.horiz, d2 = this.opposite, k2 = this.options.title, h2, a2 = this.chart.styledMode;
            this.axisTitle || ((h2 = k2.textAlign) || (h2 = (e2 ? { low: "left", middle: "center", high: "right" } : { low: d2 ? "right" : "left", middle: "center", high: d2 ? "left" : "right" })[k2.align]), this.axisTitle = b.text(k2.text, 0, 0, k2.useHTML).attr({ zIndex: 7, rotation: k2.rotation || 0, align: h2 }).addClass("highcharts-axis-title"), a2 || this.axisTitle.css(g(k2.style)), this.axisTitle.add(this.axisGroup), this.axisTitle.isNew = true);
            a2 || k2.style.width || this.isRadial || this.axisTitle.css({ width: this.len + "px" });
            this.axisTitle[c ? "show" : "hide"](c);
          };
          e.prototype.generateTick = function(c) {
            var b = this.ticks;
            b[c] ? b[c].addLabel() : b[c] = new n2(this, c);
          };
          e.prototype.getOffset = function() {
            var c = this, b = c.chart, e2 = b.renderer, d2 = c.options, g2 = c.tickPositions, k2 = c.ticks, h2 = c.horiz, a2 = c.side, l2 = b.inverted && !c.isZAxis ? [1, 0, 3, 2][a2] : a2, m2, f2 = 0, p2 = 0, B2 = d2.title, A2 = d2.labels, M2 = 0, F2 = b.axisOffset;
            b = b.clipOffset;
            var u2 = [-1, 1, 1, -1][a2], N2 = d2.className, v2 = c.axisParent;
            var I2 = c.hasData();
            c.showAxis = m2 = I2 || r(d2.showEmpty, true);
            c.staggerLines = c.horiz && A2.staggerLines;
            c.axisGroup || (c.gridGroup = e2.g("grid").attr({ zIndex: d2.gridZIndex || 1 }).addClass("highcharts-" + this.coll.toLowerCase() + "-grid " + (N2 || "")).add(v2), c.axisGroup = e2.g("axis").attr({ zIndex: d2.zIndex || 2 }).addClass("highcharts-" + this.coll.toLowerCase() + " " + (N2 || "")).add(v2), c.labelGroup = e2.g("axis-labels").attr({ zIndex: A2.zIndex || 7 }).addClass("highcharts-" + c.coll.toLowerCase() + "-labels " + (N2 || "")).add(v2));
            I2 || c.isLinked ? (g2.forEach(function(b2, e3) {
              c.generateTick(b2, e3);
            }), c.renderUnsquish(), c.reserveSpaceDefault = 0 === a2 || 2 === a2 || { 1: "left", 3: "right" }[a2] === c.labelAlign, r(A2.reserveSpace, "center" === c.labelAlign ? true : null, c.reserveSpaceDefault) && g2.forEach(function(b2) {
              M2 = Math.max(k2[b2].getLabelSize(), M2);
            }), c.staggerLines && (M2 *= c.staggerLines), c.labelOffset = M2 * (c.opposite ? -1 : 1)) : x(k2, function(b2, c2) {
              b2.destroy();
              delete k2[c2];
            });
            if (B2 && B2.text && false !== B2.enabled && (c.addTitle(m2), m2 && false !== B2.reserveSpace)) {
              c.titleOffset = f2 = c.axisTitle.getBBox()[h2 ? "height" : "width"];
              var R2 = B2.offset;
              p2 = q(R2) ? 0 : r(B2.margin, h2 ? 5 : 10);
            }
            c.renderLine();
            c.offset = u2 * r(d2.offset, F2[a2] ? F2[a2] + (d2.margin || 0) : 0);
            c.tickRotCorr = c.tickRotCorr || { x: 0, y: 0 };
            e2 = 0 === a2 ? -c.labelMetrics().h : 2 === a2 ? c.tickRotCorr.y : 0;
            p2 = Math.abs(M2) + p2;
            M2 && (p2 = p2 - e2 + u2 * (h2 ? r(A2.y, c.tickRotCorr.y + 8 * u2) : A2.x));
            c.axisTitleMargin = r(R2, p2);
            c.getMaxLabelDimensions && (c.maxLabelDimensions = c.getMaxLabelDimensions(k2, g2));
            h2 = this.tickSize("tick");
            F2[a2] = Math.max(F2[a2], c.axisTitleMargin + f2 + u2 * c.offset, p2, g2 && g2.length && h2 ? h2[0] + u2 * c.offset : 0);
            d2 = d2.offset ? 0 : 2 * Math.floor(c.axisLine.strokeWidth() / 2);
            b[l2] = Math.max(b[l2], d2);
            t(this, "afterGetOffset");
          };
          e.prototype.getLinePath = function(c) {
            var b = this.chart, e2 = this.opposite, d2 = this.offset, g2 = this.horiz, k2 = this.left + (e2 ? this.width : 0) + d2;
            d2 = b.chartHeight - this.bottom - (e2 ? this.height : 0) + d2;
            e2 && (c *= -1);
            return b.renderer.crispLine([["M", g2 ? this.left : k2, g2 ? d2 : this.top], ["L", g2 ? b.chartWidth - this.right : k2, g2 ? d2 : b.chartHeight - this.bottom]], c);
          };
          e.prototype.renderLine = function() {
            this.axisLine || (this.axisLine = this.chart.renderer.path().addClass("highcharts-axis-line").add(this.axisGroup), this.chart.styledMode || this.axisLine.attr({ stroke: this.options.lineColor, "stroke-width": this.options.lineWidth, zIndex: 7 }));
          };
          e.prototype.getTitlePosition = function() {
            var c = this.horiz, b = this.left, e2 = this.top, d2 = this.len, g2 = this.options.title, k2 = c ? b : e2, h2 = this.opposite, a2 = this.offset, l2 = g2.x || 0, m2 = g2.y || 0, r2 = this.axisTitle, x2 = this.chart.renderer.fontMetrics(g2.style && g2.style.fontSize, r2);
            r2 = Math.max(r2.getBBox(null, 0).height - x2.h - 1, 0);
            d2 = { low: k2 + (c ? 0 : d2), middle: k2 + d2 / 2, high: k2 + (c ? d2 : 0) }[g2.align];
            b = (c ? e2 + this.height : b) + (c ? 1 : -1) * (h2 ? -1 : 1) * this.axisTitleMargin + [-r2, r2, x2.f, -r2][this.side];
            c = { x: c ? d2 + l2 : b + (h2 ? this.width : 0) + a2 + l2, y: c ? b + m2 - (h2 ? this.height : 0) + a2 : d2 + m2 };
            t(this, "afterGetTitlePosition", { titlePosition: c });
            return c;
          };
          e.prototype.renderMinorTick = function(c) {
            var b = this.chart.hasRendered && l(this.oldMin), e2 = this.minorTicks;
            e2[c] || (e2[c] = new n2(this, c, "minor"));
            b && e2[c].isNew && e2[c].render(null, true);
            e2[c].render(null, false, 1);
          };
          e.prototype.renderTick = function(c, b) {
            var e2 = this.isLinked, d2 = this.ticks, g2 = this.chart.hasRendered && l(this.oldMin);
            if (!e2 || c >= this.min && c <= this.max) d2[c] || (d2[c] = new n2(this, c)), g2 && d2[c].isNew && d2[c].render(b, true, -1), d2[c].render(b);
          };
          e.prototype.render = function() {
            var c = this, b = c.chart, e2 = c.logarithmic, d2 = c.options, g2 = c.isLinked, k2 = c.tickPositions, h2 = c.axisTitle, m2 = c.ticks, r2 = c.minorTicks, f2 = c.alternateBands, p2 = d2.stackLabels, B2 = d2.alternateGridColor, A2 = c.tickmarkOffset, Q = c.axisLine, F2 = c.showAxis, u2 = C(b.renderer.globalAnimation), q2, N2;
            c.labelEdge.length = 0;
            c.overlap = false;
            [m2, r2, f2].forEach(function(b2) {
              x(b2, function(b3) {
                b3.isActive = false;
              });
            });
            if (c.hasData() || g2) c.minorTickInterval && !c.categories && c.getMinorTickPositions().forEach(function(b2) {
              c.renderMinorTick(b2);
            }), k2.length && (k2.forEach(function(b2, e3) {
              c.renderTick(b2, e3);
            }), A2 && (0 === c.min || c.single) && (m2[-1] || (m2[-1] = new n2(c, -1, null, true)), m2[-1].render(-1))), B2 && k2.forEach(function(d3, g3) {
              N2 = "undefined" !== typeof k2[g3 + 1] ? k2[g3 + 1] + A2 : c.max - A2;
              0 === g3 % 2 && d3 < c.max && N2 <= c.max + (b.polar ? -A2 : A2) && (f2[d3] || (f2[d3] = new a.PlotLineOrBand(c)), q2 = d3 + A2, f2[d3].options = { from: e2 ? e2.lin2log(q2) : q2, to: e2 ? e2.lin2log(N2) : N2, color: B2, className: "highcharts-alternate-grid" }, f2[d3].render(), f2[d3].isActive = true);
            }), c._addedPlotLB || ((d2.plotLines || []).concat(d2.plotBands || []).forEach(function(b2) {
              c.addPlotBandOrLine(b2);
            }), c._addedPlotLB = true);
            [m2, r2, f2].forEach(function(c2) {
              var e3, d3 = [], g3 = u2.duration;
              x(c2, function(b2, c3) {
                b2.isActive || (b2.render(c3, false, 0), b2.isActive = false, d3.push(c3));
              });
              M(function() {
                for (e3 = d3.length; e3--; ) c2[d3[e3]] && !c2[d3[e3]].isActive && (c2[d3[e3]].destroy(), delete c2[d3[e3]]);
              }, c2 !== f2 && b.hasRendered && g3 ? g3 : 0);
            });
            Q && (Q[Q.isPlaced ? "animate" : "attr"]({ d: this.getLinePath(Q.strokeWidth()) }), Q.isPlaced = true, Q[F2 ? "show" : "hide"](F2));
            h2 && F2 && (d2 = c.getTitlePosition(), l(d2.y) ? (h2[h2.isNew ? "attr" : "animate"](d2), h2.isNew = false) : (h2.attr("y", -9999), h2.isNew = true));
            p2 && p2.enabled && c.stacking && c.stacking.renderStackTotals();
            c.isDirty = false;
            t(this, "afterRender");
          };
          e.prototype.redraw = function() {
            this.visible && (this.render(), this.plotLinesAndBands.forEach(function(c) {
              c.render();
            }));
            this.series.forEach(function(c) {
              c.isDirty = true;
            });
          };
          e.prototype.getKeepProps = function() {
            return this.keepProps || e.keepProps;
          };
          e.prototype.destroy = function(c) {
            var b = this, e2 = b.plotLinesAndBands, d2;
            t(this, "destroy", { keepEvents: c });
            c || N(b);
            [b.ticks, b.minorTicks, b.alternateBands].forEach(function(b2) {
              K(b2);
            });
            if (e2) for (c = e2.length; c--; ) e2[c].destroy();
            "axisLine axisTitle axisGroup gridGroup labelGroup cross scrollbar".split(" ").forEach(function(c2) {
              b[c2] && (b[c2] = b[c2].destroy());
            });
            for (d2 in b.plotLinesAndBandsGroups) b.plotLinesAndBandsGroups[d2] = b.plotLinesAndBandsGroups[d2].destroy();
            x(b, function(c2, e3) {
              -1 === b.getKeepProps().indexOf(e3) && delete b[e3];
            });
          };
          e.prototype.drawCrosshair = function(c, b) {
            var e2 = this.crosshair, d2 = r(e2.snap, true), g2, k2 = this.cross, h2 = this.chart;
            t(this, "drawCrosshair", { e: c, point: b });
            c || (c = this.cross && this.cross.e);
            if (this.crosshair && false !== (q(b) || !d2)) {
              d2 ? q(b) && (g2 = r("colorAxis" !== this.coll ? b.crosshairPos : null, this.isXAxis ? b.plotX : this.len - b.plotY)) : g2 = c && (this.horiz ? c.chartX - this.pos : this.len - c.chartY + this.pos);
              if (q(g2)) {
                var a2 = { value: b && (this.isXAxis ? b.x : r(b.stackY, b.y)), translatedValue: g2 };
                h2.polar && p(a2, { isCrosshair: true, chartX: c && c.chartX, chartY: c && c.chartY, point: b });
                a2 = this.getPlotLinePath(a2) || null;
              }
              if (!q(a2)) {
                this.hideCrosshair();
                return;
              }
              d2 = this.categories && !this.isRadial;
              k2 || (this.cross = k2 = h2.renderer.path().addClass("highcharts-crosshair highcharts-crosshair-" + (d2 ? "category " : "thin ") + e2.className).attr({ zIndex: r(e2.zIndex, 2) }).add(), h2.styledMode || (k2.attr({ stroke: e2.color || (d2 ? f.parse("#ccd6eb").setOpacity(0.25).get() : "#cccccc"), "stroke-width": r(e2.width, 1) }).css({ "pointer-events": "none" }), e2.dashStyle && k2.attr({ dashstyle: e2.dashStyle })));
              k2.show().attr({ d: a2 });
              d2 && !e2.width && k2.attr({ "stroke-width": this.transA });
              this.cross.e = c;
            } else this.hideCrosshair();
            t(this, "afterDrawCrosshair", { e: c, point: b });
          };
          e.prototype.hideCrosshair = function() {
            this.cross && this.cross.hide();
            t(this, "afterHideCrosshair");
          };
          e.prototype.hasVerticalPanning = function() {
            var c, b;
            return /y/.test((null === (b = null === (c = this.chart.options.chart) || void 0 === c ? void 0 : c.panning) || void 0 === b ? void 0 : b.type) || "");
          };
          e.prototype.validatePositiveValue = function(c) {
            return l(c) && 0 < c;
          };
          e.defaultOptions = { dateTimeLabelFormats: {
            millisecond: { main: "%H:%M:%S.%L", range: false },
            second: { main: "%H:%M:%S", range: false },
            minute: { main: "%H:%M", range: false },
            hour: { main: "%H:%M", range: false },
            day: { main: "%e. %b" },
            week: { main: "%e. %b" },
            month: { main: "%b '%y" },
            year: { main: "%Y" }
          }, endOnTick: false, labels: { enabled: true, indentation: 10, x: 0, style: { color: "#666666", cursor: "default", fontSize: "11px" } }, maxPadding: 0.01, minorTickLength: 2, minorTickPosition: "outside", minPadding: 0.01, showEmpty: true, startOfWeek: 1, startOnTick: false, tickLength: 10, tickPixelInterval: 100, tickmarkPlacement: "between", tickPosition: "outside", title: {
            align: "middle",
            style: { color: "#666666" }
          }, type: "linear", minorGridLineColor: "#f2f2f2", minorGridLineWidth: 1, minorTickColor: "#999999", lineColor: "#ccd6eb", lineWidth: 1, gridLineColor: "#e6e6e6", tickColor: "#ccd6eb" };
          e.defaultYAxisOptions = { endOnTick: true, maxPadding: 0.05, minPadding: 0.05, tickPixelInterval: 72, showLastLabel: true, labels: { x: -8 }, startOnTick: true, title: { rotation: 270, text: "Values" }, stackLabels: { animation: {}, allowOverlap: false, enabled: false, crop: true, overflow: "justify", formatter: function() {
            var c = this.axis.chart.numberFormatter;
            return c(
              this.total,
              -1
            );
          }, style: { color: "#000000", fontSize: "11px", fontWeight: "bold", textOutline: "1px contrast" } }, gridLineWidth: 1, lineWidth: 0 };
          e.defaultLeftAxisOptions = { labels: { x: -15 }, title: { rotation: 270 } };
          e.defaultRightAxisOptions = { labels: { x: 15 }, title: { rotation: 90 } };
          e.defaultBottomAxisOptions = { labels: { autoRotation: [-45], x: 0 }, margin: 15, title: { rotation: 0 } };
          e.defaultTopAxisOptions = { labels: { autoRotation: [-45], x: 0 }, margin: 15, title: { rotation: 0 } };
          e.keepProps = "extKey hcEvents names series userMax userMin".split(" ");
          return e;
        })();
        a.Axis = y;
        return a.Axis;
      });
      O(n, "Core/Axis/DateTimeAxis.js", [n["Core/Axis/Axis.js"], n["Core/Utilities.js"]], function(f, a) {
        var n2 = a.addEvent, y = a.getMagnitude, D = a.normalizeTickInterval, G = a.timeUnits, C = (function() {
          function a2(a3) {
            this.axis = a3;
          }
          a2.prototype.normalizeTimeTickInterval = function(a3, f2) {
            var v = f2 || [["millisecond", [1, 2, 5, 10, 20, 25, 50, 100, 200, 500]], ["second", [1, 2, 5, 10, 15, 30]], ["minute", [1, 2, 5, 10, 15, 30]], ["hour", [1, 2, 3, 4, 6, 8, 12]], ["day", [1, 2]], ["week", [1, 2]], ["month", [1, 2, 3, 4, 6]], ["year", null]];
            f2 = v[v.length - 1];
            var q = G[f2[0]], H = f2[1], E;
            for (E = 0; E < v.length && !(f2 = v[E], q = G[f2[0]], H = f2[1], v[E + 1] && a3 <= (q * H[H.length - 1] + G[v[E + 1][0]]) / 2); E++) ;
            q === G.year && a3 < 5 * q && (H = [1, 2, 5]);
            a3 = D(a3 / q, H, "year" === f2[0] ? Math.max(y(a3 / q), 1) : 1);
            return { unitRange: q, count: a3, unitName: f2[0] };
          };
          return a2;
        })();
        a = (function() {
          function a2() {
          }
          a2.compose = function(a3) {
            a3.keepProps.push("dateTime");
            a3.prototype.getTimeTicks = function() {
              return this.chart.time.getTimeTicks.apply(this.chart.time, arguments);
            };
            n2(a3, "init", function(a4) {
              "datetime" !== a4.userOptions.type ? this.dateTime = void 0 : this.dateTime || (this.dateTime = new C(this));
            });
          };
          a2.AdditionsClass = C;
          return a2;
        })();
        a.compose(f);
        return a;
      });
      O(n, "Core/Axis/LogarithmicAxis.js", [n["Core/Axis/Axis.js"], n["Core/Utilities.js"]], function(f, a) {
        var n2 = a.addEvent, y = a.getMagnitude, D = a.normalizeTickInterval, G = a.pick, C = (function() {
          function a2(a3) {
            this.axis = a3;
          }
          a2.prototype.getLogTickPositions = function(a3, f2, n3, q) {
            var v = this.axis, E = v.len, p = v.options, t = [];
            q || (this.minorAutoInterval = void 0);
            if (0.5 <= a3) a3 = Math.round(a3), t = v.getLinearTickPositions(a3, f2, n3);
            else if (0.08 <= a3) {
              p = Math.floor(f2);
              var I, u;
              for (E = 0.3 < a3 ? [1, 2, 4] : 0.15 < a3 ? [1, 2, 4, 6, 8] : [1, 2, 3, 4, 5, 6, 7, 8, 9]; p < n3 + 1 && !u; p++) {
                var m = E.length;
                for (I = 0; I < m && !u; I++) {
                  var h = this.log2lin(this.lin2log(p) * E[I]);
                  h > f2 && (!q || l <= n3) && "undefined" !== typeof l && t.push(l);
                  l > n3 && (u = true);
                  var l = h;
                }
              }
            } else f2 = this.lin2log(f2), n3 = this.lin2log(n3), a3 = q ? v.getMinorTickInterval() : p.tickInterval, a3 = G("auto" === a3 ? null : a3, this.minorAutoInterval, p.tickPixelInterval / (q ? 5 : 1) * (n3 - f2) / ((q ? E / v.tickPositions.length : E) || 1)), a3 = D(a3, void 0, y(a3)), t = v.getLinearTickPositions(a3, f2, n3).map(this.log2lin), q || (this.minorAutoInterval = a3 / 5);
            q || (v.tickInterval = a3);
            return t;
          };
          a2.prototype.lin2log = function(a3) {
            return Math.pow(10, a3);
          };
          a2.prototype.log2lin = function(a3) {
            return Math.log(a3) / Math.LN10;
          };
          return a2;
        })();
        a = (function() {
          function a2() {
          }
          a2.compose = function(a3) {
            a3.keepProps.push("logarithmic");
            var f2 = a3.prototype, H = C.prototype;
            f2.log2lin = H.log2lin;
            f2.lin2log = H.lin2log;
            n2(a3, "init", function(a4) {
              var f3 = this.logarithmic;
              "logarithmic" !== a4.userOptions.type ? this.logarithmic = void 0 : (f3 || (f3 = this.logarithmic = new C(this)), this.log2lin !== f3.log2lin && (f3.log2lin = this.log2lin.bind(this)), this.lin2log !== f3.lin2log && (f3.lin2log = this.lin2log.bind(this)));
            });
            n2(a3, "afterInit", function() {
              var a4 = this.logarithmic;
              a4 && (this.lin2val = function(f3) {
                return a4.lin2log(f3);
              }, this.val2lin = function(f3) {
                return a4.log2lin(f3);
              });
            });
          };
          return a2;
        })();
        a.compose(f);
        return a;
      });
      O(n, "Core/Axis/PlotLineOrBand.js", [n["Core/Axis/Axis.js"], n["Core/Globals.js"], n["Core/Utilities.js"]], function(f, a, n2) {
        var y = n2.arrayMax, D = n2.arrayMin, G = n2.defined, C = n2.destroyObjectProperties, J = n2.erase, H = n2.extend, v = n2.merge, L = n2.objectEach, q = n2.pick, K = (function() {
          function f2(a2, f3) {
            this.axis = a2;
            f3 && (this.options = f3, this.id = f3.id);
          }
          f2.prototype.render = function() {
            a.fireEvent(this, "render");
            var f3 = this, t = f3.axis, I = t.horiz, u = t.logarithmic, m = f3.options, h = m.label, l = f3.label, k = m.to, g = m.from, d = m.value, x = G(g) && G(k), r = G(d), A = f3.svgElem, N = !A, B = [], M = m.color, R = q(m.zIndex, 0), F = m.events;
            B = { "class": "highcharts-plot-" + (x ? "band " : "line ") + (m.className || "") };
            var e = {}, c = t.chart.renderer, b = x ? "bands" : "lines";
            u && (g = u.log2lin(g), k = u.log2lin(k), d = u.log2lin(d));
            t.chart.styledMode || (r ? (B.stroke = M || "#999999", B["stroke-width"] = q(m.width, 1), m.dashStyle && (B.dashstyle = m.dashStyle)) : x && (B.fill = M || "#e6ebf5", m.borderWidth && (B.stroke = m.borderColor, B["stroke-width"] = m.borderWidth)));
            e.zIndex = R;
            b += "-" + R;
            (u = t.plotLinesAndBandsGroups[b]) || (t.plotLinesAndBandsGroups[b] = u = c.g("plot-" + b).attr(e).add());
            N && (f3.svgElem = A = c.path().attr(B).add(u));
            if (r) B = t.getPlotLinePath({ value: d, lineWidth: A.strokeWidth(), acrossPanes: m.acrossPanes });
            else if (x) B = t.getPlotBandPath(
              g,
              k,
              m
            );
            else return;
            !f3.eventsAdded && F && (L(F, function(b2, c2) {
              A.on(c2, function(b3) {
                F[c2].apply(f3, [b3]);
              });
            }), f3.eventsAdded = true);
            (N || !A.d) && B && B.length ? A.attr({ d: B }) : A && (B ? (A.show(true), A.animate({ d: B })) : A.d && (A.hide(), l && (f3.label = l = l.destroy())));
            h && (G(h.text) || G(h.formatter)) && B && B.length && 0 < t.width && 0 < t.height && !B.isFlat ? (h = v({ align: I && x && "center", x: I ? !x && 4 : 10, verticalAlign: !I && x && "middle", y: I ? x ? 16 : 10 : x ? 6 : -4, rotation: I && !x && 90 }, h), this.renderLabel(h, B, x, R)) : l && l.hide();
            return f3;
          };
          f2.prototype.renderLabel = function(a2, f3, q2, u) {
            var m = this.label, h = this.axis.chart.renderer;
            m || (m = { align: a2.textAlign || a2.align, rotation: a2.rotation, "class": "highcharts-plot-" + (q2 ? "band" : "line") + "-label " + (a2.className || "") }, m.zIndex = u, u = this.getLabelText(a2), this.label = m = h.text(u, 0, 0, a2.useHTML).attr(m).add(), this.axis.chart.styledMode || m.css(a2.style));
            h = f3.xBounds || [f3[0][1], f3[1][1], q2 ? f3[2][1] : f3[0][1]];
            f3 = f3.yBounds || [f3[0][2], f3[1][2], q2 ? f3[2][2] : f3[0][2]];
            q2 = D(h);
            u = D(f3);
            m.align(a2, false, { x: q2, y: u, width: y(h) - q2, height: y(f3) - u });
            m.show(true);
          };
          f2.prototype.getLabelText = function(a2) {
            return G(a2.formatter) ? a2.formatter.call(this) : a2.text;
          };
          f2.prototype.destroy = function() {
            J(this.axis.plotLinesAndBands, this);
            delete this.axis;
            C(this);
          };
          return f2;
        })();
        H(f.prototype, { getPlotBandPath: function(a2, f2) {
          var p = this.getPlotLinePath({ value: f2, force: true, acrossPanes: this.options.acrossPanes }), q2 = this.getPlotLinePath({ value: a2, force: true, acrossPanes: this.options.acrossPanes }), u = [], m = this.horiz, h = 1;
          a2 = a2 < this.min && f2 < this.min || a2 > this.max && f2 > this.max;
          if (q2 && p) {
            if (a2) {
              var l = q2.toString() === p.toString();
              h = 0;
            }
            for (a2 = 0; a2 < q2.length; a2 += 2) {
              f2 = q2[a2];
              var k = q2[a2 + 1], g = p[a2], d = p[a2 + 1];
              "M" !== f2[0] && "L" !== f2[0] || "M" !== k[0] && "L" !== k[0] || "M" !== g[0] && "L" !== g[0] || "M" !== d[0] && "L" !== d[0] || (m && g[1] === f2[1] ? (g[1] += h, d[1] += h) : m || g[2] !== f2[2] || (g[2] += h, d[2] += h), u.push(["M", f2[1], f2[2]], ["L", k[1], k[2]], ["L", d[1], d[2]], ["L", g[1], g[2]], ["Z"]));
              u.isFlat = l;
            }
          }
          return u;
        }, addPlotBand: function(a2) {
          return this.addPlotBandOrLine(a2, "plotBands");
        }, addPlotLine: function(a2) {
          return this.addPlotBandOrLine(a2, "plotLines");
        }, addPlotBandOrLine: function(a2, f2) {
          var p = new K(this, a2).render(), q2 = this.userOptions;
          if (p) {
            if (f2) {
              var u = q2[f2] || [];
              u.push(a2);
              q2[f2] = u;
            }
            this.plotLinesAndBands.push(p);
            this._addedPlotLB = true;
          }
          return p;
        }, removePlotBandOrLine: function(a2) {
          for (var f2 = this.plotLinesAndBands, t = this.options, q2 = this.userOptions, u = f2.length; u--; ) f2[u].id === a2 && f2[u].destroy();
          [t.plotLines || [], q2.plotLines || [], t.plotBands || [], q2.plotBands || []].forEach(function(f3) {
            for (u = f3.length; u--; ) (f3[u] || {}).id === a2 && J(f3, f3[u]);
          });
        }, removePlotBand: function(a2) {
          this.removePlotBandOrLine(a2);
        }, removePlotLine: function(a2) {
          this.removePlotBandOrLine(a2);
        } });
        a.PlotLineOrBand = K;
        return a.PlotLineOrBand;
      });
      O(n, "Core/Tooltip.js", [n["Core/Globals.js"], n["Core/Utilities.js"]], function(f, a) {
        var n2 = f.doc, y = a.clamp, D = a.css, G = a.defined, C = a.discardElement, J = a.extend, H = a.fireEvent, v = a.format, L = a.isNumber, q = a.isString, K = a.merge, E = a.pick, p = a.splat, t = a.syncTimeout, I = a.timeUnits;
        "";
        var u = (function() {
          function m(h, a2) {
            this.container = void 0;
            this.crosshairs = [];
            this.distance = 0;
            this.isHidden = true;
            this.isSticky = false;
            this.now = {};
            this.options = {};
            this.outside = false;
            this.chart = h;
            this.init(
              h,
              a2
            );
          }
          m.prototype.applyFilter = function() {
            var h = this.chart;
            h.renderer.definition({ tagName: "filter", id: "drop-shadow-" + h.index, opacity: 0.5, children: [{ tagName: "feGaussianBlur", "in": "SourceAlpha", stdDeviation: 1 }, { tagName: "feOffset", dx: 1, dy: 1 }, { tagName: "feComponentTransfer", children: [{ tagName: "feFuncA", type: "linear", slope: 0.3 }] }, { tagName: "feMerge", children: [{ tagName: "feMergeNode" }, { tagName: "feMergeNode", "in": "SourceGraphic" }] }] });
            h.renderer.definition({ tagName: "style", textContent: ".highcharts-tooltip-" + h.index + "{filter:url(#drop-shadow-" + h.index + ")}" });
          };
          m.prototype.bodyFormatter = function(h) {
            return h.map(function(h2) {
              var k = h2.series.tooltipOptions;
              return (k[(h2.point.formatPrefix || "point") + "Formatter"] || h2.point.tooltipFormatter).call(h2.point, k[(h2.point.formatPrefix || "point") + "Format"] || "");
            });
          };
          m.prototype.cleanSplit = function(h) {
            this.chart.series.forEach(function(a2) {
              var k = a2 && a2.tt;
              k && (!k.isActive || h ? a2.tt = k.destroy() : k.isActive = false);
            });
          };
          m.prototype.defaultFormatter = function(h) {
            var a2 = this.points || p(this);
            var k = [h.tooltipFooterHeaderFormatter(a2[0])];
            k = k.concat(h.bodyFormatter(a2));
            k.push(h.tooltipFooterHeaderFormatter(a2[0], true));
            return k;
          };
          m.prototype.destroy = function() {
            this.label && (this.label = this.label.destroy());
            this.split && this.tt && (this.cleanSplit(this.chart, true), this.tt = this.tt.destroy());
            this.renderer && (this.renderer = this.renderer.destroy(), C(this.container));
            a.clearTimeout(this.hideTimer);
            a.clearTimeout(this.tooltipTimeout);
          };
          m.prototype.getAnchor = function(h, a2) {
            var k = this.chart, g = k.pointer, d = k.inverted, l = k.plotTop, f2 = k.plotLeft, m2 = 0, t2 = 0, B, M;
            h = p(h);
            this.followPointer && a2 ? ("undefined" === typeof a2.chartX && (a2 = g.normalize(a2)), h = [a2.chartX - f2, a2.chartY - l]) : h[0].tooltipPos ? h = h[0].tooltipPos : (h.forEach(function(g2) {
              B = g2.series.yAxis;
              M = g2.series.xAxis;
              m2 += g2.plotX + (!d && M ? M.left - f2 : 0);
              t2 += (g2.plotLow ? (g2.plotLow + g2.plotHigh) / 2 : g2.plotY) + (!d && B ? B.top - l : 0);
            }), m2 /= h.length, t2 /= h.length, h = [d ? k.plotWidth - t2 : m2, this.shared && !d && 1 < h.length && a2 ? a2.chartY - l : d ? k.plotHeight - m2 : t2]);
            return h.map(Math.round);
          };
          m.prototype.getDateFormat = function(h, a2, k, g) {
            var d = this.chart.time, l = d.dateFormat(
              "%m-%d %H:%M:%S.%L",
              a2
            ), f2 = { millisecond: 15, second: 12, minute: 9, hour: 6, day: 3 }, m2 = "millisecond";
            for (p2 in I) {
              if (h === I.week && +d.dateFormat("%w", a2) === k && "00:00:00.000" === l.substr(6)) {
                var p2 = "week";
                break;
              }
              if (I[p2] > h) {
                p2 = m2;
                break;
              }
              if (f2[p2] && l.substr(f2[p2]) !== "01-01 00:00:00.000".substr(f2[p2])) break;
              "week" !== p2 && (m2 = p2);
            }
            if (p2) var B = d.resolveDTLFormat(g[p2]).main;
            return B;
          };
          m.prototype.getLabel = function() {
            var h, a2, k = this, g = this.chart.renderer, d = this.chart.styledMode, m2 = this.options, r = "tooltip" + (G(m2.className) ? " " + m2.className : ""), p2 = (null === (h = m2.style) || void 0 === h ? void 0 : h.pointerEvents) || (!this.followPointer && m2.stickOnContact ? "auto" : "none"), t2;
            h = function() {
              k.inContact = true;
            };
            var B = function() {
              var e = k.chart.hoverSeries;
              k.inContact = false;
              if (e && e.onMouseOut) e.onMouseOut();
            };
            if (!this.label) {
              this.outside && (this.container = t2 = f.doc.createElement("div"), t2.className = "highcharts-tooltip-container", D(t2, { position: "absolute", top: "1px", pointerEvents: p2, zIndex: 3 }), f.doc.body.appendChild(t2), this.renderer = g = new f.Renderer(t2, 0, 0, null === (a2 = this.chart.options.chart) || void 0 === a2 ? void 0 : a2.style, void 0, void 0, g.styledMode));
              this.split ? this.label = g.g(r) : (this.label = g.label("", 0, 0, m2.shape || "callout", null, null, m2.useHTML, null, r).attr({ padding: m2.padding, r: m2.borderRadius }), d || this.label.attr({ fill: m2.backgroundColor, "stroke-width": m2.borderWidth }).css(m2.style).css({ pointerEvents: p2 }).shadow(m2.shadow));
              d && (this.applyFilter(), this.label.addClass("highcharts-tooltip-" + this.chart.index));
              if (k.outside && !k.split) {
                var M = this.label, q2 = M.xSetter, F = M.ySetter;
                M.xSetter = function(e) {
                  q2.call(M, k.distance);
                  t2.style.left = e + "px";
                };
                M.ySetter = function(e) {
                  F.call(M, k.distance);
                  t2.style.top = e + "px";
                };
              }
              this.label.on("mouseenter", h).on("mouseleave", B).attr({ zIndex: 8 }).add();
            }
            return this.label;
          };
          m.prototype.getPosition = function(a2, l, k) {
            var g = this.chart, d = this.distance, h = {}, f2 = g.inverted && k.h || 0, m2, p2 = this.outside, B = p2 ? n2.documentElement.clientWidth - 2 * d : g.chartWidth, M = p2 ? Math.max(n2.body.scrollHeight, n2.documentElement.scrollHeight, n2.body.offsetHeight, n2.documentElement.offsetHeight, n2.documentElement.clientHeight) : g.chartHeight, t2 = g.pointer.getChartPosition(), F = g.containerScaling, e = function(b2) {
              return F ? b2 * F.scaleX : b2;
            }, c = function(b2) {
              return F ? b2 * F.scaleY : b2;
            }, b = function(b2) {
              var h2 = "x" === b2;
              return [b2, h2 ? B : M, h2 ? a2 : l].concat(p2 ? [h2 ? e(a2) : c(l), h2 ? t2.left - d + e(k.plotX + g.plotLeft) : t2.top - d + c(k.plotY + g.plotTop), 0, h2 ? B : M] : [h2 ? a2 : l, h2 ? k.plotX + g.plotLeft : k.plotY + g.plotTop, h2 ? g.plotLeft : g.plotTop, h2 ? g.plotLeft + g.plotWidth : g.plotTop + g.plotHeight]);
            }, z = b("y"), w = b("x"), q2 = !this.followPointer && E(k.ttBelow, !g.inverted === !!k.negative), u2 = function(b2, g2, k2, a3, l2, m3, r) {
              var x = "y" === b2 ? c(d) : e(d), w2 = (k2 - a3) / 2, p3 = a3 < l2 - d, B2 = l2 + d + a3 < g2, z2 = l2 - x - k2 + w2;
              l2 = l2 + x - w2;
              if (q2 && B2) h[b2] = l2;
              else if (!q2 && p3) h[b2] = z2;
              else if (p3) h[b2] = Math.min(r - a3, 0 > z2 - f2 ? z2 : z2 - f2);
              else if (B2) h[b2] = Math.max(m3, l2 + f2 + k2 > g2 ? l2 : l2 + f2);
              else return false;
            }, v2 = function(b2, c2, e2, g2, k2) {
              var a3;
              k2 < d || k2 > c2 - d ? a3 = false : h[b2] = k2 < e2 / 2 ? 1 : k2 > c2 - g2 / 2 ? c2 - g2 - 2 : k2 - e2 / 2;
              return a3;
            }, I2 = function(b2) {
              var c2 = z;
              z = w;
              w = c2;
              m2 = b2;
            }, H2 = function() {
              false !== u2.apply(0, z) ? false !== v2.apply(0, w) || m2 || (I2(true), H2()) : m2 ? h.x = h.y = 0 : (I2(true), H2());
            };
            (g.inverted || 1 < this.len) && I2();
            H2();
            return h;
          };
          m.prototype.getXDateFormat = function(a2, l, k) {
            l = l.dateTimeLabelFormats;
            var g = k && k.closestPointRange;
            return (g ? this.getDateFormat(g, a2.x, k.options.startOfWeek, l) : l.day) || l.year;
          };
          m.prototype.hide = function(h) {
            var l = this;
            a.clearTimeout(this.hideTimer);
            h = E(h, this.options.hideDelay, 500);
            this.isHidden || (this.hideTimer = t(function() {
              l.getLabel().fadeOut(h ? void 0 : h);
              l.isHidden = true;
            }, h));
          };
          m.prototype.init = function(a2, l) {
            this.chart = a2;
            this.options = l;
            this.crosshairs = [];
            this.now = { x: 0, y: 0 };
            this.isHidden = true;
            this.split = l.split && !a2.inverted && !a2.polar;
            this.shared = l.shared || this.split;
            this.outside = E(l.outside, !(!a2.scrollablePixelsX && !a2.scrollablePixelsY));
          };
          m.prototype.isStickyOnContact = function() {
            return !(this.followPointer || !this.options.stickOnContact || !this.inContact);
          };
          m.prototype.move = function(h, l, k, g) {
            var d = this, f2 = d.now, m2 = false !== d.options.animation && !d.isHidden && (1 < Math.abs(h - f2.x) || 1 < Math.abs(l - f2.y)), p2 = d.followPointer || 1 < d.len;
            J(f2, { x: m2 ? (2 * f2.x + h) / 3 : h, y: m2 ? (f2.y + l) / 2 : l, anchorX: p2 ? void 0 : m2 ? (2 * f2.anchorX + k) / 3 : k, anchorY: p2 ? void 0 : m2 ? (f2.anchorY + g) / 2 : g });
            d.getLabel().attr(f2);
            d.drawTracker();
            m2 && (a.clearTimeout(this.tooltipTimeout), this.tooltipTimeout = setTimeout(function() {
              d && d.move(h, l, k, g);
            }, 32));
          };
          m.prototype.refresh = function(h, l) {
            var k = this.chart, g = this.options, d = h, f2 = {}, m2 = [], A = g.formatter || this.defaultFormatter;
            f2 = this.shared;
            var t2 = k.styledMode;
            if (g.enabled) {
              a.clearTimeout(this.hideTimer);
              this.followPointer = p(d)[0].series.tooltipOptions.followPointer;
              var B = this.getAnchor(d, l);
              l = B[0];
              var M = B[1];
              !f2 || d.series && d.series.noSharedTooltip ? f2 = d.getLabelConfig() : (k.pointer.applyInactiveState(d), d.forEach(function(d2) {
                d2.setState("hover");
                m2.push(d2.getLabelConfig());
              }), f2 = { x: d[0].category, y: d[0].y }, f2.points = m2, d = d[0]);
              this.len = m2.length;
              k = A.call(f2, this);
              A = d.series;
              this.distance = E(A.tooltipOptions.distance, 16);
              false === k ? this.hide() : (this.split ? this.renderSplit(k, p(h)) : (h = this.getLabel(), g.style.width && !t2 || h.css({ width: this.chart.spacingBox.width + "px" }), h.attr({ text: k && k.join ? k.join("") : k }), h.removeClass(/highcharts-color-[\d]+/g).addClass("highcharts-color-" + E(d.colorIndex, A.colorIndex)), t2 || h.attr({ stroke: g.borderColor || d.color || A.color || "#666666" }), this.updatePosition({ plotX: l, plotY: M, negative: d.negative, ttBelow: d.ttBelow, h: B[2] || 0 })), this.isHidden && this.label && this.label.attr({ opacity: 1 }).show(), this.isHidden = false);
              H(this, "refresh");
            }
          };
          m.prototype.renderSplit = function(a2, l) {
            function k(b2, c2, e2, d2, g2) {
              void 0 === g2 && (g2 = true);
              e2 ? (c2 = H2 ? 0 : G2, b2 = y(b2 - d2 / 2, n3.left, n3.right - d2)) : (c2 -= K2, b2 = g2 ? b2 - d2 - z : b2 + z, b2 = y(b2, g2 ? b2 : n3.left, n3.right));
              return { x: b2, y: c2 };
            }
            var g = this, d = g.chart, h = g.chart, m2 = h.plotHeight, p2 = h.plotLeft, t2 = h.plotTop, B = h.pointer, M = h.renderer, u2 = h.scrollablePixelsY, F = void 0 === u2 ? 0 : u2;
            u2 = h.scrollingContainer;
            u2 = void 0 === u2 ? { scrollLeft: 0, scrollTop: 0 } : u2;
            var e = u2.scrollLeft, c = u2.scrollTop, b = h.styledMode, z = g.distance, w = g.options, v2 = g.options.positioner, n3 = { left: e, right: e + h.chartWidth, top: c, bottom: c + h.chartHeight }, I2 = g.getLabel(), H2 = !(!d.xAxis[0] || !d.xAxis[0].opposite), K2 = t2 + c, C2 = 0, G2 = m2 - F;
            q(a2) && (a2 = [false, a2]);
            a2 = a2.slice(0, l.length + 1).reduce(function(e2, d2, a3) {
              if (false !== d2 && "" !== d2) {
                a3 = l[a3 - 1] || { isHeader: true, plotX: l[0].plotX, plotY: m2, series: {} };
                var h2 = a3.isHeader, f2 = h2 ? g : a3.series, r = f2.tt, x = a3.isHeader;
                var B2 = a3.series;
                var A = "highcharts-color-" + E(a3.colorIndex, B2.colorIndex, "none");
                r || (r = { padding: w.padding, r: w.borderRadius }, b || (r.fill = w.backgroundColor, r["stroke-width"] = w.borderWidth), r = M.label("", 0, 0, w[x ? "headerShape" : "shape"] || "callout", void 0, void 0, w.useHTML).addClass((x ? "highcharts-tooltip-header " : "") + "highcharts-tooltip-box " + A).attr(r).add(I2));
                r.isActive = true;
                r.attr({ text: d2 });
                b || r.css(w.style).shadow(w.shadow).attr({ stroke: w.borderColor || a3.color || B2.color || "#333333" });
                d2 = f2.tt = r;
                x = d2.getBBox();
                f2 = x.width + d2.strokeWidth();
                h2 && (C2 = x.height, G2 += C2, H2 && (K2 -= C2));
                B2 = a3.plotX;
                B2 = void 0 === B2 ? 0 : B2;
                A = a3.plotY;
                A = void 0 === A ? 0 : A;
                var Q = a3.series;
                if (a3.isHeader) {
                  B2 = p2 + B2;
                  var u3 = t2 + m2 / 2;
                } else r = Q.xAxis, Q = Q.yAxis, B2 = r.pos + y(B2, -z, r.len + z), Q.pos + A >= c + t2 && Q.pos + A <= c + t2 + m2 - F && (u3 = Q.pos + A);
                B2 = y(B2, n3.left - z, n3.right + z);
                "number" === typeof u3 ? (x = x.height + 1, A = v2 ? v2.call(g, f2, x, a3) : k(B2, u3, h2, f2), e2.push({ align: v2 ? 0 : void 0, anchorX: B2, anchorY: u3, boxWidth: f2, point: a3, rank: E(A.rank, h2 ? 1 : 0), size: x, target: A.y, tt: d2, x: A.x })) : d2.isActive = false;
              }
              return e2;
            }, []);
            !v2 && a2.some(function(b2) {
              return b2.x < n3.left;
            }) && (a2 = a2.map(function(b2) {
              var c2 = k(b2.anchorX, b2.anchorY, b2.point.isHeader, b2.boxWidth, false);
              return J(b2, { target: c2.y, x: c2.x });
            }));
            g.cleanSplit();
            f.distribute(a2, G2);
            a2.forEach(function(b2) {
              var c2 = b2.pos;
              b2.tt.attr({ visibility: "undefined" === typeof c2 ? "hidden" : "inherit", x: b2.x, y: c2 + K2, anchorX: b2.anchorX, anchorY: b2.anchorY });
            });
            a2 = g.container;
            d = g.renderer;
            g.outside && a2 && d && (h = I2.getBBox(), d.setSize(h.width + h.x, h.height + h.y, false), B = B.getChartPosition(), a2.style.left = B.left + "px", a2.style.top = B.top + "px");
          };
          m.prototype.drawTracker = function() {
            if (this.followPointer || !this.options.stickOnContact) this.tracker && this.tracker.destroy();
            else {
              var a2 = this.chart, l = this.label, k = a2.hoverPoint;
              if (l && k) {
                var g = { x: 0, y: 0, width: 0, height: 0 };
                k = this.getAnchor(k);
                var d = l.getBBox();
                k[0] += a2.plotLeft - l.translateX;
                k[1] += a2.plotTop - l.translateY;
                g.x = Math.min(0, k[0]);
                g.y = Math.min(0, k[1]);
                g.width = 0 > k[0] ? Math.max(Math.abs(k[0]), d.width - k[0]) : Math.max(Math.abs(k[0]), d.width);
                g.height = 0 > k[1] ? Math.max(Math.abs(k[1]), d.height - Math.abs(k[1])) : Math.max(Math.abs(k[1]), d.height);
                this.tracker ? this.tracker.attr(g) : (this.tracker = l.renderer.rect(g).addClass("highcharts-tracker").add(l), a2.styledMode || this.tracker.attr({ fill: "rgba(0,0,0,0)" }));
              }
            }
          };
          m.prototype.styledModeFormat = function(a2) {
            return a2.replace('style="font-size: 10px"', 'class="highcharts-header"').replace(/style="color:{(point|series)\.color}"/g, 'class="highcharts-color-{$1.colorIndex}"');
          };
          m.prototype.tooltipFooterHeaderFormatter = function(a2, l) {
            var k = l ? "footer" : "header", g = a2.series, d = g.tooltipOptions, h = d.xDateFormat, f2 = g.xAxis, m2 = f2 && "datetime" === f2.options.type && L(a2.key), p2 = d[k + "Format"];
            l = { isFooter: l, labelConfig: a2 };
            H(this, "headerFormatter", l, function(k2) {
              m2 && !h && (h = this.getXDateFormat(a2, d, f2));
              m2 && h && (a2.point && a2.point.tooltipDateKeys || ["key"]).forEach(function(d2) {
                p2 = p2.replace("{point." + d2 + "}", "{point." + d2 + ":" + h + "}");
              });
              g.chart.styledMode && (p2 = this.styledModeFormat(p2));
              k2.text = v(p2, { point: a2, series: g }, this.chart);
            });
            return l.text;
          };
          m.prototype.update = function(a2) {
            this.destroy();
            K(true, this.chart.options.tooltip.userOptions, a2);
            this.init(this.chart, K(true, this.options, a2));
          };
          m.prototype.updatePosition = function(a2) {
            var h = this.chart, k = h.pointer, g = this.getLabel(), d = a2.plotX + h.plotLeft, f2 = a2.plotY + h.plotTop;
            k = k.getChartPosition();
            a2 = (this.options.positioner || this.getPosition).call(this, g.width, g.height, a2);
            if (this.outside) {
              var m2 = (this.options.borderWidth || 0) + 2 * this.distance;
              this.renderer.setSize(g.width + m2, g.height + m2, false);
              if (h = h.containerScaling) D(this.container, { transform: "scale(" + h.scaleX + ", " + h.scaleY + ")" }), d *= h.scaleX, f2 *= h.scaleY;
              d += k.left - a2.x;
              f2 += k.top - a2.y;
            }
            this.move(
              Math.round(a2.x),
              Math.round(a2.y || 0),
              d,
              f2
            );
          };
          return m;
        })();
        f.Tooltip = u;
        return f.Tooltip;
      });
      O(n, "Core/Pointer.js", [n["Core/Color.js"], n["Core/Globals.js"], n["Core/Tooltip.js"], n["Core/Utilities.js"]], function(f, a, n2, y) {
        var D = f.parse, G = a.charts, C = a.noop, J = y.addEvent, H = y.attr, v = y.css, L = y.defined, q = y.extend, K = y.find, E = y.fireEvent, p = y.isNumber, t = y.isObject, I = y.objectEach, u = y.offset, m = y.pick, h = y.splat;
        "";
        f = (function() {
          function l(a2, g) {
            this.lastValidTouch = {};
            this.pinchDown = [];
            this.runChartClick = false;
            this.chart = a2;
            this.hasDragged = false;
            this.options = g;
            this.unbindContainerMouseLeave = function() {
            };
            this.unbindContainerMouseEnter = function() {
            };
            this.init(a2, g);
          }
          l.prototype.applyInactiveState = function(a2) {
            var g = [], d;
            (a2 || []).forEach(function(a3) {
              d = a3.series;
              g.push(d);
              d.linkedParent && g.push(d.linkedParent);
              d.linkedSeries && (g = g.concat(d.linkedSeries));
              d.navigatorSeries && g.push(d.navigatorSeries);
            });
            this.chart.series.forEach(function(d2) {
              -1 === g.indexOf(d2) ? d2.setState("inactive", true) : d2.options.inactiveOtherPoints && d2.setAllPointsToState("inactive");
            });
          };
          l.prototype.destroy = function() {
            var k = this;
            "undefined" !== typeof k.unDocMouseMove && k.unDocMouseMove();
            this.unbindContainerMouseLeave();
            a.chartCount || (a.unbindDocumentMouseUp && (a.unbindDocumentMouseUp = a.unbindDocumentMouseUp()), a.unbindDocumentTouchEnd && (a.unbindDocumentTouchEnd = a.unbindDocumentTouchEnd()));
            clearInterval(k.tooltipTimeout);
            I(k, function(g, d) {
              k[d] = void 0;
            });
          };
          l.prototype.drag = function(a2) {
            var g = this.chart, d = g.options.chart, k = a2.chartX, h2 = a2.chartY, l2 = this.zoomHor, f2 = this.zoomVert, m2 = g.plotLeft, p2 = g.plotTop, u2 = g.plotWidth, F = g.plotHeight, e = this.selectionMarker, c = this.mouseDownX || 0, b = this.mouseDownY || 0, z = t(d.panning) ? d.panning && d.panning.enabled : d.panning, w = d.panKey && a2[d.panKey + "Key"];
            if (!e || !e.touch) {
              if (k < m2 ? k = m2 : k > m2 + u2 && (k = m2 + u2), h2 < p2 ? h2 = p2 : h2 > p2 + F && (h2 = p2 + F), this.hasDragged = Math.sqrt(Math.pow(c - k, 2) + Math.pow(b - h2, 2)), 10 < this.hasDragged) {
                var q2 = g.isInsidePlot(c - m2, b - p2);
                g.hasCartesianSeries && (this.zoomX || this.zoomY) && q2 && !w && !e && (this.selectionMarker = e = g.renderer.rect(m2, p2, l2 ? 1 : u2, f2 ? 1 : F, 0).attr({ "class": "highcharts-selection-marker", zIndex: 7 }).add(), g.styledMode || e.attr({ fill: d.selectionMarkerFill || D("#335cad").setOpacity(0.25).get() }));
                e && l2 && (k -= c, e.attr({ width: Math.abs(k), x: (0 < k ? 0 : k) + c }));
                e && f2 && (k = h2 - b, e.attr({ height: Math.abs(k), y: (0 < k ? 0 : k) + b }));
                q2 && !e && z && g.pan(a2, d.panning);
              }
            }
          };
          l.prototype.dragStart = function(a2) {
            var g = this.chart;
            g.mouseIsDown = a2.type;
            g.cancelClick = false;
            g.mouseDownX = this.mouseDownX = a2.chartX;
            g.mouseDownY = this.mouseDownY = a2.chartY;
          };
          l.prototype.drop = function(a2) {
            var g = this, d = this.chart, k = this.hasPinched;
            if (this.selectionMarker) {
              var h2 = { originalEvent: a2, xAxis: [], yAxis: [] }, l2 = this.selectionMarker, f2 = l2.attr ? l2.attr("x") : l2.x, m2 = l2.attr ? l2.attr("y") : l2.y, t2 = l2.attr ? l2.attr("width") : l2.width, u2 = l2.attr ? l2.attr("height") : l2.height, F;
              if (this.hasDragged || k) d.axes.forEach(function(e) {
                if (e.zoomEnabled && L(e.min) && (k || g[{ xAxis: "zoomX", yAxis: "zoomY" }[e.coll]]) && p(f2) && p(m2)) {
                  var c = e.horiz, b = "touchend" === a2.type ? e.minPixelPadding : 0, d2 = e.toValue((c ? f2 : m2) + b);
                  c = e.toValue((c ? f2 + t2 : m2 + u2) - b);
                  h2[e.coll].push({ axis: e, min: Math.min(d2, c), max: Math.max(d2, c) });
                  F = true;
                }
              }), F && E(d, "selection", h2, function(e) {
                d.zoom(q(e, k ? { animation: false } : null));
              });
              p(d.index) && (this.selectionMarker = this.selectionMarker.destroy());
              k && this.scaleGroups();
            }
            d && p(d.index) && (v(d.container, { cursor: d._cursor }), d.cancelClick = 10 < this.hasDragged, d.mouseIsDown = this.hasDragged = this.hasPinched = false, this.pinchDown = []);
          };
          l.prototype.findNearestKDPoint = function(a2, g, d) {
            var k = this.chart, h2 = k.hoverPoint;
            k = k.tooltip;
            if (h2 && k && k.isStickyOnContact()) return h2;
            var l2;
            a2.forEach(function(a3) {
              var k2 = !(a3.noSharedTooltip && g) && 0 > a3.options.findNearestPointBy.indexOf("y");
              a3 = a3.searchPoint(d, k2);
              if ((k2 = t(a3, true)) && !(k2 = !t(l2, true))) {
                k2 = l2.distX - a3.distX;
                var h3 = l2.dist - a3.dist, f2 = (a3.series.group && a3.series.group.zIndex) - (l2.series.group && l2.series.group.zIndex);
                k2 = 0 < (0 !== k2 && g ? k2 : 0 !== h3 ? h3 : 0 !== f2 ? f2 : l2.series.index > a3.series.index ? -1 : 1);
              }
              k2 && (l2 = a3);
            });
            return l2;
          };
          l.prototype.getChartCoordinatesFromPoint = function(a2, g) {
            var d = a2.series, k = d.xAxis;
            d = d.yAxis;
            var h2 = m(a2.clientX, a2.plotX), l2 = a2.shapeArgs;
            if (k && d) return g ? { chartX: k.len + k.pos - h2, chartY: d.len + d.pos - a2.plotY } : { chartX: h2 + k.pos, chartY: a2.plotY + d.pos };
            if (l2 && l2.x && l2.y) return {
              chartX: l2.x,
              chartY: l2.y
            };
          };
          l.prototype.getChartPosition = function() {
            return this.chartPosition || (this.chartPosition = u(this.chart.container));
          };
          l.prototype.getCoordinates = function(a2) {
            var g = { xAxis: [], yAxis: [] };
            this.chart.axes.forEach(function(d) {
              g[d.isXAxis ? "xAxis" : "yAxis"].push({ axis: d, value: d.toValue(a2[d.horiz ? "chartX" : "chartY"]) });
            });
            return g;
          };
          l.prototype.getHoverData = function(a2, g, d, h2, l2, f2) {
            var k, r = [];
            h2 = !(!h2 || !a2);
            var p2 = g && !g.stickyTracking, x = { chartX: f2 ? f2.chartX : void 0, chartY: f2 ? f2.chartY : void 0, shared: l2 };
            E(
              this,
              "beforeGetHoverData",
              x
            );
            p2 = p2 ? [g] : d.filter(function(d2) {
              return x.filter ? x.filter(d2) : d2.visible && !(!l2 && d2.directTouch) && m(d2.options.enableMouseTracking, true) && d2.stickyTracking;
            });
            g = (k = h2 || !f2 ? a2 : this.findNearestKDPoint(p2, l2, f2)) && k.series;
            k && (l2 && !g.noSharedTooltip ? (p2 = d.filter(function(d2) {
              return x.filter ? x.filter(d2) : d2.visible && !(!l2 && d2.directTouch) && m(d2.options.enableMouseTracking, true) && !d2.noSharedTooltip;
            }), p2.forEach(function(d2) {
              var e = K(d2.points, function(c) {
                return c.x === k.x && !c.isNull;
              });
              t(e) && (d2.chart.isBoosting && (e = d2.getPoint(e)), r.push(e));
            })) : r.push(k));
            x = { hoverPoint: k };
            E(this, "afterGetHoverData", x);
            return { hoverPoint: x.hoverPoint, hoverSeries: g, hoverPoints: r };
          };
          l.prototype.getPointFromEvent = function(a2) {
            a2 = a2.target;
            for (var g; a2 && !g; ) g = a2.point, a2 = a2.parentNode;
            return g;
          };
          l.prototype.onTrackerMouseOut = function(a2) {
            a2 = a2.relatedTarget || a2.toElement;
            var g = this.chart.hoverSeries;
            this.isDirectTouch = false;
            if (!(!g || !a2 || g.stickyTracking || this.inClass(a2, "highcharts-tooltip") || this.inClass(a2, "highcharts-series-" + g.index) && this.inClass(a2, "highcharts-tracker"))) g.onMouseOut();
          };
          l.prototype.inClass = function(a2, g) {
            for (var d; a2; ) {
              if (d = H(a2, "class")) {
                if (-1 !== d.indexOf(g)) return true;
                if (-1 !== d.indexOf("highcharts-container")) return false;
              }
              a2 = a2.parentNode;
            }
          };
          l.prototype.init = function(a2, g) {
            this.options = g;
            this.chart = a2;
            this.runChartClick = g.chart.events && !!g.chart.events.click;
            this.pinchDown = [];
            this.lastValidTouch = {};
            n2 && (a2.tooltip = new n2(a2, g.tooltip), this.followTouchMove = m(g.tooltip.followTouchMove, true));
            this.setDOMEvents();
          };
          l.prototype.normalize = function(a2, g) {
            var d = a2.touches, k = d ? d.length ? d.item(0) : m(d.changedTouches, a2.changedTouches)[0] : a2;
            g || (g = this.getChartPosition());
            d = k.pageX - g.left;
            g = k.pageY - g.top;
            if (k = this.chart.containerScaling) d /= k.scaleX, g /= k.scaleY;
            return q(a2, { chartX: Math.round(d), chartY: Math.round(g) });
          };
          l.prototype.onContainerClick = function(a2) {
            var g = this.chart, d = g.hoverPoint;
            a2 = this.normalize(a2);
            var k = g.plotLeft, h2 = g.plotTop;
            g.cancelClick || (d && this.inClass(a2.target, "highcharts-tracker") ? (E(d.series, "click", q(a2, { point: d })), g.hoverPoint && d.firePointEvent("click", a2)) : (q(a2, this.getCoordinates(a2)), g.isInsidePlot(a2.chartX - k, a2.chartY - h2) && E(g, "click", a2)));
          };
          l.prototype.onContainerMouseDown = function(k) {
            var g = 1 === ((k.buttons || k.button) & 1);
            k = this.normalize(k);
            if (a.isFirefox && 0 !== k.button) this.onContainerMouseMove(k);
            if ("undefined" === typeof k.button || g) this.zoomOption(k), g && k.preventDefault && k.preventDefault(), this.dragStart(k);
          };
          l.prototype.onContainerMouseLeave = function(k) {
            var g = G[m(a.hoverChartIndex, -1)], d = this.chart.tooltip;
            k = this.normalize(k);
            g && (k.relatedTarget || k.toElement) && (g.pointer.reset(), g.pointer.chartPosition = void 0);
            d && !d.isHidden && this.reset();
          };
          l.prototype.onContainerMouseEnter = function(a2) {
            delete this.chartPosition;
          };
          l.prototype.onContainerMouseMove = function(a2) {
            var g = this.chart;
            a2 = this.normalize(a2);
            this.setHoverChartIndex();
            a2.preventDefault || (a2.returnValue = false);
            "mousedown" === g.mouseIsDown && this.drag(a2);
            g.openMenu || !this.inClass(a2.target, "highcharts-tracker") && !g.isInsidePlot(a2.chartX - g.plotLeft, a2.chartY - g.plotTop) || this.runPointActions(a2);
          };
          l.prototype.onDocumentTouchEnd = function(k) {
            G[a.hoverChartIndex] && G[a.hoverChartIndex].pointer.drop(k);
          };
          l.prototype.onContainerTouchMove = function(a2) {
            this.touch(a2);
          };
          l.prototype.onContainerTouchStart = function(a2) {
            this.zoomOption(a2);
            this.touch(a2, true);
          };
          l.prototype.onDocumentMouseMove = function(a2) {
            var g = this.chart, d = this.chartPosition;
            a2 = this.normalize(a2, d);
            var h2 = g.tooltip;
            !d || h2 && h2.isStickyOnContact() || g.isInsidePlot(a2.chartX - g.plotLeft, a2.chartY - g.plotTop) || this.inClass(a2.target, "highcharts-tracker") || this.reset();
          };
          l.prototype.onDocumentMouseUp = function(h2) {
            var g = G[m(
              a.hoverChartIndex,
              -1
            )];
            g && g.pointer.drop(h2);
          };
          l.prototype.pinch = function(a2) {
            var g = this, d = g.chart, h2 = g.pinchDown, k = a2.touches || [], l2 = k.length, f2 = g.lastValidTouch, p2 = g.hasZoom, t2 = g.selectionMarker, u2 = {}, F = 1 === l2 && (g.inClass(a2.target, "highcharts-tracker") && d.runTrackerClick || g.runChartClick), e = {};
            1 < l2 && (g.initiated = true);
            p2 && g.initiated && !F && a2.preventDefault();
            [].map.call(k, function(c) {
              return g.normalize(c);
            });
            "touchstart" === a2.type ? ([].forEach.call(k, function(c, b) {
              h2[b] = { chartX: c.chartX, chartY: c.chartY };
            }), f2.x = [h2[0].chartX, h2[1] && h2[1].chartX], f2.y = [h2[0].chartY, h2[1] && h2[1].chartY], d.axes.forEach(function(c) {
              if (c.zoomEnabled) {
                var b = d.bounds[c.horiz ? "h" : "v"], e2 = c.minPixelPadding, g2 = c.toPixels(Math.min(m(c.options.min, c.dataMin), c.dataMin)), a3 = c.toPixels(Math.max(m(c.options.max, c.dataMax), c.dataMax)), h3 = Math.max(g2, a3);
                b.min = Math.min(c.pos, Math.min(g2, a3) - e2);
                b.max = Math.max(c.pos + c.len, h3 + e2);
              }
            }), g.res = true) : g.followTouchMove && 1 === l2 ? this.runPointActions(g.normalize(a2)) : h2.length && (t2 || (g.selectionMarker = t2 = q({ destroy: C, touch: true }, d.plotBox)), g.pinchTranslate(
              h2,
              k,
              u2,
              t2,
              e,
              f2
            ), g.hasPinched = p2, g.scaleGroups(u2, e), g.res && (g.res = false, this.reset(false, 0)));
          };
          l.prototype.pinchTranslate = function(a2, g, d, h2, l2, f2) {
            this.zoomHor && this.pinchTranslateDirection(true, a2, g, d, h2, l2, f2);
            this.zoomVert && this.pinchTranslateDirection(false, a2, g, d, h2, l2, f2);
          };
          l.prototype.pinchTranslateDirection = function(a2, g, d, h2, l2, f2, m2, p2) {
            var k = this.chart, r = a2 ? "x" : "y", x = a2 ? "X" : "Y", e = "chart" + x, c = a2 ? "width" : "height", b = k["plot" + (a2 ? "Left" : "Top")], B, w, t2 = p2 || 1, A = k.inverted, u2 = k.bounds[a2 ? "h" : "v"], q2 = 1 === g.length, v2 = g[0][e], n3 = d[0][e], I2 = !q2 && g[1][e], N = !q2 && d[1][e];
            d = function() {
              "number" === typeof N && 20 < Math.abs(v2 - I2) && (t2 = p2 || Math.abs(n3 - N) / Math.abs(v2 - I2));
              w = (b - n3) / t2 + v2;
              B = k["plot" + (a2 ? "Width" : "Height")] / t2;
            };
            d();
            g = w;
            if (g < u2.min) {
              g = u2.min;
              var H2 = true;
            } else g + B > u2.max && (g = u2.max - B, H2 = true);
            H2 ? (n3 -= 0.8 * (n3 - m2[r][0]), "number" === typeof N && (N -= 0.8 * (N - m2[r][1])), d()) : m2[r] = [n3, N];
            A || (f2[r] = w - b, f2[c] = B);
            f2 = A ? 1 / t2 : t2;
            l2[c] = B;
            l2[r] = g;
            h2[A ? a2 ? "scaleY" : "scaleX" : "scale" + x] = t2;
            h2["translate" + x] = f2 * b + (n3 - f2 * v2);
          };
          l.prototype.reset = function(a2, g) {
            var d = this.chart, k = d.hoverSeries, l2 = d.hoverPoint, f2 = d.hoverPoints, m2 = d.tooltip, p2 = m2 && m2.shared ? f2 : l2;
            a2 && p2 && h(p2).forEach(function(d2) {
              d2.series.isCartesian && "undefined" === typeof d2.plotX && (a2 = false);
            });
            if (a2) m2 && p2 && h(p2).length && (m2.refresh(p2), m2.shared && f2 ? f2.forEach(function(d2) {
              d2.setState(d2.state, true);
              d2.series.isCartesian && (d2.series.xAxis.crosshair && d2.series.xAxis.drawCrosshair(null, d2), d2.series.yAxis.crosshair && d2.series.yAxis.drawCrosshair(null, d2));
            }) : l2 && (l2.setState(l2.state, true), d.axes.forEach(function(d2) {
              d2.crosshair && l2.series[d2.coll] === d2 && d2.drawCrosshair(null, l2);
            })));
            else {
              if (l2) l2.onMouseOut();
              f2 && f2.forEach(function(d2) {
                d2.setState();
              });
              if (k) k.onMouseOut();
              m2 && m2.hide(g);
              this.unDocMouseMove && (this.unDocMouseMove = this.unDocMouseMove());
              d.axes.forEach(function(d2) {
                d2.hideCrosshair();
              });
              this.hoverX = d.hoverPoints = d.hoverPoint = null;
            }
          };
          l.prototype.runPointActions = function(h2, g) {
            var d = this.chart, k = d.tooltip && d.tooltip.options.enabled ? d.tooltip : void 0, l2 = k ? k.shared : false, f2 = g || d.hoverPoint, p2 = f2 && f2.series || d.hoverSeries;
            p2 = this.getHoverData(
              f2,
              p2,
              d.series,
              (!h2 || "touchmove" !== h2.type) && (!!g || p2 && p2.directTouch && this.isDirectTouch),
              l2,
              h2
            );
            f2 = p2.hoverPoint;
            var B = p2.hoverPoints;
            g = (p2 = p2.hoverSeries) && p2.tooltipOptions.followPointer;
            l2 = l2 && p2 && !p2.noSharedTooltip;
            if (f2 && (f2 !== d.hoverPoint || k && k.isHidden)) {
              (d.hoverPoints || []).forEach(function(d2) {
                -1 === B.indexOf(d2) && d2.setState();
              });
              if (d.hoverSeries !== p2) p2.onMouseOver();
              this.applyInactiveState(B);
              (B || []).forEach(function(d2) {
                d2.setState("hover");
              });
              d.hoverPoint && d.hoverPoint.firePointEvent("mouseOut");
              if (!f2.series) return;
              d.hoverPoints = B;
              d.hoverPoint = f2;
              f2.firePointEvent("mouseOver");
              k && k.refresh(
                l2 ? B : f2,
                h2
              );
            } else g && k && !k.isHidden && (f2 = k.getAnchor([{}], h2), k.updatePosition({ plotX: f2[0], plotY: f2[1] }));
            this.unDocMouseMove || (this.unDocMouseMove = J(d.container.ownerDocument, "mousemove", function(d2) {
              var g2 = G[a.hoverChartIndex];
              if (g2) g2.pointer.onDocumentMouseMove(d2);
            }));
            d.axes.forEach(function(g2) {
              var a2 = m((g2.crosshair || {}).snap, true), k2;
              a2 && ((k2 = d.hoverPoint) && k2.series[g2.coll] === g2 || (k2 = K(B, function(e) {
                return e.series[g2.coll] === g2;
              })));
              k2 || !a2 ? g2.drawCrosshair(h2, k2) : g2.hideCrosshair();
            });
          };
          l.prototype.scaleGroups = function(a2, g) {
            var d = this.chart, h2;
            d.series.forEach(function(k) {
              h2 = a2 || k.getPlotBox();
              k.xAxis && k.xAxis.zoomEnabled && k.group && (k.group.attr(h2), k.markerGroup && (k.markerGroup.attr(h2), k.markerGroup.clip(g ? d.clipRect : null)), k.dataLabelsGroup && k.dataLabelsGroup.attr(h2));
            });
            d.clipRect.attr(g || d.clipBox);
          };
          l.prototype.setDOMEvents = function() {
            var h2 = this.chart.container, g = h2.ownerDocument;
            h2.onmousedown = this.onContainerMouseDown.bind(this);
            h2.onmousemove = this.onContainerMouseMove.bind(this);
            h2.onclick = this.onContainerClick.bind(this);
            this.unbindContainerMouseEnter = J(h2, "mouseenter", this.onContainerMouseEnter.bind(this));
            this.unbindContainerMouseLeave = J(h2, "mouseleave", this.onContainerMouseLeave.bind(this));
            a.unbindDocumentMouseUp || (a.unbindDocumentMouseUp = J(g, "mouseup", this.onDocumentMouseUp.bind(this)));
            a.hasTouch && (J(h2, "touchstart", this.onContainerTouchStart.bind(this)), J(h2, "touchmove", this.onContainerTouchMove.bind(this)), a.unbindDocumentTouchEnd || (a.unbindDocumentTouchEnd = J(g, "touchend", this.onDocumentTouchEnd.bind(this))));
          };
          l.prototype.setHoverChartIndex = function() {
            var h2 = this.chart, g = a.charts[m(a.hoverChartIndex, -1)];
            if (g && g !== h2) g.pointer.onContainerMouseLeave({ relatedTarget: true });
            g && g.mouseIsDown || (a.hoverChartIndex = h2.index);
          };
          l.prototype.touch = function(a2, g) {
            var d = this.chart, h2;
            this.setHoverChartIndex();
            if (1 === a2.touches.length) if (a2 = this.normalize(a2), (h2 = d.isInsidePlot(a2.chartX - d.plotLeft, a2.chartY - d.plotTop)) && !d.openMenu) {
              g && this.runPointActions(a2);
              if ("touchmove" === a2.type) {
                g = this.pinchDown;
                var k = g[0] ? 4 <= Math.sqrt(Math.pow(g[0].chartX - a2.chartX, 2) + Math.pow(g[0].chartY - a2.chartY, 2)) : false;
              }
              m(k, true) && this.pinch(a2);
            } else g && this.reset();
            else 2 === a2.touches.length && this.pinch(a2);
          };
          l.prototype.zoomOption = function(a2) {
            var g = this.chart, d = g.options.chart, h2 = d.zoomType || "";
            g = g.inverted;
            /touch/.test(a2.type) && (h2 = m(d.pinchType, h2));
            this.zoomX = a2 = /x/.test(h2);
            this.zoomY = h2 = /y/.test(h2);
            this.zoomHor = a2 && !g || h2 && g;
            this.zoomVert = h2 && !g || a2 && g;
            this.hasZoom = a2 || h2;
          };
          return l;
        })();
        return a.Pointer = f;
      });
      O(n, "Core/MSPointer.js", [n["Core/Globals.js"], n["Core/Pointer.js"], n["Core/Utilities.js"]], function(f, a, n2) {
        function y() {
          var a2 = [];
          a2.item = function(a3) {
            return this[a3];
          };
          q(E, function(f2) {
            a2.push({ pageX: f2.pageX, pageY: f2.pageY, target: f2.target });
          });
          return a2;
        }
        function D(a2, p2, u, m) {
          "touch" !== a2.pointerType && a2.pointerType !== a2.MSPOINTER_TYPE_TOUCH || !C[f.hoverChartIndex] || (m(a2), m = C[f.hoverChartIndex].pointer, m[p2]({ type: u, target: a2.currentTarget, preventDefault: H, touches: y() }));
        }
        var G = this && this.__extends || /* @__PURE__ */ (function() {
          var a2 = function(f2, p2) {
            a2 = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(a3, h) {
              a3.__proto__ = h;
            } || function(a3, h) {
              for (var l in h) h.hasOwnProperty(l) && (a3[l] = h[l]);
            };
            return a2(f2, p2);
          };
          return function(f2, p2) {
            function m() {
              this.constructor = f2;
            }
            a2(f2, p2);
            f2.prototype = null === p2 ? Object.create(p2) : (m.prototype = p2.prototype, new m());
          };
        })(), C = f.charts, J = f.doc, H = f.noop, v = n2.addEvent, L = n2.css, q = n2.objectEach, K = n2.removeEvent, E = {}, p = !!f.win.PointerEvent;
        return (function(a2) {
          function f2() {
            return null !== a2 && a2.apply(this, arguments) || this;
          }
          G(f2, a2);
          f2.prototype.batchMSEvents = function(a3) {
            a3(this.chart.container, p ? "pointerdown" : "MSPointerDown", this.onContainerPointerDown);
            a3(this.chart.container, p ? "pointermove" : "MSPointerMove", this.onContainerPointerMove);
            a3(J, p ? "pointerup" : "MSPointerUp", this.onDocumentPointerUp);
          };
          f2.prototype.destroy = function() {
            this.batchMSEvents(K);
            a2.prototype.destroy.call(this);
          };
          f2.prototype.init = function(f3, m) {
            a2.prototype.init.call(this, f3, m);
            this.hasZoom && L(f3.container, { "-ms-touch-action": "none", "touch-action": "none" });
          };
          f2.prototype.onContainerPointerDown = function(a3) {
            D(a3, "onContainerTouchStart", "touchstart", function(a4) {
              E[a4.pointerId] = {
                pageX: a4.pageX,
                pageY: a4.pageY,
                target: a4.currentTarget
              };
            });
          };
          f2.prototype.onContainerPointerMove = function(a3) {
            D(a3, "onContainerTouchMove", "touchmove", function(a4) {
              E[a4.pointerId] = { pageX: a4.pageX, pageY: a4.pageY };
              E[a4.pointerId].target || (E[a4.pointerId].target = a4.currentTarget);
            });
          };
          f2.prototype.onDocumentPointerUp = function(a3) {
            D(a3, "onDocumentTouchEnd", "touchend", function(a4) {
              delete E[a4.pointerId];
            });
          };
          f2.prototype.setDOMEvents = function() {
            a2.prototype.setDOMEvents.call(this);
            (this.hasZoom || this.followTouchMove) && this.batchMSEvents(v);
          };
          return f2;
        })(a);
      });
      O(n, "Core/Legend.js", [n["Core/Globals.js"], n["Core/Utilities.js"]], function(f, a) {
        var n2 = a.addEvent, y = a.animObject, D = a.css, G = a.defined, C = a.discardElement, J = a.find, H = a.fireEvent, v = a.format, L = a.isNumber, q = a.merge, K = a.pick, E = a.relativeLength, p = a.setAnimation, t = a.stableSort, I = a.syncTimeout;
        a = a.wrap;
        var u = f.isFirefox, m = f.marginNames, h = f.win, l = (function() {
          function a2(a3, d) {
            this.allItems = [];
            this.contentGroup = this.box = void 0;
            this.display = false;
            this.group = void 0;
            this.offsetWidth = this.maxLegendWidth = this.maxItemWidth = this.legendWidth = this.legendHeight = this.lastLineHeight = this.lastItemY = this.itemY = this.itemX = this.itemMarginTop = this.itemMarginBottom = this.itemHeight = this.initialItemY = 0;
            this.options = {};
            this.padding = 0;
            this.pages = [];
            this.proximate = false;
            this.scrollGroup = void 0;
            this.widthOption = this.totalItemWidth = this.titleHeight = this.symbolWidth = this.symbolHeight = 0;
            this.chart = a3;
            this.init(a3, d);
          }
          a2.prototype.init = function(a3, d) {
            this.chart = a3;
            this.setOptions(d);
            d.enabled && (this.render(), n2(this.chart, "endResize", function() {
              this.legend.positionCheckboxes();
            }), this.proximate ? this.unchartrender = n2(this.chart, "render", function() {
              this.legend.proximatePositions();
              this.legend.positionItems();
            }) : this.unchartrender && this.unchartrender());
          };
          a2.prototype.setOptions = function(a3) {
            var d = K(a3.padding, 8);
            this.options = a3;
            this.chart.styledMode || (this.itemStyle = a3.itemStyle, this.itemHiddenStyle = q(this.itemStyle, a3.itemHiddenStyle));
            this.itemMarginTop = a3.itemMarginTop || 0;
            this.itemMarginBottom = a3.itemMarginBottom || 0;
            this.padding = d;
            this.initialItemY = d - 5;
            this.symbolWidth = K(
              a3.symbolWidth,
              16
            );
            this.pages = [];
            this.proximate = "proximate" === a3.layout && !this.chart.inverted;
            this.baseline = void 0;
          };
          a2.prototype.update = function(a3, d) {
            var g = this.chart;
            this.setOptions(q(true, this.options, a3));
            this.destroy();
            g.isDirtyLegend = g.isDirtyBox = true;
            K(d, true) && g.redraw();
            H(this, "afterUpdate");
          };
          a2.prototype.colorizeItem = function(a3, d) {
            a3.legendGroup[d ? "removeClass" : "addClass"]("highcharts-legend-item-hidden");
            if (!this.chart.styledMode) {
              var g = this.options, h2 = a3.legendItem, k = a3.legendLine, f2 = a3.legendSymbol, l2 = this.itemHiddenStyle.color;
              g = d ? g.itemStyle.color : l2;
              var m2 = d ? a3.color || l2 : l2, p2 = a3.options && a3.options.marker, t2 = { fill: m2 };
              h2 && h2.css({ fill: g, color: g });
              k && k.attr({ stroke: m2 });
              f2 && (p2 && f2.isMarker && (t2 = a3.pointAttribs(), d || (t2.stroke = t2.fill = l2)), f2.attr(t2));
            }
            H(this, "afterColorizeItem", { item: a3, visible: d });
          };
          a2.prototype.positionItems = function() {
            this.allItems.forEach(this.positionItem, this);
            this.chart.isResizing || this.positionCheckboxes();
          };
          a2.prototype.positionItem = function(a3) {
            var d = this, g = this.options, h2 = g.symbolPadding, k = !g.rtl, f2 = a3._legendItemPos;
            g = f2[0];
            f2 = f2[1];
            var l2 = a3.checkbox, m2 = a3.legendGroup;
            m2 && m2.element && (h2 = { translateX: k ? g : this.legendWidth - g - 2 * h2 - 4, translateY: f2 }, k = function() {
              H(d, "afterPositionItem", { item: a3 });
            }, G(m2.translateY) ? m2.animate(h2, void 0, k) : (m2.attr(h2), k()));
            l2 && (l2.x = g, l2.y = f2);
          };
          a2.prototype.destroyItem = function(a3) {
            var d = a3.checkbox;
            ["legendItem", "legendLine", "legendSymbol", "legendGroup"].forEach(function(d2) {
              a3[d2] && (a3[d2] = a3[d2].destroy());
            });
            d && C(a3.checkbox);
          };
          a2.prototype.destroy = function() {
            function a3(d) {
              this[d] && (this[d] = this[d].destroy());
            }
            this.getAllItems().forEach(function(d) {
              [
                "legendItem",
                "legendGroup"
              ].forEach(a3, d);
            });
            "clipRect up down pager nav box title group".split(" ").forEach(a3, this);
            this.display = null;
          };
          a2.prototype.positionCheckboxes = function() {
            var a3 = this.group && this.group.alignAttr, d = this.clipHeight || this.legendHeight, h2 = this.titleHeight;
            if (a3) {
              var k = a3.translateY;
              this.allItems.forEach(function(g) {
                var f2 = g.checkbox;
                if (f2) {
                  var l2 = k + h2 + f2.y + (this.scrollOffset || 0) + 3;
                  D(f2, { left: a3.translateX + g.checkboxOffset + f2.x - 20 + "px", top: l2 + "px", display: this.proximate || l2 > k - 6 && l2 < k + d - 6 ? "" : "none" });
                }
              }, this);
            }
          };
          a2.prototype.renderTitle = function() {
            var a3 = this.options, d = this.padding, h2 = a3.title, k = 0;
            h2.text && (this.title || (this.title = this.chart.renderer.label(h2.text, d - 3, d - 4, null, null, null, a3.useHTML, null, "legend-title").attr({ zIndex: 1 }), this.chart.styledMode || this.title.css(h2.style), this.title.add(this.group)), h2.width || this.title.css({ width: this.maxLegendWidth + "px" }), a3 = this.title.getBBox(), k = a3.height, this.offsetWidth = a3.width, this.contentGroup.attr({ translateY: k }));
            this.titleHeight = k;
          };
          a2.prototype.setText = function(a3) {
            var d = this.options;
            a3.legendItem.attr({ text: d.labelFormat ? v(d.labelFormat, a3, this.chart) : d.labelFormatter.call(a3) });
          };
          a2.prototype.renderItem = function(a3) {
            var d = this.chart, g = d.renderer, h2 = this.options, k = this.symbolWidth, f2 = h2.symbolPadding, l2 = this.itemStyle, m2 = this.itemHiddenStyle, p2 = "horizontal" === h2.layout ? K(h2.itemDistance, 20) : 0, t2 = !h2.rtl, e = a3.legendItem, c = !a3.series, b = !c && a3.series.drawLegendSymbol ? a3.series : a3, z = b.options;
            z = this.createCheckboxForItem && z && z.showCheckbox;
            p2 = k + f2 + p2 + (z ? 20 : 0);
            var w = h2.useHTML, u2 = a3.options.className;
            e || (a3.legendGroup = g.g("legend-item").addClass("highcharts-" + b.type + "-series highcharts-color-" + a3.colorIndex + (u2 ? " " + u2 : "") + (c ? " highcharts-series-" + a3.index : "")).attr({ zIndex: 1 }).add(this.scrollGroup), a3.legendItem = e = g.text("", t2 ? k + f2 : -f2, this.baseline || 0, w), d.styledMode || e.css(q(a3.visible ? l2 : m2)), e.attr({ align: t2 ? "left" : "right", zIndex: 2 }).add(a3.legendGroup), this.baseline || (this.fontMetrics = g.fontMetrics(d.styledMode ? 12 : l2.fontSize, e), this.baseline = this.fontMetrics.f + 3 + this.itemMarginTop, e.attr("y", this.baseline)), this.symbolHeight = h2.symbolHeight || this.fontMetrics.f, b.drawLegendSymbol(this, a3), this.setItemEvents && this.setItemEvents(a3, e, w));
            z && !a3.checkbox && this.createCheckboxForItem && this.createCheckboxForItem(a3);
            this.colorizeItem(a3, a3.visible);
            !d.styledMode && l2.width || e.css({ width: (h2.itemWidth || this.widthOption || d.spacingBox.width) - p2 + "px" });
            this.setText(a3);
            d = e.getBBox();
            a3.itemWidth = a3.checkboxOffset = h2.itemWidth || a3.legendItemWidth || d.width + p2;
            this.maxItemWidth = Math.max(this.maxItemWidth, a3.itemWidth);
            this.totalItemWidth += a3.itemWidth;
            this.itemHeight = a3.itemHeight = Math.round(a3.legendItemHeight || d.height || this.symbolHeight);
          };
          a2.prototype.layoutItem = function(a3) {
            var d = this.options, g = this.padding, h2 = "horizontal" === d.layout, k = a3.itemHeight, f2 = this.itemMarginBottom, l2 = this.itemMarginTop, m2 = h2 ? K(d.itemDistance, 20) : 0, p2 = this.maxLegendWidth;
            d = d.alignColumns && this.totalItemWidth > p2 ? this.maxItemWidth : a3.itemWidth;
            h2 && this.itemX - g + d > p2 && (this.itemX = g, this.lastLineHeight && (this.itemY += l2 + this.lastLineHeight + f2), this.lastLineHeight = 0);
            this.lastItemY = l2 + this.itemY + f2;
            this.lastLineHeight = Math.max(k, this.lastLineHeight);
            a3._legendItemPos = [this.itemX, this.itemY];
            h2 ? this.itemX += d : (this.itemY += l2 + k + f2, this.lastLineHeight = k);
            this.offsetWidth = this.widthOption || Math.max((h2 ? this.itemX - g - (a3.checkbox ? 0 : m2) : d) + g, this.offsetWidth);
          };
          a2.prototype.getAllItems = function() {
            var a3 = [];
            this.chart.series.forEach(function(d) {
              var g = d && d.options;
              d && K(g.showInLegend, G(g.linkedTo) ? false : void 0, true) && (a3 = a3.concat(d.legendItems || ("point" === g.legendType ? d.data : d)));
            });
            H(
              this,
              "afterGetAllItems",
              { allItems: a3 }
            );
            return a3;
          };
          a2.prototype.getAlignment = function() {
            var a3 = this.options;
            return this.proximate ? a3.align.charAt(0) + "tv" : a3.floating ? "" : a3.align.charAt(0) + a3.verticalAlign.charAt(0) + a3.layout.charAt(0);
          };
          a2.prototype.adjustMargins = function(a3, d) {
            var g = this.chart, h2 = this.options, k = this.getAlignment();
            k && [/(lth|ct|rth)/, /(rtv|rm|rbv)/, /(rbh|cb|lbh)/, /(lbv|lm|ltv)/].forEach(function(f2, l2) {
              f2.test(k) && !G(a3[l2]) && (g[m[l2]] = Math.max(g[m[l2]], g.legend[(l2 + 1) % 2 ? "legendHeight" : "legendWidth"] + [1, -1, -1, 1][l2] * h2[l2 % 2 ? "x" : "y"] + K(h2.margin, 12) + d[l2] + (g.titleOffset[l2] || 0)));
            });
          };
          a2.prototype.proximatePositions = function() {
            var a3 = this.chart, d = [], h2 = "left" === this.options.align;
            this.allItems.forEach(function(g) {
              var k;
              var l2 = h2;
              if (g.yAxis) {
                g.xAxis.options.reversed && (l2 = !l2);
                g.points && (k = J(l2 ? g.points : g.points.slice(0).reverse(), function(d2) {
                  return L(d2.plotY);
                }));
                l2 = this.itemMarginTop + g.legendItem.getBBox().height + this.itemMarginBottom;
                var f2 = g.yAxis.top - a3.plotTop;
                g.visible ? (k = k ? k.plotY : g.yAxis.height, k += f2 - 0.3 * l2) : k = f2 + g.yAxis.height;
                d.push({
                  target: k,
                  size: l2,
                  item: g
                });
              }
            }, this);
            f.distribute(d, a3.plotHeight);
            d.forEach(function(d2) {
              d2.item._legendItemPos[1] = a3.plotTop - a3.spacing[0] + d2.pos;
            });
          };
          a2.prototype.render = function() {
            var a3 = this.chart, d = a3.renderer, h2 = this.group, k = this.box, l2 = this.options, f2 = this.padding;
            this.itemX = f2;
            this.itemY = this.initialItemY;
            this.lastItemY = this.offsetWidth = 0;
            this.widthOption = E(l2.width, a3.spacingBox.width - f2);
            var m2 = a3.spacingBox.width - 2 * f2 - l2.x;
            -1 < ["rm", "lm"].indexOf(this.getAlignment().substring(0, 2)) && (m2 /= 2);
            this.maxLegendWidth = this.widthOption || m2;
            h2 || (this.group = h2 = d.g("legend").attr({ zIndex: 7 }).add(), this.contentGroup = d.g().attr({ zIndex: 1 }).add(h2), this.scrollGroup = d.g().add(this.contentGroup));
            this.renderTitle();
            var p2 = this.getAllItems();
            t(p2, function(d2, e) {
              return (d2.options && d2.options.legendIndex || 0) - (e.options && e.options.legendIndex || 0);
            });
            l2.reversed && p2.reverse();
            this.allItems = p2;
            this.display = m2 = !!p2.length;
            this.itemHeight = this.totalItemWidth = this.maxItemWidth = this.lastLineHeight = 0;
            p2.forEach(this.renderItem, this);
            p2.forEach(this.layoutItem, this);
            p2 = (this.widthOption || this.offsetWidth) + f2;
            var q2 = this.lastItemY + this.lastLineHeight + this.titleHeight;
            q2 = this.handleOverflow(q2);
            q2 += f2;
            k || (this.box = k = d.rect().addClass("highcharts-legend-box").attr({ r: l2.borderRadius }).add(h2), k.isNew = true);
            a3.styledMode || k.attr({ stroke: l2.borderColor, "stroke-width": l2.borderWidth || 0, fill: l2.backgroundColor || "none" }).shadow(l2.shadow);
            0 < p2 && 0 < q2 && (k[k.isNew ? "attr" : "animate"](k.crisp.call({}, { x: 0, y: 0, width: p2, height: q2 }, k.strokeWidth())), k.isNew = false);
            k[m2 ? "show" : "hide"]();
            a3.styledMode && "none" === h2.getStyle("display") && (p2 = q2 = 0);
            this.legendWidth = p2;
            this.legendHeight = q2;
            m2 && this.align();
            this.proximate || this.positionItems();
            H(this, "afterRender");
          };
          a2.prototype.align = function(a3) {
            void 0 === a3 && (a3 = this.chart.spacingBox);
            var d = this.chart, g = this.options, h2 = a3.y;
            /(lth|ct|rth)/.test(this.getAlignment()) && 0 < d.titleOffset[0] ? h2 += d.titleOffset[0] : /(lbh|cb|rbh)/.test(this.getAlignment()) && 0 < d.titleOffset[2] && (h2 -= d.titleOffset[2]);
            h2 !== a3.y && (a3 = q(a3, { y: h2 }));
            this.group.align(q(g, {
              width: this.legendWidth,
              height: this.legendHeight,
              verticalAlign: this.proximate ? "top" : g.verticalAlign
            }), true, a3);
          };
          a2.prototype.handleOverflow = function(a3) {
            var d = this, g = this.chart, h2 = g.renderer, k = this.options, l2 = k.y, f2 = this.padding;
            l2 = g.spacingBox.height + ("top" === k.verticalAlign ? -l2 : l2) - f2;
            var m2 = k.maxHeight, p2, t2 = this.clipRect, e = k.navigation, c = K(e.animation, true), b = e.arrowSize || 12, z = this.nav, w = this.pages, q2, u2 = this.allItems, n3 = function(b2) {
              "number" === typeof b2 ? t2.attr({ height: b2 }) : t2 && (d.clipRect = t2.destroy(), d.contentGroup.clip());
              d.contentGroup.div && (d.contentGroup.div.style.clip = b2 ? "rect(" + f2 + "px,9999px," + (f2 + b2) + "px,0)" : "auto");
            }, v2 = function(c2) {
              d[c2] = h2.circle(0, 0, 1.3 * b).translate(b / 2, b / 2).add(z);
              g.styledMode || d[c2].attr("fill", "rgba(0,0,0,0.0001)");
              return d[c2];
            };
            "horizontal" !== k.layout || "middle" === k.verticalAlign || k.floating || (l2 /= 2);
            m2 && (l2 = Math.min(l2, m2));
            w.length = 0;
            a3 > l2 && false !== e.enabled ? (this.clipHeight = p2 = Math.max(l2 - 20 - this.titleHeight - f2, 0), this.currentPage = K(this.currentPage, 1), this.fullHeight = a3, u2.forEach(function(b2, c2) {
              var e2 = b2._legendItemPos[1], d2 = Math.round(b2.legendItem.getBBox().height), a4 = w.length;
              if (!a4 || e2 - w[a4 - 1] > p2 && (q2 || e2) !== w[a4 - 1]) w.push(q2 || e2), a4++;
              b2.pageIx = a4 - 1;
              q2 && (u2[c2 - 1].pageIx = a4 - 1);
              c2 === u2.length - 1 && e2 + d2 - w[a4 - 1] > p2 && e2 !== q2 && (w.push(e2), b2.pageIx = a4);
              e2 !== q2 && (q2 = e2);
            }), t2 || (t2 = d.clipRect = h2.clipRect(0, f2, 9999, 0), d.contentGroup.clip(t2)), n3(p2), z || (this.nav = z = h2.g().attr({ zIndex: 1 }).add(this.group), this.up = h2.symbol("triangle", 0, 0, b, b).add(z), v2("upTracker").on("click", function() {
              d.scroll(-1, c);
            }), this.pager = h2.text("", 15, 10).addClass("highcharts-legend-navigation"), g.styledMode || this.pager.css(e.style), this.pager.add(z), this.down = h2.symbol("triangle-down", 0, 0, b, b).add(z), v2("downTracker").on("click", function() {
              d.scroll(1, c);
            })), d.scroll(0), a3 = l2) : z && (n3(), this.nav = z.destroy(), this.scrollGroup.attr({ translateY: 1 }), this.clipHeight = 0);
            return a3;
          };
          a2.prototype.scroll = function(a3, d) {
            var g = this, h2 = this.chart, k = this.pages, l2 = k.length, f2 = this.currentPage + a3;
            a3 = this.clipHeight;
            var m2 = this.options.navigation, t2 = this.pager, q2 = this.padding;
            f2 > l2 && (f2 = l2);
            0 < f2 && ("undefined" !== typeof d && p(d, h2), this.nav.attr({ translateX: q2, translateY: a3 + this.padding + 7 + this.titleHeight, visibility: "visible" }), [this.up, this.upTracker].forEach(function(e) {
              e.attr({ "class": 1 === f2 ? "highcharts-legend-nav-inactive" : "highcharts-legend-nav-active" });
            }), t2.attr({ text: f2 + "/" + l2 }), [this.down, this.downTracker].forEach(function(e) {
              e.attr({ x: 18 + this.pager.getBBox().width, "class": f2 === l2 ? "highcharts-legend-nav-inactive" : "highcharts-legend-nav-active" });
            }, this), h2.styledMode || (this.up.attr({ fill: 1 === f2 ? m2.inactiveColor : m2.activeColor }), this.upTracker.css({ cursor: 1 === f2 ? "default" : "pointer" }), this.down.attr({ fill: f2 === l2 ? m2.inactiveColor : m2.activeColor }), this.downTracker.css({ cursor: f2 === l2 ? "default" : "pointer" })), this.scrollOffset = -k[f2 - 1] + this.initialItemY, this.scrollGroup.animate({ translateY: this.scrollOffset }), this.currentPage = f2, this.positionCheckboxes(), d = y(K(d, h2.renderer.globalAnimation, true)), I(function() {
              H(g, "afterScroll", { currentPage: f2 });
            }, d.duration));
          };
          return a2;
        })();
        (/Trident\/7\.0/.test(h.navigator && h.navigator.userAgent) || u) && a(l.prototype, "positionItem", function(a2, g) {
          var d = this, h2 = function() {
            g._legendItemPos && a2.call(d, g);
          };
          h2();
          d.bubbleLegend || setTimeout(h2);
        });
        f.Legend = l;
        return f.Legend;
      });
      O(n, "Core/Chart/Chart.js", [n["Core/Axis/Axis.js"], n["Core/Globals.js"], n["Core/Legend.js"], n["Core/MSPointer.js"], n["Core/Options.js"], n["Core/Pointer.js"], n["Core/Time.js"], n["Core/Utilities.js"]], function(f, a, n2, y, D, G, C, J) {
        var H = a.charts, v = a.doc, L = a.seriesTypes, q = a.win, K = D.defaultOptions, E = J.addEvent, p = J.animate, t = J.animObject, I = J.attr, u = J.createElement, m = J.css, h = J.defined, l = J.discardElement, k = J.erase, g = J.error, d = J.extend, x = J.find, r = J.fireEvent, A = J.getStyle, N = J.isArray, B = J.isFunction, M = J.isNumber, R = J.isObject, F = J.isString, e = J.merge, c = J.numberFormat, b = J.objectEach, z = J.pick, w = J.pInt, P = J.relativeLength, Z = J.removeEvent, W = J.setAnimation, aa = J.splat, X = J.syncTimeout, ba = J.uniqueKey, ca = a.marginNames, Y = (function() {
          function D2(b2, c2, e2) {
            this.yAxis = this.xAxis = this.userOptions = this.titleOffset = this.time = this.symbolCounter = this.spacingBox = this.spacing = this.series = this.renderTo = this.renderer = this.pointer = this.pointCount = this.plotWidth = this.plotTop = this.plotLeft = this.plotHeight = this.plotBox = this.options = this.numberFormatter = this.margin = this.legend = this.labelCollectors = this.isResizing = this.index = this.container = this.colorCounter = this.clipBox = this.chartWidth = this.chartHeight = this.bounds = this.axisOffset = this.axes = void 0;
            this.getArgs(b2, c2, e2);
          }
          D2.prototype.getArgs = function(b2, c2, e2) {
            F(b2) || b2.nodeName ? (this.renderTo = b2, this.init(c2, e2)) : this.init(b2, c2);
          };
          D2.prototype.init = function(d2, g2) {
            var h2, k2 = d2.series, l2 = d2.plotOptions || {};
            r(this, "init", { args: arguments }, function() {
              d2.series = null;
              h2 = e(K, d2);
              var f2 = h2.chart || {};
              b(h2.plotOptions, function(b2, c2) {
                R(b2) && (b2.tooltip = l2[c2] && e(l2[c2].tooltip) || void 0);
              });
              h2.tooltip.userOptions = d2.chart && d2.chart.forExport && d2.tooltip.userOptions || d2.tooltip;
              h2.series = d2.series = k2;
              this.userOptions = d2;
              var m2 = f2.events;
              this.margin = [];
              this.spacing = [];
              this.bounds = { h: {}, v: {} };
              this.labelCollectors = [];
              this.callback = g2;
              this.isResizing = 0;
              this.options = h2;
              this.axes = [];
              this.series = [];
              this.time = d2.time && Object.keys(d2.time).length ? new C(d2.time) : a.time;
              this.numberFormatter = f2.numberFormatter || c;
              this.styledMode = f2.styledMode;
              this.hasCartesianSeries = f2.showAxes;
              var p2 = this;
              p2.index = H.length;
              H.push(p2);
              a.chartCount++;
              m2 && b(m2, function(b2, c2) {
                B(b2) && E(p2, c2, b2);
              });
              p2.xAxis = [];
              p2.yAxis = [];
              p2.pointCount = p2.colorCounter = p2.symbolCounter = 0;
              r(p2, "afterInit");
              p2.firstRender();
            });
          };
          D2.prototype.initSeries = function(b2) {
            var c2 = this.options.chart;
            c2 = b2.type || c2.type || c2.defaultSeriesType;
            var e2 = L[c2];
            e2 || g(17, true, this, { missingModuleFor: c2 });
            c2 = new e2();
            c2.init(this, b2);
            return c2;
          };
          D2.prototype.setSeriesData = function() {
            this.getSeriesOrderByLinks().forEach(function(b2) {
              b2.points || b2.data || !b2.enabledDataSorting || b2.setData(b2.options.data, false);
            });
          };
          D2.prototype.getSeriesOrderByLinks = function() {
            return this.series.concat().sort(function(b2, c2) {
              return b2.linkedSeries.length || c2.linkedSeries.length ? c2.linkedSeries.length - b2.linkedSeries.length : 0;
            });
          };
          D2.prototype.orderSeries = function(b2) {
            var c2 = this.series;
            for (b2 = b2 || 0; b2 < c2.length; b2++) c2[b2] && (c2[b2].index = b2, c2[b2].name = c2[b2].getName());
          };
          D2.prototype.isInsidePlot = function(b2, c2, e2) {
            var d2 = e2 ? c2 : b2;
            b2 = e2 ? b2 : c2;
            d2 = { x: d2, y: b2, isInsidePlot: 0 <= d2 && d2 <= this.plotWidth && 0 <= b2 && b2 <= this.plotHeight };
            r(this, "afterIsInsidePlot", d2);
            return d2.isInsidePlot;
          };
          D2.prototype.redraw = function(b2) {
            r(this, "beforeRedraw");
            var c2 = this, e2 = c2.axes, a2 = c2.series, g2 = c2.pointer, h2 = c2.legend, k2 = c2.userOptions.legend, l2 = c2.isDirtyLegend, f2 = c2.hasCartesianSeries, m2 = c2.isDirtyBox, p2 = c2.renderer, w2 = p2.isHidden(), z2 = [];
            c2.setResponsive && c2.setResponsive(false);
            W(c2.hasRendered ? b2 : false, c2);
            w2 && c2.temporaryDisplay();
            c2.layOutTitles();
            for (b2 = a2.length; b2--; ) {
              var t2 = a2[b2];
              if (t2.options.stacking) {
                var B2 = true;
                if (t2.isDirty) {
                  var q2 = true;
                  break;
                }
              }
            }
            if (q2) for (b2 = a2.length; b2--; ) t2 = a2[b2], t2.options.stacking && (t2.isDirty = true);
            a2.forEach(function(b3) {
              b3.isDirty && ("point" === b3.options.legendType ? (b3.updateTotals && b3.updateTotals(), l2 = true) : k2 && (k2.labelFormatter || k2.labelFormat) && (l2 = true));
              b3.isDirtyData && r(b3, "updatedData");
            });
            l2 && h2 && h2.options.enabled && (h2.render(), c2.isDirtyLegend = false);
            B2 && c2.getStacks();
            f2 && e2.forEach(function(b3) {
              c2.isResizing && M(b3.min) || (b3.updateNames(), b3.setScale());
            });
            c2.getMargins();
            f2 && (e2.forEach(function(b3) {
              b3.isDirty && (m2 = true);
            }), e2.forEach(function(b3) {
              var c3 = b3.min + "," + b3.max;
              b3.extKey !== c3 && (b3.extKey = c3, z2.push(function() {
                r(b3, "afterSetExtremes", d(b3.eventArgs, b3.getExtremes()));
                delete b3.eventArgs;
              }));
              (m2 || B2) && b3.redraw();
            }));
            m2 && c2.drawChartBox();
            r(c2, "predraw");
            a2.forEach(function(b3) {
              (m2 || b3.isDirty) && b3.visible && b3.redraw();
              b3.isDirtyData = false;
            });
            g2 && g2.reset(true);
            p2.draw();
            r(c2, "redraw");
            r(c2, "render");
            w2 && c2.temporaryDisplay(true);
            z2.forEach(function(b3) {
              b3.call();
            });
          };
          D2.prototype.get = function(b2) {
            function c2(c3) {
              return c3.id === b2 || c3.options && c3.options.id === b2;
            }
            var e2 = this.series, d2;
            var a2 = x(this.axes, c2) || x(this.series, c2);
            for (d2 = 0; !a2 && d2 < e2.length; d2++) a2 = x(e2[d2].points || [], c2);
            return a2;
          };
          D2.prototype.getAxes = function() {
            var b2 = this, c2 = this.options, e2 = c2.xAxis = aa(c2.xAxis || {});
            c2 = c2.yAxis = aa(c2.yAxis || {});
            r(this, "getAxes");
            e2.forEach(function(b3, c3) {
              b3.index = c3;
              b3.isX = true;
            });
            c2.forEach(function(b3, c3) {
              b3.index = c3;
            });
            e2.concat(c2).forEach(function(c3) {
              new f(b2, c3);
            });
            r(this, "afterGetAxes");
          };
          D2.prototype.getSelectedPoints = function() {
            var b2 = [];
            this.series.forEach(function(c2) {
              b2 = b2.concat(c2.getPointsCollection().filter(function(b3) {
                return z(
                  b3.selectedStaging,
                  b3.selected
                );
              }));
            });
            return b2;
          };
          D2.prototype.getSelectedSeries = function() {
            return this.series.filter(function(b2) {
              return b2.selected;
            });
          };
          D2.prototype.setTitle = function(b2, c2, e2) {
            this.applyDescription("title", b2);
            this.applyDescription("subtitle", c2);
            this.applyDescription("caption", void 0);
            this.layOutTitles(e2);
          };
          D2.prototype.applyDescription = function(b2, c2) {
            var d2 = this, a2 = "title" === b2 ? { color: "#333333", fontSize: this.options.isStock ? "16px" : "18px" } : { color: "#666666" };
            a2 = this.options[b2] = e(
              !this.styledMode && { style: a2 },
              this.options[b2],
              c2
            );
            var g2 = this[b2];
            g2 && c2 && (this[b2] = g2 = g2.destroy());
            a2 && !g2 && (g2 = this.renderer.text(a2.text, 0, 0, a2.useHTML).attr({ align: a2.align, "class": "highcharts-" + b2, zIndex: a2.zIndex || 4 }).add(), g2.update = function(c3) {
              d2[{ title: "setTitle", subtitle: "setSubtitle", caption: "setCaption" }[b2]](c3);
            }, this.styledMode || g2.css(a2.style), this[b2] = g2);
          };
          D2.prototype.layOutTitles = function(b2) {
            var c2 = [0, 0, 0], e2 = this.renderer, a2 = this.spacingBox;
            ["title", "subtitle", "caption"].forEach(function(b3) {
              var g3 = this[b3], h2 = this.options[b3], k2 = h2.verticalAlign || "top";
              b3 = "title" === b3 ? -3 : "top" === k2 ? c2[0] + 2 : 0;
              if (g3) {
                if (!this.styledMode) var l2 = h2.style.fontSize;
                l2 = e2.fontMetrics(l2, g3).b;
                g3.css({ width: (h2.width || a2.width + (h2.widthAdjust || 0)) + "px" });
                var f2 = Math.round(g3.getBBox(h2.useHTML).height);
                g3.align(d({ y: "bottom" === k2 ? l2 : b3 + l2, height: f2 }, h2), false, "spacingBox");
                h2.floating || ("top" === k2 ? c2[0] = Math.ceil(c2[0] + f2) : "bottom" === k2 && (c2[2] = Math.ceil(c2[2] + f2)));
              }
            }, this);
            c2[0] && "top" === (this.options.title.verticalAlign || "top") && (c2[0] += this.options.title.margin);
            c2[2] && "bottom" === this.options.caption.verticalAlign && (c2[2] += this.options.caption.margin);
            var g2 = !this.titleOffset || this.titleOffset.join(",") !== c2.join(",");
            this.titleOffset = c2;
            r(this, "afterLayOutTitles");
            !this.isDirtyBox && g2 && (this.isDirtyBox = this.isDirtyLegend = g2, this.hasRendered && z(b2, true) && this.isDirtyBox && this.redraw());
          };
          D2.prototype.getChartSize = function() {
            var b2 = this.options.chart, c2 = b2.width;
            b2 = b2.height;
            var e2 = this.renderTo;
            h(c2) || (this.containerWidth = A(e2, "width"));
            h(b2) || (this.containerHeight = A(e2, "height"));
            this.chartWidth = Math.max(0, c2 || this.containerWidth || 600);
            this.chartHeight = Math.max(0, P(b2, this.chartWidth) || (1 < this.containerHeight ? this.containerHeight : 400));
          };
          D2.prototype.temporaryDisplay = function(b2) {
            var c2 = this.renderTo;
            if (b2) for (; c2 && c2.style; ) c2.hcOrigStyle && (m(c2, c2.hcOrigStyle), delete c2.hcOrigStyle), c2.hcOrigDetached && (v.body.removeChild(c2), c2.hcOrigDetached = false), c2 = c2.parentNode;
            else for (; c2 && c2.style; ) {
              v.body.contains(c2) || c2.parentNode || (c2.hcOrigDetached = true, v.body.appendChild(c2));
              if ("none" === A(c2, "display", false) || c2.hcOricDetached) c2.hcOrigStyle = {
                display: c2.style.display,
                height: c2.style.height,
                overflow: c2.style.overflow
              }, b2 = { display: "block", overflow: "hidden" }, c2 !== this.renderTo && (b2.height = 0), m(c2, b2), c2.offsetWidth || c2.style.setProperty("display", "block", "important");
              c2 = c2.parentNode;
              if (c2 === v.body) break;
            }
          };
          D2.prototype.setClassName = function(b2) {
            this.container.className = "highcharts-container " + (b2 || "");
          };
          D2.prototype.getContainer = function() {
            var b2 = this.options, c2 = b2.chart;
            var e2 = this.renderTo;
            var h2 = ba(), k2, l2;
            e2 || (this.renderTo = e2 = c2.renderTo);
            F(e2) && (this.renderTo = e2 = v.getElementById(e2));
            e2 || g(13, true, this);
            var f2 = w(I(e2, "data-highcharts-chart"));
            M(f2) && H[f2] && H[f2].hasRendered && H[f2].destroy();
            I(e2, "data-highcharts-chart", this.index);
            e2.innerHTML = "";
            c2.skipClone || e2.offsetWidth || this.temporaryDisplay();
            this.getChartSize();
            f2 = this.chartWidth;
            var p2 = this.chartHeight;
            m(e2, { overflow: "hidden" });
            this.styledMode || (k2 = d({ position: "relative", overflow: "hidden", width: f2 + "px", height: p2 + "px", textAlign: "left", lineHeight: "normal", zIndex: 0, "-webkit-tap-highlight-color": "rgba(0,0,0,0)", userSelect: "none" }, c2.style));
            this.container = e2 = u("div", { id: h2 }, k2, e2);
            this._cursor = e2.style.cursor;
            this.renderer = new (a[c2.renderer] || a.Renderer)(e2, f2, p2, null, c2.forExport, b2.exporting && b2.exporting.allowHTML, this.styledMode);
            W(void 0, this);
            this.setClassName(c2.className);
            if (this.styledMode) for (l2 in b2.defs) this.renderer.definition(b2.defs[l2]);
            else this.renderer.setStyle(c2.style);
            this.renderer.chartIndex = this.index;
            r(this, "afterGetContainer");
          };
          D2.prototype.getMargins = function(b2) {
            var c2 = this.spacing, e2 = this.margin, d2 = this.titleOffset;
            this.resetMargins();
            d2[0] && !h(e2[0]) && (this.plotTop = Math.max(this.plotTop, d2[0] + c2[0]));
            d2[2] && !h(e2[2]) && (this.marginBottom = Math.max(this.marginBottom, d2[2] + c2[2]));
            this.legend && this.legend.display && this.legend.adjustMargins(e2, c2);
            r(this, "getMargins");
            b2 || this.getAxisMargins();
          };
          D2.prototype.getAxisMargins = function() {
            var b2 = this, c2 = b2.axisOffset = [0, 0, 0, 0], e2 = b2.colorAxis, d2 = b2.margin, a2 = function(b3) {
              b3.forEach(function(b4) {
                b4.visible && b4.getOffset();
              });
            };
            b2.hasCartesianSeries ? a2(b2.axes) : e2 && e2.length && a2(e2);
            ca.forEach(function(e3, a3) {
              h(d2[a3]) || (b2[e3] += c2[a3]);
            });
            b2.setChartSize();
          };
          D2.prototype.reflow = function(b2) {
            var c2 = this, e2 = c2.options.chart, d2 = c2.renderTo, a2 = h(e2.width) && h(e2.height), g2 = e2.width || A(d2, "width");
            e2 = e2.height || A(d2, "height");
            d2 = b2 ? b2.target : q;
            if (!a2 && !c2.isPrinting && g2 && e2 && (d2 === q || d2 === v)) {
              if (g2 !== c2.containerWidth || e2 !== c2.containerHeight) J.clearTimeout(c2.reflowTimeout), c2.reflowTimeout = X(function() {
                c2.container && c2.setSize(void 0, void 0, false);
              }, b2 ? 100 : 0);
              c2.containerWidth = g2;
              c2.containerHeight = e2;
            }
          };
          D2.prototype.setReflow = function(b2) {
            var c2 = this;
            false === b2 || this.unbindReflow ? false === b2 && this.unbindReflow && (this.unbindReflow = this.unbindReflow()) : (this.unbindReflow = E(q, "resize", function(b3) {
              c2.options && c2.reflow(b3);
            }), E(this, "destroy", this.unbindReflow));
          };
          D2.prototype.setSize = function(b2, c2, e2) {
            var d2 = this, a2 = d2.renderer;
            d2.isResizing += 1;
            W(e2, d2);
            e2 = a2.globalAnimation;
            d2.oldChartHeight = d2.chartHeight;
            d2.oldChartWidth = d2.chartWidth;
            "undefined" !== typeof b2 && (d2.options.chart.width = b2);
            "undefined" !== typeof c2 && (d2.options.chart.height = c2);
            d2.getChartSize();
            d2.styledMode || (e2 ? p : m)(d2.container, { width: d2.chartWidth + "px", height: d2.chartHeight + "px" }, e2);
            d2.setChartSize(true);
            a2.setSize(d2.chartWidth, d2.chartHeight, e2);
            d2.axes.forEach(function(b3) {
              b3.isDirty = true;
              b3.setScale();
            });
            d2.isDirtyLegend = true;
            d2.isDirtyBox = true;
            d2.layOutTitles();
            d2.getMargins();
            d2.redraw(e2);
            d2.oldChartHeight = null;
            r(d2, "resize");
            X(function() {
              d2 && r(d2, "endResize", null, function() {
                --d2.isResizing;
              });
            }, t(e2).duration);
          };
          D2.prototype.setChartSize = function(b2) {
            var c2 = this.inverted, e2 = this.renderer, d2 = this.chartWidth, a2 = this.chartHeight, g2 = this.options.chart, h2 = this.spacing, k2 = this.clipOffset, f2, l2, m2, p2;
            this.plotLeft = f2 = Math.round(this.plotLeft);
            this.plotTop = l2 = Math.round(this.plotTop);
            this.plotWidth = m2 = Math.max(0, Math.round(d2 - f2 - this.marginRight));
            this.plotHeight = p2 = Math.max(0, Math.round(a2 - l2 - this.marginBottom));
            this.plotSizeX = c2 ? p2 : m2;
            this.plotSizeY = c2 ? m2 : p2;
            this.plotBorderWidth = g2.plotBorderWidth || 0;
            this.spacingBox = e2.spacingBox = { x: h2[3], y: h2[0], width: d2 - h2[3] - h2[1], height: a2 - h2[0] - h2[2] };
            this.plotBox = e2.plotBox = { x: f2, y: l2, width: m2, height: p2 };
            d2 = 2 * Math.floor(this.plotBorderWidth / 2);
            c2 = Math.ceil(Math.max(d2, k2[3]) / 2);
            e2 = Math.ceil(Math.max(d2, k2[0]) / 2);
            this.clipBox = { x: c2, y: e2, width: Math.floor(this.plotSizeX - Math.max(d2, k2[1]) / 2 - c2), height: Math.max(0, Math.floor(this.plotSizeY - Math.max(d2, k2[2]) / 2 - e2)) };
            b2 || this.axes.forEach(function(b3) {
              b3.setAxisSize();
              b3.setAxisTranslation();
            });
            r(this, "afterSetChartSize", { skipAxes: b2 });
          };
          D2.prototype.resetMargins = function() {
            r(this, "resetMargins");
            var b2 = this, c2 = b2.options.chart;
            ["margin", "spacing"].forEach(function(e2) {
              var d2 = c2[e2], a2 = R(d2) ? d2 : [d2, d2, d2, d2];
              ["Top", "Right", "Bottom", "Left"].forEach(function(d3, g2) {
                b2[e2][g2] = z(c2[e2 + d3], a2[g2]);
              });
            });
            ca.forEach(function(c3, e2) {
              b2[c3] = z(b2.margin[e2], b2.spacing[e2]);
            });
            b2.axisOffset = [0, 0, 0, 0];
            b2.clipOffset = [0, 0, 0, 0];
          };
          D2.prototype.drawChartBox = function() {
            var b2 = this.options.chart, c2 = this.renderer, e2 = this.chartWidth, d2 = this.chartHeight, a2 = this.chartBackground, g2 = this.plotBackground, h2 = this.plotBorder, k2 = this.styledMode, f2 = this.plotBGImage, l2 = b2.backgroundColor, m2 = b2.plotBackgroundColor, p2 = b2.plotBackgroundImage, w2, z2 = this.plotLeft, t2 = this.plotTop, B2 = this.plotWidth, q2 = this.plotHeight, x2 = this.plotBox, u2 = this.clipRect, A2 = this.clipBox, F2 = "animate";
            a2 || (this.chartBackground = a2 = c2.rect().addClass("highcharts-background").add(), F2 = "attr");
            if (k2) var n3 = w2 = a2.strokeWidth();
            else {
              n3 = b2.borderWidth || 0;
              w2 = n3 + (b2.shadow ? 8 : 0);
              l2 = { fill: l2 || "none" };
              if (n3 || a2["stroke-width"]) l2.stroke = b2.borderColor, l2["stroke-width"] = n3;
              a2.attr(l2).shadow(b2.shadow);
            }
            a2[F2]({ x: w2 / 2, y: w2 / 2, width: e2 - w2 - n3 % 2, height: d2 - w2 - n3 % 2, r: b2.borderRadius });
            F2 = "animate";
            g2 || (F2 = "attr", this.plotBackground = g2 = c2.rect().addClass("highcharts-plot-background").add());
            g2[F2](x2);
            k2 || (g2.attr({ fill: m2 || "none" }).shadow(b2.plotShadow), p2 && (f2 ? (p2 !== f2.attr("href") && f2.attr("href", p2), f2.animate(x2)) : this.plotBGImage = c2.image(p2, z2, t2, B2, q2).add()));
            u2 ? u2.animate({ width: A2.width, height: A2.height }) : this.clipRect = c2.clipRect(A2);
            F2 = "animate";
            h2 || (F2 = "attr", this.plotBorder = h2 = c2.rect().addClass("highcharts-plot-border").attr({ zIndex: 1 }).add());
            k2 || h2.attr({ stroke: b2.plotBorderColor, "stroke-width": b2.plotBorderWidth || 0, fill: "none" });
            h2[F2](h2.crisp({ x: z2, y: t2, width: B2, height: q2 }, -h2.strokeWidth()));
            this.isDirtyBox = false;
            r(this, "afterDrawChartBox");
          };
          D2.prototype.propFromSeries = function() {
            var b2 = this, c2 = b2.options.chart, e2, d2 = b2.options.series, a2, g2;
            ["inverted", "angular", "polar"].forEach(function(h2) {
              e2 = L[c2.type || c2.defaultSeriesType];
              g2 = c2[h2] || e2 && e2.prototype[h2];
              for (a2 = d2 && d2.length; !g2 && a2--; ) (e2 = L[d2[a2].type]) && e2.prototype[h2] && (g2 = true);
              b2[h2] = g2;
            });
          };
          D2.prototype.linkSeries = function() {
            var b2 = this, c2 = b2.series;
            c2.forEach(function(b3) {
              b3.linkedSeries.length = 0;
            });
            c2.forEach(function(c3) {
              var e2 = c3.options.linkedTo;
              F(e2) && (e2 = ":previous" === e2 ? b2.series[c3.index - 1] : b2.get(e2)) && e2.linkedParent !== c3 && (e2.linkedSeries.push(c3), c3.linkedParent = e2, e2.enabledDataSorting && c3.setDataSortingOptions(), c3.visible = z(c3.options.visible, e2.options.visible, c3.visible));
            });
            r(this, "afterLinkSeries");
          };
          D2.prototype.renderSeries = function() {
            this.series.forEach(function(b2) {
              b2.translate();
              b2.render();
            });
          };
          D2.prototype.renderLabels = function() {
            var b2 = this, c2 = b2.options.labels;
            c2.items && c2.items.forEach(function(e2) {
              var a2 = d(c2.style, e2.style), g2 = w(a2.left) + b2.plotLeft, h2 = w(a2.top) + b2.plotTop + 12;
              delete a2.left;
              delete a2.top;
              b2.renderer.text(
                e2.html,
                g2,
                h2
              ).attr({ zIndex: 2 }).css(a2).add();
            });
          };
          D2.prototype.render = function() {
            var b2 = this.axes, c2 = this.colorAxis, e2 = this.renderer, d2 = this.options, a2 = 0, g2 = function(b3) {
              b3.forEach(function(b4) {
                b4.visible && b4.render();
              });
            };
            this.setTitle();
            this.legend = new n2(this, d2.legend);
            this.getStacks && this.getStacks();
            this.getMargins(true);
            this.setChartSize();
            d2 = this.plotWidth;
            b2.some(function(b3) {
              if (b3.horiz && b3.visible && b3.options.labels.enabled && b3.series.length) return a2 = 21, true;
            });
            var h2 = this.plotHeight = Math.max(this.plotHeight - a2, 0);
            b2.forEach(function(b3) {
              b3.setScale();
            });
            this.getAxisMargins();
            var k2 = 1.1 < d2 / this.plotWidth;
            var l2 = 1.05 < h2 / this.plotHeight;
            if (k2 || l2) b2.forEach(function(b3) {
              (b3.horiz && k2 || !b3.horiz && l2) && b3.setTickInterval(true);
            }), this.getMargins();
            this.drawChartBox();
            this.hasCartesianSeries ? g2(b2) : c2 && c2.length && g2(c2);
            this.seriesGroup || (this.seriesGroup = e2.g("series-group").attr({ zIndex: 3 }).add());
            this.renderSeries();
            this.renderLabels();
            this.addCredits();
            this.setResponsive && this.setResponsive();
            this.updateContainerScaling();
            this.hasRendered = true;
          };
          D2.prototype.addCredits = function(b2) {
            var c2 = this, d2 = e(true, this.options.credits, b2);
            d2.enabled && !this.credits && (this.credits = this.renderer.text(d2.text + (this.mapCredits || ""), 0, 0).addClass("highcharts-credits").on("click", function() {
              d2.href && (q.location.href = d2.href);
            }).attr({ align: d2.position.align, zIndex: 8 }), c2.styledMode || this.credits.css(d2.style), this.credits.add().align(d2.position), this.credits.update = function(b3) {
              c2.credits = c2.credits.destroy();
              c2.addCredits(b3);
            });
          };
          D2.prototype.updateContainerScaling = function() {
            var b2 = this.container;
            if (2 < b2.offsetWidth && 2 < b2.offsetHeight && b2.getBoundingClientRect) {
              var c2 = b2.getBoundingClientRect(), e2 = c2.width / b2.offsetWidth;
              b2 = c2.height / b2.offsetHeight;
              1 !== e2 || 1 !== b2 ? this.containerScaling = { scaleX: e2, scaleY: b2 } : delete this.containerScaling;
            }
          };
          D2.prototype.destroy = function() {
            var c2 = this, e2 = c2.axes, d2 = c2.series, g2 = c2.container, h2, f2 = g2 && g2.parentNode;
            r(c2, "destroy");
            c2.renderer.forExport ? k(H, c2) : H[c2.index] = void 0;
            a.chartCount--;
            c2.renderTo.removeAttribute("data-highcharts-chart");
            Z(c2);
            for (h2 = e2.length; h2--; ) e2[h2] = e2[h2].destroy();
            this.scroller && this.scroller.destroy && this.scroller.destroy();
            for (h2 = d2.length; h2--; ) d2[h2] = d2[h2].destroy();
            "title subtitle chartBackground plotBackground plotBGImage plotBorder seriesGroup clipRect credits pointer rangeSelector legend resetZoomButton tooltip renderer".split(" ").forEach(function(b2) {
              var e3 = c2[b2];
              e3 && e3.destroy && (c2[b2] = e3.destroy());
            });
            g2 && (g2.innerHTML = "", Z(g2), f2 && l(g2));
            b(c2, function(b2, e3) {
              delete c2[e3];
            });
          };
          D2.prototype.firstRender = function() {
            var b2 = this, c2 = b2.options;
            if (!b2.isReadyToRender || b2.isReadyToRender()) {
              b2.getContainer();
              b2.resetMargins();
              b2.setChartSize();
              b2.propFromSeries();
              b2.getAxes();
              (N(c2.series) ? c2.series : []).forEach(function(c3) {
                b2.initSeries(c3);
              });
              b2.linkSeries();
              b2.setSeriesData();
              r(b2, "beforeRender");
              G && (b2.pointer = a.hasTouch || !q.PointerEvent && !q.MSPointerEvent ? new G(b2, c2) : new y(b2, c2));
              b2.render();
              if (!b2.renderer.imgCount && !b2.hasLoaded) b2.onload();
              b2.temporaryDisplay(true);
            }
          };
          D2.prototype.onload = function() {
            this.callbacks.concat([this.callback]).forEach(function(b2) {
              b2 && "undefined" !== typeof this.index && b2.apply(this, [this]);
            }, this);
            r(this, "load");
            r(this, "render");
            h(this.index) && this.setReflow(this.options.chart.reflow);
            this.hasLoaded = true;
          };
          return D2;
        })();
        Y.prototype.callbacks = [];
        a.chart = function(b2, c2, e2) {
          return new Y(b2, c2, e2);
        };
        return a.Chart = Y;
      });
      O(n, "Extensions/ScrollablePlotArea.js", [n["Core/Chart/Chart.js"], n["Core/Globals.js"], n["Core/Utilities.js"]], function(f, a, n2) {
        var y = n2.addEvent, D = n2.createElement, G = n2.pick, C = n2.stop;
        "";
        y(f, "afterSetChartSize", function(f2) {
          var n3 = this.options.chart.scrollablePlotArea, v = n3 && n3.minWidth;
          n3 = n3 && n3.minHeight;
          if (!this.renderer.forExport) {
            if (v) {
              if (this.scrollablePixelsX = v = Math.max(0, v - this.chartWidth)) {
                this.plotWidth += v;
                this.inverted ? (this.clipBox.height += v, this.plotBox.height += v) : (this.clipBox.width += v, this.plotBox.width += v);
                var C2 = { 1: { name: "right", value: v } };
              }
            } else n3 && (this.scrollablePixelsY = v = Math.max(0, n3 - this.chartHeight)) && (this.plotHeight += v, this.inverted ? (this.clipBox.width += v, this.plotBox.width += v) : (this.clipBox.height += v, this.plotBox.height += v), C2 = { 2: { name: "bottom", value: v } });
            C2 && !f2.skipAxes && this.axes.forEach(function(f3) {
              C2[f3.side] ? f3.getPlotLinePath = function() {
                var q = C2[f3.side].name, n4 = this[q];
                this[q] = n4 - C2[f3.side].value;
                var p = a.Axis.prototype.getPlotLinePath.apply(this, arguments);
                this[q] = n4;
                return p;
              } : (f3.setAxisSize(), f3.setAxisTranslation());
            });
          }
        });
        y(f, "render", function() {
          this.scrollablePixelsX || this.scrollablePixelsY ? (this.setUpScrolling && this.setUpScrolling(), this.applyFixed()) : this.fixedDiv && this.applyFixed();
        });
        f.prototype.setUpScrolling = function() {
          var a2 = this, f2 = { WebkitOverflowScrolling: "touch", overflowX: "hidden", overflowY: "hidden" };
          this.scrollablePixelsX && (f2.overflowX = "auto");
          this.scrollablePixelsY && (f2.overflowY = "auto");
          this.scrollingParent = D("div", { className: "highcharts-scrolling-parent" }, { position: "relative" }, this.renderTo);
          this.scrollingContainer = D("div", { className: "highcharts-scrolling" }, f2, this.scrollingParent);
          y(this.scrollingContainer, "scroll", function() {
            a2.pointer && delete a2.pointer.chartPosition;
          });
          this.innerContainer = D("div", { className: "highcharts-inner-container" }, null, this.scrollingContainer);
          this.innerContainer.appendChild(this.container);
          this.setUpScrolling = null;
        };
        f.prototype.moveFixedElements = function() {
          var a2 = this.container, f2 = this.fixedRenderer, n3 = ".highcharts-contextbutton .highcharts-credits .highcharts-legend .highcharts-legend-checkbox .highcharts-navigator-series .highcharts-navigator-xaxis .highcharts-navigator-yaxis .highcharts-navigator .highcharts-reset-zoom .highcharts-scrollbar .highcharts-subtitle .highcharts-title".split(" "), C2;
          this.scrollablePixelsX && !this.inverted ? C2 = ".highcharts-yaxis" : this.scrollablePixelsX && this.inverted ? C2 = ".highcharts-xaxis" : this.scrollablePixelsY && !this.inverted ? C2 = ".highcharts-xaxis" : this.scrollablePixelsY && this.inverted && (C2 = ".highcharts-yaxis");
          n3.push(C2, C2 + "-labels");
          n3.forEach(function(q) {
            [].forEach.call(a2.querySelectorAll(q), function(a3) {
              (a3.namespaceURI === f2.SVG_NS ? f2.box : f2.box.parentNode).appendChild(a3);
              a3.style.pointerEvents = "auto";
            });
          });
        };
        f.prototype.applyFixed = function() {
          var f2, n3, v = !this.fixedDiv, L = this.options.chart.scrollablePlotArea;
          v ? (this.fixedDiv = D("div", { className: "highcharts-fixed" }, {
            position: "absolute",
            overflow: "hidden",
            pointerEvents: "none",
            zIndex: 2,
            top: 0
          }, null, true), null === (f2 = this.scrollingContainer) || void 0 === f2 ? void 0 : f2.parentNode.insertBefore(this.fixedDiv, this.scrollingContainer), this.renderTo.style.overflow = "visible", this.fixedRenderer = f2 = new a.Renderer(this.fixedDiv, this.chartWidth, this.chartHeight, null === (n3 = this.options.chart) || void 0 === n3 ? void 0 : n3.style), this.scrollableMask = f2.path().attr({ fill: this.options.chart.backgroundColor || "#fff", "fill-opacity": G(L.opacity, 0.85), zIndex: -1 }).addClass("highcharts-scrollable-mask").add(), this.moveFixedElements(), y(this, "afterShowResetZoom", this.moveFixedElements), y(this, "afterLayOutTitles", this.moveFixedElements)) : this.fixedRenderer.setSize(this.chartWidth, this.chartHeight);
          n3 = this.chartWidth + (this.scrollablePixelsX || 0);
          f2 = this.chartHeight + (this.scrollablePixelsY || 0);
          C(this.container);
          this.container.style.width = n3 + "px";
          this.container.style.height = f2 + "px";
          this.renderer.boxWrapper.attr({ width: n3, height: f2, viewBox: [0, 0, n3, f2].join(" ") });
          this.chartBackground.attr({ width: n3, height: f2 });
          this.scrollingContainer.style.height = this.chartHeight + "px";
          v && (L.scrollPositionX && (this.scrollingContainer.scrollLeft = this.scrollablePixelsX * L.scrollPositionX), L.scrollPositionY && (this.scrollingContainer.scrollTop = this.scrollablePixelsY * L.scrollPositionY));
          f2 = this.axisOffset;
          v = this.plotTop - f2[0] - 1;
          L = this.plotLeft - f2[3] - 1;
          n3 = this.plotTop + this.plotHeight + f2[2] + 1;
          f2 = this.plotLeft + this.plotWidth + f2[1] + 1;
          var q = this.plotLeft + this.plotWidth - (this.scrollablePixelsX || 0), K = this.plotTop + this.plotHeight - (this.scrollablePixelsY || 0);
          v = this.scrollablePixelsX ? [["M", 0, v], ["L", this.plotLeft - 1, v], ["L", this.plotLeft - 1, n3], ["L", 0, n3], ["Z"], ["M", q, v], ["L", this.chartWidth, v], ["L", this.chartWidth, n3], ["L", q, n3], ["Z"]] : this.scrollablePixelsY ? [["M", L, 0], ["L", L, this.plotTop - 1], ["L", f2, this.plotTop - 1], ["L", f2, 0], ["Z"], ["M", L, K], ["L", L, this.chartHeight], ["L", f2, this.chartHeight], ["L", f2, K], ["Z"]] : [["M", 0, 0]];
          "adjustHeight" !== this.redrawTrigger && this.scrollableMask.attr({ d: v });
        };
      });
      O(n, "Core/Axis/StackingAxis.js", [n["Core/Utilities.js"]], function(f) {
        var a = f.addEvent, n2 = f.destroyObjectProperties, y = f.fireEvent, D = f.getDeferredAnimation, G = f.objectEach, C = f.pick, J = (function() {
          function a2(a3) {
            this.oldStacks = {};
            this.stacks = {};
            this.stacksTouched = 0;
            this.axis = a3;
          }
          a2.prototype.buildStacks = function() {
            var a3 = this.axis, f2 = a3.series, q = C(a3.options.reversedStacks, true), n3 = f2.length, E;
            if (!a3.isXAxis) {
              this.usePercentage = false;
              for (E = n3; E--; ) {
                var p = f2[q ? E : n3 - E - 1];
                p.setStackedPoints();
                p.setGroupedPoints();
              }
              for (E = 0; E < n3; E++) f2[E].modifyStacks();
              y(a3, "afterBuildStacks");
            }
          };
          a2.prototype.cleanStacks = function() {
            if (!this.axis.isXAxis) {
              if (this.oldStacks) var a3 = this.stacks = this.oldStacks;
              G(a3, function(a4) {
                G(a4, function(a5) {
                  a5.cumulative = a5.total;
                });
              });
            }
          };
          a2.prototype.resetStacks = function() {
            var a3 = this, f2 = a3.stacks;
            a3.axis.isXAxis || G(f2, function(f3) {
              G(f3, function(q, n3) {
                q.touched < a3.stacksTouched ? (q.destroy(), delete f3[n3]) : (q.total = null, q.cumulative = null);
              });
            });
          };
          a2.prototype.renderStackTotals = function() {
            var a3 = this.axis, f2 = a3.chart, q = f2.renderer, n3 = this.stacks;
            a3 = D(f2, a3.options.stackLabels.animation);
            var E = this.stackTotalGroup = this.stackTotalGroup || q.g("stack-labels").attr({
              visibility: "visible",
              zIndex: 6,
              opacity: 0
            }).add();
            E.translate(f2.plotLeft, f2.plotTop);
            G(n3, function(a4) {
              G(a4, function(a5) {
                a5.render(E);
              });
            });
            E.animate({ opacity: 1 }, a3);
          };
          return a2;
        })();
        return (function() {
          function f2() {
          }
          f2.compose = function(n3) {
            a(n3, "init", f2.onInit);
            a(n3, "destroy", f2.onDestroy);
          };
          f2.onDestroy = function() {
            var a2 = this.stacking;
            if (a2) {
              var f3 = a2.stacks;
              G(f3, function(a3, v) {
                n2(a3);
                f3[v] = null;
              });
              a2 && a2.stackTotalGroup && a2.stackTotalGroup.destroy();
            }
          };
          f2.onInit = function() {
            this.stacking || (this.stacking = new J(this));
          };
          return f2;
        })();
      });
      O(
        n,
        "Mixins/LegendSymbol.js",
        [n["Core/Globals.js"], n["Core/Utilities.js"]],
        function(f, a) {
          var n2 = a.merge, y = a.pick;
          return f.LegendSymbolMixin = { drawRectangle: function(a2, f2) {
            var n3 = a2.symbolHeight, D = a2.options.squareSymbol;
            f2.legendSymbol = this.chart.renderer.rect(D ? (a2.symbolWidth - n3) / 2 : 0, a2.baseline - n3 + 1, D ? n3 : a2.symbolWidth, n3, y(a2.options.symbolRadius, n3 / 2)).addClass("highcharts-point").attr({ zIndex: 3 }).add(f2.legendGroup);
          }, drawLineMarker: function(a2) {
            var f2 = this.options, C = f2.marker, D = a2.symbolWidth, H = a2.symbolHeight, v = H / 2, L = this.chart.renderer, q = this.legendGroup;
            a2 = a2.baseline - Math.round(0.3 * a2.fontMetrics.b);
            var K = {};
            this.chart.styledMode || (K = { "stroke-width": f2.lineWidth || 0 }, f2.dashStyle && (K.dashstyle = f2.dashStyle));
            this.legendLine = L.path([["M", 0, a2], ["L", D, a2]]).addClass("highcharts-graph").attr(K).add(q);
            C && false !== C.enabled && D && (f2 = Math.min(y(C.radius, v), v), 0 === this.symbol.indexOf("url") && (C = n2(C, { width: H, height: H }), f2 = 0), this.legendSymbol = C = L.symbol(this.symbol, D / 2 - f2, a2 - f2, 2 * f2, 2 * f2, C).addClass("highcharts-point").add(q), C.isMarker = true);
          } };
        }
      );
      O(
        n,
        "Core/Series/Point.js",
        [n["Core/Globals.js"], n["Core/Utilities.js"]],
        function(f, a) {
          var n2 = a.animObject, y = a.defined, D = a.erase, G = a.extend, C = a.fireEvent, J = a.format, H = a.getNestedProperty, v = a.isArray, L = a.isNumber, q = a.isObject, K = a.syncTimeout, E = a.pick, p = a.removeEvent, t = a.uniqueKey;
          "";
          a = (function() {
            function a2() {
              this.colorIndex = this.category = void 0;
              this.formatPrefix = "point";
              this.id = void 0;
              this.isNull = false;
              this.percentage = this.options = this.name = void 0;
              this.selected = false;
              this.total = this.series = void 0;
              this.visible = true;
              this.x = void 0;
            }
            a2.prototype.animateBeforeDestroy = function() {
              var a3 = this, f2 = { x: a3.startXPos, opacity: 0 }, h, l = a3.getGraphicalProps();
              l.singular.forEach(function(k) {
                h = "dataLabel" === k;
                a3[k] = a3[k].animate(h ? { x: a3[k].startXPos, y: a3[k].startYPos, opacity: 0 } : f2);
              });
              l.plural.forEach(function(h2) {
                a3[h2].forEach(function(g) {
                  g.element && g.animate(G({ x: a3.startXPos }, g.startYPos ? { x: g.startXPos, y: g.startYPos } : {}));
                });
              });
            };
            a2.prototype.applyOptions = function(f2, m) {
              var h = this.series, l = h.options.pointValKey || h.pointValKey;
              f2 = a2.prototype.optionsToObject.call(this, f2);
              G(this, f2);
              this.options = this.options ? G(this.options, f2) : f2;
              f2.group && delete this.group;
              f2.dataLabels && delete this.dataLabels;
              l && (this.y = a2.prototype.getNestedProperty.call(this, l));
              this.formatPrefix = (this.isNull = E(this.isValid && !this.isValid(), null === this.x || !L(this.y))) ? "null" : "point";
              this.selected && (this.state = "select");
              "name" in this && "undefined" === typeof m && h.xAxis && h.xAxis.hasNames && (this.x = h.xAxis.nameToX(this));
              "undefined" === typeof this.x && h && (this.x = "undefined" === typeof m ? h.autoIncrement(this) : m);
              return this;
            };
            a2.prototype.destroy = function() {
              function a3() {
                if (f2.graphic || f2.dataLabel || f2.dataLabels) p(f2), f2.destroyElements();
                for (d in f2) f2[d] = null;
              }
              var f2 = this, h = f2.series, l = h.chart;
              h = h.options.dataSorting;
              var k = l.hoverPoints, g = n2(f2.series.chart.renderer.globalAnimation), d;
              f2.legendItem && l.legend.destroyItem(f2);
              k && (f2.setState(), D(k, f2), k.length || (l.hoverPoints = null));
              if (f2 === l.hoverPoint) f2.onMouseOut();
              h && h.enabled ? (this.animateBeforeDestroy(), K(a3, g.duration)) : a3();
              l.pointCount--;
            };
            a2.prototype.destroyElements = function(a3) {
              var f2 = this;
              a3 = f2.getGraphicalProps(a3);
              a3.singular.forEach(function(a4) {
                f2[a4] = f2[a4].destroy();
              });
              a3.plural.forEach(function(a4) {
                f2[a4].forEach(function(a5) {
                  a5.element && a5.destroy();
                });
                delete f2[a4];
              });
            };
            a2.prototype.firePointEvent = function(a3, f2, h) {
              var l = this, k = this.series.options;
              (k.point.events[a3] || l.options && l.options.events && l.options.events[a3]) && l.importEvents();
              "click" === a3 && k.allowPointSelect && (h = function(a4) {
                l.select && l.select(null, a4.ctrlKey || a4.metaKey || a4.shiftKey);
              });
              C(l, a3, f2, h);
            };
            a2.prototype.getClassName = function() {
              return "highcharts-point" + (this.selected ? " highcharts-point-select" : "") + (this.negative ? " highcharts-negative" : "") + (this.isNull ? " highcharts-null-point" : "") + ("undefined" !== typeof this.colorIndex ? " highcharts-color-" + this.colorIndex : "") + (this.options.className ? " " + this.options.className : "") + (this.zone && this.zone.className ? " " + this.zone.className.replace("highcharts-negative", "") : "");
            };
            a2.prototype.getGraphicalProps = function(a3) {
              var f2 = this, h = [], l, k = { singular: [], plural: [] };
              a3 = a3 || { graphic: 1, dataLabel: 1 };
              a3.graphic && h.push("graphic", "shadowGroup");
              a3.dataLabel && h.push("dataLabel", "dataLabelUpper", "connector");
              for (l = h.length; l--; ) {
                var g = h[l];
                f2[g] && k.singular.push(g);
              }
              ["dataLabel", "connector"].forEach(function(d) {
                var g2 = d + "s";
                a3[d] && f2[g2] && k.plural.push(g2);
              });
              return k;
            };
            a2.prototype.getLabelConfig = function() {
              return { x: this.category, y: this.y, color: this.color, colorIndex: this.colorIndex, key: this.name || this.category, series: this.series, point: this, percentage: this.percentage, total: this.total || this.stackTotal };
            };
            a2.prototype.getNestedProperty = function(a3) {
              if (a3) return 0 === a3.indexOf("custom.") ? H(a3, this.options) : this[a3];
            };
            a2.prototype.getZone = function() {
              var a3 = this.series, f2 = a3.zones;
              a3 = a3.zoneAxis || "y";
              var h = 0, l;
              for (l = f2[h]; this[a3] >= l.value; ) l = f2[++h];
              this.nonZonedColor || (this.nonZonedColor = this.color);
              this.color = l && l.color && !this.options.color ? l.color : this.nonZonedColor;
              return l;
            };
            a2.prototype.hasNewShapeType = function() {
              return (this.graphic && (this.graphic.symbolName || this.graphic.element.nodeName)) !== this.shapeType;
            };
            a2.prototype.init = function(a3, f2, h) {
              this.series = a3;
              this.applyOptions(
                f2,
                h
              );
              this.id = y(this.id) ? this.id : t();
              this.resolveColor();
              a3.chart.pointCount++;
              C(this, "afterInit");
              return this;
            };
            a2.prototype.optionsToObject = function(f2) {
              var m = {}, h = this.series, l = h.options.keys, k = l || h.pointArrayMap || ["y"], g = k.length, d = 0, p2 = 0;
              if (L(f2) || null === f2) m[k[0]] = f2;
              else if (v(f2)) for (!l && f2.length > g && (h = typeof f2[0], "string" === h ? m.name = f2[0] : "number" === h && (m.x = f2[0]), d++); p2 < g; ) l && "undefined" === typeof f2[d] || (0 < k[p2].indexOf(".") ? a2.prototype.setNestedProperty(m, f2[d], k[p2]) : m[k[p2]] = f2[d]), d++, p2++;
              else "object" === typeof f2 && (m = f2, f2.dataLabels && (h._hasPointLabels = true), f2.marker && (h._hasPointMarkers = true));
              return m;
            };
            a2.prototype.resolveColor = function() {
              var a3 = this.series;
              var f2 = a3.chart.options.chart.colorCount;
              var h = a3.chart.styledMode;
              delete this.nonZonedColor;
              h || this.options.color || (this.color = a3.color);
              a3.options.colorByPoint ? (h || (f2 = a3.options.colors || a3.chart.options.colors, this.color = this.color || f2[a3.colorCounter], f2 = f2.length), h = a3.colorCounter, a3.colorCounter++, a3.colorCounter === f2 && (a3.colorCounter = 0)) : h = a3.colorIndex;
              this.colorIndex = E(this.colorIndex, h);
            };
            a2.prototype.setNestedProperty = function(a3, f2, h) {
              h.split(".").reduce(function(a4, h2, g, d) {
                a4[h2] = d.length - 1 === g ? f2 : q(a4[h2], true) ? a4[h2] : {};
                return a4[h2];
              }, a3);
              return a3;
            };
            a2.prototype.tooltipFormatter = function(a3) {
              var f2 = this.series, h = f2.tooltipOptions, l = E(h.valueDecimals, ""), k = h.valuePrefix || "", g = h.valueSuffix || "";
              f2.chart.styledMode && (a3 = f2.chart.tooltip.styledModeFormat(a3));
              (f2.pointArrayMap || ["y"]).forEach(function(d) {
                d = "{point." + d;
                if (k || g) a3 = a3.replace(RegExp(d + "}", "g"), k + d + "}" + g);
                a3 = a3.replace(RegExp(d + "}", "g"), d + ":,." + l + "f}");
              });
              return J(a3, { point: this, series: this.series }, f2.chart);
            };
            return a2;
          })();
          return f.Point = a;
        }
      );
      O(n, "Core/Series/Series.js", [n["Core/Globals.js"], n["Mixins/LegendSymbol.js"], n["Core/Options.js"], n["Core/Series/Point.js"], n["Core/Renderer/SVG/SVGElement.js"], n["Core/Utilities.js"]], function(f, a, n2, y, D, G) {
        var C = n2.defaultOptions, J = G.addEvent, H = G.animObject, v = G.arrayMax, L = G.arrayMin, q = G.clamp, K = G.correctFloat, E = G.defined, p = G.erase, t = G.error, I = G.extend, u = G.find, m = G.fireEvent, h = G.getNestedProperty, l = G.isArray, k = G.isFunction, g = G.isNumber, d = G.isString, x = G.merge, r = G.objectEach, A = G.pick, N = G.removeEvent;
        n2 = G.seriesType;
        var B = G.splat, M = G.syncTimeout;
        "";
        var R = f.seriesTypes, F = f.win;
        f.Series = n2("line", null, { lineWidth: 2, allowPointSelect: false, crisp: true, showCheckbox: false, animation: { duration: 1e3 }, events: {}, marker: { enabledThreshold: 2, lineColor: "#ffffff", lineWidth: 0, radius: 4, states: { normal: { animation: true }, hover: { animation: { duration: 50 }, enabled: true, radiusPlus: 2, lineWidthPlus: 1 }, select: {
          fillColor: "#cccccc",
          lineColor: "#000000",
          lineWidth: 2
        } } }, point: { events: {} }, dataLabels: { animation: {}, align: "center", defer: true, formatter: function() {
          var e = this.series.chart.numberFormatter;
          return "number" !== typeof this.y ? "" : e(this.y, -1);
        }, padding: 5, style: { fontSize: "11px", fontWeight: "bold", color: "contrast", textOutline: "1px contrast" }, verticalAlign: "bottom", x: 0, y: 0 }, cropThreshold: 300, opacity: 1, pointRange: 0, softThreshold: true, states: {
          normal: { animation: true },
          hover: { animation: { duration: 50 }, lineWidthPlus: 1, marker: {}, halo: { size: 10, opacity: 0.25 } },
          select: { animation: { duration: 0 } },
          inactive: { animation: { duration: 50 }, opacity: 0.2 }
        }, stickyTracking: true, turboThreshold: 1e3, findNearestPointBy: "x" }, {
          axisTypes: ["xAxis", "yAxis"],
          coll: "series",
          colorCounter: 0,
          cropShoulder: 1,
          directTouch: false,
          isCartesian: true,
          parallelArrays: ["x", "y"],
          pointClass: y,
          requireSorting: true,
          sorted: true,
          init: function(e, c) {
            m(this, "init", { options: c });
            var b = this, a2 = e.series, d2;
            this.eventOptions = this.eventOptions || {};
            this.eventsToUnbind = [];
            b.chart = e;
            b.options = c = b.setOptions(c);
            b.linkedSeries = [];
            b.bindAxes();
            I(b, {
              name: c.name,
              state: "",
              visible: false !== c.visible,
              selected: true === c.selected
            });
            var g2 = c.events;
            r(g2, function(c2, e2) {
              k(c2) && b.eventOptions[e2] !== c2 && (k(b.eventOptions[e2]) && N(b, e2, b.eventOptions[e2]), b.eventOptions[e2] = c2, J(b, e2, c2));
            });
            if (g2 && g2.click || c.point && c.point.events && c.point.events.click || c.allowPointSelect) e.runTrackerClick = true;
            b.getColor();
            b.getSymbol();
            b.parallelArrays.forEach(function(c2) {
              b[c2 + "Data"] || (b[c2 + "Data"] = []);
            });
            b.isCartesian && (e.hasCartesianSeries = true);
            a2.length && (d2 = a2[a2.length - 1]);
            b._i = A(d2 && d2._i, -1) + 1;
            b.opacity = b.options.opacity;
            e.orderSeries(this.insert(a2));
            c.dataSorting && c.dataSorting.enabled ? b.setDataSortingOptions() : b.points || b.data || b.setData(c.data, false);
            m(this, "afterInit");
          },
          is: function(e) {
            return R[e] && this instanceof R[e];
          },
          insert: function(e) {
            var c = this.options.index, b;
            if (g(c)) {
              for (b = e.length; b--; ) if (c >= A(e[b].options.index, e[b]._i)) {
                e.splice(b + 1, 0, this);
                break;
              }
              -1 === b && e.unshift(this);
              b += 1;
            } else e.push(this);
            return A(b, e.length - 1);
          },
          bindAxes: function() {
            var e = this, c = e.options, b = e.chart, a2;
            m(this, "bindAxes", null, function() {
              (e.axisTypes || []).forEach(function(d2) {
                b[d2].forEach(function(b2) {
                  a2 = b2.options;
                  if (c[d2] === a2.index || "undefined" !== typeof c[d2] && c[d2] === a2.id || "undefined" === typeof c[d2] && 0 === a2.index) e.insert(b2.series), e[d2] = b2, b2.isDirty = true;
                });
                e[d2] || e.optionalAxis === d2 || t(18, true, b);
              });
            });
            m(this, "afterBindAxes");
          },
          updateParallelArrays: function(e, c) {
            var b = e.series, a2 = arguments, d2 = g(c) ? function(a3) {
              var d3 = "y" === a3 && b.toYData ? b.toYData(e) : e[a3];
              b[a3 + "Data"][c] = d3;
            } : function(e2) {
              Array.prototype[c].apply(b[e2 + "Data"], Array.prototype.slice.call(a2, 2));
            };
            b.parallelArrays.forEach(d2);
          },
          hasData: function() {
            return this.visible && "undefined" !== typeof this.dataMax && "undefined" !== typeof this.dataMin || this.visible && this.yData && 0 < this.yData.length;
          },
          autoIncrement: function() {
            var e = this.options, c = this.xIncrement, b, a2 = e.pointIntervalUnit, d2 = this.chart.time;
            c = A(c, e.pointStart, 0);
            this.pointInterval = b = A(this.pointInterval, e.pointInterval, 1);
            a2 && (e = new d2.Date(c), "day" === a2 ? d2.set("Date", e, d2.get("Date", e) + b) : "month" === a2 ? d2.set("Month", e, d2.get("Month", e) + b) : "year" === a2 && d2.set("FullYear", e, d2.get(
              "FullYear",
              e
            ) + b), b = e.getTime() - c);
            this.xIncrement = c + b;
            return c;
          },
          setDataSortingOptions: function() {
            var e = this.options;
            I(this, { requireSorting: false, sorted: false, enabledDataSorting: true, allowDG: false });
            E(e.pointRange) || (e.pointRange = 1);
          },
          setOptions: function(e) {
            var c = this.chart, b = c.options, a2 = b.plotOptions, d2 = c.userOptions || {};
            e = x(e);
            c = c.styledMode;
            var g2 = { plotOptions: a2, userOptions: e };
            m(this, "setOptions", g2);
            var h2 = g2.plotOptions[this.type], f2 = d2.plotOptions || {};
            this.userOptions = g2.userOptions;
            d2 = x(
              h2,
              a2.series,
              d2.plotOptions && d2.plotOptions[this.type],
              e
            );
            this.tooltipOptions = x(C.tooltip, C.plotOptions.series && C.plotOptions.series.tooltip, C.plotOptions[this.type].tooltip, b.tooltip.userOptions, a2.series && a2.series.tooltip, a2[this.type].tooltip, e.tooltip);
            this.stickyTracking = A(e.stickyTracking, f2[this.type] && f2[this.type].stickyTracking, f2.series && f2.series.stickyTracking, this.tooltipOptions.shared && !this.noSharedTooltip ? true : d2.stickyTracking);
            null === h2.marker && delete d2.marker;
            this.zoneAxis = d2.zoneAxis;
            b = this.zones = (d2.zones || []).slice();
            !d2.negativeColor && !d2.negativeFillColor || d2.zones || (a2 = { value: d2[this.zoneAxis + "Threshold"] || d2.threshold || 0, className: "highcharts-negative" }, c || (a2.color = d2.negativeColor, a2.fillColor = d2.negativeFillColor), b.push(a2));
            b.length && E(b[b.length - 1].value) && b.push(c ? {} : { color: this.color, fillColor: this.fillColor });
            m(this, "afterSetOptions", { options: d2 });
            return d2;
          },
          getName: function() {
            return A(this.options.name, "Series " + (this.index + 1));
          },
          getCyclic: function(e, c, b) {
            var a2 = this.chart, d2 = this.userOptions, g2 = e + "Index", h2 = e + "Counter", f2 = b ? b.length : A(a2.options.chart[e + "Count"], a2[e + "Count"]);
            if (!c) {
              var k2 = A(d2[g2], d2["_" + g2]);
              E(k2) || (a2.series.length || (a2[h2] = 0), d2["_" + g2] = k2 = a2[h2] % f2, a2[h2] += 1);
              b && (c = b[k2]);
            }
            "undefined" !== typeof k2 && (this[g2] = k2);
            this[e] = c;
          },
          getColor: function() {
            this.chart.styledMode ? this.getCyclic("color") : this.options.colorByPoint ? this.options.color = null : this.getCyclic("color", this.options.color || C.plotOptions[this.type].color, this.chart.options.colors);
          },
          getPointsCollection: function() {
            return (this.hasGroupedData ? this.points : this.data) || [];
          },
          getSymbol: function() {
            this.getCyclic(
              "symbol",
              this.options.marker.symbol,
              this.chart.options.symbols
            );
          },
          findPointIndex: function(e, c) {
            var b = e.id, a2 = e.x, d2 = this.points, h2, f2 = this.options.dataSorting;
            if (b) var k2 = this.chart.get(b);
            else if (this.linkedParent || this.enabledDataSorting) {
              var l2 = f2 && f2.matchByName ? "name" : "index";
              k2 = u(d2, function(b2) {
                return !b2.touched && b2[l2] === e[l2];
              });
              if (!k2) return;
            }
            if (k2) {
              var m2 = k2 && k2.index;
              "undefined" !== typeof m2 && (h2 = true);
            }
            "undefined" === typeof m2 && g(a2) && (m2 = this.xData.indexOf(a2, c));
            -1 !== m2 && "undefined" !== typeof m2 && this.cropped && (m2 = m2 >= this.cropStart ? m2 - this.cropStart : m2);
            !h2 && d2[m2] && d2[m2].touched && (m2 = void 0);
            return m2;
          },
          drawLegendSymbol: a.drawLineMarker,
          updateData: function(e, c) {
            var b = this.options, a2 = b.dataSorting, d2 = this.points, h2 = [], f2, k2, l2, m2 = this.requireSorting, p2 = e.length === d2.length, r2 = true;
            this.xIncrement = null;
            e.forEach(function(c2, e2) {
              var k3 = E(c2) && this.pointClass.prototype.optionsToObject.call({ series: this }, c2) || {};
              var w = k3.x;
              if (k3.id || g(w)) {
                if (w = this.findPointIndex(k3, l2), -1 === w || "undefined" === typeof w ? h2.push(c2) : d2[w] && c2 !== b.data[w] ? (d2[w].update(c2, false, null, false), d2[w].touched = true, m2 && (l2 = w + 1)) : d2[w] && (d2[w].touched = true), !p2 || e2 !== w || a2 && a2.enabled || this.hasDerivedData) f2 = true;
              } else h2.push(c2);
            }, this);
            if (f2) for (e = d2.length; e--; ) (k2 = d2[e]) && !k2.touched && k2.remove && k2.remove(false, c);
            else !p2 || a2 && a2.enabled ? r2 = false : (e.forEach(function(b2, c2) {
              d2[c2].update && b2 !== d2[c2].y && d2[c2].update(b2, false, null, false);
            }), h2.length = 0);
            d2.forEach(function(b2) {
              b2 && (b2.touched = false);
            });
            if (!r2) return false;
            h2.forEach(function(b2) {
              this.addPoint(b2, false, null, null, false);
            }, this);
            null === this.xIncrement && this.xData && this.xData.length && (this.xIncrement = v(this.xData), this.autoIncrement());
            return true;
          },
          setData: function(e, c, b, a2) {
            var h2 = this, f2 = h2.points, k2 = f2 && f2.length || 0, m2, p2 = h2.options, r2 = h2.chart, B2 = p2.dataSorting, z = null, n3 = h2.xAxis;
            z = p2.turboThreshold;
            var q2 = this.xData, x2 = this.yData, F2 = (m2 = h2.pointArrayMap) && m2.length, M2 = p2.keys, v2 = 0, u2 = 1, I2;
            e = e || [];
            m2 = e.length;
            c = A(c, true);
            B2 && B2.enabled && (e = this.sortData(e));
            false !== a2 && m2 && k2 && !h2.cropped && !h2.hasGroupedData && h2.visible && !h2.isSeriesBoosting && (I2 = this.updateData(e, b));
            if (!I2) {
              h2.xIncrement = null;
              h2.colorCounter = 0;
              this.parallelArrays.forEach(function(b2) {
                h2[b2 + "Data"].length = 0;
              });
              if (z && m2 > z) if (z = h2.getFirstValidPoint(e), g(z)) for (b = 0; b < m2; b++) q2[b] = this.autoIncrement(), x2[b] = e[b];
              else if (l(z)) if (F2) for (b = 0; b < m2; b++) a2 = e[b], q2[b] = a2[0], x2[b] = a2.slice(1, F2 + 1);
              else for (M2 && (v2 = M2.indexOf("x"), u2 = M2.indexOf("y"), v2 = 0 <= v2 ? v2 : 0, u2 = 0 <= u2 ? u2 : 1), b = 0; b < m2; b++) a2 = e[b], q2[b] = a2[v2], x2[b] = a2[u2];
              else t(12, false, r2);
              else for (b = 0; b < m2; b++) "undefined" !== typeof e[b] && (a2 = { series: h2 }, h2.pointClass.prototype.applyOptions.apply(a2, [e[b]]), h2.updateParallelArrays(a2, b));
              x2 && d(x2[0]) && t(14, true, r2);
              h2.data = [];
              h2.options.data = h2.userOptions.data = e;
              for (b = k2; b--; ) f2[b] && f2[b].destroy && f2[b].destroy();
              n3 && (n3.minRange = n3.userMinRange);
              h2.isDirty = r2.isDirtyBox = true;
              h2.isDirtyData = !!f2;
              b = false;
            }
            "point" === p2.legendType && (this.processData(), this.generatePoints());
            c && r2.redraw(b);
          },
          sortData: function(e) {
            var c = this, b = c.options.dataSorting.sortKey || "y", a2 = function(b2, c2) {
              return E(c2) && b2.pointClass.prototype.optionsToObject.call({ series: b2 }, c2) || {};
            };
            e.forEach(function(b2, d2) {
              e[d2] = a2(c, b2);
              e[d2].index = d2;
            }, this);
            e.concat().sort(function(c2, e2) {
              c2 = h(b, c2);
              e2 = h(b, e2);
              return e2 < c2 ? -1 : e2 > c2 ? 1 : 0;
            }).forEach(function(b2, c2) {
              b2.x = c2;
            }, this);
            c.linkedSeries && c.linkedSeries.forEach(function(b2) {
              var c2 = b2.options, d2 = c2.data;
              c2.dataSorting && c2.dataSorting.enabled || !d2 || (d2.forEach(function(c3, g2) {
                d2[g2] = a2(b2, c3);
                e[g2] && (d2[g2].x = e[g2].x, d2[g2].index = g2);
              }), b2.setData(d2, false));
            });
            return e;
          },
          getProcessedData: function(e) {
            var c = this.xData, b = this.yData, a2 = c.length;
            var d2 = 0;
            var g2 = this.xAxis, h2 = this.options;
            var f2 = h2.cropThreshold;
            var k2 = e || this.getExtremesFromAll || h2.getExtremesFromAll, l2 = this.isCartesian;
            e = g2 && g2.val2lin;
            h2 = !(!g2 || !g2.logarithmic);
            var m2 = this.requireSorting;
            if (g2) {
              g2 = g2.getExtremes();
              var p2 = g2.min;
              var r2 = g2.max;
            }
            if (l2 && this.sorted && !k2 && (!f2 || a2 > f2 || this.forceCrop)) {
              if (c[a2 - 1] < p2 || c[0] > r2) c = [], b = [];
              else if (this.yData && (c[0] < p2 || c[a2 - 1] > r2)) {
                d2 = this.cropData(this.xData, this.yData, p2, r2);
                c = d2.xData;
                b = d2.yData;
                d2 = d2.start;
                var B2 = true;
              }
            }
            for (f2 = c.length || 1; --f2; ) if (a2 = h2 ? e(c[f2]) - e(c[f2 - 1]) : c[f2] - c[f2 - 1], 0 < a2 && ("undefined" === typeof n3 || a2 < n3)) var n3 = a2;
            else 0 > a2 && m2 && (t(15, false, this.chart), m2 = false);
            return { xData: c, yData: b, cropped: B2, cropStart: d2, closestPointRange: n3 };
          },
          processData: function(e) {
            var c = this.xAxis;
            if (this.isCartesian && !this.isDirty && !c.isDirty && !this.yAxis.isDirty && !e) return false;
            e = this.getProcessedData();
            this.cropped = e.cropped;
            this.cropStart = e.cropStart;
            this.processedXData = e.xData;
            this.processedYData = e.yData;
            this.closestPointRange = this.basePointRange = e.closestPointRange;
          },
          cropData: function(e, c, b, a2, d2) {
            var g2 = e.length, h2 = 0, f2 = g2, k2;
            d2 = A(d2, this.cropShoulder);
            for (k2 = 0; k2 < g2; k2++) if (e[k2] >= b) {
              h2 = Math.max(0, k2 - d2);
              break;
            }
            for (b = k2; b < g2; b++) if (e[b] > a2) {
              f2 = b + d2;
              break;
            }
            return {
              xData: e.slice(h2, f2),
              yData: c.slice(h2, f2),
              start: h2,
              end: f2
            };
          },
          generatePoints: function() {
            var e = this.options, c = e.data, b = this.data, a2, d2 = this.processedXData, g2 = this.processedYData, h2 = this.pointClass, f2 = d2.length, k2 = this.cropStart || 0, l2 = this.hasGroupedData;
            e = e.keys;
            var p2 = [], r2;
            b || l2 || (b = [], b.length = c.length, b = this.data = b);
            e && l2 && (this.options.keys = false);
            for (r2 = 0; r2 < f2; r2++) {
              var t2 = k2 + r2;
              if (l2) {
                var n3 = new h2().init(this, [d2[r2]].concat(B(g2[r2])));
                n3.dataGroup = this.groupMap[r2];
                n3.dataGroup.options && (n3.options = n3.dataGroup.options, I(n3, n3.dataGroup.options), delete n3.dataLabels);
              } else (n3 = b[t2]) || "undefined" === typeof c[t2] || (b[t2] = n3 = new h2().init(this, c[t2], d2[r2]));
              n3 && (n3.index = t2, p2[r2] = n3);
            }
            this.options.keys = e;
            if (b && (f2 !== (a2 = b.length) || l2)) for (r2 = 0; r2 < a2; r2++) r2 !== k2 || l2 || (r2 += f2), b[r2] && (b[r2].destroyElements(), b[r2].plotX = void 0);
            this.data = b;
            this.points = p2;
            m(this, "afterGeneratePoints");
          },
          getXExtremes: function(e) {
            return { min: L(e), max: v(e) };
          },
          getExtremes: function(e, c) {
            var b = this.xAxis, a2 = this.yAxis, d2 = this.processedXData || this.xData, h2 = [], f2 = 0, k2 = 0;
            var p2 = 0;
            var r2 = this.requireSorting ? this.cropShoulder : 0, t2 = a2 ? a2.positiveValuesOnly : false, B2;
            e = e || this.stackedYData || this.processedYData || [];
            a2 = e.length;
            b && (p2 = b.getExtremes(), k2 = p2.min, p2 = p2.max);
            for (B2 = 0; B2 < a2; B2++) {
              var n3 = d2[B2];
              var q2 = e[B2];
              var x2 = (g(q2) || l(q2)) && (q2.length || 0 < q2 || !t2);
              n3 = c || this.getExtremesFromAll || this.options.getExtremesFromAll || this.cropped || !b || (d2[B2 + r2] || n3) >= k2 && (d2[B2 - r2] || n3) <= p2;
              if (x2 && n3) if (x2 = q2.length) for (; x2--; ) g(q2[x2]) && (h2[f2++] = q2[x2]);
              else h2[f2++] = q2;
            }
            e = { dataMin: L(h2), dataMax: v(h2) };
            m(this, "afterGetExtremes", { dataExtremes: e });
            return e;
          },
          applyExtremes: function() {
            var e = this.getExtremes();
            this.dataMin = e.dataMin;
            this.dataMax = e.dataMax;
            return e;
          },
          getFirstValidPoint: function(e) {
            for (var c = null, b = e.length, a2 = 0; null === c && a2 < b; ) c = e[a2], a2++;
            return c;
          },
          translate: function() {
            this.processedXData || this.processData();
            this.generatePoints();
            var e = this.options, c = e.stacking, b = this.xAxis, a2 = b.categories, d2 = this.enabledDataSorting, h2 = this.yAxis, f2 = this.points, k2 = f2.length, p2 = !!this.modifyValue, r2, t2 = this.pointPlacementToXValue(), B2 = !!t2, n3 = e.threshold, x2 = e.startFromThreshold ? n3 : 0, F2, M2 = this.zoneAxis || "y", v2 = Number.MAX_VALUE;
            for (r2 = 0; r2 < k2; r2++) {
              var u2 = f2[r2], I2 = u2.x, C2 = u2.y, H2 = u2.low, R2 = c && h2.stacking && h2.stacking.stacks[(this.negStacks && C2 < (x2 ? 0 : n3) ? "-" : "") + this.stackKey];
              if (h2.positiveValuesOnly && !h2.validatePositiveValue(C2) || b.positiveValuesOnly && !b.validatePositiveValue(I2)) u2.isNull = true;
              u2.plotX = F2 = K(q(b.translate(I2, 0, 0, 0, 1, t2, "flags" === this.type), -1e5, 1e5));
              if (c && this.visible && R2 && R2[I2]) {
                var y2 = this.getStackIndicator(y2, I2, this.index);
                if (!u2.isNull) {
                  var N2 = R2[I2];
                  var D2 = N2.points[y2.key];
                }
              }
              l(D2) && (H2 = D2[0], C2 = D2[1], H2 === x2 && y2.key === R2[I2].base && (H2 = A(g(n3) && n3, h2.min)), h2.positiveValuesOnly && 0 >= H2 && (H2 = null), u2.total = u2.stackTotal = N2.total, u2.percentage = N2.total && u2.y / N2.total * 100, u2.stackY = C2, this.irregularWidths || N2.setOffset(this.pointXOffset || 0, this.barW || 0));
              u2.yBottom = E(H2) ? q(h2.translate(H2, 0, 1, 0, 1), -1e5, 1e5) : null;
              p2 && (C2 = this.modifyValue(C2, u2));
              u2.plotY = "number" === typeof C2 && Infinity !== C2 ? q(h2.translate(C2, 0, 1, 0, 1), -1e5, 1e5) : void 0;
              u2.isInside = this.isPointInside(u2);
              u2.clientX = B2 ? K(b.translate(I2, 0, 0, 0, 1, t2)) : F2;
              u2.negative = u2[M2] < (e[M2 + "Threshold"] || n3 || 0);
              u2.category = a2 && "undefined" !== typeof a2[u2.x] ? a2[u2.x] : u2.x;
              if (!u2.isNull && false !== u2.visible) {
                "undefined" !== typeof G2 && (v2 = Math.min(v2, Math.abs(F2 - G2)));
                var G2 = F2;
              }
              u2.zone = this.zones.length && u2.getZone();
              !u2.graphic && this.group && d2 && (u2.isNew = true);
            }
            this.closestPointRangePx = v2;
            m(this, "afterTranslate");
          },
          getValidPoints: function(e, c, b) {
            var a2 = this.chart;
            return (e || this.points || []).filter(function(e2) {
              return c && !a2.isInsidePlot(e2.plotX, e2.plotY, a2.inverted) ? false : false !== e2.visible && (b || !e2.isNull);
            });
          },
          getClipBox: function(e, c) {
            var b = this.options, a2 = this.chart, d2 = a2.inverted, g2 = this.xAxis, h2 = g2 && this.yAxis, f2 = a2.options.chart.scrollablePlotArea || {};
            e && false === b.clip && h2 ? e = d2 ? { y: -a2.chartWidth + h2.len + h2.pos, height: a2.chartWidth, width: a2.chartHeight, x: -a2.chartHeight + g2.len + g2.pos } : { y: -h2.pos, height: a2.chartHeight, width: a2.chartWidth, x: -g2.pos } : (e = this.clipBox || a2.clipBox, c && (e.width = a2.plotSizeX, e.x = (a2.scrollablePixelsX || 0) * (f2.scrollPositionX || 0)));
            return c ? { width: e.width, x: e.x } : e;
          },
          setClip: function(e) {
            var c = this.chart, b = this.options, a2 = c.renderer, d2 = c.inverted, g2 = this.clipBox, h2 = this.getClipBox(e), f2 = this.sharedClipKey || [
              "_sharedClip",
              e && e.duration,
              e && e.easing,
              h2.height,
              b.xAxis,
              b.yAxis
            ].join(), k2 = c[f2], l2 = c[f2 + "m"];
            e && (h2.width = 0, d2 && (h2.x = c.plotHeight + (false !== b.clip ? 0 : c.plotTop)));
            k2 ? c.hasLoaded || k2.attr(h2) : (e && (c[f2 + "m"] = l2 = a2.clipRect(d2 ? c.plotSizeX + 99 : -99, d2 ? -c.plotLeft : -c.plotTop, 99, d2 ? c.chartWidth : c.chartHeight)), c[f2] = k2 = a2.clipRect(h2), k2.count = { length: 0 });
            e && !k2.count[this.index] && (k2.count[this.index] = true, k2.count.length += 1);
            if (false !== b.clip || e) this.group.clip(e || g2 ? k2 : c.clipRect), this.markerGroup.clip(l2), this.sharedClipKey = f2;
            e || (k2.count[this.index] && (delete k2.count[this.index], --k2.count.length), 0 === k2.count.length && f2 && c[f2] && (g2 || (c[f2] = c[f2].destroy()), c[f2 + "m"] && (c[f2 + "m"] = c[f2 + "m"].destroy())));
          },
          animate: function(e) {
            var c = this.chart, b = H(this.options.animation);
            if (!c.hasRendered) if (e) this.setClip(b);
            else {
              var a2 = this.sharedClipKey;
              e = c[a2];
              var d2 = this.getClipBox(b, true);
              e && e.animate(d2, b);
              c[a2 + "m"] && c[a2 + "m"].animate({ width: d2.width + 99, x: d2.x - (c.inverted ? 0 : 99) }, b);
            }
          },
          afterAnimate: function() {
            this.setClip();
            m(this, "afterAnimate");
            this.finishedAnimating = true;
          },
          drawPoints: function() {
            var e = this.points, c = this.chart, b, a2, d2 = this.options.marker, g2 = this[this.specialGroup] || this.markerGroup, h2 = this.xAxis, f2 = A(d2.enabled, !h2 || h2.isRadial ? true : null, this.closestPointRangePx >= d2.enabledThreshold * d2.radius);
            if (false !== d2.enabled || this._hasPointMarkers) for (b = 0; b < e.length; b++) {
              var k2 = e[b];
              var l2 = (a2 = k2.graphic) ? "animate" : "attr";
              var m2 = k2.marker || {};
              var p2 = !!k2.marker;
              if ((f2 && "undefined" === typeof m2.enabled || m2.enabled) && !k2.isNull && false !== k2.visible) {
                var r2 = A(m2.symbol, this.symbol);
                var t2 = this.markerAttribs(k2, k2.selected && "select");
                this.enabledDataSorting && (k2.startXPos = h2.reversed ? -t2.width : h2.width);
                var B2 = false !== k2.isInside;
                a2 ? a2[B2 ? "show" : "hide"](B2).animate(t2) : B2 && (0 < t2.width || k2.hasImage) && (k2.graphic = a2 = c.renderer.symbol(r2, t2.x, t2.y, t2.width, t2.height, p2 ? m2 : d2).add(g2), this.enabledDataSorting && c.hasRendered && (a2.attr({ x: k2.startXPos }), l2 = "animate"));
                a2 && "animate" === l2 && a2[B2 ? "show" : "hide"](B2).animate(t2);
                if (a2 && !c.styledMode) a2[l2](this.pointAttribs(k2, k2.selected && "select"));
                a2 && a2.addClass(k2.getClassName(), true);
              } else a2 && (k2.graphic = a2.destroy());
            }
          },
          markerAttribs: function(e, c) {
            var b = this.options, a2 = b.marker, d2 = e.marker || {}, g2 = d2.symbol || a2.symbol, h2 = A(d2.radius, a2.radius);
            c && (a2 = a2.states[c], c = d2.states && d2.states[c], h2 = A(c && c.radius, a2 && a2.radius, h2 + (a2 && a2.radiusPlus || 0)));
            e.hasImage = g2 && 0 === g2.indexOf("url");
            e.hasImage && (h2 = 0);
            e = { x: b.crisp ? Math.floor(e.plotX) - h2 : e.plotX - h2, y: e.plotY - h2 };
            h2 && (e.width = e.height = 2 * h2);
            return e;
          },
          pointAttribs: function(e, c) {
            var b = this.options.marker, a2 = e && e.options, d2 = a2 && a2.marker || {}, g2 = this.color, h2 = a2 && a2.color, f2 = e && e.color;
            a2 = A(d2.lineWidth, b.lineWidth);
            var k2 = e && e.zone && e.zone.color;
            e = 1;
            g2 = h2 || k2 || f2 || g2;
            h2 = d2.fillColor || b.fillColor || g2;
            g2 = d2.lineColor || b.lineColor || g2;
            c = c || "normal";
            b = b.states[c];
            c = d2.states && d2.states[c] || {};
            a2 = A(c.lineWidth, b.lineWidth, a2 + A(c.lineWidthPlus, b.lineWidthPlus, 0));
            h2 = c.fillColor || b.fillColor || h2;
            g2 = c.lineColor || b.lineColor || g2;
            e = A(c.opacity, b.opacity, e);
            return { stroke: g2, "stroke-width": a2, fill: h2, opacity: e };
          },
          destroy: function(e) {
            var c = this, b = c.chart, a2 = /AppleWebKit\/533/.test(F.navigator.userAgent), d2, g2, h2 = c.data || [], f2, k2;
            m(c, "destroy");
            this.removeEvents(e);
            (c.axisTypes || []).forEach(function(b2) {
              (k2 = c[b2]) && k2.series && (p(k2.series, c), k2.isDirty = k2.forceRedraw = true);
            });
            c.legendItem && c.chart.legend.destroyItem(c);
            for (g2 = h2.length; g2--; ) (f2 = h2[g2]) && f2.destroy && f2.destroy();
            c.points = null;
            G.clearTimeout(c.animationTimeout);
            r(c, function(b2, c2) {
              b2 instanceof D && !b2.survive && (d2 = a2 && "group" === c2 ? "hide" : "destroy", b2[d2]());
            });
            b.hoverSeries === c && (b.hoverSeries = null);
            p(b.series, c);
            b.orderSeries();
            r(c, function(b2, a3) {
              e && "hcEvents" === a3 || delete c[a3];
            });
          },
          getGraphPath: function(e, c, b) {
            var a2 = this, d2 = a2.options, g2 = d2.step, h2, f2 = [], k2 = [], l2;
            e = e || a2.points;
            (h2 = e.reversed) && e.reverse();
            (g2 = { right: 1, center: 2 }[g2] || g2 && 3) && h2 && (g2 = 4 - g2);
            e = this.getValidPoints(e, false, !(d2.connectNulls && !c && !b));
            e.forEach(function(h3, m2) {
              var p2 = h3.plotX, r2 = h3.plotY, t2 = e[m2 - 1];
              (h3.leftCliff || t2 && t2.rightCliff) && !b && (l2 = true);
              h3.isNull && !E(c) && 0 < m2 ? l2 = !d2.connectNulls : h3.isNull && !c ? l2 = true : (0 === m2 || l2 ? m2 = [["M", h3.plotX, h3.plotY]] : a2.getPointSpline ? m2 = [a2.getPointSpline(e, h3, m2)] : g2 ? (m2 = 1 === g2 ? [["L", t2.plotX, r2]] : 2 === g2 ? [["L", (t2.plotX + p2) / 2, t2.plotY], ["L", (t2.plotX + p2) / 2, r2]] : [["L", p2, t2.plotY]], m2.push(["L", p2, r2])) : m2 = [[
                "L",
                p2,
                r2
              ]], k2.push(h3.x), g2 && (k2.push(h3.x), 2 === g2 && k2.push(h3.x)), f2.push.apply(f2, m2), l2 = false);
            });
            f2.xMap = k2;
            return a2.graphPath = f2;
          },
          drawGraph: function() {
            var e = this, c = this.options, b = (this.gappedPath || this.getGraphPath).call(this), a2 = this.chart.styledMode, d2 = [["graph", "highcharts-graph"]];
            a2 || d2[0].push(c.lineColor || this.color || "#cccccc", c.dashStyle);
            d2 = e.getZonesGraphs(d2);
            d2.forEach(function(d3, g2) {
              var h2 = d3[0], f2 = e[h2], k2 = f2 ? "animate" : "attr";
              f2 ? (f2.endX = e.preventGraphAnimation ? null : b.xMap, f2.animate({ d: b })) : b.length && (e[h2] = f2 = e.chart.renderer.path(b).addClass(d3[1]).attr({ zIndex: 1 }).add(e.group));
              f2 && !a2 && (h2 = { stroke: d3[2], "stroke-width": c.lineWidth, fill: e.fillGraph && e.color || "none" }, d3[3] ? h2.dashstyle = d3[3] : "square" !== c.linecap && (h2["stroke-linecap"] = h2["stroke-linejoin"] = "round"), f2[k2](h2).shadow(2 > g2 && c.shadow));
              f2 && (f2.startX = b.xMap, f2.isArea = b.isArea);
            });
          },
          getZonesGraphs: function(a2) {
            this.zones.forEach(function(c, b) {
              b = ["zone-graph-" + b, "highcharts-graph highcharts-zone-graph-" + b + " " + (c.className || "")];
              this.chart.styledMode || b.push(c.color || this.color, c.dashStyle || this.options.dashStyle);
              a2.push(b);
            }, this);
            return a2;
          },
          applyZones: function() {
            var a2 = this, c = this.chart, b = c.renderer, d2 = this.zones, g2, h2, f2 = this.clips || [], k2, l2 = this.graph, m2 = this.area, p2 = Math.max(c.chartWidth, c.chartHeight), r2 = this[(this.zoneAxis || "y") + "Axis"], t2 = c.inverted, B2, n3, x2, F2 = false, u2, M2;
            if (d2.length && (l2 || m2) && r2 && "undefined" !== typeof r2.min) {
              var v2 = r2.reversed;
              var I2 = r2.horiz;
              l2 && !this.showLine && l2.hide();
              m2 && m2.hide();
              var E2 = r2.getExtremes();
              d2.forEach(function(e, d3) {
                g2 = v2 ? I2 ? c.plotWidth : 0 : I2 ? 0 : r2.toPixels(E2.min) || 0;
                g2 = q(A(h2, g2), 0, p2);
                h2 = q(Math.round(r2.toPixels(
                  A(e.value, E2.max),
                  true
                ) || 0), 0, p2);
                F2 && (g2 = h2 = r2.toPixels(E2.max));
                B2 = Math.abs(g2 - h2);
                n3 = Math.min(g2, h2);
                x2 = Math.max(g2, h2);
                r2.isXAxis ? (k2 = { x: t2 ? x2 : n3, y: 0, width: B2, height: p2 }, I2 || (k2.x = c.plotHeight - k2.x)) : (k2 = { x: 0, y: t2 ? x2 : n3, width: p2, height: B2 }, I2 && (k2.y = c.plotWidth - k2.y));
                t2 && b.isVML && (k2 = r2.isXAxis ? { x: 0, y: v2 ? n3 : x2, height: k2.width, width: c.chartWidth } : { x: k2.y - c.plotLeft - c.spacingBox.x, y: 0, width: k2.height, height: c.chartHeight });
                f2[d3] ? f2[d3].animate(k2) : f2[d3] = b.clipRect(k2);
                u2 = a2["zone-area-" + d3];
                M2 = a2["zone-graph-" + d3];
                l2 && M2 && M2.clip(f2[d3]);
                m2 && u2 && u2.clip(f2[d3]);
                F2 = e.value > E2.max;
                a2.resetZones && 0 === h2 && (h2 = void 0);
              });
              this.clips = f2;
            } else a2.visible && (l2 && l2.show(true), m2 && m2.show(true));
          },
          invertGroups: function(a2) {
            function c() {
              ["group", "markerGroup"].forEach(function(c2) {
                b[c2] && (e.renderer.isVML && b[c2].attr({ width: b.yAxis.len, height: b.xAxis.len }), b[c2].width = b.yAxis.len, b[c2].height = b.xAxis.len, b[c2].invert(b.isRadialSeries ? false : a2));
              });
            }
            var b = this, e = b.chart;
            b.xAxis && (b.eventsToUnbind.push(J(e, "resize", c)), c(), b.invertGroups = c);
          },
          plotGroup: function(a2, c, b, d2, g2) {
            var e = this[a2], h2 = !e;
            b = { visibility: b, zIndex: d2 || 0.1 };
            "undefined" === typeof this.opacity || this.chart.styledMode || "inactive" === this.state || (b.opacity = this.opacity);
            h2 && (this[a2] = e = this.chart.renderer.g().add(g2));
            e.addClass("highcharts-" + c + " highcharts-series-" + this.index + " highcharts-" + this.type + "-series " + (E(this.colorIndex) ? "highcharts-color-" + this.colorIndex + " " : "") + (this.options.className || "") + (e.hasClass("highcharts-tracker") ? " highcharts-tracker" : ""), true);
            e.attr(b)[h2 ? "attr" : "animate"](this.getPlotBox());
            return e;
          },
          getPlotBox: function() {
            var a2 = this.chart, c = this.xAxis, b = this.yAxis;
            a2.inverted && (c = b, b = this.xAxis);
            return { translateX: c ? c.left : a2.plotLeft, translateY: b ? b.top : a2.plotTop, scaleX: 1, scaleY: 1 };
          },
          removeEvents: function(a2) {
            a2 ? this.eventsToUnbind.length && (this.eventsToUnbind.forEach(function(c) {
              c();
            }), this.eventsToUnbind.length = 0) : N(this);
          },
          render: function() {
            var a2 = this, c = a2.chart, b = a2.options, d2 = H(b.animation), g2 = !a2.finishedAnimating && c.renderer.isSVG && d2.duration, h2 = a2.visible ? "inherit" : "hidden", f2 = b.zIndex, k2 = a2.hasRendered, l2 = c.seriesGroup, p2 = c.inverted;
            m(this, "render");
            var r2 = a2.plotGroup("group", "series", h2, f2, l2);
            a2.markerGroup = a2.plotGroup("markerGroup", "markers", h2, f2, l2);
            g2 && a2.animate && a2.animate(true);
            r2.inverted = a2.isCartesian || a2.invertable ? p2 : false;
            a2.drawGraph && (a2.drawGraph(), a2.applyZones());
            a2.visible && a2.drawPoints();
            a2.drawDataLabels && a2.drawDataLabels();
            a2.redrawPoints && a2.redrawPoints();
            a2.drawTracker && false !== a2.options.enableMouseTracking && a2.drawTracker();
            a2.invertGroups(p2);
            false === b.clip || a2.sharedClipKey || k2 || r2.clip(c.clipRect);
            g2 && a2.animate && a2.animate();
            k2 || (g2 && d2.defer && (g2 += d2.defer), a2.animationTimeout = M(function() {
              a2.afterAnimate();
            }, g2 || 0));
            a2.isDirty = false;
            a2.hasRendered = true;
            m(a2, "afterRender");
          },
          redraw: function() {
            var a2 = this.chart, c = this.isDirty || this.isDirtyData, b = this.group, d2 = this.xAxis, g2 = this.yAxis;
            b && (a2.inverted && b.attr({ width: a2.plotWidth, height: a2.plotHeight }), b.animate({ translateX: A(d2 && d2.left, a2.plotLeft), translateY: A(g2 && g2.top, a2.plotTop) }));
            this.translate();
            this.render();
            c && delete this.kdTree;
          },
          kdAxisArray: ["clientX", "plotY"],
          searchPoint: function(a2, c) {
            var b = this.xAxis, d2 = this.yAxis, e = this.chart.inverted;
            return this.searchKDTree({ clientX: e ? b.len - a2.chartY + b.pos : a2.chartX - b.pos, plotY: e ? d2.len - a2.chartX + d2.pos : a2.chartY - d2.pos }, c, a2);
          },
          buildKDTree: function(a2) {
            function c(a3, d3, e) {
              var g2;
              if (g2 = a3 && a3.length) {
                var h2 = b.kdAxisArray[d3 % e];
                a3.sort(function(b2, c2) {
                  return b2[h2] - c2[h2];
                });
                g2 = Math.floor(g2 / 2);
                return { point: a3[g2], left: c(a3.slice(0, g2), d3 + 1, e), right: c(a3.slice(g2 + 1), d3 + 1, e) };
              }
            }
            this.buildingKdTree = true;
            var b = this, d2 = -1 < b.options.findNearestPointBy.indexOf("y") ? 2 : 1;
            delete b.kdTree;
            M(function() {
              b.kdTree = c(b.getValidPoints(
                null,
                !b.directTouch
              ), d2, d2);
              b.buildingKdTree = false;
            }, b.options.kdNow || a2 && "touchstart" === a2.type ? 0 : 1);
          },
          searchKDTree: function(a2, c, b) {
            function d2(b2, c2, a3, k2) {
              var l2 = c2.point, m2 = e.kdAxisArray[a3 % k2], p2 = l2;
              var r2 = E(b2[g2]) && E(l2[g2]) ? Math.pow(b2[g2] - l2[g2], 2) : null;
              var t2 = E(b2[h2]) && E(l2[h2]) ? Math.pow(b2[h2] - l2[h2], 2) : null;
              t2 = (r2 || 0) + (t2 || 0);
              l2.dist = E(t2) ? Math.sqrt(t2) : Number.MAX_VALUE;
              l2.distX = E(r2) ? Math.sqrt(r2) : Number.MAX_VALUE;
              m2 = b2[m2] - l2[m2];
              t2 = 0 > m2 ? "left" : "right";
              r2 = 0 > m2 ? "right" : "left";
              c2[t2] && (t2 = d2(b2, c2[t2], a3 + 1, k2), p2 = t2[f2] < p2[f2] ? t2 : l2);
              c2[r2] && Math.sqrt(m2 * m2) < p2[f2] && (b2 = d2(b2, c2[r2], a3 + 1, k2), p2 = b2[f2] < p2[f2] ? b2 : p2);
              return p2;
            }
            var e = this, g2 = this.kdAxisArray[0], h2 = this.kdAxisArray[1], f2 = c ? "distX" : "dist";
            c = -1 < e.options.findNearestPointBy.indexOf("y") ? 2 : 1;
            this.kdTree || this.buildingKdTree || this.buildKDTree(b);
            if (this.kdTree) return d2(a2, this.kdTree, c, c);
          },
          pointPlacementToXValue: function() {
            var a2 = this.options, c = a2.pointRange, b = this.xAxis;
            a2 = a2.pointPlacement;
            "between" === a2 && (a2 = b.reversed ? -0.5 : 0.5);
            return g(a2) ? a2 * A(c, b.pointRange) : 0;
          },
          isPointInside: function(a2) {
            return "undefined" !== typeof a2.plotY && "undefined" !== typeof a2.plotX && 0 <= a2.plotY && a2.plotY <= this.yAxis.len && 0 <= a2.plotX && a2.plotX <= this.xAxis.len;
          }
        });
        "";
      });
      O(n, "Extensions/Stacking.js", [n["Core/Axis/Axis.js"], n["Core/Chart/Chart.js"], n["Core/Globals.js"], n["Core/Axis/StackingAxis.js"], n["Core/Utilities.js"]], function(f, a, n2, y, D) {
        var G = D.correctFloat, C = D.defined, J = D.destroyObjectProperties, H = D.format, v = D.isNumber, L = D.pick;
        "";
        var q = n2.Series, K = (function() {
          function a2(a3, f2, n3, q2, m) {
            var h = a3.chart.inverted;
            this.axis = a3;
            this.isNegative = n3;
            this.options = f2 = f2 || {};
            this.x = q2;
            this.total = null;
            this.points = {};
            this.hasValidPoints = false;
            this.stack = m;
            this.rightCliff = this.leftCliff = 0;
            this.alignOptions = { align: f2.align || (h ? n3 ? "left" : "right" : "center"), verticalAlign: f2.verticalAlign || (h ? "middle" : n3 ? "bottom" : "top"), y: f2.y, x: f2.x };
            this.textAlign = f2.textAlign || (h ? n3 ? "right" : "left" : "center");
          }
          a2.prototype.destroy = function() {
            J(this, this.axis);
          };
          a2.prototype.render = function(a3) {
            var f2 = this.axis.chart, p = this.options, n3 = p.format;
            n3 = n3 ? H(n3, this, f2) : p.formatter.call(this);
            this.label ? this.label.attr({
              text: n3,
              visibility: "hidden"
            }) : (this.label = f2.renderer.label(n3, null, null, p.shape, null, null, p.useHTML, false, "stack-labels"), n3 = { r: p.borderRadius || 0, text: n3, rotation: p.rotation, padding: L(p.padding, 5), visibility: "hidden" }, f2.styledMode || (n3.fill = p.backgroundColor, n3.stroke = p.borderColor, n3["stroke-width"] = p.borderWidth, this.label.css(p.style)), this.label.attr(n3), this.label.added || this.label.add(a3));
            this.label.labelrank = f2.plotHeight;
          };
          a2.prototype.setOffset = function(a3, f2, n3, u, m) {
            var h = this.axis, l = h.chart;
            u = h.translate(h.stacking.usePercentage ? 100 : u ? u : this.total, 0, 0, 0, 1);
            n3 = h.translate(n3 ? n3 : 0);
            n3 = C(u) && Math.abs(u - n3);
            a3 = L(m, l.xAxis[0].translate(this.x)) + a3;
            h = C(u) && this.getStackBox(l, this, a3, u, f2, n3, h);
            f2 = this.label;
            n3 = this.isNegative;
            a3 = "justify" === L(this.options.overflow, "justify");
            var k = this.textAlign;
            f2 && h && (m = f2.getBBox(), u = f2.padding, k = "left" === k ? l.inverted ? -u : u : "right" === k ? m.width : l.inverted && "center" === k ? m.width / 2 : l.inverted ? n3 ? m.width + u : -u : m.width / 2, n3 = l.inverted ? m.height / 2 : n3 ? -u : m.height, this.alignOptions.x = L(this.options.x, 0), this.alignOptions.y = L(this.options.y, 0), h.x -= k, h.y -= n3, f2.align(this.alignOptions, null, h), l.isInsidePlot(f2.alignAttr.x + k - this.alignOptions.x, f2.alignAttr.y + n3 - this.alignOptions.y) ? f2.show() : (f2.alignAttr.y = -9999, a3 = false), a3 && q.prototype.justifyDataLabel.call(this.axis, f2, this.alignOptions, f2.alignAttr, m, h), f2.attr({ x: f2.alignAttr.x, y: f2.alignAttr.y }), L(!a3 && this.options.crop, true) && ((l = v(f2.x) && v(f2.y) && l.isInsidePlot(f2.x - u + f2.width, f2.y) && l.isInsidePlot(f2.x + u, f2.y)) || f2.hide()));
          };
          a2.prototype.getStackBox = function(a3, f2, n3, q2, m, h, l) {
            var k = f2.axis.reversed, g = a3.inverted, d = l.height + l.pos - (g ? a3.plotLeft : a3.plotTop);
            f2 = f2.isNegative && !k || !f2.isNegative && k;
            return { x: g ? f2 ? q2 - l.right : q2 - h + l.pos - a3.plotLeft : n3 + a3.xAxis[0].transB - a3.plotLeft, y: g ? l.height - n3 - m : f2 ? d - q2 - h : d - q2, width: g ? h : m, height: g ? m : h };
          };
          return a2;
        })();
        a.prototype.getStacks = function() {
          var a2 = this, f2 = a2.inverted;
          a2.yAxis.forEach(function(a3) {
            a3.stacking && a3.stacking.stacks && a3.hasVisibleSeries && (a3.stacking.oldStacks = a3.stacking.stacks);
          });
          a2.series.forEach(function(p) {
            var n3 = p.xAxis && p.xAxis.options || {};
            !p.options.stacking || true !== p.visible && false !== a2.options.chart.ignoreHiddenSeries || (p.stackKey = [p.type, L(p.options.stack, ""), f2 ? n3.top : n3.left, f2 ? n3.height : n3.width].join());
          });
        };
        y.compose(f);
        q.prototype.setGroupedPoints = function() {
          this.options.centerInCategory && (this.is("column") || this.is("columnrange")) && !this.options.stacking && 1 < this.chart.series.length && q.prototype.setStackedPoints.call(this, "group");
        };
        q.prototype.setStackedPoints = function(a2) {
          var f2 = a2 || this.options.stacking;
          if (f2 && (true === this.visible || false === this.chart.options.chart.ignoreHiddenSeries)) {
            var n3 = this.processedXData, q2 = this.processedYData, u = [], m = q2.length, h = this.options, l = h.threshold, k = L(h.startFromThreshold && l, 0);
            h = h.stack;
            a2 = a2 ? this.type + "," + f2 : this.stackKey;
            var g = "-" + a2, d = this.negStacks, x = this.yAxis, r = x.stacking.stacks, A = x.stacking.oldStacks, v2, B;
            x.stacking.stacksTouched += 1;
            for (B = 0; B < m; B++) {
              var M = n3[B];
              var E = q2[B];
              var F = this.getStackIndicator(F, M, this.index);
              var e = F.key;
              var c = (v2 = d && E < (k ? 0 : l)) ? g : a2;
              r[c] || (r[c] = {});
              r[c][M] || (A[c] && A[c][M] ? (r[c][M] = A[c][M], r[c][M].total = null) : r[c][M] = new K(
                x,
                x.options.stackLabels,
                v2,
                M,
                h
              ));
              c = r[c][M];
              null !== E ? (c.points[e] = c.points[this.index] = [L(c.cumulative, k)], C(c.cumulative) || (c.base = e), c.touched = x.stacking.stacksTouched, 0 < F.index && false === this.singleStacks && (c.points[e][0] = c.points[this.index + "," + M + ",0"][0])) : c.points[e] = c.points[this.index] = null;
              "percent" === f2 ? (v2 = v2 ? a2 : g, d && r[v2] && r[v2][M] ? (v2 = r[v2][M], c.total = v2.total = Math.max(v2.total, c.total) + Math.abs(E) || 0) : c.total = G(c.total + (Math.abs(E) || 0))) : "group" === f2 ? null !== E && (c.total = (c.total || 0) + 1) : c.total = G(c.total + (E || 0));
              c.cumulative = "group" === f2 ? (c.total || 1) - 1 : L(c.cumulative, k) + (E || 0);
              null !== E && (c.points[e].push(c.cumulative), u[B] = c.cumulative, c.hasValidPoints = true);
            }
            "percent" === f2 && (x.stacking.usePercentage = true);
            "group" !== f2 && (this.stackedYData = u);
            x.stacking.oldStacks = {};
          }
        };
        q.prototype.modifyStacks = function() {
          var a2 = this, f2 = a2.stackKey, n3 = a2.yAxis.stacking.stacks, q2 = a2.processedXData, v2, m = a2.options.stacking;
          a2[m + "Stacker"] && [f2, "-" + f2].forEach(function(h) {
            for (var f3 = q2.length, k, g; f3--; ) if (k = q2[f3], v2 = a2.getStackIndicator(v2, k, a2.index, h), g = (k = n3[h] && n3[h][k]) && k.points[v2.key]) a2[m + "Stacker"](g, k, f3);
          });
        };
        q.prototype.percentStacker = function(a2, f2, n3) {
          f2 = f2.total ? 100 / f2.total : 0;
          a2[0] = G(a2[0] * f2);
          a2[1] = G(a2[1] * f2);
          this.stackedYData[n3] = a2[1];
        };
        q.prototype.getStackIndicator = function(a2, f2, n3, q2) {
          !C(a2) || a2.x !== f2 || q2 && a2.key !== q2 ? a2 = { x: f2, index: 0, key: q2 } : a2.index++;
          a2.key = [n3, f2, a2.index].join();
          return a2;
        };
        n2.StackItem = K;
        return n2.StackItem;
      });
      O(n, "Core/Dynamics.js", [
        n["Core/Axis/Axis.js"],
        n["Core/Chart/Chart.js"],
        n["Core/Globals.js"],
        n["Core/Options.js"],
        n["Core/Series/Point.js"],
        n["Core/Time.js"],
        n["Core/Utilities.js"]
      ], function(f, a, n2, y, D, G, C) {
        var J = y.time, H = C.addEvent, v = C.animate, L = C.createElement, q = C.css, K = C.defined, E = C.erase, p = C.error, t = C.extend, I = C.fireEvent, u = C.isArray, m = C.isNumber, h = C.isObject, l = C.isString, k = C.merge, g = C.objectEach, d = C.pick, x = C.relativeLength, r = C.setAnimation, A = C.splat;
        y = n2.Series;
        var N = n2.seriesTypes;
        n2.cleanRecursively = function(a2, d2) {
          var f2 = {};
          g(a2, function(g2, e) {
            if (h(a2[e], true) && !a2.nodeType && d2[e]) g2 = n2.cleanRecursively(a2[e], d2[e]), Object.keys(g2).length && (f2[e] = g2);
            else if (h(a2[e]) || a2[e] !== d2[e]) f2[e] = a2[e];
          });
          return f2;
        };
        t(a.prototype, {
          addSeries: function(a2, g2, h2) {
            var f2, e = this;
            a2 && (g2 = d(g2, true), I(e, "addSeries", { options: a2 }, function() {
              f2 = e.initSeries(a2);
              e.isDirtyLegend = true;
              e.linkSeries();
              f2.enabledDataSorting && f2.setData(a2.data, false);
              I(e, "afterAddSeries", { series: f2 });
              g2 && e.redraw(h2);
            }));
            return f2;
          },
          addAxis: function(a2, d2, g2, h2) {
            return this.createAxis(d2 ? "xAxis" : "yAxis", { axis: a2, redraw: g2, animation: h2 });
          },
          addColorAxis: function(a2, d2, g2) {
            return this.createAxis("colorAxis", { axis: a2, redraw: d2, animation: g2 });
          },
          createAxis: function(a2, g2) {
            var h2 = this.options, l2 = "colorAxis" === a2, e = g2.redraw, c = g2.animation;
            g2 = k(g2.axis, { index: this[a2].length, isX: "xAxis" === a2 });
            var b = l2 ? new n2.ColorAxis(this, g2) : new f(this, g2);
            h2[a2] = A(h2[a2] || {});
            h2[a2].push(g2);
            l2 && (this.isDirtyLegend = true, this.axes.forEach(function(b2) {
              b2.series = [];
            }), this.series.forEach(function(b2) {
              b2.bindAxes();
              b2.isDirtyData = true;
            }));
            d(e, true) && this.redraw(c);
            return b;
          },
          showLoading: function(a2) {
            var g2 = this, h2 = g2.options, f2 = g2.loadingDiv, e = h2.loading, c = function() {
              f2 && q(f2, { left: g2.plotLeft + "px", top: g2.plotTop + "px", width: g2.plotWidth + "px", height: g2.plotHeight + "px" });
            };
            f2 || (g2.loadingDiv = f2 = L("div", { className: "highcharts-loading highcharts-loading-hidden" }, null, g2.container), g2.loadingSpan = L("span", { className: "highcharts-loading-inner" }, null, f2), H(g2, "redraw", c));
            f2.className = "highcharts-loading";
            g2.loadingSpan.innerHTML = d(a2, h2.lang.loading, "");
            g2.styledMode || (q(f2, t(e.style, { zIndex: 10 })), q(g2.loadingSpan, e.labelStyle), g2.loadingShown || (q(f2, { opacity: 0, display: "" }), v(f2, { opacity: e.style.opacity || 0.5 }, { duration: e.showDuration || 0 })));
            g2.loadingShown = true;
            c();
          },
          hideLoading: function() {
            var a2 = this.options, d2 = this.loadingDiv;
            d2 && (d2.className = "highcharts-loading highcharts-loading-hidden", this.styledMode || v(d2, { opacity: 0 }, { duration: a2.loading.hideDuration || 100, complete: function() {
              q(d2, { display: "none" });
            } }));
            this.loadingShown = false;
          },
          propsRequireDirtyBox: "backgroundColor borderColor borderWidth borderRadius plotBackgroundColor plotBackgroundImage plotBorderColor plotBorderWidth plotShadow shadow".split(" "),
          propsRequireReflow: "margin marginTop marginRight marginBottom marginLeft spacing spacingTop spacingRight spacingBottom spacingLeft".split(" "),
          propsRequireUpdateSeries: "chart.inverted chart.polar chart.ignoreHiddenSeries chart.type colors plotOptions time tooltip".split(" "),
          collectionsWithUpdate: ["xAxis", "yAxis", "zAxis", "series"],
          update: function(a2, h2, f2, p2) {
            var e = this, c = { credits: "addCredits", title: "setTitle", subtitle: "setSubtitle", caption: "setCaption" }, b, r2, q2, t2 = a2.isResponsiveOptions, B = [];
            I(e, "update", { options: a2 });
            t2 || e.setResponsive(false, true);
            a2 = n2.cleanRecursively(a2, e.options);
            k(true, e.userOptions, a2);
            if (b = a2.chart) {
              k(true, e.options.chart, b);
              "className" in b && e.setClassName(b.className);
              "reflow" in b && e.setReflow(b.reflow);
              if ("inverted" in b || "polar" in b || "type" in b) {
                e.propFromSeries();
                var F = true;
              }
              "alignTicks" in b && (F = true);
              g(b, function(b2, a3) {
                -1 !== e.propsRequireUpdateSeries.indexOf("chart." + a3) && (r2 = true);
                -1 !== e.propsRequireDirtyBox.indexOf(a3) && (e.isDirtyBox = true);
                -1 !== e.propsRequireReflow.indexOf(a3) && (t2 ? e.isDirtyBox = true : q2 = true);
              });
              !e.styledMode && "style" in b && e.renderer.setStyle(b.style);
            }
            !e.styledMode && a2.colors && (this.options.colors = a2.colors);
            a2.plotOptions && k(
              true,
              this.options.plotOptions,
              a2.plotOptions
            );
            a2.time && this.time === J && (this.time = new G(a2.time));
            g(a2, function(b2, a3) {
              if (e[a3] && "function" === typeof e[a3].update) e[a3].update(b2, false);
              else if ("function" === typeof e[c[a3]]) e[c[a3]](b2);
              "chart" !== a3 && -1 !== e.propsRequireUpdateSeries.indexOf(a3) && (r2 = true);
            });
            this.collectionsWithUpdate.forEach(function(b2) {
              if (a2[b2]) {
                if ("series" === b2) {
                  var c2 = [];
                  e[b2].forEach(function(b3, a3) {
                    b3.options.isInternal || c2.push(d(b3.options.index, a3));
                  });
                }
                A(a2[b2]).forEach(function(a3, d2) {
                  var g2 = K(a3.id), h3;
                  g2 && (h3 = e.get(a3.id));
                  h3 || (h3 = e[b2][c2 ? c2[d2] : d2]) && g2 && K(h3.options.id) && (h3 = void 0);
                  h3 && h3.coll === b2 && (h3.update(a3, false), f2 && (h3.touched = true));
                  !h3 && f2 && e.collectionsWithInit[b2] && (e.collectionsWithInit[b2][0].apply(e, [a3].concat(e.collectionsWithInit[b2][1] || []).concat([false])).touched = true);
                });
                f2 && e[b2].forEach(function(b3) {
                  b3.touched || b3.options.isInternal ? delete b3.touched : B.push(b3);
                });
              }
            });
            B.forEach(function(b2) {
              b2.remove && b2.remove(false);
            });
            F && e.axes.forEach(function(b2) {
              b2.update({}, false);
            });
            r2 && e.getSeriesOrderByLinks().forEach(function(b2) {
              b2.chart && b2.update({}, false);
            }, this);
            a2.loading && k(true, e.options.loading, a2.loading);
            F = b && b.width;
            b = b && b.height;
            l(b) && (b = x(b, F || e.chartWidth));
            q2 || m(F) && F !== e.chartWidth || m(b) && b !== e.chartHeight ? e.setSize(F, b, p2) : d(h2, true) && e.redraw(p2);
            I(e, "afterUpdate", { options: a2, redraw: h2, animation: p2 });
          },
          setSubtitle: function(a2, d2) {
            this.applyDescription("subtitle", a2);
            this.layOutTitles(d2);
          },
          setCaption: function(a2, d2) {
            this.applyDescription("caption", a2);
            this.layOutTitles(d2);
          }
        });
        a.prototype.collectionsWithInit = {
          xAxis: [a.prototype.addAxis, [true]],
          yAxis: [a.prototype.addAxis, [false]],
          series: [a.prototype.addSeries]
        };
        t(D.prototype, { update: function(a2, g2, f2, k2) {
          function e() {
            c.applyOptions(a2);
            var e2 = l2 && c.hasDummyGraphic;
            e2 = null === c.y ? !e2 : e2;
            l2 && e2 && (c.graphic = l2.destroy(), delete c.hasDummyGraphic);
            h(a2, true) && (l2 && l2.element && a2 && a2.marker && "undefined" !== typeof a2.marker.symbol && (c.graphic = l2.destroy()), a2 && a2.dataLabels && c.dataLabel && (c.dataLabel = c.dataLabel.destroy()), c.connector && (c.connector = c.connector.destroy()));
            m2 = c.index;
            b.updateParallelArrays(c, m2);
            r2.data[m2] = h(r2.data[m2], true) || h(a2, true) ? c.options : d(a2, r2.data[m2]);
            b.isDirty = b.isDirtyData = true;
            !b.fixedBox && b.hasCartesianSeries && (p2.isDirtyBox = true);
            "point" === r2.legendType && (p2.isDirtyLegend = true);
            g2 && p2.redraw(f2);
          }
          var c = this, b = c.series, l2 = c.graphic, m2, p2 = b.chart, r2 = b.options;
          g2 = d(g2, true);
          false === k2 ? e() : c.firePointEvent("update", { options: a2 }, e);
        }, remove: function(a2, d2) {
          this.series.removePoint(this.series.data.indexOf(this), a2, d2);
        } });
        t(y.prototype, { addPoint: function(a2, g2, h2, f2, e) {
          var c = this.options, b = this.data, k2 = this.chart, l2 = this.xAxis;
          l2 = l2 && l2.hasNames && l2.names;
          var m2 = c.data, p2 = this.xData, r2;
          g2 = d(g2, true);
          var n3 = { series: this };
          this.pointClass.prototype.applyOptions.apply(n3, [a2]);
          var q2 = n3.x;
          var t2 = p2.length;
          if (this.requireSorting && q2 < p2[t2 - 1]) for (r2 = true; t2 && p2[t2 - 1] > q2; ) t2--;
          this.updateParallelArrays(n3, "splice", t2, 0, 0);
          this.updateParallelArrays(n3, t2);
          l2 && n3.name && (l2[q2] = n3.name);
          m2.splice(t2, 0, a2);
          r2 && (this.data.splice(t2, 0, null), this.processData());
          "point" === c.legendType && this.generatePoints();
          h2 && (b[0] && b[0].remove ? b[0].remove(false) : (b.shift(), this.updateParallelArrays(n3, "shift"), m2.shift()));
          false !== e && I(
            this,
            "addPoint",
            { point: n3 }
          );
          this.isDirtyData = this.isDirty = true;
          g2 && k2.redraw(f2);
        }, removePoint: function(a2, g2, h2) {
          var f2 = this, e = f2.data, c = e[a2], b = f2.points, k2 = f2.chart, l2 = function() {
            b && b.length === e.length && b.splice(a2, 1);
            e.splice(a2, 1);
            f2.options.data.splice(a2, 1);
            f2.updateParallelArrays(c || { series: f2 }, "splice", a2, 1);
            c && c.destroy();
            f2.isDirty = true;
            f2.isDirtyData = true;
            g2 && k2.redraw();
          };
          r(h2, k2);
          g2 = d(g2, true);
          c ? c.firePointEvent("remove", null, l2) : l2();
        }, remove: function(a2, g2, h2, f2) {
          function e() {
            c.destroy(f2);
            c.remove = null;
            b.isDirtyLegend = b.isDirtyBox = true;
            b.linkSeries();
            d(a2, true) && b.redraw(g2);
          }
          var c = this, b = c.chart;
          false !== h2 ? I(c, "remove", null, e) : e();
        }, update: function(a2, g2) {
          a2 = n2.cleanRecursively(a2, this.userOptions);
          I(this, "update", { options: a2 });
          var h2 = this, f2 = h2.chart, e = h2.userOptions, c = h2.initialType || h2.type, b = a2.type || e.type || f2.options.chart.type, l2 = !(this.hasDerivedData || a2.dataGrouping || b && b !== this.type || "undefined" !== typeof a2.pointStart || a2.pointInterval || a2.pointIntervalUnit || a2.keys), m2 = N[c].prototype, r2, q2 = ["eventOptions", "navigatorSeries", "baseSeries"], x2 = h2.finishedAnimating && { animation: false }, B = {};
          l2 && (q2.push("data", "isDirtyData", "points", "processedXData", "processedYData", "xIncrement", "cropped", "_hasPointMarkers", "_hasPointLabels", "mapMap", "mapData", "minY", "maxY", "minX", "maxX"), false !== a2.visible && q2.push("area", "graph"), h2.parallelArrays.forEach(function(b2) {
            q2.push(b2 + "Data");
          }), a2.data && (a2.dataSorting && t(h2.options.dataSorting, a2.dataSorting), this.setData(a2.data, false)));
          a2 = k(
            e,
            x2,
            { index: "undefined" === typeof e.index ? h2.index : e.index, pointStart: d(e.pointStart, h2.xData[0]) },
            !l2 && { data: h2.options.data },
            a2
          );
          l2 && a2.data && (a2.data = h2.options.data);
          q2 = ["group", "markerGroup", "dataLabelsGroup", "transformGroup"].concat(q2);
          q2.forEach(function(b2) {
            q2[b2] = h2[b2];
            delete h2[b2];
          });
          h2.remove(false, null, false, true);
          for (r2 in m2) h2[r2] = void 0;
          N[b || c] ? t(h2, N[b || c].prototype) : p(17, true, f2, { missingModuleFor: b || c });
          q2.forEach(function(b2) {
            h2[b2] = q2[b2];
          });
          h2.init(f2, a2);
          if (l2 && this.points) {
            var A2 = h2.options;
            false === A2.visible ? (B.graphic = 1, B.dataLabel = 1) : h2._hasPointLabels || (a2 = A2.marker, e = A2.dataLabels, a2 && (false === a2.enabled || "symbol" in a2) && (B.graphic = 1), e && false === e.enabled && (B.dataLabel = 1));
            this.points.forEach(function(b2) {
              b2 && b2.series && (b2.resolveColor(), Object.keys(B).length && b2.destroyElements(B), false === A2.showInLegend && b2.legendItem && f2.legend.destroyItem(b2));
            }, this);
          }
          h2.initialType = c;
          f2.linkSeries();
          I(this, "afterUpdate");
          d(g2, true) && f2.redraw(l2 ? void 0 : false);
        }, setName: function(a2) {
          this.name = this.options.name = this.userOptions.name = a2;
          this.chart.isDirtyLegend = true;
        } });
        t(f.prototype, { update: function(a2, h2) {
          var f2 = this.chart, l2 = a2 && a2.events || {};
          a2 = k(this.userOptions, a2);
          f2.options[this.coll].indexOf && (f2.options[this.coll][f2.options[this.coll].indexOf(this.userOptions)] = a2);
          g(f2.options[this.coll].events, function(a3, c) {
            "undefined" === typeof l2[c] && (l2[c] = void 0);
          });
          this.destroy(true);
          this.init(f2, t(a2, { events: l2 }));
          f2.isDirtyBox = true;
          d(h2, true) && f2.redraw();
        }, remove: function(a2) {
          for (var g2 = this.chart, h2 = this.coll, f2 = this.series, e = f2.length; e--; ) f2[e] && f2[e].remove(false);
          E(g2.axes, this);
          E(g2[h2], this);
          u(g2.options[h2]) ? g2.options[h2].splice(this.options.index, 1) : delete g2.options[h2];
          g2[h2].forEach(function(a3, b) {
            a3.options.index = a3.userOptions.index = b;
          });
          this.destroy();
          g2.isDirtyBox = true;
          d(a2, true) && g2.redraw();
        }, setTitle: function(a2, d2) {
          this.update({ title: a2 }, d2);
        }, setCategories: function(a2, d2) {
          this.update({ categories: a2 }, d2);
        } });
      });
      O(n, "Series/AreaSeries.js", [n["Core/Globals.js"], n["Core/Color.js"], n["Mixins/LegendSymbol.js"], n["Core/Utilities.js"]], function(f, a, n2, y) {
        var D = a.parse, G = y.objectEach, C = y.pick;
        a = y.seriesType;
        var J = f.Series;
        a("area", "line", { threshold: 0 }, { singleStacks: false, getStackPoints: function(a2) {
          var f2 = [], n3 = [], q = this.xAxis, H = this.yAxis, E = H.stacking.stacks[this.stackKey], p = {}, t = this.index, I = H.series, u = I.length, m = C(H.options.reversedStacks, true) ? 1 : -1, h;
          a2 = a2 || this.points;
          if (this.options.stacking) {
            for (h = 0; h < a2.length; h++) a2[h].leftNull = a2[h].rightNull = void 0, p[a2[h].x] = a2[h];
            G(E, function(a3, g) {
              null !== a3.total && n3.push(g);
            });
            n3.sort(function(a3, g) {
              return a3 - g;
            });
            var l = I.map(function(a3) {
              return a3.visible;
            });
            n3.forEach(function(a3, g) {
              var d = 0, k, r;
              if (p[a3] && !p[a3].isNull) f2.push(p[a3]), [-1, 1].forEach(function(d2) {
                var f3 = 1 === d2 ? "rightNull" : "leftNull", q2 = 0, x = E[n3[g + d2]];
                if (x) for (h = t; 0 <= h && h < u; ) k = x.points[h], k || (h === t ? p[a3][f3] = true : l[h] && (r = E[a3].points[h]) && (q2 -= r[1] - r[0])), h += m;
                p[a3][1 === d2 ? "rightCliff" : "leftCliff"] = q2;
              });
              else {
                for (h = t; 0 <= h && h < u; ) {
                  if (k = E[a3].points[h]) {
                    d = k[1];
                    break;
                  }
                  h += m;
                }
                d = H.translate(d, 0, 1, 0, 1);
                f2.push({ isNull: true, plotX: q.translate(a3, 0, 0, 0, 1), x: a3, plotY: d, yBottom: d });
              }
            });
          }
          return f2;
        }, getGraphPath: function(a2) {
          var f2 = J.prototype.getGraphPath, n3 = this.options, q = n3.stacking, y2 = this.yAxis, E, p = [], t = [], I = this.index, u = y2.stacking.stacks[this.stackKey], m = n3.threshold, h = Math.round(y2.getThreshold(n3.threshold));
          n3 = C(
            n3.connectNulls,
            "percent" === q
          );
          var l = function(d2, f3, k2) {
            var l2 = a2[d2];
            d2 = q && u[l2.x].points[I];
            var r = l2[k2 + "Null"] || 0;
            k2 = l2[k2 + "Cliff"] || 0;
            l2 = true;
            if (k2 || r) {
              var n4 = (r ? d2[0] : d2[1]) + k2;
              var x = d2[0] + k2;
              l2 = !!r;
            } else !q && a2[f3] && a2[f3].isNull && (n4 = x = m);
            "undefined" !== typeof n4 && (t.push({ plotX: g, plotY: null === n4 ? h : y2.getThreshold(n4), isNull: l2, isCliff: true }), p.push({ plotX: g, plotY: null === x ? h : y2.getThreshold(x), doCurve: false }));
          };
          a2 = a2 || this.points;
          q && (a2 = this.getStackPoints(a2));
          for (E = 0; E < a2.length; E++) {
            q || (a2[E].leftCliff = a2[E].rightCliff = a2[E].leftNull = a2[E].rightNull = void 0);
            var k = a2[E].isNull;
            var g = C(a2[E].rectPlotX, a2[E].plotX);
            var d = q ? a2[E].yBottom : h;
            if (!k || n3) n3 || l(E, E - 1, "left"), k && !q && n3 || (t.push(a2[E]), p.push({ x: E, plotX: g, plotY: d })), n3 || l(E, E + 1, "right");
          }
          E = f2.call(this, t, true, true);
          p.reversed = true;
          k = f2.call(this, p, true, true);
          (d = k[0]) && "M" === d[0] && (k[0] = ["L", d[1], d[2]]);
          k = E.concat(k);
          f2 = f2.call(this, t, false, n3);
          k.xMap = E.xMap;
          this.areaPath = k;
          return f2;
        }, drawGraph: function() {
          this.areaPath = [];
          J.prototype.drawGraph.apply(this);
          var a2 = this, f2 = this.areaPath, n3 = this.options, q = [[
            "area",
            "highcharts-area",
            this.color,
            n3.fillColor
          ]];
          this.zones.forEach(function(f3, v) {
            q.push(["zone-area-" + v, "highcharts-area highcharts-zone-area-" + v + " " + f3.className, f3.color || a2.color, f3.fillColor || n3.fillColor]);
          });
          q.forEach(function(q2) {
            var v = q2[0], p = a2[v], t = p ? "animate" : "attr", I = {};
            p ? (p.endX = a2.preventGraphAnimation ? null : f2.xMap, p.animate({ d: f2 })) : (I.zIndex = 0, p = a2[v] = a2.chart.renderer.path(f2).addClass(q2[1]).add(a2.group), p.isArea = true);
            a2.chart.styledMode || (I.fill = C(q2[3], D(q2[2]).setOpacity(C(n3.fillOpacity, 0.75)).get()));
            p[t](I);
            p.startX = f2.xMap;
            p.shiftUnit = n3.step ? 2 : 1;
          });
        }, drawLegendSymbol: n2.drawRectangle });
        "";
      });
      O(n, "Series/SplineSeries.js", [n["Core/Utilities.js"]], function(f) {
        var a = f.pick;
        f = f.seriesType;
        f("spline", "line", {}, { getPointSpline: function(f2, n2, D) {
          var y = n2.plotX || 0, C = n2.plotY || 0, J = f2[D - 1];
          D = f2[D + 1];
          if (J && !J.isNull && false !== J.doCurve && !n2.isCliff && D && !D.isNull && false !== D.doCurve && !n2.isCliff) {
            f2 = J.plotY || 0;
            var H = D.plotX || 0;
            D = D.plotY || 0;
            var v = 0;
            var L = (1.5 * y + (J.plotX || 0)) / 2.5;
            var q = (1.5 * C + f2) / 2.5;
            H = (1.5 * y + H) / 2.5;
            var K = (1.5 * C + D) / 2.5;
            H !== L && (v = (K - q) * (H - y) / (H - L) + C - K);
            q += v;
            K += v;
            q > f2 && q > C ? (q = Math.max(f2, C), K = 2 * C - q) : q < f2 && q < C && (q = Math.min(f2, C), K = 2 * C - q);
            K > D && K > C ? (K = Math.max(D, C), q = 2 * C - K) : K < D && K < C && (K = Math.min(D, C), q = 2 * C - K);
            n2.rightContX = H;
            n2.rightContY = K;
          }
          n2 = ["C", a(J.rightContX, J.plotX, 0), a(J.rightContY, J.plotY, 0), a(L, y, 0), a(q, C, 0), y, C];
          J.rightContX = J.rightContY = void 0;
          return n2;
        } });
        "";
      });
      O(n, "Series/AreaSplineSeries.js", [n["Core/Globals.js"], n["Mixins/LegendSymbol.js"], n["Core/Options.js"], n["Core/Utilities.js"]], function(f, a, n2, y) {
        y = y.seriesType;
        f = f.seriesTypes.area.prototype;
        y("areaspline", "spline", n2.defaultOptions.plotOptions.area, { getStackPoints: f.getStackPoints, getGraphPath: f.getGraphPath, drawGraph: f.drawGraph, drawLegendSymbol: a.drawRectangle });
        "";
      });
      O(n, "Series/ColumnSeries.js", [n["Core/Globals.js"], n["Core/Color.js"], n["Mixins/LegendSymbol.js"], n["Core/Utilities.js"]], function(f, a, n2, y) {
        "";
        var D = a.parse, G = y.animObject, C = y.clamp, J = y.defined, H = y.extend, v = y.isNumber, L = y.merge, q = y.pick;
        a = y.seriesType;
        var K = y.objectEach, E = f.Series;
        a("column", "line", {
          borderRadius: 0,
          centerInCategory: false,
          groupPadding: 0.2,
          marker: null,
          pointPadding: 0.1,
          minPointLength: 0,
          cropThreshold: 50,
          pointRange: null,
          states: { hover: { halo: false, brightness: 0.1 }, select: { color: "#cccccc", borderColor: "#000000" } },
          dataLabels: { align: void 0, verticalAlign: void 0, y: void 0 },
          startFromThreshold: true,
          stickyTracking: false,
          tooltip: { distance: 6 },
          threshold: 0,
          borderColor: "#ffffff"
        }, { cropShoulder: 0, directTouch: true, trackerGroups: ["group", "dataLabelsGroup"], negStacks: true, init: function() {
          E.prototype.init.apply(this, arguments);
          var a2 = this, f2 = a2.chart;
          f2.hasRendered && f2.series.forEach(function(f3) {
            f3.type === a2.type && (f3.isDirty = true);
          });
        }, getColumnMetrics: function() {
          var a2 = this, f2 = a2.options, n3 = a2.xAxis, v2 = a2.yAxis, m = n3.options.reversedStacks;
          m = n3.reversed && !m || !n3.reversed && m;
          var h, l = {}, k = 0;
          false === f2.grouping ? k = 1 : a2.chart.series.forEach(function(d2) {
            var g2 = d2.yAxis, f3 = d2.options;
            if (d2.type === a2.type && (d2.visible || !a2.chart.options.chart.ignoreHiddenSeries) && v2.len === g2.len && v2.pos === g2.pos) {
              if (f3.stacking && "group" !== f3.stacking) {
                h = d2.stackKey;
                "undefined" === typeof l[h] && (l[h] = k++);
                var m2 = l[h];
              } else false !== f3.grouping && (m2 = k++);
              d2.columnIndex = m2;
            }
          });
          var g = Math.min(Math.abs(n3.transA) * (n3.ordinal && n3.ordinal.slope || f2.pointRange || n3.closestPointRange || n3.tickInterval || 1), n3.len), d = g * f2.groupPadding, x = (g - 2 * d) / (k || 1);
          f2 = Math.min(f2.maxPointWidth || n3.len, q(f2.pointWidth, x * (1 - 2 * f2.pointPadding)));
          a2.columnMetrics = { width: f2, offset: (x - f2) / 2 + (d + ((a2.columnIndex || 0) + (m ? 1 : 0)) * x - g / 2) * (m ? -1 : 1), paddedWidth: x, columnCount: k };
          return a2.columnMetrics;
        }, crispCol: function(a2, f2, n3, q2) {
          var m = this.chart, h = this.borderWidth, l = -(h % 2 ? 0.5 : 0);
          h = h % 2 ? 0.5 : 1;
          m.inverted && m.renderer.isVML && (h += 1);
          this.options.crisp && (n3 = Math.round(a2 + n3) + l, a2 = Math.round(a2) + l, n3 -= a2);
          q2 = Math.round(f2 + q2) + h;
          l = 0.5 >= Math.abs(f2) && 0.5 < q2;
          f2 = Math.round(f2) + h;
          q2 -= f2;
          l && q2 && (--f2, q2 += 1);
          return { x: a2, y: f2, width: n3, height: q2 };
        }, adjustForMissingColumns: function(a2, n3, q2, v2) {
          var m = this, h = this.options.stacking;
          if (!q2.isNull && 1 < v2.columnCount) {
            var l = 0, k = 0;
            K(this.yAxis.stacking && this.yAxis.stacking.stacks, function(a3) {
              if ("number" === typeof q2.x && (a3 = a3[q2.x.toString()])) {
                var d = a3.points[m.index], g = a3.total;
                h ? (d && (l = k), a3.hasValidPoints && k++) : f.isArray(d) && (l = d[1], k = g || 0);
              }
            });
            a2 = (q2.plotX || 0) + ((k - 1) * v2.paddedWidth + n3) / 2 - n3 - l * v2.paddedWidth;
          }
          return a2;
        }, translate: function() {
          var a2 = this, f2 = a2.chart, n3 = a2.options, u = a2.dense = 2 > a2.closestPointRange * a2.xAxis.transA;
          u = a2.borderWidth = q(n3.borderWidth, u ? 0 : 1);
          var m = a2.xAxis, h = a2.yAxis, l = n3.threshold, k = a2.translatedThreshold = h.getThreshold(l), g = q(n3.minPointLength, 5), d = a2.getColumnMetrics(), x = d.width, r = a2.barW = Math.max(x, 1 + 2 * u), A = a2.pointXOffset = d.offset, y2 = a2.dataMin, B = a2.dataMax;
          f2.inverted && (k -= 0.5);
          n3.pointPadding && (r = Math.ceil(r));
          E.prototype.translate.apply(a2);
          a2.points.forEach(function(p) {
            var t = q(p.yBottom, k), F = 999 + Math.abs(t), e = x, c = p.plotX || 0;
            F = C(p.plotY, -F, h.len + F);
            var b = c + A, u2 = r, w = Math.min(F, t), E2 = Math.max(F, t) - w;
            if (g && Math.abs(E2) < g) {
              E2 = g;
              var M = !h.reversed && !p.negative || h.reversed && p.negative;
              v(l) && v(B) && p.y === l && B <= l && (h.min || 0) < l && y2 !== B && (M = !M);
              w = Math.abs(w - k) > g ? t - g : k - (M ? g : 0);
            }
            J(p.options.pointWidth) && (e = u2 = Math.ceil(p.options.pointWidth), b -= Math.round((e - x) / 2));
            n3.centerInCategory && (b = a2.adjustForMissingColumns(b, e, p, d));
            p.barX = b;
            p.pointWidth = e;
            p.tooltipPos = f2.inverted ? [h.len + h.pos - f2.plotLeft - F, m.len + m.pos - f2.plotTop - (c || 0) - A - u2 / 2, E2] : [b + u2 / 2, F + h.pos - f2.plotTop, E2];
            p.shapeType = a2.pointClass.prototype.shapeType || "rect";
            p.shapeArgs = a2.crispCol.apply(a2, p.isNull ? [b, k, u2, 0] : [b, w, u2, E2]);
          });
        }, getSymbol: f.noop, drawLegendSymbol: n2.drawRectangle, drawGraph: function() {
          this.group[this.dense ? "addClass" : "removeClass"]("highcharts-dense-data");
        }, pointAttribs: function(a2, f2) {
          var p = this.options, n3 = this.pointAttrToOptions || {};
          var m = n3.stroke || "borderColor";
          var h = n3["stroke-width"] || "borderWidth", l = a2 && a2.color || this.color, k = a2 && a2[m] || p[m] || this.color || l, g = a2 && a2[h] || p[h] || this[h] || 0;
          n3 = a2 && a2.options.dashStyle || p.dashStyle;
          var d = q(a2 && a2.opacity, p.opacity, 1);
          if (a2 && this.zones.length) {
            var x = a2.getZone();
            l = a2.options.color || x && (x.color || a2.nonZonedColor) || this.color;
            x && (k = x.borderColor || k, n3 = x.dashStyle || n3, g = x.borderWidth || g);
          }
          f2 && a2 && (a2 = L(p.states[f2], a2.options.states && a2.options.states[f2] || {}), f2 = a2.brightness, l = a2.color || "undefined" !== typeof f2 && D(l).brighten(a2.brightness).get() || l, k = a2[m] || k, g = a2[h] || g, n3 = a2.dashStyle || n3, d = q(a2.opacity, d));
          m = { fill: l, stroke: k, "stroke-width": g, opacity: d };
          n3 && (m.dashstyle = n3);
          return m;
        }, drawPoints: function() {
          var a2 = this, f2 = this.chart, n3 = a2.options, q2 = f2.renderer, m = n3.animationLimit || 250, h;
          a2.points.forEach(function(l) {
            var k = l.graphic, g = !!k, d = k && f2.pointCount < m ? "animate" : "attr";
            if (v(l.plotY) && null !== l.y) {
              h = l.shapeArgs;
              k && l.hasNewShapeType() && (k = k.destroy());
              a2.enabledDataSorting && (l.startXPos = a2.xAxis.reversed ? -(h ? h.width : 0) : a2.xAxis.width);
              k || (l.graphic = k = q2[l.shapeType](h).add(l.group || a2.group)) && a2.enabledDataSorting && f2.hasRendered && f2.pointCount < m && (k.attr({ x: l.startXPos }), g = true, d = "animate");
              if (k && g) k[d](L(h));
              if (n3.borderRadius) k[d]({ r: n3.borderRadius });
              f2.styledMode || k[d](a2.pointAttribs(l, l.selected && "select")).shadow(false !== l.allowShadow && n3.shadow, null, n3.stacking && !n3.borderRadius);
              k.addClass(l.getClassName(), true);
            } else k && (l.graphic = k.destroy());
          });
        }, animate: function(a2) {
          var f2 = this, p = this.yAxis, n3 = f2.options, m = this.chart.inverted, h = {}, l = m ? "translateX" : "translateY";
          if (a2) h.scaleY = 1e-3, a2 = C(
            p.toPixels(n3.threshold),
            p.pos,
            p.pos + p.len
          ), m ? h.translateX = a2 - p.len : h.translateY = a2, f2.clipBox && f2.setClip(), f2.group.attr(h);
          else {
            var k = f2.group.attr(l);
            f2.group.animate({ scaleY: 1 }, H(G(f2.options.animation), { step: function(a3, d) {
              f2.group && (h[l] = k + d.pos * (p.pos - k), f2.group.attr(h));
            } }));
          }
        }, remove: function() {
          var a2 = this, f2 = a2.chart;
          f2.hasRendered && f2.series.forEach(function(f3) {
            f3.type === a2.type && (f3.isDirty = true);
          });
          E.prototype.remove.apply(a2, arguments);
        } });
        "";
      });
      O(n, "Series/BarSeries.js", [n["Core/Utilities.js"]], function(f) {
        f = f.seriesType;
        f(
          "bar",
          "column",
          null,
          { inverted: true }
        );
        "";
      });
      O(n, "Series/ScatterSeries.js", [n["Core/Globals.js"], n["Core/Utilities.js"]], function(f, a) {
        var n2 = a.addEvent;
        a = a.seriesType;
        var y = f.Series;
        a("scatter", "line", { lineWidth: 0, findNearestPointBy: "xy", jitter: { x: 0, y: 0 }, marker: { enabled: true }, tooltip: { headerFormat: '<span style="color:{point.color}">●</span> <span style="font-size: 10px"> {series.name}</span><br/>', pointFormat: "x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>" } }, { sorted: false, requireSorting: false, noSharedTooltip: true, trackerGroups: [
          "group",
          "markerGroup",
          "dataLabelsGroup"
        ], takeOrdinalPosition: false, drawGraph: function() {
          this.options.lineWidth && y.prototype.drawGraph.call(this);
        }, applyJitter: function() {
          var a2 = this, f2 = this.options.jitter, n3 = this.points.length;
          f2 && this.points.forEach(function(C, y2) {
            ["x", "y"].forEach(function(v, D) {
              var q = "plot" + v.toUpperCase();
              if (f2[v] && !C.isNull) {
                var H = a2[v + "Axis"];
                var E = f2[v] * H.transA;
                if (H && !H.isLog) {
                  var p = Math.max(0, C[q] - E);
                  H = Math.min(H.len, C[q] + E);
                  D = 1e4 * Math.sin(y2 + D * n3);
                  C[q] = p + (H - p) * (D - Math.floor(D));
                  "x" === v && (C.clientX = C.plotX);
                }
              }
            });
          });
        } });
        n2(y, "afterTranslate", function() {
          this.applyJitter && this.applyJitter();
        });
        "";
      });
      O(n, "Mixins/CenteredSeries.js", [n["Core/Globals.js"], n["Core/Utilities.js"]], function(f, a) {
        var n2 = a.isNumber, y = a.pick, D = a.relativeLength, G = f.deg2rad;
        return f.CenteredSeriesMixin = { getCenter: function() {
          var a2 = this.options, n3 = this.chart, H = 2 * (a2.slicedOffset || 0), v = n3.plotWidth - 2 * H, G2 = n3.plotHeight - 2 * H, q = a2.center, K = Math.min(v, G2), E = a2.size, p = a2.innerSize || 0;
          "string" === typeof E && (E = parseFloat(E));
          "string" === typeof p && (p = parseFloat(p));
          a2 = [y(q[0], "50%"), y(q[1], "50%"), y(E && 0 > E ? void 0 : a2.size, "100%"), y(p && 0 > p ? void 0 : a2.innerSize || 0, "0%")];
          !n3.angular || this instanceof f.Series || (a2[3] = 0);
          for (q = 0; 4 > q; ++q) E = a2[q], n3 = 2 > q || 2 === q && /%$/.test(E), a2[q] = D(E, [v, G2, K, a2[2]][q]) + (n3 ? H : 0);
          a2[3] > a2[2] && (a2[3] = a2[2]);
          return a2;
        }, getStartAndEndRadians: function(a2, f2) {
          a2 = n2(a2) ? a2 : 0;
          f2 = n2(f2) && f2 > a2 && 360 > f2 - a2 ? f2 : a2 + 360;
          return { start: G * (a2 + -90), end: G * (f2 + -90) };
        } };
      });
      O(n, "Series/PieSeries.js", [
        n["Core/Globals.js"],
        n["Core/Renderer/SVG/SVGRenderer.js"],
        n["Mixins/LegendSymbol.js"],
        n["Core/Series/Point.js"],
        n["Core/Utilities.js"],
        n["Mixins/CenteredSeries.js"]
      ], function(f, a, n2, y, D, G) {
        var C = D.addEvent, J = D.clamp, H = D.defined, v = D.fireEvent, L = D.isNumber, q = D.merge, K = D.pick, E = D.relativeLength, p = D.seriesType, t = D.setAnimation, I = G.getStartAndEndRadians;
        D = f.noop;
        var u = f.Series;
        p("pie", "line", { center: [null, null], clip: false, colorByPoint: true, dataLabels: { allowOverlap: true, connectorPadding: 5, connectorShape: "fixedOffset", crookDistance: "70%", distance: 30, enabled: true, formatter: function() {
          return this.point.isNull ? void 0 : this.point.name;
        }, softConnector: true, x: 0 }, fillColor: void 0, ignoreHiddenPoint: true, inactiveOtherPoints: true, legendType: "point", marker: null, size: null, showInLegend: false, slicedOffset: 10, stickyTracking: false, tooltip: { followPointer: true }, borderColor: "#ffffff", borderWidth: 1, lineWidth: void 0, states: { hover: { brightness: 0.1 } } }, { isCartesian: false, requireSorting: false, directTouch: true, noSharedTooltip: true, trackerGroups: ["group", "dataLabelsGroup"], axisTypes: [], pointAttribs: f.seriesTypes.column.prototype.pointAttribs, animate: function(a2) {
          var f2 = this, l = f2.points, k = f2.startAngleRad;
          a2 || l.forEach(function(a3) {
            var d = a3.graphic, g = a3.shapeArgs;
            d && g && (d.attr({ r: K(a3.startR, f2.center && f2.center[3] / 2), start: k, end: k }), d.animate({ r: g.r, start: g.start, end: g.end }, f2.options.animation));
          });
        }, hasData: function() {
          return !!this.processedXData.length;
        }, updateTotals: function() {
          var a2, f2 = 0, l = this.points, k = l.length, g = this.options.ignoreHiddenPoint;
          for (a2 = 0; a2 < k; a2++) {
            var d = l[a2];
            f2 += g && !d.visible ? 0 : d.isNull ? 0 : d.y;
          }
          this.total = f2;
          for (a2 = 0; a2 < k; a2++) d = l[a2], d.percentage = 0 < f2 && (d.visible || !g) ? d.y / f2 * 100 : 0, d.total = f2;
        }, generatePoints: function() {
          u.prototype.generatePoints.call(this);
          this.updateTotals();
        }, getX: function(a2, f2, l) {
          var h = this.center, g = this.radii ? this.radii[l.index] : h[2] / 2;
          a2 = Math.asin(J((a2 - h[1]) / (g + l.labelDistance), -1, 1));
          return h[0] + (f2 ? -1 : 1) * Math.cos(a2) * (g + l.labelDistance) + (0 < l.labelDistance ? (f2 ? -1 : 1) * this.options.dataLabels.padding : 0);
        }, translate: function(a2) {
          this.generatePoints();
          var f2 = 0, l = this.options, k = l.slicedOffset, g = k + (l.borderWidth || 0), d = I(l.startAngle, l.endAngle), m = this.startAngleRad = d.start;
          d = (this.endAngleRad = d.end) - m;
          var n3 = this.points, p2 = l.dataLabels.distance;
          l = l.ignoreHiddenPoint;
          var q2, t2 = n3.length;
          a2 || (this.center = a2 = this.getCenter());
          for (q2 = 0; q2 < t2; q2++) {
            var u2 = n3[q2];
            var y2 = m + f2 * d;
            if (!l || u2.visible) f2 += u2.percentage / 100;
            var F = m + f2 * d;
            u2.shapeType = "arc";
            u2.shapeArgs = { x: a2[0], y: a2[1], r: a2[2] / 2, innerR: a2[3] / 2, start: Math.round(1e3 * y2) / 1e3, end: Math.round(1e3 * F) / 1e3 };
            u2.labelDistance = K(u2.options.dataLabels && u2.options.dataLabels.distance, p2);
            u2.labelDistance = E(u2.labelDistance, u2.shapeArgs.r);
            this.maxLabelDistance = Math.max(this.maxLabelDistance || 0, u2.labelDistance);
            F = (F + y2) / 2;
            F > 1.5 * Math.PI ? F -= 2 * Math.PI : F < -Math.PI / 2 && (F += 2 * Math.PI);
            u2.slicedTranslation = { translateX: Math.round(Math.cos(F) * k), translateY: Math.round(Math.sin(F) * k) };
            var e = Math.cos(F) * a2[2] / 2;
            var c = Math.sin(F) * a2[2] / 2;
            u2.tooltipPos = [a2[0] + 0.7 * e, a2[1] + 0.7 * c];
            u2.half = F < -Math.PI / 2 || F > Math.PI / 2 ? 1 : 0;
            u2.angle = F;
            y2 = Math.min(g, u2.labelDistance / 5);
            u2.labelPosition = { natural: { x: a2[0] + e + Math.cos(F) * u2.labelDistance, y: a2[1] + c + Math.sin(F) * u2.labelDistance }, "final": {}, alignment: 0 > u2.labelDistance ? "center" : u2.half ? "right" : "left", connectorPosition: { breakAt: { x: a2[0] + e + Math.cos(F) * y2, y: a2[1] + c + Math.sin(F) * y2 }, touchingSliceAt: { x: a2[0] + e, y: a2[1] + c } } };
          }
          v(this, "afterTranslate");
        }, drawEmpty: function() {
          var f2 = this.startAngleRad, h = this.endAngleRad, l = this.options;
          if (0 === this.total && this.center) {
            var k = this.center[0];
            var g = this.center[1];
            this.graph || (this.graph = this.chart.renderer.arc(k, g, this.center[1] / 2, 0, f2, h).addClass("highcharts-empty-series").add(this.group));
            this.graph.attr({ d: a.prototype.symbols.arc(
              k,
              g,
              this.center[2] / 2,
              0,
              { start: f2, end: h, innerR: this.center[3] / 2 }
            ) });
            this.chart.styledMode || this.graph.attr({ "stroke-width": l.borderWidth, fill: l.fillColor || "none", stroke: l.color || "#cccccc" });
          } else this.graph && (this.graph = this.graph.destroy());
        }, redrawPoints: function() {
          var a2 = this, f2 = a2.chart, l = f2.renderer, k, g, d, n3, p2 = a2.options.shadow;
          this.drawEmpty();
          !p2 || a2.shadowGroup || f2.styledMode || (a2.shadowGroup = l.g("shadow").attr({ zIndex: -1 }).add(a2.group));
          a2.points.forEach(function(h) {
            var m = {};
            g = h.graphic;
            if (!h.isNull && g) {
              n3 = h.shapeArgs;
              k = h.getTranslate();
              if (!f2.styledMode) {
                var r = h.shadowGroup;
                p2 && !r && (r = h.shadowGroup = l.g("shadow").add(a2.shadowGroup));
                r && r.attr(k);
                d = a2.pointAttribs(h, h.selected && "select");
              }
              h.delayedRendering ? (g.setRadialReference(a2.center).attr(n3).attr(k), f2.styledMode || g.attr(d).attr({ "stroke-linejoin": "round" }).shadow(p2, r), h.delayedRendering = false) : (g.setRadialReference(a2.center), f2.styledMode || q(true, m, d), q(true, m, n3, k), g.animate(m));
              g.attr({ visibility: h.visible ? "inherit" : "hidden" });
              g.addClass(h.getClassName());
            } else g && (h.graphic = g.destroy());
          });
        }, drawPoints: function() {
          var a2 = this.chart.renderer;
          this.points.forEach(function(f2) {
            f2.graphic && f2.hasNewShapeType() && (f2.graphic = f2.graphic.destroy());
            f2.graphic || (f2.graphic = a2[f2.shapeType](f2.shapeArgs).add(f2.series.group), f2.delayedRendering = true);
          });
        }, searchPoint: D, sortByAngle: function(a2, f2) {
          a2.sort(function(a3, h) {
            return "undefined" !== typeof a3.angle && (h.angle - a3.angle) * f2;
          });
        }, drawLegendSymbol: n2.drawRectangle, getCenter: G.getCenter, getSymbol: D, drawGraph: null }, { init: function() {
          y.prototype.init.apply(
            this,
            arguments
          );
          var a2 = this;
          a2.name = K(a2.name, "Slice");
          var f2 = function(f3) {
            a2.slice("select" === f3.type);
          };
          C(a2, "select", f2);
          C(a2, "unselect", f2);
          return a2;
        }, isValid: function() {
          return L(this.y) && 0 <= this.y;
        }, setVisible: function(a2, f2) {
          var h = this, k = h.series, g = k.chart, d = k.options.ignoreHiddenPoint;
          f2 = K(f2, d);
          a2 !== h.visible && (h.visible = h.options.visible = a2 = "undefined" === typeof a2 ? !h.visible : a2, k.options.data[k.data.indexOf(h)] = h.options, ["graphic", "dataLabel", "connector", "shadowGroup"].forEach(function(d2) {
            if (h[d2]) h[d2][a2 ? "show" : "hide"](true);
          }), h.legendItem && g.legend.colorizeItem(h, a2), a2 || "hover" !== h.state || h.setState(""), d && (k.isDirty = true), f2 && g.redraw());
        }, slice: function(a2, f2, l) {
          var h = this.series;
          t(l, h.chart);
          K(f2, true);
          this.sliced = this.options.sliced = H(a2) ? a2 : !this.sliced;
          h.options.data[h.data.indexOf(this)] = this.options;
          this.graphic && this.graphic.animate(this.getTranslate());
          this.shadowGroup && this.shadowGroup.animate(this.getTranslate());
        }, getTranslate: function() {
          return this.sliced ? this.slicedTranslation : { translateX: 0, translateY: 0 };
        }, haloPath: function(a2) {
          var f2 = this.shapeArgs;
          return this.sliced || !this.visible ? [] : this.series.chart.renderer.symbols.arc(f2.x, f2.y, f2.r + a2, f2.r + a2, { innerR: f2.r - 1, start: f2.start, end: f2.end });
        }, connectorShapes: { fixedOffset: function(a2, f2, l) {
          var h = f2.breakAt;
          f2 = f2.touchingSliceAt;
          return [["M", a2.x, a2.y], l.softConnector ? ["C", a2.x + ("left" === a2.alignment ? -5 : 5), a2.y, 2 * h.x - f2.x, 2 * h.y - f2.y, h.x, h.y] : ["L", h.x, h.y], ["L", f2.x, f2.y]];
        }, straight: function(a2, f2) {
          f2 = f2.touchingSliceAt;
          return [["M", a2.x, a2.y], ["L", f2.x, f2.y]];
        }, crookedLine: function(a2, f2, l) {
          f2 = f2.touchingSliceAt;
          var h = this.series, g = h.center[0], d = h.chart.plotWidth, m = h.chart.plotLeft;
          h = a2.alignment;
          var n3 = this.shapeArgs.r;
          l = E(l.crookDistance, 1);
          d = "left" === h ? g + n3 + (d + m - g - n3) * (1 - l) : m + (g - n3) * l;
          l = ["L", d, a2.y];
          g = true;
          if ("left" === h ? d > a2.x || d < f2.x : d < a2.x || d > f2.x) g = false;
          a2 = [["M", a2.x, a2.y]];
          g && a2.push(l);
          a2.push(["L", f2.x, f2.y]);
          return a2;
        } }, getConnectorPath: function() {
          var a2 = this.labelPosition, f2 = this.series.options.dataLabels, l = f2.connectorShape, k = this.connectorShapes;
          k[l] && (l = k[l]);
          return l.call(
            this,
            { x: a2.final.x, y: a2.final.y, alignment: a2.alignment },
            a2.connectorPosition,
            f2
          );
        } });
        "";
      });
      O(n, "Core/Series/DataLabels.js", [n["Core/Globals.js"], n["Core/Utilities.js"]], function(f, a) {
        var n2 = f.noop, y = f.seriesTypes, D = a.arrayMax, G = a.clamp, C = a.defined, J = a.extend, H = a.fireEvent, v = a.format, L = a.getDeferredAnimation, q = a.isArray, K = a.merge, E = a.objectEach, p = a.pick, t = a.relativeLength, I = a.splat, u = a.stableSort, m = f.Series;
        f.distribute = function(a2, l, k) {
          function g(a3, d2) {
            return a3.target - d2.target;
          }
          var d, h = true, m2 = a2, n3 = [];
          var q2 = 0;
          var t2 = m2.reducedLen || l;
          for (d = a2.length; d--; ) q2 += a2[d].size;
          if (q2 > t2) {
            u(a2, function(a3, d2) {
              return (d2.rank || 0) - (a3.rank || 0);
            });
            for (q2 = d = 0; q2 <= t2; ) q2 += a2[d].size, d++;
            n3 = a2.splice(d - 1, a2.length);
          }
          u(a2, g);
          for (a2 = a2.map(function(a3) {
            return { size: a3.size, targets: [a3.target], align: p(a3.align, 0.5) };
          }); h; ) {
            for (d = a2.length; d--; ) h = a2[d], q2 = (Math.min.apply(0, h.targets) + Math.max.apply(0, h.targets)) / 2, h.pos = G(q2 - h.size * h.align, 0, l - h.size);
            d = a2.length;
            for (h = false; d--; ) 0 < d && a2[d - 1].pos + a2[d - 1].size > a2[d].pos && (a2[d - 1].size += a2[d].size, a2[d - 1].targets = a2[d - 1].targets.concat(a2[d].targets), a2[d - 1].align = 0.5, a2[d - 1].pos + a2[d - 1].size > l && (a2[d - 1].pos = l - a2[d - 1].size), a2.splice(d, 1), h = true);
          }
          m2.push.apply(m2, n3);
          d = 0;
          a2.some(function(a3) {
            var g2 = 0;
            if (a3.targets.some(function() {
              m2[d].pos = a3.pos + g2;
              if ("undefined" !== typeof k && Math.abs(m2[d].pos - m2[d].target) > k) return m2.slice(0, d + 1).forEach(function(a4) {
                delete a4.pos;
              }), m2.reducedLen = (m2.reducedLen || l) - 0.1 * l, m2.reducedLen > 0.1 * l && f.distribute(m2, l, k), true;
              g2 += m2[d].size;
              d++;
            })) return true;
          });
          u(m2, g);
        };
        m.prototype.drawDataLabels = function() {
          function a2(a3, d2) {
            var c = d2.filter;
            return c ? (d2 = c.operator, a3 = a3[c.property], c = c.value, ">" === d2 && a3 > c || "<" === d2 && a3 < c || ">=" === d2 && a3 >= c || "<=" === d2 && a3 <= c || "==" === d2 && a3 == c || "===" === d2 && a3 === c ? true : false) : true;
          }
          function f2(a3, d2) {
            var c = [], b;
            if (q(a3) && !q(d2)) c = a3.map(function(a4) {
              return K(a4, d2);
            });
            else if (q(d2) && !q(a3)) c = d2.map(function(b2) {
              return K(a3, b2);
            });
            else if (q(a3) || q(d2)) for (b = Math.max(a3.length, d2.length); b--; ) c[b] = K(a3[b], d2[b]);
            else c = K(a3, d2);
            return c;
          }
          var k = this, g = k.chart, d = k.options, m2 = d.dataLabels, n3 = k.points, t2, u2 = k.hasRendered || 0, B = m2.animation;
          B = m2.defer ? L(g, B, k) : { defer: 0, duration: 0 };
          var y2 = g.renderer;
          m2 = f2(f2(g.options.plotOptions && g.options.plotOptions.series && g.options.plotOptions.series.dataLabels, g.options.plotOptions && g.options.plotOptions[k.type] && g.options.plotOptions[k.type].dataLabels), m2);
          H(this, "drawDataLabels");
          if (q(m2) || m2.enabled || k._hasPointLabels) {
            var D2 = k.plotGroup("dataLabelsGroup", "data-labels", u2 ? "inherit" : "hidden", m2.zIndex || 6);
            D2.attr({ opacity: +u2 });
            !u2 && (u2 = k.dataLabelsGroup) && (k.visible && D2.show(true), u2[d.animation ? "animate" : "attr"]({ opacity: 1 }, B));
            n3.forEach(function(h) {
              t2 = I(f2(m2, h.dlOptions || h.options && h.options.dataLabels));
              t2.forEach(function(e, c) {
                var b = e.enabled && (!h.isNull || h.dataLabelOnNull) && a2(h, e), f3 = h.dataLabels ? h.dataLabels[c] : h.dataLabel, l = h.connectors ? h.connectors[c] : h.connector, m3 = p(e.distance, h.labelDistance), n4 = !f3;
                if (b) {
                  var r = h.getLabelConfig();
                  var q2 = p(e[h.formatPrefix + "Format"], e.format);
                  r = C(q2) ? v(q2, r, g) : (e[h.formatPrefix + "Formatter"] || e.formatter).call(r, e);
                  q2 = e.style;
                  var t3 = e.rotation;
                  g.styledMode || (q2.color = p(e.color, q2.color, k.color, "#000000"), "contrast" === q2.color ? (h.contrastColor = y2.getContrast(h.color || k.color), q2.color = !C(m3) && e.inside || 0 > m3 || d.stacking ? h.contrastColor : "#000000") : delete h.contrastColor, d.cursor && (q2.cursor = d.cursor));
                  var x = { r: e.borderRadius || 0, rotation: t3, padding: e.padding, zIndex: 1 };
                  g.styledMode || (x.fill = e.backgroundColor, x.stroke = e.borderColor, x["stroke-width"] = e.borderWidth);
                  E(x, function(a3, b2) {
                    "undefined" === typeof a3 && delete x[b2];
                  });
                }
                !f3 || b && C(r) ? b && C(r) && (f3 ? x.text = r : (h.dataLabels = h.dataLabels || [], f3 = h.dataLabels[c] = t3 ? y2.text(r, 0, -9999, e.useHTML).addClass("highcharts-data-label") : y2.label(
                  r,
                  0,
                  -9999,
                  e.shape,
                  null,
                  null,
                  e.useHTML,
                  null,
                  "data-label"
                ), c || (h.dataLabel = f3), f3.addClass(" highcharts-data-label-color-" + h.colorIndex + " " + (e.className || "") + (e.useHTML ? " highcharts-tracker" : ""))), f3.options = e, f3.attr(x), g.styledMode || f3.css(q2).shadow(e.shadow), f3.added || f3.add(D2), e.textPath && !e.useHTML && (f3.setTextPath(h.getDataLabelPath && h.getDataLabelPath(f3) || h.graphic, e.textPath), h.dataLabelPath && !e.textPath.enabled && (h.dataLabelPath = h.dataLabelPath.destroy())), k.alignDataLabel(h, f3, e, null, n4)) : (h.dataLabel = h.dataLabel && h.dataLabel.destroy(), h.dataLabels && (1 === h.dataLabels.length ? delete h.dataLabels : delete h.dataLabels[c]), c || delete h.dataLabel, l && (h.connector = h.connector.destroy(), h.connectors && (1 === h.connectors.length ? delete h.connectors : delete h.connectors[c])));
              });
            });
          }
          H(this, "afterDrawDataLabels");
        };
        m.prototype.alignDataLabel = function(a2, f2, k, g, d) {
          var h = this, l = this.chart, m2 = this.isCartesian && l.inverted, n3 = this.enabledDataSorting, q2 = p(a2.dlBox && a2.dlBox.centerX, a2.plotX, -9999), t2 = p(a2.plotY, -9999), v2 = f2.getBBox(), u2 = k.rotation, e = k.align, c = l.isInsidePlot(q2, Math.round(t2), m2), b = "justify" === p(k.overflow, n3 ? "none" : "justify"), z = this.visible && false !== a2.visible && (a2.series.forceDL || n3 && !b || c || k.inside && g && l.isInsidePlot(q2, m2 ? g.x + 1 : g.y + g.height - 1, m2));
          var w = function(e2) {
            n3 && h.xAxis && !b && h.setDataLabelStartPos(a2, f2, d, c, e2);
          };
          if (z) {
            var y2 = l.renderer.fontMetrics(l.styledMode ? void 0 : k.style.fontSize, f2).b;
            g = J({ x: m2 ? this.yAxis.len - t2 : q2, y: Math.round(m2 ? this.xAxis.len - q2 : t2), width: 0, height: 0 }, g);
            J(k, { width: v2.width, height: v2.height });
            u2 ? (b = false, q2 = l.renderer.rotCorr(
              y2,
              u2
            ), q2 = { x: g.x + (k.x || 0) + g.width / 2 + q2.x, y: g.y + (k.y || 0) + { top: 0, middle: 0.5, bottom: 1 }[k.verticalAlign] * g.height }, w(q2), f2[d ? "attr" : "animate"](q2).attr({ align: e }), w = (u2 + 720) % 360, w = 180 < w && 360 > w, "left" === e ? q2.y -= w ? v2.height : 0 : "center" === e ? (q2.x -= v2.width / 2, q2.y -= v2.height / 2) : "right" === e && (q2.x -= v2.width, q2.y -= w ? 0 : v2.height), f2.placed = true, f2.alignAttr = q2) : (w(g), f2.align(k, null, g), q2 = f2.alignAttr);
            b && 0 <= g.height ? this.justifyDataLabel(f2, k, q2, v2, g, d) : p(k.crop, true) && (z = l.isInsidePlot(q2.x, q2.y) && l.isInsidePlot(q2.x + v2.width, q2.y + v2.height));
            if (k.shape && !u2) f2[d ? "attr" : "animate"]({ anchorX: m2 ? l.plotWidth - a2.plotY : a2.plotX, anchorY: m2 ? l.plotHeight - a2.plotX : a2.plotY });
          }
          d && n3 && (f2.placed = false);
          z || n3 && !b || (f2.hide(true), f2.placed = false);
        };
        m.prototype.setDataLabelStartPos = function(a2, f2, k, g, d) {
          var h = this.chart, l = h.inverted, m2 = this.xAxis, n3 = m2.reversed, p2 = l ? f2.height / 2 : f2.width / 2;
          a2 = (a2 = a2.pointWidth) ? a2 / 2 : 0;
          m2 = l ? d.x : n3 ? -p2 - a2 : m2.width - p2 + a2;
          d = l ? n3 ? this.yAxis.height - p2 + a2 : -p2 - a2 : d.y;
          f2.startXPos = m2;
          f2.startYPos = d;
          g ? "hidden" === f2.visibility && (f2.show(), f2.attr({ opacity: 0 }).animate({ opacity: 1 })) : f2.attr({ opacity: 1 }).animate(
            { opacity: 0 },
            void 0,
            f2.hide
          );
          h.hasRendered && (k && f2.attr({ x: f2.startXPos, y: f2.startYPos }), f2.placed = true);
        };
        m.prototype.justifyDataLabel = function(a2, f2, k, g, d, m2) {
          var h = this.chart, l = f2.align, n3 = f2.verticalAlign, p2 = a2.box ? 0 : a2.padding || 0, q2 = f2.x;
          q2 = void 0 === q2 ? 0 : q2;
          var t2 = f2.y;
          var x = void 0 === t2 ? 0 : t2;
          t2 = k.x + p2;
          if (0 > t2) {
            "right" === l && 0 <= q2 ? (f2.align = "left", f2.inside = true) : q2 -= t2;
            var e = true;
          }
          t2 = k.x + g.width - p2;
          t2 > h.plotWidth && ("left" === l && 0 >= q2 ? (f2.align = "right", f2.inside = true) : q2 += h.plotWidth - t2, e = true);
          t2 = k.y + p2;
          0 > t2 && ("bottom" === n3 && 0 <= x ? (f2.verticalAlign = "top", f2.inside = true) : x -= t2, e = true);
          t2 = k.y + g.height - p2;
          t2 > h.plotHeight && ("top" === n3 && 0 >= x ? (f2.verticalAlign = "bottom", f2.inside = true) : x += h.plotHeight - t2, e = true);
          e && (f2.x = q2, f2.y = x, a2.placed = !m2, a2.align(f2, void 0, d));
          return e;
        };
        y.pie && (y.pie.prototype.dataLabelPositioners = { radialDistributionY: function(a2) {
          return a2.top + a2.distributeBox.pos;
        }, radialDistributionX: function(a2, f2, k, g) {
          return a2.getX(k < f2.top + 2 || k > f2.bottom - 2 ? g : k, f2.half, f2);
        }, justify: function(a2, f2, k) {
          return k[0] + (a2.half ? -1 : 1) * (f2 + a2.labelDistance);
        }, alignToPlotEdges: function(a2, f2, k, g) {
          a2 = a2.getBBox().width;
          return f2 ? a2 + g : k - a2 - g;
        }, alignToConnectors: function(a2, f2, k, g) {
          var d = 0, h;
          a2.forEach(function(a3) {
            h = a3.dataLabel.getBBox().width;
            h > d && (d = h);
          });
          return f2 ? d + g : k - d - g;
        } }, y.pie.prototype.drawDataLabels = function() {
          var a2 = this, l = a2.data, k, g = a2.chart, d = a2.options.dataLabels || {}, n3 = d.connectorPadding, r, q2 = g.plotWidth, t2 = g.plotHeight, v2 = g.plotLeft, u2 = Math.round(g.chartWidth / 3), y2, F = a2.center, e = F[2] / 2, c = F[1], b, z, w, E2, H2 = [[], []], G2, I2, J2, L2, O2 = [0, 0, 0, 0], S = a2.dataLabelPositioners, V;
          a2.visible && (d.enabled || a2._hasPointLabels) && (l.forEach(function(a3) {
            a3.dataLabel && a3.visible && a3.dataLabel.shortened && (a3.dataLabel.attr({ width: "auto" }).css({ width: "auto", textOverflow: "clip" }), a3.dataLabel.shortened = false);
          }), m.prototype.drawDataLabels.apply(a2), l.forEach(function(a3) {
            a3.dataLabel && (a3.visible ? (H2[a3.half].push(a3), a3.dataLabel._pos = null, !C(d.style.width) && !C(a3.options.dataLabels && a3.options.dataLabels.style && a3.options.dataLabels.style.width) && a3.dataLabel.getBBox().width > u2 && (a3.dataLabel.css({ width: Math.round(0.7 * u2) + "px" }), a3.dataLabel.shortened = true)) : (a3.dataLabel = a3.dataLabel.destroy(), a3.dataLabels && 1 === a3.dataLabels.length && delete a3.dataLabels));
          }), H2.forEach(function(h, l2) {
            var m2 = h.length, r2 = [], x;
            if (m2) {
              a2.sortByAngle(h, l2 - 0.5);
              if (0 < a2.maxLabelDistance) {
                var u3 = Math.max(0, c - e - a2.maxLabelDistance);
                var A = Math.min(c + e + a2.maxLabelDistance, g.plotHeight);
                h.forEach(function(a3) {
                  0 < a3.labelDistance && a3.dataLabel && (a3.top = Math.max(0, c - e - a3.labelDistance), a3.bottom = Math.min(c + e + a3.labelDistance, g.plotHeight), x = a3.dataLabel.getBBox().height || 21, a3.distributeBox = {
                    target: a3.labelPosition.natural.y - a3.top + x / 2,
                    size: x,
                    rank: a3.y
                  }, r2.push(a3.distributeBox));
                });
                u3 = A + x - u3;
                f.distribute(r2, u3, u3 / 5);
              }
              for (L2 = 0; L2 < m2; L2++) {
                k = h[L2];
                w = k.labelPosition;
                b = k.dataLabel;
                J2 = false === k.visible ? "hidden" : "inherit";
                I2 = u3 = w.natural.y;
                r2 && C(k.distributeBox) && ("undefined" === typeof k.distributeBox.pos ? J2 = "hidden" : (E2 = k.distributeBox.size, I2 = S.radialDistributionY(k)));
                delete k.positionIndex;
                if (d.justify) G2 = S.justify(k, e, F);
                else switch (d.alignTo) {
                  case "connectors":
                    G2 = S.alignToConnectors(h, l2, q2, v2);
                    break;
                  case "plotEdges":
                    G2 = S.alignToPlotEdges(b, l2, q2, v2);
                    break;
                  default:
                    G2 = S.radialDistributionX(
                      a2,
                      k,
                      I2,
                      u3
                    );
                }
                b._attr = { visibility: J2, align: w.alignment };
                V = k.options.dataLabels || {};
                b._pos = { x: G2 + p(V.x, d.x) + ({ left: n3, right: -n3 }[w.alignment] || 0), y: I2 + p(V.y, d.y) - 10 };
                w.final.x = G2;
                w.final.y = I2;
                p(d.crop, true) && (z = b.getBBox().width, u3 = null, G2 - z < n3 && 1 === l2 ? (u3 = Math.round(z - G2 + n3), O2[3] = Math.max(u3, O2[3])) : G2 + z > q2 - n3 && 0 === l2 && (u3 = Math.round(G2 + z - q2 + n3), O2[1] = Math.max(u3, O2[1])), 0 > I2 - E2 / 2 ? O2[0] = Math.max(Math.round(-I2 + E2 / 2), O2[0]) : I2 + E2 / 2 > t2 && (O2[2] = Math.max(Math.round(I2 + E2 / 2 - t2), O2[2])), b.sideOverflow = u3);
              }
            }
          }), 0 === D(O2) || this.verifyDataLabelOverflow(O2)) && (this.placeDataLabels(), this.points.forEach(function(c2) {
            V = K(d, c2.options.dataLabels);
            if (r = p(V.connectorWidth, 1)) {
              var e2;
              y2 = c2.connector;
              if ((b = c2.dataLabel) && b._pos && c2.visible && 0 < c2.labelDistance) {
                J2 = b._attr.visibility;
                if (e2 = !y2) c2.connector = y2 = g.renderer.path().addClass("highcharts-data-label-connector  highcharts-color-" + c2.colorIndex + (c2.className ? " " + c2.className : "")).add(a2.dataLabelsGroup), g.styledMode || y2.attr({ "stroke-width": r, stroke: V.connectorColor || c2.color || "#666666" });
                y2[e2 ? "attr" : "animate"]({ d: c2.getConnectorPath() });
                y2.attr("visibility", J2);
              } else y2 && (c2.connector = y2.destroy());
            }
          }));
        }, y.pie.prototype.placeDataLabels = function() {
          this.points.forEach(function(a2) {
            var f2 = a2.dataLabel, h;
            f2 && a2.visible && ((h = f2._pos) ? (f2.sideOverflow && (f2._attr.width = Math.max(f2.getBBox().width - f2.sideOverflow, 0), f2.css({ width: f2._attr.width + "px", textOverflow: (this.options.dataLabels.style || {}).textOverflow || "ellipsis" }), f2.shortened = true), f2.attr(f2._attr), f2[f2.moved ? "animate" : "attr"](h), f2.moved = true) : f2 && f2.attr({ y: -9999 }));
            delete a2.distributeBox;
          }, this);
        }, y.pie.prototype.alignDataLabel = n2, y.pie.prototype.verifyDataLabelOverflow = function(a2) {
          var f2 = this.center, h = this.options, g = h.center, d = h.minSize || 80, m2 = null !== h.size;
          if (!m2) {
            if (null !== g[0]) var n3 = Math.max(f2[2] - Math.max(a2[1], a2[3]), d);
            else n3 = Math.max(f2[2] - a2[1] - a2[3], d), f2[0] += (a2[3] - a2[1]) / 2;
            null !== g[1] ? n3 = G(n3, d, f2[2] - Math.max(a2[0], a2[2])) : (n3 = G(n3, d, f2[2] - a2[0] - a2[2]), f2[1] += (a2[0] - a2[2]) / 2);
            n3 < f2[2] ? (f2[2] = n3, f2[3] = Math.min(t(h.innerSize || 0, n3), n3), this.translate(f2), this.drawDataLabels && this.drawDataLabels()) : m2 = true;
          }
          return m2;
        });
        y.column && (y.column.prototype.alignDataLabel = function(a2, f2, k, g, d) {
          var h = this.chart.inverted, l = a2.series, n3 = a2.dlBox || a2.shapeArgs, q2 = p(a2.below, a2.plotY > p(this.translatedThreshold, l.yAxis.len)), t2 = p(k.inside, !!this.options.stacking);
          n3 && (g = K(n3), 0 > g.y && (g.height += g.y, g.y = 0), n3 = g.y + g.height - l.yAxis.len, 0 < n3 && n3 < g.height && (g.height -= n3), h && (g = { x: l.yAxis.len - g.y - g.height, y: l.xAxis.len - g.x - g.width, width: g.height, height: g.width }), t2 || (h ? (g.x += q2 ? 0 : g.width, g.width = 0) : (g.y += q2 ? g.height : 0, g.height = 0)));
          k.align = p(k.align, !h || t2 ? "center" : q2 ? "right" : "left");
          k.verticalAlign = p(k.verticalAlign, h || t2 ? "middle" : q2 ? "top" : "bottom");
          m.prototype.alignDataLabel.call(this, a2, f2, k, g, d);
          k.inside && a2.contrastColor && f2.css({ color: a2.contrastColor });
        });
      });
      O(n, "Extensions/OverlappingDataLabels.js", [n["Core/Chart/Chart.js"], n["Core/Utilities.js"]], function(f, a) {
        var n2 = a.addEvent, y = a.fireEvent, D = a.isArray, G = a.isNumber, C = a.objectEach, J = a.pick;
        n2(f, "render", function() {
          var a2 = [];
          (this.labelCollectors || []).forEach(function(f2) {
            a2 = a2.concat(f2());
          });
          (this.yAxis || []).forEach(function(f2) {
            f2.stacking && f2.options.stackLabels && !f2.options.stackLabels.allowOverlap && C(f2.stacking.stacks, function(f3) {
              C(f3, function(f4) {
                a2.push(f4.label);
              });
            });
          });
          (this.series || []).forEach(function(f2) {
            var n3 = f2.options.dataLabels;
            f2.visible && (false !== n3.enabled || f2._hasPointLabels) && (f2.nodes || f2.points).forEach(function(f3) {
              f3.visible && (D(f3.dataLabels) ? f3.dataLabels : f3.dataLabel ? [f3.dataLabel] : []).forEach(function(n4) {
                var q = n4.options;
                n4.labelrank = J(q.labelrank, f3.labelrank, f3.shapeArgs && f3.shapeArgs.height);
                q.allowOverlap || a2.push(n4);
              });
            });
          });
          this.hideOverlappingLabels(a2);
        });
        f.prototype.hideOverlappingLabels = function(a2) {
          var f2 = this, n3 = a2.length, q = f2.renderer, C2, E, p, t = false;
          var D2 = function(a3) {
            var f3, h = a3.box ? 0 : a3.padding || 0, g = f3 = 0, d;
            if (a3 && (!a3.alignAttr || a3.placed)) {
              var m2 = a3.alignAttr || { x: a3.attr("x"), y: a3.attr("y") };
              var n4 = a3.parentGroup;
              a3.width || (f3 = a3.getBBox(), a3.width = f3.width, a3.height = f3.height, f3 = q.fontMetrics(null, a3.element).h);
              var p2 = a3.width - 2 * h;
              (d = { left: "0", center: "0.5", right: "1" }[a3.alignValue]) ? g = +d * p2 : G(a3.x) && Math.round(a3.x) !== a3.translateX && (g = a3.x - a3.translateX);
              return { x: m2.x + (n4.translateX || 0) + h - (g || 0), y: m2.y + (n4.translateY || 0) + h - f3, width: a3.width - 2 * h, height: a3.height - 2 * h };
            }
          };
          for (E = 0; E < n3; E++) if (C2 = a2[E]) C2.oldOpacity = C2.opacity, C2.newOpacity = 1, C2.absoluteBox = D2(C2);
          a2.sort(function(a3, f3) {
            return (f3.labelrank || 0) - (a3.labelrank || 0);
          });
          for (E = 0; E < n3; E++) {
            var u = (D2 = a2[E]) && D2.absoluteBox;
            for (C2 = E + 1; C2 < n3; ++C2) {
              var m = (p = a2[C2]) && p.absoluteBox;
              !u || !m || D2 === p || 0 === D2.newOpacity || 0 === p.newOpacity || m.x >= u.x + u.width || m.x + m.width <= u.x || m.y >= u.y + u.height || m.y + m.height <= u.y || ((D2.labelrank < p.labelrank ? D2 : p).newOpacity = 0);
            }
          }
          a2.forEach(function(a3) {
            if (a3) {
              var h = a3.newOpacity;
              a3.oldOpacity !== h && (a3.alignAttr && a3.placed ? (a3[h ? "removeClass" : "addClass"]("highcharts-data-label-hidden"), t = true, a3.alignAttr.opacity = h, a3[a3.isOld ? "animate" : "attr"](a3.alignAttr, null, function() {
                f2.styledMode || a3.css({ pointerEvents: h ? "auto" : "none" });
                a3.visibility = h ? "inherit" : "hidden";
              }), y(f2, "afterHideOverlappingLabel")) : a3.attr({ opacity: h }));
              a3.isOld = true;
            }
          });
          t && y(f2, "afterHideAllOverlappingLabels");
        };
      });
      O(n, "Core/Interaction.js", [
        n["Core/Chart/Chart.js"],
        n["Core/Globals.js"],
        n["Core/Legend.js"],
        n["Core/Options.js"],
        n["Core/Series/Point.js"],
        n["Core/Utilities.js"]
      ], function(f, a, n2, y, D, G) {
        var C = y.defaultOptions, J = G.addEvent, H = G.createElement, v = G.css, L = G.defined, q = G.extend, K = G.fireEvent, E = G.isArray, p = G.isFunction, t = G.isNumber, I = G.isObject, u = G.merge, m = G.objectEach, h = G.pick, l = a.hasTouch;
        y = a.Series;
        G = a.seriesTypes;
        var k = a.svg;
        var g = a.TrackerMixin = {
          drawTrackerPoint: function() {
            var a2 = this, f2 = a2.chart, g2 = f2.pointer, h2 = function(a3) {
              var d = g2.getPointFromEvent(a3);
              "undefined" !== typeof d && (g2.isDirectTouch = true, d.onMouseOver(a3));
            }, k2;
            a2.points.forEach(function(a3) {
              k2 = E(a3.dataLabels) ? a3.dataLabels : a3.dataLabel ? [a3.dataLabel] : [];
              a3.graphic && (a3.graphic.element.point = a3);
              k2.forEach(function(d) {
                d.div ? d.div.point = a3 : d.element.point = a3;
              });
            });
            a2._hasTracking || (a2.trackerGroups.forEach(function(d) {
              if (a2[d]) {
                a2[d].addClass("highcharts-tracker").on("mouseover", h2).on("mouseout", function(a3) {
                  g2.onTrackerMouseOut(a3);
                });
                if (l) a2[d].on("touchstart", h2);
                !f2.styledMode && a2.options.cursor && a2[d].css(v).css({ cursor: a2.options.cursor });
              }
            }), a2._hasTracking = true);
            K(this, "afterDrawTracker");
          },
          drawTrackerGraph: function() {
            var a2 = this, f2 = a2.options, g2 = f2.trackByArea, h2 = [].concat(g2 ? a2.areaPath : a2.graphPath), m2 = a2.chart, n3 = m2.pointer, p2 = m2.renderer, q2 = m2.options.tooltip.snap, t2 = a2.tracker, e = function(b) {
              if (m2.hoverSeries !== a2) a2.onMouseOver();
            }, c = "rgba(192,192,192," + (k ? 1e-4 : 2e-3) + ")";
            t2 ? t2.attr({ d: h2 }) : a2.graph && (a2.tracker = p2.path(h2).attr({ visibility: a2.visible ? "visible" : "hidden", zIndex: 2 }).addClass(g2 ? "highcharts-tracker-area" : "highcharts-tracker-line").add(a2.group), m2.styledMode || a2.tracker.attr({
              "stroke-linecap": "round",
              "stroke-linejoin": "round",
              stroke: c,
              fill: g2 ? c : "none",
              "stroke-width": a2.graph.strokeWidth() + (g2 ? 0 : 2 * q2)
            }), [a2.tracker, a2.markerGroup].forEach(function(a3) {
              a3.addClass("highcharts-tracker").on("mouseover", e).on("mouseout", function(a4) {
                n3.onTrackerMouseOut(a4);
              });
              f2.cursor && !m2.styledMode && a3.css({ cursor: f2.cursor });
              if (l) a3.on("touchstart", e);
            }));
            K(this, "afterDrawTracker");
          }
        };
        G.column && (G.column.prototype.drawTracker = g.drawTrackerPoint);
        G.pie && (G.pie.prototype.drawTracker = g.drawTrackerPoint);
        G.scatter && (G.scatter.prototype.drawTracker = g.drawTrackerPoint);
        q(n2.prototype, { setItemEvents: function(a2, f2, g2) {
          var d = this, h2 = d.chart.renderer.boxWrapper, k2 = a2 instanceof D, l2 = "highcharts-legend-" + (k2 ? "point" : "series") + "-active", m2 = d.chart.styledMode;
          (g2 ? [f2, a2.legendSymbol] : [a2.legendGroup]).forEach(function(g3) {
            if (g3) g3.on("mouseover", function() {
              a2.visible && d.allItems.forEach(function(d2) {
                a2 !== d2 && d2.setState("inactive", !k2);
              });
              a2.setState("hover");
              a2.visible && h2.addClass(l2);
              m2 || f2.css(d.options.itemHoverStyle);
            }).on("mouseout", function() {
              d.chart.styledMode || f2.css(u(a2.visible ? d.itemStyle : d.itemHiddenStyle));
              d.allItems.forEach(function(d2) {
                a2 !== d2 && d2.setState("", !k2);
              });
              h2.removeClass(l2);
              a2.setState();
            }).on("click", function(e) {
              var c = function() {
                a2.setVisible && a2.setVisible();
                d.allItems.forEach(function(b) {
                  a2 !== b && b.setState(a2.visible ? "inactive" : "", !k2);
                });
              };
              h2.removeClass(l2);
              e = { browserEvent: e };
              a2.firePointEvent ? a2.firePointEvent("legendItemClick", e, c) : K(a2, "legendItemClick", e, c);
            });
          });
        }, createCheckboxForItem: function(a2) {
          a2.checkbox = H("input", {
            type: "checkbox",
            className: "highcharts-legend-checkbox",
            checked: a2.selected,
            defaultChecked: a2.selected
          }, this.options.itemCheckboxStyle, this.chart.container);
          J(a2.checkbox, "click", function(d) {
            K(a2.series || a2, "checkboxClick", { checked: d.target.checked, item: a2 }, function() {
              a2.select();
            });
          });
        } });
        q(f.prototype, { showResetZoom: function() {
          function a2() {
            f2.zoomOut();
          }
          var f2 = this, g2 = C.lang, h2 = f2.options.chart.resetZoomButton, k2 = h2.theme, l2 = k2.states, m2 = "chart" === h2.relativeTo || "spaceBox" === h2.relativeTo ? null : "plotBox";
          K(this, "beforeShowResetZoom", null, function() {
            f2.resetZoomButton = f2.renderer.button(
              g2.resetZoom,
              null,
              null,
              a2,
              k2,
              l2 && l2.hover
            ).attr({ align: h2.position.align, title: g2.resetZoomTitle }).addClass("highcharts-reset-zoom").add().align(h2.position, false, m2);
          });
          K(this, "afterShowResetZoom");
        }, zoomOut: function() {
          K(this, "selection", { resetSelection: true }, this.zoom);
        }, zoom: function(a2) {
          var d = this, f2, g2 = d.pointer, k2 = false, l2 = d.inverted ? g2.mouseDownX : g2.mouseDownY;
          !a2 || a2.resetSelection ? (d.axes.forEach(function(a3) {
            f2 = a3.zoom();
          }), g2.initiated = false) : a2.xAxis.concat(a2.yAxis).forEach(function(a3) {
            var h2 = a3.axis, e = d.inverted ? h2.left : h2.top, c = d.inverted ? e + h2.width : e + h2.height, b = h2.isXAxis, m3 = false;
            if (!b && l2 >= e && l2 <= c || b || !L(l2)) m3 = true;
            g2[b ? "zoomX" : "zoomY"] && m3 && (f2 = h2.zoom(a3.min, a3.max), h2.displayBtn && (k2 = true));
          });
          var m2 = d.resetZoomButton;
          k2 && !m2 ? d.showResetZoom() : !k2 && I(m2) && (d.resetZoomButton = m2.destroy());
          f2 && d.redraw(h(d.options.chart.animation, a2 && a2.animation, 100 > d.pointCount));
        }, pan: function(d, f2) {
          var g2 = this, h2 = g2.hoverPoints, k2 = g2.options.chart, l2 = g2.options.mapNavigation && g2.options.mapNavigation.enabled, m2;
          f2 = "object" === typeof f2 ? f2 : { enabled: f2, type: "x" };
          k2 && k2.panning && (k2.panning = f2);
          var n3 = f2.type;
          K(this, "pan", { originalEvent: d }, function() {
            h2 && h2.forEach(function(a2) {
              a2.setState();
            });
            var f3 = [1];
            "xy" === n3 ? f3 = [1, 0] : "y" === n3 && (f3 = [0]);
            f3.forEach(function(e) {
              var c = g2[e ? "xAxis" : "yAxis"][0], b = c.horiz, f4 = d[b ? "chartX" : "chartY"];
              b = b ? "mouseDownX" : "mouseDownY";
              var h3 = g2[b], k3 = (c.pointRange || 0) / 2, p2 = c.reversed && !g2.inverted || !c.reversed && g2.inverted ? -1 : 1, q2 = c.getExtremes(), r = c.toValue(h3 - f4, true) + k3 * p2;
              p2 = c.toValue(h3 + c.len - f4, true) - k3 * p2;
              var u2 = p2 < r;
              h3 = u2 ? p2 : r;
              r = u2 ? r : p2;
              var v2 = c.hasVerticalPanning(), x = c.panningState;
              c.series.forEach(function(a2) {
                if (v2 && !e && (!x || x.isDirty)) {
                  var b2 = a2.getProcessedData(true);
                  a2 = a2.getExtremes(b2.yData, true);
                  x || (x = { startMin: Number.MAX_VALUE, startMax: -Number.MAX_VALUE });
                  t(a2.dataMin) && t(a2.dataMax) && (x.startMin = Math.min(a2.dataMin, x.startMin), x.startMax = Math.max(a2.dataMax, x.startMax));
                }
              });
              p2 = Math.min(a.pick(null === x || void 0 === x ? void 0 : x.startMin, q2.dataMin), k3 ? q2.min : c.toValue(c.toPixels(q2.min) - c.minPixelPadding));
              k3 = Math.max(a.pick(null === x || void 0 === x ? void 0 : x.startMax, q2.dataMax), k3 ? q2.max : c.toValue(c.toPixels(q2.max) + c.minPixelPadding));
              c.panningState = x;
              c.isOrdinal || (u2 = p2 - h3, 0 < u2 && (r += u2, h3 = p2), u2 = r - k3, 0 < u2 && (r = k3, h3 -= u2), c.series.length && h3 !== q2.min && r !== q2.max && h3 >= p2 && r <= k3 && (c.setExtremes(h3, r, false, false, { trigger: "pan" }), g2.resetZoomButton || l2 || h3 === p2 || r === k3 || !n3.match("y") || (g2.showResetZoom(), c.displayBtn = false), m2 = true), g2[b] = f4);
            });
            m2 && g2.redraw(false);
            v(g2.container, { cursor: "move" });
          });
        } });
        q(D.prototype, { select: function(a2, f2) {
          var d = this, g2 = d.series, k2 = g2.chart;
          this.selectedStaging = a2 = h(a2, !d.selected);
          d.firePointEvent(a2 ? "select" : "unselect", { accumulate: f2 }, function() {
            d.selected = d.options.selected = a2;
            g2.options.data[g2.data.indexOf(d)] = d.options;
            d.setState(a2 && "select");
            f2 || k2.getSelectedPoints().forEach(function(a3) {
              var f3 = a3.series;
              a3.selected && a3 !== d && (a3.selected = a3.options.selected = false, f3.options.data[f3.data.indexOf(a3)] = a3.options, a3.setState(k2.hoverPoints && f3.options.inactiveOtherPoints ? "inactive" : ""), a3.firePointEvent("unselect"));
            });
          });
          delete this.selectedStaging;
        }, onMouseOver: function(a2) {
          var d = this.series.chart, f2 = d.pointer;
          a2 = a2 ? f2.normalize(a2) : f2.getChartCoordinatesFromPoint(this, d.inverted);
          f2.runPointActions(a2, this);
        }, onMouseOut: function() {
          var a2 = this.series.chart;
          this.firePointEvent("mouseOut");
          this.series.options.inactiveOtherPoints || (a2.hoverPoints || []).forEach(function(a3) {
            a3.setState();
          });
          a2.hoverPoints = a2.hoverPoint = null;
        }, importEvents: function() {
          if (!this.hasImportedEvents) {
            var a2 = this, f2 = u(a2.series.options.point, a2.options).events;
            a2.events = f2;
            m(f2, function(d, f3) {
              p(d) && J(a2, f3, d);
            });
            this.hasImportedEvents = true;
          }
        }, setState: function(a2, f2) {
          var d = this.series, g2 = this.state, k2 = d.options.states[a2 || "normal"] || {}, l2 = C.plotOptions[d.type].marker && d.options.marker, m2 = l2 && false === l2.enabled, n3 = l2 && l2.states && l2.states[a2 || "normal"] || {}, p2 = false === n3.enabled, e = d.stateMarkerGraphic, c = this.marker || {}, b = d.chart, t2 = d.halo, u2, v2 = l2 && d.markerAttribs;
          a2 = a2 || "";
          if (!(a2 === this.state && !f2 || this.selected && "select" !== a2 || false === k2.enabled || a2 && (p2 || m2 && false === n3.enabled) || a2 && c.states && c.states[a2] && false === c.states[a2].enabled)) {
            this.state = a2;
            v2 && (u2 = d.markerAttribs(this, a2));
            if (this.graphic) {
              g2 && this.graphic.removeClass("highcharts-point-" + g2);
              a2 && this.graphic.addClass("highcharts-point-" + a2);
              if (!b.styledMode) {
                var x = d.pointAttribs(this, a2);
                var y2 = h(b.options.chart.animation, k2.animation);
                d.options.inactiveOtherPoints && x.opacity && ((this.dataLabels || []).forEach(function(a3) {
                  a3 && a3.animate({ opacity: x.opacity }, y2);
                }), this.connector && this.connector.animate({ opacity: x.opacity }, y2));
                this.graphic.animate(x, y2);
              }
              u2 && this.graphic.animate(u2, h(b.options.chart.animation, n3.animation, l2.animation));
              e && e.hide();
            } else {
              if (a2 && n3) {
                g2 = c.symbol || d.symbol;
                e && e.currentSymbol !== g2 && (e = e.destroy());
                if (u2) if (e) e[f2 ? "animate" : "attr"]({
                  x: u2.x,
                  y: u2.y
                });
                else g2 && (d.stateMarkerGraphic = e = b.renderer.symbol(g2, u2.x, u2.y, u2.width, u2.height).add(d.markerGroup), e.currentSymbol = g2);
                !b.styledMode && e && e.attr(d.pointAttribs(this, a2));
              }
              e && (e[a2 && this.isInside ? "show" : "hide"](), e.element.point = this);
            }
            a2 = k2.halo;
            k2 = (e = this.graphic || e) && e.visibility || "inherit";
            a2 && a2.size && e && "hidden" !== k2 && !this.isCluster ? (t2 || (d.halo = t2 = b.renderer.path().add(e.parentGroup)), t2.show()[f2 ? "animate" : "attr"]({ d: this.haloPath(a2.size) }), t2.attr({ "class": "highcharts-halo highcharts-color-" + h(
              this.colorIndex,
              d.colorIndex
            ) + (this.className ? " " + this.className : ""), visibility: k2, zIndex: -1 }), t2.point = this, b.styledMode || t2.attr(q({ fill: this.color || d.color, "fill-opacity": a2.opacity }, a2.attributes))) : t2 && t2.point && t2.point.haloPath && t2.animate({ d: t2.point.haloPath(0) }, null, t2.hide);
            K(this, "afterSetState");
          }
        }, haloPath: function(a2) {
          return this.series.chart.renderer.symbols.circle(Math.floor(this.plotX) - a2, this.plotY - a2, 2 * a2, 2 * a2);
        } });
        q(y.prototype, { onMouseOver: function() {
          var a2 = this.chart, f2 = a2.hoverSeries;
          a2.pointer.setHoverChartIndex();
          if (f2 && f2 !== this) f2.onMouseOut();
          this.options.events.mouseOver && K(this, "mouseOver");
          this.setState("hover");
          a2.hoverSeries = this;
        }, onMouseOut: function() {
          var a2 = this.options, f2 = this.chart, g2 = f2.tooltip, h2 = f2.hoverPoint;
          f2.hoverSeries = null;
          if (h2) h2.onMouseOut();
          this && a2.events.mouseOut && K(this, "mouseOut");
          !g2 || this.stickyTracking || g2.shared && !this.noSharedTooltip || g2.hide();
          f2.series.forEach(function(a3) {
            a3.setState("", true);
          });
        }, setState: function(a2, f2) {
          var d = this, g2 = d.options, k2 = d.graph, l2 = g2.inactiveOtherPoints, m2 = g2.states, n3 = g2.lineWidth, p2 = g2.opacity, e = h(m2[a2 || "normal"] && m2[a2 || "normal"].animation, d.chart.options.chart.animation);
          g2 = 0;
          a2 = a2 || "";
          if (d.state !== a2 && ([d.group, d.markerGroup, d.dataLabelsGroup].forEach(function(c) {
            c && (d.state && c.removeClass("highcharts-series-" + d.state), a2 && c.addClass("highcharts-series-" + a2));
          }), d.state = a2, !d.chart.styledMode)) {
            if (m2[a2] && false === m2[a2].enabled) return;
            a2 && (n3 = m2[a2].lineWidth || n3 + (m2[a2].lineWidthPlus || 0), p2 = h(m2[a2].opacity, p2));
            if (k2 && !k2.dashstyle) for (m2 = { "stroke-width": n3 }, k2.animate(m2, e); d["zone-graph-" + g2]; ) d["zone-graph-" + g2].attr(m2), g2 += 1;
            l2 || [d.group, d.markerGroup, d.dataLabelsGroup, d.labelBySeries].forEach(function(a3) {
              a3 && a3.animate({ opacity: p2 }, e);
            });
          }
          f2 && l2 && d.points && d.setAllPointsToState(a2);
        }, setAllPointsToState: function(a2) {
          this.points.forEach(function(d) {
            d.setState && d.setState(a2);
          });
        }, setVisible: function(a2, f2) {
          var d = this, g2 = d.chart, h2 = d.legendItem, k2 = g2.options.chart.ignoreHiddenSeries, l2 = d.visible;
          var m2 = (d.visible = a2 = d.options.visible = d.userOptions.visible = "undefined" === typeof a2 ? !l2 : a2) ? "show" : "hide";
          [
            "group",
            "dataLabelsGroup",
            "markerGroup",
            "tracker",
            "tt"
          ].forEach(function(a3) {
            if (d[a3]) d[a3][m2]();
          });
          if (g2.hoverSeries === d || (g2.hoverPoint && g2.hoverPoint.series) === d) d.onMouseOut();
          h2 && g2.legend.colorizeItem(d, a2);
          d.isDirty = true;
          d.options.stacking && g2.series.forEach(function(a3) {
            a3.options.stacking && a3.visible && (a3.isDirty = true);
          });
          d.linkedSeries.forEach(function(d2) {
            d2.setVisible(a2, false);
          });
          k2 && (g2.isDirtyBox = true);
          K(d, m2);
          false !== f2 && g2.redraw();
        }, show: function() {
          this.setVisible(true);
        }, hide: function() {
          this.setVisible(false);
        }, select: function(a2) {
          this.selected = a2 = this.options.selected = "undefined" === typeof a2 ? !this.selected : a2;
          this.checkbox && (this.checkbox.checked = a2);
          K(this, a2 ? "select" : "unselect");
        }, drawTracker: g.drawTrackerGraph });
      });
      O(n, "Core/Responsive.js", [n["Core/Chart/Chart.js"], n["Core/Utilities.js"]], function(f, a) {
        var n2 = a.find, y = a.isArray, D = a.isObject, G = a.merge, C = a.objectEach, J = a.pick, H = a.splat, v = a.uniqueKey;
        f.prototype.setResponsive = function(a2, f2) {
          var q = this.options.responsive, y2 = [], p = this.currentResponsive;
          !f2 && q && q.rules && q.rules.forEach(function(a3) {
            "undefined" === typeof a3._id && (a3._id = v());
            this.matchResponsiveRule(a3, y2);
          }, this);
          f2 = G.apply(0, y2.map(function(a3) {
            return n2(q.rules, function(f3) {
              return f3._id === a3;
            }).chartOptions;
          }));
          f2.isResponsiveOptions = true;
          y2 = y2.toString() || void 0;
          y2 !== (p && p.ruleIds) && (p && this.update(p.undoOptions, a2, true), y2 ? (p = this.currentOptions(f2), p.isResponsiveOptions = true, this.currentResponsive = { ruleIds: y2, mergedOptions: f2, undoOptions: p }, this.update(f2, a2, true)) : this.currentResponsive = void 0);
        };
        f.prototype.matchResponsiveRule = function(a2, f2) {
          var n3 = a2.condition;
          (n3.callback || function() {
            return this.chartWidth <= J(n3.maxWidth, Number.MAX_VALUE) && this.chartHeight <= J(n3.maxHeight, Number.MAX_VALUE) && this.chartWidth >= J(n3.minWidth, 0) && this.chartHeight >= J(n3.minHeight, 0);
          }).call(this) && f2.push(a2._id);
        };
        f.prototype.currentOptions = function(a2) {
          function f2(a3, q, v3, u) {
            var m;
            C(a3, function(a4, l) {
              if (!u && -1 < n3.collectionsWithUpdate.indexOf(l)) for (a4 = H(a4), v3[l] = [], m = 0; m < Math.max(a4.length, q[l].length); m++) q[l][m] && (void 0 === a4[m] ? v3[l][m] = q[l][m] : (v3[l][m] = {}, f2(a4[m], q[l][m], v3[l][m], u + 1)));
              else D(a4) ? (v3[l] = y(a4) ? [] : {}, f2(a4, q[l] || {}, v3[l], u + 1)) : v3[l] = "undefined" === typeof q[l] ? null : q[l];
            });
          }
          var n3 = this, v2 = {};
          f2(a2, this.options, v2, 0);
          return v2;
        };
      });
      O(n, "masters/highcharts.src.js", [n["Core/Globals.js"]], function(f) {
        return f;
      });
      n["masters/highcharts.src.js"]._modules = n;
      return n["masters/highcharts.src.js"];
    });
  }
});
export default require_highcharts();
//# sourceMappingURL=highcharts.js.map

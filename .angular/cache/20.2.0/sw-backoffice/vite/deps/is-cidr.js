import {
  __commonJS
} from "./chunk-WOR4A3D2.js";

// node_modules/ip-regex/index.js
var require_ip_regex = __commonJS({
  "node_modules/ip-regex/index.js"(exports, module) {
    "use strict";
    var word = "[a-fA-F\\d:]";
    var b = (options) => options && options.includeBoundaries ? `(?:(?<=\\s|^)(?=${word})|(?<=${word})(?=\\s|$))` : "";
    var v4 = "(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}";
    var v6seg = "[a-fA-F\\d]{1,4}";
    var v6 = `
(?:
(?:${v6seg}:){7}(?:${v6seg}|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8
(?:${v6seg}:){6}(?:${v4}|:${v6seg}|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::*******
(?:${v6seg}:){5}(?::${v4}|(?::${v6seg}){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:*******
(?:${v6seg}:){4}(?:(?::${v6seg}){0,1}:${v4}|(?::${v6seg}){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:*******
(?:${v6seg}:){3}(?:(?::${v6seg}){0,2}:${v4}|(?::${v6seg}){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:*******
(?:${v6seg}:){2}(?:(?::${v6seg}){0,3}:${v4}|(?::${v6seg}){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:*******
(?:${v6seg}:){1}(?:(?::${v6seg}){0,4}:${v4}|(?::${v6seg}){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:*******
(?::(?:(?::${v6seg}){0,5}:${v4}|(?::${v6seg}){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::*******
)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1
`.replace(/\s*\/\/.*$/gm, "").replace(/\n/g, "").trim();
    var v46Exact = new RegExp(`(?:^${v4}$)|(?:^${v6}$)`);
    var v4exact = new RegExp(`^${v4}$`);
    var v6exact = new RegExp(`^${v6}$`);
    var ip = (options) => options && options.exact ? v46Exact : new RegExp(`(?:${b(options)}${v4}${b(options)})|(?:${b(options)}${v6}${b(options)})`, "g");
    ip.v4 = (options) => options && options.exact ? v4exact : new RegExp(`${b(options)}${v4}${b(options)}`, "g");
    ip.v6 = (options) => options && options.exact ? v6exact : new RegExp(`${b(options)}${v6}${b(options)}`, "g");
    module.exports = ip;
  }
});

// node_modules/cidr-regex/index.js
var require_cidr_regex = __commonJS({
  "node_modules/cidr-regex/index.js"(exports, module) {
    "use strict";
    var ipRegex = require_ip_regex();
    var defaultOpts = { exact: false };
    var v4str = `${ipRegex.v4().source}\\/(3[0-2]|[12]?[0-9])`;
    var v6str = `${ipRegex.v6().source}\\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])`;
    var v4exact = new RegExp(`^${v4str}$`);
    var v6exact = new RegExp(`^${v6str}$`);
    var v46exact = new RegExp(`(?:^${v4str}$)|(?:^${v6str}$)`);
    module.exports = ({ exact } = defaultOpts) => exact ? v46exact : new RegExp(`(?:${v4str})|(?:${v6str})`, "g");
    module.exports.v4 = ({ exact } = defaultOpts) => exact ? v4exact : new RegExp(v4str, "g");
    module.exports.v6 = ({ exact } = defaultOpts) => exact ? v6exact : new RegExp(v6str, "g");
  }
});

// node_modules/is-cidr/index.js
var require_is_cidr = __commonJS({
  "node_modules/is-cidr/index.js"(exports, module) {
    var { v4, v6 } = require_cidr_regex();
    var re4 = v4({ exact: true });
    var re6 = v6({ exact: true });
    module.exports = (str) => re4.test(str) ? 4 : re6.test(str) ? 6 : 0;
    module.exports.v4 = (str) => re4.test(str);
    module.exports.v6 = (str) => re6.test(str);
  }
});
export default require_is_cidr();
//# sourceMappingURL=is-cidr.js.map

{"version": 3, "sources": ["../../../../../../node_modules/ip-regex/index.js", "../../../../../../node_modules/cidr-regex/index.js", "../../../../../../node_modules/is-cidr/index.js"], "sourcesContent": ["'use strict';\n\nconst word = '[a-fA-F\\\\d:]';\nconst b = options => options && options.includeBoundaries ?\n\t`(?:(?<=\\\\s|^)(?=${word})|(?<=${word})(?=\\\\s|$))` :\n\t'';\n\nconst v4 = '(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}';\n\nconst v6seg = '[a-fA-F\\\\d]{1,4}';\nconst v6 = `\n(?:\n(?:${v6seg}:){7}(?:${v6seg}|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8\n(?:${v6seg}:){6}(?:${v4}|:${v6seg}|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::*******\n(?:${v6seg}:){5}(?::${v4}|(?::${v6seg}){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:*******\n(?:${v6seg}:){4}(?:(?::${v6seg}){0,1}:${v4}|(?::${v6seg}){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:*******\n(?:${v6seg}:){3}(?:(?::${v6seg}){0,2}:${v4}|(?::${v6seg}){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:*******\n(?:${v6seg}:){2}(?:(?::${v6seg}){0,3}:${v4}|(?::${v6seg}){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:*******\n(?:${v6seg}:){1}(?:(?::${v6seg}){0,4}:${v4}|(?::${v6seg}){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:*******\n(?::(?:(?::${v6seg}){0,5}:${v4}|(?::${v6seg}){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::*******\n)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1\n`.replace(/\\s*\\/\\/.*$/gm, '').replace(/\\n/g, '').trim();\n\n// Pre-compile only the exact regexes because adding a global flag make regexes stateful\nconst v46Exact = new RegExp(`(?:^${v4}$)|(?:^${v6}$)`);\nconst v4exact = new RegExp(`^${v4}$`);\nconst v6exact = new RegExp(`^${v6}$`);\n\nconst ip = options => options && options.exact ?\n\tv46Exact :\n\tnew RegExp(`(?:${b(options)}${v4}${b(options)})|(?:${b(options)}${v6}${b(options)})`, 'g');\n\nip.v4 = options => options && options.exact ? v4exact : new RegExp(`${b(options)}${v4}${b(options)}`, 'g');\nip.v6 = options => options && options.exact ? v6exact : new RegExp(`${b(options)}${v6}${b(options)}`, 'g');\n\nmodule.exports = ip;\n", "\"use strict\";\n\nconst ipRegex = require(\"ip-regex\");\n\nconst defaultOpts = {exact: false};\n\nconst v4str = `${ipRegex.v4().source}\\\\/(3[0-2]|[12]?[0-9])`;\nconst v6str = `${ipRegex.v6().source}\\\\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])`;\n\n// can not precompile the non-exact regexes because global flag makes the regex object stateful\n// which would require the user to reset .lastIndex on subsequent calls\nconst v4exact = new RegExp(`^${v4str}$`);\nconst v6exact = new RegExp(`^${v6str}$`);\nconst v46exact = new RegExp(`(?:^${v4str}$)|(?:^${v6str}$)`);\n\nmodule.exports = ({exact} = defaultOpts) => exact ? v46exact : new RegExp(`(?:${v4str})|(?:${v6str})`, \"g\");\nmodule.exports.v4 = ({exact} = defaultOpts) => exact ? v4exact : new RegExp(v4str, \"g\");\nmodule.exports.v6 = ({exact} = defaultOpts) => exact ? v6exact : new RegExp(v6str, \"g\");\n", "\"use strict\";\nconst {v4, v6} = require(\"cidr-regex\");\n\nconst re4 = v4({exact: true});\nconst re6 = v6({exact: true});\n\nmodule.exports = str => re4.test(str) ? 4 : (re6.test(str) ? 6 : 0);\nmodule.exports.v4 = str => re4.test(str);\nmodule.exports.v6 = str => re6.test(str);\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAEA,QAAM,OAAO;AACb,QAAM,IAAI,aAAW,WAAW,QAAQ,oBACvC,mBAAmB,IAAI,SAAS,IAAI,gBACpC;AAED,QAAM,KAAK;AAEX,QAAM,QAAQ;AACd,QAAM,KAAK;AAAA;AAAA,KAEN,KAAK,WAAW,KAAK;AAAA,KACrB,KAAK,WAAW,EAAE,KAAK,KAAK;AAAA,KAC5B,KAAK,YAAY,EAAE,QAAQ,KAAK;AAAA,KAChC,KAAK,eAAe,KAAK,UAAU,EAAE,QAAQ,KAAK;AAAA,KAClD,KAAK,eAAe,KAAK,UAAU,EAAE,QAAQ,KAAK;AAAA,KAClD,KAAK,eAAe,KAAK,UAAU,EAAE,QAAQ,KAAK;AAAA,KAClD,KAAK,eAAe,KAAK,UAAU,EAAE,QAAQ,KAAK;AAAA,aAC1C,KAAK,UAAU,EAAE,QAAQ,KAAK;AAAA;AAAA,EAEzC,QAAQ,gBAAgB,EAAE,EAAE,QAAQ,OAAO,EAAE,EAAE,KAAK;AAGtD,QAAM,WAAW,IAAI,OAAO,OAAO,EAAE,UAAU,EAAE,IAAI;AACrD,QAAM,UAAU,IAAI,OAAO,IAAI,EAAE,GAAG;AACpC,QAAM,UAAU,IAAI,OAAO,IAAI,EAAE,GAAG;AAEpC,QAAM,KAAK,aAAW,WAAW,QAAQ,QACxC,WACA,IAAI,OAAO,MAAM,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,KAAK,GAAG;AAE1F,OAAG,KAAK,aAAW,WAAW,QAAQ,QAAQ,UAAU,IAAI,OAAO,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,IAAI,GAAG;AACzG,OAAG,KAAK,aAAW,WAAW,QAAQ,QAAQ,UAAU,IAAI,OAAO,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,IAAI,GAAG;AAEzG,WAAO,UAAU;AAAA;AAAA;;;ACnCjB;AAAA;AAAA;AAEA,QAAM,UAAU;AAEhB,QAAM,cAAc,EAAC,OAAO,MAAK;AAEjC,QAAM,QAAQ,GAAG,QAAQ,GAAG,EAAE,MAAM;AACpC,QAAM,QAAQ,GAAG,QAAQ,GAAG,EAAE,MAAM;AAIpC,QAAM,UAAU,IAAI,OAAO,IAAI,KAAK,GAAG;AACvC,QAAM,UAAU,IAAI,OAAO,IAAI,KAAK,GAAG;AACvC,QAAM,WAAW,IAAI,OAAO,OAAO,KAAK,UAAU,KAAK,IAAI;AAE3D,WAAO,UAAU,CAAC,EAAC,MAAK,IAAI,gBAAgB,QAAQ,WAAW,IAAI,OAAO,MAAM,KAAK,QAAQ,KAAK,KAAK,GAAG;AAC1G,WAAO,QAAQ,KAAK,CAAC,EAAC,MAAK,IAAI,gBAAgB,QAAQ,UAAU,IAAI,OAAO,OAAO,GAAG;AACtF,WAAO,QAAQ,KAAK,CAAC,EAAC,MAAK,IAAI,gBAAgB,QAAQ,UAAU,IAAI,OAAO,OAAO,GAAG;AAAA;AAAA;;;ACjBtF;AAAA;AACA,QAAM,EAAC,IAAI,GAAE,IAAI;AAEjB,QAAM,MAAM,GAAG,EAAC,OAAO,KAAI,CAAC;AAC5B,QAAM,MAAM,GAAG,EAAC,OAAO,KAAI,CAAC;AAE5B,WAAO,UAAU,SAAO,IAAI,KAAK,GAAG,IAAI,IAAK,IAAI,KAAK,GAAG,IAAI,IAAI;AACjE,WAAO,QAAQ,KAAK,SAAO,IAAI,KAAK,GAAG;AACvC,WAAO,QAAQ,KAAK,SAAO,IAAI,KAAK,GAAG;AAAA;AAAA;", "names": []}
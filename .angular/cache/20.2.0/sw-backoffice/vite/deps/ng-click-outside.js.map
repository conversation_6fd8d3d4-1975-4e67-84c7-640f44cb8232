{"version": 3, "sources": ["../../../../../../node_modules/ng-click-outside/lib_esmodule/click-outside.directive.js", "../../../../../../node_modules/ng-click-outside/lib_esmodule/click-outside.module.js"], "sourcesContent": ["import { Directive, ElementRef, EventEmitter, Inject, Input, Output, PLATFORM_ID, NgZone } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nvar ClickOutsideDirective = function () {\n  function ClickOutsideDirective(_el, _ngZone, platformId) {\n    this._el = _el;\n    this._ngZone = _ngZone;\n    this.platformId = platformId;\n    this.clickOutsideEnabled = true;\n    this.attachOutsideOnClick = false;\n    this.delayClickOutsideInit = false;\n    this.emitOnBlur = false;\n    this.exclude = '';\n    this.excludeBeforeClick = false;\n    this.clickOutsideEvents = '';\n    this.clickOutside = new EventEmitter();\n    this._nodesExcluded = [];\n    this._events = ['click'];\n    this._initOnClickBody = this._initOnClickBody.bind(this);\n    this._onClickBody = this._onClickBody.bind(this);\n    this._onWindowBlur = this._onWindowBlur.bind(this);\n  }\n  ClickOutsideDirective.prototype.ngOnInit = function () {\n    if (!isPlatformBrowser(this.platformId)) {\n      return;\n    }\n    this._init();\n  };\n  ClickOutsideDirective.prototype.ngOnDestroy = function () {\n    if (!isPlatformBrowser(this.platformId)) {\n      return;\n    }\n    this._removeClickOutsideListener();\n    this._removeAttachOutsideOnClickListener();\n    this._removeWindowBlurListener();\n  };\n  ClickOutsideDirective.prototype.ngOnChanges = function (changes) {\n    if (!isPlatformBrowser(this.platformId)) {\n      return;\n    }\n    if (changes['attachOutsideOnClick'] || changes['exclude'] || changes['emitOnBlur']) {\n      this._init();\n    }\n  };\n  ClickOutsideDirective.prototype._init = function () {\n    if (this.clickOutsideEvents !== '') {\n      this._events = this.clickOutsideEvents.split(',').map(function (e) {\n        return e.trim();\n      });\n    }\n    this._excludeCheck();\n    if (this.attachOutsideOnClick) {\n      this._initAttachOutsideOnClickListener();\n    } else {\n      this._initOnClickBody();\n    }\n    if (this.emitOnBlur) {\n      this._initWindowBlurListener();\n    }\n  };\n  ClickOutsideDirective.prototype._initOnClickBody = function () {\n    if (this.delayClickOutsideInit) {\n      setTimeout(this._initClickOutsideListener.bind(this));\n    } else {\n      this._initClickOutsideListener();\n    }\n  };\n  ClickOutsideDirective.prototype._excludeCheck = function () {\n    if (this.exclude) {\n      try {\n        var nodes = Array.from(document.querySelectorAll(this.exclude));\n        if (nodes) {\n          this._nodesExcluded = nodes;\n        }\n      } catch (err) {\n        console.error('[ng-click-outside] Check your exclude selector syntax.', err);\n      }\n    }\n  };\n  ClickOutsideDirective.prototype._onClickBody = function (ev) {\n    if (!this.clickOutsideEnabled) {\n      return;\n    }\n    if (this.excludeBeforeClick) {\n      this._excludeCheck();\n    }\n    if (!this._el.nativeElement.contains(ev.target) && !this._shouldExclude(ev.target)) {\n      this._emit(ev);\n      if (this.attachOutsideOnClick) {\n        this._removeClickOutsideListener();\n      }\n    }\n  };\n  ClickOutsideDirective.prototype._onWindowBlur = function (ev) {\n    var _this = this;\n    setTimeout(function () {\n      if (!document.hidden) {\n        _this._emit(ev);\n      }\n    });\n  };\n  ClickOutsideDirective.prototype._emit = function (ev) {\n    var _this = this;\n    if (!this.clickOutsideEnabled) {\n      return;\n    }\n    this._ngZone.run(function () {\n      return _this.clickOutside.emit(ev);\n    });\n  };\n  ClickOutsideDirective.prototype._shouldExclude = function (target) {\n    for (var _i = 0, _a = this._nodesExcluded; _i < _a.length; _i++) {\n      var excludedNode = _a[_i];\n      if (excludedNode.contains(target)) {\n        return true;\n      }\n    }\n    return false;\n  };\n  ClickOutsideDirective.prototype._initClickOutsideListener = function () {\n    var _this = this;\n    this._ngZone.runOutsideAngular(function () {\n      _this._events.forEach(function (e) {\n        return document.addEventListener(e, _this._onClickBody);\n      });\n    });\n  };\n  ClickOutsideDirective.prototype._removeClickOutsideListener = function () {\n    var _this = this;\n    this._ngZone.runOutsideAngular(function () {\n      _this._events.forEach(function (e) {\n        return document.removeEventListener(e, _this._onClickBody);\n      });\n    });\n  };\n  ClickOutsideDirective.prototype._initAttachOutsideOnClickListener = function () {\n    var _this = this;\n    this._ngZone.runOutsideAngular(function () {\n      _this._events.forEach(function (e) {\n        return _this._el.nativeElement.addEventListener(e, _this._initOnClickBody);\n      });\n    });\n  };\n  ClickOutsideDirective.prototype._removeAttachOutsideOnClickListener = function () {\n    var _this = this;\n    this._ngZone.runOutsideAngular(function () {\n      _this._events.forEach(function (e) {\n        return _this._el.nativeElement.removeEventListener(e, _this._initOnClickBody);\n      });\n    });\n  };\n  ClickOutsideDirective.prototype._initWindowBlurListener = function () {\n    var _this = this;\n    this._ngZone.runOutsideAngular(function () {\n      window.addEventListener('blur', _this._onWindowBlur);\n    });\n  };\n  ClickOutsideDirective.prototype._removeWindowBlurListener = function () {\n    var _this = this;\n    this._ngZone.runOutsideAngular(function () {\n      window.removeEventListener('blur', _this._onWindowBlur);\n    });\n  };\n  ClickOutsideDirective.ɵfac = function ClickOutsideDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ClickOutsideDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(PLATFORM_ID));\n  };\n  ClickOutsideDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ClickOutsideDirective,\n    selectors: [[\"\", \"clickOutside\", \"\"]],\n    inputs: {\n      clickOutsideEnabled: \"clickOutsideEnabled\",\n      attachOutsideOnClick: \"attachOutsideOnClick\",\n      delayClickOutsideInit: \"delayClickOutsideInit\",\n      emitOnBlur: \"emitOnBlur\",\n      exclude: \"exclude\",\n      excludeBeforeClick: \"excludeBeforeClick\",\n      clickOutsideEvents: \"clickOutsideEvents\"\n    },\n    outputs: {\n      clickOutside: \"clickOutside\"\n    },\n    standalone: false,\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n  return ClickOutsideDirective;\n}();\nexport { ClickOutsideDirective };\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ClickOutsideDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[clickOutside]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: Object,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }];\n  }, {\n    clickOutsideEnabled: [{\n      type: Input\n    }],\n    attachOutsideOnClick: [{\n      type: Input\n    }],\n    delayClickOutsideInit: [{\n      type: Input\n    }],\n    emitOnBlur: [{\n      type: Input\n    }],\n    exclude: [{\n      type: Input\n    }],\n    excludeBeforeClick: [{\n      type: Input\n    }],\n    clickOutsideEvents: [{\n      type: Input\n    }],\n    clickOutside: [{\n      type: Output\n    }]\n  });\n})();", "import { NgModule } from '@angular/core';\nimport { ClickOutsideDirective } from './click-outside.directive';\nimport * as i0 from \"@angular/core\";\nvar ClickOutsideModule = function () {\n  function ClickOutsideModule() {}\n  ClickOutsideModule.ɵfac = function ClickOutsideModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ClickOutsideModule)();\n  };\n  ClickOutsideModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ClickOutsideModule,\n    declarations: [ClickOutsideDirective],\n    exports: [ClickOutsideDirective]\n  });\n  ClickOutsideModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  return ClickOutsideModule;\n}();\nexport { ClickOutsideModule };\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ClickOutsideModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [ClickOutsideDirective],\n      exports: [ClickOutsideDirective]\n    }]\n  }], null, null);\n})();"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA,IAAI,yBAAwB,WAAY;AACtC,WAASA,uBAAsB,KAAK,SAAS,YAAY;AACvD,SAAK,MAAM;AACX,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,sBAAsB;AAC3B,SAAK,uBAAuB;AAC5B,SAAK,wBAAwB;AAC7B,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB;AAC1B,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,iBAAiB,CAAC;AACvB,SAAK,UAAU,CAAC,OAAO;AACvB,SAAK,mBAAmB,KAAK,iBAAiB,KAAK,IAAI;AACvD,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AAAA,EACnD;AACA,EAAAA,uBAAsB,UAAU,WAAW,WAAY;AACrD,QAAI,CAAC,kBAAkB,KAAK,UAAU,GAAG;AACvC;AAAA,IACF;AACA,SAAK,MAAM;AAAA,EACb;AACA,EAAAA,uBAAsB,UAAU,cAAc,WAAY;AACxD,QAAI,CAAC,kBAAkB,KAAK,UAAU,GAAG;AACvC;AAAA,IACF;AACA,SAAK,4BAA4B;AACjC,SAAK,oCAAoC;AACzC,SAAK,0BAA0B;AAAA,EACjC;AACA,EAAAA,uBAAsB,UAAU,cAAc,SAAU,SAAS;AAC/D,QAAI,CAAC,kBAAkB,KAAK,UAAU,GAAG;AACvC;AAAA,IACF;AACA,QAAI,QAAQ,sBAAsB,KAAK,QAAQ,SAAS,KAAK,QAAQ,YAAY,GAAG;AAClF,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AACA,EAAAA,uBAAsB,UAAU,QAAQ,WAAY;AAClD,QAAI,KAAK,uBAAuB,IAAI;AAClC,WAAK,UAAU,KAAK,mBAAmB,MAAM,GAAG,EAAE,IAAI,SAAU,GAAG;AACjE,eAAO,EAAE,KAAK;AAAA,MAChB,CAAC;AAAA,IACH;AACA,SAAK,cAAc;AACnB,QAAI,KAAK,sBAAsB;AAC7B,WAAK,kCAAkC;AAAA,IACzC,OAAO;AACL,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,KAAK,YAAY;AACnB,WAAK,wBAAwB;AAAA,IAC/B;AAAA,EACF;AACA,EAAAA,uBAAsB,UAAU,mBAAmB,WAAY;AAC7D,QAAI,KAAK,uBAAuB;AAC9B,iBAAW,KAAK,0BAA0B,KAAK,IAAI,CAAC;AAAA,IACtD,OAAO;AACL,WAAK,0BAA0B;AAAA,IACjC;AAAA,EACF;AACA,EAAAA,uBAAsB,UAAU,gBAAgB,WAAY;AAC1D,QAAI,KAAK,SAAS;AAChB,UAAI;AACF,YAAI,QAAQ,MAAM,KAAK,SAAS,iBAAiB,KAAK,OAAO,CAAC;AAC9D,YAAI,OAAO;AACT,eAAK,iBAAiB;AAAA,QACxB;AAAA,MACF,SAAS,KAAK;AACZ,gBAAQ,MAAM,0DAA0D,GAAG;AAAA,MAC7E;AAAA,IACF;AAAA,EACF;AACA,EAAAA,uBAAsB,UAAU,eAAe,SAAU,IAAI;AAC3D,QAAI,CAAC,KAAK,qBAAqB;AAC7B;AAAA,IACF;AACA,QAAI,KAAK,oBAAoB;AAC3B,WAAK,cAAc;AAAA,IACrB;AACA,QAAI,CAAC,KAAK,IAAI,cAAc,SAAS,GAAG,MAAM,KAAK,CAAC,KAAK,eAAe,GAAG,MAAM,GAAG;AAClF,WAAK,MAAM,EAAE;AACb,UAAI,KAAK,sBAAsB;AAC7B,aAAK,4BAA4B;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AACA,EAAAA,uBAAsB,UAAU,gBAAgB,SAAU,IAAI;AAC5D,QAAI,QAAQ;AACZ,eAAW,WAAY;AACrB,UAAI,CAAC,SAAS,QAAQ;AACpB,cAAM,MAAM,EAAE;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH;AACA,EAAAA,uBAAsB,UAAU,QAAQ,SAAU,IAAI;AACpD,QAAI,QAAQ;AACZ,QAAI,CAAC,KAAK,qBAAqB;AAC7B;AAAA,IACF;AACA,SAAK,QAAQ,IAAI,WAAY;AAC3B,aAAO,MAAM,aAAa,KAAK,EAAE;AAAA,IACnC,CAAC;AAAA,EACH;AACA,EAAAA,uBAAsB,UAAU,iBAAiB,SAAU,QAAQ;AACjE,aAAS,KAAK,GAAG,KAAK,KAAK,gBAAgB,KAAK,GAAG,QAAQ,MAAM;AAC/D,UAAI,eAAe,GAAG,EAAE;AACxB,UAAI,aAAa,SAAS,MAAM,GAAG;AACjC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,EAAAA,uBAAsB,UAAU,4BAA4B,WAAY;AACtE,QAAI,QAAQ;AACZ,SAAK,QAAQ,kBAAkB,WAAY;AACzC,YAAM,QAAQ,QAAQ,SAAU,GAAG;AACjC,eAAO,SAAS,iBAAiB,GAAG,MAAM,YAAY;AAAA,MACxD,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,EAAAA,uBAAsB,UAAU,8BAA8B,WAAY;AACxE,QAAI,QAAQ;AACZ,SAAK,QAAQ,kBAAkB,WAAY;AACzC,YAAM,QAAQ,QAAQ,SAAU,GAAG;AACjC,eAAO,SAAS,oBAAoB,GAAG,MAAM,YAAY;AAAA,MAC3D,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,EAAAA,uBAAsB,UAAU,oCAAoC,WAAY;AAC9E,QAAI,QAAQ;AACZ,SAAK,QAAQ,kBAAkB,WAAY;AACzC,YAAM,QAAQ,QAAQ,SAAU,GAAG;AACjC,eAAO,MAAM,IAAI,cAAc,iBAAiB,GAAG,MAAM,gBAAgB;AAAA,MAC3E,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,EAAAA,uBAAsB,UAAU,sCAAsC,WAAY;AAChF,QAAI,QAAQ;AACZ,SAAK,QAAQ,kBAAkB,WAAY;AACzC,YAAM,QAAQ,QAAQ,SAAU,GAAG;AACjC,eAAO,MAAM,IAAI,cAAc,oBAAoB,GAAG,MAAM,gBAAgB;AAAA,MAC9E,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,EAAAA,uBAAsB,UAAU,0BAA0B,WAAY;AACpE,QAAI,QAAQ;AACZ,SAAK,QAAQ,kBAAkB,WAAY;AACzC,aAAO,iBAAiB,QAAQ,MAAM,aAAa;AAAA,IACrD,CAAC;AAAA,EACH;AACA,EAAAA,uBAAsB,UAAU,4BAA4B,WAAY;AACtE,QAAI,QAAQ;AACZ,SAAK,QAAQ,kBAAkB,WAAY;AACzC,aAAO,oBAAoB,QAAQ,MAAM,aAAa;AAAA,IACxD,CAAC;AAAA,EACH;AACA,EAAAA,uBAAsB,OAAO,SAAS,8BAA8B,mBAAmB;AACrF,WAAO,KAAK,qBAAqBA,wBAA0B,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAkB,WAAW,CAAC;AAAA,EACjK;AACA,EAAAA,uBAAsB,OAAyB,kBAAkB;AAAA,IAC/D,MAAMA;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,IACpC,QAAQ;AAAA,MACN,qBAAqB;AAAA,MACrB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,IACtB;AAAA,IACA,SAAS;AAAA,MACP,cAAc;AAAA,IAChB;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACD,SAAOA;AACT,GAAE;CAED,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,WAAW;AAAA,MACpB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;;;ACvOH;AAEA;AACA,IAAI,sBAAqB,WAAY;AACnC,WAASC,sBAAqB;AAAA,EAAC;AAC/B,EAAAA,oBAAmB,OAAO,SAAS,2BAA2B,mBAAmB;AAC/E,WAAO,KAAK,qBAAqBA,qBAAoB;AAAA,EACvD;AACA,EAAAA,oBAAmB,OAAyB,iBAAiB;AAAA,IAC3D,MAAMA;AAAA,IACN,cAAc,CAAC,qBAAqB;AAAA,IACpC,SAAS,CAAC,qBAAqB;AAAA,EACjC,CAAC;AACD,EAAAA,oBAAmB,OAAyB,iBAAiB,CAAC,CAAC;AAC/D,SAAOA;AACT,GAAE;CAED,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,qBAAqB;AAAA,MACpC,SAAS,CAAC,qBAAqB;AAAA,IACjC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["ClickOutsideDirective", "ClickOutsideModule"]}
{"version": 3, "sources": ["../../../../../../node_modules/ng-table-virtual-scroll/fesm2020/ng-table-virtual-scroll.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, forwardRef, Directive, Input, ContentChild, NgModule } from '@angular/core';\nimport { VIRTUAL_SCROLL_STRATEGY } from '@angular/cdk/scrolling';\nimport { CdkTable } from '@angular/cdk/table';\nimport { Subject, BehaviorSubject, Subscription, ReplaySubject, merge, of, combineLatest, from } from 'rxjs';\nimport { distinctUntilChanged, map, startWith, delayWhen, tap, takeUntil, switchMap, take } from 'rxjs/operators';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { DataSource } from '@angular/cdk/collections';\nclass FixedSizeTableVirtualScrollStrategy {\n  constructor() {\n    this.indexChange = new Subject();\n    this.stickyChange = new Subject();\n    this.renderedRangeStream = new BehaviorSubject({\n      start: 0,\n      end: 0\n    });\n    this.scrolledIndexChange = this.indexChange.pipe(distinctUntilChanged());\n    this._dataLength = 0;\n  }\n  get dataLength() {\n    return this._dataLength;\n  }\n  set dataLength(value) {\n    if (value !== this._dataLength) {\n      this._dataLength = value;\n      this.onDataLengthChanged();\n    }\n  }\n  attach(viewport) {\n    this.viewport = viewport;\n    this.viewport.renderedRangeStream.subscribe(this.renderedRangeStream);\n    this.stickyChange.next(0);\n    this.onDataLengthChanged();\n  }\n  detach() {\n    this.indexChange.complete();\n    this.stickyChange.complete();\n    this.renderedRangeStream.complete();\n  }\n  onContentScrolled() {\n    this.updateContent();\n  }\n  onDataLengthChanged() {\n    if (this.viewport) {\n      const contentSize = this.dataLength * this.rowHeight + this.headerHeight + this.footerHeight;\n      this.viewport.setTotalContentSize(contentSize);\n      const viewportSize = this.viewport.getViewportSize();\n      if (this.viewport.measureScrollOffset() + viewportSize >= contentSize) {\n        this.viewport.scrollToOffset(contentSize - viewportSize);\n      }\n    }\n    this.updateContent();\n  }\n  onContentRendered() {}\n  onRenderedOffsetChanged() {\n    // no-op\n  }\n  scrollToIndex(index, behavior) {\n    if (!this.viewport || !this.rowHeight) {\n      return;\n    }\n    this.viewport.scrollToOffset((index - 1) * this.rowHeight + this.headerHeight, behavior);\n  }\n  setConfig(configs) {\n    const {\n      rowHeight,\n      headerHeight,\n      footerHeight,\n      bufferMultiplier\n    } = configs;\n    if (this.rowHeight === rowHeight && this.headerHeight === headerHeight && this.footerHeight === footerHeight && this.bufferMultiplier === bufferMultiplier) {\n      return;\n    }\n    this.rowHeight = rowHeight;\n    this.headerHeight = headerHeight;\n    this.footerHeight = footerHeight;\n    this.bufferMultiplier = bufferMultiplier;\n    this.onDataLengthChanged();\n  }\n  updateContent() {\n    if (!this.viewport || !this.rowHeight) {\n      return;\n    }\n    const renderedOffset = this.viewport.getOffsetToRenderedContentStart();\n    const start = renderedOffset / this.rowHeight;\n    const itemsDisplayed = Math.ceil(this.viewport.getViewportSize() / this.rowHeight);\n    const bufferItems = Math.ceil(itemsDisplayed * this.bufferMultiplier);\n    const end = start + itemsDisplayed + 2 * bufferItems;\n    const bufferOffset = renderedOffset + bufferItems * this.rowHeight;\n    const scrollOffset = this.viewport.measureScrollOffset();\n    // How far the scroll offset is from the lower buffer, which is usually where items start being displayed\n    const relativeScrollOffset = scrollOffset - bufferOffset;\n    const rowsScrolled = relativeScrollOffset / this.rowHeight;\n    const displayed = scrollOffset / this.rowHeight;\n    this.indexChange.next(displayed);\n    // Only bother updating the displayed information if we've scrolled more than a row\n    const rowSensitivity = 1.0;\n    if (Math.abs(rowsScrolled) < rowSensitivity) {\n      this.viewport.setRenderedContentOffset(renderedOffset);\n      this.viewport.setRenderedRange({\n        start,\n        end\n      });\n      return;\n    }\n    // Special case for the start of the table.\n    // At the top of the table, the first few rows are first rendered because they're visible, and then still rendered\n    // Because they move into the buffer. So we only need to change what's rendered once the user scrolls far enough down.\n    if (renderedOffset === 0 && rowsScrolled < 0) {\n      this.viewport.setRenderedContentOffset(renderedOffset);\n      this.viewport.setRenderedRange({\n        start,\n        end\n      });\n      return;\n    }\n    const rowsToMove = Math.sign(rowsScrolled) * Math.floor(Math.abs(rowsScrolled));\n    const adjustedRenderedOffset = Math.max(0, renderedOffset + rowsToMove * this.rowHeight);\n    this.viewport.setRenderedContentOffset(adjustedRenderedOffset);\n    const adjustedStart = Math.max(0, start + rowsToMove);\n    const adjustedEnd = adjustedStart + itemsDisplayed + 2 * bufferItems;\n    this.viewport.setRenderedRange({\n      start: adjustedStart,\n      end: adjustedEnd\n    });\n    this.stickyChange.next(adjustedRenderedOffset);\n  }\n}\nFixedSizeTableVirtualScrollStrategy.ɵfac = function FixedSizeTableVirtualScrollStrategy_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || FixedSizeTableVirtualScrollStrategy)();\n};\nFixedSizeTableVirtualScrollStrategy.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: FixedSizeTableVirtualScrollStrategy,\n  factory: FixedSizeTableVirtualScrollStrategy.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FixedSizeTableVirtualScrollStrategy, [{\n    type: Injectable\n  }], null, null);\n})();\nfunction isTVSDataSource(dataSource) {\n  return dataSource instanceof CdkTableVirtualScrollDataSource || dataSource instanceof TableVirtualScrollDataSource;\n}\nclass CdkTableVirtualScrollDataSource extends DataSource {\n  constructor(initialData = []) {\n    super();\n    /** Stream emitting render data to the table (depends on ordered data changes). */\n    this._renderData = new BehaviorSubject([]);\n    /**\n     * Subscription to the changes that should trigger an update to the table's rendered rows, such\n     * as filtering, sorting, pagination, or base data changes.\n     */\n    this._renderChangesSubscription = null;\n    this._data = new BehaviorSubject(initialData);\n    this._updateChangeSubscription();\n  }\n  /** Array of data that should be rendered by the table, where each object represents one row. */\n  get data() {\n    return this._data.value;\n  }\n  set data(data) {\n    data = Array.isArray(data) ? data : [];\n    this._data.next(data);\n  }\n  _updateChangeSubscription() {\n    this.initStreams();\n    this._renderChangesSubscription?.unsubscribe();\n    this._renderChangesSubscription = new Subscription();\n    this._renderChangesSubscription.add(this._data.subscribe(data => this.dataToRender$.next(data)));\n    this._renderChangesSubscription.add(this.dataOfRange$.subscribe(data => this._renderData.next(data)));\n  }\n  connect() {\n    if (!this._renderChangesSubscription) {\n      this._updateChangeSubscription();\n    }\n    return this._renderData;\n  }\n  disconnect() {\n    this._renderChangesSubscription?.unsubscribe();\n    this._renderChangesSubscription = null;\n  }\n  initStreams() {\n    if (!this.streamsReady) {\n      this.dataToRender$ = new ReplaySubject(1);\n      this.dataOfRange$ = new ReplaySubject(1);\n      this.streamsReady = true;\n    }\n  }\n}\nclass TableVirtualScrollDataSource extends MatTableDataSource {\n  _updateChangeSubscription() {\n    this.initStreams();\n    const _sort = this['_sort'];\n    const _paginator = this['_paginator'];\n    const _internalPageChanges = this['_internalPageChanges'];\n    const _filter = this['_filter'];\n    const _renderData = this['_renderData'];\n    const sortChange = _sort ? merge(_sort.sortChange, _sort.initialized) : of(null);\n    const pageChange = _paginator ? merge(_paginator.page, _internalPageChanges, _paginator.initialized) : of(null);\n    const dataStream = this['_data'];\n    const filteredData = combineLatest([dataStream, _filter]).pipe(map(([data]) => this._filterData(data)));\n    const orderedData = combineLatest([filteredData, sortChange]).pipe(map(([data]) => this._orderData(data)));\n    const paginatedData = combineLatest([orderedData, pageChange]).pipe(map(([data]) => this._pageData(data)));\n    this._renderChangesSubscription?.unsubscribe();\n    this._renderChangesSubscription = new Subscription();\n    this._renderChangesSubscription.add(paginatedData.subscribe(data => this.dataToRender$.next(data)));\n    this._renderChangesSubscription.add(this.dataOfRange$.subscribe(data => _renderData.next(data)));\n  }\n  initStreams() {\n    if (!this.streamsReady) {\n      this.dataToRender$ = new ReplaySubject(1);\n      this.dataOfRange$ = new ReplaySubject(1);\n      this.streamsReady = true;\n    }\n  }\n}\nfunction _tableVirtualScrollDirectiveStrategyFactory(tableDir) {\n  return tableDir.scrollStrategy;\n}\nfunction combineSelectors(...pairs) {\n  return pairs.map(selectors => `${selectors.join(' ')}, ${selectors.join('')}`).join(', ');\n}\nconst stickyHeaderSelector = combineSelectors(['.mat-mdc-header-row', '.mat-mdc-table-sticky'], ['.mat-header-row', '.mat-table-sticky'], ['.cdk-header-row', '.cdk-table-sticky']);\nconst stickyFooterSelector = combineSelectors(['.mat-mdc-footer-row', '.mat-mdc-table-sticky'], ['.mat-footer-row', '.mat-table-sticky'], ['.cdk-footer-row', '.cdk-table-sticky']);\nfunction isMatTable(table) {\n  return table instanceof CdkTable && table['stickyCssClass'].includes('mat');\n}\nfunction isCdkTable(table) {\n  return table instanceof CdkTable && table['stickyCssClass'].includes('cdk');\n}\nconst defaults = {\n  rowHeight: 48,\n  headerHeight: 56,\n  headerEnabled: true,\n  footerHeight: 48,\n  footerEnabled: false,\n  bufferMultiplier: 0.7\n};\nclass TableItemSizeDirective {\n  constructor(zone) {\n    this.zone = zone;\n    this.destroyed$ = new Subject();\n    // eslint-disable-next-line @angular-eslint/no-input-rename\n    this.rowHeight = defaults.rowHeight;\n    this.headerEnabled = defaults.headerEnabled;\n    this.headerHeight = defaults.headerHeight;\n    this.footerEnabled = defaults.footerEnabled;\n    this.footerHeight = defaults.footerHeight;\n    this.bufferMultiplier = defaults.bufferMultiplier;\n    this.scrollStrategy = new FixedSizeTableVirtualScrollStrategy();\n    this.dataSourceChanges = new Subject();\n    this.resetStickyPositions = new Subject();\n    this.stickyEnabled = {\n      header: false,\n      footer: false\n    };\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n    this.dataSourceChanges.complete();\n  }\n  ngAfterContentInit() {\n    const switchDataSourceOrigin = this.table['_switchDataSource'];\n    this.table['_switchDataSource'] = dataSource => {\n      switchDataSourceOrigin.call(this.table, dataSource);\n      this.connectDataSource(dataSource);\n    };\n    const updateStickyColumnStylesOrigin = this.table.updateStickyColumnStyles;\n    this.table.updateStickyColumnStyles = () => {\n      const stickyColumnStylesNeedReset = this.table['_stickyColumnStylesNeedReset'];\n      updateStickyColumnStylesOrigin.call(this.table);\n      if (stickyColumnStylesNeedReset) {\n        this.resetStickyPositions.next();\n      }\n    };\n    this.connectDataSource(this.table.dataSource);\n    combineLatest([this.scrollStrategy.stickyChange, this.resetStickyPositions.pipe(startWith(void 0), delayWhen(() => this.getScheduleObservable()), tap(() => {\n      this.stickyPositions = null;\n    }))]).pipe(takeUntil(this.destroyed$)).subscribe(([stickyOffset]) => {\n      if (!this.stickyPositions) {\n        this.initStickyPositions();\n      }\n      if (this.stickyEnabled.header) {\n        this.setStickyHeader(stickyOffset);\n      }\n      if (this.stickyEnabled.footer) {\n        this.setStickyFooter(stickyOffset);\n      }\n    });\n  }\n  connectDataSource(dataSource) {\n    this.dataSourceChanges.next();\n    if (!isTVSDataSource(dataSource)) {\n      throw new Error('[tvsItemSize] requires TableVirtualScrollDataSource or CdkTableVirtualScrollDataSource be set as [dataSource] of the table');\n    }\n    if (isMatTable(this.table) && !(dataSource instanceof TableVirtualScrollDataSource)) {\n      throw new Error('[tvsItemSize] requires TableVirtualScrollDataSource be set as [dataSource] of [mat-table]');\n    }\n    if (isCdkTable(this.table) && !(dataSource instanceof CdkTableVirtualScrollDataSource)) {\n      throw new Error('[tvsItemSize] requires CdkTableVirtualScrollDataSource be set as [dataSource] of [cdk-table]');\n    }\n    dataSource.dataToRender$.pipe(distinctUntilChanged(), takeUntil(this.dataSourceChanges), takeUntil(this.destroyed$), tap(data => this.scrollStrategy.dataLength = data.length), switchMap(data => this.scrollStrategy.renderedRangeStream.pipe(map(({\n      start,\n      end\n    }) => typeof start !== 'number' || typeof end !== 'number' ? data : data.slice(start, end))))).subscribe(data => {\n      this.zone.run(() => {\n        dataSource.dataOfRange$.next(data);\n      });\n    });\n  }\n  ngOnChanges() {\n    const config = {\n      rowHeight: +this.rowHeight || defaults.rowHeight,\n      headerHeight: this.headerEnabled ? +this.headerHeight || defaults.headerHeight : 0,\n      footerHeight: this.footerEnabled ? +this.footerHeight || defaults.footerHeight : 0,\n      bufferMultiplier: +this.bufferMultiplier || defaults.bufferMultiplier\n    };\n    this.scrollStrategy.setConfig(config);\n  }\n  setStickyEnabled() {\n    if (!this.scrollStrategy.viewport) {\n      this.stickyEnabled = {\n        header: false,\n        footer: false\n      };\n      return;\n    }\n    const isEnabled = rowDefs => rowDefs.map(def => def.sticky).reduce((prevState, state) => prevState && state, true);\n    this.stickyEnabled = {\n      header: isEnabled(this.table['_headerRowDefs']),\n      footer: isEnabled(this.table['_footerRowDefs'])\n    };\n  }\n  setStickyHeader(offset) {\n    this.scrollStrategy.viewport.elementRef.nativeElement.querySelectorAll(stickyHeaderSelector).forEach(el => {\n      const parent = el.parentElement;\n      let baseOffset = 0;\n      if (this.stickyPositions.has(parent)) {\n        baseOffset = this.stickyPositions.get(parent);\n      }\n      el.style.top = `${baseOffset - offset}px`;\n    });\n  }\n  setStickyFooter(offset) {\n    this.scrollStrategy.viewport.elementRef.nativeElement.querySelectorAll(stickyFooterSelector).forEach(el => {\n      const parent = el.parentElement;\n      let baseOffset = 0;\n      if (this.stickyPositions.has(parent)) {\n        baseOffset = this.stickyPositions.get(parent);\n      }\n      el.style.bottom = `${-baseOffset + offset}px`;\n    });\n  }\n  initStickyPositions() {\n    this.stickyPositions = new Map();\n    this.setStickyEnabled();\n    if (this.stickyEnabled.header) {\n      this.scrollStrategy.viewport.elementRef.nativeElement.querySelectorAll(stickyHeaderSelector).forEach(el => {\n        const parent = el.parentElement;\n        if (!this.stickyPositions.has(parent)) {\n          this.stickyPositions.set(parent, parent.offsetTop);\n        }\n      });\n    }\n    if (this.stickyEnabled.footer) {\n      this.scrollStrategy.viewport.elementRef.nativeElement.querySelectorAll(stickyFooterSelector).forEach(el => {\n        const parent = el.parentElement;\n        if (!this.stickyPositions.has(parent)) {\n          this.stickyPositions.set(parent, -parent.offsetTop);\n        }\n      });\n    }\n  }\n  getScheduleObservable() {\n    // Use onStable when in the context of an ongoing change detection cycle so that we\n    // do not accidentally trigger additional cycles.\n    return this.zone.isStable ? from(Promise.resolve(undefined)) : this.zone.onStable.pipe(take(1));\n  }\n}\nTableItemSizeDirective.ɵfac = function TableItemSizeDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || TableItemSizeDirective)(i0.ɵɵdirectiveInject(i0.NgZone));\n};\nTableItemSizeDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TableItemSizeDirective,\n  selectors: [[\"cdk-virtual-scroll-viewport\", \"tvsItemSize\", \"\"]],\n  contentQueries: function TableItemSizeDirective_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, CdkTable, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.table = _t.first);\n    }\n  },\n  inputs: {\n    rowHeight: [0, \"tvsItemSize\", \"rowHeight\"],\n    headerEnabled: \"headerEnabled\",\n    headerHeight: \"headerHeight\",\n    footerEnabled: \"footerEnabled\",\n    footerHeight: \"footerHeight\",\n    bufferMultiplier: \"bufferMultiplier\"\n  },\n  standalone: false,\n  features: [i0.ɵɵProvidersFeature([{\n    provide: VIRTUAL_SCROLL_STRATEGY,\n    useFactory: _tableVirtualScrollDirectiveStrategyFactory,\n    deps: [forwardRef(() => TableItemSizeDirective)]\n  }]), i0.ɵɵNgOnChangesFeature]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TableItemSizeDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-virtual-scroll-viewport[tvsItemSize]',\n      providers: [{\n        provide: VIRTUAL_SCROLL_STRATEGY,\n        useFactory: _tableVirtualScrollDirectiveStrategyFactory,\n        deps: [forwardRef(() => TableItemSizeDirective)]\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }];\n  }, {\n    rowHeight: [{\n      type: Input,\n      args: ['tvsItemSize']\n    }],\n    headerEnabled: [{\n      type: Input\n    }],\n    headerHeight: [{\n      type: Input\n    }],\n    footerEnabled: [{\n      type: Input\n    }],\n    footerHeight: [{\n      type: Input\n    }],\n    bufferMultiplier: [{\n      type: Input\n    }],\n    table: [{\n      type: ContentChild,\n      args: [CdkTable, {\n        static: false\n      }]\n    }]\n  });\n})();\nclass TableVirtualScrollModule {}\nTableVirtualScrollModule.ɵfac = function TableVirtualScrollModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || TableVirtualScrollModule)();\n};\nTableVirtualScrollModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: TableVirtualScrollModule,\n  declarations: [TableItemSizeDirective],\n  exports: [TableItemSizeDirective]\n});\nTableVirtualScrollModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TableVirtualScrollModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [TableItemSizeDirective],\n      imports: [],\n      exports: [TableItemSizeDirective]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of ng-fixed-size-table-virtual-scroll\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkTableVirtualScrollDataSource, FixedSizeTableVirtualScrollStrategy, TableItemSizeDirective, TableVirtualScrollDataSource, TableVirtualScrollModule, _tableVirtualScrollDirectiveStrategyFactory, isTVSDataSource };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAGA;AACA;AAGA,IAAM,sCAAN,MAA0C;AAAA,EACxC,cAAc;AACZ,SAAK,cAAc,IAAI,QAAQ;AAC/B,SAAK,eAAe,IAAI,QAAQ;AAChC,SAAK,sBAAsB,IAAI,gBAAgB;AAAA,MAC7C,OAAO;AAAA,MACP,KAAK;AAAA,IACP,CAAC;AACD,SAAK,sBAAsB,KAAK,YAAY,KAAK,qBAAqB,CAAC;AACvE,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,QAAI,UAAU,KAAK,aAAa;AAC9B,WAAK,cAAc;AACnB,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,OAAO,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS,oBAAoB,UAAU,KAAK,mBAAmB;AACpE,SAAK,aAAa,KAAK,CAAC;AACxB,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,SAAS;AACP,SAAK,YAAY,SAAS;AAC1B,SAAK,aAAa,SAAS;AAC3B,SAAK,oBAAoB,SAAS;AAAA,EACpC;AAAA,EACA,oBAAoB;AAClB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,UAAU;AACjB,YAAM,cAAc,KAAK,aAAa,KAAK,YAAY,KAAK,eAAe,KAAK;AAChF,WAAK,SAAS,oBAAoB,WAAW;AAC7C,YAAM,eAAe,KAAK,SAAS,gBAAgB;AACnD,UAAI,KAAK,SAAS,oBAAoB,IAAI,gBAAgB,aAAa;AACrE,aAAK,SAAS,eAAe,cAAc,YAAY;AAAA,MACzD;AAAA,IACF;AACA,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,oBAAoB;AAAA,EAAC;AAAA,EACrB,0BAA0B;AAAA,EAE1B;AAAA,EACA,cAAc,OAAO,UAAU;AAC7B,QAAI,CAAC,KAAK,YAAY,CAAC,KAAK,WAAW;AACrC;AAAA,IACF;AACA,SAAK,SAAS,gBAAgB,QAAQ,KAAK,KAAK,YAAY,KAAK,cAAc,QAAQ;AAAA,EACzF;AAAA,EACA,UAAU,SAAS;AACjB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,KAAK,cAAc,aAAa,KAAK,iBAAiB,gBAAgB,KAAK,iBAAiB,gBAAgB,KAAK,qBAAqB,kBAAkB;AAC1J;AAAA,IACF;AACA,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,mBAAmB;AACxB,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,gBAAgB;AACd,QAAI,CAAC,KAAK,YAAY,CAAC,KAAK,WAAW;AACrC;AAAA,IACF;AACA,UAAM,iBAAiB,KAAK,SAAS,gCAAgC;AACrE,UAAM,QAAQ,iBAAiB,KAAK;AACpC,UAAM,iBAAiB,KAAK,KAAK,KAAK,SAAS,gBAAgB,IAAI,KAAK,SAAS;AACjF,UAAM,cAAc,KAAK,KAAK,iBAAiB,KAAK,gBAAgB;AACpE,UAAM,MAAM,QAAQ,iBAAiB,IAAI;AACzC,UAAM,eAAe,iBAAiB,cAAc,KAAK;AACzD,UAAM,eAAe,KAAK,SAAS,oBAAoB;AAEvD,UAAM,uBAAuB,eAAe;AAC5C,UAAM,eAAe,uBAAuB,KAAK;AACjD,UAAM,YAAY,eAAe,KAAK;AACtC,SAAK,YAAY,KAAK,SAAS;AAE/B,UAAM,iBAAiB;AACvB,QAAI,KAAK,IAAI,YAAY,IAAI,gBAAgB;AAC3C,WAAK,SAAS,yBAAyB,cAAc;AACrD,WAAK,SAAS,iBAAiB;AAAA,QAC7B;AAAA,QACA;AAAA,MACF,CAAC;AACD;AAAA,IACF;AAIA,QAAI,mBAAmB,KAAK,eAAe,GAAG;AAC5C,WAAK,SAAS,yBAAyB,cAAc;AACrD,WAAK,SAAS,iBAAiB;AAAA,QAC7B;AAAA,QACA;AAAA,MACF,CAAC;AACD;AAAA,IACF;AACA,UAAM,aAAa,KAAK,KAAK,YAAY,IAAI,KAAK,MAAM,KAAK,IAAI,YAAY,CAAC;AAC9E,UAAM,yBAAyB,KAAK,IAAI,GAAG,iBAAiB,aAAa,KAAK,SAAS;AACvF,SAAK,SAAS,yBAAyB,sBAAsB;AAC7D,UAAM,gBAAgB,KAAK,IAAI,GAAG,QAAQ,UAAU;AACpD,UAAM,cAAc,gBAAgB,iBAAiB,IAAI;AACzD,SAAK,SAAS,iBAAiB;AAAA,MAC7B,OAAO;AAAA,MACP,KAAK;AAAA,IACP,CAAC;AACD,SAAK,aAAa,KAAK,sBAAsB;AAAA,EAC/C;AACF;AACA,oCAAoC,OAAO,SAAS,4CAA4C,mBAAmB;AACjH,SAAO,KAAK,qBAAqB,qCAAqC;AACxE;AACA,oCAAoC,QAA0B,mBAAmB;AAAA,EAC/E,OAAO;AAAA,EACP,SAAS,oCAAoC;AAC/C,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qCAAqC,CAAC;AAAA,IAC5G,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,gBAAgB,YAAY;AACnC,SAAO,sBAAsB,mCAAmC,sBAAsB;AACxF;AACA,IAAM,kCAAN,cAA8C,WAAW;AAAA,EACvD,YAAY,cAAc,CAAC,GAAG;AAC5B,UAAM;AAEN,SAAK,cAAc,IAAI,gBAAgB,CAAC,CAAC;AAKzC,SAAK,6BAA6B;AAClC,SAAK,QAAQ,IAAI,gBAAgB,WAAW;AAC5C,SAAK,0BAA0B;AAAA,EACjC;AAAA;AAAA,EAEA,IAAI,OAAO;AACT,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,KAAK,MAAM;AACb,WAAO,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC;AACrC,SAAK,MAAM,KAAK,IAAI;AAAA,EACtB;AAAA,EACA,4BAA4B;AAC1B,SAAK,YAAY;AACjB,SAAK,4BAA4B,YAAY;AAC7C,SAAK,6BAA6B,IAAI,aAAa;AACnD,SAAK,2BAA2B,IAAI,KAAK,MAAM,UAAU,UAAQ,KAAK,cAAc,KAAK,IAAI,CAAC,CAAC;AAC/F,SAAK,2BAA2B,IAAI,KAAK,aAAa,UAAU,UAAQ,KAAK,YAAY,KAAK,IAAI,CAAC,CAAC;AAAA,EACtG;AAAA,EACA,UAAU;AACR,QAAI,CAAC,KAAK,4BAA4B;AACpC,WAAK,0BAA0B;AAAA,IACjC;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa;AACX,SAAK,4BAA4B,YAAY;AAC7C,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA,cAAc;AACZ,QAAI,CAAC,KAAK,cAAc;AACtB,WAAK,gBAAgB,IAAI,cAAc,CAAC;AACxC,WAAK,eAAe,IAAI,cAAc,CAAC;AACvC,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AACF;AACA,IAAM,+BAAN,cAA2C,mBAAmB;AAAA,EAC5D,4BAA4B;AAC1B,SAAK,YAAY;AACjB,UAAM,QAAQ,KAAK,OAAO;AAC1B,UAAM,aAAa,KAAK,YAAY;AACpC,UAAM,uBAAuB,KAAK,sBAAsB;AACxD,UAAM,UAAU,KAAK,SAAS;AAC9B,UAAM,cAAc,KAAK,aAAa;AACtC,UAAM,aAAa,QAAQ,MAAM,MAAM,YAAY,MAAM,WAAW,IAAI,GAAG,IAAI;AAC/E,UAAM,aAAa,aAAa,MAAM,WAAW,MAAM,sBAAsB,WAAW,WAAW,IAAI,GAAG,IAAI;AAC9G,UAAM,aAAa,KAAK,OAAO;AAC/B,UAAM,eAAe,cAAc,CAAC,YAAY,OAAO,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,IAAI,MAAM,KAAK,YAAY,IAAI,CAAC,CAAC;AACtG,UAAM,cAAc,cAAc,CAAC,cAAc,UAAU,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,IAAI,MAAM,KAAK,WAAW,IAAI,CAAC,CAAC;AACzG,UAAM,gBAAgB,cAAc,CAAC,aAAa,UAAU,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,IAAI,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC;AACzG,SAAK,4BAA4B,YAAY;AAC7C,SAAK,6BAA6B,IAAI,aAAa;AACnD,SAAK,2BAA2B,IAAI,cAAc,UAAU,UAAQ,KAAK,cAAc,KAAK,IAAI,CAAC,CAAC;AAClG,SAAK,2BAA2B,IAAI,KAAK,aAAa,UAAU,UAAQ,YAAY,KAAK,IAAI,CAAC,CAAC;AAAA,EACjG;AAAA,EACA,cAAc;AACZ,QAAI,CAAC,KAAK,cAAc;AACtB,WAAK,gBAAgB,IAAI,cAAc,CAAC;AACxC,WAAK,eAAe,IAAI,cAAc,CAAC;AACvC,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AACF;AACA,SAAS,4CAA4C,UAAU;AAC7D,SAAO,SAAS;AAClB;AACA,SAAS,oBAAoB,OAAO;AAClC,SAAO,MAAM,IAAI,eAAa,GAAG,UAAU,KAAK,GAAG,CAAC,KAAK,UAAU,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI;AAC1F;AACA,IAAM,uBAAuB,iBAAiB,CAAC,uBAAuB,uBAAuB,GAAG,CAAC,mBAAmB,mBAAmB,GAAG,CAAC,mBAAmB,mBAAmB,CAAC;AAClL,IAAM,uBAAuB,iBAAiB,CAAC,uBAAuB,uBAAuB,GAAG,CAAC,mBAAmB,mBAAmB,GAAG,CAAC,mBAAmB,mBAAmB,CAAC;AAClL,SAAS,WAAW,OAAO;AACzB,SAAO,iBAAiB,YAAY,MAAM,gBAAgB,EAAE,SAAS,KAAK;AAC5E;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,iBAAiB,YAAY,MAAM,gBAAgB,EAAE,SAAS,KAAK;AAC5E;AACA,IAAM,WAAW;AAAA,EACf,WAAW;AAAA,EACX,cAAc;AAAA,EACd,eAAe;AAAA,EACf,cAAc;AAAA,EACd,eAAe;AAAA,EACf,kBAAkB;AACpB;AACA,IAAM,yBAAN,MAA6B;AAAA,EAC3B,YAAY,MAAM;AAChB,SAAK,OAAO;AACZ,SAAK,aAAa,IAAI,QAAQ;AAE9B,SAAK,YAAY,SAAS;AAC1B,SAAK,gBAAgB,SAAS;AAC9B,SAAK,eAAe,SAAS;AAC7B,SAAK,gBAAgB,SAAS;AAC9B,SAAK,eAAe,SAAS;AAC7B,SAAK,mBAAmB,SAAS;AACjC,SAAK,iBAAiB,IAAI,oCAAoC;AAC9D,SAAK,oBAAoB,IAAI,QAAQ;AACrC,SAAK,uBAAuB,IAAI,QAAQ;AACxC,SAAK,gBAAgB;AAAA,MACnB,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,SAAK,kBAAkB,SAAS;AAAA,EAClC;AAAA,EACA,qBAAqB;AACnB,UAAM,yBAAyB,KAAK,MAAM,mBAAmB;AAC7D,SAAK,MAAM,mBAAmB,IAAI,gBAAc;AAC9C,6BAAuB,KAAK,KAAK,OAAO,UAAU;AAClD,WAAK,kBAAkB,UAAU;AAAA,IACnC;AACA,UAAM,iCAAiC,KAAK,MAAM;AAClD,SAAK,MAAM,2BAA2B,MAAM;AAC1C,YAAM,8BAA8B,KAAK,MAAM,8BAA8B;AAC7E,qCAA+B,KAAK,KAAK,KAAK;AAC9C,UAAI,6BAA6B;AAC/B,aAAK,qBAAqB,KAAK;AAAA,MACjC;AAAA,IACF;AACA,SAAK,kBAAkB,KAAK,MAAM,UAAU;AAC5C,kBAAc,CAAC,KAAK,eAAe,cAAc,KAAK,qBAAqB,KAAK,UAAU,MAAM,GAAG,UAAU,MAAM,KAAK,sBAAsB,CAAC,GAAG,IAAI,MAAM;AAC1J,WAAK,kBAAkB;AAAA,IACzB,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,CAAC,CAAC,YAAY,MAAM;AACnE,UAAI,CAAC,KAAK,iBAAiB;AACzB,aAAK,oBAAoB;AAAA,MAC3B;AACA,UAAI,KAAK,cAAc,QAAQ;AAC7B,aAAK,gBAAgB,YAAY;AAAA,MACnC;AACA,UAAI,KAAK,cAAc,QAAQ;AAC7B,aAAK,gBAAgB,YAAY;AAAA,MACnC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB,YAAY;AAC5B,SAAK,kBAAkB,KAAK;AAC5B,QAAI,CAAC,gBAAgB,UAAU,GAAG;AAChC,YAAM,IAAI,MAAM,4HAA4H;AAAA,IAC9I;AACA,QAAI,WAAW,KAAK,KAAK,KAAK,EAAE,sBAAsB,+BAA+B;AACnF,YAAM,IAAI,MAAM,2FAA2F;AAAA,IAC7G;AACA,QAAI,WAAW,KAAK,KAAK,KAAK,EAAE,sBAAsB,kCAAkC;AACtF,YAAM,IAAI,MAAM,8FAA8F;AAAA,IAChH;AACA,eAAW,cAAc,KAAK,qBAAqB,GAAG,UAAU,KAAK,iBAAiB,GAAG,UAAU,KAAK,UAAU,GAAG,IAAI,UAAQ,KAAK,eAAe,aAAa,KAAK,MAAM,GAAG,UAAU,UAAQ,KAAK,eAAe,oBAAoB,KAAK,IAAI,CAAC;AAAA,MAClP;AAAA,MACA;AAAA,IACF,MAAM,OAAO,UAAU,YAAY,OAAO,QAAQ,WAAW,OAAO,KAAK,MAAM,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,UAAQ;AAC/G,WAAK,KAAK,IAAI,MAAM;AAClB,mBAAW,aAAa,KAAK,IAAI;AAAA,MACnC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,UAAM,SAAS;AAAA,MACb,WAAW,CAAC,KAAK,aAAa,SAAS;AAAA,MACvC,cAAc,KAAK,gBAAgB,CAAC,KAAK,gBAAgB,SAAS,eAAe;AAAA,MACjF,cAAc,KAAK,gBAAgB,CAAC,KAAK,gBAAgB,SAAS,eAAe;AAAA,MACjF,kBAAkB,CAAC,KAAK,oBAAoB,SAAS;AAAA,IACvD;AACA,SAAK,eAAe,UAAU,MAAM;AAAA,EACtC;AAAA,EACA,mBAAmB;AACjB,QAAI,CAAC,KAAK,eAAe,UAAU;AACjC,WAAK,gBAAgB;AAAA,QACnB,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AACA;AAAA,IACF;AACA,UAAM,YAAY,aAAW,QAAQ,IAAI,SAAO,IAAI,MAAM,EAAE,OAAO,CAAC,WAAW,UAAU,aAAa,OAAO,IAAI;AACjH,SAAK,gBAAgB;AAAA,MACnB,QAAQ,UAAU,KAAK,MAAM,gBAAgB,CAAC;AAAA,MAC9C,QAAQ,UAAU,KAAK,MAAM,gBAAgB,CAAC;AAAA,IAChD;AAAA,EACF;AAAA,EACA,gBAAgB,QAAQ;AACtB,SAAK,eAAe,SAAS,WAAW,cAAc,iBAAiB,oBAAoB,EAAE,QAAQ,QAAM;AACzG,YAAM,SAAS,GAAG;AAClB,UAAI,aAAa;AACjB,UAAI,KAAK,gBAAgB,IAAI,MAAM,GAAG;AACpC,qBAAa,KAAK,gBAAgB,IAAI,MAAM;AAAA,MAC9C;AACA,SAAG,MAAM,MAAM,GAAG,aAAa,MAAM;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,QAAQ;AACtB,SAAK,eAAe,SAAS,WAAW,cAAc,iBAAiB,oBAAoB,EAAE,QAAQ,QAAM;AACzG,YAAM,SAAS,GAAG;AAClB,UAAI,aAAa;AACjB,UAAI,KAAK,gBAAgB,IAAI,MAAM,GAAG;AACpC,qBAAa,KAAK,gBAAgB,IAAI,MAAM;AAAA,MAC9C;AACA,SAAG,MAAM,SAAS,GAAG,CAAC,aAAa,MAAM;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB;AACpB,SAAK,kBAAkB,oBAAI,IAAI;AAC/B,SAAK,iBAAiB;AACtB,QAAI,KAAK,cAAc,QAAQ;AAC7B,WAAK,eAAe,SAAS,WAAW,cAAc,iBAAiB,oBAAoB,EAAE,QAAQ,QAAM;AACzG,cAAM,SAAS,GAAG;AAClB,YAAI,CAAC,KAAK,gBAAgB,IAAI,MAAM,GAAG;AACrC,eAAK,gBAAgB,IAAI,QAAQ,OAAO,SAAS;AAAA,QACnD;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,KAAK,cAAc,QAAQ;AAC7B,WAAK,eAAe,SAAS,WAAW,cAAc,iBAAiB,oBAAoB,EAAE,QAAQ,QAAM;AACzG,cAAM,SAAS,GAAG;AAClB,YAAI,CAAC,KAAK,gBAAgB,IAAI,MAAM,GAAG;AACrC,eAAK,gBAAgB,IAAI,QAAQ,CAAC,OAAO,SAAS;AAAA,QACpD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,wBAAwB;AAGtB,WAAO,KAAK,KAAK,WAAW,KAAK,QAAQ,QAAQ,MAAS,CAAC,IAAI,KAAK,KAAK,SAAS,KAAK,KAAK,CAAC,CAAC;AAAA,EAChG;AACF;AACA,uBAAuB,OAAO,SAAS,+BAA+B,mBAAmB;AACvF,SAAO,KAAK,qBAAqB,wBAA2B,kBAAqB,MAAM,CAAC;AAC1F;AACA,uBAAuB,OAAyB,kBAAkB;AAAA,EAChE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,+BAA+B,eAAe,EAAE,CAAC;AAAA,EAC9D,gBAAgB,SAAS,sCAAsC,IAAI,KAAK,UAAU;AAChF,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,UAAU,UAAU,CAAC;AAAA,IACzC;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,WAAW,CAAC,GAAG,eAAe,WAAW;AAAA,IACzC,eAAe;AAAA,IACf,cAAc;AAAA,IACd,eAAe;AAAA,IACf,cAAc;AAAA,IACd,kBAAkB;AAAA,EACpB;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,IAChC,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,MAAM,CAAC,WAAW,MAAM,sBAAsB,CAAC;AAAA,EACjD,CAAC,CAAC,GAAM,oBAAoB;AAC9B,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,MAAM,CAAC,WAAW,MAAM,sBAAsB,CAAC;AAAA,MACjD,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,2BAAN,MAA+B;AAAC;AAChC,yBAAyB,OAAO,SAAS,iCAAiC,mBAAmB;AAC3F,SAAO,KAAK,qBAAqB,0BAA0B;AAC7D;AACA,yBAAyB,OAAyB,iBAAiB;AAAA,EACjE,MAAM;AAAA,EACN,cAAc,CAAC,sBAAsB;AAAA,EACrC,SAAS,CAAC,sBAAsB;AAClC,CAAC;AACD,yBAAyB,OAAyB,iBAAiB,CAAC,CAAC;AAAA,CACpE,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,sBAAsB;AAAA,MACrC,SAAS,CAAC;AAAA,MACV,SAAS,CAAC,sBAAsB;AAAA,IAClC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}
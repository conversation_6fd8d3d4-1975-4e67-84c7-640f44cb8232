{"version": 3, "sources": ["../../../../../../node_modules/atoa/atoa.js", "../../../../../../node_modules/ticky/ticky-browser.js", "../../../../../../node_modules/contra/debounce.js", "../../../../../../node_modules/contra/emitter.js", "../../../../../../node_modules/custom-event/index.js", "../../../../../../node_modules/crossvent/src/eventmap.js", "../../../../../../node_modules/crossvent/src/crossvent.js", "../../../../../../node_modules/dragula/classes.js", "../../../../../../node_modules/dragula/dragula.js", "../../../../../../node_modules/ng2-dragula/fesm2022/ng2-dragula.mjs"], "sourcesContent": ["module.exports = function atoa (a, n) { return Array.prototype.slice.call(a, n); }\n", "var si = typeof setImmediate === 'function', tick;\nif (si) {\n  tick = function (fn) { setImmediate(fn); };\n} else {\n  tick = function (fn) { setTimeout(fn, 0); };\n}\n\nmodule.exports = tick;", "'use strict';\n\nvar ticky = require('ticky');\n\nmodule.exports = function debounce (fn, args, ctx) {\n  if (!fn) { return; }\n  ticky(function run () {\n    fn.apply(ctx || null, args || []);\n  });\n};\n", "'use strict';\n\nvar atoa = require('atoa');\nvar debounce = require('./debounce');\n\nmodule.exports = function emitter (thing, options) {\n  var opts = options || {};\n  var evt = {};\n  if (thing === undefined) { thing = {}; }\n  thing.on = function (type, fn) {\n    if (!evt[type]) {\n      evt[type] = [fn];\n    } else {\n      evt[type].push(fn);\n    }\n    return thing;\n  };\n  thing.once = function (type, fn) {\n    fn._once = true; // thing.off(fn) still works!\n    thing.on(type, fn);\n    return thing;\n  };\n  thing.off = function (type, fn) {\n    var c = arguments.length;\n    if (c === 1) {\n      delete evt[type];\n    } else if (c === 0) {\n      evt = {};\n    } else {\n      var et = evt[type];\n      if (!et) { return thing; }\n      et.splice(et.indexOf(fn), 1);\n    }\n    return thing;\n  };\n  thing.emit = function () {\n    var args = atoa(arguments);\n    return thing.emitterSnapshot(args.shift()).apply(this, args);\n  };\n  thing.emitterSnapshot = function (type) {\n    var et = (evt[type] || []).slice(0);\n    return function () {\n      var args = atoa(arguments);\n      var ctx = this || thing;\n      if (type === 'error' && opts.throws !== false && !et.length) { throw args.length === 1 ? args[0] : args; }\n      et.forEach(function emitter (listen) {\n        if (opts.async) { debounce(listen, args, ctx); } else { listen.apply(ctx, args); }\n        if (listen._once) { thing.off(type, listen); }\n      });\n      return thing;\n    };\n  };\n  return thing;\n};\n", "\nvar NativeCustomEvent = global.CustomEvent;\n\nfunction useNative () {\n  try {\n    var p = new NativeCustomEvent('cat', { detail: { foo: 'bar' } });\n    return  'cat' === p.type && 'bar' === p.detail.foo;\n  } catch (e) {\n  }\n  return false;\n}\n\n/**\n * Cross-browser `CustomEvent` constructor.\n *\n * https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent.CustomEvent\n *\n * @public\n */\n\nmodule.exports = useNative() ? NativeCustomEvent :\n\n// IE >= 9\n'function' === typeof document.createEvent ? function CustomEvent (type, params) {\n  var e = document.createEvent('CustomEvent');\n  if (params) {\n    e.initCustomEvent(type, params.bubbles, params.cancelable, params.detail);\n  } else {\n    e.initCustomEvent(type, false, false, void 0);\n  }\n  return e;\n} :\n\n// IE <= 8\nfunction CustomEvent (type, params) {\n  var e = document.createEventObject();\n  e.type = type;\n  if (params) {\n    e.bubbles = Boolean(params.bubbles);\n    e.cancelable = Boolean(params.cancelable);\n    e.detail = params.detail;\n  } else {\n    e.bubbles = false;\n    e.cancelable = false;\n    e.detail = void 0;\n  }\n  return e;\n}\n", "'use strict';\n\nvar eventmap = [];\nvar eventname = '';\nvar ron = /^on/;\n\nfor (eventname in global) {\n  if (ron.test(eventname)) {\n    eventmap.push(eventname.slice(2));\n  }\n}\n\nmodule.exports = eventmap;\n", "'use strict';\n\nvar customEvent = require('custom-event');\nvar eventmap = require('./eventmap');\nvar doc = global.document;\nvar addEvent = addEventEasy;\nvar removeEvent = removeEventEasy;\nvar hardCache = [];\n\nif (!global.addEventListener) {\n  addEvent = addEventHard;\n  removeEvent = removeEventHard;\n}\n\nmodule.exports = {\n  add: addEvent,\n  remove: removeEvent,\n  fabricate: fabricateEvent\n};\n\nfunction addEventEasy (el, type, fn, capturing) {\n  return el.addEventListener(type, fn, capturing);\n}\n\nfunction addEventHard (el, type, fn) {\n  return el.attachEvent('on' + type, wrap(el, type, fn));\n}\n\nfunction removeEventEasy (el, type, fn, capturing) {\n  return el.removeEventListener(type, fn, capturing);\n}\n\nfunction removeEventHard (el, type, fn) {\n  var listener = unwrap(el, type, fn);\n  if (listener) {\n    return el.detachEvent('on' + type, listener);\n  }\n}\n\nfunction fabricateEvent (el, type, model) {\n  var e = eventmap.indexOf(type) === -1 ? makeCustomEvent() : makeClassicEvent();\n  if (el.dispatchEvent) {\n    el.dispatchEvent(e);\n  } else {\n    el.fireEvent('on' + type, e);\n  }\n  function makeClassicEvent () {\n    var e;\n    if (doc.createEvent) {\n      e = doc.createEvent('Event');\n      e.initEvent(type, true, true);\n    } else if (doc.createEventObject) {\n      e = doc.createEventObject();\n    }\n    return e;\n  }\n  function makeCustomEvent () {\n    return new customEvent(type, { detail: model });\n  }\n}\n\nfunction wrapperFactory (el, type, fn) {\n  return function wrapper (originalEvent) {\n    var e = originalEvent || global.event;\n    e.target = e.target || e.srcElement;\n    e.preventDefault = e.preventDefault || function preventDefault () { e.returnValue = false; };\n    e.stopPropagation = e.stopPropagation || function stopPropagation () { e.cancelBubble = true; };\n    e.which = e.which || e.keyCode;\n    fn.call(el, e);\n  };\n}\n\nfunction wrap (el, type, fn) {\n  var wrapper = unwrap(el, type, fn) || wrapperFactory(el, type, fn);\n  hardCache.push({\n    wrapper: wrapper,\n    element: el,\n    type: type,\n    fn: fn\n  });\n  return wrapper;\n}\n\nfunction unwrap (el, type, fn) {\n  var i = find(el, type, fn);\n  if (i) {\n    var wrapper = hardCache[i].wrapper;\n    hardCache.splice(i, 1); // free up a tad of memory\n    return wrapper;\n  }\n}\n\nfunction find (el, type, fn) {\n  var i, item;\n  for (i = 0; i < hardCache.length; i++) {\n    item = hardCache[i];\n    if (item.element === el && item.type === type && item.fn === fn) {\n      return i;\n    }\n  }\n}\n", "'use strict';\n\nvar cache = {};\nvar start = '(?:^|\\\\s)';\nvar end = '(?:\\\\s|$)';\n\nfunction lookupClass (className) {\n  var cached = cache[className];\n  if (cached) {\n    cached.lastIndex = 0;\n  } else {\n    cache[className] = cached = new RegExp(start + className + end, 'g');\n  }\n  return cached;\n}\n\nfunction addClass (el, className) {\n  var current = el.className;\n  if (!current.length) {\n    el.className = className;\n  } else if (!lookupClass(className).test(current)) {\n    el.className += ' ' + className;\n  }\n}\n\nfunction rmClass (el, className) {\n  el.className = el.className.replace(lookupClass(className), ' ').trim();\n}\n\nmodule.exports = {\n  add: addClass,\n  rm: rmClass\n};\n", "'use strict';\n\nvar emitter = require('contra/emitter');\nvar crossvent = require('crossvent');\nvar classes = require('./classes');\nvar doc = document;\nvar documentElement = doc.documentElement;\n\nfunction dragula (initialContainers, options) {\n  var len = arguments.length;\n  if (len === 1 && Array.isArray(initialContainers) === false) {\n    options = initialContainers;\n    initialContainers = [];\n  }\n  var _mirror; // mirror image\n  var _source; // source container\n  var _item; // item being dragged\n  var _offsetX; // reference x\n  var _offsetY; // reference y\n  var _moveX; // reference move x\n  var _moveY; // reference move y\n  var _initialSibling; // reference sibling when grabbed\n  var _currentSibling; // reference sibling now\n  var _copy; // item used for copying\n  var _renderTimer; // timer for setTimeout renderMirrorImage\n  var _lastDropTarget = null; // last container item was over\n  var _grabbed; // holds mousedown context until first mousemove\n\n  var o = options || {};\n  if (o.moves === void 0) { o.moves = always; }\n  if (o.accepts === void 0) { o.accepts = always; }\n  if (o.invalid === void 0) { o.invalid = invalidTarget; }\n  if (o.containers === void 0) { o.containers = initialContainers || []; }\n  if (o.isContainer === void 0) { o.isContainer = never; }\n  if (o.copy === void 0) { o.copy = false; }\n  if (o.copySortSource === void 0) { o.copySortSource = false; }\n  if (o.revertOnSpill === void 0) { o.revertOnSpill = false; }\n  if (o.removeOnSpill === void 0) { o.removeOnSpill = false; }\n  if (o.direction === void 0) { o.direction = 'vertical'; }\n  if (o.ignoreInputTextSelection === void 0) { o.ignoreInputTextSelection = true; }\n  if (o.mirrorContainer === void 0) { o.mirrorContainer = doc.body; }\n\n  var drake = emitter({\n    containers: o.containers,\n    start: manualStart,\n    end: end,\n    cancel: cancel,\n    remove: remove,\n    destroy: destroy,\n    canMove: canMove,\n    dragging: false\n  });\n\n  if (o.removeOnSpill === true) {\n    drake.on('over', spillOver).on('out', spillOut);\n  }\n\n  events();\n\n  return drake;\n\n  function isContainer (el) {\n    return drake.containers.indexOf(el) !== -1 || o.isContainer(el);\n  }\n\n  function events (remove) {\n    var op = remove ? 'remove' : 'add';\n    touchy(documentElement, op, 'mousedown', grab);\n    touchy(documentElement, op, 'mouseup', release);\n  }\n\n  function eventualMovements (remove) {\n    var op = remove ? 'remove' : 'add';\n    touchy(documentElement, op, 'mousemove', startBecauseMouseMoved);\n  }\n\n  function movements (remove) {\n    var op = remove ? 'remove' : 'add';\n    crossvent[op](documentElement, 'selectstart', preventGrabbed); // IE8\n    crossvent[op](documentElement, 'click', preventGrabbed);\n  }\n\n  function destroy () {\n    events(true);\n    release({});\n  }\n\n  function preventGrabbed (e) {\n    if (_grabbed) {\n      e.preventDefault();\n    }\n  }\n\n  function grab (e) {\n    _moveX = e.clientX;\n    _moveY = e.clientY;\n\n    var ignore = whichMouseButton(e) !== 1 || e.metaKey || e.ctrlKey;\n    if (ignore) {\n      return; // we only care about honest-to-god left clicks and touch events\n    }\n    var item = e.target;\n    var context = canStart(item);\n    if (!context) {\n      return;\n    }\n    _grabbed = context;\n    eventualMovements();\n    if (e.type === 'mousedown') {\n      if (isInput(item)) { // see also: https://github.com/bevacqua/dragula/issues/208\n        item.focus(); // fixes https://github.com/bevacqua/dragula/issues/176\n      } else {\n        e.preventDefault(); // fixes https://github.com/bevacqua/dragula/issues/155\n      }\n    }\n  }\n\n  function startBecauseMouseMoved (e) {\n    if (!_grabbed) {\n      return;\n    }\n    if (whichMouseButton(e) === 0) {\n      release({});\n      return; // when text is selected on an input and then dragged, mouseup doesn't fire. this is our only hope\n    }\n    // truthy check fixes #239, equality fixes #207\n    if (e.clientX !== void 0 && e.clientX === _moveX && e.clientY !== void 0 && e.clientY === _moveY) {\n      return;\n    }\n    if (o.ignoreInputTextSelection) {\n      var clientX = getCoord('clientX', e);\n      var clientY = getCoord('clientY', e);\n      var elementBehindCursor = doc.elementFromPoint(clientX, clientY);\n      if (isInput(elementBehindCursor)) {\n        return;\n      }\n    }\n\n    var grabbed = _grabbed; // call to end() unsets _grabbed\n    eventualMovements(true);\n    movements();\n    end();\n    start(grabbed);\n\n    var offset = getOffset(_item);\n    _offsetX = getCoord('pageX', e) - offset.left;\n    _offsetY = getCoord('pageY', e) - offset.top;\n\n    classes.add(_copy || _item, 'gu-transit');\n    renderMirrorImage();\n    drag(e);\n  }\n\n  function canStart (item) {\n    if (drake.dragging && _mirror) {\n      return;\n    }\n    if (isContainer(item)) {\n      return; // don't drag container itself\n    }\n    var handle = item;\n    while (getParent(item) && isContainer(getParent(item)) === false) {\n      if (o.invalid(item, handle)) {\n        return;\n      }\n      item = getParent(item); // drag target should be a top element\n      if (!item) {\n        return;\n      }\n    }\n    var source = getParent(item);\n    if (!source) {\n      return;\n    }\n    if (o.invalid(item, handle)) {\n      return;\n    }\n\n    var movable = o.moves(item, source, handle, nextEl(item));\n    if (!movable) {\n      return;\n    }\n\n    return {\n      item: item,\n      source: source\n    };\n  }\n\n  function canMove (item) {\n    return !!canStart(item);\n  }\n\n  function manualStart (item) {\n    var context = canStart(item);\n    if (context) {\n      start(context);\n    }\n  }\n\n  function start (context) {\n    if (isCopy(context.item, context.source)) {\n      _copy = context.item.cloneNode(true);\n      drake.emit('cloned', _copy, context.item, 'copy');\n    }\n\n    _source = context.source;\n    _item = context.item;\n    _initialSibling = _currentSibling = nextEl(context.item);\n\n    drake.dragging = true;\n    drake.emit('drag', _item, _source);\n  }\n\n  function invalidTarget () {\n    return false;\n  }\n\n  function end () {\n    if (!drake.dragging) {\n      return;\n    }\n    var item = _copy || _item;\n    drop(item, getParent(item));\n  }\n\n  function ungrab () {\n    _grabbed = false;\n    eventualMovements(true);\n    movements(true);\n  }\n\n  function release (e) {\n    ungrab();\n\n    if (!drake.dragging) {\n      return;\n    }\n    var item = _copy || _item;\n    var clientX = getCoord('clientX', e);\n    var clientY = getCoord('clientY', e);\n    var elementBehindCursor = getElementBehindPoint(_mirror, clientX, clientY);\n    var dropTarget = findDropTarget(elementBehindCursor, clientX, clientY);\n    if (dropTarget && ((_copy && o.copySortSource) || (!_copy || dropTarget !== _source))) {\n      drop(item, dropTarget);\n    } else if (o.removeOnSpill) {\n      remove();\n    } else {\n      cancel();\n    }\n  }\n\n  function drop (item, target) {\n    var parent = getParent(item);\n    if (_copy && o.copySortSource && target === _source) {\n      parent.removeChild(_item);\n    }\n    if (isInitialPlacement(target)) {\n      drake.emit('cancel', item, _source, _source);\n    } else {\n      drake.emit('drop', item, target, _source, _currentSibling);\n    }\n    cleanup();\n  }\n\n  function remove () {\n    if (!drake.dragging) {\n      return;\n    }\n    var item = _copy || _item;\n    var parent = getParent(item);\n    if (parent) {\n      parent.removeChild(item);\n    }\n    drake.emit(_copy ? 'cancel' : 'remove', item, parent, _source);\n    cleanup();\n  }\n\n  function cancel (revert) {\n    if (!drake.dragging) {\n      return;\n    }\n    var reverts = arguments.length > 0 ? revert : o.revertOnSpill;\n    var item = _copy || _item;\n    var parent = getParent(item);\n    var initial = isInitialPlacement(parent);\n    if (initial === false && reverts) {\n      if (_copy) {\n        if (parent) {\n          parent.removeChild(_copy);\n        }\n      } else {\n        _source.insertBefore(item, _initialSibling);\n      }\n    }\n    if (initial || reverts) {\n      drake.emit('cancel', item, _source, _source);\n    } else {\n      drake.emit('drop', item, parent, _source, _currentSibling);\n    }\n    cleanup();\n  }\n\n  function cleanup () {\n    var item = _copy || _item;\n    ungrab();\n    removeMirrorImage();\n    if (item) {\n      classes.rm(item, 'gu-transit');\n    }\n    if (_renderTimer) {\n      clearTimeout(_renderTimer);\n    }\n    drake.dragging = false;\n    if (_lastDropTarget) {\n      drake.emit('out', item, _lastDropTarget, _source);\n    }\n    drake.emit('dragend', item);\n    _source = _item = _copy = _initialSibling = _currentSibling = _renderTimer = _lastDropTarget = null;\n  }\n\n  function isInitialPlacement (target, s) {\n    var sibling;\n    if (s !== void 0) {\n      sibling = s;\n    } else if (_mirror) {\n      sibling = _currentSibling;\n    } else {\n      sibling = nextEl(_copy || _item);\n    }\n    return target === _source && sibling === _initialSibling;\n  }\n\n  function findDropTarget (elementBehindCursor, clientX, clientY) {\n    var target = elementBehindCursor;\n    while (target && !accepted()) {\n      target = getParent(target);\n    }\n    return target;\n\n    function accepted () {\n      var droppable = isContainer(target);\n      if (droppable === false) {\n        return false;\n      }\n\n      var immediate = getImmediateChild(target, elementBehindCursor);\n      var reference = getReference(target, immediate, clientX, clientY);\n      var initial = isInitialPlacement(target, reference);\n      if (initial) {\n        return true; // should always be able to drop it right back where it was\n      }\n      return o.accepts(_item, target, _source, reference);\n    }\n  }\n\n  function drag (e) {\n    if (!_mirror) {\n      return;\n    }\n    e.preventDefault();\n\n    var clientX = getCoord('clientX', e);\n    var clientY = getCoord('clientY', e);\n    var x = clientX - _offsetX;\n    var y = clientY - _offsetY;\n\n    _mirror.style.left = x + 'px';\n    _mirror.style.top = y + 'px';\n\n    var item = _copy || _item;\n    var elementBehindCursor = getElementBehindPoint(_mirror, clientX, clientY);\n    var dropTarget = findDropTarget(elementBehindCursor, clientX, clientY);\n    var changed = dropTarget !== null && dropTarget !== _lastDropTarget;\n    if (changed || dropTarget === null) {\n      out();\n      _lastDropTarget = dropTarget;\n      over();\n    }\n    var parent = getParent(item);\n    if (dropTarget === _source && _copy && !o.copySortSource) {\n      if (parent) {\n        parent.removeChild(item);\n      }\n      return;\n    }\n    var reference;\n    var immediate = getImmediateChild(dropTarget, elementBehindCursor);\n    if (immediate !== null) {\n      reference = getReference(dropTarget, immediate, clientX, clientY);\n    } else if (o.revertOnSpill === true && !_copy) {\n      reference = _initialSibling;\n      dropTarget = _source;\n    } else {\n      if (_copy && parent) {\n        parent.removeChild(item);\n      }\n      return;\n    }\n    if (\n      (reference === null && changed) ||\n      reference !== item &&\n      reference !== nextEl(item)\n    ) {\n      _currentSibling = reference;\n      dropTarget.insertBefore(item, reference);\n      drake.emit('shadow', item, dropTarget, _source);\n    }\n    function moved (type) { drake.emit(type, item, _lastDropTarget, _source); }\n    function over () { if (changed) { moved('over'); } }\n    function out () { if (_lastDropTarget) { moved('out'); } }\n  }\n\n  function spillOver (el) {\n    classes.rm(el, 'gu-hide');\n  }\n\n  function spillOut (el) {\n    if (drake.dragging) { classes.add(el, 'gu-hide'); }\n  }\n\n  function renderMirrorImage () {\n    if (_mirror) {\n      return;\n    }\n    var rect = _item.getBoundingClientRect();\n    _mirror = _item.cloneNode(true);\n    _mirror.style.width = getRectWidth(rect) + 'px';\n    _mirror.style.height = getRectHeight(rect) + 'px';\n    classes.rm(_mirror, 'gu-transit');\n    classes.add(_mirror, 'gu-mirror');\n    o.mirrorContainer.appendChild(_mirror);\n    touchy(documentElement, 'add', 'mousemove', drag);\n    classes.add(o.mirrorContainer, 'gu-unselectable');\n    drake.emit('cloned', _mirror, _item, 'mirror');\n  }\n\n  function removeMirrorImage () {\n    if (_mirror) {\n      classes.rm(o.mirrorContainer, 'gu-unselectable');\n      touchy(documentElement, 'remove', 'mousemove', drag);\n      getParent(_mirror).removeChild(_mirror);\n      _mirror = null;\n    }\n  }\n\n  function getImmediateChild (dropTarget, target) {\n    var immediate = target;\n    while (immediate !== dropTarget && getParent(immediate) !== dropTarget) {\n      immediate = getParent(immediate);\n    }\n    if (immediate === documentElement) {\n      return null;\n    }\n    return immediate;\n  }\n\n  function getReference (dropTarget, target, x, y) {\n    var horizontal = o.direction === 'horizontal';\n    var reference = target !== dropTarget ? inside() : outside();\n    return reference;\n\n    function outside () { // slower, but able to figure out any position\n      var len = dropTarget.children.length;\n      var i;\n      var el;\n      var rect;\n      for (i = 0; i < len; i++) {\n        el = dropTarget.children[i];\n        rect = el.getBoundingClientRect();\n        if (horizontal && (rect.left + rect.width / 2) > x) { return el; }\n        if (!horizontal && (rect.top + rect.height / 2) > y) { return el; }\n      }\n      return null;\n    }\n\n    function inside () { // faster, but only available if dropped inside a child element\n      var rect = target.getBoundingClientRect();\n      if (horizontal) {\n        return resolve(x > rect.left + getRectWidth(rect) / 2);\n      }\n      return resolve(y > rect.top + getRectHeight(rect) / 2);\n    }\n\n    function resolve (after) {\n      return after ? nextEl(target) : target;\n    }\n  }\n\n  function isCopy (item, container) {\n    return typeof o.copy === 'boolean' ? o.copy : o.copy(item, container);\n  }\n}\n\nfunction touchy (el, op, type, fn) {\n  var touch = {\n    mouseup: 'touchend',\n    mousedown: 'touchstart',\n    mousemove: 'touchmove'\n  };\n  var pointers = {\n    mouseup: 'pointerup',\n    mousedown: 'pointerdown',\n    mousemove: 'pointermove'\n  };\n  var microsoft = {\n    mouseup: 'MSPointerUp',\n    mousedown: 'MSPointerDown',\n    mousemove: 'MSPointerMove'\n  };\n  if (global.navigator.pointerEnabled) {\n    crossvent[op](el, pointers[type], fn);\n  } else if (global.navigator.msPointerEnabled) {\n    crossvent[op](el, microsoft[type], fn);\n  } else {\n    crossvent[op](el, touch[type], fn);\n    crossvent[op](el, type, fn);\n  }\n}\n\nfunction whichMouseButton (e) {\n  if (e.touches !== void 0) { return e.touches.length; }\n  if (e.which !== void 0 && e.which !== 0) { return e.which; } // see https://github.com/bevacqua/dragula/issues/261\n  if (e.buttons !== void 0) { return e.buttons; }\n  var button = e.button;\n  if (button !== void 0) { // see https://github.com/jquery/jquery/blob/99e8ff1baa7ae341e94bb89c3e84570c7c3ad9ea/src/event.js#L573-L575\n    return button & 1 ? 1 : button & 2 ? 3 : (button & 4 ? 2 : 0);\n  }\n}\n\nfunction getOffset (el) {\n  var rect = el.getBoundingClientRect();\n  return {\n    left: rect.left + getScroll('scrollLeft', 'pageXOffset'),\n    top: rect.top + getScroll('scrollTop', 'pageYOffset')\n  };\n}\n\nfunction getScroll (scrollProp, offsetProp) {\n  if (typeof global[offsetProp] !== 'undefined') {\n    return global[offsetProp];\n  }\n  if (documentElement.clientHeight) {\n    return documentElement[scrollProp];\n  }\n  return doc.body[scrollProp];\n}\n\nfunction getElementBehindPoint (point, x, y) {\n  var p = point || {};\n  var state = p.className;\n  var el;\n  p.className += ' gu-hide';\n  el = doc.elementFromPoint(x, y);\n  p.className = state;\n  return el;\n}\n\nfunction never () { return false; }\nfunction always () { return true; }\nfunction getRectWidth (rect) { return rect.width || (rect.right - rect.left); }\nfunction getRectHeight (rect) { return rect.height || (rect.bottom - rect.top); }\nfunction getParent (el) { return el.parentNode === doc ? null : el.parentNode; }\nfunction isInput (el) { return el.tagName === 'INPUT' || el.tagName === 'TEXTAREA' || el.tagName === 'SELECT' || isEditable(el); }\nfunction isEditable (el) {\n  if (!el) { return false; } // no parents were editable\n  if (el.contentEditable === 'false') { return false; } // stop the lookup\n  if (el.contentEditable === 'true') { return true; } // found a contentEditable element in the chain\n  return isEditable(getParent(el)); // contentEditable is set to 'inherit'\n}\n\nfunction nextEl (el) {\n  return el.nextElementSibling || manually();\n  function manually () {\n    var sibling = el;\n    do {\n      sibling = sibling.nextSibling;\n    } while (sibling && sibling.nodeType !== 1);\n    return sibling;\n  }\n}\n\nfunction getEventHost (e) {\n  // on touchend event, we have to use `e.changedTouches`\n  // see http://stackoverflow.com/questions/7192563/touchend-event-properties\n  // see https://github.com/bevacqua/dragula/issues/34\n  if (e.targetTouches && e.targetTouches.length) {\n    return e.targetTouches[0];\n  }\n  if (e.changedTouches && e.changedTouches.length) {\n    return e.changedTouches[0];\n  }\n  return e;\n}\n\nfunction getCoord (coord, e) {\n  var host = getEventHost(e);\n  var missMap = {\n    pageX: 'clientX', // IE8\n    pageY: 'clientY' // IE8\n  };\n  if (coord in missMap && !(coord in host) && missMap[coord] in host) {\n    coord = missMap[coord];\n  }\n  return host[coord];\n}\n\nmodule.exports = dragula;\n", "import * as i0 from '@angular/core';\nimport { Injectable, Optional, EventEmitter, Directive, Input, Output, NgModule } from '@angular/core';\nimport { Subject, Subscription } from 'rxjs';\nimport { filter, map } from 'rxjs/operators';\nimport * as dragulaExpt from 'dragula';\nclass Group {\n  constructor(name, drake, options) {\n    this.name = name;\n    this.drake = drake;\n    this.options = options;\n    this.initEvents = false;\n  }\n}\nvar EventTypes;\n(function (EventTypes) {\n  EventTypes[\"Cancel\"] = \"cancel\";\n  EventTypes[\"Cloned\"] = \"cloned\";\n  EventTypes[\"Drag\"] = \"drag\";\n  EventTypes[\"DragEnd\"] = \"dragend\";\n  EventTypes[\"Drop\"] = \"drop\";\n  EventTypes[\"Out\"] = \"out\";\n  EventTypes[\"Over\"] = \"over\";\n  EventTypes[\"Remove\"] = \"remove\";\n  EventTypes[\"Shadow\"] = \"shadow\";\n  EventTypes[\"DropModel\"] = \"dropModel\";\n  EventTypes[\"RemoveModel\"] = \"removeModel\";\n})(EventTypes || (EventTypes = {}));\nconst AllEvents = Object.keys(EventTypes).map(k => EventTypes[k]);\nconst dragula = dragulaExpt.default || dragulaExpt;\nclass DrakeFactory {\n  constructor(build = dragula) {\n    this.build = build;\n  }\n}\nconst filterEvent = (eventType, filterDragType, projector) => input => {\n  return input.pipe(filter(({\n    event,\n    name\n  }) => {\n    return event === eventType && (filterDragType === undefined || name === filterDragType);\n  }), map(({\n    name,\n    args\n  }) => projector(name, args)));\n};\nconst elContainerSourceProjector = (name, [el, container, source]) => ({\n  name,\n  el,\n  container,\n  source\n});\nclass DragulaService {\n  constructor(drakeFactory) {\n    this.drakeFactory = drakeFactory;\n    this.groups = {};\n    this.dispatch$ = new Subject();\n    this.elContainerSource = eventType => groupName => this.dispatch$.pipe(filterEvent(eventType, groupName, elContainerSourceProjector));\n    /* https://github.com/bevacqua/dragula#drakeon-events */\n    // eslint-disable-next-line @typescript-eslint/member-ordering\n    this.cancel = this.elContainerSource(EventTypes.Cancel);\n    // eslint-disable-next-line @typescript-eslint/member-ordering\n    this.remove = this.elContainerSource(EventTypes.Remove);\n    // eslint-disable-next-line @typescript-eslint/member-ordering\n    this.shadow = this.elContainerSource(EventTypes.Shadow);\n    // eslint-disable-next-line @typescript-eslint/member-ordering\n    this.over = this.elContainerSource(EventTypes.Over);\n    // eslint-disable-next-line @typescript-eslint/member-ordering\n    this.out = this.elContainerSource(EventTypes.Out);\n    this.drag = groupName => this.dispatch$.pipe(filterEvent(EventTypes.Drag, groupName, (name, [el, source]) => ({\n      name,\n      el,\n      source\n    })));\n    this.dragend = groupName => this.dispatch$.pipe(filterEvent(EventTypes.DragEnd, groupName, (name, [el]) => ({\n      name,\n      el\n    })));\n    this.drop = groupName => this.dispatch$.pipe(filterEvent(EventTypes.Drop, groupName, (name, [el, target, source, sibling]) => {\n      return {\n        name,\n        el,\n        target,\n        source,\n        sibling\n      };\n    }));\n    this.cloned = groupName => this.dispatch$.pipe(filterEvent(EventTypes.Cloned, groupName, (name, [clone, original, cloneType]) => {\n      return {\n        name,\n        clone,\n        original,\n        cloneType\n      };\n    }));\n    this.dropModel = groupName => this.dispatch$.pipe(filterEvent(EventTypes.DropModel, groupName, (name, [el, target, source, sibling, item, sourceModel, targetModel, sourceIndex, targetIndex]) => {\n      return {\n        name,\n        el,\n        target,\n        source,\n        sibling,\n        item,\n        sourceModel,\n        targetModel,\n        sourceIndex,\n        targetIndex\n      };\n    }));\n    this.removeModel = groupName => this.dispatch$.pipe(filterEvent(EventTypes.RemoveModel, groupName, (name, [el, container, source, item, sourceModel, sourceIndex]) => {\n      return {\n        name,\n        el,\n        container,\n        source,\n        item,\n        sourceModel,\n        sourceIndex\n      };\n    }));\n    if (this.drakeFactory === null || this.drakeFactory === undefined) {\n      this.drakeFactory = new DrakeFactory();\n    }\n  }\n  /** Public mainly for testing purposes. Prefer `createGroup()`. */\n  add(group) {\n    const existingGroup = this.find(group.name);\n    if (existingGroup) {\n      throw new Error('Group named: \"' + group.name + '\" already exists.');\n    }\n    this.groups[group.name] = group;\n    this.handleModels(group);\n    this.setupEvents(group);\n    return group;\n  }\n  find(name) {\n    return this.groups[name];\n  }\n  destroy(name) {\n    const group = this.find(name);\n    if (!group) {\n      return;\n    }\n    group.drake && group.drake.destroy();\n    delete this.groups[name];\n  }\n  /**\n   * Creates a group with the specified name and options.\n   *\n   * Note: formerly known as `setOptions`\n   */\n  createGroup(name, options) {\n    return this.add(new Group(name, this.drakeFactory.build([], options), options));\n  }\n  handleModels({\n    name,\n    drake,\n    options\n  }) {\n    let dragElm;\n    let dragIndex;\n    let dropIndex;\n    drake.on('remove', (el, container, source) => {\n      if (!drake.models) {\n        return;\n      }\n      let sourceModel = drake.models[drake.containers.indexOf(source)];\n      sourceModel = sourceModel.slice(0); // clone it\n      const item = sourceModel.splice(dragIndex, 1)[0];\n      this.dispatch$.next({\n        event: EventTypes.RemoveModel,\n        name,\n        args: [el, container, source, item, sourceModel, dragIndex]\n      });\n    });\n    drake.on('drag', (el, source) => {\n      if (!drake.models) {\n        return;\n      }\n      dragElm = el;\n      dragIndex = this.domIndexOf(el, source);\n    });\n    drake.on('drop', (dropElm, target, source, sibling) => {\n      if (!drake.models || !target) {\n        return;\n      }\n      dropIndex = this.domIndexOf(dropElm, target);\n      let sourceModel = drake.models[drake.containers.indexOf(source)];\n      let targetModel = drake.models[drake.containers.indexOf(target)];\n      let item;\n      if (target === source) {\n        sourceModel = sourceModel.slice(0);\n        item = sourceModel.splice(dragIndex, 1)[0];\n        sourceModel.splice(dropIndex, 0, item);\n        // this was true before we cloned and updated sourceModel,\n        // but targetModel still has the old value\n        targetModel = sourceModel;\n      } else {\n        const isCopying = dragElm !== dropElm;\n        item = sourceModel[dragIndex];\n        if (isCopying) {\n          if (!options.copyItem) {\n            throw new Error('If you have enabled `copy` on a group, you must provide a `copyItem` function.');\n          }\n          item = options.copyItem(item);\n        }\n        if (!isCopying) {\n          sourceModel = sourceModel.slice(0);\n          sourceModel.splice(dragIndex, 1);\n        }\n        targetModel = targetModel.slice(0);\n        targetModel.splice(dropIndex, 0, item);\n        if (isCopying) {\n          try {\n            target.removeChild(dropElm);\n            // eslint-disable-next-line no-empty\n          } catch (e) {}\n        }\n      }\n      this.dispatch$.next({\n        event: EventTypes.DropModel,\n        name,\n        args: [dropElm, target, source, sibling, item, sourceModel, targetModel, dragIndex, dropIndex]\n      });\n    });\n  }\n  setupEvents(group) {\n    if (group.initEvents) {\n      return;\n    }\n    group.initEvents = true;\n    const name = group.name;\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n    const emitter = event => {\n      switch (event) {\n        case EventTypes.Drag:\n          group.drake.on(event, (...args) => {\n            this.dispatch$.next({\n              event,\n              name,\n              args\n            });\n          });\n          break;\n        case EventTypes.Drop:\n          group.drake.on(event, (...args) => {\n            this.dispatch$.next({\n              event,\n              name,\n              args\n            });\n          });\n          break;\n        case EventTypes.DragEnd:\n          group.drake.on(event, (...args) => {\n            this.dispatch$.next({\n              event,\n              name,\n              args\n            });\n          });\n          break;\n        case EventTypes.Cancel:\n        case EventTypes.Remove:\n        case EventTypes.Shadow:\n        case EventTypes.Over:\n        case EventTypes.Out:\n          group.drake.on(event, (...args) => {\n            this.dispatch$.next({\n              event,\n              name,\n              args\n            });\n          });\n          break;\n        case EventTypes.Cloned:\n          group.drake.on(event, (...args) => {\n            this.dispatch$.next({\n              event,\n              name,\n              args\n            });\n          });\n          break;\n        case EventTypes.DropModel:\n          group.drake.on(event, (...args) => {\n            this.dispatch$.next({\n              event,\n              name,\n              args\n            });\n          });\n          break;\n        case EventTypes.RemoveModel:\n          group.drake.on(event, (...args) => {\n            this.dispatch$.next({\n              event,\n              name,\n              args\n            });\n          });\n          break;\n        default:\n          break;\n      }\n    };\n    AllEvents.forEach(emitter);\n  }\n  domIndexOf(child, parent) {\n    if (parent) {\n      return Array.prototype.indexOf.call(parent.children, child);\n    }\n  }\n  static {\n    this.ɵfac = function DragulaService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DragulaService)(i0.ɵɵinject(DrakeFactory, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DragulaService,\n      factory: DragulaService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragulaService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: DrakeFactory,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\nclass DragulaDirective {\n  get container() {\n    return this.el && this.el.nativeElement;\n  }\n  constructor(el, dragulaService) {\n    this.el = el;\n    this.dragulaService = dragulaService;\n    this.dragulaModelChange = new EventEmitter();\n  }\n  ngOnChanges(changes) {\n    if (changes && changes.dragula) {\n      const {\n        previousValue: prev,\n        currentValue: current,\n        firstChange\n      } = changes.dragula;\n      const hadPreviousValue = !!prev;\n      const hasNewValue = !!current;\n      // something -> null       =>  teardown only\n      // something -> something  =>  teardown, then setup\n      //      null -> something  =>  setup only\n      //\n      //      null -> null (precluded by fact of change being present)\n      if (hadPreviousValue) {\n        this.teardown(prev);\n      }\n      if (hasNewValue) {\n        this.setup();\n      }\n    } else if (changes && changes.dragulaModel) {\n      // this code only runs when you're not changing the group name\n      // because if you're changing the group name, you'll be doing setup or teardown\n      // it also only runs if there is a group name to attach to.\n      const {\n        previousValue: prev,\n        currentValue: current,\n        firstChange\n      } = changes.dragulaModel;\n      const drake = this.group?.drake;\n      if (this.dragula && drake) {\n        drake.models = drake.models || [];\n        const prevIndex = drake.models.indexOf(prev);\n        if (prevIndex !== -1) {\n          // delete the previous\n          drake.models.splice(prevIndex, 1);\n          // maybe insert a new one at the same spot\n          if (current) {\n            drake.models.splice(prevIndex, 0, current);\n          }\n        } else if (current) {\n          // no previous one to remove; just push this one.\n          drake.models.push(current);\n        }\n      }\n    }\n  }\n  // call ngOnInit 'setup' because we want to call it in ngOnChanges\n  // and it would otherwise run twice\n  setup() {\n    const checkModel = group => {\n      if (this.dragulaModel) {\n        if (group.drake?.models) {\n          group.drake?.models?.push(this.dragulaModel);\n        } else {\n          if (group.drake) {\n            group.drake.models = [this.dragulaModel];\n          }\n        }\n      }\n    };\n    // find or create a group\n    if (!this.dragula) {\n      return;\n    }\n    let group = this.dragulaService.find(this.dragula);\n    if (!group) {\n      const options = {};\n      group = this.dragulaService.createGroup(this.dragula, options);\n    }\n    // ensure model and container element are pushed\n    checkModel(group);\n    group.drake?.containers.push(this.container);\n    this.subscribe(this.dragula);\n    this.group = group;\n  }\n  subscribe(name) {\n    this.subs = new Subscription();\n    this.subs.add(this.dragulaService.dropModel(name).subscribe(({\n      source,\n      target,\n      sourceModel,\n      targetModel\n    }) => {\n      if (source === this.el.nativeElement) {\n        this.dragulaModelChange.emit(sourceModel);\n      } else if (target === this.el.nativeElement) {\n        this.dragulaModelChange.emit(targetModel);\n      }\n    }));\n    this.subs.add(this.dragulaService.removeModel(name).subscribe(({\n      source,\n      sourceModel\n    }) => {\n      if (source === this.el.nativeElement) {\n        this.dragulaModelChange.emit(sourceModel);\n      }\n    }));\n  }\n  teardown(groupName) {\n    if (this.subs) {\n      this.subs.unsubscribe();\n    }\n    const group = this.dragulaService.find(groupName);\n    if (group) {\n      const itemToRemove = group.drake?.containers.indexOf(this.el.nativeElement);\n      if (itemToRemove !== -1) {\n        group.drake?.containers.splice(itemToRemove, 1);\n      }\n      if (this.dragulaModel && group.drake && group.drake.models) {\n        const modelIndex = group.drake.models.indexOf(this.dragulaModel);\n        if (modelIndex !== -1) {\n          group.drake.models.splice(modelIndex, 1);\n        }\n      }\n    }\n  }\n  ngOnDestroy() {\n    if (!this.dragula) {\n      return;\n    }\n    this.teardown(this.dragula);\n  }\n  static {\n    this.ɵfac = function DragulaDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DragulaDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DragulaService));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DragulaDirective,\n      selectors: [[\"\", \"dragula\", \"\"]],\n      inputs: {\n        dragula: \"dragula\",\n        dragulaModel: \"dragulaModel\"\n      },\n      outputs: {\n        dragulaModelChange: \"dragulaModelChange\"\n      },\n      standalone: false,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragulaDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[dragula]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: DragulaService\n    }];\n  }, {\n    dragula: [{\n      type: Input\n    }],\n    dragulaModel: [{\n      type: Input\n    }],\n    dragulaModelChange: [{\n      type: Output\n    }]\n  });\n})();\nclass DragulaModule {\n  static forRoot() {\n    return {\n      ngModule: DragulaModule,\n      providers: [DragulaService]\n    };\n  }\n  static {\n    this.ɵfac = function DragulaModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DragulaModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: DragulaModule,\n      declarations: [DragulaDirective],\n      exports: [DragulaDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [DragulaService]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragulaModule, [{\n    type: NgModule,\n    args: [{\n      exports: [DragulaDirective],\n      declarations: [DragulaDirective],\n      providers: [DragulaService]\n    }]\n  }], null, null);\n})();\nconst MockDrakeFactory = new DrakeFactory((containers, options) => {\n  return new MockDrake(containers, options);\n});\n/** You can use MockDrake to simulate Drake events.\n *\n * The three methods that actually do anything are `on(event, listener)`,\n * `destroy()`, and a new method, `emit()`. Use `emit()` to manually emit Drake\n * events, and if you injected MockDrake properly with MockDrakeFactory or\n * mocked the DragulaService.find() method, then you can make ng2-dragula think\n * drags and drops are happening.\n *\n * Caveats:\n *\n * 1. YOU MUST MAKE THE DOM CHANGES YOURSELF.\n * 2. REPEAT: YOU MUST MAKE THE DOM CHANGES YOURSELF.\n *    That means `source.removeChild(el)`, and `target.insertBefore(el)`.\n * 3. None of the other methods do anything.\n *    That's ok, because ng2-dragula doesn't use them.\n */\nclass MockDrake {\n  /**\n   * @param containers A list of container elements.\n   * @param options These will NOT be used. At all.\n   * @param models Nonstandard, but useful for testing using `new MockDrake()` directly.\n   *               Note, default value is undefined, like a real Drake. Don't change that.\n   */\n  constructor(containers = [], options = {}, models) {\n    this.containers = containers;\n    this.options = options;\n    this.models = models;\n    // Basic but fully functional event emitter shim\n    this.emitter$ = new Subject();\n    this.subs = new Subscription();\n    /* Doesn't represent anything meaningful. */\n    this.dragging = false;\n  }\n  on(event, callback) {\n    this.subs.add(this.emitter$.pipe(filter(({\n      eventType\n    }) => eventType === event)).subscribe(({\n      eventType,\n      args\n    }) => {\n      if (eventType === EventTypes.Drag) {\n        const argument = Array.from(args);\n        const el = argument[0];\n        const source = argument[1];\n        //@ts-ignore\n        callback(el, source);\n        return;\n      }\n      if (eventType === EventTypes.Drop) {\n        const argument = Array.from(args);\n        const el = argument[0];\n        const target = argument[1];\n        const source = argument[2];\n        const sibling = argument[3];\n        //@ts-ignore\n        callback(el, target, source, sibling);\n        return;\n      }\n      if (eventType === EventTypes.Remove) {\n        const argument = Array.from(args);\n        const el = argument[0];\n        const container = argument[1];\n        const source = argument[2];\n        //@ts-ignore\n        callback(el, container, source);\n        return;\n      }\n      callback(args);\n    }));\n  }\n  /* Does nothing useful. */\n  start(item) {\n    this.dragging = true;\n  }\n  /* Does nothing useful. */\n  end() {\n    this.dragging = false;\n  }\n  cancel(revert) {\n    this.dragging = false;\n  }\n  /* Does nothing useful. */\n  canMove(item) {\n    return this.options.accepts ? this.options.accepts(item) : false;\n  }\n  /* Does nothing useful. */\n  remove() {\n    this.dragging = false;\n  }\n  destroy() {\n    this.subs.unsubscribe();\n  }\n  /**\n   * This is the most useful method. You can use it to manually fire events that would normally\n   * be fired by a real drake.\n   *\n   * You're likely most interested in firing `drag`, `remove` and `drop`, the three events\n   * DragulaService uses to implement [dragulaModel].\n   *\n   * See https://github.com/bevacqua/dragula#drakeon-events for what you should emit (and in what order).\n   *\n   * (Note also, firing dropModel and removeModel won't work. You would have to mock DragulaService for that.)\n   */\n  emit(eventType, ...args) {\n    this.emitter$.next({\n      eventType,\n      args\n    });\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DragulaDirective, DragulaModule, DragulaService, DrakeFactory, EventTypes, Group, MockDrake, MockDrakeFactory, dragula };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,WAAO,UAAU,SAAS,KAAM,GAAG,GAAG;AAAE,aAAO,MAAM,UAAU,MAAM,KAAK,GAAG,CAAC;AAAA,IAAG;AAAA;AAAA;;;ACAjF;AAAA;AAAA,QAAI,KAAK,OAAO,iBAAiB;AAAjC,QAA6C;AAC7C,QAAI,IAAI;AACN,aAAO,SAAU,IAAI;AAAE,qBAAa,EAAE;AAAA,MAAG;AAAA,IAC3C,OAAO;AACL,aAAO,SAAU,IAAI;AAAE,mBAAW,IAAI,CAAC;AAAA,MAAG;AAAA,IAC5C;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACPjB;AAAA;AAAA;AAEA,QAAI,QAAQ;AAEZ,WAAO,UAAU,SAAS,SAAU,IAAI,MAAM,KAAK;AACjD,UAAI,CAAC,IAAI;AAAE;AAAA,MAAQ;AACnB,YAAM,SAAS,MAAO;AACpB,WAAG,MAAM,OAAO,MAAM,QAAQ,CAAC,CAAC;AAAA,MAClC,CAAC;AAAA,IACH;AAAA;AAAA;;;ACTA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,WAAW;AAEf,WAAO,UAAU,SAAS,QAAS,OAAO,SAAS;AACjD,UAAI,OAAO,WAAW,CAAC;AACvB,UAAI,MAAM,CAAC;AACX,UAAI,UAAU,QAAW;AAAE,gBAAQ,CAAC;AAAA,MAAG;AACvC,YAAM,KAAK,SAAU,MAAM,IAAI;AAC7B,YAAI,CAAC,IAAI,IAAI,GAAG;AACd,cAAI,IAAI,IAAI,CAAC,EAAE;AAAA,QACjB,OAAO;AACL,cAAI,IAAI,EAAE,KAAK,EAAE;AAAA,QACnB;AACA,eAAO;AAAA,MACT;AACA,YAAM,OAAO,SAAU,MAAM,IAAI;AAC/B,WAAG,QAAQ;AACX,cAAM,GAAG,MAAM,EAAE;AACjB,eAAO;AAAA,MACT;AACA,YAAM,MAAM,SAAU,MAAM,IAAI;AAC9B,YAAI,IAAI,UAAU;AAClB,YAAI,MAAM,GAAG;AACX,iBAAO,IAAI,IAAI;AAAA,QACjB,WAAW,MAAM,GAAG;AAClB,gBAAM,CAAC;AAAA,QACT,OAAO;AACL,cAAI,KAAK,IAAI,IAAI;AACjB,cAAI,CAAC,IAAI;AAAE,mBAAO;AAAA,UAAO;AACzB,aAAG,OAAO,GAAG,QAAQ,EAAE,GAAG,CAAC;AAAA,QAC7B;AACA,eAAO;AAAA,MACT;AACA,YAAM,OAAO,WAAY;AACvB,YAAI,OAAO,KAAK,SAAS;AACzB,eAAO,MAAM,gBAAgB,KAAK,MAAM,CAAC,EAAE,MAAM,MAAM,IAAI;AAAA,MAC7D;AACA,YAAM,kBAAkB,SAAU,MAAM;AACtC,YAAI,MAAM,IAAI,IAAI,KAAK,CAAC,GAAG,MAAM,CAAC;AAClC,eAAO,WAAY;AACjB,cAAI,OAAO,KAAK,SAAS;AACzB,cAAI,MAAM,QAAQ;AAClB,cAAI,SAAS,WAAW,KAAK,WAAW,SAAS,CAAC,GAAG,QAAQ;AAAE,kBAAM,KAAK,WAAW,IAAI,KAAK,CAAC,IAAI;AAAA,UAAM;AACzG,aAAG,QAAQ,SAASA,SAAS,QAAQ;AACnC,gBAAI,KAAK,OAAO;AAAE,uBAAS,QAAQ,MAAM,GAAG;AAAA,YAAG,OAAO;AAAE,qBAAO,MAAM,KAAK,IAAI;AAAA,YAAG;AACjF,gBAAI,OAAO,OAAO;AAAE,oBAAM,IAAI,MAAM,MAAM;AAAA,YAAG;AAAA,UAC/C,CAAC;AACD,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACrDA;AAAA;AACA,QAAI,oBAAoB,OAAO;AAE/B,aAAS,YAAa;AACpB,UAAI;AACF,YAAI,IAAI,IAAI,kBAAkB,OAAO,EAAE,QAAQ,EAAE,KAAK,MAAM,EAAE,CAAC;AAC/D,eAAQ,UAAU,EAAE,QAAQ,UAAU,EAAE,OAAO;AAAA,MACjD,SAAS,GAAG;AAAA,MACZ;AACA,aAAO;AAAA,IACT;AAUA,WAAO,UAAU,UAAU,IAAI;AAAA;AAAA,MAG/B,eAAe,OAAO,SAAS,cAAc,SAAS,YAAa,MAAM,QAAQ;AAC/E,YAAI,IAAI,SAAS,YAAY,aAAa;AAC1C,YAAI,QAAQ;AACV,YAAE,gBAAgB,MAAM,OAAO,SAAS,OAAO,YAAY,OAAO,MAAM;AAAA,QAC1E,OAAO;AACL,YAAE,gBAAgB,MAAM,OAAO,OAAO,MAAM;AAAA,QAC9C;AACA,eAAO;AAAA,MACT;AAAA;AAAA,QAGA,SAAS,YAAa,MAAM,QAAQ;AAClC,cAAI,IAAI,SAAS,kBAAkB;AACnC,YAAE,OAAO;AACT,cAAI,QAAQ;AACV,cAAE,UAAU,QAAQ,OAAO,OAAO;AAClC,cAAE,aAAa,QAAQ,OAAO,UAAU;AACxC,cAAE,SAAS,OAAO;AAAA,UACpB,OAAO;AACL,cAAE,UAAU;AACZ,cAAE,aAAa;AACf,cAAE,SAAS;AAAA,UACb;AACA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;;;AC/CA;AAAA;AAAA;AAEA,QAAI,WAAW,CAAC;AAChB,QAAI,YAAY;AAChB,QAAI,MAAM;AAEV,SAAK,aAAa,QAAQ;AACxB,UAAI,IAAI,KAAK,SAAS,GAAG;AACvB,iBAAS,KAAK,UAAU,MAAM,CAAC,CAAC;AAAA,MAClC;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACZjB;AAAA;AAAA;AAEA,QAAI,cAAc;AAClB,QAAI,WAAW;AACf,QAAI,MAAM,OAAO;AACjB,QAAI,WAAW;AACf,QAAI,cAAc;AAClB,QAAI,YAAY,CAAC;AAEjB,QAAI,CAAC,OAAO,kBAAkB;AAC5B,iBAAW;AACX,oBAAc;AAAA,IAChB;AAEA,WAAO,UAAU;AAAA,MACf,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,WAAW;AAAA,IACb;AAEA,aAAS,aAAc,IAAI,MAAM,IAAI,WAAW;AAC9C,aAAO,GAAG,iBAAiB,MAAM,IAAI,SAAS;AAAA,IAChD;AAEA,aAAS,aAAc,IAAI,MAAM,IAAI;AACnC,aAAO,GAAG,YAAY,OAAO,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;AAAA,IACvD;AAEA,aAAS,gBAAiB,IAAI,MAAM,IAAI,WAAW;AACjD,aAAO,GAAG,oBAAoB,MAAM,IAAI,SAAS;AAAA,IACnD;AAEA,aAAS,gBAAiB,IAAI,MAAM,IAAI;AACtC,UAAI,WAAW,OAAO,IAAI,MAAM,EAAE;AAClC,UAAI,UAAU;AACZ,eAAO,GAAG,YAAY,OAAO,MAAM,QAAQ;AAAA,MAC7C;AAAA,IACF;AAEA,aAAS,eAAgB,IAAI,MAAM,OAAO;AACxC,UAAI,IAAI,SAAS,QAAQ,IAAI,MAAM,KAAK,gBAAgB,IAAI,iBAAiB;AAC7E,UAAI,GAAG,eAAe;AACpB,WAAG,cAAc,CAAC;AAAA,MACpB,OAAO;AACL,WAAG,UAAU,OAAO,MAAM,CAAC;AAAA,MAC7B;AACA,eAAS,mBAAoB;AAC3B,YAAIC;AACJ,YAAI,IAAI,aAAa;AACnB,UAAAA,KAAI,IAAI,YAAY,OAAO;AAC3B,UAAAA,GAAE,UAAU,MAAM,MAAM,IAAI;AAAA,QAC9B,WAAW,IAAI,mBAAmB;AAChC,UAAAA,KAAI,IAAI,kBAAkB;AAAA,QAC5B;AACA,eAAOA;AAAA,MACT;AACA,eAAS,kBAAmB;AAC1B,eAAO,IAAI,YAAY,MAAM,EAAE,QAAQ,MAAM,CAAC;AAAA,MAChD;AAAA,IACF;AAEA,aAAS,eAAgB,IAAI,MAAM,IAAI;AACrC,aAAO,SAAS,QAAS,eAAe;AACtC,YAAI,IAAI,iBAAiB,OAAO;AAChC,UAAE,SAAS,EAAE,UAAU,EAAE;AACzB,UAAE,iBAAiB,EAAE,kBAAkB,SAAS,iBAAkB;AAAE,YAAE,cAAc;AAAA,QAAO;AAC3F,UAAE,kBAAkB,EAAE,mBAAmB,SAAS,kBAAmB;AAAE,YAAE,eAAe;AAAA,QAAM;AAC9F,UAAE,QAAQ,EAAE,SAAS,EAAE;AACvB,WAAG,KAAK,IAAI,CAAC;AAAA,MACf;AAAA,IACF;AAEA,aAAS,KAAM,IAAI,MAAM,IAAI;AAC3B,UAAI,UAAU,OAAO,IAAI,MAAM,EAAE,KAAK,eAAe,IAAI,MAAM,EAAE;AACjE,gBAAU,KAAK;AAAA,QACb;AAAA,QACA,SAAS;AAAA,QACT;AAAA,QACA;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAEA,aAAS,OAAQ,IAAI,MAAM,IAAI;AAC7B,UAAI,IAAI,KAAK,IAAI,MAAM,EAAE;AACzB,UAAI,GAAG;AACL,YAAI,UAAU,UAAU,CAAC,EAAE;AAC3B,kBAAU,OAAO,GAAG,CAAC;AACrB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,KAAM,IAAI,MAAM,IAAI;AAC3B,UAAI,GAAG;AACP,WAAK,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACrC,eAAO,UAAU,CAAC;AAClB,YAAI,KAAK,YAAY,MAAM,KAAK,SAAS,QAAQ,KAAK,OAAO,IAAI;AAC/D,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACpGA;AAAA;AAAA;AAEA,QAAI,QAAQ,CAAC;AACb,QAAI,QAAQ;AACZ,QAAI,MAAM;AAEV,aAAS,YAAa,WAAW;AAC/B,UAAI,SAAS,MAAM,SAAS;AAC5B,UAAI,QAAQ;AACV,eAAO,YAAY;AAAA,MACrB,OAAO;AACL,cAAM,SAAS,IAAI,SAAS,IAAI,OAAO,QAAQ,YAAY,KAAK,GAAG;AAAA,MACrE;AACA,aAAO;AAAA,IACT;AAEA,aAAS,SAAU,IAAI,WAAW;AAChC,UAAI,UAAU,GAAG;AACjB,UAAI,CAAC,QAAQ,QAAQ;AACnB,WAAG,YAAY;AAAA,MACjB,WAAW,CAAC,YAAY,SAAS,EAAE,KAAK,OAAO,GAAG;AAChD,WAAG,aAAa,MAAM;AAAA,MACxB;AAAA,IACF;AAEA,aAAS,QAAS,IAAI,WAAW;AAC/B,SAAG,YAAY,GAAG,UAAU,QAAQ,YAAY,SAAS,GAAG,GAAG,EAAE,KAAK;AAAA,IACxE;AAEA,WAAO,UAAU;AAAA,MACf,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AAAA;AAAA;;;AChCA;AAAA;AAAA;AAEA,QAAI,UAAU;AACd,QAAI,YAAY;AAChB,QAAI,UAAU;AACd,QAAI,MAAM;AACV,QAAI,kBAAkB,IAAI;AAE1B,aAASC,SAAS,mBAAmB,SAAS;AAC5C,UAAI,MAAM,UAAU;AACpB,UAAI,QAAQ,KAAK,MAAM,QAAQ,iBAAiB,MAAM,OAAO;AAC3D,kBAAU;AACV,4BAAoB,CAAC;AAAA,MACvB;AACA,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,kBAAkB;AACtB,UAAI;AAEJ,UAAI,IAAI,WAAW,CAAC;AACpB,UAAI,EAAE,UAAU,QAAQ;AAAE,UAAE,QAAQ;AAAA,MAAQ;AAC5C,UAAI,EAAE,YAAY,QAAQ;AAAE,UAAE,UAAU;AAAA,MAAQ;AAChD,UAAI,EAAE,YAAY,QAAQ;AAAE,UAAE,UAAU;AAAA,MAAe;AACvD,UAAI,EAAE,eAAe,QAAQ;AAAE,UAAE,aAAa,qBAAqB,CAAC;AAAA,MAAG;AACvE,UAAI,EAAE,gBAAgB,QAAQ;AAAE,UAAE,cAAc;AAAA,MAAO;AACvD,UAAI,EAAE,SAAS,QAAQ;AAAE,UAAE,OAAO;AAAA,MAAO;AACzC,UAAI,EAAE,mBAAmB,QAAQ;AAAE,UAAE,iBAAiB;AAAA,MAAO;AAC7D,UAAI,EAAE,kBAAkB,QAAQ;AAAE,UAAE,gBAAgB;AAAA,MAAO;AAC3D,UAAI,EAAE,kBAAkB,QAAQ;AAAE,UAAE,gBAAgB;AAAA,MAAO;AAC3D,UAAI,EAAE,cAAc,QAAQ;AAAE,UAAE,YAAY;AAAA,MAAY;AACxD,UAAI,EAAE,6BAA6B,QAAQ;AAAE,UAAE,2BAA2B;AAAA,MAAM;AAChF,UAAI,EAAE,oBAAoB,QAAQ;AAAE,UAAE,kBAAkB,IAAI;AAAA,MAAM;AAElE,UAAI,QAAQ,QAAQ;AAAA,QAClB,YAAY,EAAE;AAAA,QACd,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU;AAAA,MACZ,CAAC;AAED,UAAI,EAAE,kBAAkB,MAAM;AAC5B,cAAM,GAAG,QAAQ,SAAS,EAAE,GAAG,OAAO,QAAQ;AAAA,MAChD;AAEA,aAAO;AAEP,aAAO;AAEP,eAAS,YAAa,IAAI;AACxB,eAAO,MAAM,WAAW,QAAQ,EAAE,MAAM,MAAM,EAAE,YAAY,EAAE;AAAA,MAChE;AAEA,eAAS,OAAQC,SAAQ;AACvB,YAAI,KAAKA,UAAS,WAAW;AAC7B,eAAO,iBAAiB,IAAI,aAAa,IAAI;AAC7C,eAAO,iBAAiB,IAAI,WAAW,OAAO;AAAA,MAChD;AAEA,eAAS,kBAAmBA,SAAQ;AAClC,YAAI,KAAKA,UAAS,WAAW;AAC7B,eAAO,iBAAiB,IAAI,aAAa,sBAAsB;AAAA,MACjE;AAEA,eAAS,UAAWA,SAAQ;AAC1B,YAAI,KAAKA,UAAS,WAAW;AAC7B,kBAAU,EAAE,EAAE,iBAAiB,eAAe,cAAc;AAC5D,kBAAU,EAAE,EAAE,iBAAiB,SAAS,cAAc;AAAA,MACxD;AAEA,eAAS,UAAW;AAClB,eAAO,IAAI;AACX,gBAAQ,CAAC,CAAC;AAAA,MACZ;AAEA,eAAS,eAAgB,GAAG;AAC1B,YAAI,UAAU;AACZ,YAAE,eAAe;AAAA,QACnB;AAAA,MACF;AAEA,eAAS,KAAM,GAAG;AAChB,iBAAS,EAAE;AACX,iBAAS,EAAE;AAEX,YAAI,SAAS,iBAAiB,CAAC,MAAM,KAAK,EAAE,WAAW,EAAE;AACzD,YAAI,QAAQ;AACV;AAAA,QACF;AACA,YAAI,OAAO,EAAE;AACb,YAAI,UAAU,SAAS,IAAI;AAC3B,YAAI,CAAC,SAAS;AACZ;AAAA,QACF;AACA,mBAAW;AACX,0BAAkB;AAClB,YAAI,EAAE,SAAS,aAAa;AAC1B,cAAI,QAAQ,IAAI,GAAG;AACjB,iBAAK,MAAM;AAAA,UACb,OAAO;AACL,cAAE,eAAe;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AAEA,eAAS,uBAAwB,GAAG;AAClC,YAAI,CAAC,UAAU;AACb;AAAA,QACF;AACA,YAAI,iBAAiB,CAAC,MAAM,GAAG;AAC7B,kBAAQ,CAAC,CAAC;AACV;AAAA,QACF;AAEA,YAAI,EAAE,YAAY,UAAU,EAAE,YAAY,UAAU,EAAE,YAAY,UAAU,EAAE,YAAY,QAAQ;AAChG;AAAA,QACF;AACA,YAAI,EAAE,0BAA0B;AAC9B,cAAI,UAAU,SAAS,WAAW,CAAC;AACnC,cAAI,UAAU,SAAS,WAAW,CAAC;AACnC,cAAI,sBAAsB,IAAI,iBAAiB,SAAS,OAAO;AAC/D,cAAI,QAAQ,mBAAmB,GAAG;AAChC;AAAA,UACF;AAAA,QACF;AAEA,YAAI,UAAU;AACd,0BAAkB,IAAI;AACtB,kBAAU;AACV,YAAI;AACJ,cAAM,OAAO;AAEb,YAAI,SAAS,UAAU,KAAK;AAC5B,mBAAW,SAAS,SAAS,CAAC,IAAI,OAAO;AACzC,mBAAW,SAAS,SAAS,CAAC,IAAI,OAAO;AAEzC,gBAAQ,IAAI,SAAS,OAAO,YAAY;AACxC,0BAAkB;AAClB,aAAK,CAAC;AAAA,MACR;AAEA,eAAS,SAAU,MAAM;AACvB,YAAI,MAAM,YAAY,SAAS;AAC7B;AAAA,QACF;AACA,YAAI,YAAY,IAAI,GAAG;AACrB;AAAA,QACF;AACA,YAAI,SAAS;AACb,eAAO,UAAU,IAAI,KAAK,YAAY,UAAU,IAAI,CAAC,MAAM,OAAO;AAChE,cAAI,EAAE,QAAQ,MAAM,MAAM,GAAG;AAC3B;AAAA,UACF;AACA,iBAAO,UAAU,IAAI;AACrB,cAAI,CAAC,MAAM;AACT;AAAA,UACF;AAAA,QACF;AACA,YAAI,SAAS,UAAU,IAAI;AAC3B,YAAI,CAAC,QAAQ;AACX;AAAA,QACF;AACA,YAAI,EAAE,QAAQ,MAAM,MAAM,GAAG;AAC3B;AAAA,QACF;AAEA,YAAI,UAAU,EAAE,MAAM,MAAM,QAAQ,QAAQ,OAAO,IAAI,CAAC;AACxD,YAAI,CAAC,SAAS;AACZ;AAAA,QACF;AAEA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,eAAS,QAAS,MAAM;AACtB,eAAO,CAAC,CAAC,SAAS,IAAI;AAAA,MACxB;AAEA,eAAS,YAAa,MAAM;AAC1B,YAAI,UAAU,SAAS,IAAI;AAC3B,YAAI,SAAS;AACX,gBAAM,OAAO;AAAA,QACf;AAAA,MACF;AAEA,eAAS,MAAO,SAAS;AACvB,YAAI,OAAO,QAAQ,MAAM,QAAQ,MAAM,GAAG;AACxC,kBAAQ,QAAQ,KAAK,UAAU,IAAI;AACnC,gBAAM,KAAK,UAAU,OAAO,QAAQ,MAAM,MAAM;AAAA,QAClD;AAEA,kBAAU,QAAQ;AAClB,gBAAQ,QAAQ;AAChB,0BAAkB,kBAAkB,OAAO,QAAQ,IAAI;AAEvD,cAAM,WAAW;AACjB,cAAM,KAAK,QAAQ,OAAO,OAAO;AAAA,MACnC;AAEA,eAAS,gBAAiB;AACxB,eAAO;AAAA,MACT;AAEA,eAAS,MAAO;AACd,YAAI,CAAC,MAAM,UAAU;AACnB;AAAA,QACF;AACA,YAAI,OAAO,SAAS;AACpB,aAAK,MAAM,UAAU,IAAI,CAAC;AAAA,MAC5B;AAEA,eAAS,SAAU;AACjB,mBAAW;AACX,0BAAkB,IAAI;AACtB,kBAAU,IAAI;AAAA,MAChB;AAEA,eAAS,QAAS,GAAG;AACnB,eAAO;AAEP,YAAI,CAAC,MAAM,UAAU;AACnB;AAAA,QACF;AACA,YAAI,OAAO,SAAS;AACpB,YAAI,UAAU,SAAS,WAAW,CAAC;AACnC,YAAI,UAAU,SAAS,WAAW,CAAC;AACnC,YAAI,sBAAsB,sBAAsB,SAAS,SAAS,OAAO;AACzE,YAAI,aAAa,eAAe,qBAAqB,SAAS,OAAO;AACrE,YAAI,eAAgB,SAAS,EAAE,mBAAoB,CAAC,SAAS,eAAe,WAAW;AACrF,eAAK,MAAM,UAAU;AAAA,QACvB,WAAW,EAAE,eAAe;AAC1B,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,eAAS,KAAM,MAAM,QAAQ;AAC3B,YAAI,SAAS,UAAU,IAAI;AAC3B,YAAI,SAAS,EAAE,kBAAkB,WAAW,SAAS;AACnD,iBAAO,YAAY,KAAK;AAAA,QAC1B;AACA,YAAI,mBAAmB,MAAM,GAAG;AAC9B,gBAAM,KAAK,UAAU,MAAM,SAAS,OAAO;AAAA,QAC7C,OAAO;AACL,gBAAM,KAAK,QAAQ,MAAM,QAAQ,SAAS,eAAe;AAAA,QAC3D;AACA,gBAAQ;AAAA,MACV;AAEA,eAAS,SAAU;AACjB,YAAI,CAAC,MAAM,UAAU;AACnB;AAAA,QACF;AACA,YAAI,OAAO,SAAS;AACpB,YAAI,SAAS,UAAU,IAAI;AAC3B,YAAI,QAAQ;AACV,iBAAO,YAAY,IAAI;AAAA,QACzB;AACA,cAAM,KAAK,QAAQ,WAAW,UAAU,MAAM,QAAQ,OAAO;AAC7D,gBAAQ;AAAA,MACV;AAEA,eAAS,OAAQ,QAAQ;AACvB,YAAI,CAAC,MAAM,UAAU;AACnB;AAAA,QACF;AACA,YAAI,UAAU,UAAU,SAAS,IAAI,SAAS,EAAE;AAChD,YAAI,OAAO,SAAS;AACpB,YAAI,SAAS,UAAU,IAAI;AAC3B,YAAI,UAAU,mBAAmB,MAAM;AACvC,YAAI,YAAY,SAAS,SAAS;AAChC,cAAI,OAAO;AACT,gBAAI,QAAQ;AACV,qBAAO,YAAY,KAAK;AAAA,YAC1B;AAAA,UACF,OAAO;AACL,oBAAQ,aAAa,MAAM,eAAe;AAAA,UAC5C;AAAA,QACF;AACA,YAAI,WAAW,SAAS;AACtB,gBAAM,KAAK,UAAU,MAAM,SAAS,OAAO;AAAA,QAC7C,OAAO;AACL,gBAAM,KAAK,QAAQ,MAAM,QAAQ,SAAS,eAAe;AAAA,QAC3D;AACA,gBAAQ;AAAA,MACV;AAEA,eAAS,UAAW;AAClB,YAAI,OAAO,SAAS;AACpB,eAAO;AACP,0BAAkB;AAClB,YAAI,MAAM;AACR,kBAAQ,GAAG,MAAM,YAAY;AAAA,QAC/B;AACA,YAAI,cAAc;AAChB,uBAAa,YAAY;AAAA,QAC3B;AACA,cAAM,WAAW;AACjB,YAAI,iBAAiB;AACnB,gBAAM,KAAK,OAAO,MAAM,iBAAiB,OAAO;AAAA,QAClD;AACA,cAAM,KAAK,WAAW,IAAI;AAC1B,kBAAU,QAAQ,QAAQ,kBAAkB,kBAAkB,eAAe,kBAAkB;AAAA,MACjG;AAEA,eAAS,mBAAoB,QAAQ,GAAG;AACtC,YAAI;AACJ,YAAI,MAAM,QAAQ;AAChB,oBAAU;AAAA,QACZ,WAAW,SAAS;AAClB,oBAAU;AAAA,QACZ,OAAO;AACL,oBAAU,OAAO,SAAS,KAAK;AAAA,QACjC;AACA,eAAO,WAAW,WAAW,YAAY;AAAA,MAC3C;AAEA,eAAS,eAAgB,qBAAqB,SAAS,SAAS;AAC9D,YAAI,SAAS;AACb,eAAO,UAAU,CAAC,SAAS,GAAG;AAC5B,mBAAS,UAAU,MAAM;AAAA,QAC3B;AACA,eAAO;AAEP,iBAAS,WAAY;AACnB,cAAI,YAAY,YAAY,MAAM;AAClC,cAAI,cAAc,OAAO;AACvB,mBAAO;AAAA,UACT;AAEA,cAAI,YAAY,kBAAkB,QAAQ,mBAAmB;AAC7D,cAAI,YAAY,aAAa,QAAQ,WAAW,SAAS,OAAO;AAChE,cAAI,UAAU,mBAAmB,QAAQ,SAAS;AAClD,cAAI,SAAS;AACX,mBAAO;AAAA,UACT;AACA,iBAAO,EAAE,QAAQ,OAAO,QAAQ,SAAS,SAAS;AAAA,QACpD;AAAA,MACF;AAEA,eAAS,KAAM,GAAG;AAChB,YAAI,CAAC,SAAS;AACZ;AAAA,QACF;AACA,UAAE,eAAe;AAEjB,YAAI,UAAU,SAAS,WAAW,CAAC;AACnC,YAAI,UAAU,SAAS,WAAW,CAAC;AACnC,YAAI,IAAI,UAAU;AAClB,YAAI,IAAI,UAAU;AAElB,gBAAQ,MAAM,OAAO,IAAI;AACzB,gBAAQ,MAAM,MAAM,IAAI;AAExB,YAAI,OAAO,SAAS;AACpB,YAAI,sBAAsB,sBAAsB,SAAS,SAAS,OAAO;AACzE,YAAI,aAAa,eAAe,qBAAqB,SAAS,OAAO;AACrE,YAAI,UAAU,eAAe,QAAQ,eAAe;AACpD,YAAI,WAAW,eAAe,MAAM;AAClC,cAAI;AACJ,4BAAkB;AAClB,eAAK;AAAA,QACP;AACA,YAAI,SAAS,UAAU,IAAI;AAC3B,YAAI,eAAe,WAAW,SAAS,CAAC,EAAE,gBAAgB;AACxD,cAAI,QAAQ;AACV,mBAAO,YAAY,IAAI;AAAA,UACzB;AACA;AAAA,QACF;AACA,YAAI;AACJ,YAAI,YAAY,kBAAkB,YAAY,mBAAmB;AACjE,YAAI,cAAc,MAAM;AACtB,sBAAY,aAAa,YAAY,WAAW,SAAS,OAAO;AAAA,QAClE,WAAW,EAAE,kBAAkB,QAAQ,CAAC,OAAO;AAC7C,sBAAY;AACZ,uBAAa;AAAA,QACf,OAAO;AACL,cAAI,SAAS,QAAQ;AACnB,mBAAO,YAAY,IAAI;AAAA,UACzB;AACA;AAAA,QACF;AACA,YACG,cAAc,QAAQ,WACvB,cAAc,QACd,cAAc,OAAO,IAAI,GACzB;AACA,4BAAkB;AAClB,qBAAW,aAAa,MAAM,SAAS;AACvC,gBAAM,KAAK,UAAU,MAAM,YAAY,OAAO;AAAA,QAChD;AACA,iBAAS,MAAO,MAAM;AAAE,gBAAM,KAAK,MAAM,MAAM,iBAAiB,OAAO;AAAA,QAAG;AAC1E,iBAAS,OAAQ;AAAE,cAAI,SAAS;AAAE,kBAAM,MAAM;AAAA,UAAG;AAAA,QAAE;AACnD,iBAAS,MAAO;AAAE,cAAI,iBAAiB;AAAE,kBAAM,KAAK;AAAA,UAAG;AAAA,QAAE;AAAA,MAC3D;AAEA,eAAS,UAAW,IAAI;AACtB,gBAAQ,GAAG,IAAI,SAAS;AAAA,MAC1B;AAEA,eAAS,SAAU,IAAI;AACrB,YAAI,MAAM,UAAU;AAAE,kBAAQ,IAAI,IAAI,SAAS;AAAA,QAAG;AAAA,MACpD;AAEA,eAAS,oBAAqB;AAC5B,YAAI,SAAS;AACX;AAAA,QACF;AACA,YAAI,OAAO,MAAM,sBAAsB;AACvC,kBAAU,MAAM,UAAU,IAAI;AAC9B,gBAAQ,MAAM,QAAQ,aAAa,IAAI,IAAI;AAC3C,gBAAQ,MAAM,SAAS,cAAc,IAAI,IAAI;AAC7C,gBAAQ,GAAG,SAAS,YAAY;AAChC,gBAAQ,IAAI,SAAS,WAAW;AAChC,UAAE,gBAAgB,YAAY,OAAO;AACrC,eAAO,iBAAiB,OAAO,aAAa,IAAI;AAChD,gBAAQ,IAAI,EAAE,iBAAiB,iBAAiB;AAChD,cAAM,KAAK,UAAU,SAAS,OAAO,QAAQ;AAAA,MAC/C;AAEA,eAAS,oBAAqB;AAC5B,YAAI,SAAS;AACX,kBAAQ,GAAG,EAAE,iBAAiB,iBAAiB;AAC/C,iBAAO,iBAAiB,UAAU,aAAa,IAAI;AACnD,oBAAU,OAAO,EAAE,YAAY,OAAO;AACtC,oBAAU;AAAA,QACZ;AAAA,MACF;AAEA,eAAS,kBAAmB,YAAY,QAAQ;AAC9C,YAAI,YAAY;AAChB,eAAO,cAAc,cAAc,UAAU,SAAS,MAAM,YAAY;AACtE,sBAAY,UAAU,SAAS;AAAA,QACjC;AACA,YAAI,cAAc,iBAAiB;AACjC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAEA,eAAS,aAAc,YAAY,QAAQ,GAAG,GAAG;AAC/C,YAAI,aAAa,EAAE,cAAc;AACjC,YAAI,YAAY,WAAW,aAAa,OAAO,IAAI,QAAQ;AAC3D,eAAO;AAEP,iBAAS,UAAW;AAClB,cAAIC,OAAM,WAAW,SAAS;AAC9B,cAAI;AACJ,cAAI;AACJ,cAAI;AACJ,eAAK,IAAI,GAAG,IAAIA,MAAK,KAAK;AACxB,iBAAK,WAAW,SAAS,CAAC;AAC1B,mBAAO,GAAG,sBAAsB;AAChC,gBAAI,cAAe,KAAK,OAAO,KAAK,QAAQ,IAAK,GAAG;AAAE,qBAAO;AAAA,YAAI;AACjE,gBAAI,CAAC,cAAe,KAAK,MAAM,KAAK,SAAS,IAAK,GAAG;AAAE,qBAAO;AAAA,YAAI;AAAA,UACpE;AACA,iBAAO;AAAA,QACT;AAEA,iBAAS,SAAU;AACjB,cAAI,OAAO,OAAO,sBAAsB;AACxC,cAAI,YAAY;AACd,mBAAO,QAAQ,IAAI,KAAK,OAAO,aAAa,IAAI,IAAI,CAAC;AAAA,UACvD;AACA,iBAAO,QAAQ,IAAI,KAAK,MAAM,cAAc,IAAI,IAAI,CAAC;AAAA,QACvD;AAEA,iBAAS,QAAS,OAAO;AACvB,iBAAO,QAAQ,OAAO,MAAM,IAAI;AAAA,QAClC;AAAA,MACF;AAEA,eAAS,OAAQ,MAAM,WAAW;AAChC,eAAO,OAAO,EAAE,SAAS,YAAY,EAAE,OAAO,EAAE,KAAK,MAAM,SAAS;AAAA,MACtE;AAAA,IACF;AAEA,aAAS,OAAQ,IAAI,IAAI,MAAM,IAAI;AACjC,UAAI,QAAQ;AAAA,QACV,SAAS;AAAA,QACT,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AACA,UAAI,WAAW;AAAA,QACb,SAAS;AAAA,QACT,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AACA,UAAI,YAAY;AAAA,QACd,SAAS;AAAA,QACT,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AACA,UAAI,OAAO,UAAU,gBAAgB;AACnC,kBAAU,EAAE,EAAE,IAAI,SAAS,IAAI,GAAG,EAAE;AAAA,MACtC,WAAW,OAAO,UAAU,kBAAkB;AAC5C,kBAAU,EAAE,EAAE,IAAI,UAAU,IAAI,GAAG,EAAE;AAAA,MACvC,OAAO;AACL,kBAAU,EAAE,EAAE,IAAI,MAAM,IAAI,GAAG,EAAE;AACjC,kBAAU,EAAE,EAAE,IAAI,MAAM,EAAE;AAAA,MAC5B;AAAA,IACF;AAEA,aAAS,iBAAkB,GAAG;AAC5B,UAAI,EAAE,YAAY,QAAQ;AAAE,eAAO,EAAE,QAAQ;AAAA,MAAQ;AACrD,UAAI,EAAE,UAAU,UAAU,EAAE,UAAU,GAAG;AAAE,eAAO,EAAE;AAAA,MAAO;AAC3D,UAAI,EAAE,YAAY,QAAQ;AAAE,eAAO,EAAE;AAAA,MAAS;AAC9C,UAAI,SAAS,EAAE;AACf,UAAI,WAAW,QAAQ;AACrB,eAAO,SAAS,IAAI,IAAI,SAAS,IAAI,IAAK,SAAS,IAAI,IAAI;AAAA,MAC7D;AAAA,IACF;AAEA,aAAS,UAAW,IAAI;AACtB,UAAI,OAAO,GAAG,sBAAsB;AACpC,aAAO;AAAA,QACL,MAAM,KAAK,OAAO,UAAU,cAAc,aAAa;AAAA,QACvD,KAAK,KAAK,MAAM,UAAU,aAAa,aAAa;AAAA,MACtD;AAAA,IACF;AAEA,aAAS,UAAW,YAAY,YAAY;AAC1C,UAAI,OAAO,OAAO,UAAU,MAAM,aAAa;AAC7C,eAAO,OAAO,UAAU;AAAA,MAC1B;AACA,UAAI,gBAAgB,cAAc;AAChC,eAAO,gBAAgB,UAAU;AAAA,MACnC;AACA,aAAO,IAAI,KAAK,UAAU;AAAA,IAC5B;AAEA,aAAS,sBAAuB,OAAO,GAAG,GAAG;AAC3C,UAAI,IAAI,SAAS,CAAC;AAClB,UAAI,QAAQ,EAAE;AACd,UAAI;AACJ,QAAE,aAAa;AACf,WAAK,IAAI,iBAAiB,GAAG,CAAC;AAC9B,QAAE,YAAY;AACd,aAAO;AAAA,IACT;AAEA,aAAS,QAAS;AAAE,aAAO;AAAA,IAAO;AAClC,aAAS,SAAU;AAAE,aAAO;AAAA,IAAM;AAClC,aAAS,aAAc,MAAM;AAAE,aAAO,KAAK,SAAU,KAAK,QAAQ,KAAK;AAAA,IAAO;AAC9E,aAAS,cAAe,MAAM;AAAE,aAAO,KAAK,UAAW,KAAK,SAAS,KAAK;AAAA,IAAM;AAChF,aAAS,UAAW,IAAI;AAAE,aAAO,GAAG,eAAe,MAAM,OAAO,GAAG;AAAA,IAAY;AAC/E,aAAS,QAAS,IAAI;AAAE,aAAO,GAAG,YAAY,WAAW,GAAG,YAAY,cAAc,GAAG,YAAY,YAAY,WAAW,EAAE;AAAA,IAAG;AACjI,aAAS,WAAY,IAAI;AACvB,UAAI,CAAC,IAAI;AAAE,eAAO;AAAA,MAAO;AACzB,UAAI,GAAG,oBAAoB,SAAS;AAAE,eAAO;AAAA,MAAO;AACpD,UAAI,GAAG,oBAAoB,QAAQ;AAAE,eAAO;AAAA,MAAM;AAClD,aAAO,WAAW,UAAU,EAAE,CAAC;AAAA,IACjC;AAEA,aAAS,OAAQ,IAAI;AACnB,aAAO,GAAG,sBAAsB,SAAS;AACzC,eAAS,WAAY;AACnB,YAAI,UAAU;AACd,WAAG;AACD,oBAAU,QAAQ;AAAA,QACpB,SAAS,WAAW,QAAQ,aAAa;AACzC,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,aAAc,GAAG;AAIxB,UAAI,EAAE,iBAAiB,EAAE,cAAc,QAAQ;AAC7C,eAAO,EAAE,cAAc,CAAC;AAAA,MAC1B;AACA,UAAI,EAAE,kBAAkB,EAAE,eAAe,QAAQ;AAC/C,eAAO,EAAE,eAAe,CAAC;AAAA,MAC3B;AACA,aAAO;AAAA,IACT;AAEA,aAAS,SAAU,OAAO,GAAG;AAC3B,UAAI,OAAO,aAAa,CAAC;AACzB,UAAI,UAAU;AAAA,QACZ,OAAO;AAAA;AAAA,QACP,OAAO;AAAA;AAAA,MACT;AACA,UAAI,SAAS,WAAW,EAAE,SAAS,SAAS,QAAQ,KAAK,KAAK,MAAM;AAClE,gBAAQ,QAAQ,KAAK;AAAA,MACvB;AACA,aAAO,KAAK,KAAK;AAAA,IACnB;AAEA,WAAO,UAAUF;AAAA;AAAA;;;AC/lBjB;AACA;AACA;AACA;AACA,kBAA6B;AAC7B,IAAM,QAAN,MAAY;AAAA,EACV,YAAY,MAAM,OAAO,SAAS;AAChC,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,aAAa;AAAA,EACpB;AACF;AACA,IAAI;AAAA,CACH,SAAUG,aAAY;AACrB,EAAAA,YAAW,QAAQ,IAAI;AACvB,EAAAA,YAAW,QAAQ,IAAI;AACvB,EAAAA,YAAW,MAAM,IAAI;AACrB,EAAAA,YAAW,SAAS,IAAI;AACxB,EAAAA,YAAW,MAAM,IAAI;AACrB,EAAAA,YAAW,KAAK,IAAI;AACpB,EAAAA,YAAW,MAAM,IAAI;AACrB,EAAAA,YAAW,QAAQ,IAAI;AACvB,EAAAA,YAAW,QAAQ,IAAI;AACvB,EAAAA,YAAW,WAAW,IAAI;AAC1B,EAAAA,YAAW,aAAa,IAAI;AAC9B,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAM,YAAY,OAAO,KAAK,UAAU,EAAE,IAAI,OAAK,WAAW,CAAC,CAAC;AAChE,IAAM,UAAsB,uBAAW;AACvC,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAY,QAAQ,SAAS;AAC3B,SAAK,QAAQ;AAAA,EACf;AACF;AACA,IAAM,cAAc,CAAC,WAAW,gBAAgB,cAAc,WAAS;AACrE,SAAO,MAAM,KAAK,OAAO,CAAC;AAAA,IACxB;AAAA,IACA;AAAA,EACF,MAAM;AACJ,WAAO,UAAU,cAAc,mBAAmB,UAAa,SAAS;AAAA,EAC1E,CAAC,GAAG,IAAI,CAAC;AAAA,IACP;AAAA,IACA;AAAA,EACF,MAAM,UAAU,MAAM,IAAI,CAAC,CAAC;AAC9B;AACA,IAAM,6BAA6B,CAAC,MAAM,CAAC,IAAI,WAAW,MAAM,OAAO;AAAA,EACrE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,YAAY,cAAc;AACxB,SAAK,eAAe;AACpB,SAAK,SAAS,CAAC;AACf,SAAK,YAAY,IAAI,QAAQ;AAC7B,SAAK,oBAAoB,eAAa,eAAa,KAAK,UAAU,KAAK,YAAY,WAAW,WAAW,0BAA0B,CAAC;AAGpI,SAAK,SAAS,KAAK,kBAAkB,WAAW,MAAM;AAEtD,SAAK,SAAS,KAAK,kBAAkB,WAAW,MAAM;AAEtD,SAAK,SAAS,KAAK,kBAAkB,WAAW,MAAM;AAEtD,SAAK,OAAO,KAAK,kBAAkB,WAAW,IAAI;AAElD,SAAK,MAAM,KAAK,kBAAkB,WAAW,GAAG;AAChD,SAAK,OAAO,eAAa,KAAK,UAAU,KAAK,YAAY,WAAW,MAAM,WAAW,CAAC,MAAM,CAAC,IAAI,MAAM,OAAO;AAAA,MAC5G;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAE,CAAC;AACH,SAAK,UAAU,eAAa,KAAK,UAAU,KAAK,YAAY,WAAW,SAAS,WAAW,CAAC,MAAM,CAAC,EAAE,OAAO;AAAA,MAC1G;AAAA,MACA;AAAA,IACF,EAAE,CAAC;AACH,SAAK,OAAO,eAAa,KAAK,UAAU,KAAK,YAAY,WAAW,MAAM,WAAW,CAAC,MAAM,CAAC,IAAI,QAAQ,QAAQ,OAAO,MAAM;AAC5H,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AACF,SAAK,SAAS,eAAa,KAAK,UAAU,KAAK,YAAY,WAAW,QAAQ,WAAW,CAAC,MAAM,CAAC,OAAO,UAAU,SAAS,MAAM;AAC/H,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AACF,SAAK,YAAY,eAAa,KAAK,UAAU,KAAK,YAAY,WAAW,WAAW,WAAW,CAAC,MAAM,CAAC,IAAI,QAAQ,QAAQ,SAAS,MAAM,aAAa,aAAa,aAAa,WAAW,MAAM;AAChM,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AACF,SAAK,cAAc,eAAa,KAAK,UAAU,KAAK,YAAY,WAAW,aAAa,WAAW,CAAC,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,aAAa,WAAW,MAAM;AACpK,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AACF,QAAI,KAAK,iBAAiB,QAAQ,KAAK,iBAAiB,QAAW;AACjE,WAAK,eAAe,IAAI,aAAa;AAAA,IACvC;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,OAAO;AACT,UAAM,gBAAgB,KAAK,KAAK,MAAM,IAAI;AAC1C,QAAI,eAAe;AACjB,YAAM,IAAI,MAAM,mBAAmB,MAAM,OAAO,mBAAmB;AAAA,IACrE;AACA,SAAK,OAAO,MAAM,IAAI,IAAI;AAC1B,SAAK,aAAa,KAAK;AACvB,SAAK,YAAY,KAAK;AACtB,WAAO;AAAA,EACT;AAAA,EACA,KAAK,MAAM;AACT,WAAO,KAAK,OAAO,IAAI;AAAA,EACzB;AAAA,EACA,QAAQ,MAAM;AACZ,UAAM,QAAQ,KAAK,KAAK,IAAI;AAC5B,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AACA,UAAM,SAAS,MAAM,MAAM,QAAQ;AACnC,WAAO,KAAK,OAAO,IAAI;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,MAAM,SAAS;AACzB,WAAO,KAAK,IAAI,IAAI,MAAM,MAAM,KAAK,aAAa,MAAM,CAAC,GAAG,OAAO,GAAG,OAAO,CAAC;AAAA,EAChF;AAAA,EACA,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,UAAM,GAAG,UAAU,CAAC,IAAI,WAAW,WAAW;AAC5C,UAAI,CAAC,MAAM,QAAQ;AACjB;AAAA,MACF;AACA,UAAI,cAAc,MAAM,OAAO,MAAM,WAAW,QAAQ,MAAM,CAAC;AAC/D,oBAAc,YAAY,MAAM,CAAC;AACjC,YAAM,OAAO,YAAY,OAAO,WAAW,CAAC,EAAE,CAAC;AAC/C,WAAK,UAAU,KAAK;AAAA,QAClB,OAAO,WAAW;AAAA,QAClB;AAAA,QACA,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,aAAa,SAAS;AAAA,MAC5D,CAAC;AAAA,IACH,CAAC;AACD,UAAM,GAAG,QAAQ,CAAC,IAAI,WAAW;AAC/B,UAAI,CAAC,MAAM,QAAQ;AACjB;AAAA,MACF;AACA,gBAAU;AACV,kBAAY,KAAK,WAAW,IAAI,MAAM;AAAA,IACxC,CAAC;AACD,UAAM,GAAG,QAAQ,CAAC,SAAS,QAAQ,QAAQ,YAAY;AACrD,UAAI,CAAC,MAAM,UAAU,CAAC,QAAQ;AAC5B;AAAA,MACF;AACA,kBAAY,KAAK,WAAW,SAAS,MAAM;AAC3C,UAAI,cAAc,MAAM,OAAO,MAAM,WAAW,QAAQ,MAAM,CAAC;AAC/D,UAAI,cAAc,MAAM,OAAO,MAAM,WAAW,QAAQ,MAAM,CAAC;AAC/D,UAAI;AACJ,UAAI,WAAW,QAAQ;AACrB,sBAAc,YAAY,MAAM,CAAC;AACjC,eAAO,YAAY,OAAO,WAAW,CAAC,EAAE,CAAC;AACzC,oBAAY,OAAO,WAAW,GAAG,IAAI;AAGrC,sBAAc;AAAA,MAChB,OAAO;AACL,cAAM,YAAY,YAAY;AAC9B,eAAO,YAAY,SAAS;AAC5B,YAAI,WAAW;AACb,cAAI,CAAC,QAAQ,UAAU;AACrB,kBAAM,IAAI,MAAM,gFAAgF;AAAA,UAClG;AACA,iBAAO,QAAQ,SAAS,IAAI;AAAA,QAC9B;AACA,YAAI,CAAC,WAAW;AACd,wBAAc,YAAY,MAAM,CAAC;AACjC,sBAAY,OAAO,WAAW,CAAC;AAAA,QACjC;AACA,sBAAc,YAAY,MAAM,CAAC;AACjC,oBAAY,OAAO,WAAW,GAAG,IAAI;AACrC,YAAI,WAAW;AACb,cAAI;AACF,mBAAO,YAAY,OAAO;AAAA,UAE5B,SAAS,GAAG;AAAA,UAAC;AAAA,QACf;AAAA,MACF;AACA,WAAK,UAAU,KAAK;AAAA,QAClB,OAAO,WAAW;AAAA,QAClB;AAAA,QACA,MAAM,CAAC,SAAS,QAAQ,QAAQ,SAAS,MAAM,aAAa,aAAa,WAAW,SAAS;AAAA,MAC/F,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,MAAM,YAAY;AACpB;AAAA,IACF;AACA,UAAM,aAAa;AACnB,UAAM,OAAO,MAAM;AAEnB,UAAM,OAAO;AACb,UAAM,UAAU,WAAS;AACvB,cAAQ,OAAO;AAAA,QACb,KAAK,WAAW;AACd,gBAAM,MAAM,GAAG,OAAO,IAAI,SAAS;AACjC,iBAAK,UAAU,KAAK;AAAA,cAClB;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AACD;AAAA,QACF,KAAK,WAAW;AACd,gBAAM,MAAM,GAAG,OAAO,IAAI,SAAS;AACjC,iBAAK,UAAU,KAAK;AAAA,cAClB;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AACD;AAAA,QACF,KAAK,WAAW;AACd,gBAAM,MAAM,GAAG,OAAO,IAAI,SAAS;AACjC,iBAAK,UAAU,KAAK;AAAA,cAClB;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AACD;AAAA,QACF,KAAK,WAAW;AAAA,QAChB,KAAK,WAAW;AAAA,QAChB,KAAK,WAAW;AAAA,QAChB,KAAK,WAAW;AAAA,QAChB,KAAK,WAAW;AACd,gBAAM,MAAM,GAAG,OAAO,IAAI,SAAS;AACjC,iBAAK,UAAU,KAAK;AAAA,cAClB;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AACD;AAAA,QACF,KAAK,WAAW;AACd,gBAAM,MAAM,GAAG,OAAO,IAAI,SAAS;AACjC,iBAAK,UAAU,KAAK;AAAA,cAClB;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AACD;AAAA,QACF,KAAK,WAAW;AACd,gBAAM,MAAM,GAAG,OAAO,IAAI,SAAS;AACjC,iBAAK,UAAU,KAAK;AAAA,cAClB;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AACD;AAAA,QACF,KAAK,WAAW;AACd,gBAAM,MAAM,GAAG,OAAO,IAAI,SAAS;AACjC,iBAAK,UAAU,KAAK;AAAA,cAClB;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AACD;AAAA,QACF;AACE;AAAA,MACJ;AAAA,IACF;AACA,cAAU,QAAQ,OAAO;AAAA,EAC3B;AAAA,EACA,WAAW,OAAO,QAAQ;AACxB,QAAI,QAAQ;AACV,aAAO,MAAM,UAAU,QAAQ,KAAK,OAAO,UAAU,KAAK;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAmB,SAAS,cAAc,CAAC,CAAC;AAAA,IAC/E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,gBAAe;AAAA,MACxB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,IAAI,YAAY;AACd,WAAO,KAAK,MAAM,KAAK,GAAG;AAAA,EAC5B;AAAA,EACA,YAAY,IAAI,gBAAgB;AAC9B,SAAK,KAAK;AACV,SAAK,iBAAiB;AACtB,SAAK,qBAAqB,IAAI,aAAa;AAAA,EAC7C;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,WAAW,QAAQ,SAAS;AAC9B,YAAM;AAAA,QACJ,eAAe;AAAA,QACf,cAAc;AAAA,QACd;AAAA,MACF,IAAI,QAAQ;AACZ,YAAM,mBAAmB,CAAC,CAAC;AAC3B,YAAM,cAAc,CAAC,CAAC;AAMtB,UAAI,kBAAkB;AACpB,aAAK,SAAS,IAAI;AAAA,MACpB;AACA,UAAI,aAAa;AACf,aAAK,MAAM;AAAA,MACb;AAAA,IACF,WAAW,WAAW,QAAQ,cAAc;AAI1C,YAAM;AAAA,QACJ,eAAe;AAAA,QACf,cAAc;AAAA,QACd;AAAA,MACF,IAAI,QAAQ;AACZ,YAAM,QAAQ,KAAK,OAAO;AAC1B,UAAI,KAAK,WAAW,OAAO;AACzB,cAAM,SAAS,MAAM,UAAU,CAAC;AAChC,cAAM,YAAY,MAAM,OAAO,QAAQ,IAAI;AAC3C,YAAI,cAAc,IAAI;AAEpB,gBAAM,OAAO,OAAO,WAAW,CAAC;AAEhC,cAAI,SAAS;AACX,kBAAM,OAAO,OAAO,WAAW,GAAG,OAAO;AAAA,UAC3C;AAAA,QACF,WAAW,SAAS;AAElB,gBAAM,OAAO,KAAK,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA,EAGA,QAAQ;AACN,UAAM,aAAa,CAAAC,WAAS;AAC1B,UAAI,KAAK,cAAc;AACrB,YAAIA,OAAM,OAAO,QAAQ;AACvB,UAAAA,OAAM,OAAO,QAAQ,KAAK,KAAK,YAAY;AAAA,QAC7C,OAAO;AACL,cAAIA,OAAM,OAAO;AACf,YAAAA,OAAM,MAAM,SAAS,CAAC,KAAK,YAAY;AAAA,UACzC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,QAAI,CAAC,KAAK,SAAS;AACjB;AAAA,IACF;AACA,QAAI,QAAQ,KAAK,eAAe,KAAK,KAAK,OAAO;AACjD,QAAI,CAAC,OAAO;AACV,YAAM,UAAU,CAAC;AACjB,cAAQ,KAAK,eAAe,YAAY,KAAK,SAAS,OAAO;AAAA,IAC/D;AAEA,eAAW,KAAK;AAChB,UAAM,OAAO,WAAW,KAAK,KAAK,SAAS;AAC3C,SAAK,UAAU,KAAK,OAAO;AAC3B,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,UAAU,MAAM;AACd,SAAK,OAAO,IAAI,aAAa;AAC7B,SAAK,KAAK,IAAI,KAAK,eAAe,UAAU,IAAI,EAAE,UAAU,CAAC;AAAA,MAC3D;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AACJ,UAAI,WAAW,KAAK,GAAG,eAAe;AACpC,aAAK,mBAAmB,KAAK,WAAW;AAAA,MAC1C,WAAW,WAAW,KAAK,GAAG,eAAe;AAC3C,aAAK,mBAAmB,KAAK,WAAW;AAAA,MAC1C;AAAA,IACF,CAAC,CAAC;AACF,SAAK,KAAK,IAAI,KAAK,eAAe,YAAY,IAAI,EAAE,UAAU,CAAC;AAAA,MAC7D;AAAA,MACA;AAAA,IACF,MAAM;AACJ,UAAI,WAAW,KAAK,GAAG,eAAe;AACpC,aAAK,mBAAmB,KAAK,WAAW;AAAA,MAC1C;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,YAAY;AAAA,IACxB;AACA,UAAM,QAAQ,KAAK,eAAe,KAAK,SAAS;AAChD,QAAI,OAAO;AACT,YAAM,eAAe,MAAM,OAAO,WAAW,QAAQ,KAAK,GAAG,aAAa;AAC1E,UAAI,iBAAiB,IAAI;AACvB,cAAM,OAAO,WAAW,OAAO,cAAc,CAAC;AAAA,MAChD;AACA,UAAI,KAAK,gBAAgB,MAAM,SAAS,MAAM,MAAM,QAAQ;AAC1D,cAAM,aAAa,MAAM,MAAM,OAAO,QAAQ,KAAK,YAAY;AAC/D,YAAI,eAAe,IAAI;AACrB,gBAAM,MAAM,OAAO,OAAO,YAAY,CAAC;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,CAAC,KAAK,SAAS;AACjB;AAAA,IACF;AACA,SAAK,SAAS,KAAK,OAAO;AAAA,EAC5B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,kBAAqB,UAAU,GAAM,kBAAkB,cAAc,CAAC;AAAA,IAC9H;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,MAC/B,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,cAAc;AAAA,MAChB;AAAA,MACA,SAAS;AAAA,QACP,oBAAoB;AAAA,MACtB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,UAAU;AACf,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,cAAc;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,gBAAgB;AAAA,MAC/B,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,cAAc;AAAA,IAC5B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,gBAAgB;AAAA,MAC1B,cAAc,CAAC,gBAAgB;AAAA,MAC/B,WAAW,CAAC,cAAc;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,mBAAmB,IAAI,aAAa,CAAC,YAAY,YAAY;AACjE,SAAO,IAAI,UAAU,YAAY,OAAO;AAC1C,CAAC;AAiBD,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOd,YAAY,aAAa,CAAC,GAAG,UAAU,CAAC,GAAG,QAAQ;AACjD,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,SAAS;AAEd,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,OAAO,IAAI,aAAa;AAE7B,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,GAAG,OAAO,UAAU;AAClB,SAAK,KAAK,IAAI,KAAK,SAAS,KAAK,OAAO,CAAC;AAAA,MACvC;AAAA,IACF,MAAM,cAAc,KAAK,CAAC,EAAE,UAAU,CAAC;AAAA,MACrC;AAAA,MACA;AAAA,IACF,MAAM;AACJ,UAAI,cAAc,WAAW,MAAM;AACjC,cAAM,WAAW,MAAM,KAAK,IAAI;AAChC,cAAM,KAAK,SAAS,CAAC;AACrB,cAAM,SAAS,SAAS,CAAC;AAEzB,iBAAS,IAAI,MAAM;AACnB;AAAA,MACF;AACA,UAAI,cAAc,WAAW,MAAM;AACjC,cAAM,WAAW,MAAM,KAAK,IAAI;AAChC,cAAM,KAAK,SAAS,CAAC;AACrB,cAAM,SAAS,SAAS,CAAC;AACzB,cAAM,SAAS,SAAS,CAAC;AACzB,cAAM,UAAU,SAAS,CAAC;AAE1B,iBAAS,IAAI,QAAQ,QAAQ,OAAO;AACpC;AAAA,MACF;AACA,UAAI,cAAc,WAAW,QAAQ;AACnC,cAAM,WAAW,MAAM,KAAK,IAAI;AAChC,cAAM,KAAK,SAAS,CAAC;AACrB,cAAM,YAAY,SAAS,CAAC;AAC5B,cAAM,SAAS,SAAS,CAAC;AAEzB,iBAAS,IAAI,WAAW,MAAM;AAC9B;AAAA,MACF;AACA,eAAS,IAAI;AAAA,IACf,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA,EAEA,MAAM,MAAM;AACV,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,MAAM;AACJ,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO,QAAQ;AACb,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,QAAQ,MAAM;AACZ,WAAO,KAAK,QAAQ,UAAU,KAAK,QAAQ,QAAQ,IAAI,IAAI;AAAA,EAC7D;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,UAAU;AACR,SAAK,KAAK,YAAY;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,KAAK,cAAc,MAAM;AACvB,SAAK,SAAS,KAAK;AAAA,MACjB;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;", "names": ["emitter", "e", "dragula", "remove", "len", "EventTypes", "group"]}
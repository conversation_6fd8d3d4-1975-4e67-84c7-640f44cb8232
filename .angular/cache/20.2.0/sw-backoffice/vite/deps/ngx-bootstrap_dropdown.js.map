{"version": 3, "sources": ["../../../../../../node_modules/ngx-bootstrap/dropdown/fesm2022/ngx-bootstrap-dropdown.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Component, ChangeDetectionStrategy, Directive, Input, Output, HostListener, HostBinding, NgModule } from '@angular/core';\nimport { filter } from 'rxjs/operators';\nimport * as i1 from 'ngx-bootstrap/component-loader';\nimport { ComponentLoaderFactory } from 'ngx-bootstrap/component-loader';\nimport * as i2 from '@angular/animations';\nimport { style, animate } from '@angular/animations';\nimport { NgClass } from '@angular/common';\n\n/** Default dropdown configuration */\nconst _c0 = [\"*\"];\nconst _c1 = a0 => ({\n  dropdown: a0\n});\nclass BsDropdownConfig {\n  constructor() {\n    /** default dropdown auto closing behavior */\n    this.autoClose = true;\n    /** default dropdown auto closing behavior */\n    this.insideClick = false;\n    /** turn on/off animation */\n    this.isAnimated = false;\n    /** value true of stopOnClickPropagation allows event stopPropagation*/\n    this.stopOnClickPropagation = false;\n  }\n  static {\n    this.ɵfac = function BsDropdownConfig_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BsDropdownConfig)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BsDropdownConfig,\n      factory: BsDropdownConfig.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownConfig, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass BsDropdownState {\n  constructor() {\n    this.direction = 'down';\n    this.autoClose = true;\n    this.insideClick = false;\n    this.isAnimated = false;\n    this.stopOnClickPropagation = false;\n    this.isOpenChange = new EventEmitter();\n    this.isDisabledChange = new EventEmitter();\n    this.toggleClick = new EventEmitter();\n    this.counts = 0;\n    this.dropdownMenu = new Promise(resolve => {\n      this.resolveDropdownMenu = resolve;\n    });\n  }\n  static {\n    this.ɵfac = function BsDropdownState_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BsDropdownState)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BsDropdownState,\n      factory: BsDropdownState.ɵfac,\n      providedIn: 'platform'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownState, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'platform'\n    }]\n  }], () => [], null);\n})();\nconst DROPDOWN_ANIMATION_TIMING = '220ms cubic-bezier(0, 0, 0.2, 1)';\nconst dropdownAnimation = [style({\n  height: 0,\n  overflow: 'hidden'\n}), animate(DROPDOWN_ANIMATION_TIMING, style({\n  height: '*',\n  overflow: 'hidden'\n}))];\n\n// todo: revert ngClass to [class] when false positive angular-cli issue is fixed\n//          [class.dropdown]=\"direction === 'down'\"-->\nclass BsDropdownContainerComponent {\n  get direction() {\n    return this._state.direction;\n  }\n  constructor(_state, cd, _renderer, _element, _builder) {\n    this._state = _state;\n    this.cd = cd;\n    this._renderer = _renderer;\n    this._element = _element;\n    this.isOpen = false;\n    this._factoryDropDownAnimation = _builder.build(dropdownAnimation);\n    this._subscription = _state.isOpenChange.subscribe(value => {\n      this.isOpen = value;\n      const dropdown = this._element.nativeElement.querySelector('.dropdown-menu');\n      this._renderer.addClass(this._element.nativeElement.querySelector('div'), 'open');\n      if (dropdown) {\n        this._renderer.addClass(dropdown, 'show');\n        if (dropdown.classList.contains('dropdown-menu-right') || dropdown.classList.contains('dropdown-menu-end')) {\n          this._renderer.setStyle(dropdown, 'left', 'auto');\n          this._renderer.setStyle(dropdown, 'right', '0');\n        }\n        if (this.direction === 'up') {\n          this._renderer.setStyle(dropdown, 'top', 'auto');\n          this._renderer.setStyle(dropdown, 'transform', 'translateY(-101%)');\n        }\n      }\n      if (dropdown && this._state.isAnimated) {\n        this._factoryDropDownAnimation.create(dropdown).play();\n      }\n      this.cd.markForCheck();\n      this.cd.detectChanges();\n    });\n  }\n  /** @internal */\n  _contains(el) {\n    return this._element.nativeElement.contains(el);\n  }\n  ngOnDestroy() {\n    this._subscription.unsubscribe();\n  }\n  static {\n    this.ɵfac = function BsDropdownContainerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BsDropdownContainerComponent)(i0.ɵɵdirectiveInject(BsDropdownState), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.AnimationBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: BsDropdownContainerComponent,\n      selectors: [[\"bs-dropdown-container\"]],\n      hostAttrs: [2, \"display\", \"block\", \"position\", \"absolute\", \"z-index\", \"1040\"],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 9,\n      consts: [[3, \"ngClass\"]],\n      template: function BsDropdownContainerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"dropup\", ctx.direction === \"up\")(\"show\", ctx.isOpen)(\"open\", ctx.isOpen);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c1, ctx.direction === \"down\"));\n        }\n      },\n      dependencies: [NgClass],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownContainerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'bs-dropdown-container',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true,\n      imports: [NgClass],\n      // eslint-disable-next-line @angular-eslint/no-host-metadata-property\n      host: {\n        style: 'display:block;position: absolute;z-index: 1040'\n      },\n      template: `\n    <div [class.dropup]=\"direction === 'up'\"\n         [ngClass]=\"{dropdown: direction === 'down'}\"\n         [class.show]=\"isOpen\"\n         [class.open]=\"isOpen\"><ng-content></ng-content>\n    </div>\n  `\n    }]\n  }], () => [{\n    type: BsDropdownState\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i2.AnimationBuilder\n  }], null);\n})();\nclass BsDropdownDirective {\n  /**\n   * Indicates that dropdown will be closed on item or document click,\n   * and after pressing ESC\n   */\n  set autoClose(value) {\n    this._state.autoClose = value;\n  }\n  get autoClose() {\n    return this._state.autoClose;\n  }\n  /**\n   * Indicates that dropdown will be animated\n   */\n  set isAnimated(value) {\n    this._state.isAnimated = value;\n  }\n  get isAnimated() {\n    return this._state.isAnimated;\n  }\n  /**\n   * This attribute indicates that the dropdown shouldn't close on inside click when autoClose is set to true\n   */\n  set insideClick(value) {\n    this._state.insideClick = value;\n  }\n  get insideClick() {\n    return this._state.insideClick;\n  }\n  /**\n   * Disables dropdown toggle and hides dropdown menu if opened\n   */\n  set isDisabled(value) {\n    this._isDisabled = value;\n    this._state.isDisabledChange.emit(value);\n    if (value) {\n      this.hide();\n    }\n  }\n  get isDisabled() {\n    return this._isDisabled;\n  }\n  /**\n   * Returns whether or not the popover is currently being shown\n   */\n  get isOpen() {\n    if (this._showInline) {\n      return this._isInlineOpen;\n    }\n    return this._dropdown.isShown;\n  }\n  set isOpen(value) {\n    if (value) {\n      this.show();\n    } else {\n      this.hide();\n    }\n  }\n  get _showInline() {\n    return !this.container;\n  }\n  constructor(_elementRef, _renderer, _viewContainerRef, _cis, _state, _config, _builder) {\n    this._elementRef = _elementRef;\n    this._renderer = _renderer;\n    this._viewContainerRef = _viewContainerRef;\n    this._cis = _cis;\n    this._state = _state;\n    this._config = _config;\n    /**\n     * This attribute indicates that the dropdown should be opened upwards\n     */\n    this.dropup = false;\n    // todo: move to component loader\n    this._isInlineOpen = false;\n    this._isDisabled = false;\n    this._subscriptions = [];\n    this._isInited = false;\n    // set initial dropdown state from config\n    this._state.autoClose = this._config.autoClose;\n    this._state.insideClick = this._config.insideClick;\n    this._state.isAnimated = this._config.isAnimated;\n    this._state.stopOnClickPropagation = this._config.stopOnClickPropagation;\n    this._factoryDropDownAnimation = _builder.build(dropdownAnimation);\n    // create dropdown component loader\n    this._dropdown = this._cis.createLoader(this._elementRef, this._viewContainerRef, this._renderer).provide({\n      provide: BsDropdownState,\n      useValue: this._state\n    });\n    this.onShown = this._dropdown.onShown;\n    this.onHidden = this._dropdown.onHidden;\n    this.isOpenChange = this._state.isOpenChange;\n  }\n  ngOnInit() {\n    // fix: seems there are an issue with `routerLinkActive`\n    // which result in duplicated call ngOnInit without call to ngOnDestroy\n    // read more: https://github.com/valor-software/ngx-bootstrap/issues/1885\n    if (this._isInited) {\n      return;\n    }\n    this._isInited = true;\n    // attach DOM listeners\n    this._dropdown.listen({\n      // because of dropdown inline mode\n      outsideClick: false,\n      triggers: this.triggers,\n      show: () => this.show()\n    });\n    // toggle visibility on toggle element click\n    this._subscriptions.push(this._state.toggleClick.subscribe(value => this.toggle(value)));\n    // hide dropdown if set disabled while opened\n    this._subscriptions.push(this._state.isDisabledChange.pipe(filter(value => value)).subscribe((/*value: boolean*/) => this.hide()));\n  }\n  /**\n   * Opens an element’s popover. This is considered a “manual” triggering of\n   * the popover.\n   */\n  show() {\n    if (this.isOpen || this.isDisabled) {\n      return;\n    }\n    if (this._showInline) {\n      if (!this._inlinedMenu) {\n        this._state.dropdownMenu.then(dropdownMenu => {\n          this._dropdown.attachInline(dropdownMenu.viewContainer, dropdownMenu.templateRef);\n          this._inlinedMenu = this._dropdown._inlineViewRef;\n          this.addBs4Polyfills();\n          if (this._inlinedMenu) {\n            this._renderer.addClass(this._inlinedMenu.rootNodes[0].parentNode, 'open');\n          }\n          this.playAnimation();\n        })\n        // swallow errors\n        .catch();\n      }\n      this.addBs4Polyfills();\n      this._isInlineOpen = true;\n      this.onShown.emit(true);\n      this._state.isOpenChange.emit(true);\n      this.playAnimation();\n      return;\n    }\n    this._state.dropdownMenu.then(dropdownMenu => {\n      // check direction in which dropdown should be opened\n      const _dropup = this.dropup || typeof this.dropup !== 'undefined' && this.dropup;\n      this._state.direction = _dropup ? 'up' : 'down';\n      const _placement = this.placement || (_dropup ? 'top start' : 'bottom start');\n      // show dropdown\n      this._dropdown.attach(BsDropdownContainerComponent).to(this.container).position({\n        attachment: _placement\n      }).show({\n        content: dropdownMenu.templateRef,\n        placement: _placement\n      });\n      this._state.isOpenChange.emit(true);\n    })\n    // swallow error\n    .catch();\n  }\n  /**\n   * Closes an element’s popover. This is considered a “manual” triggering of\n   * the popover.\n   */\n  hide() {\n    if (!this.isOpen) {\n      return;\n    }\n    if (this._showInline) {\n      this.removeShowClass();\n      this.removeDropupStyles();\n      this._isInlineOpen = false;\n      this.onHidden.emit(true);\n    } else {\n      this._dropdown.hide();\n    }\n    this._state.isOpenChange.emit(false);\n  }\n  /**\n   * Toggles an element’s popover. This is considered a “manual” triggering of\n   * the popover. With parameter <code>true</code> allows toggling, with parameter <code>false</code>\n   * only hides opened dropdown. Parameter usage will be removed in ngx-bootstrap v3\n   */\n  toggle(value) {\n    if (this.isOpen || !value) {\n      return this.hide();\n    }\n    return this.show();\n  }\n  /** @internal */\n  _contains(event) {\n    // todo: valorkin fix typings\n    return this._elementRef.nativeElement.contains(event.target) || this._dropdown.instance && this._dropdown.instance._contains(event.target);\n  }\n  navigationClick(event) {\n    const ref = this._elementRef.nativeElement.querySelector('.dropdown-menu');\n    if (!ref) {\n      return;\n    }\n    const firstActive = this._elementRef.nativeElement.ownerDocument.activeElement;\n    const allRef = ref.querySelectorAll('.dropdown-item');\n    switch (event.keyCode) {\n      case 38:\n        if (this._state.counts > 0) {\n          allRef[--this._state.counts].focus();\n        }\n        break;\n      case 40:\n        if (this._state.counts + 1 < allRef.length) {\n          if (firstActive.classList !== allRef[this._state.counts].classList) {\n            allRef[this._state.counts].focus();\n          } else {\n            allRef[++this._state.counts].focus();\n          }\n        }\n        break;\n      default:\n    }\n    event.preventDefault();\n  }\n  ngOnDestroy() {\n    // clean up subscriptions and destroy dropdown\n    for (const sub of this._subscriptions) {\n      sub.unsubscribe();\n    }\n    this._dropdown.dispose();\n  }\n  addBs4Polyfills() {\n    this.addShowClass();\n    this.checkRightAlignment();\n    this.addDropupStyles();\n  }\n  playAnimation() {\n    if (this._state.isAnimated && this._inlinedMenu) {\n      setTimeout(() => {\n        if (this._inlinedMenu) {\n          this._factoryDropDownAnimation.create(this._inlinedMenu.rootNodes[0]).play();\n        }\n      });\n    }\n  }\n  addShowClass() {\n    if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n      this._renderer.addClass(this._inlinedMenu.rootNodes[0], 'show');\n    }\n  }\n  removeShowClass() {\n    if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n      this._renderer.removeClass(this._inlinedMenu.rootNodes[0], 'show');\n    }\n  }\n  checkRightAlignment() {\n    if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n      const isRightAligned = this._inlinedMenu.rootNodes[0].classList.contains('dropdown-menu-right') || this._inlinedMenu.rootNodes[0].classList.contains('dropdown-menu-end');\n      this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'left', isRightAligned ? 'auto' : '0');\n      this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'right', isRightAligned ? '0' : 'auto');\n    }\n  }\n  addDropupStyles() {\n    if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n      // a little hack to not break support of bootstrap 4 beta\n      this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'top', this.dropup ? 'auto' : '100%');\n      this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'transform', this.dropup ? 'translateY(-101%)' : 'translateY(0)');\n      this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'bottom', 'auto');\n    }\n  }\n  removeDropupStyles() {\n    if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n      this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'top');\n      this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'transform');\n      this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'bottom');\n    }\n  }\n  static {\n    this.ɵfac = function BsDropdownDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BsDropdownDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i1.ComponentLoaderFactory), i0.ɵɵdirectiveInject(BsDropdownState), i0.ɵɵdirectiveInject(BsDropdownConfig), i0.ɵɵdirectiveInject(i2.AnimationBuilder));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: BsDropdownDirective,\n      selectors: [[\"\", \"bsDropdown\", \"\"], [\"\", \"dropdown\", \"\"]],\n      hostVars: 6,\n      hostBindings: function BsDropdownDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.arrowDown\", function BsDropdownDirective_keydown_arrowDown_HostBindingHandler($event) {\n            return ctx.navigationClick($event);\n          })(\"keydown.arrowUp\", function BsDropdownDirective_keydown_arrowUp_HostBindingHandler($event) {\n            return ctx.navigationClick($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"dropup\", ctx.dropup)(\"open\", ctx.isOpen)(\"show\", ctx.isOpen);\n        }\n      },\n      inputs: {\n        placement: \"placement\",\n        triggers: \"triggers\",\n        container: \"container\",\n        dropup: \"dropup\",\n        autoClose: \"autoClose\",\n        isAnimated: \"isAnimated\",\n        insideClick: \"insideClick\",\n        isDisabled: \"isDisabled\",\n        isOpen: \"isOpen\"\n      },\n      outputs: {\n        isOpenChange: \"isOpenChange\",\n        onShown: \"onShown\",\n        onHidden: \"onHidden\"\n      },\n      exportAs: [\"bs-dropdown\"],\n      features: [i0.ɵɵProvidersFeature([BsDropdownState, ComponentLoaderFactory, BsDropdownConfig])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[bsDropdown], [dropdown]',\n      exportAs: 'bs-dropdown',\n      providers: [BsDropdownState, ComponentLoaderFactory, BsDropdownConfig],\n      standalone: true,\n      // eslint-disable-next-line @angular-eslint/no-host-metadata-property\n      host: {\n        '[class.dropup]': 'dropup',\n        '[class.open]': 'isOpen',\n        '[class.show]': 'isOpen'\n      }\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i1.ComponentLoaderFactory\n  }, {\n    type: BsDropdownState\n  }, {\n    type: BsDropdownConfig\n  }, {\n    type: i2.AnimationBuilder\n  }], {\n    placement: [{\n      type: Input\n    }],\n    triggers: [{\n      type: Input\n    }],\n    container: [{\n      type: Input\n    }],\n    dropup: [{\n      type: Input\n    }],\n    autoClose: [{\n      type: Input\n    }],\n    isAnimated: [{\n      type: Input\n    }],\n    insideClick: [{\n      type: Input\n    }],\n    isDisabled: [{\n      type: Input\n    }],\n    isOpen: [{\n      type: Input\n    }],\n    isOpenChange: [{\n      type: Output\n    }],\n    onShown: [{\n      type: Output\n    }],\n    onHidden: [{\n      type: Output\n    }],\n    navigationClick: [{\n      type: HostListener,\n      args: ['keydown.arrowDown', ['$event']]\n    }, {\n      type: HostListener,\n      args: ['keydown.arrowUp', ['$event']]\n    }]\n  });\n})();\nclass BsDropdownMenuDirective {\n  constructor(_state, _viewContainer, _templateRef) {\n    _state.resolveDropdownMenu({\n      templateRef: _templateRef,\n      viewContainer: _viewContainer\n    });\n  }\n  static {\n    this.ɵfac = function BsDropdownMenuDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BsDropdownMenuDirective)(i0.ɵɵdirectiveInject(BsDropdownState), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: BsDropdownMenuDirective,\n      selectors: [[\"\", \"bsDropdownMenu\", \"\"], [\"\", \"dropdownMenu\", \"\"]],\n      exportAs: [\"bs-dropdown-menu\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownMenuDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[bsDropdownMenu],[dropdownMenu]',\n      exportAs: 'bs-dropdown-menu',\n      standalone: true\n    }]\n  }], () => [{\n    type: BsDropdownState\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.TemplateRef\n  }], null);\n})();\nclass BsDropdownToggleDirective {\n  constructor(_changeDetectorRef, _dropdown, _element, _renderer, _state) {\n    this._changeDetectorRef = _changeDetectorRef;\n    this._dropdown = _dropdown;\n    this._element = _element;\n    this._renderer = _renderer;\n    this._state = _state;\n    this.isOpen = false;\n    this._subscriptions = [];\n    // sync is open value with state\n    this._subscriptions.push(this._state.isOpenChange.subscribe(value => {\n      this.isOpen = value;\n      if (value) {\n        this._documentClickListener = this._renderer.listen('document', 'click', event => {\n          if (this._state.autoClose && event.button !== 2 && !this._element.nativeElement.contains(event.target) && !(this._state.insideClick && this._dropdown._contains(event))) {\n            this._state.toggleClick.emit(false);\n            this._changeDetectorRef.detectChanges();\n          }\n        });\n        this._escKeyUpListener = this._renderer.listen(this._element.nativeElement, 'keyup.esc', () => {\n          if (this._state.autoClose) {\n            this._state.toggleClick.emit(false);\n            this._changeDetectorRef.detectChanges();\n          }\n        });\n      } else {\n        this._documentClickListener && this._documentClickListener();\n        this._escKeyUpListener && this._escKeyUpListener();\n      }\n    }));\n    // populate disabled state\n    this._subscriptions.push(this._state.isDisabledChange.subscribe(value => this.isDisabled = value || void 0));\n  }\n  onClick(event) {\n    if (this._state.stopOnClickPropagation) {\n      event.stopPropagation();\n    }\n    if (this.isDisabled) {\n      return;\n    }\n    this._state.toggleClick.emit(true);\n  }\n  ngOnDestroy() {\n    if (this._documentClickListener) {\n      this._documentClickListener();\n    }\n    if (this._escKeyUpListener) {\n      this._escKeyUpListener();\n    }\n    for (const sub of this._subscriptions) {\n      sub.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function BsDropdownToggleDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BsDropdownToggleDirective)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(BsDropdownDirective), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(BsDropdownState));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: BsDropdownToggleDirective,\n      selectors: [[\"\", \"bsDropdownToggle\", \"\"], [\"\", \"dropdownToggle\", \"\"]],\n      hostVars: 3,\n      hostBindings: function BsDropdownToggleDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function BsDropdownToggleDirective_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-haspopup\", true)(\"disabled\", ctx.isDisabled)(\"aria-expanded\", ctx.isOpen);\n        }\n      },\n      exportAs: [\"bs-dropdown-toggle\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownToggleDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[bsDropdownToggle],[dropdownToggle]',\n      exportAs: 'bs-dropdown-toggle',\n      // eslint-disable-next-line @angular-eslint/no-host-metadata-property\n      host: {\n        '[attr.aria-haspopup]': 'true'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: BsDropdownDirective\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: BsDropdownState\n  }], {\n    isDisabled: [{\n      type: HostBinding,\n      args: ['attr.disabled']\n    }],\n    isOpen: [{\n      type: HostBinding,\n      args: ['attr.aria-expanded']\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }]\n  });\n})();\nclass BsDropdownModule {\n  // @deprecated method not required anymore, will be deleted in v19.0.0\n  static forRoot() {\n    return {\n      ngModule: BsDropdownModule,\n      providers: []\n    };\n  }\n  static {\n    this.ɵfac = function BsDropdownModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BsDropdownModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: BsDropdownModule,\n      imports: [BsDropdownDirective, BsDropdownContainerComponent, BsDropdownMenuDirective, BsDropdownToggleDirective],\n      exports: [BsDropdownMenuDirective, BsDropdownToggleDirective, BsDropdownDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BsDropdownDirective, BsDropdownContainerComponent, BsDropdownMenuDirective, BsDropdownToggleDirective],\n      exports: [BsDropdownMenuDirective, BsDropdownToggleDirective, BsDropdownDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BsDropdownConfig, BsDropdownContainerComponent, BsDropdownDirective, BsDropdownMenuDirective, BsDropdownModule, BsDropdownState, BsDropdownToggleDirective };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAKA;AAGA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,SAAO;AAAA,EACjB,UAAU;AACZ;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,cAAc;AAEZ,SAAK,YAAY;AAEjB,SAAK,cAAc;AAEnB,SAAK,aAAa;AAElB,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AACZ,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,yBAAyB;AAC9B,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,mBAAmB,IAAI,aAAa;AACzC,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,SAAS;AACd,SAAK,eAAe,IAAI,QAAQ,aAAW;AACzC,WAAK,sBAAsB;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,MACzB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,4BAA4B;AAClC,IAAM,oBAAoB,CAAC,MAAM;AAAA,EAC/B,QAAQ;AAAA,EACR,UAAU;AACZ,CAAC,GAAG,QAAQ,2BAA2B,MAAM;AAAA,EAC3C,QAAQ;AAAA,EACR,UAAU;AACZ,CAAC,CAAC,CAAC;AAIH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,IAAI,YAAY;AACd,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,YAAY,QAAQ,IAAI,WAAW,UAAU,UAAU;AACrD,SAAK,SAAS;AACd,SAAK,KAAK;AACV,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,4BAA4B,SAAS,MAAM,iBAAiB;AACjE,SAAK,gBAAgB,OAAO,aAAa,UAAU,WAAS;AAC1D,WAAK,SAAS;AACd,YAAM,WAAW,KAAK,SAAS,cAAc,cAAc,gBAAgB;AAC3E,WAAK,UAAU,SAAS,KAAK,SAAS,cAAc,cAAc,KAAK,GAAG,MAAM;AAChF,UAAI,UAAU;AACZ,aAAK,UAAU,SAAS,UAAU,MAAM;AACxC,YAAI,SAAS,UAAU,SAAS,qBAAqB,KAAK,SAAS,UAAU,SAAS,mBAAmB,GAAG;AAC1G,eAAK,UAAU,SAAS,UAAU,QAAQ,MAAM;AAChD,eAAK,UAAU,SAAS,UAAU,SAAS,GAAG;AAAA,QAChD;AACA,YAAI,KAAK,cAAc,MAAM;AAC3B,eAAK,UAAU,SAAS,UAAU,OAAO,MAAM;AAC/C,eAAK,UAAU,SAAS,UAAU,aAAa,mBAAmB;AAAA,QACpE;AAAA,MACF;AACA,UAAI,YAAY,KAAK,OAAO,YAAY;AACtC,aAAK,0BAA0B,OAAO,QAAQ,EAAE,KAAK;AAAA,MACvD;AACA,WAAK,GAAG,aAAa;AACrB,WAAK,GAAG,cAAc;AAAA,IACxB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,UAAU,IAAI;AACZ,WAAO,KAAK,SAAS,cAAc,SAAS,EAAE;AAAA,EAChD;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,YAAY;AAAA,EACjC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,mBAAmB;AAC3E,aAAO,KAAK,qBAAqB,+BAAiC,kBAAkB,eAAe,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,IACtQ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,MACrC,WAAW,CAAC,GAAG,WAAW,SAAS,YAAY,YAAY,WAAW,MAAM;AAAA,MAC5E,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC;AAAA,MACvB,UAAU,SAAS,sCAAsC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,UAAU,IAAI,cAAc,IAAI,EAAE,QAAQ,IAAI,MAAM,EAAE,QAAQ,IAAI,MAAM;AACvF,UAAG,WAAW,WAAc,gBAAgB,GAAG,KAAK,IAAI,cAAc,MAAM,CAAC;AAAA,QAC/E;AAAA,MACF;AAAA,MACA,cAAc,CAAC,OAAO;AAAA,MACtB,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,SAAS,CAAC,OAAO;AAAA;AAAA,MAEjB,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,IAAI,UAAU,OAAO;AACnB,SAAK,OAAO,YAAY;AAAA,EAC1B;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,WAAW,OAAO;AACpB,SAAK,OAAO,aAAa;AAAA,EAC3B;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,YAAY,OAAO;AACrB,SAAK,OAAO,cAAc;AAAA,EAC5B;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,WAAW,OAAO;AACpB,SAAK,cAAc;AACnB,SAAK,OAAO,iBAAiB,KAAK,KAAK;AACvC,QAAI,OAAO;AACT,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AACX,QAAI,KAAK,aAAa;AACpB,aAAO,KAAK;AAAA,IACd;AACA,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,QAAI,OAAO;AACT,WAAK,KAAK;AAAA,IACZ,OAAO;AACL,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,CAAC,KAAK;AAAA,EACf;AAAA,EACA,YAAY,aAAa,WAAW,mBAAmB,MAAM,QAAQ,SAAS,UAAU;AACtF,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,oBAAoB;AACzB,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,UAAU;AAIf,SAAK,SAAS;AAEd,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,iBAAiB,CAAC;AACvB,SAAK,YAAY;AAEjB,SAAK,OAAO,YAAY,KAAK,QAAQ;AACrC,SAAK,OAAO,cAAc,KAAK,QAAQ;AACvC,SAAK,OAAO,aAAa,KAAK,QAAQ;AACtC,SAAK,OAAO,yBAAyB,KAAK,QAAQ;AAClD,SAAK,4BAA4B,SAAS,MAAM,iBAAiB;AAEjE,SAAK,YAAY,KAAK,KAAK,aAAa,KAAK,aAAa,KAAK,mBAAmB,KAAK,SAAS,EAAE,QAAQ;AAAA,MACxG,SAAS;AAAA,MACT,UAAU,KAAK;AAAA,IACjB,CAAC;AACD,SAAK,UAAU,KAAK,UAAU;AAC9B,SAAK,WAAW,KAAK,UAAU;AAC/B,SAAK,eAAe,KAAK,OAAO;AAAA,EAClC;AAAA,EACA,WAAW;AAIT,QAAI,KAAK,WAAW;AAClB;AAAA,IACF;AACA,SAAK,YAAY;AAEjB,SAAK,UAAU,OAAO;AAAA;AAAA,MAEpB,cAAc;AAAA,MACd,UAAU,KAAK;AAAA,MACf,MAAM,MAAM,KAAK,KAAK;AAAA,IACxB,CAAC;AAED,SAAK,eAAe,KAAK,KAAK,OAAO,YAAY,UAAU,WAAS,KAAK,OAAO,KAAK,CAAC,CAAC;AAEvF,SAAK,eAAe,KAAK,KAAK,OAAO,iBAAiB,KAAK,OAAO,WAAS,KAAK,CAAC,EAAE,UAAU,MAAwB,KAAK,KAAK,CAAC,CAAC;AAAA,EACnI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,QAAI,KAAK,UAAU,KAAK,YAAY;AAClC;AAAA,IACF;AACA,QAAI,KAAK,aAAa;AACpB,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,OAAO,aAAa,KAAK,kBAAgB;AAC5C,eAAK,UAAU,aAAa,aAAa,eAAe,aAAa,WAAW;AAChF,eAAK,eAAe,KAAK,UAAU;AACnC,eAAK,gBAAgB;AACrB,cAAI,KAAK,cAAc;AACrB,iBAAK,UAAU,SAAS,KAAK,aAAa,UAAU,CAAC,EAAE,YAAY,MAAM;AAAA,UAC3E;AACA,eAAK,cAAc;AAAA,QACrB,CAAC,EAEA,MAAM;AAAA,MACT;AACA,WAAK,gBAAgB;AACrB,WAAK,gBAAgB;AACrB,WAAK,QAAQ,KAAK,IAAI;AACtB,WAAK,OAAO,aAAa,KAAK,IAAI;AAClC,WAAK,cAAc;AACnB;AAAA,IACF;AACA,SAAK,OAAO,aAAa,KAAK,kBAAgB;AAE5C,YAAM,UAAU,KAAK,UAAU,OAAO,KAAK,WAAW,eAAe,KAAK;AAC1E,WAAK,OAAO,YAAY,UAAU,OAAO;AACzC,YAAM,aAAa,KAAK,cAAc,UAAU,cAAc;AAE9D,WAAK,UAAU,OAAO,4BAA4B,EAAE,GAAG,KAAK,SAAS,EAAE,SAAS;AAAA,QAC9E,YAAY;AAAA,MACd,CAAC,EAAE,KAAK;AAAA,QACN,SAAS,aAAa;AAAA,QACtB,WAAW;AAAA,MACb,CAAC;AACD,WAAK,OAAO,aAAa,KAAK,IAAI;AAAA,IACpC,CAAC,EAEA,MAAM;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,QAAI,CAAC,KAAK,QAAQ;AAChB;AAAA,IACF;AACA,QAAI,KAAK,aAAa;AACpB,WAAK,gBAAgB;AACrB,WAAK,mBAAmB;AACxB,WAAK,gBAAgB;AACrB,WAAK,SAAS,KAAK,IAAI;AAAA,IACzB,OAAO;AACL,WAAK,UAAU,KAAK;AAAA,IACtB;AACA,SAAK,OAAO,aAAa,KAAK,KAAK;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAO;AACZ,QAAI,KAAK,UAAU,CAAC,OAAO;AACzB,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA;AAAA,EAEA,UAAU,OAAO;AAEf,WAAO,KAAK,YAAY,cAAc,SAAS,MAAM,MAAM,KAAK,KAAK,UAAU,YAAY,KAAK,UAAU,SAAS,UAAU,MAAM,MAAM;AAAA,EAC3I;AAAA,EACA,gBAAgB,OAAO;AACrB,UAAM,MAAM,KAAK,YAAY,cAAc,cAAc,gBAAgB;AACzE,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,cAAc,KAAK,YAAY,cAAc,cAAc;AACjE,UAAM,SAAS,IAAI,iBAAiB,gBAAgB;AACpD,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AACH,YAAI,KAAK,OAAO,SAAS,GAAG;AAC1B,iBAAO,EAAE,KAAK,OAAO,MAAM,EAAE,MAAM;AAAA,QACrC;AACA;AAAA,MACF,KAAK;AACH,YAAI,KAAK,OAAO,SAAS,IAAI,OAAO,QAAQ;AAC1C,cAAI,YAAY,cAAc,OAAO,KAAK,OAAO,MAAM,EAAE,WAAW;AAClE,mBAAO,KAAK,OAAO,MAAM,EAAE,MAAM;AAAA,UACnC,OAAO;AACL,mBAAO,EAAE,KAAK,OAAO,MAAM,EAAE,MAAM;AAAA,UACrC;AAAA,QACF;AACA;AAAA,MACF;AAAA,IACF;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,cAAc;AAEZ,eAAW,OAAO,KAAK,gBAAgB;AACrC,UAAI,YAAY;AAAA,IAClB;AACA,SAAK,UAAU,QAAQ;AAAA,EACzB;AAAA,EACA,kBAAkB;AAChB,SAAK,aAAa;AAClB,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,gBAAgB;AACd,QAAI,KAAK,OAAO,cAAc,KAAK,cAAc;AAC/C,iBAAW,MAAM;AACf,YAAI,KAAK,cAAc;AACrB,eAAK,0BAA0B,OAAO,KAAK,aAAa,UAAU,CAAC,CAAC,EAAE,KAAK;AAAA,QAC7E;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,eAAe;AACb,QAAI,KAAK,gBAAgB,KAAK,aAAa,UAAU,CAAC,GAAG;AACvD,WAAK,UAAU,SAAS,KAAK,aAAa,UAAU,CAAC,GAAG,MAAM;AAAA,IAChE;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,gBAAgB,KAAK,aAAa,UAAU,CAAC,GAAG;AACvD,WAAK,UAAU,YAAY,KAAK,aAAa,UAAU,CAAC,GAAG,MAAM;AAAA,IACnE;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,gBAAgB,KAAK,aAAa,UAAU,CAAC,GAAG;AACvD,YAAM,iBAAiB,KAAK,aAAa,UAAU,CAAC,EAAE,UAAU,SAAS,qBAAqB,KAAK,KAAK,aAAa,UAAU,CAAC,EAAE,UAAU,SAAS,mBAAmB;AACxK,WAAK,UAAU,SAAS,KAAK,aAAa,UAAU,CAAC,GAAG,QAAQ,iBAAiB,SAAS,GAAG;AAC7F,WAAK,UAAU,SAAS,KAAK,aAAa,UAAU,CAAC,GAAG,SAAS,iBAAiB,MAAM,MAAM;AAAA,IAChG;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,gBAAgB,KAAK,aAAa,UAAU,CAAC,GAAG;AAEvD,WAAK,UAAU,SAAS,KAAK,aAAa,UAAU,CAAC,GAAG,OAAO,KAAK,SAAS,SAAS,MAAM;AAC5F,WAAK,UAAU,SAAS,KAAK,aAAa,UAAU,CAAC,GAAG,aAAa,KAAK,SAAS,sBAAsB,eAAe;AACxH,WAAK,UAAU,SAAS,KAAK,aAAa,UAAU,CAAC,GAAG,UAAU,MAAM;AAAA,IAC1E;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,gBAAgB,KAAK,aAAa,UAAU,CAAC,GAAG;AACvD,WAAK,UAAU,YAAY,KAAK,aAAa,UAAU,CAAC,GAAG,KAAK;AAChE,WAAK,UAAU,YAAY,KAAK,aAAa,UAAU,CAAC,GAAG,WAAW;AACtE,WAAK,UAAU,YAAY,KAAK,aAAa,UAAU,CAAC,GAAG,QAAQ;AAAA,IACrE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAwB,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,sBAAsB,GAAM,kBAAkB,eAAe,GAAM,kBAAkB,gBAAgB,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,IACrV;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,GAAG,CAAC,IAAI,YAAY,EAAE,CAAC;AAAA,MACxD,UAAU;AAAA,MACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,qBAAqB,SAAS,yDAAyD,QAAQ;AAC3G,mBAAO,IAAI,gBAAgB,MAAM;AAAA,UACnC,CAAC,EAAE,mBAAmB,SAAS,uDAAuD,QAAQ;AAC5F,mBAAO,IAAI,gBAAgB,MAAM;AAAA,UACnC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,UAAU,IAAI,MAAM,EAAE,QAAQ,IAAI,MAAM,EAAE,QAAQ,IAAI,MAAM;AAAA,QAC7E;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,MACV;AAAA,MACA,SAAS;AAAA,QACP,cAAc;AAAA,QACd,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,MACxB,UAAU,CAAI,mBAAmB,CAAC,iBAAiB,wBAAwB,gBAAgB,CAAC,CAAC;AAAA,IAC/F,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC,iBAAiB,wBAAwB,gBAAgB;AAAA,MACrE,YAAY;AAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB,CAAC,QAAQ,CAAC;AAAA,IACxC,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC;AAAA,IACtC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,YAAY,QAAQ,gBAAgB,cAAc;AAChD,WAAO,oBAAoB;AAAA,MACzB,aAAa;AAAA,MACb,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAA4B,kBAAkB,eAAe,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,WAAW,CAAC;AAAA,IAClL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,kBAAkB,EAAE,GAAG,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,MAChE,UAAU,CAAC,kBAAkB;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,YAAY,oBAAoB,WAAW,UAAU,WAAW,QAAQ;AACtE,SAAK,qBAAqB;AAC1B,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,iBAAiB,CAAC;AAEvB,SAAK,eAAe,KAAK,KAAK,OAAO,aAAa,UAAU,WAAS;AACnE,WAAK,SAAS;AACd,UAAI,OAAO;AACT,aAAK,yBAAyB,KAAK,UAAU,OAAO,YAAY,SAAS,WAAS;AAChF,cAAI,KAAK,OAAO,aAAa,MAAM,WAAW,KAAK,CAAC,KAAK,SAAS,cAAc,SAAS,MAAM,MAAM,KAAK,EAAE,KAAK,OAAO,eAAe,KAAK,UAAU,UAAU,KAAK,IAAI;AACvK,iBAAK,OAAO,YAAY,KAAK,KAAK;AAClC,iBAAK,mBAAmB,cAAc;AAAA,UACxC;AAAA,QACF,CAAC;AACD,aAAK,oBAAoB,KAAK,UAAU,OAAO,KAAK,SAAS,eAAe,aAAa,MAAM;AAC7F,cAAI,KAAK,OAAO,WAAW;AACzB,iBAAK,OAAO,YAAY,KAAK,KAAK;AAClC,iBAAK,mBAAmB,cAAc;AAAA,UACxC;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,aAAK,0BAA0B,KAAK,uBAAuB;AAC3D,aAAK,qBAAqB,KAAK,kBAAkB;AAAA,MACnD;AAAA,IACF,CAAC,CAAC;AAEF,SAAK,eAAe,KAAK,KAAK,OAAO,iBAAiB,UAAU,WAAS,KAAK,aAAa,SAAS,MAAM,CAAC;AAAA,EAC7G;AAAA,EACA,QAAQ,OAAO;AACb,QAAI,KAAK,OAAO,wBAAwB;AACtC,YAAM,gBAAgB;AAAA,IACxB;AACA,QAAI,KAAK,YAAY;AACnB;AAAA,IACF;AACA,SAAK,OAAO,YAAY,KAAK,IAAI;AAAA,EACnC;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,wBAAwB;AAC/B,WAAK,uBAAuB;AAAA,IAC9B;AACA,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB;AAAA,IACzB;AACA,eAAW,OAAO,KAAK,gBAAgB;AACrC,UAAI,YAAY;AAAA,IAClB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,aAAO,KAAK,qBAAqB,4BAA8B,kBAAqB,iBAAiB,GAAM,kBAAkB,mBAAmB,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAkB,eAAe,CAAC;AAAA,IACnQ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,GAAG,CAAC,IAAI,kBAAkB,EAAE,CAAC;AAAA,MACpE,UAAU;AAAA,MACV,cAAc,SAAS,uCAAuC,IAAI,KAAK;AACrE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,mDAAmD,QAAQ;AACzF,mBAAO,IAAI,QAAQ,MAAM;AAAA,UAC3B,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,iBAAiB,IAAI,EAAE,YAAY,IAAI,UAAU,EAAE,iBAAiB,IAAI,MAAM;AAAA,QAC/F;AAAA,MACF;AAAA,MACA,UAAU,CAAC,oBAAoB;AAAA,IACjC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA,MAEV,MAAM;AAAA,QACJ,wBAAwB;AAAA,MAC1B;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA;AAAA,EAErB,OAAO,UAAU;AACf,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,IACd;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,qBAAqB,8BAA8B,yBAAyB,yBAAyB;AAAA,MAC/G,SAAS,CAAC,yBAAyB,2BAA2B,mBAAmB;AAAA,IACnF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,qBAAqB,8BAA8B,yBAAyB,yBAAyB;AAAA,MAC/G,SAAS,CAAC,yBAAyB,2BAA2B,mBAAmB;AAAA,IACnF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}
{"version": 3, "sources": ["../../../../../../node_modules/ngx-bootstrap/tooltip/fesm2022/ngx-bootstrap-tooltip.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Component, ChangeDetectionStrategy, EventEmitter, Directive, Input, Output, NgModule } from '@angular/core';\nimport { getBsVer, warnOnce, parseTriggers, OnChange } from 'ngx-bootstrap/utils';\nimport * as i3 from 'ngx-bootstrap/positioning';\nimport { PlacementForBs5, PositioningService } from 'ngx-bootstrap/positioning';\nimport { __decorate, __metadata } from 'tslib';\nimport * as i1 from 'ngx-bootstrap/component-loader';\nimport { ComponentLoaderFactory } from 'ngx-bootstrap/component-loader';\nimport { timer } from 'rxjs';\nimport { CommonModule } from '@angular/common';\n\n/** Default values provider for tooltip */\nconst _c0 = [\"*\"];\nclass TooltipConfig {\n  constructor() {\n    /** sets disable adaptive position */\n    this.adaptivePosition = true;\n    /** tooltip placement, supported positions: 'top', 'bottom', 'left', 'right' */\n    this.placement = 'top';\n    /** array of event names which triggers tooltip opening */\n    this.triggers = 'hover focus';\n    /** delay before showing the tooltip */\n    this.delay = 0;\n  }\n  static {\n    this.ɵfac = function TooltipConfig_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TooltipConfig)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TooltipConfig,\n      factory: TooltipConfig.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipConfig, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass TooltipContainerComponent {\n  get _bsVersions() {\n    return getBsVer();\n  }\n  constructor(config) {\n    Object.assign(this, config);\n  }\n  ngAfterViewInit() {\n    this.classMap = {\n      in: false,\n      fade: false\n    };\n    if (this.placement) {\n      if (this._bsVersions.isBs5) {\n        this.placement = PlacementForBs5[this.placement];\n      }\n      this.classMap[this.placement] = true;\n    }\n    this.classMap[`tooltip-${this.placement}`] = true;\n    this.classMap[\"in\"] = true;\n    if (this.animation) {\n      this.classMap[\"fade\"] = true;\n    }\n    if (this.containerClass) {\n      this.classMap[this.containerClass] = true;\n    }\n  }\n  static {\n    this.ɵfac = function TooltipContainerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TooltipContainerComponent)(i0.ɵɵdirectiveInject(TooltipConfig));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TooltipContainerComponent,\n      selectors: [[\"bs-tooltip-container\"]],\n      hostAttrs: [\"role\", \"tooltip\"],\n      hostVars: 3,\n      hostBindings: function TooltipContainerComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.id);\n          i0.ɵɵclassMap(\"show tooltip in tooltip-\" + ctx.placement + \" \" + \"bs-tooltip-\" + ctx.placement + \" \" + ctx.placement + \" \" + ctx.containerClass);\n        }\n      },\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 0,\n      consts: [[1, \"tooltip-arrow\", \"arrow\"], [1, \"tooltip-inner\"]],\n      template: function TooltipContainerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵdomElement(0, \"div\", 0);\n          i0.ɵɵdomElementStart(1, \"div\", 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵdomElementEnd();\n        }\n      },\n      styles: [\".tooltip[_nghost-%COMP%]{display:block;pointer-events:none;position:absolute}.tooltip[_nghost-%COMP%]   .tooltip-arrow[_ngcontent-%COMP%]{position:absolute}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipContainerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'bs-tooltip-container',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[class]': '\"show tooltip in tooltip-\" + placement + \" \" + \"bs-tooltip-\" + placement + \" \" + placement + \" \" + containerClass',\n        '[attr.id]': 'this.id',\n        role: 'tooltip'\n      },\n      template: `\n    <div class=\"tooltip-arrow arrow\"></div>\n    <div class=\"tooltip-inner\"><ng-content></ng-content></div>\n    `,\n      standalone: true,\n      styles: [\":host.tooltip{display:block;pointer-events:none;position:absolute}:host.tooltip .tooltip-arrow{position:absolute}\\n\"]\n    }]\n  }], () => [{\n    type: TooltipConfig\n  }], null);\n})();\nlet id = 0;\nclass TooltipDirective {\n  /**\n   * Returns whether or not the tooltip is currently being shown\n   */\n  get isOpen() {\n    return this._tooltip.isShown;\n  }\n  set isOpen(value) {\n    if (value) {\n      this.show();\n    } else {\n      this.hide();\n    }\n  }\n  /** @deprecated - please use `tooltip` instead */\n  set htmlContent(value) {\n    warnOnce('tooltipHtml was deprecated, please use `tooltip` instead');\n    this.tooltip = value;\n  }\n  /** @deprecated - please use `placement` instead */\n  // eslint-disable-next-line @angular-eslint/no-input-rename\n  set _placement(value) {\n    warnOnce('tooltipPlacement was deprecated, please use `placement` instead');\n    this.placement = value;\n  }\n  /** @deprecated - please use `isOpen` instead */\n  // eslint-disable-next-line @angular-eslint/no-input-rename\n  set _isOpen(value) {\n    warnOnce('tooltipIsOpen was deprecated, please use `isOpen` instead');\n    this.isOpen = value;\n  }\n  get _isOpen() {\n    warnOnce('tooltipIsOpen was deprecated, please use `isOpen` instead');\n    return this.isOpen;\n  }\n  /** @deprecated - please use `isDisabled` instead */\n  // eslint-disable-next-line @angular-eslint/no-input-rename\n  set _enable(value) {\n    warnOnce('tooltipEnable was deprecated, please use `isDisabled` instead');\n    this.isDisabled = !value;\n  }\n  get _enable() {\n    warnOnce('tooltipEnable was deprecated, please use `isDisabled` instead');\n    return this.isDisabled;\n  }\n  /** @deprecated - please use `container=\"body\"` instead */\n  // eslint-disable-next-line @angular-eslint/no-input-rename\n  set _appendToBody(value) {\n    warnOnce('tooltipAppendToBody was deprecated, please use `container=\"body\"` instead');\n    this.container = value ? 'body' : this.container;\n  }\n  get _appendToBody() {\n    warnOnce('tooltipAppendToBody was deprecated, please use `container=\"body\"` instead');\n    return this.container === 'body';\n  }\n  /** @deprecated - will replaced with customClass */\n  // eslint-disable-next-line @angular-eslint/no-input-rename\n  set _popupClass(value) {\n    warnOnce('tooltipClass deprecated');\n  }\n  /** @deprecated - removed */\n  // eslint-disable-next-line @angular-eslint/no-input-rename\n  set _tooltipContext(value) {\n    warnOnce('tooltipContext deprecated');\n  }\n  /** @deprecated */\n  // eslint-disable-next-line @angular-eslint/no-input-rename\n  set _tooltipPopupDelay(value) {\n    warnOnce('tooltipPopupDelay is deprecated, use `delay` instead');\n    this.delay = value;\n  }\n  /** @deprecated -  please use `triggers` instead */\n  get _tooltipTrigger() {\n    warnOnce('tooltipTrigger was deprecated, please use `triggers` instead');\n    return this.triggers;\n  }\n  set _tooltipTrigger(value) {\n    warnOnce('tooltipTrigger was deprecated, please use `triggers` instead');\n    this.triggers = (value || '').toString();\n  }\n  constructor(_viewContainerRef, cis, config, _elementRef, _renderer, _positionService) {\n    this._elementRef = _elementRef;\n    this._renderer = _renderer;\n    this._positionService = _positionService;\n    this.tooltipId = id++;\n    /** sets disable adaptive position */\n    this.adaptivePosition = true;\n    /** Fired when tooltip content changes */\n    this.tooltipChange = new EventEmitter();\n    /**\n     * Placement of a tooltip. Accepts: \"top\", \"bottom\", \"left\", \"right\"\n     */\n    this.placement = 'top';\n    /**\n     * Specifies events that should trigger. Supports a space separated list of\n     * event names.\n     */\n    this.triggers = 'hover focus';\n    /**\n     * Css class for tooltip container\n     */\n    this.containerClass = '';\n    /**\n     * Allows to disable tooltip\n     */\n    this.isDisabled = false;\n    /**\n     * Delay before showing the tooltip\n     */\n    this.delay = 0;\n    /** @deprecated - removed, will be added to configuration */\n    this.tooltipAnimation = true;\n    /** @deprecated */\n    this.tooltipFadeDuration = 150;\n    /** @deprecated */\n    this.tooltipStateChanged = new EventEmitter();\n    this._tooltip = cis.createLoader(this._elementRef, _viewContainerRef, this._renderer).provide({\n      provide: TooltipConfig,\n      useValue: config\n    });\n    Object.assign(this, config);\n    this.onShown = this._tooltip.onShown;\n    this.onHidden = this._tooltip.onHidden;\n  }\n  ngOnInit() {\n    this._tooltip.listen({\n      triggers: this.triggers,\n      show: () => this.show()\n    });\n    this.tooltipChange.subscribe(value => {\n      if (!value) {\n        this._tooltip.hide();\n      }\n    });\n    this.onShown.subscribe(() => {\n      this.setAriaDescribedBy();\n    });\n    this.onHidden.subscribe(() => {\n      this.setAriaDescribedBy();\n    });\n  }\n  setAriaDescribedBy() {\n    this._ariaDescribedby = this.isOpen ? `tooltip-${this.tooltipId}` : void 0;\n    if (this._ariaDescribedby) {\n      this._renderer.setAttribute(this._elementRef.nativeElement, 'aria-describedby', this._ariaDescribedby);\n    } else {\n      this._renderer.removeAttribute(this._elementRef.nativeElement, 'aria-describedby');\n    }\n  }\n  /**\n   * Toggles an element’s tooltip. This is considered a “manual” triggering of\n   * the tooltip.\n   */\n  toggle() {\n    if (this.isOpen) {\n      return this.hide();\n    }\n    this.show();\n  }\n  /**\n   * Opens an element’s tooltip. This is considered a “manual” triggering of\n   * the tooltip.\n   */\n  show() {\n    this._positionService.setOptions({\n      modifiers: {\n        flip: {\n          enabled: this.adaptivePosition\n        },\n        preventOverflow: {\n          enabled: this.adaptivePosition,\n          boundariesElement: this.boundariesElement || 'scrollParent'\n        }\n      }\n    });\n    if (this.isOpen || this.isDisabled || this._delayTimeoutId || !this.tooltip) {\n      return;\n    }\n    const showTooltip = () => {\n      if (this._delayTimeoutId) {\n        this._delayTimeoutId = undefined;\n      }\n      this._tooltip.attach(TooltipContainerComponent).to(this.container).position({\n        attachment: this.placement\n      }).show({\n        content: this.tooltip,\n        placement: this.placement,\n        containerClass: this.containerClass,\n        id: `tooltip-${this.tooltipId}`\n      });\n    };\n    const cancelDelayedTooltipShowing = () => {\n      if (this._tooltipCancelShowFn) {\n        this._tooltipCancelShowFn();\n      }\n    };\n    if (this.delay) {\n      if (this._delaySubscription) {\n        this._delaySubscription.unsubscribe();\n      }\n      this._delaySubscription = timer(this.delay).subscribe(() => {\n        showTooltip();\n        cancelDelayedTooltipShowing();\n      });\n      if (this.triggers) {\n        parseTriggers(this.triggers).forEach(trigger => {\n          if (!trigger.close) {\n            return;\n          }\n          this._tooltipCancelShowFn = this._renderer.listen(this._elementRef.nativeElement, trigger.close, () => {\n            this._delaySubscription?.unsubscribe();\n            cancelDelayedTooltipShowing();\n          });\n        });\n      }\n    } else {\n      showTooltip();\n    }\n  }\n  /**\n   * Closes an element’s tooltip. This is considered a “manual” triggering of\n   * the tooltip.\n   */\n  hide() {\n    if (this._delayTimeoutId) {\n      clearTimeout(this._delayTimeoutId);\n      this._delayTimeoutId = undefined;\n    }\n    if (!this._tooltip.isShown) {\n      return;\n    }\n    if (this._tooltip.instance?.classMap) {\n      this._tooltip.instance.classMap['in'] = false;\n    }\n    setTimeout(() => {\n      this._tooltip.hide();\n    }, this.tooltipFadeDuration);\n  }\n  ngOnDestroy() {\n    this._tooltip.dispose();\n    this.tooltipChange.unsubscribe();\n    if (this._delaySubscription) {\n      this._delaySubscription.unsubscribe();\n    }\n    this.onShown.unsubscribe();\n    this.onHidden.unsubscribe();\n  }\n  static {\n    this.ɵfac = function TooltipDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TooltipDirective)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i1.ComponentLoaderFactory), i0.ɵɵdirectiveInject(TooltipConfig), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i3.PositioningService));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TooltipDirective,\n      selectors: [[\"\", \"tooltip\", \"\"], [\"\", \"tooltipHtml\", \"\"]],\n      inputs: {\n        adaptivePosition: \"adaptivePosition\",\n        tooltip: \"tooltip\",\n        placement: \"placement\",\n        triggers: \"triggers\",\n        container: \"container\",\n        containerClass: \"containerClass\",\n        boundariesElement: \"boundariesElement\",\n        isOpen: \"isOpen\",\n        isDisabled: \"isDisabled\",\n        delay: \"delay\",\n        htmlContent: [0, \"tooltipHtml\", \"htmlContent\"],\n        _placement: [0, \"tooltipPlacement\", \"_placement\"],\n        _isOpen: [0, \"tooltipIsOpen\", \"_isOpen\"],\n        _enable: [0, \"tooltipEnable\", \"_enable\"],\n        _appendToBody: [0, \"tooltipAppendToBody\", \"_appendToBody\"],\n        tooltipAnimation: \"tooltipAnimation\",\n        _popupClass: [0, \"tooltipClass\", \"_popupClass\"],\n        _tooltipContext: [0, \"tooltipContext\", \"_tooltipContext\"],\n        _tooltipPopupDelay: [0, \"tooltipPopupDelay\", \"_tooltipPopupDelay\"],\n        tooltipFadeDuration: \"tooltipFadeDuration\",\n        _tooltipTrigger: [0, \"tooltipTrigger\", \"_tooltipTrigger\"]\n      },\n      outputs: {\n        tooltipChange: \"tooltipChange\",\n        onShown: \"onShown\",\n        onHidden: \"onHidden\",\n        tooltipStateChanged: \"tooltipStateChanged\"\n      },\n      exportAs: [\"bs-tooltip\"],\n      features: [i0.ɵɵProvidersFeature([ComponentLoaderFactory, PositioningService])]\n    });\n  }\n}\n__decorate([OnChange(), __metadata(\"design:type\", Object)], TooltipDirective.prototype, \"tooltip\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[tooltip], [tooltipHtml]',\n      exportAs: 'bs-tooltip',\n      standalone: true,\n      providers: [ComponentLoaderFactory, PositioningService]\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: i1.ComponentLoaderFactory\n  }, {\n    type: TooltipConfig\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i3.PositioningService\n  }], {\n    adaptivePosition: [{\n      type: Input\n    }],\n    tooltip: [{\n      type: Input\n    }],\n    tooltipChange: [{\n      type: Output\n    }],\n    placement: [{\n      type: Input\n    }],\n    triggers: [{\n      type: Input\n    }],\n    container: [{\n      type: Input\n    }],\n    containerClass: [{\n      type: Input\n    }],\n    boundariesElement: [{\n      type: Input\n    }],\n    isOpen: [{\n      type: Input\n    }],\n    isDisabled: [{\n      type: Input\n    }],\n    delay: [{\n      type: Input\n    }],\n    onShown: [{\n      type: Output\n    }],\n    onHidden: [{\n      type: Output\n    }],\n    htmlContent: [{\n      type: Input,\n      args: ['tooltipHtml']\n    }],\n    _placement: [{\n      type: Input,\n      args: ['tooltipPlacement']\n    }],\n    _isOpen: [{\n      type: Input,\n      args: ['tooltipIsOpen']\n    }],\n    _enable: [{\n      type: Input,\n      args: ['tooltipEnable']\n    }],\n    _appendToBody: [{\n      type: Input,\n      args: ['tooltipAppendToBody']\n    }],\n    tooltipAnimation: [{\n      type: Input\n    }],\n    _popupClass: [{\n      type: Input,\n      args: ['tooltipClass']\n    }],\n    _tooltipContext: [{\n      type: Input,\n      args: ['tooltipContext']\n    }],\n    _tooltipPopupDelay: [{\n      type: Input,\n      args: ['tooltipPopupDelay']\n    }],\n    tooltipFadeDuration: [{\n      type: Input\n    }],\n    _tooltipTrigger: [{\n      type: Input,\n      args: ['tooltipTrigger']\n    }],\n    tooltipStateChanged: [{\n      type: Output\n    }]\n  });\n})();\nclass TooltipModule {\n  // @deprecated method not required anymore, will be deleted in v19.0.0\n  static forRoot() {\n    return {\n      ngModule: TooltipModule,\n      providers: []\n    };\n  }\n  static {\n    this.ɵfac = function TooltipModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TooltipModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TooltipModule,\n      imports: [CommonModule, TooltipDirective, TooltipContainerComponent],\n      exports: [TooltipDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, TooltipDirective, TooltipContainerComponent],\n      exports: [TooltipDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TooltipConfig, TooltipContainerComponent, TooltipDirective, TooltipModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAOA;AACA;AAGA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,cAAc;AAEZ,SAAK,mBAAmB;AAExB,SAAK,YAAY;AAEjB,SAAK,WAAW;AAEhB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,eAAc;AAAA,MACvB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,IAAI,cAAc;AAChB,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,YAAY,QAAQ;AAClB,WAAO,OAAO,MAAM,MAAM;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAChB,SAAK,WAAW;AAAA,MACd,IAAI;AAAA,MACJ,MAAM;AAAA,IACR;AACA,QAAI,KAAK,WAAW;AAClB,UAAI,KAAK,YAAY,OAAO;AAC1B,aAAK,YAAY,gBAAgB,KAAK,SAAS;AAAA,MACjD;AACA,WAAK,SAAS,KAAK,SAAS,IAAI;AAAA,IAClC;AACA,SAAK,SAAS,WAAW,KAAK,SAAS,EAAE,IAAI;AAC7C,SAAK,SAAS,IAAI,IAAI;AACtB,QAAI,KAAK,WAAW;AAClB,WAAK,SAAS,MAAM,IAAI;AAAA,IAC1B;AACA,QAAI,KAAK,gBAAgB;AACvB,WAAK,SAAS,KAAK,cAAc,IAAI;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,aAAO,KAAK,qBAAqB,4BAA8B,kBAAkB,aAAa,CAAC;AAAA,IACjG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,MACpC,WAAW,CAAC,QAAQ,SAAS;AAAA,MAC7B,UAAU;AAAA,MACV,cAAc,SAAS,uCAAuC,IAAI,KAAK;AACrE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,MAAM,IAAI,EAAE;AAC3B,UAAG,WAAW,6BAA6B,IAAI,YAAY,iBAAsB,IAAI,YAAY,MAAM,IAAI,YAAY,MAAM,IAAI,cAAc;AAAA,QACjJ;AAAA,MACF;AAAA,MACA,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,iBAAiB,OAAO,GAAG,CAAC,GAAG,eAAe,CAAC;AAAA,MAC5D,UAAU,SAAS,mCAAmC,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,GAAG,OAAO,CAAC;AAC3B,UAAG,kBAAkB,GAAG,OAAO,CAAC;AAChC,UAAG,aAAa,CAAC;AACjB,UAAG,gBAAgB;AAAA,QACrB;AAAA,MACF;AAAA,MACA,QAAQ,CAAC,8JAA8J;AAAA,MACvK,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,WAAW;AAAA,QACX,aAAa;AAAA,QACb,MAAM;AAAA,MACR;AAAA,MACA,UAAU;AAAA;AAAA;AAAA;AAAA,MAIV,YAAY;AAAA,MACZ,QAAQ,CAAC,qHAAqH;AAAA,IAChI,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAI,KAAK;AACT,IAAM,mBAAN,MAAM,kBAAiB;AAAA;AAAA;AAAA;AAAA,EAIrB,IAAI,SAAS;AACX,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,QAAI,OAAO;AACT,WAAK,KAAK;AAAA,IACZ,OAAO;AACL,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,YAAY,OAAO;AACrB,aAAS,0DAA0D;AACnE,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA,EAGA,IAAI,WAAW,OAAO;AACpB,aAAS,iEAAiE;AAC1E,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA,EAGA,IAAI,QAAQ,OAAO;AACjB,aAAS,2DAA2D;AACpE,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,IAAI,UAAU;AACZ,aAAS,2DAA2D;AACpE,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA,EAGA,IAAI,QAAQ,OAAO;AACjB,aAAS,+DAA+D;AACxE,SAAK,aAAa,CAAC;AAAA,EACrB;AAAA,EACA,IAAI,UAAU;AACZ,aAAS,+DAA+D;AACxE,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA,EAGA,IAAI,cAAc,OAAO;AACvB,aAAS,2EAA2E;AACpF,SAAK,YAAY,QAAQ,SAAS,KAAK;AAAA,EACzC;AAAA,EACA,IAAI,gBAAgB;AAClB,aAAS,2EAA2E;AACpF,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA;AAAA;AAAA,EAGA,IAAI,YAAY,OAAO;AACrB,aAAS,yBAAyB;AAAA,EACpC;AAAA;AAAA;AAAA,EAGA,IAAI,gBAAgB,OAAO;AACzB,aAAS,2BAA2B;AAAA,EACtC;AAAA;AAAA;AAAA,EAGA,IAAI,mBAAmB,OAAO;AAC5B,aAAS,sDAAsD;AAC/D,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA,EAEA,IAAI,kBAAkB;AACpB,aAAS,8DAA8D;AACvE,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB,OAAO;AACzB,aAAS,8DAA8D;AACvE,SAAK,YAAY,SAAS,IAAI,SAAS;AAAA,EACzC;AAAA,EACA,YAAY,mBAAmB,KAAK,QAAQ,aAAa,WAAW,kBAAkB;AACpF,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,YAAY;AAEjB,SAAK,mBAAmB;AAExB,SAAK,gBAAgB,IAAI,aAAa;AAItC,SAAK,YAAY;AAKjB,SAAK,WAAW;AAIhB,SAAK,iBAAiB;AAItB,SAAK,aAAa;AAIlB,SAAK,QAAQ;AAEb,SAAK,mBAAmB;AAExB,SAAK,sBAAsB;AAE3B,SAAK,sBAAsB,IAAI,aAAa;AAC5C,SAAK,WAAW,IAAI,aAAa,KAAK,aAAa,mBAAmB,KAAK,SAAS,EAAE,QAAQ;AAAA,MAC5F,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AACD,WAAO,OAAO,MAAM,MAAM;AAC1B,SAAK,UAAU,KAAK,SAAS;AAC7B,SAAK,WAAW,KAAK,SAAS;AAAA,EAChC;AAAA,EACA,WAAW;AACT,SAAK,SAAS,OAAO;AAAA,MACnB,UAAU,KAAK;AAAA,MACf,MAAM,MAAM,KAAK,KAAK;AAAA,IACxB,CAAC;AACD,SAAK,cAAc,UAAU,WAAS;AACpC,UAAI,CAAC,OAAO;AACV,aAAK,SAAS,KAAK;AAAA,MACrB;AAAA,IACF,CAAC;AACD,SAAK,QAAQ,UAAU,MAAM;AAC3B,WAAK,mBAAmB;AAAA,IAC1B,CAAC;AACD,SAAK,SAAS,UAAU,MAAM;AAC5B,WAAK,mBAAmB;AAAA,IAC1B,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,mBAAmB,KAAK,SAAS,WAAW,KAAK,SAAS,KAAK;AACpE,QAAI,KAAK,kBAAkB;AACzB,WAAK,UAAU,aAAa,KAAK,YAAY,eAAe,oBAAoB,KAAK,gBAAgB;AAAA,IACvG,OAAO;AACL,WAAK,UAAU,gBAAgB,KAAK,YAAY,eAAe,kBAAkB;AAAA,IACnF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,QAAI,KAAK,QAAQ;AACf,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,SAAK,KAAK;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,SAAK,iBAAiB,WAAW;AAAA,MAC/B,WAAW;AAAA,QACT,MAAM;AAAA,UACJ,SAAS,KAAK;AAAA,QAChB;AAAA,QACA,iBAAiB;AAAA,UACf,SAAS,KAAK;AAAA,UACd,mBAAmB,KAAK,qBAAqB;AAAA,QAC/C;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,KAAK,UAAU,KAAK,cAAc,KAAK,mBAAmB,CAAC,KAAK,SAAS;AAC3E;AAAA,IACF;AACA,UAAM,cAAc,MAAM;AACxB,UAAI,KAAK,iBAAiB;AACxB,aAAK,kBAAkB;AAAA,MACzB;AACA,WAAK,SAAS,OAAO,yBAAyB,EAAE,GAAG,KAAK,SAAS,EAAE,SAAS;AAAA,QAC1E,YAAY,KAAK;AAAA,MACnB,CAAC,EAAE,KAAK;AAAA,QACN,SAAS,KAAK;AAAA,QACd,WAAW,KAAK;AAAA,QAChB,gBAAgB,KAAK;AAAA,QACrB,IAAI,WAAW,KAAK,SAAS;AAAA,MAC/B,CAAC;AAAA,IACH;AACA,UAAM,8BAA8B,MAAM;AACxC,UAAI,KAAK,sBAAsB;AAC7B,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF;AACA,QAAI,KAAK,OAAO;AACd,UAAI,KAAK,oBAAoB;AAC3B,aAAK,mBAAmB,YAAY;AAAA,MACtC;AACA,WAAK,qBAAqB,MAAM,KAAK,KAAK,EAAE,UAAU,MAAM;AAC1D,oBAAY;AACZ,oCAA4B;AAAA,MAC9B,CAAC;AACD,UAAI,KAAK,UAAU;AACjB,sBAAc,KAAK,QAAQ,EAAE,QAAQ,aAAW;AAC9C,cAAI,CAAC,QAAQ,OAAO;AAClB;AAAA,UACF;AACA,eAAK,uBAAuB,KAAK,UAAU,OAAO,KAAK,YAAY,eAAe,QAAQ,OAAO,MAAM;AACrG,iBAAK,oBAAoB,YAAY;AACrC,wCAA4B;AAAA,UAC9B,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,kBAAY;AAAA,IACd;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,QAAI,KAAK,iBAAiB;AACxB,mBAAa,KAAK,eAAe;AACjC,WAAK,kBAAkB;AAAA,IACzB;AACA,QAAI,CAAC,KAAK,SAAS,SAAS;AAC1B;AAAA,IACF;AACA,QAAI,KAAK,SAAS,UAAU,UAAU;AACpC,WAAK,SAAS,SAAS,SAAS,IAAI,IAAI;AAAA,IAC1C;AACA,eAAW,MAAM;AACf,WAAK,SAAS,KAAK;AAAA,IACrB,GAAG,KAAK,mBAAmB;AAAA,EAC7B;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,QAAQ;AACtB,SAAK,cAAc,YAAY;AAC/B,QAAI,KAAK,oBAAoB;AAC3B,WAAK,mBAAmB,YAAY;AAAA,IACtC;AACA,SAAK,QAAQ,YAAY;AACzB,SAAK,SAAS,YAAY;AAAA,EAC5B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,kBAAqB,gBAAgB,GAAM,kBAAqB,sBAAsB,GAAM,kBAAkB,aAAa,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,kBAAkB,CAAC;AAAA,IAC1S;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,GAAG,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,MACxD,QAAQ;AAAA,QACN,kBAAkB;AAAA,QAClB,SAAS;AAAA,QACT,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,mBAAmB;AAAA,QACnB,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,aAAa,CAAC,GAAG,eAAe,aAAa;AAAA,QAC7C,YAAY,CAAC,GAAG,oBAAoB,YAAY;AAAA,QAChD,SAAS,CAAC,GAAG,iBAAiB,SAAS;AAAA,QACvC,SAAS,CAAC,GAAG,iBAAiB,SAAS;AAAA,QACvC,eAAe,CAAC,GAAG,uBAAuB,eAAe;AAAA,QACzD,kBAAkB;AAAA,QAClB,aAAa,CAAC,GAAG,gBAAgB,aAAa;AAAA,QAC9C,iBAAiB,CAAC,GAAG,kBAAkB,iBAAiB;AAAA,QACxD,oBAAoB,CAAC,GAAG,qBAAqB,oBAAoB;AAAA,QACjE,qBAAqB;AAAA,QACrB,iBAAiB,CAAC,GAAG,kBAAkB,iBAAiB;AAAA,MAC1D;AAAA,MACA,SAAS;AAAA,QACP,eAAe;AAAA,QACf,SAAS;AAAA,QACT,UAAU;AAAA,QACV,qBAAqB;AAAA,MACvB;AAAA,MACA,UAAU,CAAC,YAAY;AAAA,MACvB,UAAU,CAAI,mBAAmB,CAAC,wBAAwB,kBAAkB,CAAC,CAAC;AAAA,IAChF,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,SAAS,GAAG,WAAW,eAAe,MAAM,CAAC,GAAG,iBAAiB,WAAW,WAAW,MAAM;AAAA,CACxG,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW,CAAC,wBAAwB,kBAAkB;AAAA,IACxD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA;AAAA,EAElB,OAAO,UAAU;AACf,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,IACd;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,cAAc,kBAAkB,yBAAyB;AAAA,MACnE,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,kBAAkB,yBAAyB;AAAA,MACnE,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}
import {
  CommonModule,
  init_common
} from "./chunk-TEPKXNLU.js";
import "./chunk-4RF3VZXG.js";
import {
  DOCUMENT,
  Directive,
  EventEmitter,
  HostListener,
  Inject,
  Injectable,
  InjectionToken,
  Input,
  NgModule,
  Optional,
  Output,
  TemplateRef,
  ViewContainerRef,
  init_core,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-WUNHS5KN.js";
import {
  init_esm5
} from "./chunk-N726T63C.js";
import "./chunk-H5IUTQT7.js";
import {
  Subject
} from "./chunk-CTL5GG7J.js";
import "./chunk-WOR4A3D2.js";

// node_modules/ngx-clipboard/fesm2015/ngx-clipboard.js
init_common();
init_core();

// node_modules/ngx-window-token/fesm2020/ngx-window-token.mjs
init_core();
var WINDOW = new InjectionToken("WindowToken", typeof window !== "undefined" && window.document ? { providedIn: "root", factory: () => window } : { providedIn: "root", factory: () => void 0 });

// node_modules/ngx-clipboard/fesm2015/ngx-clipboard.js
init_esm5();
var ClipboardService = class {
  constructor(document, window2) {
    this.document = document;
    this.window = window2;
    this.copySubject = new Subject();
    this.copyResponse$ = this.copySubject.asObservable();
    this.config = {};
  }
  configure(config) {
    this.config = config;
  }
  copy(content) {
    if (!this.isSupported || !content) {
      return this.pushCopyResponse({ isSuccess: false, content });
    }
    const copyResult = this.copyFromContent(content);
    if (copyResult) {
      return this.pushCopyResponse({ content, isSuccess: copyResult });
    }
    return this.pushCopyResponse({ isSuccess: false, content });
  }
  get isSupported() {
    return !!this.document.queryCommandSupported && !!this.document.queryCommandSupported("copy") && !!this.window;
  }
  isTargetValid(element) {
    if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
      if (element.hasAttribute("disabled")) {
        throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');
      }
      return true;
    }
    throw new Error("Target should be input or textarea");
  }
  /**
   * Attempts to copy from an input `targetElm`
   */
  copyFromInputElement(targetElm, isFocus = true) {
    try {
      this.selectTarget(targetElm);
      const re = this.copyText();
      this.clearSelection(isFocus ? targetElm : void 0, this.window);
      return re && this.isCopySuccessInIE11();
    } catch (error) {
      return false;
    }
  }
  /**
   * This is a hack for IE11 to return `true` even if copy fails.
   */
  isCopySuccessInIE11() {
    const clipboardData = this.window["clipboardData"];
    if (clipboardData && clipboardData.getData) {
      if (!clipboardData.getData("Text")) {
        return false;
      }
    }
    return true;
  }
  /**
   * Creates a fake textarea element, sets its value from `text` property,
   * and makes a selection on it.
   */
  copyFromContent(content, container = this.document.body) {
    if (this.tempTextArea && !container.contains(this.tempTextArea)) {
      this.destroy(this.tempTextArea.parentElement || void 0);
    }
    if (!this.tempTextArea) {
      this.tempTextArea = this.createTempTextArea(this.document, this.window);
      try {
        container.appendChild(this.tempTextArea);
      } catch (error) {
        throw new Error("Container should be a Dom element");
      }
    }
    this.tempTextArea.value = content;
    const toReturn = this.copyFromInputElement(this.tempTextArea, false);
    if (this.config.cleanUpAfterCopy) {
      this.destroy(this.tempTextArea.parentElement || void 0);
    }
    return toReturn;
  }
  /**
   * Remove temporary textarea if any exists.
   */
  destroy(container = this.document.body) {
    if (this.tempTextArea) {
      container.removeChild(this.tempTextArea);
      this.tempTextArea = void 0;
    }
  }
  /**
   * Select the target html input element.
   */
  selectTarget(inputElement) {
    inputElement.select();
    inputElement.setSelectionRange(0, inputElement.value.length);
    return inputElement.value.length;
  }
  copyText() {
    return this.document.execCommand("copy");
  }
  /**
   * Moves focus away from `target` and back to the trigger, removes current selection.
   */
  clearSelection(inputElement, window2) {
    var _a;
    inputElement && inputElement.focus();
    (_a = window2.getSelection()) === null || _a === void 0 ? void 0 : _a.removeAllRanges();
  }
  /**
   * Creates a fake textarea for copy command.
   */
  createTempTextArea(doc, window2) {
    const isRTL = doc.documentElement.getAttribute("dir") === "rtl";
    let ta;
    ta = doc.createElement("textarea");
    ta.style.fontSize = "12pt";
    ta.style.border = "0";
    ta.style.padding = "0";
    ta.style.margin = "0";
    ta.style.position = "absolute";
    ta.style[isRTL ? "right" : "left"] = "-9999px";
    const yPosition = window2.pageYOffset || doc.documentElement.scrollTop;
    ta.style.top = yPosition + "px";
    ta.setAttribute("readonly", "");
    return ta;
  }
  /**
   * Pushes copy operation response to copySubject, to provide global access
   * to the response.
   */
  pushCopyResponse(response) {
    this.copySubject.next(response);
  }
  /**
   * @deprecated use pushCopyResponse instead.
   */
  pushCopyReponse(response) {
    this.pushCopyResponse(response);
  }
};
ClipboardService.ɵprov = ɵɵdefineInjectable({ factory: function ClipboardService_Factory() {
  return new ClipboardService(ɵɵinject(DOCUMENT), ɵɵinject(WINDOW, 8));
}, token: ClipboardService, providedIn: "root" });
ClipboardService.decorators = [
  { type: Injectable, args: [{ providedIn: "root" }] }
];
ClipboardService.ctorParameters = () => [
  { type: void 0, decorators: [{ type: Inject, args: [DOCUMENT] }] },
  { type: void 0, decorators: [{ type: Optional }, { type: Inject, args: [WINDOW] }] }
];
var ClipboardDirective = class {
  constructor(clipboardSrv) {
    this.clipboardSrv = clipboardSrv;
    this.cbOnSuccess = new EventEmitter();
    this.cbOnError = new EventEmitter();
  }
  // tslint:disable-next-line:no-empty
  ngOnInit() {
  }
  ngOnDestroy() {
    this.clipboardSrv.destroy(this.container);
  }
  onClick(event) {
    if (!this.clipboardSrv.isSupported) {
      this.handleResult(false, void 0, event);
    } else if (this.targetElm && this.clipboardSrv.isTargetValid(this.targetElm)) {
      this.handleResult(this.clipboardSrv.copyFromInputElement(this.targetElm), this.targetElm.value, event);
    } else if (this.cbContent) {
      this.handleResult(this.clipboardSrv.copyFromContent(this.cbContent, this.container), this.cbContent, event);
    }
  }
  /**
   * Fires an event based on the copy operation result.
   * @param succeeded
   */
  handleResult(succeeded, copiedContent, event) {
    let response = {
      isSuccess: succeeded,
      event
    };
    if (succeeded) {
      response = Object.assign(response, {
        content: copiedContent,
        successMessage: this.cbSuccessMsg
      });
      this.cbOnSuccess.emit(response);
    } else {
      this.cbOnError.emit(response);
    }
    this.clipboardSrv.pushCopyResponse(response);
  }
};
ClipboardDirective.decorators = [
  { type: Directive, args: [{
    selector: "[ngxClipboard]"
  }] }
];
ClipboardDirective.ctorParameters = () => [
  { type: ClipboardService }
];
ClipboardDirective.propDecorators = {
  targetElm: [{ type: Input, args: ["ngxClipboard"] }],
  container: [{ type: Input }],
  cbContent: [{ type: Input }],
  cbSuccessMsg: [{ type: Input }],
  cbOnSuccess: [{ type: Output }],
  cbOnError: [{ type: Output }],
  onClick: [{ type: HostListener, args: ["click", ["$event.target"]] }]
};
var ClipboardIfSupportedDirective = class {
  constructor(_clipboardService, _viewContainerRef, _templateRef) {
    this._clipboardService = _clipboardService;
    this._viewContainerRef = _viewContainerRef;
    this._templateRef = _templateRef;
  }
  ngOnInit() {
    if (this._clipboardService.isSupported) {
      this._viewContainerRef.createEmbeddedView(this._templateRef);
    }
  }
};
ClipboardIfSupportedDirective.decorators = [
  { type: Directive, args: [{
    selector: "[ngxClipboardIfSupported]"
  }] }
];
ClipboardIfSupportedDirective.ctorParameters = () => [
  { type: ClipboardService },
  { type: ViewContainerRef },
  { type: TemplateRef }
];
var ClipboardModule = class {
};
ClipboardModule.decorators = [
  { type: NgModule, args: [{
    imports: [CommonModule],
    declarations: [ClipboardDirective, ClipboardIfSupportedDirective],
    exports: [ClipboardDirective, ClipboardIfSupportedDirective]
  }] }
];
export {
  ClipboardDirective,
  ClipboardIfSupportedDirective,
  ClipboardModule,
  ClipboardService
};
//# sourceMappingURL=ngx-clipboard.js.map

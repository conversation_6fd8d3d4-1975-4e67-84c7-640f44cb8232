{"version": 3, "sources": ["../../../../../../node_modules/ngx-clipboard/fesm2015/ngx-clipboard.js", "../../../../../../node_modules/ngx-window-token/fesm2020/ngx-window-token.mjs"], "sourcesContent": ["import { DOCUMENT, CommonModule } from '@angular/common';\nimport { ɵɵdefineInjectable, ɵɵinject, Injectable, Inject, Optional, EventEmitter, Directive, Input, Output, HostListener, ViewContainerRef, TemplateRef, NgModule } from '@angular/core';\nimport { WINDOW } from 'ngx-window-token';\nimport { Subject } from 'rxjs';\n\n/**\r\n * The following code is heavily copied from https://github.com/zenorocha/clipboard.js\r\n */\r\nclass ClipboardService {\r\n    constructor(document, window) {\r\n        this.document = document;\r\n        this.window = window;\r\n        this.copySubject = new Subject();\r\n        this.copyResponse$ = this.copySubject.asObservable();\r\n        this.config = {};\r\n    }\r\n    configure(config) {\r\n        this.config = config;\r\n    }\r\n    copy(content) {\r\n        if (!this.isSupported || !content) {\r\n            return this.pushCopyResponse({ isSuccess: false, content });\r\n        }\r\n        const copyResult = this.copyFromContent(content);\r\n        if (copyResult) {\r\n            return this.pushCopyResponse({ content, isSuccess: copyResult });\r\n        }\r\n        return this.pushCopyResponse({ isSuccess: false, content });\r\n    }\r\n    get isSupported() {\r\n        return !!this.document.queryCommandSupported && !!this.document.queryCommandSupported('copy') && !!this.window;\r\n    }\r\n    isTargetValid(element) {\r\n        if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {\r\n            if (element.hasAttribute('disabled')) {\r\n                throw new Error('Invalid \"target\" attribute. Please use \"readonly\" instead of \"disabled\" attribute');\r\n            }\r\n            return true;\r\n        }\r\n        throw new Error('Target should be input or textarea');\r\n    }\r\n    /**\r\n     * Attempts to copy from an input `targetElm`\r\n     */\r\n    copyFromInputElement(targetElm, isFocus = true) {\r\n        try {\r\n            this.selectTarget(targetElm);\r\n            const re = this.copyText();\r\n            this.clearSelection(isFocus ? targetElm : undefined, this.window);\r\n            return re && this.isCopySuccessInIE11();\r\n        }\r\n        catch (error) {\r\n            return false;\r\n        }\r\n    }\r\n    /**\r\n     * This is a hack for IE11 to return `true` even if copy fails.\r\n     */\r\n    isCopySuccessInIE11() {\r\n        const clipboardData = this.window['clipboardData'];\r\n        if (clipboardData && clipboardData.getData) {\r\n            if (!clipboardData.getData('Text')) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n    /**\r\n     * Creates a fake textarea element, sets its value from `text` property,\r\n     * and makes a selection on it.\r\n     */\r\n    copyFromContent(content, container = this.document.body) {\r\n        // check if the temp textarea still belongs to the current container.\r\n        // In case we have multiple places using ngx-clipboard, one is in a modal using container but the other one is not.\r\n        if (this.tempTextArea && !container.contains(this.tempTextArea)) {\r\n            this.destroy(this.tempTextArea.parentElement || undefined);\r\n        }\r\n        if (!this.tempTextArea) {\r\n            this.tempTextArea = this.createTempTextArea(this.document, this.window);\r\n            try {\r\n                container.appendChild(this.tempTextArea);\r\n            }\r\n            catch (error) {\r\n                throw new Error('Container should be a Dom element');\r\n            }\r\n        }\r\n        this.tempTextArea.value = content;\r\n        const toReturn = this.copyFromInputElement(this.tempTextArea, false);\r\n        if (this.config.cleanUpAfterCopy) {\r\n            this.destroy(this.tempTextArea.parentElement || undefined);\r\n        }\r\n        return toReturn;\r\n    }\r\n    /**\r\n     * Remove temporary textarea if any exists.\r\n     */\r\n    destroy(container = this.document.body) {\r\n        if (this.tempTextArea) {\r\n            container.removeChild(this.tempTextArea);\r\n            // removeChild doesn't remove the reference from memory\r\n            this.tempTextArea = undefined;\r\n        }\r\n    }\r\n    /**\r\n     * Select the target html input element.\r\n     */\r\n    selectTarget(inputElement) {\r\n        inputElement.select();\r\n        inputElement.setSelectionRange(0, inputElement.value.length);\r\n        return inputElement.value.length;\r\n    }\r\n    copyText() {\r\n        return this.document.execCommand('copy');\r\n    }\r\n    /**\r\n     * Moves focus away from `target` and back to the trigger, removes current selection.\r\n     */\r\n    clearSelection(inputElement, window) {\r\n        var _a;\r\n        inputElement && inputElement.focus();\r\n        (_a = window.getSelection()) === null || _a === void 0 ? void 0 : _a.removeAllRanges();\r\n    }\r\n    /**\r\n     * Creates a fake textarea for copy command.\r\n     */\r\n    createTempTextArea(doc, window) {\r\n        const isRTL = doc.documentElement.getAttribute('dir') === 'rtl';\r\n        let ta;\r\n        ta = doc.createElement('textarea');\r\n        // Prevent zooming on iOS\r\n        ta.style.fontSize = '12pt';\r\n        // Reset box model\r\n        ta.style.border = '0';\r\n        ta.style.padding = '0';\r\n        ta.style.margin = '0';\r\n        // Move element out of screen horizontally\r\n        ta.style.position = 'absolute';\r\n        ta.style[isRTL ? 'right' : 'left'] = '-9999px';\r\n        // Move element to the same position vertically\r\n        const yPosition = window.pageYOffset || doc.documentElement.scrollTop;\r\n        ta.style.top = yPosition + 'px';\r\n        ta.setAttribute('readonly', '');\r\n        return ta;\r\n    }\r\n    /**\r\n     * Pushes copy operation response to copySubject, to provide global access\r\n     * to the response.\r\n     */\r\n    pushCopyResponse(response) {\r\n        this.copySubject.next(response);\r\n    }\r\n    /**\r\n     * @deprecated use pushCopyResponse instead.\r\n     */\r\n    pushCopyReponse(response) {\r\n        this.pushCopyResponse(response);\r\n    }\r\n}\r\nClipboardService.ɵprov = ɵɵdefineInjectable({ factory: function ClipboardService_Factory() { return new ClipboardService(ɵɵinject(DOCUMENT), ɵɵinject(WINDOW, 8)); }, token: ClipboardService, providedIn: \"root\" });\r\nClipboardService.decorators = [\r\n    { type: Injectable, args: [{ providedIn: 'root' },] }\r\n];\r\nClipboardService.ctorParameters = () => [\r\n    { type: undefined, decorators: [{ type: Inject, args: [DOCUMENT,] }] },\r\n    { type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [WINDOW,] }] }\r\n];\n\nclass ClipboardDirective {\r\n    constructor(clipboardSrv) {\r\n        this.clipboardSrv = clipboardSrv;\r\n        this.cbOnSuccess = new EventEmitter();\r\n        this.cbOnError = new EventEmitter();\r\n    }\r\n    // tslint:disable-next-line:no-empty\r\n    ngOnInit() { }\r\n    ngOnDestroy() {\r\n        this.clipboardSrv.destroy(this.container);\r\n    }\r\n    onClick(event) {\r\n        if (!this.clipboardSrv.isSupported) {\r\n            this.handleResult(false, undefined, event);\r\n        }\r\n        else if (this.targetElm && this.clipboardSrv.isTargetValid(this.targetElm)) {\r\n            this.handleResult(this.clipboardSrv.copyFromInputElement(this.targetElm), this.targetElm.value, event);\r\n        }\r\n        else if (this.cbContent) {\r\n            this.handleResult(this.clipboardSrv.copyFromContent(this.cbContent, this.container), this.cbContent, event);\r\n        }\r\n    }\r\n    /**\r\n     * Fires an event based on the copy operation result.\r\n     * @param succeeded\r\n     */\r\n    handleResult(succeeded, copiedContent, event) {\r\n        let response = {\r\n            isSuccess: succeeded,\r\n            event\r\n        };\r\n        if (succeeded) {\r\n            response = Object.assign(response, {\r\n                content: copiedContent,\r\n                successMessage: this.cbSuccessMsg\r\n            });\r\n            this.cbOnSuccess.emit(response);\r\n        }\r\n        else {\r\n            this.cbOnError.emit(response);\r\n        }\r\n        this.clipboardSrv.pushCopyResponse(response);\r\n    }\r\n}\r\nClipboardDirective.decorators = [\r\n    { type: Directive, args: [{\r\n                selector: '[ngxClipboard]'\r\n            },] }\r\n];\r\nClipboardDirective.ctorParameters = () => [\r\n    { type: ClipboardService }\r\n];\r\nClipboardDirective.propDecorators = {\r\n    targetElm: [{ type: Input, args: ['ngxClipboard',] }],\r\n    container: [{ type: Input }],\r\n    cbContent: [{ type: Input }],\r\n    cbSuccessMsg: [{ type: Input }],\r\n    cbOnSuccess: [{ type: Output }],\r\n    cbOnError: [{ type: Output }],\r\n    onClick: [{ type: HostListener, args: ['click', ['$event.target'],] }]\r\n};\n\nclass ClipboardIfSupportedDirective {\r\n    constructor(_clipboardService, _viewContainerRef, _templateRef) {\r\n        this._clipboardService = _clipboardService;\r\n        this._viewContainerRef = _viewContainerRef;\r\n        this._templateRef = _templateRef;\r\n    }\r\n    ngOnInit() {\r\n        if (this._clipboardService.isSupported) {\r\n            this._viewContainerRef.createEmbeddedView(this._templateRef);\r\n        }\r\n    }\r\n}\r\nClipboardIfSupportedDirective.decorators = [\r\n    { type: Directive, args: [{\r\n                selector: '[ngxClipboardIfSupported]'\r\n            },] }\r\n];\r\nClipboardIfSupportedDirective.ctorParameters = () => [\r\n    { type: ClipboardService },\r\n    { type: ViewContainerRef },\r\n    { type: TemplateRef }\r\n];\n\nclass ClipboardModule {\r\n}\r\nClipboardModule.decorators = [\r\n    { type: NgModule, args: [{\r\n                imports: [CommonModule],\r\n                declarations: [ClipboardDirective, ClipboardIfSupportedDirective],\r\n                exports: [ClipboardDirective, ClipboardIfSupportedDirective]\r\n            },] }\r\n];\n\n/*\r\n * Public API Surface of ngx-clipboard\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { ClipboardDirective, ClipboardIfSupportedDirective, ClipboardModule, ClipboardService };\n\n", "import { InjectionToken } from '@angular/core';\n\nconst WINDOW = new InjectionToken('WindowToken', typeof window !== 'undefined' && window.document\n    ? { providedIn: 'root', factory: () => window }\n    : { providedIn: 'root', factory: () => undefined });\n\n/*\n * Public API Surface of ngx-window-token\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { WINDOW };\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;ACDA;AAEA,IAAM,SAAS,IAAI,eAAe,eAAe,OAAO,WAAW,eAAe,OAAO,WACnF,EAAE,YAAY,QAAQ,SAAS,MAAM,OAAO,IAC5C,EAAE,YAAY,QAAQ,SAAS,MAAM,OAAU,CAAC;;;ADDtD;AAKA,IAAM,mBAAN,MAAuB;AAAA,EACnB,YAAY,UAAUA,SAAQ;AAC1B,SAAK,WAAW;AAChB,SAAK,SAASA;AACd,SAAK,cAAc,IAAI,QAAQ;AAC/B,SAAK,gBAAgB,KAAK,YAAY,aAAa;AACnD,SAAK,SAAS,CAAC;AAAA,EACnB;AAAA,EACA,UAAU,QAAQ;AACd,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,KAAK,SAAS;AACV,QAAI,CAAC,KAAK,eAAe,CAAC,SAAS;AAC/B,aAAO,KAAK,iBAAiB,EAAE,WAAW,OAAO,QAAQ,CAAC;AAAA,IAC9D;AACA,UAAM,aAAa,KAAK,gBAAgB,OAAO;AAC/C,QAAI,YAAY;AACZ,aAAO,KAAK,iBAAiB,EAAE,SAAS,WAAW,WAAW,CAAC;AAAA,IACnE;AACA,WAAO,KAAK,iBAAiB,EAAE,WAAW,OAAO,QAAQ,CAAC;AAAA,EAC9D;AAAA,EACA,IAAI,cAAc;AACd,WAAO,CAAC,CAAC,KAAK,SAAS,yBAAyB,CAAC,CAAC,KAAK,SAAS,sBAAsB,MAAM,KAAK,CAAC,CAAC,KAAK;AAAA,EAC5G;AAAA,EACA,cAAc,SAAS;AACnB,QAAI,mBAAmB,oBAAoB,mBAAmB,qBAAqB;AAC/E,UAAI,QAAQ,aAAa,UAAU,GAAG;AAClC,cAAM,IAAI,MAAM,mFAAmF;AAAA,MACvG;AACA,aAAO;AAAA,IACX;AACA,UAAM,IAAI,MAAM,oCAAoC;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB,WAAW,UAAU,MAAM;AAC5C,QAAI;AACA,WAAK,aAAa,SAAS;AAC3B,YAAM,KAAK,KAAK,SAAS;AACzB,WAAK,eAAe,UAAU,YAAY,QAAW,KAAK,MAAM;AAChE,aAAO,MAAM,KAAK,oBAAoB;AAAA,IAC1C,SACO,OAAO;AACV,aAAO;AAAA,IACX;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AAClB,UAAM,gBAAgB,KAAK,OAAO,eAAe;AACjD,QAAI,iBAAiB,cAAc,SAAS;AACxC,UAAI,CAAC,cAAc,QAAQ,MAAM,GAAG;AAChC,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,SAAS,YAAY,KAAK,SAAS,MAAM;AAGrD,QAAI,KAAK,gBAAgB,CAAC,UAAU,SAAS,KAAK,YAAY,GAAG;AAC7D,WAAK,QAAQ,KAAK,aAAa,iBAAiB,MAAS;AAAA,IAC7D;AACA,QAAI,CAAC,KAAK,cAAc;AACpB,WAAK,eAAe,KAAK,mBAAmB,KAAK,UAAU,KAAK,MAAM;AACtE,UAAI;AACA,kBAAU,YAAY,KAAK,YAAY;AAAA,MAC3C,SACO,OAAO;AACV,cAAM,IAAI,MAAM,mCAAmC;AAAA,MACvD;AAAA,IACJ;AACA,SAAK,aAAa,QAAQ;AAC1B,UAAM,WAAW,KAAK,qBAAqB,KAAK,cAAc,KAAK;AACnE,QAAI,KAAK,OAAO,kBAAkB;AAC9B,WAAK,QAAQ,KAAK,aAAa,iBAAiB,MAAS;AAAA,IAC7D;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,YAAY,KAAK,SAAS,MAAM;AACpC,QAAI,KAAK,cAAc;AACnB,gBAAU,YAAY,KAAK,YAAY;AAEvC,WAAK,eAAe;AAAA,IACxB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,cAAc;AACvB,iBAAa,OAAO;AACpB,iBAAa,kBAAkB,GAAG,aAAa,MAAM,MAAM;AAC3D,WAAO,aAAa,MAAM;AAAA,EAC9B;AAAA,EACA,WAAW;AACP,WAAO,KAAK,SAAS,YAAY,MAAM;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,cAAcA,SAAQ;AACjC,QAAI;AACJ,oBAAgB,aAAa,MAAM;AACnC,KAAC,KAAKA,QAAO,aAAa,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB;AAAA,EACzF;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB,KAAKA,SAAQ;AAC5B,UAAM,QAAQ,IAAI,gBAAgB,aAAa,KAAK,MAAM;AAC1D,QAAI;AACJ,SAAK,IAAI,cAAc,UAAU;AAEjC,OAAG,MAAM,WAAW;AAEpB,OAAG,MAAM,SAAS;AAClB,OAAG,MAAM,UAAU;AACnB,OAAG,MAAM,SAAS;AAElB,OAAG,MAAM,WAAW;AACpB,OAAG,MAAM,QAAQ,UAAU,MAAM,IAAI;AAErC,UAAM,YAAYA,QAAO,eAAe,IAAI,gBAAgB;AAC5D,OAAG,MAAM,MAAM,YAAY;AAC3B,OAAG,aAAa,YAAY,EAAE;AAC9B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,UAAU;AACvB,SAAK,YAAY,KAAK,QAAQ;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,UAAU;AACtB,SAAK,iBAAiB,QAAQ;AAAA,EAClC;AACJ;AACA,iBAAiB,QAAQ,mBAAmB,EAAE,SAAS,SAAS,2BAA2B;AAAE,SAAO,IAAI,iBAAiB,SAAS,QAAQ,GAAG,SAAS,QAAQ,CAAC,CAAC;AAAG,GAAG,OAAO,kBAAkB,YAAY,OAAO,CAAC;AACnN,iBAAiB,aAAa;AAAA,EAC1B,EAAE,MAAM,YAAY,MAAM,CAAC,EAAE,YAAY,OAAO,CAAE,EAAE;AACxD;AACA,iBAAiB,iBAAiB,MAAM;AAAA,EACpC,EAAE,MAAM,QAAW,YAAY,CAAC,EAAE,MAAM,QAAQ,MAAM,CAAC,QAAS,EAAE,CAAC,EAAE;AAAA,EACrE,EAAE,MAAM,QAAW,YAAY,CAAC,EAAE,MAAM,SAAS,GAAG,EAAE,MAAM,QAAQ,MAAM,CAAC,MAAO,EAAE,CAAC,EAAE;AAC3F;AAEA,IAAM,qBAAN,MAAyB;AAAA,EACrB,YAAY,cAAc;AACtB,SAAK,eAAe;AACpB,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,YAAY,IAAI,aAAa;AAAA,EACtC;AAAA;AAAA,EAEA,WAAW;AAAA,EAAE;AAAA,EACb,cAAc;AACV,SAAK,aAAa,QAAQ,KAAK,SAAS;AAAA,EAC5C;AAAA,EACA,QAAQ,OAAO;AACX,QAAI,CAAC,KAAK,aAAa,aAAa;AAChC,WAAK,aAAa,OAAO,QAAW,KAAK;AAAA,IAC7C,WACS,KAAK,aAAa,KAAK,aAAa,cAAc,KAAK,SAAS,GAAG;AACxE,WAAK,aAAa,KAAK,aAAa,qBAAqB,KAAK,SAAS,GAAG,KAAK,UAAU,OAAO,KAAK;AAAA,IACzG,WACS,KAAK,WAAW;AACrB,WAAK,aAAa,KAAK,aAAa,gBAAgB,KAAK,WAAW,KAAK,SAAS,GAAG,KAAK,WAAW,KAAK;AAAA,IAC9G;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,WAAW,eAAe,OAAO;AAC1C,QAAI,WAAW;AAAA,MACX,WAAW;AAAA,MACX;AAAA,IACJ;AACA,QAAI,WAAW;AACX,iBAAW,OAAO,OAAO,UAAU;AAAA,QAC/B,SAAS;AAAA,QACT,gBAAgB,KAAK;AAAA,MACzB,CAAC;AACD,WAAK,YAAY,KAAK,QAAQ;AAAA,IAClC,OACK;AACD,WAAK,UAAU,KAAK,QAAQ;AAAA,IAChC;AACA,SAAK,aAAa,iBAAiB,QAAQ;AAAA,EAC/C;AACJ;AACA,mBAAmB,aAAa;AAAA,EAC5B,EAAE,MAAM,WAAW,MAAM,CAAC;AAAA,IACd,UAAU;AAAA,EACd,CAAE,EAAE;AAChB;AACA,mBAAmB,iBAAiB,MAAM;AAAA,EACtC,EAAE,MAAM,iBAAiB;AAC7B;AACA,mBAAmB,iBAAiB;AAAA,EAChC,WAAW,CAAC,EAAE,MAAM,OAAO,MAAM,CAAC,cAAe,EAAE,CAAC;AAAA,EACpD,WAAW,CAAC,EAAE,MAAM,MAAM,CAAC;AAAA,EAC3B,WAAW,CAAC,EAAE,MAAM,MAAM,CAAC;AAAA,EAC3B,cAAc,CAAC,EAAE,MAAM,MAAM,CAAC;AAAA,EAC9B,aAAa,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,EAC9B,WAAW,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,EAC5B,SAAS,CAAC,EAAE,MAAM,cAAc,MAAM,CAAC,SAAS,CAAC,eAAe,CAAE,EAAE,CAAC;AACzE;AAEA,IAAM,gCAAN,MAAoC;AAAA,EAChC,YAAY,mBAAmB,mBAAmB,cAAc;AAC5D,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AACzB,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,WAAW;AACP,QAAI,KAAK,kBAAkB,aAAa;AACpC,WAAK,kBAAkB,mBAAmB,KAAK,YAAY;AAAA,IAC/D;AAAA,EACJ;AACJ;AACA,8BAA8B,aAAa;AAAA,EACvC,EAAE,MAAM,WAAW,MAAM,CAAC;AAAA,IACd,UAAU;AAAA,EACd,CAAE,EAAE;AAChB;AACA,8BAA8B,iBAAiB,MAAM;AAAA,EACjD,EAAE,MAAM,iBAAiB;AAAA,EACzB,EAAE,MAAM,iBAAiB;AAAA,EACzB,EAAE,MAAM,YAAY;AACxB;AAEA,IAAM,kBAAN,MAAsB;AACtB;AACA,gBAAgB,aAAa;AAAA,EACzB,EAAE,MAAM,UAAU,MAAM,CAAC;AAAA,IACb,SAAS,CAAC,YAAY;AAAA,IACtB,cAAc,CAAC,oBAAoB,6BAA6B;AAAA,IAChE,SAAS,CAAC,oBAAoB,6BAA6B;AAAA,EAC/D,CAAE,EAAE;AAChB;", "names": ["window"]}
{"version": 3, "sources": ["../../../../../../node_modules/ua-parser-js/src/ua-parser.js"], "sourcesContent": ["/**\n * UAParser.js v0.7.12\n * Lightweight JavaScript-based User-Agent string parser\n * https://github.com/faisalman/ua-parser-js\n *\n * Copyright © 2012-2016 Faisal <PERSON> <fyz<PERSON>@gmail.com>\n * Dual licensed under GPLv2 & MIT\n */\n\n(function (window, undefined) {\n\n    'use strict';\n\n    //////////////\n    // Constants\n    /////////////\n\n\n    var LIBVERSION  = '0.7.12',\n        EMPTY       = '',\n        UNKNOWN     = '?',\n        FUNC_TYPE   = 'function',\n        UNDEF_TYPE  = 'undefined',\n        OBJ_TYPE    = 'object',\n        STR_TYPE    = 'string',\n        MAJOR       = 'major', // deprecated\n        MODEL       = 'model',\n        NAME        = 'name',\n        TYPE        = 'type',\n        VENDOR      = 'vendor',\n        VERSION     = 'version',\n        ARCHITECTURE= 'architecture',\n        CONSOLE     = 'console',\n        MOBILE      = 'mobile',\n        TABLET      = 'tablet',\n        SMARTTV     = 'smarttv',\n        WEARABLE    = 'wearable',\n        EMBEDDED    = 'embedded';\n\n\n    ///////////\n    // Helper\n    //////////\n\n\n    var util = {\n        extend : function (regexes, extensions) {\n            var margedRegexes = {};\n            for (var i in regexes) {\n                if (extensions[i] && extensions[i].length % 2 === 0) {\n                    margedRegexes[i] = extensions[i].concat(regexes[i]);\n                } else {\n                    margedRegexes[i] = regexes[i];\n                }\n            }\n            return margedRegexes;\n        },\n        has : function (str1, str2) {\n          if (typeof str1 === \"string\") {\n            return str2.toLowerCase().indexOf(str1.toLowerCase()) !== -1;\n          } else {\n            return false;\n          }\n        },\n        lowerize : function (str) {\n            return str.toLowerCase();\n        },\n        major : function (version) {\n            return typeof(version) === STR_TYPE ? version.replace(/[^\\d\\.]/g,'').split(\".\")[0] : undefined;\n        },\n        trim : function (str) {\n          return str.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n        }\n    };\n\n\n    ///////////////\n    // Map helper\n    //////////////\n\n\n    var mapper = {\n\n        rgx : function () {\n\n            var result, i = 0, j, k, p, q, matches, match, args = arguments;\n\n            // loop through all regexes maps\n            while (i < args.length && !matches) {\n\n                var regex = args[i],       // even sequence (0,2,4,..)\n                    props = args[i + 1];   // odd sequence (1,3,5,..)\n\n                // construct object barebones\n                if (typeof result === UNDEF_TYPE) {\n                    result = {};\n                    for (p in props) {\n                        if (props.hasOwnProperty(p)){\n                            q = props[p];\n                            if (typeof q === OBJ_TYPE) {\n                                result[q[0]] = undefined;\n                            } else {\n                                result[q] = undefined;\n                            }\n                        }\n                    }\n                }\n\n                // try matching uastring with regexes\n                j = k = 0;\n                while (j < regex.length && !matches) {\n                    matches = regex[j++].exec(this.getUA());\n                    if (!!matches) {\n                        for (p = 0; p < props.length; p++) {\n                            match = matches[++k];\n                            q = props[p];\n                            // check if given property is actually array\n                            if (typeof q === OBJ_TYPE && q.length > 0) {\n                                if (q.length == 2) {\n                                    if (typeof q[1] == FUNC_TYPE) {\n                                        // assign modified match\n                                        result[q[0]] = q[1].call(this, match);\n                                    } else {\n                                        // assign given value, ignore regex match\n                                        result[q[0]] = q[1];\n                                    }\n                                } else if (q.length == 3) {\n                                    // check whether function or regex\n                                    if (typeof q[1] === FUNC_TYPE && !(q[1].exec && q[1].test)) {\n                                        // call function (usually string mapper)\n                                        result[q[0]] = match ? q[1].call(this, match, q[2]) : undefined;\n                                    } else {\n                                        // sanitize match using given regex\n                                        result[q[0]] = match ? match.replace(q[1], q[2]) : undefined;\n                                    }\n                                } else if (q.length == 4) {\n                                        result[q[0]] = match ? q[3].call(this, match.replace(q[1], q[2])) : undefined;\n                                }\n                            } else {\n                                result[q] = match ? match : undefined;\n                            }\n                        }\n                    }\n                }\n                i += 2;\n            }\n            return result;\n        },\n\n        str : function (str, map) {\n\n            for (var i in map) {\n                // check if array\n                if (typeof map[i] === OBJ_TYPE && map[i].length > 0) {\n                    for (var j = 0; j < map[i].length; j++) {\n                        if (util.has(map[i][j], str)) {\n                            return (i === UNKNOWN) ? undefined : i;\n                        }\n                    }\n                } else if (util.has(map[i], str)) {\n                    return (i === UNKNOWN) ? undefined : i;\n                }\n            }\n            return str;\n        }\n    };\n\n\n    ///////////////\n    // String map\n    //////////////\n\n\n    var maps = {\n\n        browser : {\n            oldsafari : {\n                version : {\n                    '1.0'   : '/8',\n                    '1.2'   : '/1',\n                    '1.3'   : '/3',\n                    '2.0'   : '/412',\n                    '2.0.2' : '/416',\n                    '2.0.3' : '/417',\n                    '2.0.4' : '/419',\n                    '?'     : '/'\n                }\n            }\n        },\n\n        device : {\n            amazon : {\n                model : {\n                    'Fire Phone' : ['SD', 'KF']\n                }\n            },\n            sprint : {\n                model : {\n                    'Evo Shift 4G' : '7373KT'\n                },\n                vendor : {\n                    'HTC'       : 'APA',\n                    'Sprint'    : 'Sprint'\n                }\n            }\n        },\n\n        os : {\n            windows : {\n                version : {\n                    'ME'        : '4.90',\n                    'NT 3.11'   : 'NT3.51',\n                    'NT 4.0'    : 'NT4.0',\n                    '2000'      : 'NT 5.0',\n                    'XP'        : ['NT 5.1', 'NT 5.2'],\n                    'Vista'     : 'NT 6.0',\n                    '7'         : 'NT 6.1',\n                    '8'         : 'NT 6.2',\n                    '8.1'       : 'NT 6.3',\n                    '10'        : ['NT 6.4', 'NT 10.0'],\n                    'RT'        : 'ARM'\n                }\n            }\n        }\n    };\n\n\n    //////////////\n    // Regex map\n    /////////////\n\n\n    var regexes = {\n\n        browser : [[\n\n            // Presto based\n            /(opera\\smini)\\/([\\w\\.-]+)/i,                                       // Opera Mini\n            /(opera\\s[mobiletab]+).+version\\/([\\w\\.-]+)/i,                      // Opera Mobi/Tablet\n            /(opera).+version\\/([\\w\\.]+)/i,                                     // Opera > 9.80\n            /(opera)[\\/\\s]+([\\w\\.]+)/i                                          // Opera < 9.80\n            ], [NAME, VERSION], [\n\n            /(opios)[\\/\\s]+([\\w\\.]+)/i                                          // Opera mini on iphone >= 8.0\n            ], [[NAME, 'Opera Mini'], VERSION], [\n\n            /\\s(opr)\\/([\\w\\.]+)/i                                               // Opera Webkit\n            ], [[NAME, 'Opera'], VERSION], [\n\n            // Mixed\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(lunascape|maxthon|netfront|jasmine|blazer)[\\/\\s]?([\\w\\.]+)*/i,\n                                                                                // Lunascape/Maxthon/Netfront/Jasmine/Blazer\n\n            // Trident based\n            /(avant\\s|iemobile|slim|baidu)(?:browser)?[\\/\\s]?([\\w\\.]*)/i,\n                                                                                // Avant/IEMobile/SlimBrowser/Baidu\n            /(?:ms|\\()(ie)\\s([\\w\\.]+)/i,                                        // Internet Explorer\n\n            // Webkit/KHTML based\n            /(rekonq)\\/([\\w\\.]+)*/i,                                            // Rekonq\n            /(chromium|flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs)\\/([\\w\\.-]+)/i\n                                                                                // Chromium/Flock/RockMelt/Midori/Epiphany/Silk/Skyfire/Bolt/Iron/Iridium/PhantomJS\n            ], [NAME, VERSION], [\n\n            /(trident).+rv[:\\s]([\\w\\.]+).+like\\sgecko/i                         // IE11\n            ], [[NAME, 'IE'], VERSION], [\n\n            /(edge)\\/((\\d+)?[\\w\\.]+)/i                                          // Microsoft Edge\n            ], [NAME, VERSION], [\n\n            /(yabrowser)\\/([\\w\\.]+)/i                                           // Yandex\n            ], [[NAME, 'Yandex'], VERSION], [\n\n            /(comodo_dragon)\\/([\\w\\.]+)/i                                       // Comodo Dragon\n            ], [[NAME, /_/g, ' '], VERSION], [\n\n            /(micromessenger)\\/([\\w\\.]+)/i                                      // WeChat\n            ], [[NAME, 'WeChat'], VERSION], [\n\n            /xiaomi\\/miuibrowser\\/([\\w\\.]+)/i                                   // MIUI Browser\n            ], [VERSION, [NAME, 'MIUI Browser']], [\n\n            /\\swv\\).+(chrome)\\/([\\w\\.]+)/i                                      // Chrome WebView\n            ], [[NAME, /(.+)/, '$1 WebView'], VERSION], [\n\n            /android.+samsungbrowser\\/([\\w\\.]+)/i,\n            /android.+version\\/([\\w\\.]+)\\s+(?:mobile\\s?safari|safari)*/i        // Android Browser\n            ], [VERSION, [NAME, 'Android Browser']], [\n\n            /(chrome|omniweb|arora|[tizenoka]{5}\\s?browser)\\/v?([\\w\\.]+)/i,\n                                                                                // Chrome/OmniWeb/Arora/Tizen/Nokia\n            /(qqbrowser)[\\/\\s]?([\\w\\.]+)/i\n                                                                                // QQBrowser\n            ], [NAME, VERSION], [\n\n            /(uc\\s?browser)[\\/\\s]?([\\w\\.]+)/i,\n            /ucweb.+(ucbrowser)[\\/\\s]?([\\w\\.]+)/i,\n            /juc.+(ucweb)[\\/\\s]?([\\w\\.]+)/i\n                                                                                // UCBrowser\n            ], [[NAME, 'UCBrowser'], VERSION], [\n\n            /(dolfin)\\/([\\w\\.]+)/i                                              // Dolphin\n            ], [[NAME, 'Dolphin'], VERSION], [\n\n            /((?:android.+)crmo|crios)\\/([\\w\\.]+)/i                             // Chrome for Android/iOS\n            ], [[NAME, 'Chrome'], VERSION], [\n\n            /;fbav\\/([\\w\\.]+);/i                                                // Facebook App for iOS\n            ], [VERSION, [NAME, 'Facebook']], [\n\n            /fxios\\/([\\w\\.-]+)/i                                                // Firefox for iOS\n            ], [VERSION, [NAME, 'Firefox']], [\n\n            /version\\/([\\w\\.]+).+?mobile\\/\\w+\\s(safari)/i                       // Mobile Safari\n            ], [VERSION, [NAME, 'Mobile Safari']], [\n\n            /version\\/([\\w\\.]+).+?(mobile\\s?safari|safari)/i                    // Safari & Safari Mobile\n            ], [VERSION, NAME], [\n\n            /webkit.+?(mobile\\s?safari|safari)(\\/[\\w\\.]+)/i                     // Safari < 3.0\n            ], [NAME, [VERSION, mapper.str, maps.browser.oldsafari.version]], [\n\n            /(konqueror)\\/([\\w\\.]+)/i,                                          // Konqueror\n            /(webkit|khtml)\\/([\\w\\.]+)/i\n            ], [NAME, VERSION], [\n\n            // Gecko based\n            /(navigator|netscape)\\/([\\w\\.-]+)/i                                 // Netscape\n            ], [[NAME, 'Netscape'], VERSION], [\n            /(swiftfox)/i,                                                      // Swiftfox\n            /(icedragon|iceweasel|camino|chimera|fennec|maemo\\sbrowser|minimo|conkeror)[\\/\\s]?([\\w\\.\\+]+)/i,\n                                                                                // IceDragon/Iceweasel/Camino/Chimera/Fennec/Maemo/Minimo/Conkeror\n            /(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix)\\/([\\w\\.-]+)/i,\n                                                                                // Firefox/SeaMonkey/K-Meleon/IceCat/IceApe/Firebird/Phoenix\n            /(mozilla)\\/([\\w\\.]+).+rv\\:.+gecko\\/\\d+/i,                          // Mozilla\n\n            // Other\n            /(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir)[\\/\\s]?([\\w\\.]+)/i,\n                                                                                // Polaris/Lynx/Dillo/iCab/Doris/Amaya/w3m/NetSurf/Sleipnir\n            /(links)\\s\\(([\\w\\.]+)/i,                                            // Links\n            /(gobrowser)\\/?([\\w\\.]+)*/i,                                        // GoBrowser\n            /(ice\\s?browser)\\/v?([\\w\\._]+)/i,                                   // ICE Browser\n            /(mosaic)[\\/\\s]([\\w\\.]+)/i                                          // Mosaic\n            ], [NAME, VERSION]\n\n            /* /////////////////////\n            // Media players BEGIN\n            ////////////////////////\n\n            , [\n\n            /(apple(?:coremedia|))\\/((\\d+)[\\w\\._]+)/i,                          // Generic Apple CoreMedia\n            /(coremedia) v((\\d+)[\\w\\._]+)/i\n            ], [NAME, VERSION], [\n\n            /(aqualung|lyssna|bsplayer)\\/((\\d+)?[\\w\\.-]+)/i                     // Aqualung/Lyssna/BSPlayer\n            ], [NAME, VERSION], [\n\n            /(ares|ossproxy)\\s((\\d+)[\\w\\.-]+)/i                                 // Ares/OSSProxy\n            ], [NAME, VERSION], [\n\n            /(audacious|audimusicstream|amarok|bass|core|dalvik|gnomemplayer|music on console|nsplayer|psp-internetradioplayer|videos)\\/((\\d+)[\\w\\.-]+)/i,\n                                                                                // Audacious/AudiMusicStream/Amarok/BASS/OpenCORE/Dalvik/GnomeMplayer/MoC\n                                                                                // NSPlayer/PSP-InternetRadioPlayer/Videos\n            /(clementine|music player daemon)\\s((\\d+)[\\w\\.-]+)/i,               // Clementine/MPD\n            /(lg player|nexplayer)\\s((\\d+)[\\d\\.]+)/i,\n            /player\\/(nexplayer|lg player)\\s((\\d+)[\\w\\.-]+)/i                   // NexPlayer/LG Player\n            ], [NAME, VERSION], [\n            /(nexplayer)\\s((\\d+)[\\w\\.-]+)/i                                     // Nexplayer\n            ], [NAME, VERSION], [\n\n            /(flrp)\\/((\\d+)[\\w\\.-]+)/i                                          // Flip Player\n            ], [[NAME, 'Flip Player'], VERSION], [\n\n            /(fstream|nativehost|queryseekspider|ia-archiver|facebookexternalhit)/i\n                                                                                // FStream/NativeHost/QuerySeekSpider/IA Archiver/facebookexternalhit\n            ], [NAME], [\n\n            /(gstreamer) souphttpsrc (?:\\([^\\)]+\\)){0,1} libsoup\\/((\\d+)[\\w\\.-]+)/i\n                                                                                // Gstreamer\n            ], [NAME, VERSION], [\n\n            /(htc streaming player)\\s[\\w_]+\\s\\/\\s((\\d+)[\\d\\.]+)/i,              // HTC Streaming Player\n            /(java|python-urllib|python-requests|wget|libcurl)\\/((\\d+)[\\w\\.-_]+)/i,\n                                                                                // Java/urllib/requests/wget/cURL\n            /(lavf)((\\d+)[\\d\\.]+)/i                                             // Lavf (FFMPEG)\n            ], [NAME, VERSION], [\n\n            /(htc_one_s)\\/((\\d+)[\\d\\.]+)/i                                      // HTC One S\n            ], [[NAME, /_/g, ' '], VERSION], [\n\n            /(mplayer)(?:\\s|\\/)(?:(?:sherpya-){0,1}svn)(?:-|\\s)(r\\d+(?:-\\d+[\\w\\.-]+){0,1})/i\n                                                                                // MPlayer SVN\n            ], [NAME, VERSION], [\n\n            /(mplayer)(?:\\s|\\/|[unkow-]+)((\\d+)[\\w\\.-]+)/i                      // MPlayer\n            ], [NAME, VERSION], [\n\n            /(mplayer)/i,                                                       // MPlayer (no other info)\n            /(yourmuze)/i,                                                      // YourMuze\n            /(media player classic|nero showtime)/i                             // Media Player Classic/Nero ShowTime\n            ], [NAME], [\n\n            /(nero (?:home|scout))\\/((\\d+)[\\w\\.-]+)/i                           // Nero Home/Nero Scout\n            ], [NAME, VERSION], [\n\n            /(nokia\\d+)\\/((\\d+)[\\w\\.-]+)/i                                      // Nokia\n            ], [NAME, VERSION], [\n\n            /\\s(songbird)\\/((\\d+)[\\w\\.-]+)/i                                    // Songbird/Philips-Songbird\n            ], [NAME, VERSION], [\n\n            /(winamp)3 version ((\\d+)[\\w\\.-]+)/i,                               // Winamp\n            /(winamp)\\s((\\d+)[\\w\\.-]+)/i,\n            /(winamp)mpeg\\/((\\d+)[\\w\\.-]+)/i\n            ], [NAME, VERSION], [\n\n            /(ocms-bot|tapinradio|tunein radio|unknown|winamp|inlight radio)/i  // OCMS-bot/tap in radio/tunein/unknown/winamp (no other info)\n                                                                                // inlight radio\n            ], [NAME], [\n\n            /(quicktime|rma|radioapp|radioclientapplication|soundtap|totem|stagefright|streamium)\\/((\\d+)[\\w\\.-]+)/i\n                                                                                // QuickTime/RealMedia/RadioApp/RadioClientApplication/\n                                                                                // SoundTap/Totem/Stagefright/Streamium\n            ], [NAME, VERSION], [\n\n            /(smp)((\\d+)[\\d\\.]+)/i                                              // SMP\n            ], [NAME, VERSION], [\n\n            /(vlc) media player - version ((\\d+)[\\w\\.]+)/i,                     // VLC Videolan\n            /(vlc)\\/((\\d+)[\\w\\.-]+)/i,\n            /(xbmc|gvfs|xine|xmms|irapp)\\/((\\d+)[\\w\\.-]+)/i,                    // XBMC/gvfs/Xine/XMMS/irapp\n            /(foobar2000)\\/((\\d+)[\\d\\.]+)/i,                                    // Foobar2000\n            /(itunes)\\/((\\d+)[\\d\\.]+)/i                                         // iTunes\n            ], [NAME, VERSION], [\n\n            /(wmplayer)\\/((\\d+)[\\w\\.-]+)/i,                                     // Windows Media Player\n            /(windows-media-player)\\/((\\d+)[\\w\\.-]+)/i\n            ], [[NAME, /-/g, ' '], VERSION], [\n\n            /windows\\/((\\d+)[\\w\\.-]+) upnp\\/[\\d\\.]+ dlnadoc\\/[\\d\\.]+ (home media server)/i\n                                                                                // Windows Media Server\n            ], [VERSION, [NAME, 'Windows']], [\n\n            /(com\\.riseupradioalarm)\\/((\\d+)[\\d\\.]*)/i                          // RiseUP Radio Alarm\n            ], [NAME, VERSION], [\n\n            /(rad.io)\\s((\\d+)[\\d\\.]+)/i,                                        // Rad.io\n            /(radio.(?:de|at|fr))\\s((\\d+)[\\d\\.]+)/i\n            ], [[NAME, 'rad.io'], VERSION]\n\n            //////////////////////\n            // Media players END\n            ////////////////////*/\n\n        ],\n\n        cpu : [[\n\n            /(?:(amd|x(?:(?:86|64)[_-])?|wow|win)64)[;\\)]/i                     // AMD64\n            ], [[ARCHITECTURE, 'amd64']], [\n\n            /(ia32(?=;))/i                                                      // IA32 (quicktime)\n            ], [[ARCHITECTURE, util.lowerize]], [\n\n            /((?:i[346]|x)86)[;\\)]/i                                            // IA32\n            ], [[ARCHITECTURE, 'ia32']], [\n\n            // PocketPC mistakenly identified as PowerPC\n            /windows\\s(ce|mobile);\\sppc;/i\n            ], [[ARCHITECTURE, 'arm']], [\n\n            /((?:ppc|powerpc)(?:64)?)(?:\\smac|;|\\))/i                           // PowerPC\n            ], [[ARCHITECTURE, /ower/, '', util.lowerize]], [\n\n            /(sun4\\w)[;\\)]/i                                                    // SPARC\n            ], [[ARCHITECTURE, 'sparc']], [\n\n            /((?:avr32|ia64(?=;))|68k(?=\\))|arm(?:64|(?=v\\d+;))|(?=atmel\\s)avr|(?:irix|mips|sparc)(?:64)?(?=;)|pa-risc)/i\n                                                                                // IA64, 68K, ARM/64, AVR/32, IRIX/64, MIPS/64, SPARC/64, PA-RISC\n            ], [[ARCHITECTURE, util.lowerize]]\n        ],\n\n        device : [[\n\n            /\\((ipad|playbook);[\\w\\s\\);-]+(rim|apple)/i                         // iPad/PlayBook\n            ], [MODEL, VENDOR, [TYPE, TABLET]], [\n\n            /applecoremedia\\/[\\w\\.]+ \\((ipad)/                                  // iPad\n            ], [MODEL, [VENDOR, 'Apple'], [TYPE, TABLET]], [\n\n            /(apple\\s{0,1}tv)/i                                                 // Apple TV\n            ], [[MODEL, 'Apple TV'], [VENDOR, 'Apple']], [\n\n            /(archos)\\s(gamepad2?)/i,                                           // Archos\n            /(hp).+(touchpad)/i,                                                // HP TouchPad\n            /(hp).+(tablet)/i,                                                  // HP Tablet\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /\\s(nook)[\\w\\s]+build\\/(\\w+)/i,                                     // Nook\n            /(dell)\\s(strea[kpr\\s\\d]*[\\dko])/i                                  // Dell Streak\n            ], [VENDOR, MODEL, [TYPE, TABLET]], [\n\n            /(kf[A-z]+)\\sbuild\\/[\\w\\.]+.*silk\\//i                               // Kindle Fire HD\n            ], [MODEL, [VENDOR, 'Amazon'], [TYPE, TABLET]], [\n            /(sd|kf)[0349hijorstuw]+\\sbuild\\/[\\w\\.]+.*silk\\//i                  // Fire Phone\n            ], [[MODEL, mapper.str, maps.device.amazon.model], [VENDOR, 'Amazon'], [TYPE, MOBILE]], [\n\n            /\\((ip[honed|\\s\\w*]+);.+(apple)/i                                   // iPod/iPhone\n            ], [MODEL, VENDOR, [TYPE, MOBILE]], [\n            /\\((ip[honed|\\s\\w*]+);/i                                            // iPod/iPhone\n            ], [MODEL, [VENDOR, 'Apple'], [TYPE, MOBILE]], [\n\n            /(blackberry)[\\s-]?(\\w+)/i,                                         // BlackBerry\n            /(blackberry|benq|palm(?=\\-)|sonyericsson|acer|asus|dell|huawei|meizu|motorola|polytron)[\\s_-]?([\\w-]+)*/i,\n                                                                                // BenQ/Palm/Sony-Ericsson/Acer/Asus/Dell/Huawei/Meizu/Motorola/Polytron\n            /(hp)\\s([\\w\\s]+\\w)/i,                                               // HP iPAQ\n            /(asus)-?(\\w+)/i                                                    // Asus\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n            /\\(bb10;\\s(\\w+)/i                                                   // BlackBerry 10\n            ], [MODEL, [VENDOR, 'BlackBerry'], [TYPE, MOBILE]], [\n                                                                                // Asus Tablets\n            /android.+(transfo[prime\\s]{4,10}\\s\\w+|eeepc|slider\\s\\w+|nexus 7|padfone)/i\n            ], [MODEL, [VENDOR, 'Asus'], [TYPE, TABLET]], [\n\n            /(sony)\\s(tablet\\s[ps])\\sbuild\\//i,                                  // Sony\n            /(sony)?(?:sgp.+)\\sbuild\\//i\n            ], [[VENDOR, 'Sony'], [MODEL, 'Xperia Tablet'], [TYPE, TABLET]], [\n            /(?:sony)?(?:(?:(?:c|d)\\d{4})|(?:so[-l].+))\\sbuild\\//i\n            ], [[VENDOR, 'Sony'], [MODEL, 'Xperia Phone'], [TYPE, MOBILE]], [\n\n            /\\s(ouya)\\s/i,                                                      // Ouya\n            /(nintendo)\\s([wids3u]+)/i                                          // Nintendo\n            ], [VENDOR, MODEL, [TYPE, CONSOLE]], [\n\n            /android.+;\\s(shield)\\sbuild/i                                      // Nvidia\n            ], [MODEL, [VENDOR, 'Nvidia'], [TYPE, CONSOLE]], [\n\n            /(playstation\\s[34portablevi]+)/i                                   // Playstation\n            ], [MODEL, [VENDOR, 'Sony'], [TYPE, CONSOLE]], [\n\n            /(sprint\\s(\\w+))/i                                                  // Sprint Phones\n            ], [[VENDOR, mapper.str, maps.device.sprint.vendor], [MODEL, mapper.str, maps.device.sprint.model], [TYPE, MOBILE]], [\n\n            /(lenovo)\\s?(S(?:5000|6000)+(?:[-][\\w+]))/i                         // Lenovo tablets\n            ], [VENDOR, MODEL, [TYPE, TABLET]], [\n\n            /(htc)[;_\\s-]+([\\w\\s]+(?=\\))|\\w+)*/i,                               // HTC\n            /(zte)-(\\w+)*/i,                                                    // ZTE\n            /(alcatel|geeksphone|huawei|lenovo|nexian|panasonic|(?=;\\s)sony)[_\\s-]?([\\w-]+)*/i\n                                                                                // Alcatel/GeeksPhone/Huawei/Lenovo/Nexian/Panasonic/Sony\n            ], [VENDOR, [MODEL, /_/g, ' '], [TYPE, MOBILE]], [\n\n            /(nexus\\s9)/i                                                       // HTC Nexus 9\n            ], [MODEL, [VENDOR, 'HTC'], [TYPE, TABLET]], [\n\n            /(nexus\\s6p)/i                                                      // Huawei Nexus 6P\n            ], [MODEL, [VENDOR, 'Huawei'], [TYPE, MOBILE]], [\n\n            /(microsoft);\\s(lumia[\\s\\w]+)/i                                     // Microsoft Lumia\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n\n            /[\\s\\(;](xbox(?:\\sone)?)[\\s\\);]/i                                   // Microsoft Xbox\n            ], [MODEL, [VENDOR, 'Microsoft'], [TYPE, CONSOLE]], [\n            /(kin\\.[onetw]{3})/i                                                // Microsoft Kin\n            ], [[MODEL, /\\./g, ' '], [VENDOR, 'Microsoft'], [TYPE, MOBILE]], [\n\n                                                                                // Motorola\n            /\\s(milestone|droid(?:[2-4x]|\\s(?:bionic|x2|pro|razr))?(:?\\s4g)?)[\\w\\s]+build\\//i,\n            /mot[\\s-]?(\\w+)*/i,\n            /(XT\\d{3,4}) build\\//i,\n            /(nexus\\s6)/i\n            ], [MODEL, [VENDOR, 'Motorola'], [TYPE, MOBILE]], [\n            /android.+\\s(mz60\\d|xoom[\\s2]{0,2})\\sbuild\\//i\n            ], [MODEL, [VENDOR, 'Motorola'], [TYPE, TABLET]], [\n\n            /hbbtv\\/\\d+\\.\\d+\\.\\d+\\s+\\([\\w\\s]*;\\s*(\\w[^;]*);([^;]*)/i            // HbbTV devices\n            ], [[VENDOR, util.trim], [MODEL, util.trim], [TYPE, SMARTTV]], [\n\n            /hbbtv.+maple;(\\d+)/i\n            ], [[MODEL, /^/, 'SmartTV'], [VENDOR, 'Samsung'], [TYPE, SMARTTV]], [\n\n            /\\(dtv[\\);].+(aquos)/i                                              // Sharp\n            ], [MODEL, [VENDOR, 'Sharp'], [TYPE, SMARTTV]], [\n\n            /android.+((sch-i[89]0\\d|shw-m380s|gt-p\\d{4}|gt-n\\d+|sgh-t8[56]9|nexus 10))/i,\n            /((SM-T\\w+))/i\n            ], [[VENDOR, 'Samsung'], MODEL, [TYPE, TABLET]], [                  // Samsung\n            /smart-tv.+(samsung)/i\n            ], [VENDOR, [TYPE, SMARTTV], MODEL], [\n            /((s[cgp]h-\\w+|gt-\\w+|galaxy\\snexus|sm-\\w[\\w\\d]+))/i,\n            /(sam[sung]*)[\\s-]*(\\w+-?[\\w-]*)*/i,\n            /sec-((sgh\\w+))/i\n            ], [[VENDOR, 'Samsung'], MODEL, [TYPE, MOBILE]], [\n\n            /sie-(\\w+)*/i                                                       // Siemens\n            ], [MODEL, [VENDOR, 'Siemens'], [TYPE, MOBILE]], [\n\n            /(maemo|nokia).*(n900|lumia\\s\\d+)/i,                                // Nokia\n            /(nokia)[\\s_-]?([\\w-]+)*/i\n            ], [[VENDOR, 'Nokia'], MODEL, [TYPE, MOBILE]], [\n\n            /android\\s3\\.[\\s\\w;-]{10}(a\\d{3})/i                                 // Acer\n            ], [MODEL, [VENDOR, 'Acer'], [TYPE, TABLET]], [\n\n            /android\\s3\\.[\\s\\w;-]{10}(lg?)-([06cv9]{3,4})/i                     // LG Tablet\n            ], [[VENDOR, 'LG'], MODEL, [TYPE, TABLET]], [\n            /(lg) netcast\\.tv/i                                                 // LG SmartTV\n            ], [VENDOR, MODEL, [TYPE, SMARTTV]], [\n            /(nexus\\s[45])/i,                                                   // LG\n            /lg[e;\\s\\/-]+(\\w+)*/i\n            ], [MODEL, [VENDOR, 'LG'], [TYPE, MOBILE]], [\n\n            /android.+(ideatab[a-z0-9\\-\\s]+)/i                                  // Lenovo\n            ], [MODEL, [VENDOR, 'Lenovo'], [TYPE, TABLET]], [\n\n            /linux;.+((jolla));/i                                               // Jolla\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n\n            /((pebble))app\\/[\\d\\.]+\\s/i                                         // Pebble\n            ], [VENDOR, MODEL, [TYPE, WEARABLE]], [\n\n            /android.+;\\s(glass)\\s\\d/i                                          // Google Glass\n            ], [MODEL, [VENDOR, 'Google'], [TYPE, WEARABLE]], [\n\n            /android.+(\\w+)\\s+build\\/hm\\1/i,                                    // Xiaomi Hongmi 'numeric' models\n            /android.+(hm[\\s\\-_]*note?[\\s_]*(?:\\d\\w)?)\\s+build/i,               // Xiaomi Hongmi\n            /android.+(mi[\\s\\-_]*(?:one|one[\\s_]plus|note lte)?[\\s_]*(?:\\d\\w)?)\\s+build/i    // Xiaomi Mi\n            ], [[MODEL, /_/g, ' '], [VENDOR, 'Xiaomi'], [TYPE, MOBILE]], [\n\n            /android.+a000(1)\\s+build/i                                         // OnePlus\n            ], [MODEL, [VENDOR, 'OnePlus'], [TYPE, MOBILE]], [\n\n            /\\s(tablet)[;\\/]/i,                                                 // Unidentifiable Tablet\n            /\\s(mobile)(?:[;\\/]|\\ssafari)/i                                     // Unidentifiable Mobile\n            ], [[TYPE, util.lowerize], VENDOR, MODEL]\n\n            /*//////////////////////////\n            // TODO: move to string map\n            ////////////////////////////\n\n            /(C6603)/i                                                          // Sony Xperia Z C6603\n            ], [[MODEL, 'Xperia Z C6603'], [VENDOR, 'Sony'], [TYPE, MOBILE]], [\n            /(C6903)/i                                                          // Sony Xperia Z 1\n            ], [[MODEL, 'Xperia Z 1'], [VENDOR, 'Sony'], [TYPE, MOBILE]], [\n\n            /(SM-G900[F|H])/i                                                   // Samsung Galaxy S5\n            ], [[MODEL, 'Galaxy S5'], [VENDOR, 'Samsung'], [TYPE, MOBILE]], [\n            /(SM-G7102)/i                                                       // Samsung Galaxy Grand 2\n            ], [[MODEL, 'Galaxy Grand 2'], [VENDOR, 'Samsung'], [TYPE, MOBILE]], [\n            /(SM-G530H)/i                                                       // Samsung Galaxy Grand Prime\n            ], [[MODEL, 'Galaxy Grand Prime'], [VENDOR, 'Samsung'], [TYPE, MOBILE]], [\n            /(SM-G313HZ)/i                                                      // Samsung Galaxy V\n            ], [[MODEL, 'Galaxy V'], [VENDOR, 'Samsung'], [TYPE, MOBILE]], [\n            /(SM-T805)/i                                                        // Samsung Galaxy Tab S 10.5\n            ], [[MODEL, 'Galaxy Tab S 10.5'], [VENDOR, 'Samsung'], [TYPE, TABLET]], [\n            /(SM-G800F)/i                                                       // Samsung Galaxy S5 Mini\n            ], [[MODEL, 'Galaxy S5 Mini'], [VENDOR, 'Samsung'], [TYPE, MOBILE]], [\n            /(SM-T311)/i                                                        // Samsung Galaxy Tab 3 8.0\n            ], [[MODEL, 'Galaxy Tab 3 8.0'], [VENDOR, 'Samsung'], [TYPE, TABLET]], [\n\n            /(R1001)/i                                                          // Oppo R1001\n            ], [MODEL, [VENDOR, 'OPPO'], [TYPE, MOBILE]], [\n            /(X9006)/i                                                          // Oppo Find 7a\n            ], [[MODEL, 'Find 7a'], [VENDOR, 'Oppo'], [TYPE, MOBILE]], [\n            /(R2001)/i                                                          // Oppo YOYO R2001\n            ], [[MODEL, 'Yoyo R2001'], [VENDOR, 'Oppo'], [TYPE, MOBILE]], [\n            /(R815)/i                                                           // Oppo Clover R815\n            ], [[MODEL, 'Clover R815'], [VENDOR, 'Oppo'], [TYPE, MOBILE]], [\n             /(U707)/i                                                          // Oppo Find Way S\n            ], [[MODEL, 'Find Way S'], [VENDOR, 'Oppo'], [TYPE, MOBILE]], [\n\n            /(T3C)/i                                                            // Advan Vandroid T3C\n            ], [MODEL, [VENDOR, 'Advan'], [TYPE, TABLET]], [\n            /(ADVAN T1J\\+)/i                                                    // Advan Vandroid T1J+\n            ], [[MODEL, 'Vandroid T1J+'], [VENDOR, 'Advan'], [TYPE, TABLET]], [\n            /(ADVAN S4A)/i                                                      // Advan Vandroid S4A\n            ], [[MODEL, 'Vandroid S4A'], [VENDOR, 'Advan'], [TYPE, MOBILE]], [\n\n            /(V972M)/i                                                          // ZTE V972M\n            ], [MODEL, [VENDOR, 'ZTE'], [TYPE, MOBILE]], [\n\n            /(i-mobile)\\s(IQ\\s[\\d\\.]+)/i                                        // i-mobile IQ\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n            /(IQ6.3)/i                                                          // i-mobile IQ IQ 6.3\n            ], [[MODEL, 'IQ 6.3'], [VENDOR, 'i-mobile'], [TYPE, MOBILE]], [\n            /(i-mobile)\\s(i-style\\s[\\d\\.]+)/i                                   // i-mobile i-STYLE\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n            /(i-STYLE2.1)/i                                                     // i-mobile i-STYLE 2.1\n            ], [[MODEL, 'i-STYLE 2.1'], [VENDOR, 'i-mobile'], [TYPE, MOBILE]], [\n\n            /(mobiistar touch LAI 512)/i                                        // mobiistar touch LAI 512\n            ], [[MODEL, 'Touch LAI 512'], [VENDOR, 'mobiistar'], [TYPE, MOBILE]], [\n\n            /////////////\n            // END TODO\n            ///////////*/\n\n        ],\n\n        engine : [[\n\n            /windows.+\\sedge\\/([\\w\\.]+)/i                                       // EdgeHTML\n            ], [VERSION, [NAME, 'EdgeHTML']], [\n\n            /(presto)\\/([\\w\\.]+)/i,                                             // Presto\n            /(webkit|trident|netfront|netsurf|amaya|lynx|w3m)\\/([\\w\\.]+)/i,     // WebKit/Trident/NetFront/NetSurf/Amaya/Lynx/w3m\n            /(khtml|tasman|links)[\\/\\s]\\(?([\\w\\.]+)/i,                          // KHTML/Tasman/Links\n            /(icab)[\\/\\s]([23]\\.[\\d\\.]+)/i                                      // iCab\n            ], [NAME, VERSION], [\n\n            /rv\\:([\\w\\.]+).*(gecko)/i                                           // Gecko\n            ], [VERSION, NAME]\n        ],\n\n        os : [[\n\n            // Windows based\n            /microsoft\\s(windows)\\s(vista|xp)/i                                 // Windows (iTunes)\n            ], [NAME, VERSION], [\n            /(windows)\\snt\\s6\\.2;\\s(arm)/i,                                     // Windows RT\n            /(windows\\sphone(?:\\sos)*)[\\s\\/]?([\\d\\.\\s]+\\w)*/i,                  // Windows Phone\n            /(windows\\smobile|windows)[\\s\\/]?([ntce\\d\\.\\s]+\\w)/i\n            ], [NAME, [VERSION, mapper.str, maps.os.windows.version]], [\n            /(win(?=3|9|n)|win\\s9x\\s)([nt\\d\\.]+)/i\n            ], [[NAME, 'Windows'], [VERSION, mapper.str, maps.os.windows.version]], [\n\n            // Mobile/Embedded OS\n            /\\((bb)(10);/i                                                      // BlackBerry 10\n            ], [[NAME, 'BlackBerry'], VERSION], [\n            /(blackberry)\\w*\\/?([\\w\\.]+)*/i,                                    // Blackberry\n            /(tizen)[\\/\\s]([\\w\\.]+)/i,                                          // Tizen\n            /(android|webos|palm\\sos|qnx|bada|rim\\stablet\\sos|meego|contiki)[\\/\\s-]?([\\w\\.]+)*/i,\n                                                                                // Android/WebOS/Palm/QNX/Bada/RIM/MeeGo/Contiki\n            /linux;.+(sailfish);/i                                              // Sailfish OS\n            ], [NAME, VERSION], [\n            /(symbian\\s?os|symbos|s60(?=;))[\\/\\s-]?([\\w\\.]+)*/i                 // Symbian\n            ], [[NAME, 'Symbian'], VERSION], [\n            /\\((series40);/i                                                    // Series 40\n            ], [NAME], [\n            /mozilla.+\\(mobile;.+gecko.+firefox/i                               // Firefox OS\n            ], [[NAME, 'Firefox OS'], VERSION], [\n\n            // Console\n            /(nintendo|playstation)\\s([wids34portablevu]+)/i,                   // Nintendo/Playstation\n\n            // GNU/Linux based\n            /(mint)[\\/\\s\\(]?(\\w+)*/i,                                           // Mint\n            /(mageia|vectorlinux)[;\\s]/i,                                       // Mageia/VectorLinux\n            /(joli|[kxln]?ubuntu|debian|[open]*suse|gentoo|(?=\\s)arch|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk|linpus)[\\/\\s-]?(?!chrom)([\\w\\.-]+)*/i,\n                                                                                // Joli/Ubuntu/Debian/SUSE/Gentoo/Arch/Slackware\n                                                                                // Fedora/Mandriva/CentOS/PCLinuxOS/RedHat/Zenwalk/Linpus\n            /(hurd|linux)\\s?([\\w\\.]+)*/i,                                       // Hurd/Linux\n            /(gnu)\\s?([\\w\\.]+)*/i                                               // GNU\n            ], [NAME, VERSION], [\n\n            /(cros)\\s[\\w]+\\s([\\w\\.]+\\w)/i                                       // Chromium OS\n            ], [[NAME, 'Chromium OS'], VERSION],[\n\n            // Solaris\n            /(sunos)\\s?([\\w\\.]+\\d)*/i                                           // Solaris\n            ], [[NAME, 'Solaris'], VERSION], [\n\n            // BSD based\n            /\\s([frentopc-]{0,4}bsd|dragonfly)\\s?([\\w\\.]+)*/i                   // FreeBSD/NetBSD/OpenBSD/PC-BSD/DragonFly\n            ], [NAME, VERSION],[\n\n            /(haiku)\\s(\\w+)/i                                                  // Haiku\n            ], [NAME, VERSION],[\n\n            /(ip[honead]+)(?:.*os\\s([\\w]+)*\\slike\\smac|;\\sopera)/i              // iOS\n            ], [[NAME, 'iOS'], [VERSION, /_/g, '.']], [\n\n            /(mac\\sos\\sx)\\s?([\\w\\s\\.]+\\w)*/i,\n            /(macintosh|mac(?=_powerpc)\\s)/i                                    // Mac OS\n            ], [[NAME, 'Mac OS'], [VERSION, /_/g, '.']], [\n\n            // Other\n            /((?:open)?solaris)[\\/\\s-]?([\\w\\.]+)*/i,                            // Solaris\n            /(aix)\\s((\\d)(?=\\.|\\)|\\s)[\\w\\.]*)*/i,                               // AIX\n            /(plan\\s9|minix|beos|os\\/2|amigaos|morphos|risc\\sos|openvms)/i,\n                                                                                // Plan9/Minix/BeOS/OS2/AmigaOS/MorphOS/RISCOS/OpenVMS\n            /(unix)\\s?([\\w\\.]+)*/i                                              // UNIX\n            ], [NAME, VERSION]\n        ]\n    };\n\n\n    /////////////////\n    // Constructor\n    ////////////////\n\n\n    var UAParser = function (uastring, extensions) {\n\n        if (!(this instanceof UAParser)) {\n            return new UAParser(uastring, extensions).getResult();\n        }\n\n        var ua = uastring || ((window && window.navigator && window.navigator.userAgent) ? window.navigator.userAgent : EMPTY);\n        var rgxmap = extensions ? util.extend(regexes, extensions) : regexes;\n\n        this.getBrowser = function () {\n            var browser = mapper.rgx.apply(this, rgxmap.browser);\n            browser.major = util.major(browser.version);\n            return browser;\n        };\n        this.getCPU = function () {\n            return mapper.rgx.apply(this, rgxmap.cpu);\n        };\n        this.getDevice = function () {\n            return mapper.rgx.apply(this, rgxmap.device);\n        };\n        this.getEngine = function () {\n            return mapper.rgx.apply(this, rgxmap.engine);\n        };\n        this.getOS = function () {\n            return mapper.rgx.apply(this, rgxmap.os);\n        };\n        this.getResult = function() {\n            return {\n                ua      : this.getUA(),\n                browser : this.getBrowser(),\n                engine  : this.getEngine(),\n                os      : this.getOS(),\n                device  : this.getDevice(),\n                cpu     : this.getCPU()\n            };\n        };\n        this.getUA = function () {\n            return ua;\n        };\n        this.setUA = function (uastring) {\n            ua = uastring;\n            return this;\n        };\n        return this;\n    };\n\n    UAParser.VERSION = LIBVERSION;\n    UAParser.BROWSER = {\n        NAME    : NAME,\n        MAJOR   : MAJOR, // deprecated\n        VERSION : VERSION\n    };\n    UAParser.CPU = {\n        ARCHITECTURE : ARCHITECTURE\n    };\n    UAParser.DEVICE = {\n        MODEL   : MODEL,\n        VENDOR  : VENDOR,\n        TYPE    : TYPE,\n        CONSOLE : CONSOLE,\n        MOBILE  : MOBILE,\n        SMARTTV : SMARTTV,\n        TABLET  : TABLET,\n        WEARABLE: WEARABLE,\n        EMBEDDED: EMBEDDED\n    };\n    UAParser.ENGINE = {\n        NAME    : NAME,\n        VERSION : VERSION\n    };\n    UAParser.OS = {\n        NAME    : NAME,\n        VERSION : VERSION\n    };\n\n\n    ///////////\n    // Export\n    //////////\n\n\n    // check js environment\n    if (typeof(exports) !== UNDEF_TYPE) {\n        // nodejs env\n        if (typeof module !== UNDEF_TYPE && module.exports) {\n            exports = module.exports = UAParser;\n        }\n        exports.UAParser = UAParser;\n    } else {\n        // requirejs env (optional)\n        if (typeof(define) === FUNC_TYPE && define.amd) {\n            define(function () {\n                return UAParser;\n            });\n        } else {\n            // browser env\n            window.UAParser = UAParser;\n        }\n    }\n\n    // jQuery/Zepto specific (optional)\n    // Note:\n    //   In AMD env the global scope should be kept clean, but jQuery is an exception.\n    //   jQuery always exports to global scope, unless jQuery.noConflict(true) is used,\n    //   and we should catch that.\n    var $ = window.jQuery || window.Zepto;\n    if (typeof $ !== UNDEF_TYPE) {\n        var parser = new UAParser();\n        $.ua = parser.getResult();\n        $.ua.get = function() {\n            return parser.getUA();\n        };\n        $.ua.set = function (uastring) {\n            parser.setUA(uastring);\n            var result = parser.getResult();\n            for (var prop in result) {\n                $.ua[prop] = result[prop];\n            }\n        };\n    }\n\n})(typeof window === 'object' ? window : this);\n"], "mappings": ";;;;;AAAA;AAAA;AASA,KAAC,SAAUA,SAAQ,WAAW;AAE1B;AAOA,UAAI,aAAc,UACd,QAAc,IACd,UAAc,KACd,YAAc,YACd,aAAc,aACd,WAAc,UACd,WAAc,UACd,QAAc,SACd,QAAc,SACd,OAAc,QACd,OAAc,QACd,SAAc,UACd,UAAc,WACd,eAAc,gBACd,UAAc,WACd,SAAc,UACd,SAAc,UACd,UAAc,WACd,WAAc,YACd,WAAc;AAQlB,UAAI,OAAO;AAAA,QACP,QAAS,SAAUC,UAAS,YAAY;AACpC,cAAI,gBAAgB,CAAC;AACrB,mBAAS,KAAKA,UAAS;AACnB,gBAAI,WAAW,CAAC,KAAK,WAAW,CAAC,EAAE,SAAS,MAAM,GAAG;AACjD,4BAAc,CAAC,IAAI,WAAW,CAAC,EAAE,OAAOA,SAAQ,CAAC,CAAC;AAAA,YACtD,OAAO;AACH,4BAAc,CAAC,IAAIA,SAAQ,CAAC;AAAA,YAChC;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA,QACA,KAAM,SAAU,MAAM,MAAM;AAC1B,cAAI,OAAO,SAAS,UAAU;AAC5B,mBAAO,KAAK,YAAY,EAAE,QAAQ,KAAK,YAAY,CAAC,MAAM;AAAA,UAC5D,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,UAAW,SAAU,KAAK;AACtB,iBAAO,IAAI,YAAY;AAAA,QAC3B;AAAA,QACA,OAAQ,SAAU,SAAS;AACvB,iBAAO,OAAO,YAAa,WAAW,QAAQ,QAAQ,YAAW,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,QACzF;AAAA,QACA,MAAO,SAAU,KAAK;AACpB,iBAAO,IAAI,QAAQ,sCAAsC,EAAE;AAAA,QAC7D;AAAA,MACJ;AAQA,UAAI,SAAS;AAAA,QAET,KAAM,WAAY;AAEd,cAAI,QAAQ,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,SAAS,OAAO,OAAO;AAGtD,iBAAO,IAAI,KAAK,UAAU,CAAC,SAAS;AAEhC,gBAAI,QAAQ,KAAK,CAAC,GACd,QAAQ,KAAK,IAAI,CAAC;AAGtB,gBAAI,OAAO,WAAW,YAAY;AAC9B,uBAAS,CAAC;AACV,mBAAK,KAAK,OAAO;AACb,oBAAI,MAAM,eAAe,CAAC,GAAE;AACxB,sBAAI,MAAM,CAAC;AACX,sBAAI,OAAO,MAAM,UAAU;AACvB,2BAAO,EAAE,CAAC,CAAC,IAAI;AAAA,kBACnB,OAAO;AACH,2BAAO,CAAC,IAAI;AAAA,kBAChB;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ;AAGA,gBAAI,IAAI;AACR,mBAAO,IAAI,MAAM,UAAU,CAAC,SAAS;AACjC,wBAAU,MAAM,GAAG,EAAE,KAAK,KAAK,MAAM,CAAC;AACtC,kBAAI,CAAC,CAAC,SAAS;AACX,qBAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC/B,0BAAQ,QAAQ,EAAE,CAAC;AACnB,sBAAI,MAAM,CAAC;AAEX,sBAAI,OAAO,MAAM,YAAY,EAAE,SAAS,GAAG;AACvC,wBAAI,EAAE,UAAU,GAAG;AACf,0BAAI,OAAO,EAAE,CAAC,KAAK,WAAW;AAE1B,+BAAO,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,MAAM,KAAK;AAAA,sBACxC,OAAO;AAEH,+BAAO,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;AAAA,sBACtB;AAAA,oBACJ,WAAW,EAAE,UAAU,GAAG;AAEtB,0BAAI,OAAO,EAAE,CAAC,MAAM,aAAa,EAAE,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,OAAO;AAExD,+BAAO,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC,EAAE,KAAK,MAAM,OAAO,EAAE,CAAC,CAAC,IAAI;AAAA,sBAC1D,OAAO;AAEH,+BAAO,EAAE,CAAC,CAAC,IAAI,QAAQ,MAAM,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI;AAAA,sBACvD;AAAA,oBACJ,WAAW,EAAE,UAAU,GAAG;AAClB,6BAAO,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC,EAAE,KAAK,MAAM,MAAM,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI;AAAA,oBAC5E;AAAA,kBACJ,OAAO;AACH,2BAAO,CAAC,IAAI,QAAQ,QAAQ;AAAA,kBAChC;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ;AACA,iBAAK;AAAA,UACT;AACA,iBAAO;AAAA,QACX;AAAA,QAEA,KAAM,SAAU,KAAK,KAAK;AAEtB,mBAAS,KAAK,KAAK;AAEf,gBAAI,OAAO,IAAI,CAAC,MAAM,YAAY,IAAI,CAAC,EAAE,SAAS,GAAG;AACjD,uBAAS,IAAI,GAAG,IAAI,IAAI,CAAC,EAAE,QAAQ,KAAK;AACpC,oBAAI,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG;AAC1B,yBAAQ,MAAM,UAAW,YAAY;AAAA,gBACzC;AAAA,cACJ;AAAA,YACJ,WAAW,KAAK,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG;AAC9B,qBAAQ,MAAM,UAAW,YAAY;AAAA,YACzC;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA,MACJ;AAQA,UAAI,OAAO;AAAA,QAEP,SAAU;AAAA,UACN,WAAY;AAAA,YACR,SAAU;AAAA,cACN,OAAU;AAAA,cACV,OAAU;AAAA,cACV,OAAU;AAAA,cACV,OAAU;AAAA,cACV,SAAU;AAAA,cACV,SAAU;AAAA,cACV,SAAU;AAAA,cACV,KAAU;AAAA,YACd;AAAA,UACJ;AAAA,QACJ;AAAA,QAEA,QAAS;AAAA,UACL,QAAS;AAAA,YACL,OAAQ;AAAA,cACJ,cAAe,CAAC,MAAM,IAAI;AAAA,YAC9B;AAAA,UACJ;AAAA,UACA,QAAS;AAAA,YACL,OAAQ;AAAA,cACJ,gBAAiB;AAAA,YACrB;AAAA,YACA,QAAS;AAAA,cACL,OAAc;AAAA,cACd,UAAc;AAAA,YAClB;AAAA,UACJ;AAAA,QACJ;AAAA,QAEA,IAAK;AAAA,UACD,SAAU;AAAA,YACN,SAAU;AAAA,cACN,MAAc;AAAA,cACd,WAAc;AAAA,cACd,UAAc;AAAA,cACd,QAAc;AAAA,cACd,MAAc,CAAC,UAAU,QAAQ;AAAA,cACjC,SAAc;AAAA,cACd,KAAc;AAAA,cACd,KAAc;AAAA,cACd,OAAc;AAAA,cACd,MAAc,CAAC,UAAU,SAAS;AAAA,cAClC,MAAc;AAAA,YAClB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAQA,UAAI,UAAU;AAAA,QAEV,SAAU;AAAA,UAAC;AAAA;AAAA,YAGP;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YAEpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,YAAY,GAAG,OAAO;AAAA,UAAG;AAAA,YAEpC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,OAAO,GAAG,OAAO;AAAA,UAAG;AAAA;AAAA,YAG/B;AAAA;AAAA,YACA;AAAA;AAAA;AAAA,YAIA;AAAA;AAAA,YAEA;AAAA;AAAA;AAAA,YAGA;AAAA;AAAA,YACA;AAAA;AAAA,UAEA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YAEpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,IAAI,GAAG,OAAO;AAAA,UAAG;AAAA,YAE5B;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YAEpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,QAAQ,GAAG,OAAO;AAAA,UAAG;AAAA,YAEhC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,MAAM,GAAG,GAAG,OAAO;AAAA,UAAG;AAAA,YAEjC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,QAAQ,GAAG,OAAO;AAAA,UAAG;AAAA,YAEhC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,cAAc,CAAC;AAAA,UAAG;AAAA,YAEtC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,QAAQ,YAAY,GAAG,OAAO;AAAA,UAAG;AAAA,YAE5C;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,iBAAiB,CAAC;AAAA,UAAG;AAAA,YAEzC;AAAA;AAAA,YAEA;AAAA;AAAA,UAEA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YAEpB;AAAA,YACA;AAAA,YACA;AAAA;AAAA,UAEA;AAAA,UAAG,CAAC,CAAC,MAAM,WAAW,GAAG,OAAO;AAAA,UAAG;AAAA,YAEnC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,SAAS,GAAG,OAAO;AAAA,UAAG;AAAA,YAEjC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,QAAQ,GAAG,OAAO;AAAA,UAAG;AAAA,YAEhC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,UAAU,CAAC;AAAA,UAAG;AAAA,YAElC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC;AAAA,UAAG;AAAA,YAEjC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,eAAe,CAAC;AAAA,UAAG;AAAA,YAEvC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,IAAI;AAAA,UAAG;AAAA,YAEpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,CAAC,SAAS,OAAO,KAAK,KAAK,QAAQ,UAAU,OAAO,CAAC;AAAA,UAAG;AAAA,YAElE;AAAA;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA;AAAA,YAGpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,UAAU,GAAG,OAAO;AAAA,UAAG;AAAA,YAClC;AAAA;AAAA,YACA;AAAA;AAAA,YAEA;AAAA;AAAA,YAEA;AAAA;AAAA;AAAA,YAGA;AAAA;AAAA,YAEA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAgHrB;AAAA,QAEA,KAAM;AAAA,UAAC;AAAA,YAEH;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,cAAc,OAAO,CAAC;AAAA,UAAG;AAAA,YAE9B;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,cAAc,KAAK,QAAQ,CAAC;AAAA,UAAG;AAAA,YAEpC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,cAAc,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAG7B;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,cAAc,KAAK,CAAC;AAAA,UAAG;AAAA,YAE5B;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,cAAc,QAAQ,IAAI,KAAK,QAAQ,CAAC;AAAA,UAAG;AAAA,YAEhD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,cAAc,OAAO,CAAC;AAAA,UAAG;AAAA,YAE9B;AAAA;AAAA,UAEA;AAAA,UAAG,CAAC,CAAC,cAAc,KAAK,QAAQ,CAAC;AAAA,QACrC;AAAA,QAEA,QAAS;AAAA,UAAC;AAAA,YAEN;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,QAAQ,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAEpC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAE/C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,OAAO,UAAU,GAAG,CAAC,QAAQ,OAAO,CAAC;AAAA,UAAG;AAAA,YAE7C;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAEpC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAChD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,OAAO,OAAO,KAAK,KAAK,OAAO,OAAO,KAAK,GAAG,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAExF;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,QAAQ,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YACpC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAE/C;AAAA;AAAA,YACA;AAAA;AAAA,YAEA;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YACpC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,YAAY,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAEpD;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAE9C;AAAA;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,QAAQ,MAAM,GAAG,CAAC,OAAO,eAAe,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YACjE;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,QAAQ,MAAM,GAAG,CAAC,OAAO,cAAc,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAEhE;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAErC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAEjD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAE/C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,QAAQ,OAAO,KAAK,KAAK,OAAO,OAAO,MAAM,GAAG,CAAC,OAAO,OAAO,KAAK,KAAK,OAAO,OAAO,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAErH;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAEpC;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,UAEA;AAAA,UAAG,CAAC,QAAQ,CAAC,OAAO,MAAM,GAAG,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAEjD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAE7C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAEhD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAEpC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,WAAW,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YACpD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,OAAO,OAAO,GAAG,GAAG,CAAC,QAAQ,WAAW,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YAGjE;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAClD;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAElD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,QAAQ,KAAK,IAAI,GAAG,CAAC,OAAO,KAAK,IAAI,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAE/D;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,OAAO,KAAK,SAAS,GAAG,CAAC,QAAQ,SAAS,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAEpE;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YAEhD;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,QAAQ,SAAS,GAAG,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA;AAAA,YACjD;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,CAAC,MAAM,OAAO,GAAG,KAAK;AAAA,UAAG;AAAA,YACrC;AAAA,YACA;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,QAAQ,SAAS,GAAG,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAEjD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,SAAS,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAEjD;AAAA;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,QAAQ,OAAO,GAAG,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAE/C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAE9C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,QAAQ,IAAI,GAAG,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAC5C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,UAAG;AAAA,YACrC;AAAA;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAE5C;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAEhD;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAEpC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YAEtC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,UAAG;AAAA,YAElD;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,OAAO,MAAM,GAAG,GAAG,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAE7D;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,OAAO,CAAC,QAAQ,SAAS,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,UAAG;AAAA,YAEjD;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,KAAK,QAAQ,GAAG,QAAQ,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QA+D5C;AAAA,QAEA,QAAS;AAAA,UAAC;AAAA,YAEN;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,CAAC,MAAM,UAAU,CAAC;AAAA,UAAG;AAAA,YAElC;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YAEpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,SAAS,IAAI;AAAA,QACrB;AAAA,QAEA,IAAK;AAAA,UAAC;AAAA;AAAA,YAGF;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YACpB;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,CAAC,SAAS,OAAO,KAAK,KAAK,GAAG,QAAQ,OAAO,CAAC;AAAA,UAAG;AAAA,YAC3D;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,SAAS,GAAG,CAAC,SAAS,OAAO,KAAK,KAAK,GAAG,QAAQ,OAAO,CAAC;AAAA,UAAG;AAAA;AAAA,YAGxE;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,YAAY,GAAG,OAAO;AAAA,UAAG;AAAA,YACpC;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YAEA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YACpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,SAAS,GAAG,OAAO;AAAA,UAAG;AAAA,YACjC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,IAAI;AAAA,UAAG;AAAA,YACX;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,YAAY,GAAG,OAAO;AAAA,UAAG;AAAA;AAAA,YAGpC;AAAA;AAAA;AAAA,YAGA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA;AAAA,YAGA;AAAA;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAG;AAAA,YAEpB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,aAAa,GAAG,OAAO;AAAA,UAAE;AAAA;AAAA,YAGpC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,SAAS,GAAG,OAAO;AAAA,UAAG;AAAA;AAAA,YAGjC;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAE;AAAA,YAEnB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,UAAE;AAAA,YAEnB;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,KAAK,GAAG,CAAC,SAAS,MAAM,GAAG,CAAC;AAAA,UAAG;AAAA,YAE1C;AAAA,YACA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,CAAC,MAAM,QAAQ,GAAG,CAAC,SAAS,MAAM,GAAG,CAAC;AAAA,UAAG;AAAA;AAAA,YAG7C;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YAEA;AAAA;AAAA,UACA;AAAA,UAAG,CAAC,MAAM,OAAO;AAAA,QACrB;AAAA,MACJ;AAQA,UAAI,WAAW,SAAU,UAAU,YAAY;AAE3C,YAAI,EAAE,gBAAgB,WAAW;AAC7B,iBAAO,IAAI,SAAS,UAAU,UAAU,EAAE,UAAU;AAAA,QACxD;AAEA,YAAI,KAAK,aAAcD,WAAUA,QAAO,aAAaA,QAAO,UAAU,YAAaA,QAAO,UAAU,YAAY;AAChH,YAAI,SAAS,aAAa,KAAK,OAAO,SAAS,UAAU,IAAI;AAE7D,aAAK,aAAa,WAAY;AAC1B,cAAI,UAAU,OAAO,IAAI,MAAM,MAAM,OAAO,OAAO;AACnD,kBAAQ,QAAQ,KAAK,MAAM,QAAQ,OAAO;AAC1C,iBAAO;AAAA,QACX;AACA,aAAK,SAAS,WAAY;AACtB,iBAAO,OAAO,IAAI,MAAM,MAAM,OAAO,GAAG;AAAA,QAC5C;AACA,aAAK,YAAY,WAAY;AACzB,iBAAO,OAAO,IAAI,MAAM,MAAM,OAAO,MAAM;AAAA,QAC/C;AACA,aAAK,YAAY,WAAY;AACzB,iBAAO,OAAO,IAAI,MAAM,MAAM,OAAO,MAAM;AAAA,QAC/C;AACA,aAAK,QAAQ,WAAY;AACrB,iBAAO,OAAO,IAAI,MAAM,MAAM,OAAO,EAAE;AAAA,QAC3C;AACA,aAAK,YAAY,WAAW;AACxB,iBAAO;AAAA,YACH,IAAU,KAAK,MAAM;AAAA,YACrB,SAAU,KAAK,WAAW;AAAA,YAC1B,QAAU,KAAK,UAAU;AAAA,YACzB,IAAU,KAAK,MAAM;AAAA,YACrB,QAAU,KAAK,UAAU;AAAA,YACzB,KAAU,KAAK,OAAO;AAAA,UAC1B;AAAA,QACJ;AACA,aAAK,QAAQ,WAAY;AACrB,iBAAO;AAAA,QACX;AACA,aAAK,QAAQ,SAAUE,WAAU;AAC7B,eAAKA;AACL,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAEA,eAAS,UAAU;AACnB,eAAS,UAAU;AAAA,QACf;AAAA,QACA;AAAA;AAAA,QACA;AAAA,MACJ;AACA,eAAS,MAAM;AAAA,QACX;AAAA,MACJ;AACA,eAAS,SAAS;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AACA,eAAS,SAAS;AAAA,QACd;AAAA,QACA;AAAA,MACJ;AACA,eAAS,KAAK;AAAA,QACV;AAAA,QACA;AAAA,MACJ;AASA,UAAI,OAAO,YAAa,YAAY;AAEhC,YAAI,OAAO,WAAW,cAAc,OAAO,SAAS;AAChD,oBAAU,OAAO,UAAU;AAAA,QAC/B;AACA,gBAAQ,WAAW;AAAA,MACvB,OAAO;AAEH,YAAI,OAAO,WAAY,aAAa,OAAO,KAAK;AAC5C,iBAAO,WAAY;AACf,mBAAO;AAAA,UACX,CAAC;AAAA,QACL,OAAO;AAEH,UAAAF,QAAO,WAAW;AAAA,QACtB;AAAA,MACJ;AAOA,UAAI,IAAIA,QAAO,UAAUA,QAAO;AAChC,UAAI,OAAO,MAAM,YAAY;AACzB,YAAI,SAAS,IAAI,SAAS;AAC1B,UAAE,KAAK,OAAO,UAAU;AACxB,UAAE,GAAG,MAAM,WAAW;AAClB,iBAAO,OAAO,MAAM;AAAA,QACxB;AACA,UAAE,GAAG,MAAM,SAAU,UAAU;AAC3B,iBAAO,MAAM,QAAQ;AACrB,cAAI,SAAS,OAAO,UAAU;AAC9B,mBAAS,QAAQ,QAAQ;AACrB,cAAE,GAAG,IAAI,IAAI,OAAO,IAAI;AAAA,UAC5B;AAAA,QACJ;AAAA,MACJ;AAAA,IAEJ,GAAG,OAAO,WAAW,WAAW,SAAS,OAAI;AAAA;AAAA;", "names": ["window", "regexes", "<PERSON><PERSON><PERSON>"]}
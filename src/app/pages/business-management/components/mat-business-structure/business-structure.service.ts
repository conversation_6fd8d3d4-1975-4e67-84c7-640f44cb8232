import { Injectable } from '@angular/core';
import moment from 'moment';
import { Observable, of, Subject } from 'rxjs';
import { map, switchMap, take } from 'rxjs/operators';
import { Entity } from 'src/app/common/models/entity.model';
import { CsvSchema, CsvService } from '../../../../common/services/csv.service';
import { EntityService } from '../../../../common/services/entity.service';
import { EntityShortInterface } from '../../../../common/typings';
import { StructureEntityModel } from './structure-entity.model';

export interface EntityChangeUpdateData {
  statusUpdating?: boolean;
  merchantLoading?: boolean;
  entityUpdate?: boolean;
  showSettingsLink?: boolean;
  entity: StructureEntityModel;
  force?: boolean;
}

export const SHORT_STRUCTURE_ADDITIONAL = {
  businessStructure: 'defaultCountry,defaultCurrency,defaultLanguage',
  bulkActions: 'dynamicDomainId,staticDomainId,environment'
};

export interface ExtendedEntity extends Entity {
  parentId?: string;
  children: string[];
  level: number;
}

export const bsActions = {
  SHOW_DIALOG: 'showDialog'
};

export const bsDialogs = {
  EDIT_REGIONAL: 'editRegional',
  SET_STATUS_CONFIRM: 'setStatusConfirm',
  ENTITY_ADD: 'entityAdd',
  ENTITY_EDIT: 'entityEdit',
};

const csvSchema: CsvSchema[] = [
  {
    name: 'id',
    title: 'ID'
  },
  {
    name: 'decryptedBrand',
    title: 'Decrypted ID'
  },
  {
    name: 'path',
    title: 'Path'
  },
  {
    name: 'title',
    title: 'Title'
  },
  {
    name: 'name',
    title: 'Name'
  },
  {
    name: 'type',
    title: 'Type'
  },
  {
    name: 'status',
    title: 'Status'
  },
  {
    name: 'key',
    title: 'Secret Key'
  },
  {
    name: 'jurisdiction_code',
    title: 'Jurisdiction code',
    transform(_: any, rows: any): string {
      return rows.jurisdiction?.code || '';
    }
  },
  {
    name: 'jurisdiction_name',
    title: 'Jurisdiction name',
    transform(_: any, rows: any): string {
      return rows.jurisdiction?.title || '';
    }
  }
];

@Injectable({
  providedIn: 'root'
})
export class BusinessStructureService {
  entities = [];
  entitiesObject;
  expandedEntities = new Map<string, boolean>();

  private _actions: Subject<any> = new Subject();
  private _maxLength = 0;

  constructor(private entityService: EntityService<Entity>,
    private csvService: CsvService) {
  }

  updateEntityStructure(force: boolean = false) {
    return this.entityService.getShortStructure(SHORT_STRUCTURE_ADDITIONAL.businessStructure, force)
      .pipe(
        take(1),
        map(str => {
          this.setStructure(str);
          return this.entities;
        })
      );
  }

  getEntity(parentId: string) {
    return parentId && this.entitiesObject[parentId] ? this.entitiesObject[parentId] : null;
  }

  getAllParents(entityFirst: Entity, oldestFirst: boolean = true): Entity[] {
    let parents = [];

    const fetchParent = (entity: Entity) => {
      const { parentId } = entity;
      return this.getEntity(parentId);
    };

    const getParentsArray = (entity: Entity, result: Entity[]) => {
      const parent = fetchParent(entity);

      if (parent !== null) {
        result = getParentsArray(parent, oldestFirst ? [parent, ...result] : [...result, parent]);
      }

      return result;
    };

    return getParentsArray(entityFirst, parents);
  }

  setStructure(structure) {
    this.entities = this.convertStructure(structure);
    this.entitiesObject = this.entities.reduce((res: Record<string, ExtendedEntity>, item) => {
      res[item.id] = item;

      return res;
    }, {});

    this._maxLength = this.entities.reduce((res, curr) => {
      const width = (curr.level * 20) + (curr.title?.length * 8);

      if (width > res) {
        return width;
      }

      return res;
    }, 0);
  }

  get maxLength(): number {
    return this._maxLength;
  }

  get actions(): Observable<any> {
    return this._actions.asObservable();
  }

  convertStructure(structure) {
    if (!structure) {
      return [];
    }

    const entities: ExtendedEntity[] = [];
    this.convertItem(structure, entities, 0);
    return entities;
  }

  convertItem(item, result, level, parent?) {
    const children = item.child ? [...item.child] : [];
    const childrenIds = children.map(({ id }) => id);

    const currItem: Entity = new Entity({ ...item, child: [], level, parentId: parent?.id, children: childrenIds });
    currItem.parentId = parent?.id;
    currItem.getEntityParent = () => this.getEntity(parent?.id);
    result.push(currItem);
    if (children) {
      children
        .forEach((child: any) => this.convertItem(child, result, level + 1, currItem));
    }
  }

  openDialog(modal: string, entityItem: Entity, actionData?: any): Observable<any> {
    let source;
    if (modal === bsDialogs.SET_STATUS_CONFIRM || modal === bsDialogs.ENTITY_ADD) {
      source = of(entityItem);
    } else if (modal === bsDialogs.ENTITY_EDIT || modal === bsDialogs.EDIT_REGIONAL) {
      source = of(entityItem).pipe(
        switchMap((entity: Entity) => entity.type === 'merchant' ?
          this.entityService.getMerchantEntityItem(entity.path) :
          entity.isRoot() ?
            this.entityService.getBrief() :
            this.entityService.getItem(entity.path)
        )
      );
    }
    source.pipe(
      take(1)
    ).subscribe(entity => {
      entity = Object.assign(entityItem, entity);
      const type = bsActions.SHOW_DIALOG;
      this._actions.next({ modal, type, entity, actionData });
    });

    return source;
  }

  downloadCSV() {
    this.entityService.getCsvStructure()
      .subscribe(data => {
        const t = this.convertToCSV(data);
        const fileName = `Export business structure ${moment().format('YYYY-MM-DD HH:MM')}`;
        this.csvService.exportToCsv(csvSchema, t, fileName, csvSchema.map(({ name }) => name));
      });
  }

  private convertToCSV(entity: EntityShortInterface): Entity[] {
    let res = [];
    const convert = (child: EntityShortInterface) => {
      res.push({ ...child, child: [] });

      child.child?.forEach(convert);
    };

    convert(entity);

    res = res.reduce((result, item) => {
      if (!item.jurisdiction?.length) {
        result.push(item);
      } else {
        item.jurisdiction.forEach(jur => {
          result.push({ ...item, jurisdiction: jur });
        });
      }

      return result;
    }, []);

    return res;
  }
}

import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { ExcelService } from '../../../../../../common/services/excel.service';
import { FlatReportsService } from '../../../../../../common/services/flat-reports.service';
import { GameGroupService } from '../../../../../../common/services/game-group.service';
import { ShowLimitsModalComponent } from './show-limits-modal.component';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    SwuiSelectModule,
    MatFormFieldModule,
    SwuiControlMessagesModule,
    LayoutModule,
    MatTableModule,
    MatSortModule,
  ],
  declarations: [
    ShowLimitsModalComponent,
  ],
  exports: [
    ShowLimitsModalComponent,
  ],
  providers: [
    GameGroupService,
    ExcelService,
    FlatReportsService,
  ],
})
export class ShowLimitsModalModule {
}

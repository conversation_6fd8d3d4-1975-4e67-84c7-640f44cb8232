import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiPagePanelModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { PipesModule } from '../../../../common/pipes/pipes.module';
import { StructureResolver } from '../../../../common/services/resolvers/structure.resolver';

import { MoveEntityComponent } from './move-entity.component';
import { MoveEntityRoutingModule } from './move-entity.routing';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MoveEntityRoutingModule,
    PipesModule,
    MatCardModule,
    TranslateModule,
    SwuiPagePanelModule,
    LayoutModule,
    MatFormFieldModule,
    SwuiSelectModule,
    SwuiControlMessagesModule,
    MatButtonModule,
    MatTableModule,
    MatTooltipModule,
  ],
  exports: [MoveEntityComponent],
  declarations: [
    MoveEntityComponent,
  ],
  providers: [
    StructureResolver,
  ],
})
export class MoveEntityModule {
}

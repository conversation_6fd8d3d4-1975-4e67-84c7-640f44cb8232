import { HttpClient, HttpErrorResponse, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridRequestData, SwuiGridDataService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import moment from 'moment';
import { Observable, of, Subject, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { API_ENDPOINT, ZERO_TIME } from '../../app.constants';
import { Domain, DOMAIN_TYPES, DomainRow, DomainType } from '../../common/models/domain.model';

@Injectable()
export class DomainsManagementService implements SwuiGridDataService<Domain> {
  isGridChanged$ = new Subject<DomainType>();

  private cachedItems: Record<string, Domain[]> = {};

  constructor(
    private readonly http: HttpClient,
    private readonly notifications: SwuiNotificationsService,
  ) { }

  getGridData(param: HttpParams, data: GridRequestData): Observable<HttpResponse<Domain[]>> {
    let fn = this.processRecord.bind(this);
    const { type, path } = data;

    return this.http.get<DomainRow[]>(this.getUrl(type, path), {
      params: param,
      observe: 'response'
    }).pipe(
      tap((response) => {
        response.body.forEach((item) => fn(item));
      }),
      catchError((error: HttpErrorResponse) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
    );
  }

  getList(type: DomainType, path?: string, force?: boolean): Observable<Domain[]> {
    if (!force) {
      if (this.cachedItems[DOMAIN_TYPES[type]]) {
        return of(this.cachedItems[DOMAIN_TYPES[type]]);
      }
    }
    return this.http.get<DomainRow[]>(this.getUrl(type, path))
      .pipe(
        map(records => records.map(this.processRecord)),
        tap((data: Domain[]) => {
          this.cachedItems[DOMAIN_TYPES[type]] = data;
        }),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  delete(id: string, type: DomainType): Observable<Object> {
    return this.http.delete(`${this.getUrl(type)}/${id}`)
      .pipe(
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  create(data: Domain, type: DomainType): Observable<Domain> {
    if ('_meta' in data) {
      delete data['_meta'];
    }
    return this.http.post<DomainRow>(this.getUrl(type), data)
      .pipe(
        map(this.processRecord),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  update(id: string, data: Partial<DomainRow>, type: DomainType): Observable<Domain> {
    if ('_meta' in data) {
      delete data['_meta'];
    }
    return this.http.patch<DomainRow>(`${this.getUrl(type)}/${id}`, data)
      .pipe(
        map(this.processRecord),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  private processRecord(record: DomainRow): Domain {
    for (const property of ['createdAt', 'updatedAt']) {
      if (record[property] === ZERO_TIME) {
        record[property] = null;
      }
    }
    return {
      ...record,
      _meta: {
        createdAt: record.createdAt && moment(record.createdAt),
        updatedAt: record.updatedAt && moment(record.updatedAt),
      }
    };
  }

  private getUrl(type: DomainType, path?: string): string {
    return `${API_ENDPOINT}${path && path !== ':' ? '/entities/' + path : ''}/domains/${type}`;
  }
}

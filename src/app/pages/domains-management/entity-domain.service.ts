import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import moment from 'moment';
import { of, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { API_ENDPOINT, ZERO_TIME } from '../../app.constants';
import { Domain, DOMAIN_TYPES, DomainRow, DomainType, StaticDomainType } from '../../common/models/domain.model';

function cacheKey(domainType: DomainType, staticDomainType?: StaticDomainType) {
  return `${DOMAIN_TYPES[domainType]}.${staticDomainType || 'none'}`;
}

class Cache {
  private readonly cachedItem: Record<string, Domain> = {};

  has(domainType: DomainType, staticDomainType?: StaticDomainType) {
    return Boolean(this.cachedItem[cacheKey(domainType, staticDomainType)]);
  }

  get(domainType: DomainType, staticDomainType?: StaticDomainType) {
    return this.cachedItem[cacheKey(domainType, staticDomainType)];
  }

  set(data: Domain, domainType: DomainType, staticDomainType?: StaticDomainType) {
    this.cachedItem[cacheKey(domainType, staticDomainType)] = data;
  }

  remove(domainType: DomainType, staticDomainType?: StaticDomainType) {
    delete this.cachedItem[cacheKey(domainType, staticDomainType)];
  }
}

@Injectable()
export class EntityDomainService {
  private cache = new Cache();

  constructor(
    private readonly http: HttpClient,
    private readonly notifications: SwuiNotificationsService
  ) { }

  setEntityDomain(domainType: DomainType, domainId: string, path: string = ':', staticDomainType?: StaticDomainType) {
    return this.invoke('PUT', path, domainType, staticDomainType, domainId);
  }

  getEntityDomain(domainType: DomainType, path: string = ':', force = false, staticDomainType?: StaticDomainType) {
    if (!force) {
      if (this.cache.has(domainType, staticDomainType)) {
        return of(this.cache.get(domainType, staticDomainType));
      }
    }
    return this.invoke('GET', path, domainType, staticDomainType);
  }

  removeEntityDomain(domainType: DomainType, path: string = ':', staticDomainType?: StaticDomainType) {
    return this.invoke('DELETE', path, domainType, staticDomainType);
  }

  bulkOperation(bulkRequestData: any[]) {
    return this.http.post(`${API_ENDPOINT}/entities/bulk-operation`, bulkRequestData).pipe(
      catchError((error: HttpErrorResponse) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
    );
  }

  setStaticDomainTags(path: string = ':', tags: string[]) {
    const url = `${API_ENDPOINT}/${path !== ':' ? path : ''}/entitydomain/static/tags`;
    return this.http.put(url, { tags }).pipe(
      catchError((error: HttpErrorResponse) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
    );
  }

  resetStaticDomainTags(path: string = ':') {
    const url = `${API_ENDPOINT}/${path !== ':' ? path : ''}/entitydomain/static/tags`;
    return this.http.delete(url).pipe(
      catchError((error: HttpErrorResponse) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
    );
  }

  private invoke(method: string, path: string, domainType: DomainType, staticDomainType: StaticDomainType, domainId = '') {
    const url = `${API_ENDPOINT}/${path !== ':' ? path : ''}/entitydomain/${domainType}${domainId ? `/` : ''}${domainId}`;
    const params = domainType === DOMAIN_TYPES.static ? { type: staticDomainType } : undefined;
    const body = domainType === DOMAIN_TYPES.static ? { type: staticDomainType } : undefined;
    return this.http.request(method, url, { params, body }).pipe(
      map<DomainRow | null, Domain>((record) => {
        if (!record) {
          return null;
        }
        for (const property of ['createdAt', 'updatedAt']) {
          if (record[property] === ZERO_TIME) {
            record[property] = null;
          }
        }
        return {
          ...record,
          _meta: {
            createdAt: record.createdAt && moment(record.createdAt),
            updatedAt: record.updatedAt && moment(record.updatedAt),
          }
        };
      }),
      tap((data: Domain) => {
        if (method === 'DELETE') {
          this.cache.remove(domainType, staticDomainType);
        } else {
          if (data) {
            this.cache.set(data, domainType, staticDomainType);
          } else {
            this.cache.remove(domainType, staticDomainType);
          }
        }
      }),
      catchError((error: HttpErrorResponse) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
    );
  }
}

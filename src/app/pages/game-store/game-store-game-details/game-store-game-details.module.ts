import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { PipesModule } from '../../../common/pipes/pipes.module';
import { CdnService } from '../../../common/services/cdn.service';

import { GameStoreGameDetailsDialogComponent } from './game-store-game-details-dialog/game-store-game-details-dialog.component';
import { GameStoreGameDetailsComponent } from './game-store-game-details.component';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule } from '@angular/material/dialog';
import { MatCardModule } from '@angular/material/card';
import { MatListModule } from '@angular/material/list';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';


@NgModule({
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    PipesModule,
    TranslateModule,
    SwuiPagePanelModule,
    MatCardModule,
    MatTabsModule,
    MatButtonModule,
    MatChipsModule,
    MatTooltipModule,
    MatExpansionModule,
    MatIconModule,
    MatDialogModule,
    MatListModule,
    LayoutModule,
  ],
  exports: [
    GameStoreGameDetailsComponent,
    GameStoreGameDetailsDialogComponent,
  ],
  declarations: [
    GameStoreGameDetailsComponent,
    GameStoreGameDetailsDialogComponent,
  ],
  providers: [
    CdnService,
  ],
})
export class GameStoreGameDetailsModule {
}

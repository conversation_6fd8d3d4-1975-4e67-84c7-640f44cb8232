import { HttpClient, HttpParams, HttpParamsOptions, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridRequestData, SettingsService, SwuiGridDataService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import moment from 'moment';
import { combineLatest, Observable, of, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { API_ENDPOINT, FORMAT_DATETIME } from '../../../../app.constants';
import { CsvSchema, CsvService, transformDate } from '../../../../common/services/csv.service';
import { GameService } from '../../../../common/services/game.service';
import { Game, UnfinishedGameHistory } from '../../../../common/typings';
import { EntitySettingsService } from '../../../../common/services/entity-settings.service';
import { EntitySettingsModel } from '../../../../common/models/entity-settings.model';
import { transformCurrencyItem, transformFormatCurrencyValue } from '../../../../common/core/currecy-transform';

const csvSchema = ( timezoneName: string, format: string, gameName: CsvSchema ): CsvSchema[] => [
  {
    name: 'playerCode',
    title: 'GAMEHISTORY.CSV.playerCode',
  },
  {
    name: 'roundId',
    title: 'GAMEHISTORY.CSV.roundId',
  },
  gameName,
  {
    name: 'gameCode',
    title: 'GAMEHISTORY.CSV.gameCode'
  },
  {
    name: 'finished',
    title: 'GAMEHISTORY.CSV.finished',
    transform( value: UnfinishedGameHistory['finished'] ) {
      return value.toString().toUpperCase();
    }
  },
  {
    name: 'status',
    title: 'GAMEHISTORY.CSV.status',
  },
  {
    name: 'device',
    title: 'GAMEHISTORY.CSV.device',
  },
  {
    name: 'isTest',
    title: 'GAMEHISTORY.CSV.type',
    transform( value: UnfinishedGameHistory['isTest'] | string ) {
      return value?.toString() === 'true' ? 'Test' : 'Real';
    }
  },
  {
    name: 'firstTs',
    title: 'GAMEHISTORY.CSV.firstTs',
    transform: transformDate(timezoneName, format),
  },
  {
    name: 'ts',
    title: 'GAMEHISTORY.CSV.ts',
    transform: transformDate(timezoneName, format),
  },
  {
    name: 'currency',
    title: 'GAMEHISTORY.CSV.currency',
    transform( data: string ): string {
      return transformCurrencyItem(0, data).label;
    }
  },
  {
    name: 'balanceAfter',
    title: 'GAMEHISTORY.CSV.balanceAfter',
    transform( data: string, rows: Record<string, any> ): string {
      return transformFormatCurrencyValue(Number(data), rows.currency);
    }
  },
  {
    name: 'bet',
    title: 'GAMEHISTORY.CSV.bet',
    transform( data: string, rows: Record<string, any> ): string {
      return transformFormatCurrencyValue(Number(data), rows.currency);
    }
  },
  {
    name: 'win',
    title: 'GAMEHISTORY.CSV.win',
    transform( data: string, rows: Record<string, any> ): string {
      return transformFormatCurrencyValue(Number(data), rows.currency);
    }
  },
  {
    name: 'credit',
    title: 'GAMEHISTORY.CSV.credit',
    transform( data: string, rows: Record<string, any> ): string {
      return transformFormatCurrencyValue(Number(data || 0), rows.currency);
    }
  },
  {
    name: 'debit',
    title: 'GAMEHISTORY.CSV.debit',
    transform( data: string, rows: Record<string, any> ): string {
      return transformFormatCurrencyValue(Number(data || 0), rows.currency);
    }
  },
  {
    name: 'revenue',
    title: 'GAMEHISTORY.CSV.revenue',
    transform( data: string, rows: Record<string, any> ): string {
      return transformFormatCurrencyValue(Number(data), rows.currency);
    }
  },
  {
    name: 'balanceBefore',
    title: 'GAMEHISTORY.CSV.balanceBefore',
    transform( data: string, rows: Record<string, any> ): string {
      return transformFormatCurrencyValue(Number(data), rows.currency);
    }
  },
  {
    name: 'outcome',
    title: 'GAMEHISTORY.CSV.outcome',
    transform( _, row: UnfinishedGameHistory ): string {
      const bet = parseFloat(String(row.bet ?? 0));
      const win = parseFloat(String(row.win ?? 0));
      if (bet > win) {
        return 'LOSE';
      }
      if (bet === win) {
        return row.recoveryType === 'revert' ? 'VOID' : 'TIE';
      }
      return 'WIN';
    }
  },
  {
    name: 'revenue',
    title: 'GAMEHISTORY.CSV.ggr',
    transform( value: UnfinishedGameHistory['revenue'], rows: UnfinishedGameHistory ): string {
      const bet = parseFloat(String(rows.bet ?? 0));
      if (!bet) {
        return '0.00';
      }
      return transformFormatCurrencyValue(((value ?? 0) / bet) * 100, '');
    }
  },
  {
    name: 'totalJpWin',
    title: 'GAMEHISTORY.CSV.totalJpWin',
    transform( data: string, rows: Record<string, any> ): string {
      return transformFormatCurrencyValue(Number(data), rows.currency);
    }
  },
];


@Injectable()
export class GameHistoryBrokenService implements SwuiGridDataService<UnfinishedGameHistory> {
  private _blocked = true;
  private unfinishedUrl = '/history/unfinished/game';
  private historyUrl = '/history/game';

  constructor( private readonly http: HttpClient,
               private readonly gameService: GameService,
               private readonly csvService: CsvService,
               private readonly setting: SettingsService,
               private readonly notifications: SwuiNotificationsService,
               private readonly entitySettingsService: EntitySettingsService<EntitySettingsModel>
  ) {
  }

  blockData() {
    this._blocked = true;
  }

  unblockData() {
    this._blocked = false;
  }

  getGridData( params: HttpParams, requestData?: GridRequestData ) {
    const path = requestData?.path;
    if (this._blocked) {
      return of(new HttpResponse<UnfinishedGameHistory[]>({
        body: []
      }));
    }

    return combineLatest([
      this.http.get<UnfinishedGameHistory[]>(this.getUrl(path, this.unfinishedUrl), {
        params,
        observe: 'response'
      }),
      this.gameService.getAllGames(path, false, true),
      this.entitySettingsService.getSettings(path, false, true)
    ]).pipe(
      map(( [response, games, settings] ) => new HttpResponse({
        body: this.processRecord(response.body, games, settings) || []
      })));
  }

  changeStatus( row: UnfinishedGameHistory, status: string, fullPath: string, ignoreMerchantParams?: boolean ) {
    const params: HttpParamsOptions['fromObject'] = {
      gameContextId: row.gameContextId
    };
    if (!['finalize', 'retryPending', 'requireTransferOut'].includes(status)) {
      params.force = `${row.status === 'broken'}`;
    }
    if (status === 'manualFinalize') {
      params.closeInSWWalletOnly = 'true';
    }
    if (ignoreMerchantParams) {
      params.ignoreMerchantParams = 'true';
      params.closeInSWWalletOnly = 'true';
    }

    const url = this.getActionUrl(row, status);
    return this.http.post(this.getUrl(fullPath, url), {}, {
      params: new HttpParams({ fromObject: params })
    }).pipe(
      catchError(err => this.handleErrors.call(this, err))
    );
  }

  downloadCsv( addParams: any = {}, forcePath = '' ): Observable<any> {
    const fileName = `Export Broken game ${moment().format('YYYY-MM-DD HH:MM')}`;
    const schema = csvSchema(this.timezoneName, this.datetimeFormat, {
      name: 'gameNameLabel',
      title: 'GAMEHISTORY.CSV.gameName',
    });
    const urlFn = ( path ) => this.getUrl(path, this.unfinishedUrl);
    return this.csvService.download(urlFn, schema, fileName, addParams, forcePath);
  }

  exportPage( data: Record<string, any>[], columns: string[], page: number ) {
    const fileName = `Export Broken game ${moment().format('YYYY-MM-DD HH:MM')} (page ${page})`;
    const schema = csvSchema(this.timezoneName, this.datetimeFormat, {
      name: 'gameNameLabel',
      title: 'GAMEHISTORY.CSV.gameName',
    });
    this.csvService.exportToCsv(schema, data, fileName, columns);
  }

  private get datetimeFormat(): string {
    const { appSettings: { dateFormat, timeFormat } } = this.setting;
    return dateFormat && timeFormat ? `${dateFormat} ${timeFormat}` : FORMAT_DATETIME;
  }

  private get timezoneName(): string {
    return this.setting.appSettings.timezoneName;
  }

  private processRecord( gameHistory: UnfinishedGameHistory[] = [], games: Game[] = [], settings: EntitySettingsModel ) {
    return gameHistory.reduce(( acc: UnfinishedGameHistory[], cur: UnfinishedGameHistory ) => {
      const gameData = games.find(( game: Game ) => game.code === cur.gameCode);
      cur.gameNameLabel = gameData?.title;
      cur.finalizationSupport = gameData?.settings?.finalizationSupport;
      cur.recoveryType = cur.recoveryType !== null ? cur.recoveryType : null;
      cur.outcome = cur.bet > cur.win ? 'LOSE' :
        (cur.bet === cur.win ? (cur.recoveryType === 'revert' ? 'VOID' : 'TIE') : 'WIN');
      if (cur.bet !== 0) {
        cur.ggrPerc = (cur.revenue / cur.bet * 100).toFixed(2);
      }
      cur.entityFinalizationSupport = settings?.finalizationSupport;

      acc.push(cur);
      return acc;
    }, []);
  }

  private getUrl( path?: string, urlList: string = this.historyUrl ): string {
    return `${API_ENDPOINT}${path ? '/entities/' + path : ''}${urlList}`;
  }

  private getActionUrl( row: UnfinishedGameHistory, status: string ) {
    switch (status) {
      case 'forceFinish':
        return `${this.historyUrl}/${row.roundId}/forceFinish`;
      case 'revert':
        return `${this.historyUrl}/${row.roundId}/revert`;
      case 'retryPending':
        return `${this.historyUrl}/${row.roundId}/retry-pending`;
      case 'requireTransferOut':
        return `${this.historyUrl}/${row.roundId}/transfer-out`;
      case 'manualFinalize':
      case 'finalize':
        return `${this.historyUrl}/recovery/finalize`;
      default: {
        return null;
      }
    }
  }

  private handleErrors( err ): Observable<never> {
    if (err.error) {
      this.notifications.error(err.error.message);
    } else {
      this.notifications.error(err.statusText, `Status: ${err.status}`);
    }

    return throwError(err);
  }
}

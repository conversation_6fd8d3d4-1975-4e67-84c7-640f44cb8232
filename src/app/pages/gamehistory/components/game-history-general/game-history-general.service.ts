import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridRequestData, SettingsService, SwuiGridDataService } from '@skywind-group/lib-swui';
import moment from 'moment';
import { BehaviorSubject, combineLatest, of } from 'rxjs';
import { Observable } from 'rxjs/Observable';
import { map, switchMap, take } from 'rxjs/operators';

import { API_ENDPOINT, FORMAT_DATETIME } from '../../../../app.constants';
import { CsvSchema, CsvService, transformDate } from '../../../../common/services/csv.service';
import { GameService } from '../../../../common/services/game.service';
import { Game, UnfinishedGameHistory } from '../../../../common/typings';
import { GameHistory } from '../../../../common/typings/reports/game_history';
import { transformCurrencyItem, transformFormatCurrencyValue } from '../../../../common/core/currecy-transform';

const csvSchema = (games: Record<string, Game>, timezoneName: string, format: string): CsvSchema[] => [
  {
    name: 'playerCode',
    title: 'GAMEHISTORY.CSV.playerCode',
  },
  {
    name: 'roundId',
    title: 'GAMEHISTORY.CSV.roundId',
  },
  {
    name: 'gameCode',
    title: 'GAMEHISTORY.CSV.gameCode'
  },
  {
    name: 'gameNameLabel',
    title: 'GAMEHISTORY.CSV.gameName',
    transform(value: GameHistory['gameNameLabel'], rows: GameHistory): string {
      value = value ?? games[rows.gameCode]?.title ?? '';
      value = value.replace(/"/g, "'");
      return `"${value}"`;
    }
  },
  {
    name: 'finished',
    title: 'GAMEHISTORY.CSV.finished',
    transform(value: GameHistory['finished']) {
      return value.toString().toUpperCase();
    }
  },
  {
    name: 'device',
    title: 'GAMEHISTORY.CSV.device',
  },
  {
    name: 'isTest',
    title: 'GAMEHISTORY.CSV.type',
    transform(value: any) {
      return value?.toString() === 'true' ? 'Test' : 'Real';
    }
  },
  {
    name: 'firstTs',
    title: 'GAMEHISTORY.CSV.firstTs',
    transform: transformDate(timezoneName, format),
  },
  {
    name: 'ts',
    title: 'GAMEHISTORY.CSV.ts',
    transform: transformDate(timezoneName, format),
  },
  {
    name: 'currency',
    title: 'GAMEHISTORY.CSV.currency',
    transform(data: string): string {
      return transformCurrencyItem(0, data).label;
    }
  },
  {
    name: 'balanceBefore',
    title: 'GAMEHISTORY.CSV.balanceBefore',
    transform(data: string, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(Number(data), rows.currency);
    }
  },
  {
    name: 'balanceAfter',
    title: 'GAMEHISTORY.CSV.balanceAfter',
    transform(data: string, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(Number(data), rows.currency);
    }
  },
  {
    name: 'bet',
    title: 'GAMEHISTORY.CSV.bet',
    transform(data: string, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(Number(data), rows.currency);
    }
  },
  {
    name: 'win',
    title: 'GAMEHISTORY.CSV.win',
    transform(data: string, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(Number(data), rows.currency);
    }
  },
  {
    name: 'revenue',
    title: 'GAMEHISTORY.CSV.revenue',
    transform(data: string, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(Number(data), rows.currency);
    }
  },
  {
    name: 'revenue',
    title: 'GAMEHISTORY.CSV.outcome',
    transform(value: GameHistory['revenue']): string {
      const amount = parseFloat(String(value ?? 0));
      if (amount > 0) {
        return 'LOSE';
      }
      if (amount < 0) {
        return 'WIN';
      }
      return 'TIE';
    }
  },
  {
    name: 'revenue',
    title: 'GAMEHISTORY.CSV.ggr',
    transform(value: GameHistory['revenue'], rows: GameHistory): string {
      const bet = parseFloat(String(rows.bet ?? 0));
      if (!bet) {
        return '0.00';
      }
      return transformFormatCurrencyValue(((value ?? 0) / bet) * 100, '');
    }
  },
  {
    name: 'totalJpWin',
    title: 'GAMEHISTORY.CSV.totalJpWin',
    transform(data: string, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(Number(data), rows.currency);
    }
  },
];

function getUrl(path?: string, urlList: string = '/history/game'): string {
  return `${API_ENDPOINT}${path ? '/entities/' + path : ''}${urlList}`;
}

@Injectable()
export class GameHistoryGeneralService implements SwuiGridDataService<GameHistory> {
  private _blocked = true;
  private _games$ = new BehaviorSubject({});

  set games(games: Record<string, Game>) {
    this._games$.next(games);
  }

  get games$(): Observable<Record<string, Game>> {
    return this._games$.asObservable();
  }

  constructor(private readonly http: HttpClient,
              private readonly csvService: CsvService,
              private readonly gameService: GameService,
              private readonly setting: SettingsService
  ) {
  }

  blockData() {
    this._blocked = true;
  }

  unblockData() {
    this._blocked = false;
  }

  getGridData(params: HttpParams, requestData?: GridRequestData) {
    const path = requestData?.path;
    if (this._blocked) {
      return of(new HttpResponse<UnfinishedGameHistory[]>({
        body: []
      }));
    }

    return combineLatest([
        this.http.get<GameHistory[]>(getUrl(path), {
          params,
          observe: 'response'
        }),
        this.gameService.getAllGames(path, false, true)
      ]
    ).pipe(
      map(([response, games]) => {
        return new HttpResponse({
          body: this.processRecord(response.body, games) || []
        });
      }));
  }

  public downloadCsv(addParams: any = {}, forcePath = ''): Observable<any> {
    const {appSettings: {dateFormat, timeFormat, timezoneName}} = this.setting;
    const datetimeFormat = dateFormat && timeFormat ? `${dateFormat} ${timeFormat}` : FORMAT_DATETIME;
    const fileName = `Export Base history ${moment().format('YYYY-MM-DD HH:MM')}`;
    return this.gameService.getAllGames(forcePath, false, true).pipe(
      map<Game[], Record<string, Game>>(games => games.reduce(( result, game ) => ({
        ...result,
        [game.code]: game
      }), {})),
      map(games => csvSchema(games, timezoneName, datetimeFormat)),
      switchMap(schema => this.csvService.download(getUrl, schema, fileName, addParams, forcePath)),
    );
  }

  public exportPage(data: Record<string, any>[], columns: string[], page: number) {
    const {appSettings: {dateFormat, timeFormat, timezoneName}} = this.setting;
    const datetimeFormat = dateFormat && timeFormat ? `${dateFormat} ${timeFormat}` : FORMAT_DATETIME;
    const fileName = `Export Base history  ${moment().format('YYYY-MM-DD HH:MM')} (page ${page})`;
    this.games$
      .pipe(
        take(1)
      ).subscribe((games) =>
      this.csvService.exportToCsv(csvSchema(games, timezoneName, datetimeFormat), data, fileName, columns));
  }

  private processRecord(gameHistory: GameHistory[] = [], games: Game[] = []) {
    return gameHistory.map(item => {
      return {
        ...item,
        currencyLabel: transformCurrencyItem(0, item.currency).label,
        gameNameLabel: games.find(({code}) => code === item.gameCode)?.title,
        outcome: item.bet > item.win ? 'LOSE' : (item.bet === item.win ? (item.recoveryType === 'revert' ? 'VOID' : 'TIE') : 'WIN'),
        ggrPerc: item.bet !== 0 ? (item.revenue / item.bet * 100).toFixed(2) : undefined,
      };
    });
  }
}

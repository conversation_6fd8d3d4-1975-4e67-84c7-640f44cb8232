import { LayoutModule } from '@angular/cdk/layout';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { GameHistorySpinService } from '../../../../../common/services/reports/gamehistory.spin.service';

import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { RoundInfoModule } from '../round/round-info-module';
import { RoundInfoViewModalComponent } from './round-info-view-modal.component';

@NgModule({
  imports: [
    CommonModule,
    RoundInfoModule,
    MatCardModule,
    LayoutModule,
    TranslateModule,
    MatButtonModule,
    MatDialogModule,
    MatIconModule,
  ],
  exports: [RoundInfoViewModalComponent],
  declarations: [RoundInfoViewModalComponent],
  providers: [GameHistorySpinService],
})
export class RoundInfoViewModalModule {
}

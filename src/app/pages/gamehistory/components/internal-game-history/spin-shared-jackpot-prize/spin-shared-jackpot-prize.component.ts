import { Component, Input, OnChanges, OnInit } from '@angular/core';
import moment from 'moment';

@Component({
    selector: 'spin-shared-jackpot-prize',
    templateUrl: './spin-shared-jackpot-prize.component.html',
    styleUrls: ['../spin-details/spin-tables.scss'],
    standalone: false
})
export class SpinSharedJackpotPrizeComponent implements OnInit, OnChanges {

  @Input() spin: any = null;

  public sharedJackpotPrize: any = null;

  ngOnInit() {
    this.parse();
  }

  ngOnChanges() {
    this.parse();
  }

  private parse() {
    if (this.spin && this.spin.details && this.spin.details.paymentType &&
      this.spin.details.paymentType === 'shared_jp_prize') {
      this.sharedJackpotPrize = this.getSharedJackpotPrizeInfo(this.spin);
    } else {
      this.sharedJackpotPrize = null;
    }
  }

  private getSharedJackpotPrizeInfo( spin ) {
    return {
      transactionId: spin.details.transactionId,
      currency: spin.currency || spin.details.currency,
      amount: spin.credit || spin.details.amount,
      dateTime: `${moment(spin.details.ts).format('L')} ${moment(spin.details.ts).format('LTS')}`,
    };
  }
}

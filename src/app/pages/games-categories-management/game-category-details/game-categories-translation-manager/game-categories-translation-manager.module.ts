import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LayoutModule } from '@angular/cdk/layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiTranslationsManagerModule } from '@skywind-group/lib-swui';
import { TrimInputValueModule } from '../../../../common/directives/trim-input-value/trim-input-value.module';

import { GameCategoriesTranslationManagerComponent } from './game-categories-translation-manager.component';
import { PipesModule } from '../../../../common/pipes/pipes.module';
import { GameCategoriesInfoComponent } from './game-categories-info/game-categories-info.component';


@NgModule({
  declarations: [
    GameCategoriesTranslationManagerComponent,
    GameCategoriesInfoComponent,
  ],
  exports: [GameCategoriesTranslationManagerComponent],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        SwuiTranslationsManagerModule,
        TranslateModule,
        SwuiControlMessagesModule,
        PipesModule,
        MatButtonModule,
        MatInputModule,
        MatFormFieldModule,
        MatIconModule,
        LayoutModule,
        TrimInputValueModule,
    ],
})
export class GameCategoriesTranslationManagerModule {
}

import { Component, ElementRef, EventEmitter, Inject, <PERSON>Z<PERSON>, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import autoScroll from 'dom-autoscroller';
import { DragulaService } from 'ng2-dragula';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { tagClassMap } from '../../../../app.constants';
import { CdnService } from '../../../../common/services/cdn.service';
import { GameInfo } from '../../../../common/typings';
import { GameSelectItem, gameSelectItemTypes } from '../games-select-manager/game-select-item.model';

export const DRAGULA_BAG_NAME: string = 'bag-preview-games';

@Component({
  selector: 'game-category-preview-modal',
  templateUrl: 'game-category-preview-modal.component.html',
  styleUrls: ['./games-category-preview-modal.component.scss'],
  standalone: false
})
export class GameCategoryPreviewModalComponent implements OnInit, OnDestroy {

  @ViewChild('scrollTable', { static: true }) table: ElementRef;

  public isEntityOwner: boolean;
  public items: GameSelectItem[];
  public games: GameSelectItem[];
  public previewApplied: EventEmitter<GameSelectItem[]> = new EventEmitter();

  public itemTypes = gameSelectItemTypes;
  public dragulaBagName = DRAGULA_BAG_NAME;
  private gameImages: { [key: string]: string; };

  private readonly destroyed$ = new Subject<void>();

  constructor(private cdnService: CdnService,
    private dragulaService: DragulaService,
    private zone: NgZone,
    private dialogRef: MatDialogRef<GameCategoryPreviewModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { items: GameSelectItem[], isEntityOwner: boolean; }
  ) {
    this.isEntityOwner = data.isEntityOwner;
    this.items = data.items;
    this.setupDragula();
  }

  ngOnInit() {
    this.buildGamesArray();
    this.cdnService.gameImages.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(data => {
      this.gameImages = data;
    });

    this.zone.runOutsideAngular(() => {
      let drake = this.dragulaService.find(DRAGULA_BAG_NAME);
      autoScroll(this.table.nativeElement, {
        margin: 20,
        maxSpeed: 5,
        scrollWhenOutside: true,
        autoScroll: function () {
          return this.down && drake && drake.drake;
        },
      });
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  hasGameImageUrl({ id, type }: GameSelectItem): boolean {
    return type === gameSelectItemTypes.GAME && this.gameImages && id in this.gameImages;
  }

  getGameImageUrl(item: GameSelectItem): string {
    return this.hasGameImageUrl(item) ? this.gameImages[(item.id)] : '';
  }

  applyPreview() {
    this.dialogRef.close(this.items);
  }

  getLabelClass(label): string[] {
    let classes = [];
    let group = label.data ? label.data['group'] : label.group;
    let labelClass = tagClassMap.hasOwnProperty(group) ? tagClassMap[group] : 'border-left-grey';

    classes.push(labelClass);

    return classes;
  }

  getGameBackgroundCssClass(item: GameSelectItem) {
    let cssClass = '';
    if ('cssClass' in item.data) {
      cssClass = item.data['cssClass'] as string;
    }
    return cssClass;
  }

  highlightGames(item: GameSelectItem) {
    if (item.type === gameSelectItemTypes.GAME) {
      this.toggleHighlightClass(item);
    } else {
      if ('previewGames' in item) {
        (item['previewGames'] as any[]).forEach(i => this.toggleHighlightClass(i));
      }
    }
  }

  removeHighlight(item: GameSelectItem) {
    if (item.type === gameSelectItemTypes.GAME) {
      this.toggleHighlightClass(item, false);
    } else {
      if ('previewGames' in item) {
        (item['previewGames'] as any[]).forEach(i => this.toggleHighlightClass(i, false));
      }
    }
  }

  private toggleHighlightClass(item, add = true) {
    item.data['cssClass'] = add ? 'game-bg-warning' : '';
  }

  private setupDragula() {
    const bag: any = this.dragulaService.find(this.dragulaBagName);
    if (bag !== undefined) this.dragulaService.destroy(this.dragulaBagName);

    this.dragulaService.createGroup(this.dragulaBagName, {
      moves: (_, __, handle) => handle.classList.contains('handle')
    });

    this.dragulaService.dragend(this.dragulaBagName).subscribe(() => this.buildGamesArray());
  }

  private buildGamesArray() {
    this.games = this.items.map((item) => {
      if ('previewGames' in item) {
        (item['previewGames'] as any[]) = [];
      }
      return item;
    }).reduce((games, item) => {
      let itemGames = [item];

      if (item.type === gameSelectItemTypes.LABEL || item.type === gameSelectItemTypes.PROVIDER) {
        itemGames = item.items
          .map((game: GameSelectItem) => this.convertToPreviewGame(item, game));
      } else if (item.type === gameSelectItemTypes.INTERSECTION) {
        itemGames = (item.data['games'] as GameSelectItem[])
          .map((game: GameSelectItem) => this.convertToPreviewGame(item, game));
      }
      return [...games, ...itemGames];
    }, []);
  }

  private convertToPreviewGame(item: GameSelectItem, game: GameSelectItem) {
    const gameItem = GameSelectItem.createFromGameInfo(<GameInfo>{ ...game.data });
    if ('previewGames' in item === false) {
      item['previewGames'] = [];
    }
    item['previewGames'].push(gameItem);
    return gameItem;
  }
}

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LayoutModule } from '@angular/cdk/layout';
import { TranslateModule } from '@ngx-translate/core';
import { DragulaModule } from 'ng2-dragula';
import { GameCategoryPreviewModalComponent } from './game-category-preview-modal.component';
import { CdnService } from '../../../../common/services/cdn.service';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule } from '@angular/material/dialog';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    DragulaModule.forRoot(),
    LayoutModule,
    MatButtonModule,
    MatCardModule,
    MatChipsModule,
    MatDialogModule,
  ],
  exports: [],
  declarations: [
    GameCategoryPreviewModalComponent,
  ],
  providers: [
    CdnService,
  ],
})
export class GameCategoryPreviewModule {
}

import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiMultiselectModule, SwuiPagePanelModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { EditableModule } from '../../../common/components/editable/editable.module';
import { TrimInputValueModule } from '../../../common/directives/trim-input-value/trim-input-value.module';
import { GamesCreateComponent } from './games-create.component';
import { LimitsModule } from './limits/limits.module';
import { TranslationsModule } from './translations/translations.module';
import { ImagesModule } from './images/images.module';


@NgModule({
  imports: [
    CommonModule,
    EditableModule,
    LimitsModule,
    ReactiveFormsModule,
    RouterModule,
    TranslateModule,
    SwuiPagePanelModule,
    MatFormFieldModule,
    SwuiControlMessagesModule,
    LayoutModule,
    MatInputModule,
    MatExpansionModule,
    MatIconModule,
    MatButtonModule,
    TrimInputValueModule,
    SwuiSelectModule,
    TranslationsModule,
    SwuiMultiselectModule,
    ImagesModule
  ],
  exports: [
    GamesCreateComponent,
  ],
  declarations: [
    GamesCreateComponent,
  ]
})
export class GamesCreateModule {
}

import { LayoutModule } from '@angular/cdk/layout';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule } from '@skywind-group/lib-swui';
import { TrimInputValueModule } from '../../../../../common/directives/trim-input-value/trim-input-value.module';
import { ScreenshotsComponent } from './screenshots.component';

@NgModule({
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    TrimInputValueModule,
    ReactiveFormsModule,
    TranslateModule,
    SwuiControlMessagesModule,
    LayoutModule
  ],
  exports: [
    ScreenshotsComponent,
  ],
  declarations: [
    ScreenshotsComponent,
  ],
})
export class ScreenshotsModule {
}

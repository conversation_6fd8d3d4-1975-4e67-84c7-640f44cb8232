import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { LimitsComponent } from './limits.component';
import { TrimInputValueModule } from '../../../../common/directives/trim-input-value/trim-input-value.module';
import { LayoutModule } from '@angular/cdk/layout';


@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    MatFormFieldModule,
    MatInputModule,
    SwuiControlMessagesModule,
    TrimInputValueModule,
    LayoutModule,
    SwuiSelectModule,
  ],
  exports: [
    LimitsComponent,
  ],
  declarations: [
    LimitsComponent,
  ],
  providers: []
})
export class LimitsModule {
}

import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { AVAILABLE_LANGUAGES } from '../../../lobby/lobby.model';
import { FormControl } from '@angular/forms';
import { Subject } from 'rxjs';
import { Game, GameTranslationByLocale } from '../../../../common/typings';
import { distinctUntilChanged, takeUntil } from 'rxjs/operators';
import { cloneDeep, isEqual } from 'lodash';
import { ActivatedRoute } from '@angular/router';


export function parseTranslations( game: Game | undefined, ordered: boolean ): GameTranslationByLocale {
  const translations: GameTranslationByLocale = cloneDeep(game?.features?.translations ?? {});
  const value = ordered ? Object.entries(translations).reduce<Record<string, any>>(( result, [lang, item], order ) => ({
    ...result, [lang]: {
      ...item,
      order: order + 1
    }
  }), {}) : translations;
  value.en = {
    title: game?.title ?? '',
    ...(game?.defaultInfo?.description ? { description: game.defaultInfo.description } : {}),
    ...(ordered ? { order: 0 } : {}),
  };
  return value;
}

@Component({
    selector: 'game-create-translations',
    templateUrl: './translations.component.html',
    standalone: false
})
export class TranslationsComponent implements OnInit, OnDestroy {
  @Input() submitted: boolean;

  @Output() valueChange = new EventEmitter<GameTranslationByLocale>();
  @Output() validStatusChange = new EventEmitter<boolean>();

  readonly availableLanguages = AVAILABLE_LANGUAGES;
  readonly translationsControl = new FormControl();

  private readonly destroyed$ = new Subject<void>();

  constructor( { snapshot: { data: { game } } }: ActivatedRoute ) {
    this.translationsControl.patchValue(parseTranslations(game, true), { emitEvent: false });
  }

  ngOnInit(): void {
    this.translationsControl.valueChanges.pipe(
      distinctUntilChanged(( prev, curr ) => isEqual(prev, curr)),
      takeUntil(this.destroyed$)
    ).subscribe(( data: Record<string, any> ) => {
      this.valueChange.emit(Object.entries(data)
        .filter(( [, { title, description }] ) => Boolean(`${title}${description}`))
        .reduce<GameTranslationByLocale>(( result, [lang, { title, description }] ) => ({
          ...result,
          [lang]: {
            title,
            ...(description ? { description } : {})
          }
        }), {})
      );
    });
    this.translationsControl.statusChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(result => {
      this.validStatusChange.emit(result === 'VALID');
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}

import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { TranslationsComponent } from './translations.component';
import { TranslationInfoComponent } from './translation-info/translation-info.component';
import { ReactiveFormsModule } from '@angular/forms';
import { LayoutModule } from '@angular/cdk/layout';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TrimInputValueModule } from '../../../../common/directives/trim-input-value/trim-input-value.module';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiTranslationsManagerModule } from '@skywind-group/lib-swui';


@NgModule({
  declarations: [
    TranslationsComponent,
    TranslationInfoComponent,
  ],
  exports: [TranslationsComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    LayoutModule,
    MatFormFieldModule,
    MatInputModule,
    TrimInputValueModule,
    TranslateModule,
    SwuiControlMessagesModule,
    SwuiTranslationsManagerModule,
  ],
})
export class TranslationsModule {
}

import { HttpErrorResponse } from '@angular/common/http';
import { Component, OnDestroy, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  PanelAction,
  RowAction,
  SelectInputOptionData,
  SwHubAuthService,
  SwuiGridComponent,
  SwuiGridDataService,
  SwuiNotificationsService,
  SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import { of, Subject, zip } from 'rxjs';
import { catchError, filter, finalize, map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { FileType, simulateBrowserDownload } from '../../../common/lib/files';
import { GameProviderService } from '../../../common/services/game-provider.service';
import { GameService } from '../../../common/services/game.service';
import { Game, GameInfo } from '../../../common/typings';
import { GameProvider } from '../game-provider.model';
import { SCHEMA_FILTER, SCHEMA_LIST } from '../schema';
import { CloneGameComponent } from './modals/clone-game/clone-game.component';
import { SetJackpotComponent } from './modals/set-jackpot/set-jackpot.component';


@Component({
    selector: 'games-list',
    templateUrl: './games-list.component.html',
    styleUrls: ['./games-list.component.scss'],
    providers: [
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: GameService }
    ],
    standalone: false
})
export class GamesListComponent implements OnDestroy {
  @ViewChild('grid', { static: true }) grid: SwuiGridComponent<GameInfo>;

  readonly schema = SCHEMA_LIST;
  readonly filterSchema = SCHEMA_FILTER;
  readonly actions: RowAction[];
  readonly panelActions: PanelAction[];

  private readonly destroyed$ = new Subject<void>();

  constructor( private readonly router: Router,
               private readonly service: GameService,
               private readonly gameProviderService: GameProviderService<GameProvider>,
               private readonly notifications: SwuiNotificationsService,
               private readonly translate: TranslateService,
               private readonly authService: SwHubAuthService,
               private readonly dialog: MatDialog
  ) {
    this.panelActions = this.getPanelActions();
    this.actions = this.getActions();

    const providerId = this.filterSchema.find(s => s.field === 'providerId');
    if (providerId) {
      (providerId as SelectInputOptionData).data =
        this.gameProviderService.getGameProviders({ isTest: false }).pipe(
          map(providers => providers.map(provider => provider.toSelectOption()))
        );
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  private onCloneGameAction( game: Game ) {
    const provider$ = this.gameProviderService.getGameProviders({ isTest: false }).pipe(
      map(providers => providers.find(provider => provider.code === game.providerCode))
    );

    const game$ = this.service.getGame(game.code);

    zip(game$, provider$).pipe(
      map(
        ( [gameData, providerData] ) => {
          game.limits = gameData.limits;
          game.url = gameData.url;
          game.providerGameCode = gameData.providerGameCode;
          game.providerId = providerData.id;
          game.historyRenderType = gameData.historyRenderType;
          game.clientFeatures = gameData.clientFeatures;

          if (gameData.schemaDefinitionId) {
            game.schemaDefinitionId = gameData.schemaDefinitionId;
          } else {
            delete game.schemaDefinitionId;
          }

          if (gameData.countries && Array.isArray(gameData.countries) && gameData.countries.length) {
            game.countries = gameData.countries;
          } else {
            game.countries = [];
          }

          return game;
        }
      ),
      takeUntil(this.destroyed$)
    ).subscribe(( gameInfo: Game ) => this.dialog.open(CloneGameComponent, {
        width: '500px',
        data: gameInfo,
        disableClose: true
      }).afterClosed()
        .pipe(
          filter(result => !!result),
          map(( gameData: Game ) => {
            gameData.gameCode = gameData.code;
            delete gameData.code;
            return gameData;
          }),
          switchMap(( gameData: Game ) => this.service.createGame(gameData)),
          switchMap(() => this.translate.get('ENTITY_SETUP.GAMES.notificationGameCreated', { game: game.title })),
          tap(( message: string ) => this.notifications.success(message, '')),
          catchError(( error: HttpErrorResponse ) => of(this.notifications.error(error.error.message))),
          finalize(() => this.grid.dataSource.loadData()),
          takeUntil(this.destroyed$)
        ).subscribe()
    );
  }

  private onEditGameAction( game: Game ) {
    this.router.navigate(['/pages/settings/games-management/edit', game.code]);
  }

  private onSetJackpot( game: Game ) {
    this.dialog.open(SetJackpotComponent,
      {
        data: game,
        disableClose: true
      });
  }

  private getPanelActions(): PanelAction[] {
    return [{
      title: 'ENTITY_SETUP.GAMES.createGame',
      color: 'primary',
      icon: 'add',
      actionFn: () => this.router.navigate(['/pages/settings/games-management/create']),
      availableFn: () => this.authService.allowedTo(['keyentity:gameprovider:game:create'])
    }];
  }

  private getActions(): RowAction[] {
    return [
      new RowAction({
        icon: 'add',
        title: 'ENTITY_SETUP.GAMES.cloneGame',
        fn: this.onCloneGameAction.bind(this),
      }),
      new RowAction({
        icon: 'edit',
        title: 'ENTITY_SETUP.GAMES.editGame',
        fn: this.onEditGameAction.bind(this),
      }),
      new RowAction({
        icon: 'insert_link',
        title: 'ENTITY_SETUP.GAMES.setJp',
        fn: this.onSetJackpot.bind(this),
      }),
      new RowAction({
        icon: 'file_download',
        title: 'ENTITY_SETUP.GAMES.exportConfig',
        fn: function( game: Game ) {
          const { features = {}, clientFeatures = {}, settings = {}, providerGameCode } = game;
          const data = { features, clientFeatures, settings };
          simulateBrowserDownload(data, `[${providerGameCode}]gameServerSideConfiguration`, FileType.Json);
        },
      })
    ];
  }
}

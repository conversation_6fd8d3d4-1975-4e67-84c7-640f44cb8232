import { Component, Inject, Input } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

import { Game } from '../../../../../common/typings';
import { ErrorMessage } from '../../../../../common/components/mat-user-editor/user-form.component';
import { MESSAGE_ERROR } from '../../../games-managment.constants';


@Component({
    selector: 'clone-game',
    templateUrl: './clone-game.component.html',
    styleUrls: ['../../games-list.component.scss'],
    standalone: false
})
export class CloneGameComponent {
  public messageErrors: ErrorMessage = MESSAGE_ERROR;

  public form: FormGroup;

  private _game: Game;

  @Input()
  set game( value: Game ) {
    if (!value) return;
    this._game = value;
    this.form.patchValue(value);
  }

  get game(): Game {
    return this._game;
  }

  constructor( public dialogRef: MatDialogRef<CloneGameComponent>,
               @Inject(MAT_DIALOG_DATA) public data: Game,
               private fb: FormBuilder ) {
    this.form = this.fb.group({
      code: ['', [Validators.required]],
      title: ['', [Validators.required]],
      url: ['', [Validators.required]]
    });

    this.game = data;
  }

  get codeControl(): FormControl {
    return this.form.get('code') as FormControl;
  }

  get titleControl(): FormControl {
    return this.form.get('title') as FormControl;
  }

  get urlControl(): FormControl {
    return this.form.get('url') as FormControl;
  }

  onNoClick() {
    this.dialogRef.close(null);
  }

  onConfirmClick() {
      if (this.form.valid) {
        let game = { ...this.game, ...this.form.value };
        this.dialogRef.close(game);
        this.form.get('code').disable();
        this.form.get('title').disable();
        this.form.get('url').disable();
        this.form.addControl('entityPath', this.fb.control('', Validators.required));
      }
    }
}

import { coerceArray } from '@angular/cdk/coercion';
import { Component, Inject, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { forkJoin, of, throwError } from 'rxjs';
import { catchError, switchMap, take } from 'rxjs/operators';
import { GameService } from '../../../../../common/services/game.service';
import { JackpotService } from '../../../../../common/services/jackpot.service';
import { Game, isLiveGame } from '../../../../../common/typings';

const jpInitialisedCode = 26;
const jpHasInstanceCode = 27;

@Component({
    selector: 'set-jackpot',
    templateUrl: './set-jackpot.component.html',
    styleUrls: ['./set-jackpot.component.scss'],
    standalone: false
})
export class SetJackpotComponent implements OnInit {
  jackpotTypeControl = new FormControl('');

  constructor( public dialogRef: MatDialogRef<SetJackpotComponent>,
               @Inject(MAT_DIALOG_DATA) public data: Game,
               private jackpotService: JackpotService,
               private notificationsService: SwuiNotificationsService,
               private gameService: GameService,
               private translate: TranslateService
  ) {
  }

  ngOnInit(): void {
  }

  onNoClick() {
    this.dialogRef.close(null);
  }

  onCreateClick() {
    const jpType = this.jackpotTypeControl.value.toLowerCase();
    this.jackpotService.initJackpot(jpType)
      .pipe(
        catchError(err => {
          if (err.error.code !== jpInitialisedCode) {
            this.notificationsService.error(err.error?.message);
            return throwError(err);
          }

          return of(null);
        }),
        switchMap(() => {
          const jackpot = {
            id: jpType.toUpperCase(),
            type: jpType
          };

          return this.jackpotService.createJackpot(jackpot);
        }),
        catchError(err => {
          if (err.error.code !== jpHasInstanceCode) {
            this.notificationsService.error(err.error?.message);
            return throwError(err);
          }

          return of(null);
        }),
        switchMap(() => {
          const jackpotTypes = coerceArray(this.data?.features?.jackpotTypes || []);
          const features = { ...this.data.features, jackpotTypes: [jpType, ...jackpotTypes] };
          const settings = { jackpotId: { [jpType]: jpType.toUpperCase() } };

          return forkJoin([
            this.gameService.patchGame(this.data.code, { features }),
            this.gameService.patchGameDetails(this.data.code, '', isLiveGame(this.data), { settings })
          ]);
        }),
        catchError(err => {
          this.notificationsService.error(err.error?.message);
          return throwError(err);
        }),
        take(1)
      )
      .subscribe(() => {
        this.notificationsService.success(
          this.translate.instant('ENTITY_SETUP.GAMES.SET_JACKPOT.set-notification', { gameCode: this.data.code })
        );
        this.dialogRef.close();
      });
  }
}

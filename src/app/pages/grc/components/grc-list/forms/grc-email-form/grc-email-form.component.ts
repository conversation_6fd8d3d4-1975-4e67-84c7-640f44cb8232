import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { <PERSON>Sani<PERSON>zer, SafeHtml } from '@angular/platform-browser';
import { ErrorMessage } from '../../../../../../common/components/mat-user-editor/user-form.component';
import { ValidationService } from '../../../../../../common/services/validation.service';
import { GrcEmail } from '../../../../../../common/typings/grc-config';
import { MESSAGE_ERROR } from '../../../../grc-errors.constants';


@Component({
    selector: 'grc-email-form',
    templateUrl: './grc-email-form.component.html',
    styleUrls: ['../../../../grc.component.scss'],
    standalone: false
})

export class GrcEmailFormComponent {
  @Output() onFormSubmitted: EventEmitter<GrcEmail> = new EventEmitter();
  @Output() isFormValid: EventEmitter<boolean> = new EventEmitter();

  public messageErrors: ErrorMessage = MESSAGE_ERROR;
  public submitted: boolean;
  public emailForm: FormGroup;
  public emailHtmlTemplate: SafeHtml;
  public recipientsArray: FormArray = this.fb.array([]);

  @Input()
  set email(email: GrcEmail) {
    if (!email) return;
    this.patchForm(email);
  }

  constructor( private fb: FormBuilder,
               private sanitizer: DomSanitizer) {
    this.emailForm = this.initTemplatesForm();
    this.emailForm.valueChanges.subscribe( data => {
      this.isFormValid.emit(this.emailForm.valid);
      this.processRecipients(data['recipients']);
    });
  }

  public onFormSubmitFn() {
    this.submitted = true;
    const email = this.processOutputForm();
    this.onFormSubmitted.emit(email);
  }

  get fromNameControl(): FormControl {
    return this.emailForm.get('fromName') as FormControl;
  }

  get recipientsControl(): FormControl {
    return this.emailForm.get('recipients') as FormControl;
  }
  get subjectControl(): FormControl {
    return this.emailForm.get('subject') as FormControl;
  }

  private initTemplatesForm(): FormGroup {
    return this.fb.group({
      fromName: ['', Validators.required],
      subject: [{ value: '', disabled: true }],
      body: [{ value: '', disabled: true }],
      recipients: ['', Validators.required]
    });
  }

  private patchForm(email: GrcEmail) {
    let processedEmail = {
      body: email.body,
      subject: email.subject,
      recipients: email.recipients.join('\n'),
      fromName: email.fromName,
      fromEmail: email.fromEmail,
    };

    this.emailHtmlTemplate = this.sanitizer.bypassSecurityTrustHtml(email.body);
    this.emailForm.patchValue(processedEmail);
  }

  private processRecipients(value: string) {
    let recipients = [];
    if (value.length > 0) {
      recipients = value.split('\n');
    }

    if (recipients && recipients.length) {
      this.clearFormArray(this.recipientsArray);
      recipients.filter(domain => !!domain)
        .forEach( item => {
          this.recipientsArray.push(
            this.fb.control(item, ValidationService.emailValidator)
          );
        });
      this.recipientsArray.updateValueAndValidity();
    }
  }

  private processOutputForm(): GrcEmail {
    if (this.emailForm.valid && this.recipientsArray.valid) {
      let email = <GrcEmail>this.emailForm.getRawValue();
      email.recipients = [];
      this.recipientsArray.controls.forEach( control => {
        email.recipients.push(control.value);
      });
      return email;
    }
  }

  private clearFormArray( formArray: FormArray ): void {
    while (formArray.controls.length) {
      formArray.removeAt(0);
    }
  }
}

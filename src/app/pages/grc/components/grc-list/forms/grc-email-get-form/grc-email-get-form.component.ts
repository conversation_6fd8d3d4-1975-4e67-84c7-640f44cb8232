import { Component, EventEmitter, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ErrorMessage } from '../../../../../../common/components/mat-user-editor/user-form.component';

import { GrcEmailStart } from '../../../../../../common/typings/grc-config';
import { MESSAGE_ERROR } from '../../../../grc-errors.constants';


@Component({
    selector: 'grc-email-get-form',
    templateUrl: './grc-email-get-form.component.html',
    standalone: false
})

export class GrcEmailGetFormComponent {
  public messageErrors: ErrorMessage = MESSAGE_ERROR;
  @Output() onFormSubmitted: EventEmitter<GrcEmailStart> = new EventEmitter();
  @Output() isFormValid: EventEmitter<boolean> = new EventEmitter();

  public submitted: boolean;
  public form: FormGroup;

  constructor( private fb: FormBuilder ) {
    this.form = this.initTemplatesForm();
    this.form.valueChanges.subscribe(() => {
      this.isFormValid.emit(this.form.valid);
    });
  }

  public onFormSubmitFn() {
    this.submitted = true;
    if (this.form.valid) {
      this.onFormSubmitted.emit(this.form.value);
    }
  }

  private initTemplatesForm(): FormGroup {
    return this.fb.group({
      nameEn: ['', Validators.required],
      nameCh: ['', Validators.required]
    });
  }

  get nameEnControl(): FormControl {
    return this.form.get('nameEn') as FormControl;
  }

  get nameCnControl(): FormControl {
    return this.form.get('nameCh') as FormControl;
  }
}

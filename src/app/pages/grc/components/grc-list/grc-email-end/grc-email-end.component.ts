import { Component, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { switchMap } from 'rxjs/operators';
import { GrcService } from '../../../../../common/services/grc.service';

import { Grc, GrcEmail } from '../../../../../common/typings/grc-config';


@Component({
    selector: 'grc-email-end',
    templateUrl: 'grc-email-end.component.html',
    styleUrls: ['../../../grc.component.scss'],
    standalone: false
})

export class GrcEmailEndComponent {
  @Input() challenge: Grc;

  public isSendFormValid: boolean;
  public emailToSend: GrcEmail;
  public loadingSend: boolean;
  public loadingGet: boolean;

  constructor( private grcService: GrcService<Grc>,
               private notifications: SwuiNotificationsService,
               private translate: TranslateService) {

  }

  getTemplate() {
    const emailType = 'cancel';
    this.loadingGet = true;
    this.grcService.getEmailTemplates(this.challenge.id, emailType)
      .pipe(
        switchMap((email: GrcEmail) => {
            this.emailToSend = email;
            return this.translate.get('GRC_BLOCK.EMAIL.notificationEmailGetSuccess');
          }
        )
      )
      .subscribe(
        message => {
          this.notifications.success(message);
          this.loadingGet = false;
        },
        () => {
          this.loadingGet = false;
        }
      );
  }

  onSubmitSendForm(email: GrcEmail) {
    this.loadingSend = true;
    this.grcService.sendEmails(email)
      .pipe(
        switchMap(() => this.translate.get('GRC_BLOCK.EMAIL.notificationEmailSendSuccess'))
      )
      .subscribe(
        message => {
          this.notifications.success(message);
          this.loadingSend  = false;
        },
        () => {
          this.loadingSend = false;
        }
      );
  }

  handleSendFormValid(isValid: boolean) {
    this.isSendFormValid = isValid;
  }
}

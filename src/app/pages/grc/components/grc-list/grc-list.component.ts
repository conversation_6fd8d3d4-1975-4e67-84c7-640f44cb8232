import { HttpErrorResponse } from '@angular/common/http';
import { Component, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';

import { TranslateService } from '@ngx-translate/core';
import {
  RowAction, SwuiGridComponent, SwuiGridDataService, SwuiNotificationsService, SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil, tap } from 'rxjs/operators';
import { BoConfirmationComponent } from '../../../../common/components/bo-confirmation/bo-confirmation.component';
import { GrcService } from '../../../../common/services/grc.service';
import { Currency } from '../../../../common/typings';
import { Grc } from '../../../../common/typings/grc-config';

import { AddGrcModalComponent } from './modals/add-grc-modal.component';
import { EditDefinitionModalComponent } from './modals/edit-definition-modal.component';
import { GrcEmailModalComponent } from './modals/grc-email-modal.component';
import { SCHEMA_FILTER, SCHEMA_LIST } from './schema';

export const GRC_AVAILABLE_STATUSES_FOR_END = [
  'CREATED', 'INITIALIZED', 'STARTED_WITHOUT_RESERVE', 'FROZEN', 'STARTED', 'STOPPED'
];

export const GRC_AVAILABLE_STATUSES_FOR_EDIT = [
  'CREATED', 'INITIALIZED', 'STARTED_WITHOUT_RESERVE', 'FROZEN', 'STARTED'
];

@Component({
    selector: 'grc-list',
    templateUrl: './grc-list.component.html',
    styleUrls: ['./../../grc.component.scss'],
    providers: [
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: GrcService }
    ],
    standalone: false
})

export class GrcListComponent {

  @ViewChild('grid', { static: true }) public grid: SwuiGridComponent<Grc>;

  public schema = SCHEMA_LIST;
  public filterSchema = SCHEMA_FILTER;
  public actions: RowAction[] = [];
  public challengeToRemove: Grc;
  public currencies: Currency[] = [];

  private endStatuses = [];
  private editStatuses = [];
  private readonly destroyed$ = new Subject<void>();

  constructor( public translate: TranslateService,
               public service: GrcService<Grc>,
               public notifications: SwuiNotificationsService,
               private route: ActivatedRoute,
               private dialog: MatDialog,
  ) {
    this.endStatuses = GRC_AVAILABLE_STATUSES_FOR_END;
    this.editStatuses = GRC_AVAILABLE_STATUSES_FOR_EDIT;
  }

  ngOnInit() {
    this.setActions();
  }

  setActions() {
    this.actions = [
      new RowAction({
        icon: 'delete',
        title: 'GRC_BLOCK.endChallenge',
        fn: this.showConfirmationModal.bind(this),
        canActivateFn: ( row: any ) => this.endStatuses.indexOf(row.state) > -1,
      }),
      new RowAction({
        icon: 'edit',
        title: 'GRC_BLOCK.DEFINITION.editDefinition',
        fn: this.showEditDefinitionModal.bind(this),
        canActivateFn: ( row: any ) => this.editStatuses.indexOf(row.state) > -1,
      }),
      new RowAction({
        icon: 'email',
        title: 'GRC_BLOCK.EMAIL.emailManagement',
        fn: this.showEmailModal.bind(this),
      })
    ];
  }

  public showAddGrcModal() {
    this.dialog.open(AddGrcModalComponent, {
      width: '800px',
      data: {
        currencies: this.route.snapshot.data['currencies']
      },
      disableClose: true
    })
      .afterClosed()
      .pipe(
        filter(data => !!data),
        switchMap(( grcJSON: Grc ) => this.service.setNewGrc(grcJSON)),
        switchMap(() => this.translate.get('GRC_BLOCK.notificationCreated')),
        tap(message => this.notifications.success(message, '')),
        takeUntil(this.destroyed$),
      )
      .subscribe(
        () => {
          this.grid.dataSource.loadData();
        },
        ( error: HttpErrorResponse ) => {
          this.notifications.error(error?.error?.message);
        }
      );
  }

  private showConfirmationModal( challenge: Grc ) {
    this.challengeToRemove = challenge;
    this.dialog.open(BoConfirmationComponent, {
      width: '500px',
      data: { message: 'GRC_BLOCK.notificationRemove' },
      disableClose: true
    }).afterClosed().pipe(
      filter(value => !!value),
      switchMap(() => this.service.deleteItem(this.challengeToRemove.id)),
      switchMap(() =>
        this.translate.get('GRC_BLOCK.notificationChallengeRemoved', { challengeId: this.challengeToRemove.id })),
      tap(message => this.notifications.success(message, '')),
      takeUntil(this.destroyed$),
    ).subscribe(() => {
      this.challengeToRemove = null;
      this.grid.dataSource.loadData();
    });
  }

  private showEmailModal( grc: Grc ) {
    this.dialog.open(GrcEmailModalComponent, {
      width: '800px',
      height: 'calc(80vh - 100px)',
      data: {
        grc
      },
      disableClose: true
    }).afterClosed().pipe(
      filter(data => !!data),
      takeUntil(this.destroyed$),
    ).subscribe(() => {
      this.grid.dataSource.loadData();
    });
  }

  private showEditDefinitionModal( grc: Grc ) {
    this.dialog.open(EditDefinitionModalComponent, {
      width: '800px',
      data: {
        grc
      },
      disableClose: true
    })
      .afterClosed()
      .pipe(
        filter(data => !!data),
        switchMap(( grcJSON: Grc ) => this.service.updateDefinition(grc.id.toString(), grcJSON.definition)),
        switchMap(() => this.translate.get('GRC_BLOCK.DEFINITION.notificationDefinitionUpdated')),
        tap(message => this.notifications.success(message, '')),
        takeUntil(this.destroyed$),
      )
      .subscribe(
        () => {
          this.grid.dataSource.loadData();
        },
        ( error: HttpErrorResponse ) => {
          this.notifications.error(error?.error?.message);
        }
      );
  }
}

import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import {
  SwuiControlMessagesModule, SwuiDatePickerModule, SwuiGridModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule, SwuiSelectModule
} from '@skywind-group/lib-swui';
import { BoConfirmationModule } from 'src/app/common/components/bo-confirmation/bo-confirmation.module';
import { CalendarModule } from '../../../../common/components/calendar/calendar.module';
import { ControlMessagesModule } from '../../../../common/components/control-messages/control-messages.module';
import { TrimInputValueModule } from '../../../../common/directives/trim-input-value/trim-input-value.module';
import { GrcService } from '../../../../common/services/grc.service';
import { GrcEmailFormComponent } from './forms/grc-email-form/grc-email-form.component';
import { GrcEmailGetFormComponent } from './forms/grc-email-get-form/grc-email-get-form.component';
import { GrcEmailEndComponent } from './grc-email-end/grc-email-end.component';
import { GrcEmailStartComponent } from './grc-email-start/grc-email-start.component';
import { GrcListComponent } from './grc-list.component';

import { AddGrcModalComponent } from './modals/add-grc-modal.component';
import { EditDefinitionModalComponent } from './modals/edit-definition-modal.component';
import { GrcEmailModalComponent } from './modals/grc-email-modal.component';

@NgModule({
    imports: [
        BoConfirmationModule,
        CalendarModule,
        CommonModule,
        ControlMessagesModule,
        ReactiveFormsModule,
        TranslateModule,
        SwuiPagePanelModule,
        MatCardModule,
        SwuiGridModule,
        MatIconModule,
        MatTooltipModule,
        MatButtonModule,
        SwuiSchemaTopFilterModule,
        MatDialogModule,
        LayoutModule,
        MatFormFieldModule,
        MatInputModule,
        SwuiControlMessagesModule,
        SwuiDatePickerModule,
        SwuiSelectModule,
        MatTabsModule,
        TrimInputValueModule
    ],
  exports: [
    GrcListComponent,
  ],
  declarations: [
    AddGrcModalComponent,
    EditDefinitionModalComponent,
    GrcEmailFormComponent,
    GrcEmailEndComponent,
    GrcEmailGetFormComponent,
    GrcEmailModalComponent,
    GrcEmailStartComponent,
    GrcListComponent,
  ],
  providers: [
    GrcService,
  ],
})

export class GrcListModule {
}

import { Component, Inject } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ErrorMessage } from '../../../../../common/components/mat-user-editor/user-form.component';
import { ValidationService } from '../../../../../common/services/validation.service';
import { MESSAGE_ERROR } from '../../../grc-errors.constants';


@Component({
    selector: 'edit-definition-modal',
    templateUrl: 'edit-definition-modal.component.html',
    styleUrls: ['../../../grc.component.scss'],
    standalone: false
})
export class EditDefinitionModalComponent {
  public form: FormGroup;
  public messageErrors: ErrorMessage = MESSAGE_ERROR;

  constructor(private fb: FormBuilder,
              public dialogRef: MatDialogRef<EditDefinitionModalComponent>,
              @Inject(MAT_DIALOG_DATA) data: any) {
    this.form = this.initAddForm();

    if (data && data.grc.definition) {
      this.definitionGroup.patchValue(data.grc.definition);
    }
  }

  initAddForm(): FormGroup {
    return this.fb.group({
      definition: this.fb.group({
        logoUrl: ['', ValidationService.IfNotEmpty(ValidationService.urlValidation)]
      })
    });
  }

  get formGroup(): FormGroup {
    return this.form as FormGroup;
  }

  get definitionGroup(): FormGroup {
    return this.formGroup.get('definition') as FormGroup;
  }

  get logoUrlControl(): FormControl {
    return this.definitionGroup.get('logoUrl') as FormControl;
  }

  onNoClick() {
    this.dialogRef.close(null);
  }

  onConfirmClick() {
    this.dialogRef.close(this.form.value);
  }
}

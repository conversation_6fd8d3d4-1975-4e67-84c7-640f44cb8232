import { Component, Inject, Input } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Grc } from '../../../../../common/typings/grc-config';


@Component({
    selector: 'grc-email-modal',
    templateUrl: 'grc-email-modal.component.html',
    styleUrls: ['../../../grc.component.scss'],
    standalone: false
})

export class GrcEmailModalComponent {
  @Input() challenge: Grc;

  constructor(public dialogRef: MatDialogRef<GrcEmailModalComponent>,
              @Inject(MAT_DIALOG_DATA) data: any ) {
    this.challenge = data.grc;
  }

  onNoClick() {
    this.dialogRef.close(null);
  }
}

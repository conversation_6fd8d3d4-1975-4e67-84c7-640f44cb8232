import { Component } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { IdService } from '../../common/services/id.service';
import { finalize, take } from 'rxjs/operators';
import { ErrorMessage } from '../../common/components/mat-user-editor/user-form.component';
import { BehaviorSubject } from 'rxjs';

@Component({
    selector: 'id-transcode',
    templateUrl: './id-transcode.component.html',
    styleUrls: ['./id-transcode.component.scss'],
    standalone: false
})
export class IdTranscodeComponent {
  idControl = new FormControl('', [Validators.required]);
  resultControl = new FormControl({
    value: '',
    disabled: true
  });
  messageErrors: ErrorMessage = {
    required: 'VALIDATION.required'
  };
  loading$ = new BehaviorSubject(false);

  constructor(private idService: IdService) {
  }

  onDecode() {
    const value = this.idControl.value.replace('\n', ',').replaceAll(' ', ',').split(',').filter(val => !!val);
    this.resultControl.setValue('');
    this.loading$.next(true);

    this.idService.decode(value)
      .pipe(
        take(1),
        finalize(() => this.loading$.next(false))
      )
      .subscribe(val => {
        this.resultControl.setValue(val);
        this.loading$.next(false);
      });
  }

  onEncode() {
    const value = this.idControl.value.replace('\n', ',').replaceAll(' ', ',').split(',').filter(val => !!val);
    this.resultControl.setValue('');
    this.loading$.next(true);

    this.idService.encode(value)
      .pipe(
        take(1),
        finalize(() => this.loading$.next(false))
      )
      .subscribe(val => {
        this.resultControl.setValue(val);
      });
  }
}

import { AfterViewInit, Component, Input, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { IntegrationsTestHistory } from '../../../common/typings/integration';


@Component({
    selector: 'integration-test-history',
    templateUrl: './integration-test-history.component.html',
    styleUrls: ['./integration-test-history.component.scss'],
    standalone: false
})
export class IntegrationTestHistoryComponent implements AfterViewInit {
  @ViewChild(MatPaginator) paginator: MatPaginator;

  @Input() path: string = '';

  @Input()
  set testHistoryResult( val: IntegrationsTestHistory[] | undefined ) {
    const data = val ?? [];
    data.sort(( a, b ) => b.id - a.id);
    this.dataSource.data = data;
  }

  readonly displayedColumns: string[] = ['id', 'createdAt', 'merchantName', 'details'];
  readonly dataSource = new MatTableDataSource<IntegrationsTestHistory>();

  constructor( private readonly router: Router ) {
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }

  showTestResult( testId: number ) {
    const url = this.router.createUrlTree(['integration-test-result'], {
      queryParams: {
        testId,
        ...(this.path && this.path !== ':' ? { path: this.path } : {})
      }
    });
    window.open(this.router.serializeUrl(url), '_blank');
  }
}

import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { SwuiNotificationsService, SwuiSelectOption } from '@skywind-group/lib-swui';
import { BehaviorSubject, of, Subject } from 'rxjs';
import { catchError, delay, filter, finalize, map, repeat, retryWhen, switchMap, takeUntil, tap } from 'rxjs/operators';
import { GameService } from '../../../common/services/game.service';
import { IntegrationService } from '../../../common/services/integration.service';
import { BoConfirmationComponent } from '../../../common/components/bo-confirmation/bo-confirmation.component';
import { HttpErrorResponse } from '@angular/common/http';
import { Entity } from '../../../common/models/entity.model';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { EntityService } from '../../../common/services/entity.service';


@Component({
    selector: 'integration-test-merchant',
    templateUrl: './integration-test-merchant.component.html',
    styleUrls: ['./integration-test-merchant.component.scss'],
    standalone: false
})
export class IntegrationTestMerchantComponent implements OnInit {
  @Input()
  set merchant( entity: Entity | undefined ) {
    this.form.patchValue(entity?.merchant ?? {});
    this.merchant$.next(entity);
    this.currencies = (entity?.currencies ?? []).map<SwuiSelectOption>(id => ({ id, text: id.toUpperCase() }));
  }

  get merchant(): Entity | undefined {
    return this.merchant$.value;
  }

  @Output() testFinished: EventEmitter<void> = new EventEmitter<void>();

  readonly form: FormGroup;

  loading = false;
  games: SwuiSelectOption[] = [];
  currencies: SwuiSelectOption[] = [];

  private readonly merchant$ = new BehaviorSubject<Entity | undefined>(undefined);
  private readonly destroyed$ = new Subject<void>();

  constructor( fb: FormBuilder,
               private readonly dialog: MatDialog,
               private readonly translation: TranslateService,
               private readonly entityService: EntityService<Entity>,
               private readonly notifications: SwuiNotificationsService,
               private readonly integrationService: IntegrationService,
               private readonly gameService: GameService
  ) {
    this.form = fb.group({
      code: [{ value: '', disabled: true }],
      type: [{ value: '', disabled: true }],
      custId: [''],
      currencyCode: [''],
      gameCode: [''],
      secondGameCode: [''],
      ticket: [''],
      params: fb.group({
        serverUrl: [{ value: '', disabled: true }],
        password: [{ value: '', disabled: true }],
      }),
      specialCases: fb.group({
        isNeedJPTests: false,
        isNeedFreeBetTests: false,
        isNeedMainTests: false,
        isNeedBonusAPITests: false,
        isNeedMultiSessionTests: false,
        isNeedCrossGameMultiSessionTests: false,
        isNeedMultibetRollbackTests: false,
        shouldValidateTicketOnlyOnce: false,
      }),
    });
  }

  ngOnInit() {
    this.merchant$.pipe(
      switchMap(merchant => merchant ? this.gameService.getAllGames(merchant?.path, undefined, true) : of([])),
      map(games => games.map<SwuiSelectOption>(( { code, title } ) => ({ id: code, text: `${title} (${code})` }))),
      takeUntil(this.destroyed$)
    ).subscribe(games => {
      this.games = games;
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  handleSaveAction() {
    const entity = this.merchant;
    if (entity && this.form.valid) {
      const { code, params: { password, serverUrl } } = this.form.getRawValue();
      const data = entity.asUpdateMerchantData();
      data.code = code;
      data.params.password = password;
      data.params.serverUrl = serverUrl;

      this.dialog.open(BoConfirmationComponent, {
        width: '500px',
        disableClose: true,
        data: { message: 'INTEGRATIONS.confirmMessage' }
      }).afterClosed().pipe(
        filter(Boolean),
        switchMap(() => this.entityService.updateMerchantEntityItem(data, entity.path)),
        switchMap(() => this.translation.get('INTEGRATIONS.notificationConfigSaved')),
        tap(( message ) => this.notifications.success(message, '')),
        catchError(( error: HttpErrorResponse ) => of(this.notifications.error(error.error.message))),
        takeUntil(this.destroyed$)
      ).subscribe();
    }
  }

  runTest() {
    const entity = this.merchant;
    if (entity && this.form.valid) {
      const values = this.form.getRawValue();
      const body = {
        code: values.code,
        type: values.type,
        pass: values.params.password,
        url: values.params.serverUrl,
        custId: values.custId,
        currencyCode: values.currencyCode,
        ticket: values.ticket,
        gameCode: values.gameCode,
        secondGameCode: values.secondGameCode,
        specialCases: values.specialCases
      };
      this.loading = true;
      this.integrationService.test(JSON.stringify(body), entity.path).pipe(
        filter(Boolean),
        switchMap(( { id } ) => this.integrationService.getTestsReports(id, entity.path)
          .pipe(
            tap(( { status } ) => {
              if (status === 'in progress') {
                throw status;
              }
            }),
            retryWhen(errors => errors.pipe(
              delay(2000),
              repeat(10)
            )),
          )),
        finalize(() => {
          this.loading = false;
        }),
        takeUntil(this.destroyed$)
      ).subscribe(() => {
        this.testFinished.emit();
      });
    }
  }
}

import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiSelectModule } from '@skywind-group/lib-swui';
import { TrimInputValueModule } from '../../../common/directives/trim-input-value/trim-input-value.module';
import { GameService } from '../../../common/services/game.service';
import { IntegrationTestMerchantComponent } from './integration-test-merchant.component';
import { RouterModule } from '@angular/router';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';


@NgModule({
  declarations: [
    IntegrationTestMerchantComponent
  ],
  exports: [
    IntegrationTestMerchantComponent
  ],
    imports: [
        CommonModule,
        TranslateModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        LayoutModule,
        MatButtonModule,
        MatIconModule,
        MatTooltipModule,
        SwuiSelectModule,
        RouterModule,
        MatCheckboxModule,
        TrimInputValueModule,
        MatProgressSpinnerModule
    ],
  providers: [GameService]
})
export class IntegrationTestMerchantModule {
}

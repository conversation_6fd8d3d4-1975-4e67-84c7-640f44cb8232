import { Component, Input, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { MatAccordion } from '@angular/material/expansion';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil, tap } from 'rxjs/operators';
import { FileType, simulateBrowserDownload } from '../../../common/lib/files';

import { IntegrationService } from '../../../common/services/integration.service';
import { Integrations } from '../../../common/typings/integration';


@Component({
    selector: 'sw-integration-test-result',
    templateUrl: './integration-test-result.component.html',
    styleUrls: ['./integration-test-result.component.scss'],
    standalone: false
})
export class IntegrationTestResultComponent implements OnDestroy, OnInit {
  @ViewChild(MatAccordion) accordion: MatAccordion;
  @Input() path: string = '';

  @Input()
  set testResult( val: Integrations ) {
    this._testResult = val;
  }

  get testResult() {
    return this._testResult;
  }

  isAccordionOpen: boolean = false;
  isOpenedFailed: boolean = false;
  private _testResult: Integrations;
  private _destroyed$ = new Subject<void>();

  constructor(
    private activatedRoute: ActivatedRoute,
    private integrationService: IntegrationService,
    private titleService: Title
  ) {
    const { snapshot } = activatedRoute;
    const { testId, path } = snapshot.queryParams;
    this.integrationService.getTestsReports(testId || '', path)
      .pipe(
        tap(( data: Integrations ) => {
          const { total } = data.report.start;
          const { start } = data.report.end;
          this.titleService.setTitle(`Integration_Test_${path}_n${total}_${start}`);
        }),
        takeUntil(this._destroyed$)
      ).subscribe(data => this.testResult = data);
  }

  ngOnInit(): void {
  }

  ngOnDestroy(): void {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  downloadResults() {
    const { testId, path } = this.activatedRoute.snapshot.queryParams;
    this.integrationService.getTestsReportsPage(testId || '', path)
      .subscribe(data => {
        const { merchType, merchCode } = this.testResult;
        simulateBrowserDownload(data, `Integration_Test_${merchType}_${merchCode}`, FileType.Html);
      });
  }

  openAll() {
    this.isAccordionOpen = !this.isAccordionOpen;
    this.accordion._openCloseAllActions.next(this.isAccordionOpen);
    if (!this.isAccordionOpen) {
      this.isOpenedFailed = false;
    }
  }

  openFailed() {
    this.isOpenedFailed = !this.isOpenedFailed;
  }
}

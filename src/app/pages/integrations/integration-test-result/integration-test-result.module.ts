import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatTooltipModule } from '@angular/material/tooltip';

import { TranslateModule } from '@ngx-translate/core';
import { IntegrationService } from '../../../common/services/integration.service';
import { IntegrationTestResultComponent } from './integration-test-result.component';
import { IntegrationTestResultRoutingModule } from './integration-test-result.routing';


@NgModule({
  declarations: [
    IntegrationTestResultComponent
  ],
    imports: [
        CommonModule,
        MatExpansionModule,
        IntegrationTestResultRoutingModule,
        MatButtonModule,
        MatIconModule,
        TranslateModule,
        LayoutModule,
        MatInputModule,
        MatDividerModule,
        MatListModule,
        MatTooltipModule,
    ],
  exports: [],
  providers: [
    IntegrationService
  ]
})
export class IntegrationTestResultModule {
}

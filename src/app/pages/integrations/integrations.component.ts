import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SwHubEntityService } from '@skywind-group/lib-swui';
import moment from 'moment';
import { of, Subject } from 'rxjs';
import { map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { Entity, EntityType } from '../../common/models/entity.model';
import { EntityDataSourceService } from '../../common/services/entity-data-source.service';
import { EntityService } from '../../common/services/entity.service';
import { IntegrationService } from '../../common/services/integration.service';
import { IntegrationsTestHistory } from '../../common/typings/integration';


@Component({
    selector: '[integrations]',
    templateUrl: 'integrations.component.html',
    styleUrls: ['./integrations.component.scss'],
    standalone: false
})
export class IntegrationsComponent implements OnInit, OnD<PERSON>roy {
  entityType: EntityType;
  entity: Entity;
  historyResult: IntegrationsTestHistory[] = [];

  readonly loadTestHistory$ = new Subject<void>();
  private readonly destroyed$ = new Subject<void>();

  constructor( route: ActivatedRoute,
               private entityService: EntityService<Entity>,
               private integrationService: IntegrationService,
               private readonly hubEntityService: SwHubEntityService,
               private readonly entityDataSourceService: EntityDataSourceService,
  ) {
    const { brief } = route.snapshot.data;
    this.entityType = brief.type;
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
    this.entityDataSourceService.hide();
  }

  ngOnInit(): void {
    this.entityDataSourceService.show();

    this.loadTestHistory$.pipe(
      switchMap(() => {
          if (this.entity) {
            return this.integrationService.getTestsHistory(this.entity.merchant.code, this.entity.path).pipe(
              map(data => data.map<IntegrationsTestHistory>(( { createdAt, ...item } ) => ({
                ...item,
                createdAt: moment(createdAt).format('YYYY-MM-DD HH:mm'),
                merchantName: this.entity.merchant.code
              }))),
            );
          }
          return of<IntegrationsTestHistory[]>([]);
        }
      ),
      takeUntil(this.destroyed$)
    ).subscribe(history => {
      this.historyResult = history;
    });

    this.hubEntityService.entitySelected$.pipe(
      tap(( { type } ) => {
        this.historyResult = [];
        this.entityType = type;
      }),
      switchMap(( { path, type } ) => {
          if ([Entity.TYPE_MERCHANT, Entity.TYPE_MERCHANT].includes(type)) {
            return this.entityService.getItemWithMerchantData(path)
              .pipe(
                map(data => {
                  if (data?.merchant) {
                    const entity = new Entity(data);
                    entity.path = path;
                    return entity;
                  }
                  return undefined;
                })
              );
          }
          return of<Entity>(undefined);
        }
      ),
      takeUntil(this.destroyed$)
    ).subscribe(( entity: Entity | undefined ) => {
      this.entity = entity;
      this.getTestHistory();
    });
  }

  getTestHistory() {
    this.loadTestHistory$.next();
  }
}

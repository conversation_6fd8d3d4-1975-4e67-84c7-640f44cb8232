import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { BoConfirmationModule } from '../../common/components/bo-confirmation/bo-confirmation.module';
import { IntegrationService } from '../../common/services/integration.service';
import { IntegrationTestBrandModule } from './integration-test-brand/integration-test-brand.module';
import { IntegrationTestHistoryModule } from './integration-test-history/integration-test-history.module';
import { IntegrationTestMerchantModule } from './integration-test-merchant/integration-test-merchant.module';

import { IntegrationsComponent } from './integrations.component';
import { IntegrationsRoutingModule } from './integrations.routing';


export const matModules = [
  MatDialogModule,
  MatButtonModule,
  MatExpansionModule,
  MatGridListModule,
  MatProgressSpinnerModule,
  IntegrationsRoutingModule,
  IntegrationTestBrandModule,
  IntegrationTestHistoryModule,
  IntegrationTestMerchantModule
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    ...matModules,
    SwuiPagePanelModule,
    LayoutModule,
    BoConfirmationModule,
  ],
  declarations: [
    IntegrationsComponent
  ],
  providers: [
    IntegrationService,
  ],
})

export class IntegrationsModule {
}

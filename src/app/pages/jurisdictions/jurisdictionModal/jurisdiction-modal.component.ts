import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { combineLatest, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ErrorMessage } from '../../../common/components/mat-user-editor/user-form.component';
import { Country } from '../../../common/typings';
import { Jurisdiction } from '../../../common/typings/jurisdiction';

@Component({
    selector: 'jurisdiction-modal',
    templateUrl: './jurisdiction-modal.component.html',
    styleUrls: ['./jurisdiction-modal.component.scss'],
    standalone: false
})
export class JurisdictionModalComponent implements OnInit {
  messageErrors: ErrorMessage = {
    required: 'VALIDATION.required',
  };

  isEdit: boolean = true;
  form: FormGroup;
  countries: SwuiSelectOption[] = [];
  countriesListType: SwuiSelectOption[] = [
    {
      id: 'allowed',
      text: 'Allowed Countries'
    },
    {
      id: 'restricted',
      text: 'Restricted Countries'
    }
  ];

  countriesList: string[] = [];

  countryTypeControl: FormControl = new FormControl(this.countriesListType[0].id);
  countriesListControl: FormControl = new FormControl([]);
  private readonly destroyed$ = new Subject<void>();

  constructor(private fb: FormBuilder,
              private cdr: ChangeDetectorRef,
              private dialogRef: MatDialogRef<JurisdictionModalComponent>,
              @Inject(MAT_DIALOG_DATA) public data: {
                jurisdiction: Jurisdiction,
                countries: Country[]
              }
  ) {
    this.isEdit = !!data?.jurisdiction?.code;
    this.initForm();
    this.initAvailableCountries();
  }

  ngOnInit(): void {
    this.countriesListControl.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe((countries: string[]) => {
      this.patchCountries(countries);
    });

    combineLatest([
      this.countryTypeControl.valueChanges,
      this.form.get('defaultCountry').valueChanges
    ]).pipe(
      takeUntil(this.destroyed$),
    ).subscribe(([type, defaultCountry]) => {
      this.countries = this.countries.map((county: SwuiSelectOption) => {
        county.disabled = county.id === defaultCountry;
        return county;
      });

      if (this.countryTypeControl.value !== 'restricted') {
        if (!this.countriesList.includes(defaultCountry)) {
          this.countriesList.push(defaultCountry);
        }
      }

      this.countriesList = this.countriesList.filter((country) => {
        return country !== defaultCountry || type !== 'restricted';
      });

      this.countriesListControl.setValue(this.countriesList);
    });

    if (this.data?.jurisdiction) {
      this.setFormData();
      this.setCountriesListType();
    } else {
      this.countryTypeControl.setValue(this.countriesListType[0].id);
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  onNoClick() {
    this.dialogRef.close(null);
  }

  onConfirmClick() {
    this.dialogRef.close(this.form.getRawValue());
  }

  private initAvailableCountries() {
    this.countries = this.data?.countries?.map((country: Country) => {
      return { text: country.displayName, id: country.code };
    });
    this.countries.sort(( a, b ) => a.text.localeCompare(b.text));
  }

  private patchCountries(countries: string[]) {
    this.countriesList = countries;

    if (this.countryTypeControl.value === 'allowed') {
      this.form.get('restrictedCountries').reset();
      this.form.get('allowedCountries').setValue(countries);
    } else if (this.countryTypeControl.value === 'restricted') {
      this.form.get('allowedCountries').reset();
      this.form.get('restrictedCountries').setValue(countries);
    }

    this.cdr.detectChanges();
  }

  private initForm() {
    this.form = this.fb.group({
      code: [{ value: '', disabled: true }],
      title: ['', Validators.required],
      description: ['', Validators.required],
      defaultCountry: ['', Validators.required],
      allowedCountries: [[]],
      restrictedCountries: [[]],
      settings: [{}]
    });
  }

  private setFormData() {
    if (this.data?.jurisdiction) {
      this.form?.patchValue(this.data?.jurisdiction);
    }
  }

  private setCountriesListType() {
    this.data.jurisdiction?.allowedCountries?.length ?
      this.countryTypeControl.setValue('allowed') :
      this.data.jurisdiction?.restrictedCountries?.length ?
        this.countryTypeControl.setValue('restricted') :
        this.countryTypeControl.setValue(null);

    if (this.countryTypeControl.value) {
      if (this.data.jurisdiction.allowedCountries?.length) {
        this.countriesListControl.patchValue(this.data.jurisdiction.allowedCountries);
        this.countriesList = this.data.jurisdiction.allowedCountries;
      } else if (this.data.jurisdiction.restrictedCountries?.length) {
        this.countriesListControl.patchValue(this.data.jurisdiction.restrictedCountries);
        this.countriesList = this.data.jurisdiction.restrictedCountries;
      }
    } else {
      this.countryTypeControl.setValue(this.countriesListType[0].id);
    }
  }
}

import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiMultiselectModule, SwuiPagePanelModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { TrimInputValueModule } from '../../../common/directives/trim-input-value/trim-input-value.module';
import { JurisdictionModalComponent } from './jurisdiction-modal.component';


@NgModule({
  declarations: [
    JurisdictionModalComponent
  ],
  exports: [
    JurisdictionModalComponent
  ],
    imports: [
        CommonModule,
        TranslateModule,
        LayoutModule,
        MatDialogModule,
        MatButtonModule,
        SwuiPagePanelModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        SwuiControlMessagesModule,
        MatSelectModule,
        SwuiSelectModule,
        SwuiMultiselectModule,
        TrimInputValueModule
    ]
})
export class JurisdictionModalModule {
}

import { Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import {
  PanelAction, RowAction, SwuiGridComponent, SwuiGridDataService, SwuiGridField, SwuiNotificationsService
} from '@skywind-group/lib-swui';
import { filter, switchMap, take, tap } from 'rxjs/operators';
import { BoConfirmationComponent } from '../../common/components/bo-confirmation/bo-confirmation.component';
import { CountryService } from '../../common/services/country.service';
import { JurisdictionService } from '../../common/services/jurisdiction.service';
import { Country } from '../../common/typings';
import { Jurisdiction } from '../../common/typings/jurisdiction';
import { JurisdictionModalComponent } from './jurisdictionModal/jurisdiction-modal.component';
import { SCHEMA_LIST, SCHEMA_FILTER } from './schema';

@Component({
    selector: 'jurisdictions',
    templateUrl: './jurisdictions.component.html',
    styleUrls: ['./jurisdictions.component.scss'],
    providers: [
        CountryService,
        JurisdictionService,
        { provide: SwuiGridDataService, useExisting: JurisdictionService }
    ],
    standalone: false
})
export class JurisdictionsComponent implements OnInit {
  @ViewChild(SwuiGridComponent) grid: SwuiGridComponent<Jurisdiction>;

  schema: SwuiGridField[] = SCHEMA_LIST;
  filterSchema: SwuiGridField[] = SCHEMA_FILTER;
  rowActions: RowAction[] = [];
  panelActions: PanelAction[] = [];
  private availableCountries: Country[] = [];

  constructor(private dialog: MatDialog,
              private readonly translate: TranslateService,
              private readonly jurisdiction: JurisdictionService,
              private readonly notifications: SwuiNotificationsService,
              private readonly countryService: CountryService<Country>
  ) {
    this.setRowActions();
    // this.setPanelActions(); TODO Phase2 2
    this.setAvailableCountry();
  }

  ngOnInit(): void {
  }

  onClick( { row }: { field: string, row: Jurisdiction } ) {
    this.showEditDialog(row);
  }

  showDeleteConfirmDialog(jurisdictionCode: string) {
    this.dialog.open(BoConfirmationComponent, {
      width: '600px',
      disableClose: true,
      data: {
        message: this.translate.instant('JURISDICTION.confirmationRemoved'),
      }
    }).afterClosed()
      .pipe(
        filter((data) => !!data),
        switchMap(() => this.jurisdiction.deleteEntityJurisdiction('', jurisdictionCode, 'true')),
        tap(() => this.notifications.success(this.translate.instant('JURISDICTION.jurisdictionRemoved'))),
        take(1)
      )
      .subscribe(() => this.grid.dataSource.loadData());
  }

  showEditDialog(jurisdiction?: Jurisdiction) {
    this.dialog.open(JurisdictionModalComponent, {
      width: '600px',
      disableClose: true,
      data: {
        jurisdiction: jurisdiction,
        countries: this.availableCountries
      }
    }).afterClosed().pipe(
      filter(result => result),
      switchMap((formValue: Jurisdiction) => jurisdiction?.id ?
        this.jurisdiction.patchJurisdiction(formValue) :
        this.jurisdiction.createJurisdiction(formValue)),
      tap(() => {
        const message = jurisdiction?.id ? 'JURISDICTION.notificationChanged' : 'JURISDICTION.notificationAdded';
        this.notifications.success(this.translate.instant(message));
      }),
      take(1),
    ).subscribe(() => this.grid.dataSource.loadData());
  }

  private setRowActions() {
    this.rowActions = [
      new RowAction({
        icon: 'create',
        inMenu: true,
        title: 'JURISDICTION.GRID.actionEdit',
        fn: (jurisdiction: Jurisdiction) => this.showEditDialog(jurisdiction)
      }),
/*      new RowAction({   TODO Phase2 2
        icon: 'delete',
        title: 'JURISDICTION.GRID.actionRemove',
        fn: ({ code }) => this.showDeleteConfirmDialog(code)
      }),*/
    ];
  }

 /* private setPanelActions() { TODO Phase2 2
    this.panelActions.push({
      title: 'Create jurisdiction',
      color: 'primary',
      actionFn: () => this.showEditDialog()
    });
  }*/

  private setAvailableCountry() {
    this.countryService.getList().pipe(
      take(1)
    ).subscribe((countries: Country[]) => this.availableCountries = countries);
  }
}

import { Component } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { SelectOptionModel } from '../../../common/models/select-option.model';
import { SUPPORTED_LABEL_TYPES } from '../schema';

export const RELATION_TYPES = [
  { id: 'o', text: 'LABEL_MANAGEMENT.TYPES.oneToOne' },
  { id: 'm', text: 'LABEL_MANAGEMENT.TYPES.oneToMany' }
];

@Component({
    selector: 'label-group-create-modal',
    templateUrl: './label-group-create-modal.component.html',
    standalone: false
})
export class LabelGroupCreateModal {
  form: FormGroup;
  messageErrors: any;
  groupType: SelectOptionModel[] = SUPPORTED_LABEL_TYPES.map(type => ({ id: type.id, text: type.displayName }));
  groups: SelectOptionModel[] = [];
  relationTypes = RELATION_TYPES;

  private readonly destroyed$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<LabelGroupCreateModal>
  ) {
    this.initForm();
  }

  get groupTypeControl(): FormControl {
    return this.form.get('type') as FormControl;
  }

  get relationTypeControl(): FormControl {
    return this.form.get('relationType') as FormControl;
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  onNoClick() {
    this.dialogRef.close();
  }

  onConfirmClick() {
    this.dialogRef.close(this.form.getRawValue());
  }

  private initForm() {
    this.form = this.fb.group({
      type: [null, Validators.required],
      group: [null, Validators.required],
      relationType: ['m', Validators.required]
    });
  }
}

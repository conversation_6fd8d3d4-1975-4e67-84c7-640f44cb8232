import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiNotificationsModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { LabelsService } from '../../../common/services/labels.service';
import { LabelGroupCreateModal } from './label-group-create-modal.component';


@NgModule({
  declarations: [LabelGroupCreateModal],
  exports: [LabelGroupCreateModal],
  imports: [
    CommonModule,
    MatDialogModule,
    ReactiveFormsModule,
    SwuiNotificationsModule,
    LayoutModule,
    TranslateModule,
    MatFormFieldModule,
    SwuiSelectModule,
    MatInputModule,
  ],
  providers: [LabelsService]
})
export class LabelGroupCreateModalModule {
}

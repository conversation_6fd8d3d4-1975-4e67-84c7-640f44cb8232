import { Component, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import {
  PanelAction, RowAction, SwuiGridComponent, SwuiGridDataService, SwuiGridField, SwuiNotificationsService, SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import { filter, switchMap, take, tap } from 'rxjs/operators';
import { Label } from 'src/app/common/typings/label';
import { BoConfirmationComponent } from '../../common/components/bo-confirmation/bo-confirmation.component';
import { GameGroupFilter } from '../../common/models/game-group.model';
import { LabelsService } from '../../common/services/labels.service';
import { LabelCreateModal } from './label-create-modal/label-create-modal.component';
import { LabelGroupCreateModal } from './label-group-create-modal/label-group-create-modal.component';
import { SCHEMA_FILTER, SCHEMA_LIST } from './schema';

@Component({
    selector: 'labels-management',
    templateUrl: './labels-management.component.html',
    styleUrls: ['./labels-management.component.scss'],
    providers: [
        LabelsService,
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: LabelsService }
    ],
    standalone: false
})
export class LabelsManagementComponent {
  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<GameGroupFilter>;

  rowActions: RowAction[] = [];
  panelActions: PanelAction[] = [];
  schema: SwuiGridField[] = SCHEMA_LIST;
  schemaFilter: SwuiGridField[] = SCHEMA_FILTER;

  constructor(private dialog: MatDialog,
              private readonly service: LabelsService,
              private readonly translate: TranslateService,
              private readonly notifications: SwuiNotificationsService,
  ) {
    this.setRowActions();
    this.setPanelActions();
  }

  private showDeleteConfirmDialog(label: Label) {
    this.dialog.open(BoConfirmationComponent, {
      width: '600px',
      disableClose: true,
      data: {
        message: this.translate.instant('LABEL_MANAGEMENT.confirmationRemoved', { name: label.title }),
      }
    }).afterClosed()
      .pipe(
        filter((data) => !!data),
        switchMap(() => this.service.deleteLabel(label.id)),
        tap(() => this.notifications.success(this.translate.instant('LABEL_MANAGEMENT.notificationRemoved'))),
        take(1)
      )
      .subscribe(() => this.grid.dataSource.loadData());
  }

  private setRowActions() {
    this.rowActions = [
      new RowAction({
        icon: 'delete',
        inMenu: true,
        title: 'LABEL_MANAGEMENT.GRID.delete',
        fn: (label: Label) => this.showDeleteConfirmDialog(label),
        canActivateFn: (label: Label) => label.group.type === 'entity'
      }),
    ];
  }

  private setPanelActions() {
    this.panelActions.push(
      {
        title: 'LABEL_MANAGEMENT.createLabel',
        color: 'primary',
        icon: 'person_add',
        actionFn: () => this.showCreateLabelDialog(),
      },
      {
        title: 'LABEL_MANAGEMENT.createLabelGroup',
        color: 'primary',
        icon: 'group_add',
        actionFn: () => this.showCreateLabelGroupDialog(),
      }
    );
  }

  private showCreateLabelGroupDialog() {
    this.dialog.open(LabelGroupCreateModal, {
      width: '700px',
      disableClose: true
    }).afterClosed()
      .pipe(
        filter( val => !!val),
        switchMap(labelGroup => this.service.addLabelGroup(labelGroup)),
        tap(() => this.notifications.success(this.translate.instant('LABEL_MANAGEMENT.notificationGroupCreated'))),
        take(1)
      ).subscribe(() => {
      this.grid.dataSource.loadData();
    });
  }

  private showCreateLabelDialog() {
    this.dialog.open(LabelCreateModal, {
      width: '700px',
      disableClose: true
    }).afterClosed()
      .pipe(
        filter( val => !!val),
        switchMap((label) => this.service.addLabel(label)),
        tap(() => this.notifications.success(this.translate.instant('LABEL_MANAGEMENT.notificationCreated'))),
        take(1)
      ).subscribe(() => {
      this.grid.dataSource.loadData();
    });
  }
}


import { HttpErrorResponse } from '@angular/common/http';
import { Component, Inject } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { of } from 'rxjs';
import { ErrorMessage } from '../../../../common/components/mat-user-editor/user-form.component';
import { LimitLevelsService } from '../../../../common/services/limit-levels.service';
import { LimitLevel } from '../../../../common/typings/limit-level';

@Component({
    selector: '',
    templateUrl: './add-level-modal.component.html',
    styleUrls: ['./add-level-modal.component.scss'],
    standalone: false
})
export class AddLevelModalComponent {

  title: FormControl = new FormControl('', Validators.required);
  path: string;
  eurLevels: any[];

  existingLevels: string[];

  public messageErrors: ErrorMessage = {
    required: 'VALIDATION.required',
  };

  constructor( @Inject(MAT_DIALOG_DATA) public data: { path: string, eurLevels: any[] },
               public dialogRef: MatDialogRef<AddLevelModalComponent>,
               private limitLevelsService: LimitLevelsService,
               private notifications: SwuiNotificationsService,
  ) {
    this.path = this.data.path;
    this.eurLevels = this.data.eurLevels.map(item => item.level);

    this.getExistingLevels();
  }

  getExistingLevels() {
    this.limitLevelsService.getLimitLevels(this.path).subscribe(
      levels => {
        if (Array.isArray(levels) && levels.length) {
          this.existingLevels = levels.filter(level => this.eurLevels.indexOf(level.title) === -1)
            .map(level => level.title);
        }
      }
    );
  }

  isLevelAlreadyAdded() {
    return this.eurLevels.includes(this.title.value?.trim());
  }

  save() {
    if (this.title.valid) {
      const value = this.title.value.trim();

      if (this.existingLevels.includes(value)) {
        this.dialogRef.close(value);
      } else {
        this.limitLevelsService.createNewLevel(this.path, { title: value }).subscribe(
          ( level: LimitLevel ) => {
            this.dialogRef.close(level.title);
          },
          ( error: HttpErrorResponse ) => {
            return of(this.notifications.error(error?.error?.message));
          },
        );
      }
    }
  }
}

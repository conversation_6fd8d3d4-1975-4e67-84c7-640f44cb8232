import { HttpErrorResponse } from '@angular/common/http';
import { Component, EventEmitter, Inject, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { of, Subject } from 'rxjs';
import { switchMap, take, takeUntil } from 'rxjs/operators';
import { ErrorMessage } from '../../../../common/components/mat-user-editor/user-form.component';
import { GameGroup } from '../../../../common/models/game-group.model';
import { SelectOptionModel } from '../../../../common/models/select-option.model';
import { GameGroupService } from '../../../../common/services/game-group.service';
import { LimitLevelsService } from '../../../../common/services/limit-levels.service';
import { Game } from '../../../../common/typings';

@Component({
    selector: 'clone-live-limits',
    templateUrl: 'clone-live-limits.component.html',
    styleUrls: ['./edit-live-limits.component.scss'],
    standalone: false
})
export class CloneLiveLimitsComponent implements OnInit, OnDestroy {

  @Output() limitsCloned = new EventEmitter<boolean>();

  fromFormValues: any;
  shortStructureOptions: SelectOptionModel[];
  initialGamesOptions: SelectOptionModel[];
  level: any;
  schemaDefinition: any;
  baseColumns: any[] = [];
  extendedLimits: any;
  entityPath: string;
  games: Game[];

  schemaDefinitionId: string;

  cloneForm: FormGroup;

  gameGroupsOptions: SelectOptionModel[] = [];
  gameGroups: GameGroup[];

  formValues: Object = {};
  betsFormValues: Object = {};

  fields: any[] = [];
  fieldsTypesMap: Map<string, string> = new Map();

  permissions;
  deniedFields;

  required: string[] = [];

  submitted = false;

  messageErrors: ErrorMessage = {
    required: 'VALIDATION.required',
  };

  private destroyed$ = new Subject<void>();

  constructor( @Inject(MAT_DIALOG_DATA) public data: {
                 fromFormValues: any,
                 shortStructureOptions: SelectOptionModel[],
                 initialGamesOptions: SelectOptionModel[],
                 level: any,
                 schemaDefinition: any,
                 baseColumns: any[],
                 extendedLimits: any,
                 entityPath: string,
                 games: Game[],
               },
               public dialogRef: MatDialogRef<CloneLiveLimitsComponent>,
               private fb: FormBuilder,
               private gameGroupsService: GameGroupService,
               private authService: SwHubAuthService,
               private translate: TranslateService,
               private limitLevelsService: LimitLevelsService,
               private notifications: SwuiNotificationsService,
  ) {
    this.fromFormValues = data.fromFormValues;
    this.shortStructureOptions = data.shortStructureOptions;
    this.initialGamesOptions = data.initialGamesOptions;
    this.level = data.level;
    this.schemaDefinition = data.schemaDefinition;
    this.baseColumns = data.baseColumns;
    this.extendedLimits = data.extendedLimits;
    this.entityPath = data.entityPath;
    this.games = data.games;
  }

  ngOnInit() {
    this.initForm();

    this.filterBetsByPermissions();

    this.parseSchemaDefinition();

    this.initPropertiesForm();
    this.initBetsForm();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  initForm() {
    this.cloneForm = this.fb.group({
      entityTo: ['', Validators.required],
      gameGroupTo: [''],
      gameTo: ['', Validators.required],
    });

    this.gameGroupToControl.disable();
    this.gameToControl.disable();

    this.entityToControl.valueChanges
      .pipe(
        switchMap(path => {
          if (path) {
            this.gameGroupToControl.enable();
            this.gameToControl.enable();

            return this.gameGroupsService.getGameGroupsList(path);
          } else {
            this.gameGroupToControl.setValue(null, { emitEvent: false });
            this.gameToControl.setValue(null, { emitEvent: false });

            this.gameGroupToControl.disable();
            this.gameToControl.disable();

            return of(null);
          }
        }),
        takeUntil(this.destroyed$),
      )
      .subscribe(
        gameGroups => this.processSelectedEntity(gameGroups)
      );

    this.gameToControl.valueChanges
      .pipe(
        takeUntil(this.destroyed$),
      )
      .subscribe(
        gameCode => {
          if (gameCode) {
            this.schemaDefinitionId = this.games.find(game => game.code === gameCode).schemaDefinitionId;
          }
        }
      );
  }

  filterBetsByPermissions() {
    let permissionKey = Object.keys(this.schemaDefinition.permissions)
      .find(key => key === this.level.currency) || 'default';

    this.permissions = this.schemaDefinition.permissions[permissionKey];

    this.deniedFields = [];

    if (!this.authService.isSuperAdmin) {
      this.baseColumns.forEach(item => {
        if ((this.permissions[item.title] && this.permissions[item.title] === 'admin') ||
          (this.permissions['bets.' + item.title] && this.permissions['bets.' + item.title] === 'admin')) {
          this.deniedFields.push(item.title);
        }
      });
    }
  }

  parseSchemaDefinition() {
    if (this.schemaDefinition && this.schemaDefinition.schema && this.schemaDefinition.schema.properties) {
      Object.entries(this.schemaDefinition.schema.properties).forEach(
        ( [key, value] ) => {
          let type = value['type'];
          this.fieldsTypesMap.set(key, type);
        }
      );
    }

    if (this.schemaDefinition && this.schemaDefinition.schema && this.schemaDefinition.schema.required) {
      this.required = this.schemaDefinition.schema.required;
    }
  }

  initPropertiesForm() {
    Object.entries(this.level.properties).forEach(( [key, value] ) => {
      let obj = value;
      obj['title'] = key;
      obj['type'] = this.fieldsTypesMap.get(key);

      if (!obj['calculated'] && obj['title'] !== 'isDefaultRoom') {
        this.fields.push(obj);
      }
    });

    if (!this.authService.isSuperAdmin) {
      this.fields.forEach(field => {
        if (this.permissions[field.title] && this.permissions[field.title] === 'admin') {
          field.type = 'forbidden';
        }
      });
    }

    if (this.fields.length) {
      this.fields.forEach(item => {
        if (item.type !== 'forbidden') {

          item.title === 'stakeAll' ? this.formValues[item.title] = item.value.join(' ; ') :
            this.formValues[item.title] = item.value;
        }
      });
    }
  }

  initBetsForm() {
    if (this.level && this.level.bets && Array.isArray(this.level.bets) && this.baseColumns.length) {
      this.level.bets.forEach(bet => {
        this.betsFormValues[bet.type] = {};

        if (Array.isArray(bet.properties)) {

          bet.properties.forEach(property => {
            let fieldType = this.baseColumns.find(column => column.title === property.title).limitConfigurationType;
            let isConfigurable = ['configurable', 'constForAllCurrencies'].indexOf(fieldType) !== -1;

            if (!this.authService.isSuperAdmin) {
              if (this.permissions.bets === 'admin' || this.deniedFields.indexOf(property.title) !== -1 ||
                !isConfigurable) {
                property['type'] = 'forbidden';
              }
            } else {
              if (!isConfigurable) {
                property['type'] = 'forbidden';
              }
            }

            this.betsFormValues[bet.type][property.title] = property.value;
          });
        }
      });
    }
  }

  processSelectedEntity( gameGroups: GameGroup[] ) {
    this.gameGroupsOptions = [];

    if (!gameGroups || !gameGroups.length) {
      this.gameGroupToControl.setValue(null, { emitEvent: false });
      return;
    }

    if (gameGroups.length) {
      this.gameGroups = gameGroups;
      gameGroups.map(( gameGroup: GameGroup ) => {
        this.gameGroupsOptions.push(new GameGroup(gameGroup).toSelectOption());
      });
    }
  }

  get entityToControl(): FormControl {
    return this.cloneForm.get('entityTo') as FormControl;
  }

  get gameGroupToControl(): FormControl {
    return this.cloneForm.get('gameGroupTo') as FormControl;
  }

  get gameToControl(): FormControl {
    return this.cloneForm.get('gameTo') as FormControl;
  }

  clone() {
    this.submitted = true;

    if (this.cloneForm.valid) {
      let formValue: Object;

      formValue = JSON.parse(JSON.stringify(this.formValues));

      if (formValue['stakeAll'] && !Array.isArray(formValue['stakeAll'].value)) {
        formValue['stakeAll'] = formValue['stakeAll'].split(';').map(Number);
      }

      formValue['bets'] = JSON.parse(JSON.stringify(this.betsFormValues));

      if (!this.authService.isSuperAdmin && this.permissions.bets === 'admin') {
        delete formValue['bets'];
      }

      const message = this.translate.instant('GAME_LIMITS.LIVE.duplicateLimitsMessage',
        { currency: this.level.currency, level: this.level.level }
      );

      let data = {
        limitsFromModal: formValue,
        entity: this.entityToControl.value,
        game: this.gameToControl.value,
        gameGroup: this.gameGroupToControl.value,
        schemaDefinitionId: this.schemaDefinitionId,
        message: message,
        isClone: true,
      };

      this.limitLevelsService.createNewLevel(this.entityToControl.value, { title: `copy of ${this.level.level}` })
        .pipe(
          take(1),
        )
        .subscribe(
          () => {
            this.dialogRef.close(data);
          },
          ( error: HttpErrorResponse ) => {
            if (error.error?.code === 40) {
              this.dialogRef.close(data);
            } else {
              this.notifications.error(error?.error?.message);
            }
          },
        );
    }
  }
}

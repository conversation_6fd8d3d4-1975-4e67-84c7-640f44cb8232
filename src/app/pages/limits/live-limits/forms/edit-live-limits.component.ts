import { Component, Inject, OnInit } from '@angular/core';
import { <PERSON><PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { ErrorMessage } from '../../../../common/components/mat-user-editor/user-form.component';
import { ValidationService } from '../../../../common/services/validation.service';

@Component({
    selector: 'edit-live-limits',
    templateUrl: './edit-live-limits.component.html',
    styleUrls: ['./edit-live-limits.component.scss', '../live-limits.component.scss'],
    standalone: false
})
export class EditLiveLimitsComponent implements OnInit {
  level: any;
  schemaDefinition: any;
  baseColumns: any[] = [];
  isNewLevel: boolean;

  form: FormGroup = new FormGroup({});
  formValues: Object = {};

  betsForm: FormGroup = new FormGroup({});
  betsFormValues: Object = {};

  fields: any[] = [];
  fieldsTypesMap: Map<string, string> = new Map();

  permissions;
  deniedFields;

  required: string[] = [];

  submitted = false;

  messageErrors: ErrorMessage = {
    required: 'VALIDATION.required',
    invalidStakeAll: `VALIDATION.invalidLiveStakeAll`,
    valuesCanNotBeDuplicated: 'VALIDATION.valuesCanNotBeDuplicated',
    minLowerThanMax: 'VALIDATION.minLowerThanMax',
    greaterThan: 'VALIDATION.greaterThan',
    lowerThan: 'VALIDATION.lowerThan',
    greaterOrEqual: 'VALIDATION.greaterOrEqual',
    invalidDigitsOnly: 'VALIDATION.noDecimals',
  };

  constructor( @Inject(MAT_DIALOG_DATA) public data: { level: any, schemaDefinition: any, baseColumns: any[], isNewLevel: boolean },
               public dialogRef: MatDialogRef<EditLiveLimitsComponent>,
               private fb: FormBuilder,
               private authService: SwHubAuthService,
  ) {
    this.level = data.level;
    this.schemaDefinition = data.schemaDefinition;
    this.baseColumns = data.baseColumns;
    this.isNewLevel = data.isNewLevel;
  }

  ngOnInit() {
    this.filterBetsByPermissions();

    this.parseSchemaDefinition();

    this.initPropertiesForm();
    this.initBetsForm();
  }

  filterBetsByPermissions() {
    let permissionKey = Object.keys(this.schemaDefinition.permissions)
      .find(key => key === this.level.currency) || 'default';

    this.permissions = this.schemaDefinition.permissions[permissionKey];

    this.deniedFields = [];

    if (!this.authService.isSuperAdmin) {
      this.baseColumns.forEach(item => {
        if ((this.permissions[item.title] && this.permissions[item.title] === 'admin') ||
          (this.permissions['bets.' + item.title] && this.permissions['bets.' + item.title] === 'admin')) {
          this.deniedFields.push(item.title);
        }
      });
    }
  }

  initPropertiesForm() {
    Object.entries(this.level.properties).forEach(( [key, value] ) => {
      let obj = value;
      obj['title'] = key;

      if (obj['title'] !== 'isDefaultRoom') {
        obj['type'] = !obj['calculated'] ? this.fieldsTypesMap.get(key) : 'forbidden';
        this.fields.push(obj);
      }
    });

    if (!this.authService.isSuperAdmin) {
      this.fields.forEach(field => {
        if (this.permissions[field.title] && this.permissions[field.title] === 'admin') {
          field.type = 'forbidden';
        }
      });
    }

    if (this.fields.length) {
      this.fields.forEach(item => {
        if (item.type !== 'forbidden') {
          this.required.includes(item.title) ?
            this.form.addControl(item.title, this.fb.control('', Validators.required)) :
            this.form.addControl(item.title, this.fb.control(''));

          let existingValidators = this.form.get(item.title).validator;

          if (item.title === 'stakeAll') {
            this.form.get(item.title).setValidators(
              Validators.compose([
                existingValidators,
                ValidationService.stakeAllValidator(';'),
                ValidationService.arrayDuplicatesValidator(';'),
              ]));
          }

          if (item.title === 'concurrentPlayers') {
            this.form.get(item.title).setValidators(
              Validators.compose([
                existingValidators,
                ValidationService.greaterThanValidator(0),
              ]));
          }

          item.title === 'stakeAll' ? this.formValues[item.title] = item.value.join(' ; ') :
            this.formValues[item.title] = item.value;
        }
      });
    }

    this.form.patchValue(this.formValues);
  }

  initBetsForm() {
    if (this.level && this.level.bets && Array.isArray(this.level.bets) && this.baseColumns.length) {
      this.level.bets.forEach(bet => {
        this.betsForm.addControl(bet.type, this.fb.group({}));
        this.betsFormValues[bet.type] = {};

        if (Array.isArray(bet.properties)) {
          let betGroup = this.betsForm.get(bet.type) as FormGroup;
          let groupValidators = [];

          bet.properties.forEach(property => {
            let fieldType = this.baseColumns.find(column => column.title === property.title).limitConfigurationType;
            let isConfigurable = ['configurable', 'constForAllCurrencies'].indexOf(fieldType) !== -1;

            if (!this.authService.isSuperAdmin) {
              if (this.permissions.bets === 'admin' || this.deniedFields.indexOf(property.title) !== -1 ||
                !isConfigurable) {
                property['type'] = 'forbidden';
              }
            } else {
              if (!isConfigurable) {
                property['type'] = 'forbidden';
              }
            }

            this.required.includes(property.title) ?
              betGroup.addControl(property.title, this.fb.control('', Validators.required)) :
              betGroup.addControl(property.title, this.fb.control(''));

            this.setBetsValidators(bet.type, property.title);

            this.betsFormValues[bet.type][property.title] = property.value;
          });

          if (betGroup.get('min') && betGroup.get('max')) {
            groupValidators.push(ValidationService.minMaxValidator('min', 'max'));
          }

          if (betGroup.get('alert') && betGroup.get('block')) {
            groupValidators.push(ValidationService.greaterOrEqualValidator('alert', 'block'));
          }

          if (groupValidators.length) {
            betGroup.setValidators(groupValidators);
          }
        }
      });

      this.betsForm.patchValue(this.betsFormValues);
    }
  }

  setBetsValidators( betType, propertyTitle ) {
    let betGroupControl = this.betsForm.get(betType) as FormGroup;
    let existingValidators = betGroupControl.get(propertyTitle).validator;

    if (betGroupControl.get('alert')) {
      betGroupControl.get('alert').setValidators(Validators.compose([
        existingValidators,
        ValidationService.digitsOnlyValidator,
        ValidationService.greaterThanValidator(0),
        ValidationService.lowerThanValidator(100),
      ]));
    }

    if (betGroupControl.get('block')) {
      betGroupControl.get('block').setValidators(Validators.compose([
        existingValidators,
        ValidationService.digitsOnlyValidator,
        ValidationService.greaterThanValidator(0),
        ValidationService.lowerThanValidator(100),
      ]));
    }
  }

  parseSchemaDefinition() {
    if (this.schemaDefinition && this.schemaDefinition.schema && this.schemaDefinition.schema.properties) {
      Object.entries(this.schemaDefinition.schema.properties).forEach(
        ( [key, value] ) => {
          let type = value['type'];
          this.fieldsTypesMap.set(key, type);
        }
      );
    }

    if (this.schemaDefinition && this.schemaDefinition.schema && this.schemaDefinition.schema.required) {
      this.required = this.schemaDefinition.schema.required;
    }

    if (this.schemaDefinition && this.schemaDefinition.definitions && this.schemaDefinition.definitions.bet) {
      this.required = [...this.required, ...this.schemaDefinition.definitions.bet.required];
    }
  }

  getBetProperty( row, column ) {
    return row.find(item => item.title === column);
  }

  getNgClass( row, column ) {
    let bet = row.find(item => item.title === column);

    return bet.custom ? 'custom' : (bet.inherited ? 'inherited' : (bet.calculatedFromBase ? 'calculatedFromBase' : ''));
  }

  autoResizeTextArea( id ) {
    let textArea = document.getElementById(id);
    textArea.style.overflow = 'hidden';
    textArea.style.height = '0px';
    textArea.style.height = textArea.scrollHeight + 'px';
  }

  save() {
    this.submitted = true;

    this.form.markAllAsTouched();
    this.betsForm.markAllAsTouched();

    if (this.form.valid && this.betsForm.valid) {
      let formValue: Object;
      formValue = JSON.parse(JSON.stringify(this.form.value));

      if (this.form.get('stakeAll') && this.form.get('stakeAll').value &&
        !Array.isArray(this.form.get('stakeAll').value)) {
        formValue['stakeAll'] = this.form.get('stakeAll').value.split(';').map(Number);
      }

      formValue['bets'] = JSON.parse(JSON.stringify(this.betsForm.value));

      if (!this.authService.isSuperAdmin && this.permissions.bets === 'admin') {
        delete formValue['bets'];
      }

      if (formValue['bets']) {
        Object.entries(formValue['bets']).forEach(( [, values] ) => {
          Object.entries(values).forEach(( [key, _] ) => {
            if (!values[key]) {
              delete values[key];
            }
          });
        });
      }

      this.dialogRef.close(formValue);
    }
  }

  close() {
    this.isNewLevel ?
      this.dialogRef.close({ cancelAddLevel: this.level.level }) :
      this.dialogRef.close();
  }
}

import { HttpErrorResponse } from '@angular/common/http';
import { Component, Inject } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { of } from 'rxjs';
import { ErrorMessage } from '../../../../common/components/mat-user-editor/user-form.component';
import { LimitLevelsService } from '../../../../common/services/limit-levels.service';
import { LimitLevel } from '../../../../common/typings/limit-level';

@Component({
    selector: '',
    templateUrl: './rename-level-modal.component.html',
    styleUrls: ['./add-level-modal.component.scss'],
    standalone: false
})
export class RenameLevelModalComponent {

  title: FormControl = new FormControl('', Validators.required);
  levelId: string;
  level: any;
  path: string;

  existingLevels: string[];

  public messageErrors: ErrorMessage = {
    required: 'VALIDATION.required',
  };

  constructor( @Inject(MAT_DIALOG_DATA) public data: { level: any, path: string },
               public dialogRef: MatDialogRef<RenameLevelModalComponent>,
               private limitLevelsService: LimitLevelsService,
               private notifications: SwuiNotificationsService,
  ) {
    this.level = this.data.level;
    this.path = this.data.path;

    this.title.patchValue(this.level.level);

    this.getExistingLevels();
  }

  getExistingLevels() {
    this.limitLevelsService.getLimitLevels(this.path).subscribe(
      levels => {
        if (Array.isArray(levels) && levels.length) {
          this.existingLevels = levels.map(( level: LimitLevel ) => level.title);
          this.levelId = levels.find(( level: LimitLevel ) => level.title === this.level.level)?.id;
        }
      }
    );
  }

  isLevelAlreadyAdded() {
    return this.existingLevels?.includes(this.title.value?.trim());
  }

  save() {
    if (this.title.valid) {
      const value = this.title.value.trim();

      this.limitLevelsService.patchLimitLevel(this.path, this.levelId, { title: value }).subscribe(
        ( level: LimitLevel ) => {
          this.dialogRef.close(level.title);
        },
        ( error: HttpErrorResponse ) => {
          return of(this.notifications.error(error?.error?.message));
        },
      );
    }
  }
}

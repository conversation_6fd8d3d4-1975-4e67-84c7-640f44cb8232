import { HttpErrorResponse } from '@angular/common/http';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService, SwuiSelectOption } from '@skywind-group/lib-swui';
import { forkJoin, NEVER, of, Subject, throwError } from 'rxjs';
import { catchError, filter, finalize, map, switchMap, take, takeUntil, tap } from 'rxjs/operators';
import { BoConfirmationComponent } from '../../../common/components/bo-confirmation/bo-confirmation.component';
import { GameGroup } from '../../../common/models/game-group.model';
import { EntityService } from '../../../common/services/entity.service';
import { GameGroupService } from '../../../common/services/game-group.service';
import { GameService } from '../../../common/services/game.service';
import { LimitLevelsService } from '../../../common/services/limit-levels.service';
import { NewLimitsService } from '../../../common/services/new-limits.service';
import { SchemaDefinitionsService } from '../../../common/services/schema-definitions.service';
import { Entity, Game } from '../../../common/typings';
import { GameLimitLevel, LimitLevel } from '../../../common/typings/limit-level';
import { AddLevelModalComponent } from './forms/add-level-modal.component';
import { CloneLiveLimitsComponent } from './forms/clone-live-limits.component';
import { EditLiveLimitsComponent } from './forms/edit-live-limits.component';
import { RenameLevelModalComponent } from './forms/rename-level-modal.component';

const entitiesStructureToSelectOptions = ( item: any, count: number = 0, resultArray: SwuiSelectOption[] = [] ) => {
  const { type, key } = item;

  resultArray.push({
    id: item.path,
    text: item.path !== ':' ? `${'-'.repeat(count)} ${item.name} - ${item.title}` : item.name,
    disabled: false,
    data: { type, key },
  });

  if (item.child) item.child.reverse().forEach(i => entitiesStructureToSelectOptions(i, count + 1, resultArray));

  return resultArray;
};

@Component({
    selector: 'live-limits',
    templateUrl: './live-limits.component.html',
    styleUrls: ['./live-limits.component.scss'],
    standalone: false
})
export class LiveLimitsComponent implements OnInit, OnDestroy {

  form: FormGroup;
  levelsSortOrder: FormControl = new FormControl('');

  shortStructureOptions: SwuiSelectOption[];

  gameGroupsOptions: SwuiSelectOption[];

  initialGamesOptions: SwuiSelectOption[];
  gamesOptions: SwuiSelectOption[];
  games: Game[];
  gameGroups: GameGroup[];

  levelsSortOption: SwuiSelectOption[] = [
    { id: 'desc', text: 'GAME_LIMITS.LIVE.descSortOption' },
    { id: 'asc', text: 'GAME_LIMITS.LIVE.ascSortOption' },
  ];

  gameTypesOptions: SwuiSelectOption[] = [
    { id: 'baccarat', text: 'Live Baccarat' },
    { id: 'roulette', text: 'Live Roulette' },
  ];

  columnsOrderMap = new Map([
    ['payout', 0],
    ['min', 1],
    ['max', 2],
    ['positionMax', 3],
    ['exposure', 4],
    ['alert', 5],
    ['block', 6],
  ]);

  entityPath;

  extendedLimits: any;
  baseLimits: any;

  eurLevels: any[] = [];
  operatorsLevels: any[] = [];

  baseColumns: any[] = [];

  schemaDefinition: any;
  isSchemaDefinitionCorrect = true;

  errorOccurred = false;

  loading = false;

  private destroyed$ = new Subject<void>();

  constructor( public dialog: MatDialog,
               private entityService: EntityService<Entity>,
               private gameGroupsService: GameGroupService,
               private activatedRoute: ActivatedRoute,
               private fb: FormBuilder,
               private newLimitsService: NewLimitsService<any>,
               private schemaDefinitionsService: SchemaDefinitionsService<any>,
               private notifications: SwuiNotificationsService,
               private translate: TranslateService,
               private gameService: GameService,
               private limitLevelsService: LimitLevelsService,
  ) {
    if (this.activatedRoute.snapshot.data.games) {
      this.games = this.activatedRoute.snapshot.data.games
        .filter(game => {
          return game.schemaDefinitionId && game.type === 'live';
        });
    }

    this.buildShortStructureOptions();
    this.initGamesOptions();
  }

  ngOnInit() {
    this.initForm();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  initForm() {
    this.form = this.fb.group({
      entity: [''],
      gameGroup: [''],
      gameType: [''],
      game: [''],
    });

    this.disableFilters();

    this.entityControl.valueChanges
      .pipe(
        switchMap(path => {
          this.gameGroupControl.setValue(null, { emitEvent: false });
          this.gameTypeControl.setValue(null, { emitEvent: false });
          this.gameControl.setValue(null, { emitEvent: false });

          if (path) {
            this.enableFilters();
            this.entityPath = path;

            return forkJoin([this.gameGroupsService.getGameGroupsList(path), this.gameService.getAllGames(path)]);
          } else {
            this.disableFilters();
            this.entityPath = null;

            return NEVER;
          }
        }),
        takeUntil(this.destroyed$),
      )
      .subscribe(( [gameGroups, games] ) => {
        this.games = games.filter(game => game.schemaDefinitionId && game.type === 'live');
        this.initGamesOptions();

        this.processSelectedEntity(gameGroups);
      });

    this.gameTypeControl.valueChanges
      .pipe(
        takeUntil(this.destroyed$),
      )
      .subscribe(
        gameType => {
          if (gameType) {
            this.gameControl.setValue(null, { emitEvent: false });

            this.gamesOptions = [];
            this.games.filter(game => game.features && game.features.live && game.features.live['type'] === gameType)
              .forEach(game => this.gamesOptions.push({ id: game.code, text: `${game.title} (${game.code})` }));
          } else {
            this.gamesOptions = this.initialGamesOptions;
          }
        }
      );

    this.gameControl.valueChanges
      .pipe(
        takeUntil(this.destroyed$),
      )
      .subscribe(
        gameCode => {
          if (gameCode) {
            let schemaDefinitionId = this.games.find(game => game.code === gameCode).schemaDefinitionId;
            this.getSchemaDefinition(schemaDefinitionId);
          }
        }
      );

    this.form.valueChanges
      .pipe(
        takeUntil(this.destroyed$),
      )
      .subscribe(params => {
        this.errorOccurred = false;
        this.eurLevels = [];
        this.operatorsLevels = [];

        if (params.entity && params.game) {
          this.initTable();
        }
      });

    this.levelsSortOrder.patchValue('asc');

    this.levelsSortOrder.valueChanges.subscribe(
      value => {
        this.sortLevels(value);
      }
    );
  }

  sortLevels( value ) {
    if (value === 'asc') {
      this.eurLevels.sort(( a, b ) => this.compareBets(a, b));
      this.operatorsLevels.sort(( a, b ) => this.compareBets(a, b));
    } else {
      this.eurLevels.sort(( a, b ) => this.compareBets(b, a));
      this.operatorsLevels.sort(( a, b ) => this.compareBets(b, a));
    }
  }

  getMinBet( level ) {
    return Math.min(...level?.bets?.map(bet => bet?.properties?.find(property => property.title === 'min')?.value));
  }

  getMaxBet( level ) {
    return Math.min(...level?.bets?.map(bet => bet?.properties?.find(property => property.title === 'max')?.value));
  }

  compareBets( a, b ): number {
    if (this.getMinBet(a) < this.getMinBet(b)) {
      return -1;
    }

    if (this.getMinBet(a) > this.getMinBet(b)) {
      return 1;
    }

    if (this.getMinBet(a) === this.getMinBet(b)) {
      if (this.getMaxBet(a) < this.getMaxBet(b)) {
        return -1;
      }

      if (this.getMaxBet(a) > this.getMaxBet(b)) {
        return 1;
      }

      if (this.getMaxBet(a) === this.getMaxBet(b)) {
        return a.level.localeCompare(b.level);
      }
    }
  }

  get entityControl(): FormControl {
    return this.form.get('entity') as FormControl;
  }

  get gameGroupControl(): FormControl {
    return this.form.get('gameGroup') as FormControl;
  }

  get gameTypeControl(): FormControl {
    return this.form.get('gameType') as FormControl;
  }

  get gameControl(): FormControl {
    return this.form.get('game') as FormControl;
  }

  disableFilters() {
    this.gameGroupControl.disable();
    this.gameTypeControl.disable();
    this.gameControl.disable();
  }

  enableFilters() {
    this.gameGroupControl.enable();
    this.gameTypeControl.enable();
    this.gameControl.enable();
  }

  processSelectedEntity( gameGroups: GameGroup[] ) {
    this.gameGroupsOptions = [];

    if (!gameGroups || !gameGroups.length) {
      this.gameGroupControl.setValue(null, { emitEvent: false });
      return;
    }

    if (gameGroups.length) {
      this.gameGroups = gameGroups;
      gameGroups?.filter(( gameGroup: GameGroup ) => gameGroup.isOwner)?.map(( gameGroup: GameGroup ) => {
        this.gameGroupsOptions.push(new GameGroup(gameGroup).toSelectOption());
      });
    }
  }

  buildShortStructureOptions() {
    this.entityService.getShortStructure()
      .pipe(
        map(( str ) => entitiesStructureToSelectOptions(str, 0, [])),
        take(1),
      )
      .subscribe(( data: any[] ) => {
        this.shortStructureOptions = data;
        const rootEntityOption = this.shortStructureOptions.find(opt => opt.id === '');
        if (rootEntityOption) {
          rootEntityOption.disabled = true;
        }
      });
  }

  initGamesOptions() {
    this.initialGamesOptions = [];

    this.games.forEach(game => this.initialGamesOptions.push(
      { id: game.code, text: `${game.title} (${game.code})` })
    );

    this.gamesOptions = this.initialGamesOptions;
  }

  getSchemaDefinition( schemaDefinitionId ) {
    this.isSchemaDefinitionCorrect = true;

    this.schemaDefinitionsService.getSchemaDefinition(schemaDefinitionId)
      .pipe(
        take(1),
      )
      .subscribe(
        schemaDefinition => {
          this.baseColumns = new Array(this.columnsOrderMap.size);
          this.schemaDefinition = schemaDefinition;

          if (schemaDefinition && schemaDefinition.definitions && schemaDefinition.definitions.bet &&
            schemaDefinition.definitions.bet.properties) {
            Object.entries(schemaDefinition.definitions.bet.properties).forEach(
              ( [key, value] ) => {
                let column = { title: key, limitConfigurationType: value['limitConfigurationType'] };
                let order = this.columnsOrderMap.get(key);

                this.baseColumns[order] = column;
              }
            );

            this.baseColumns = this.baseColumns.filter(column => column);
          } else {
            this.isSchemaDefinitionCorrect = false;
          }
        }
      );
  }

  buildLevelsTableData( gameLimitLevels: GameLimitLevel[] ) {
    this.eurLevels = [];
    this.operatorsLevels = [];

    Object.entries(this.extendedLimits).forEach(( [currency, limits] ) => {
      this.baseLimits = JSON.parse(JSON.stringify(limits));

      Object.entries(this.baseLimits).forEach(( [key, value] ) => {
        let gameLimitLevel = currency === 'EUR' ?
          gameLimitLevels?.find(item => item.level.title === key && !item.currency) :
          gameLimitLevels?.find(item => item.level.title === key && item.currency === currency);

        let obj = {
          currency: currency,
          level: key,
          properties: JSON.parse(JSON.stringify(value)),
          bets: [],
          hidden: gameLimitLevel ? gameLimitLevel.hidden : false,
          isDefault: gameLimitLevel ? gameLimitLevel.isDefault : false,
          betsExpanded: false,
          gameLimitLevel: gameLimitLevel,
        };

        delete obj['properties']['bets'];

        if (value['bets']) {
          Object.entries(value['bets']).forEach(( [k, v] ) => {
            let bet = {
              type: k,
              properties: this.objectEntriesToArray(v),
            };

            obj.bets.push(bet);
          });
        }

        obj.currency === 'EUR' ? this.eurLevels.push(obj) : this.operatorsLevels.push(obj);
      });
    });
  }

  objectEntriesToArray( object: Object ): any[] {
    let arr: any[] = [];

    Object.entries(object).forEach(( [key, value] ) => {
      let obj = value;
      obj['title'] = key;

      arr.push(obj);
    });

    return arr;
  }

  expandBaseCurrencyTable( item ) {
    item.betsExpanded = !item.betsExpanded;
  }

  expandAll( levels: any[] ) {
    if (levels.some(item => item.betsExpanded === false)) {
      levels.forEach(item => item.betsExpanded = true);
    } else {
      levels.forEach(item => item.betsExpanded = false);
    }
  }

  getCellValue( row, column ) {
    let item = row.find(i => i.title === column);
    return item ? item.value : '';
  }

  getNgClass( row, column ) {
    let bet = row.find(item => item.title === column);

    return bet && bet.custom ?
      'custom' :
      (bet && bet.inherited ? 'inherited' : (bet && bet.calculatedFromBase ? 'calculatedFromBase' : ''));
  }

  initTable() {
    if (this.isSchemaDefinitionCorrect) {
      this.newLimitsService.getExtendedGameLimits(this.entityControl.value, this.gameControl.value,
        this.gameGroupControl.value)
        .pipe(
          take(1),
          tap(() => this.loading = true),
          switchMap(extendedLimits => this.limitLevelsService.getGameLimitLevels(this.entityPath)
            .pipe(
              map(( gameLimitLevels: GameLimitLevel[] ) => ({ extendedLimits, gameLimitLevels })),
            )),
          catchError(err => {
            this.errorOccurred = true;
            this.loading = false;
            this.eurLevels = [];
            this.operatorsLevels = [];
            this.notifications.error(err.error.message);

            return throwError(err);
          }),
        )
        .subscribe(( { extendedLimits, gameLimitLevels } ) => {
          this.loading = false;
          this.extendedLimits = JSON.parse(JSON.stringify(extendedLimits));
          this.buildLevelsTableData(gameLimitLevels);

          this.sortLevels(this.levelsSortOrder.value ? this.levelsSortOrder.value : 'asc');
        });
    }
  }

  handleOpenEditLimitModal( level, isNewLevel? ) {
    this.dialog.open(EditLiveLimitsComponent, {
      width: '1000px',
      disableClose: true,
      data: {
        level: level,
        schemaDefinition: this.schemaDefinition,
        baseColumns: this.baseColumns,
        isNewLevel: isNewLevel,
      },
    }).afterClosed()
      .pipe(
        take(1),
        filter(value => !!value),
      )
      .subscribe(value => {
        if (value.cancelAddLevel) {
          this.eurLevels = this.eurLevels?.filter(item => item.level !== value.cancelAddLevel);
        } else {
          let data = {
            limitsFromModal: value,
            level: level,
            entity: this.entityControl.value,
            game: this.gameControl.value,
            gameGroup: this.gameGroupControl.value,
            schemaDefinitionId: this.schemaDefinition.id,
          };

          this.saveLimits(data);
        }
      });
  }

  handleOpenRenameLevelModal( level ) {
    if (this.eurLevels && this.eurLevels.length) {
      this.dialog.open(RenameLevelModalComponent, {
        width: '600px',
        disableClose: true,
        data: {
          level: level,
          path: this.entityPath,
        }
      }).afterClosed()
        .pipe(
          filter(title => !!title),
          catchError(err => {
            this.initTable();
            this.notifications.error(err.error.message);

            return throwError(err);
          }),
          take(1),
        )
        .subscribe(() => {
          this.initTable();

          this.notifications.success(this.translate.instant('GAME_LIMITS.LIVE.levelRenamedMessage'));
        });
    }
  }

  handleOpenCloneLimitModal( level ) {
    this.dialog.open(CloneLiveLimitsComponent, {
      width: '1000px',
      disableClose: true,
      data: {
        fromFormValues: this.form.value,
        shortStructureOptions: this.shortStructureOptions,
        initialGamesOptions: this.initialGamesOptions,
        level: level,
        schemaDefinition: this.schemaDefinition,
        baseColumns: this.baseColumns,
        extendedLimits: this.extendedLimits[level.currency][level.level],
        entityPath: this.entityPath,
        games: this.games,
      },
    }).afterClosed()
      .pipe(
        take(1),
      )
      .subscribe(
        ( data ) => {
          if (data) {
            data.level = JSON.parse(JSON.stringify(level));
            data.level.level = `copy of ${level.level}`;
            this.saveLimits(data);
          }
        }
      );
  }

  saveLimits( modalData ) {
    if (modalData.limitsFromModal) {
      let gameLimits: Object = {};

      this.newLimitsService.getLimits(modalData.entity,
        modalData.game, modalData.gameGroup, modalData.schemaDefinitionId)
        .pipe(
          take(1),
          switchMap(data => this.limitLevelsService.getLimitLevels(modalData.entity)
            .pipe(
              map(( levels: LimitLevel[] ) => ({ data, levels })),
            )),
          switchMap(( { data, levels } ) => {
            let limit;
            let id;

            const levelId = levels?.find(item => item.title === modalData.level.level)?.id;

            if (data && data.length) {
              if (modalData.gameGroup) {
                let gameGroupId = this.gameGroups.find(gameGroup => gameGroup.name === modalData.gameGroup).id;
                limit = data.find(item => item.gameGroupId === gameGroupId);

                if (limit) {
                  id = limit.id;
                }
              } else {
                limit = data.find(item => item.gameGroupId === null);
                id = limit?.id;
              }
            }

            if (limit) {
              gameLimits = JSON.parse(JSON.stringify(limit['gameLimits']));
            } else {
              id = null;
            }

            if (gameLimits[modalData.level.currency]) {
              gameLimits[modalData.level.currency][levelId] = JSON.parse(JSON.stringify(modalData.limitsFromModal));
            } else {
              gameLimits[modalData.level.currency] = {};
              gameLimits[modalData.level.currency][levelId] = JSON.parse(JSON.stringify(modalData.limitsFromModal));
            }

            let message = modalData.message ?
              modalData.message :
              this.translate.instant('GAME_LIMITS.LIVE.updateLimitsMessage',
                { currency: modalData.level.currency, level: modalData.level.level }
              );

            return this.updateLimits(id, gameLimits, message, false, levels, modalData);
          }),
        )
        .subscribe();

      this.baseColumns = this.baseColumns.filter(column => column);
    } else {
      this.initTable();
    }
  }

  prepareRequestBody( gameLimits, isReset, allLevels, modalData ) {
    let body = {
      schemaDefinitionId: modalData?.isClone ? modalData.schemaDefinitionId : this.schemaDefinition.id,
      gameCode: modalData?.isClone ? modalData.game : this.gameControl.value,
      gameGroupName: modalData?.isClone ? modalData.gameGroup : (this.gameGroupControl.value ? this.gameGroupControl.value : undefined),
      gameLimits: gameLimits,
    };

    if (!isReset) {
      let levels = [];

      if (!modalData?.isClone && this.eurLevels && this.eurLevels.length) {
        this.eurLevels.forEach(
          eurLevel => {
            let level = allLevels.find(item => item.title === eurLevel.level);

            if (level) {
              levels.push(level.id);
            }
          }
        );
      }

      body['levels'] = levels;
    }

    return body;
  }

  updateLimits( id, gameLimits, message, isReset?, levels?, modalData? ) {
    let body = this.prepareRequestBody(gameLimits, isReset, levels, modalData);

    if (modalData?.isClone) {
      return this.newLimitsService.getExtendedGameLimits(modalData.entity, modalData.game, modalData.gameGroup)
        .pipe(
          tap(extendedLimits => {
            body['levels'] = [];

            const targetConfLevels = Object.keys(extendedLimits['EUR']);
            targetConfLevels.forEach(item => {
              let level = levels.find(i => i.title === item);
              if (level) {
                body['levels'].push(level.id);
              }
            });

            let clonedLevel = levels.find(item => item.title === modalData.level.level);
            if (clonedLevel) {
              body['levels'].push(clonedLevel.id);
            }

            body['levels'] = [...new Set(body['levels'])];
          }),
          switchMap(() => this.newLimitsService.changeGameLimits(body, modalData.entity, id)),
          switchMap(() => of(this.notifications.success(message))),
          catchError(( error: HttpErrorResponse ) => of(this.notifications.error(error?.error?.message))),
          finalize(() => this.initTable()),
          take(1),
        );
    } else {
      return this.newLimitsService.changeGameLimits(body, this.entityControl.value, id)
        .pipe(
          switchMap(() => of(this.notifications.success(message))),
          catchError(( error: HttpErrorResponse ) => of(this.notifications.error(error?.error?.message))),
          finalize(() => this.initTable()),
          take(1),
        );
    }
  }

  handleOpenRemoveLimitModal( level, isReset?: boolean ) {
    let gameLimits: Object = {};
    let message = isReset ?
      this.translate.instant('GAME_LIMITS.LIVE.resetLimitsMessage', { currency: level.currency, level: level.level }) :
      this.translate.instant('GAME_LIMITS.LIVE.deleteLimitsMessage', { level: level.level });

    this.dialog.open(BoConfirmationComponent, {
      width: '500px',
      data: {
        message: message,
      },
      disableClose: true
    }).afterClosed()
      .pipe(
        take(1),
      )
      .subscribe(
        ( isConfirmed ) => {
          if (isConfirmed) {
            this.removeLimits(gameLimits, level, isReset);
          }
        }
      );
  }

  removeLimits( gameLimits, level, isReset ) {
    this.newLimitsService.getLimits(this.entityControl.value,
      this.gameControl.value, this.gameGroupControl.value, this.schemaDefinition.id)
      .pipe(
        take(1),
        switchMap(data => this.limitLevelsService.getLimitLevels(this.entityPath)
          .pipe(
            map(( levels: LimitLevel[] ) => ({ data, levels })),
          )),
        switchMap(( { data, levels } ) => {
          let limit;
          let id;

          const levelId = levels?.find(item => item.title === level.level)?.id;

          if (this.gameGroupControl.value) {
            let gameGroupId = this.gameGroups.find(gameGroup => gameGroup.name === this.gameGroupControl.value).id;
            limit = data.find(item => item.gameGroupId === gameGroupId);
          } else {
            limit = data.find(item => item.gameGroupId === null);
          }

          id = limit && limit.id;

          if (limit) {
            gameLimits = JSON.parse(JSON.stringify(limit.gameLimits));

            if (isReset && (!gameLimits[level.currency] || !gameLimits[level.currency][levelId])) {
              return this.cancelResetAlreadyInheritedLimit(level);
            } else {
              delete gameLimits[level.currency][levelId];

              let successfullyMessage = isReset ?
                this.translate.instant('GAME_LIMITS.LIVE.limitsSuccessfullyReset', {
                  currency: level.currency,
                  level: level.level,
                }) :
                this.translate.instant('GAME_LIMITS.LIVE.levelSuccessfullyDeleted', { level: level.level });

              if (isReset) {
                return this.updateLimits(id, gameLimits, successfullyMessage, true, null);
              } else {
                this.eurLevels = this.eurLevels?.filter(item => item.level !== level.level);
                return this.updateLimits(id, gameLimits, successfullyMessage, false, levels);
              }
            }
          }

          if (!id) {
            return this.cancelResetAlreadyInheritedLimit(level, !!isReset);
          }
        }),
      )
      .subscribe();
  }

  cancelResetAlreadyInheritedLimit( level, isReset = true ) {
    isReset ?
      this.notifications.success(this.translate.instant('GAME_LIMITS.LIVE.limitsAlreadyInheritedMessage', {
        currency: level.currency,
        level: level.level
      })) :
      this.notifications.warning(this.translate.instant('GAME_LIMITS.LIVE.entirelyInheritedMessage'));

    return of(null);
  }

  isResetLimitsAvailable( level ) {
    return ['low', 'mid', 'high'].indexOf(level.level) !== -1 || level.currency !== 'EUR';
  }

  handleOpenLevelModals() {
    if (this.eurLevels && this.eurLevels.length) {
      this.dialog.open(AddLevelModalComponent, {
        width: '600px',
        disableClose: true,
        data: {
          path: this.entityPath,
          eurLevels: this.eurLevels,
        }
      }).afterClosed()
        .pipe(
          filter(title => !!title),
          take(1),
        )
        .subscribe(title => {
          let defaultLevel = this.eurLevels.find(level => level.isDefault);
          let newLevel = defaultLevel ?
            JSON.parse(JSON.stringify(defaultLevel)) :
            JSON.parse(JSON.stringify(this.eurLevels[0]));
          newLevel.level = title;
          newLevel.isDefault = false;

          this.eurLevels.push(newLevel);

          this.handleOpenEditLimitModal(newLevel, true);
        });
    }
  }

  isLastVisibleLevel( currency: string ): boolean {
    return this.operatorsLevels?.filter(level => level.currency === currency && !level.hidden)?.length === 1;
  }

  checkItem( event, level, isDisabled ) {
    if (!isDisabled) {
      event.preventDefault();

      this.dialog.open(BoConfirmationComponent, {
        width: '500px',
        disableClose: true,
        data: {
          message: this.translate.instant('GAME_LIMITS.LIVE.changeLevelVisibilityMessage', { level: level.level }),
        }
      }).afterClosed()
        .pipe(
          take(1),
          filter(isRemove => !!isRemove),
          tap(() => this.loading = true),
          switchMap(() => {
            return this.limitLevelsService.getLimitLevels(this.entityPath)
              .pipe(
                switchMap(( limitLevels: LimitLevel[] ) => {
                  let limitLevel = limitLevels?.find(item => item.title === level.level);
                  let body = {
                    levelId: limitLevel?.id,
                    gameCode: this.gameControl.value,
                    isDefault: level.isDefault,
                    hidden: !level.hidden,
                    currency: level.currency !== 'EUR' ? level.currency : undefined,
                  };

                  if (!level.gameLimitLevel) {
                    return this.limitLevelsService.createNewGameLimitLevel(this.entityPath, body);
                  } else {
                    return this.limitLevelsService.removeGameLimitLevel(this.entityPath, level.gameLimitLevel.id)
                      .pipe(
                        switchMap(() => {
                          return level?.hidden && !level?.isDefault ?
                            of(null) :
                            this.limitLevelsService.createNewGameLimitLevel(this.entityPath, body);
                        }),
                      );
                  }
                })
              );
          }),
          catchError(err => {
            this.initTable();
            this.notifications.error(err.error.message);

            return throwError(err);
          }),
        )
        .subscribe(
          () => {
            this.initTable();
            this.notifications.success(this.translate.instant('GAME_LIMITS.LIVE.levelVisibilityChangedMessage', { level: level.level }));
          }
        );
    }
  }

  setLevelAsDefault( event, level ) {
    if (!level?.isDefault) {
      event.preventDefault();

      this.dialog.open(BoConfirmationComponent, {
        width: '500px',
        disableClose: true,
        data: {
          message: this.translate.instant('GAME_LIMITS.LIVE.changeDefaultLevelMessage', { level: level?.level }),
        }
      }).afterClosed()
        .pipe(
          take(1),
          filter(isRemove => {
            if (!isRemove) {
              level.isDefault = false;
            }

            return !!isRemove;
          }),
          tap(() => this.loading = true),
          switchMap(() => {
            return this.limitLevelsService.getLimitLevels(this.entityPath)
              .pipe(
                switchMap(( limitLevels: LimitLevel[] ) => {
                  let limitLevel = limitLevels?.find(item => item.title === level.level);
                  let body = {
                    levelId: limitLevel?.id,
                    gameCode: this.gameControl.value,
                    isDefault: true,
                    hidden: false,
                    inherited: true,
                  };

                  return this.createLevelCustomization(level, body);
                })
              );
          }),
          catchError(err => {
            this.initTable();
            this.notifications.error(err.error.message);

            return throwError(err);
          }),
        )
        .subscribe(
          () => {
            this.initTable();
            this.notifications.success(this.translate.instant('GAME_LIMITS.LIVE.levelSetAsDefaultMessage', { level: level.level }));
          },
        );
    }
  }

  createLevelCustomization( level, body ) {
    if (!level.gameLimitLevel) {
      return this.limitLevelsService.createNewGameLimitLevel(this.entityPath, body);
    } else {
      return this.limitLevelsService.removeGameLimitLevel(this.entityPath, level.gameLimitLevel.id)
        .pipe(
          switchMap(() => {
            return this.limitLevelsService.createNewGameLimitLevel(this.entityPath, body);
          }),
        );
    }
  }
}

import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiPagePanelModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { BoConfirmationModule } from '../../../common/components/bo-confirmation/bo-confirmation.module';
import { ControlMessagesModule } from '../../../common/components/control-messages/control-messages.module';
import { TrimInputValueModule } from '../../../common/directives/trim-input-value/trim-input-value.module';
import { GameGroupService } from '../../../common/services/game-group.service';
import { BriefResolver } from '../../../common/services/resolvers/brief.resolver';
import { AddLevelModalComponent } from './forms/add-level-modal.component';
import { CloneLiveLimitsComponent } from './forms/clone-live-limits.component';
import { EditLiveLimitsComponent } from './forms/edit-live-limits.component';
import { RenameLevelModalComponent } from './forms/rename-level-modal.component';
import { LiveLimitsComponent } from './live-limits.component';

@NgModule({
    imports: [
        TranslateModule,
        CommonModule,
        ReactiveFormsModule,
        ControlMessagesModule,
        BoConfirmationModule,
        MatDialogModule,
        SwuiPagePanelModule,
        LayoutModule,
        MatButtonModule,
        MatFormFieldModule,
        SwuiSelectModule,
        MatInputModule,
        SwuiControlMessagesModule,
        MatIconModule,
        MatTooltipModule,
        MatRadioModule,
        MatCheckboxModule,
        FormsModule,
        TrimInputValueModule,
    ],
  declarations: [
    LiveLimitsComponent,
    EditLiveLimitsComponent,
    CloneLiveLimitsComponent,
    AddLevelModalComponent,
    RenameLevelModalComponent,
  ],
  exports: [
    LiveLimitsComponent,
  ],
  providers: [
    BriefResolver,
    GameGroupService,
  ],
})
export class LiveLimitsModule {
}

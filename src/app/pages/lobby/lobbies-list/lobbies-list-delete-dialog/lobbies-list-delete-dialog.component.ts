import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { LobbyShortData } from '../../lobby.model';


@Component({
    selector: 'lobbies-list-delete-dialog',
    templateUrl: './lobbies-list-delete-dialog.component.html',
    standalone: false
})
export class LobbiesListDeleteDialogComponent {
  constructor( @Inject(MAT_DIALOG_DATA) public readonly lobby: LobbyShortData ) {
  }
}

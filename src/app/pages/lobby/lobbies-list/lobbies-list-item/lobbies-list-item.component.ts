import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { BehaviorSubject, combineLatest, Subject, timer } from 'rxjs';
import { filter, mapTo, switchMap, takeUntil, tap } from 'rxjs/operators';
import { LobbyService } from '../../../../common/services/lobby.service';
import { LobbyShortData, rebuildOptions } from '../../lobby.model';
import { ThemeData, THEMES } from '../../theme.model';
import { environment } from '../../../../../environments/environment';

const POLLING_INTERVAL_MS = 10000; // 10 seconds

@Component({
    selector: 'lobbies-list-item',
    templateUrl: './lobbies-list-item.component.html',
    styleUrls: ['./lobbies-list-item.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class LobbiesListItemComponent implements OnInit, OnDestroy {
  @Input() path: string | undefined;
  @Input() buildLoading = true;
  @Input() availability: Record<string, boolean> = {};

  @Output() onDeleteItem = new EventEmitter<LobbyShortData>();

  private readonly lobby$ = new BehaviorSubject<LobbyShortData>(undefined);
  private readonly destroyed$ = new Subject<void>();

  @Input() set lobby( lobby: LobbyShortData ) {
    this.lobby$.next(lobby);
  }

  constructor( private readonly lobbyService: LobbyService,
               private readonly notification: SwuiNotificationsService,
               private readonly translate: TranslateService,
               private readonly router: Router,
               private readonly route: ActivatedRoute,
               private readonly cd: ChangeDetectorRef ) {
  }

  ngOnInit(): void {
    this.lobby$.pipe(
      tap(() => {
        this.cd.detectChanges();
      }),
      filter(lobby => lobby.build && lobby.build.isBuilding),
      tap(lobby => {
        console.log('Schedule refresh lobby [%s] in %d seconds', lobby.id, POLLING_INTERVAL_MS / 1000);
      }),
      switchMap(lobby => timer(POLLING_INTERVAL_MS).pipe(mapTo(lobby))),
      switchMap(lobby => this.lobbyService.getItem({ id: lobby.id, path: this.path })),
      switchMap(lobby => this.lobbyService.getBuild(lobby, this.path)),
      takeUntil(this.destroyed$)
    ).subscribe(lobby => {
      this.lobby$.next(lobby);
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get lobby(): LobbyShortData {
    return this.lobby$.value;
  }

  get isBuilding(): boolean {
    return this.lobby && this.lobby.build && this.lobby.build.isBuilding;
  }

  get buildRequired(): boolean {
    if (this.isBuilding) {
      return false;
    }
    if (this.lobby.build?.isUpdated) {
      return true;
    }
    return this.lobby.info?.theme?.version !== environment.APP_VERSION;
  }

  get theme(): ThemeData | undefined {
    if (this.lobby.info && this.lobby.info.theme) {
      return THEMES[this.lobby.info.theme.key] || THEMES['playtech'];
    }
  }

  copyItem( event: MouseEvent ) {
    if (event) {
      event.preventDefault();
    }
    this.translate.get('LOBBY.copyTitle', { title: this.lobby.title }).pipe(
      switchMap(title => combineLatest([
        this.lobbyService.copy(title, this.lobby, this.path),
        this.translate.get('LOBBY.notificationCreate', { title })
      ])),
      takeUntil(this.destroyed$)
    ).subscribe(( [{ id }, message] ) => {
      this.notification.success(message, '');
      this.router.navigate(['./edit'], {
        queryParams: { id, path: this.path },
        relativeTo: this.route
      });
    });
  }

  deleteItem( event: MouseEvent ) {
    if (event) {
      event.preventDefault();
    }
    if (!this.isBuilding) {
      this.onDeleteItem.emit(this.lobby);
    }
  }

  rebuildItem( event: MouseEvent ) {
    if (event) {
      event.preventDefault();
    }
    if (!this.isBuilding) {
      this.lobby$.next({
        ...this.lobby,
        build: {
          ...(this.lobby.build ? this.lobby.build : {
            isBuilding: false,
            isReady: false,
            isUpdated: false
          }),
          isBuilding: true
        }
      });
      this.lobbyService.getItem({ id: this.lobby.id, path: this.path }).pipe(
        switchMap(lobby => this.lobbyService.update(lobby.id, rebuildOptions(lobby), true, this.path)),
        takeUntil(this.destroyed$),
      ).subscribe();
    }
  }

  editItem( event: MouseEvent ) {
    if (event) {
      event.preventDefault();
    }
    this.router.navigate(['edit'], {
      queryParams: { path: this.path, id: this.lobby.id },
      relativeTo: this.route
    });
  }

  onCopyUrlClick( event, item ) {
    if (event) {
      event.preventDefault();
    }
    navigator.clipboard.writeText(item).then();
  }

  openNewWindow( event ) {
    if (event) {
      event.preventDefault();
    }
    if (this.lobby.build) {
      const newWindow = window.open(this.lobby.build.links.web, this.lobby.title, `
        toolbar=no,
        location=no,
        directories=no,
        status=no,
        menubar=no,
        scrollbars=no,
        resizable=no,
        copyhistory=no
        innerWidth=${1280},
        innerHeight=${843}
      `);

      newWindow.focus();
    }
  }
}

import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { PanelAction, PERMISSIONS_NAMES, SwHubAuthService, SwHubEntityService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, of, Subject } from 'rxjs';
import { finalize, map, switchMap, takeUntil, tap } from 'rxjs/operators';

import { LobbyService } from '../../../common/services/lobby.service';
import { LobbyShortData } from '../lobby.model';
import { LobbiesListDeleteDialogComponent } from './lobbies-list-delete-dialog/lobbies-list-delete-dialog.component';

@Component({
    selector: 'lobbies-list',
    templateUrl: './lobbies-list.component.html',
    styleUrls: ['./lobbies-list.component.scss'],
    standalone: false
})
export class LobbiesListComponent implements OnInit, OnDestroy {
  panelActions: PanelAction[];
  path?: string;
  items$: Observable<LobbyShortData[]>;
  loading = true;
  buildLoading = true;
  availability: Record<string, boolean> = {};

  private readonly destroyed$ = new Subject<void>();

  constructor( private router: Router,
               private route: ActivatedRoute,
               private authService: SwHubAuthService,
               private readonly lobbyService: LobbyService,
               private readonly entityService: SwHubEntityService,
               private readonly notification: SwuiNotificationsService,
               private readonly translate: TranslateService,
               private readonly dialog: MatDialog
  ) {
    this.items$ = lobbyService.items;
  }

  ngOnInit(): void {
    this.entityService.entitySelected$.pipe(
      map(entity => entity?.path && entity.path !== ':' ? entity.path : ''),
      takeUntil(this.destroyed$)
    ).subscribe(path => {
      this.path = path;
      this.setAvailability();
      this.getGridData();
    });
  }

  getGridData() {
    this.lobbyService.cleanList();
    this.loading = true;
    this.buildLoading = true;

    this.lobbyService.getList(this.path).pipe(
      switchMap(lobbies => {
        this.loading = false;
        return this.lobbyService.getBuilds(lobbies, this.path);
      }),
      tap(() => {
        this.buildLoading = false;
      }),
      finalize(() => {
        this.loading = false;
        this.buildLoading = false;
      }),
      takeUntil(this.destroyed$)
    ).subscribe();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  trackByFn: any = ( _: number, { id }: LobbyShortData ) => id;

  handleRemoveClick( lobby: LobbyShortData ) {
    this.dialog.open(LobbiesListDeleteDialogComponent, {
      width: '500px',
      data: lobby,
      disableClose: true
    }).afterClosed().pipe(
      switchMap(data => data ? this.lobbyService.delete(data.id, this.path).pipe(
        switchMap(() => this.translate.get('LOBBY.notificationDelete', data)),
        tap(message => {
          this.notification.success(message, '');
          this.getGridData();
        })
      ) : of(null)),
      takeUntil(this.destroyed$),
    ).subscribe();
  }

  private setAvailability() {
    const isAllowed = !this.path || this.path === ':'
      ? this.authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_LOBBY_CREATE])
      : this.authService.allowedTo([PERMISSIONS_NAMES.LOBBY_CREATE]);

    this.panelActions = isAllowed ? [
      {
        title: 'LOBBY.create',
        color: 'primary',
        icon: 'add',
        actionFn: () => this.router.navigate(['create'], {
          relativeTo: this.route
        })
      }
    ] : [];

    if (!this.path || this.path === ':') {
      this.availability = {
        view: this.authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_LOBBY_VIEW]),
        create: this.authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_LOBBY_CREATE]),
        edit: this.authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_LOBBY_EDIT]),
        delete: this.authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_LOBBY_DELETE]),
      };
      return;
    }

    this.availability = {
      view: this.authService.allowedTo([PERMISSIONS_NAMES.LOBBY_VIEW]),
      create: this.authService.allowedTo([PERMISSIONS_NAMES.LOBBY_CREATE]),
      edit: this.authService.allowedTo([PERMISSIONS_NAMES.LOBBY_EDIT]),
      delete: this.authService.allowedTo([PERMISSIONS_NAMES.LOBBY_DELETE]),
    };
  }
}

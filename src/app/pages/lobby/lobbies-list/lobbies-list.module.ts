import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { LobbiesListDeleteDialogComponent } from './lobbies-list-delete-dialog/lobbies-list-delete-dialog.component';
import { LobbiesListItemModule } from './lobbies-list-item/lobbies-list-item.module';
import { LobbiesListComponent } from './lobbies-list.component';
import { MatDialogModule } from '@angular/material/dialog';


const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    component: LobbiesListComponent
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    TranslateModule,
    SwuiPagePanelModule,
    LobbiesListItemModule,
    MatProgressSpinnerModule,
    MatCardModule,
    MatButtonModule,
    MatDialogModule,
    LayoutModule,
  ],
  declarations: [
    LobbiesListComponent,
    LobbiesListDeleteDialogComponent,
  ],
})
export class LobbiesListModule {
}

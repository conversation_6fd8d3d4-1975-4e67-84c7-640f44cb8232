import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SwHubEntityService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { EntityDataSourceService } from '../../common/services/entity-data-source.service';

@Component({
    template: '<router-outlet></router-outlet>',
    standalone: false
})
export class LobbiesComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject();

  constructor( private readonly service: EntityDataSourceService,
               private activatedRoute: ActivatedRoute,
               private router: Router,
               private entityService: SwHubEntityService
  ) {
  }

  ngOnInit(): void {
    this.service.show();
    this.entityService.entitySelected$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.router.navigate(['./'], { relativeTo: this.activatedRoute });
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.service.hide();
  }
}

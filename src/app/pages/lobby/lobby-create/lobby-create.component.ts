import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SwHubEntityService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { switchMap, takeUntil, tap } from 'rxjs/operators';
import { LobbyService } from '../../../common/services/lobby.service';

import { UpdateLobbyData } from '../lobby.model';

@Component({
    templateUrl: './lobby-create.component.html',
    standalone: false
})
export class LobbyCreateItemComponent implements OnInit, OnDestroy {

  lobby: UpdateLobbyData = {
    title: '',
    theme: {
      key: 'playtech',
      options: [
        {
          key: 'gameLaunchMode',
          value: {
            pwaDesktop: 'modal',
            pwaMobile: 'modal'
          }
        }
      ]
    }
  };
  private path?: string;

  private readonly destroyed$ = new Subject<void>();

  constructor( private readonly service: LobbyService,
               private readonly entityService: SwHubEntityService,
               private readonly notificationService: SwuiNotificationsService,
               private readonly router: Router,
               private readonly translate: TranslateService,
  ) {
  }

  ngOnInit() {
    this.entityService.entitySelected$.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(entity => {
      this.path = entity && entity.path && entity.path !== ':' ? entity.path : '';
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  handleFormSubmit( data: UpdateLobbyData ): void {
    this.service.create(data, this.path).pipe(
      switchMap(( { title } ) => this.translate.get('LOBBY.notificationCreate', { title })),
      tap(message => this.notificationService.success(message, '')),
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      this.router.navigate(['pages', 'lobby', 'layouts'], {
        queryParams: { path: this.path }
      });
    });
  }
}

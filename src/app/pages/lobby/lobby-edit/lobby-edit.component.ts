import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { PERMISSIONS_NAMES, SwBrowserTitleService, SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { take, tap } from 'rxjs/operators';
import { LobbyService } from '../../../common/services/lobby.service';
import { buildRequired, LobbyExtendedData, UpdateLobbyData } from '../lobby.model';


@Component({
    templateUrl: './lobby-edit.component.html',
    standalone: false
})
export class LobbyEditItemComponent {
  readonly path?: string;
  readonly lobby?: LobbyExtendedData;

  title: string;
  disabled: boolean;

  constructor( private readonly service: LobbyService,
               private readonly notificationService: SwuiNotificationsService,
               private readonly router: Router,
               { snapshot: { queryParams: { path }, data: { lobbyItem } } }: ActivatedRoute,
               private readonly translate: TranslateService,
               private readonly browserTitleService: SwBrowserTitleService,
               readonly authService: SwHubAuthService
  ) {
    this.path = path;
    this.lobby = lobbyItem;
    this.title = `${this.translate.instant('LOBBY.edit')} ${this.lobby.title}`;
    this.disabled = this.path
      ? !authService.allowedTo([PERMISSIONS_NAMES.LOBBY_EDIT])
      : !authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_LOBBY_EDIT]);

    this.browserTitleService.setupTitles('Casino', this.title );
  }

  ngOnDestroy() {
    this.browserTitleService.setupTitles('Casino', '');
  }

  handleFormSubmit( data: UpdateLobbyData ): void {
    this.service.update(this.lobby.id, data, buildRequired(this.lobby, data), this.path).pipe(
      tap(() => {
        const message = this.translate.instant('LOBBY.notificationUpdate', { title: data.title });
        this.notificationService.success(message, '');
      }),
      take(1)
    ).subscribe(() => {
        this.router.navigate(['pages', 'lobby', 'layouts'], {
          queryParams: { path: this.path }
        });
      }
    );
  }
}

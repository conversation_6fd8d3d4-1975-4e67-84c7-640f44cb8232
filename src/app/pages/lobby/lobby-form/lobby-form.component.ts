import { AfterViewChecked, ChangeDetectionStrategy, Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatTabGroup } from '@angular/material/tabs';

import { ActivatedRoute } from '@angular/router';
import { LobbyService } from '../../../common/services/lobby.service';
import { buildOptions, LobbyExtendedData, LobbyThemeOption, UpdateLobbyData, upgradeLobbyThemeOptions } from '../lobby.model';
import { Theme, Themes, THEMES } from '../theme.model';
import { LobbyWidget } from '../../../common/services/lobby-widgets.service';

@Component({
    selector: 'lobby-form',
    templateUrl: './lobby-form.component.html',
    styleUrls: ['./lobby-form.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class LobbyFormComponent implements AfterViewChecked {
  readonly themes: Themes = THEMES;
  readonly path: string;
  readonly widgets: LobbyWidget[];
  activeTabIndex = 0;

  @Output() onSubmit = new EventEmitter<UpdateLobbyData>();

  readonly form: FormGroup;
  submitted: boolean;
  theme: Theme;
  @ViewChild('tabsTemplates') tabsRef: MatTabGroup;

  private lobby: UpdateLobbyData;
  private options: { [key: string]: LobbyThemeOption[] } = {};

  @Input('lobby')
  set setLobby( value: LobbyExtendedData | undefined ) {
    if (value?.theme) {
      value.theme.options = upgradeLobbyThemeOptions(value.theme.options ?? []);
    }
    if (('theme' in value) && value.theme !== null) {
      this.setTheme(value.theme.key);
    } else {
      this.setTheme('bioshock');
    }
    this.form.patchValue({
      title: value?.title ?? '',
      description: value?.description ? `${value?.description}${value.description.slice(-1) === '\n' ? '' : '\n'}` : '',
      menuItems: value?.info?.menuItems ?? [],
    });
    this.lobby = {
      title: value?.title,
      description: value?.description,
      info: value?.info,
      theme: value?.theme
    };
  }

  constructor( { snapshot: { queryParams: { path }, data: { widgets } } }: ActivatedRoute,
               private readonly service: LobbyService ) {
    this.path = path;
    this.widgets = widgets;
    this.form = new FormGroup({
      title: new FormControl('', Validators.required),
      description: new FormControl(''),
      settings: new FormGroup({}),
      theme: new FormGroup({
          options: new FormGroup({}),
        }
      ),
      menuItems: new FormControl([]),
    });
  }

  ngAfterViewChecked(): void {
    if (this.tabsRef) {
      this.tabsRef.realignInkBar();
    }
  }

  get titleControl(): FormControl {
    return this.form.get('title') as FormControl;
  }

  get lobbyOptions(): LobbyThemeOption[] {
    return this.lobby && this.lobby.theme && this.lobby.theme.options || [];
  }

  get themeOptions(): LobbyThemeOption[] {
    return this.options[this.theme.key] || this.lobbyOptions;
  }

  onSelectedIndexChange( index: number ) {
    this.activeTabIndex = index;
  }

  submit() {
    this.service.formSubmitted = true;

    if (this.form.invalid) {
      if (['title', 'description', 'settings'].map(name => this.form.get(name).invalid).some(invalid => invalid)) {
        this.activeTabIndex = 0;
      } else if (this.form.get('theme').invalid) {
        this.activeTabIndex = 1;
      } else {
        this.activeTabIndex = 2;
      }
    }
    this.submitted = true;
    this.form.markAllAsTouched();
    if (this.form.valid) {
      const {
        title,
        description,
        settings = {},
        menuItems = [],
        theme: { options: params = {} } = {}
      } = this.form.value;

      const options: LobbyThemeOption[] = Object.entries({ ...params, ...settings })
        .reduce(( result, [key, value] ) => [...result, { key, value }], []);
      const { info: infoOptions, theme: themeOptions } = buildOptions(this.theme.key, options);

      this.onSubmit.emit({
        title,
        description: description || null,
        theme: {
          key: this.theme.key,
          options: themeOptions
        },
        info: {
          ...(this.lobby && this.lobby.info ? this.lobby.info : {}),
          theme: {
            key: this.theme.key
          },
          options: infoOptions,
          menuItems,
          widgets: this.widgets.filter(( { tag } ) => tag === 'x-widget-scoreboard').reduce(( result, widget ) => ({
            ...result,
            [widget.tag]: widget
          }), {})
        }
      });
    }
  }

  handleActivateTemplate( theme ) {
    const { theme: { options = {} } = {} } = this.form.value;
    this.options[this.theme.key] = Object.entries(options).reduce(( result, [key, value] ) => [...result, {
      key,
      value
    }], []);

    this.setTheme(theme.key);
  }

  private setTheme( themeKey: string ) {
    const key = themeKey in this.themes ? themeKey : 'bioshock';
    this.theme = { key, ...this.themes[key] };
  }
}

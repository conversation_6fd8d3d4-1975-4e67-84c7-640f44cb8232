import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTabsModule } from '@angular/material/tabs';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule } from '@skywind-group/lib-swui';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { TrimInputValueModule } from '../../../common/directives/trim-input-value/trim-input-value.module';
import { LobbyFormComponent } from './lobby-form.component';
import { LobbyMenuItemsModule } from './lobby-menu-items/lobby-menu-items.module';
import { LobbySettingsModule } from './lobby-settings/lobby-settings.module';
import { TemplateSettingsModule } from './template-settings/template-settings.module';
import { TemplatesListModule } from './templates-list/templates-list.module';

@NgModule({
    imports: [
        CommonModule,
        ReactiveFormsModule,
        TabsModule.forRoot(),
        LobbySettingsModule,
        TemplateSettingsModule,
        TemplatesListModule,
        RouterModule,
        TranslateModule,
        LobbyMenuItemsModule,
        MatCardModule,
        SwuiControlMessagesModule,
        MatTabsModule,
        LayoutModule,
        MatIconModule,
        MatButtonModule,
        MatInputModule,
        MatFormFieldModule,
        MatIconModule,
        TrimInputValueModule,
    ],
  exports: [LobbyFormComponent],
  declarations: [LobbyFormComponent],
})
export class LobbyFormModule {
}

import { AfterViewInit, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { SwHubEntityService } from '@skywind-group/lib-swui';
import { DragulaService } from 'ng2-dragula';
import { Subject } from 'rxjs';
import { filter, map, switchMap, takeUntil } from 'rxjs/operators';
import { GameCategoryService } from '../../../../../common/services/game-categories.service';
import { LobbyWidget } from '../../../../../common/services/lobby-widgets.service';
import { GameCategory } from '../../../../games-categories-management/game-category.model';

import { isParentMenuItem, LobbyMenuItem } from '../../../lobby.model';

@Component({
    selector: 'lobby-menu-items-list',
    templateUrl: './lobby-menu-items-list.component.html',
    styleUrls: ['./lobby-menu-items-list.component.scss'],
    providers: [
        DragulaService,
    ],
    standalone: false
})
export class LobbyMenuItemsListComponent implements OnInit, AfterViewInit {
  @Input() themeKey: string = '';

  @Input('widgets') set setWidgets( widgets: LobbyWidget[] | undefined ) {
    this.widgets = (widgets || []).filter(( { targets } ) => targets?.includes('bo-lobby-fullpage'));
  }

  @Output() activeMenuItemChanged = new EventEmitter<{ item: LobbyMenuItem; isChild: boolean }>();
  @Output() menuItemsChanged = new EventEmitter<LobbyMenuItem[]>();
  @ViewChild('container', { read: ElementRef }) container: ElementRef<HTMLDivElement>;

  widgets: LobbyWidget[] = [];
  openedParentMenuItem: LobbyMenuItem | undefined;
  itemsGroup = 'itemsGroup';
  subItemsGroup = 'subItemsGroup';
  categories: GameCategory[] = [];

  private activeMenuItem: LobbyMenuItem | undefined;
  private _menuItems: LobbyMenuItem[] = [];
  private itemsOptions = {
    moves: ( _, __, handle: Element ) => {
      return handle.classList.contains('handle-item');
    }
  };
  private subItemsOptions = {
    moves: ( _, __, handle: Element ) => {
      return handle.classList.contains('handle-sub-item');
    }
  };
  private readonly destroyed$ = new Subject();

  @Input()
  set menuItems( value: LobbyMenuItem[] ) {
    if (!value) {
      return;
    }
    this._menuItems = value;
    if (value.length > 0) {
      this.activateItem(value[0]);
    }
  }

  get menuItems(): LobbyMenuItem[] {
    return this._menuItems;
  }

  constructor( private readonly dragulaService: DragulaService,
               private readonly categoriesService: GameCategoryService,
               private readonly entityService: SwHubEntityService
  ) {
    this.dragulaService.createGroup(this.itemsGroup, this.itemsOptions);
    this.dragulaService.createGroup(this.subItemsGroup, this.subItemsOptions);
  }

  ngOnInit() {
    this.dragulaService.dropModel(this.itemsGroup).pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( { targetModel } ) => {
      this._menuItems = targetModel;
      this.menuItemsChanged.emit(this._menuItems);
    });

    this.dragulaService.dropModel(this.subItemsGroup).pipe(
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      this.menuItemsChanged.emit(this._menuItems);
    });
    this.entityService.entitySelected$.pipe(
      filter(data => !!data),
      map(data => data.path),
      switchMap(( path: string ) => this.categoriesService.getList('', path)),
      takeUntil(this.destroyed$)
    ).subscribe(( val: GameCategory[] ) => {
      this.categories = val;
    });
  }

  ngAfterViewInit() {
    Object.assign(this.itemsOptions, {
      mirrorContainer: this.container.nativeElement
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  hasSubcategories( item: LobbyMenuItem ): boolean {
    return item && item.subcategories && item.subcategories.length > 0;
  }

  isActiveItem( item: LobbyMenuItem ): boolean {
    return this.activeMenuItem === item;
  }

  isOpenItem( item: LobbyMenuItem ): boolean {
    return this.openedParentMenuItem === item;
  }

  isParentItem( item: LobbyMenuItem ): boolean {
    return isParentMenuItem(item);
  }

  onOpenDropdown( event: Event ) {
    event.stopPropagation();
    event.preventDefault();
  }

  activateItem( item: LobbyMenuItem | undefined, parent?: LobbyMenuItem ) {
    this.activeMenuItem = item;
    this.openedParentMenuItem = parent || item;
    this.activeMenuItemChanged.emit(this.activeMenuItem ? { item: this.activeMenuItem, isChild: !!parent } : undefined);
  }

  addMenuItem( event: MouseEvent ) {
    event.preventDefault();
    const item: LobbyMenuItem = {
      title: 'New menu item',
      translations: {}
    };
    this.addItem(event, item);
  }

  addCategoryItem( event: MouseEvent, category: GameCategory, parent?: LobbyMenuItem ) {
    const item: LobbyMenuItem = {
      title: category.title,
      description: category.description,
      icon: category.icon,
      gameCategoryId: category.id,
      translations: category.translations,
    };
    this.addItem(event, item, parent);
  }

  addWidgetItem( event: MouseEvent, { title, ...widget }: LobbyWidget, parent?: LobbyMenuItem ) {
    const item: LobbyMenuItem = {
      title,
      widget,
      translations: {},
    };
    this.addItem(event, item, parent);
  }

  addFavoriteItem( event: MouseEvent, parent?: LobbyMenuItem ) {
    const item: LobbyMenuItem = {
      title: 'New favorite item',
      showFavoriteGames: true,
      translations: {},
    };
    this.addItem(event, item, parent);
  }

  addRecentItem( event: MouseEvent, parent?: LobbyMenuItem ) {
    const item: LobbyMenuItem = {
      title: 'New recent item',
      showRecentGames: true,
      translations: {},
    };
    this.addItem(event, item, parent);
  }

  deleteItem( event: Event, index: number, items: LobbyMenuItem[] ) {
    event.preventDefault();

    const item = items.splice(index, 1).shift();
    if (this.activeMenuItem === item) {
      this.activeMenuItem = undefined;
    }
    if (this.openedParentMenuItem === item) {
      this.openedParentMenuItem = undefined;
    }

    if (items.length > 0) {
      this.activeMenuItem = items[0];
    } else if (this.openedParentMenuItem) {
      if (this.menuItems.length) {
        this.activeMenuItem = this.openedParentMenuItem;
      } else {
        this.openedParentMenuItem = undefined;
        this.activeMenuItem = undefined;
      }
    } else {
      this.activeMenuItem = undefined;
    }

    this.activeMenuItemChanged.emit(this.activeMenuItem ? {
      item: this.activeMenuItem,
      isChild: !!this.openedParentMenuItem
    } : undefined);
    this.menuItemsChanged.emit(this.menuItems);
  }

  private addItem( event: MouseEvent, menuItem: LobbyMenuItem, parent?: LobbyMenuItem ): void {
    event.preventDefault();

    if (parent) {
      if (!Array.isArray(parent.subcategories)) {
        parent.subcategories = [];
      }
      parent.subcategories.push(menuItem);
    } else {
      this.menuItems.push(menuItem);
    }
    this.activateItem(menuItem, parent);
    this.menuItemsChanged.emit(this.menuItems);
  }
}

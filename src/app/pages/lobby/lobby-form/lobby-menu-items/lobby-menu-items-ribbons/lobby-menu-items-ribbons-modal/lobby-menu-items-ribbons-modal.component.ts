import { Component, Inject, Input } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ErrorMessage } from '../../../../../../common/components/mat-user-editor/user-form.component';
import { ValidationService } from '../../../../../../common/services/validation.service';

import { LobbyMenuItemRibbon } from '../../../../lobby.model';

@Component({
    selector: 'lobby-menu-items-ribbons-modal',
    templateUrl: './lobby-menu-items-ribbons-modal.component.html',
    styleUrls: ['./lobby-menu-items-ribbons-modal.component.scss'],
    standalone: false
})
export class LobbyMenuItemsRibbonsModalComponent {
  form: FormGroup;
  isEdit: boolean;
  submitted: boolean;
  messageErrors: ErrorMessage = {
    maxLength: `VALIDATION.maxLength`,
    required: 'VALIDATION.required'
  };

  private _ribbon: LobbyMenuItemRibbon | undefined;

  @Input()
  set ribbon( val: LobbyMenuItemRibbon | undefined ) {
    this.isEdit = !!val;
    this._ribbon = {
      text: val && val.text || '',
      bg: val && val.bg || '',
      color: val && val.color || ''
    };
    this.form.patchValue(this._ribbon);
  }

  get ribbon(): LobbyMenuItemRibbon | undefined {
    return this._ribbon;
  }

  constructor( public dialogRef: MatDialogRef<LobbyMenuItemsRibbonsModalComponent>,
               @Inject(MAT_DIALOG_DATA) public data: any,
               private fb: FormBuilder ) {
    this.form = this.initForm(data.maxLength);
    this.ribbon = data.ribbon;
  }

  get textControl(): FormControl {
    return this.form.get('text') as FormControl;
  }

  get colorControl(): FormControl {
    return this.form.get('color') as FormControl;
  }

  get bgControl(): FormControl {
    return this.form.get('bg') as FormControl;
  }

  onSave( event: Event ) {
    event.preventDefault();
    this.submitted = true;
    if (this.form.valid) {
      const value = this.form.value as LobbyMenuItemRibbon;

      this.dialogRef.close(value);
    }
  }

  onCancel( event: Event ) {
    event.preventDefault();
    this.dialogRef.close(null);
  }

  private initForm(maxLength?: string): FormGroup {
    return this.fb.group({
      text: ['', Validators.compose([Validators.required, maxLength ? ValidationService.maxLength(parseInt(maxLength, 10)) : null])],
      bg: ['', Validators.required],
      color: ['', Validators.required]
    });
  }
}

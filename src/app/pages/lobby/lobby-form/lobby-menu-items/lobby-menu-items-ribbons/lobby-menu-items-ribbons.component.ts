import { Component, ElementRef, forwardRef, Input, ViewChild } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { filter, takeUntil, tap } from 'rxjs/operators';
import { BoConfirmationComponent } from 'src/app/common/components/bo-confirmation/bo-confirmation.component';

import { LobbyMenuItemRibbon } from '../../../lobby.model';
import { LobbyMenuItemsRibbonsModalComponent } from './lobby-menu-items-ribbons-modal/lobby-menu-items-ribbons-modal.component';

@Component({
    selector: 'lobby-menu-items-ribbons',
    templateUrl: './lobby-menu-items-ribbons.component.html',
    styleUrls: ['./lobby-menu-items-ribbons.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => LobbyMenuItemsRibbonsComponent),
            multi: true
        }
    ],
    standalone: false
})

export class LobbyMenuItemsRibbonsComponent implements ControlValueAccessor {
  @Input()
  set ribbons( val: LobbyMenuItemRibbon[] ) {
    this.options = val;
    this._ribbons = val;
  }

  get ribbons(): LobbyMenuItemRibbon[] {
    return this._ribbons;
  }

  @Input() maxLength: number;
  @Input() placeholder = 'Ribbon';

  isDisabled: boolean;
  selectedRibbon?: LobbyMenuItemRibbon;
  options: LobbyMenuItemRibbon[] = [];

  @ViewChild('ribbonLabel') ribbonLabelRef: ElementRef | undefined;

  private _onChange: ( _: any ) => void = (() => {
  });
  private _ribbons: LobbyMenuItemRibbon[] = [];
  private readonly destroyed$ = new Subject<void>();

  constructor( private dialog: MatDialog ) {
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  writeValue( value: LobbyMenuItemRibbon | undefined ) {
    this.changeRibbon(value);
  }

  onTouched: any = () => {
  }

  setDisabledState( isDisabled: boolean ): void {
    this.isDisabled = !!isDisabled;
  }

  registerOnChange( fn: ( _: any ) => void ): void {
    this._onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  getRibbonBg( option: LobbyMenuItemRibbon ): { [key: string]: string } {
    return {
      'background': option.bg,
      'color': option.color
    };
  }

  onSelect( option: LobbyMenuItemRibbon ) {
    this.selectRibbon(option);
  }

  onClear( event: Event ) {
    event.preventDefault();
    event.stopPropagation();
    this.selectRibbon(undefined);
  }

  openModal( ribbon?: LobbyMenuItemRibbon ) {
    this.dialog.open(LobbyMenuItemsRibbonsModalComponent, {
      width: '600px',
      data: {
        ribbon: ribbon,
        maxLength: this.maxLength
      },
      disableClose: true
    }).afterClosed()
      .pipe(
        filter(data => !!data),
        takeUntil(this.destroyed$)
      ).subscribe(( val: LobbyMenuItemRibbon ) => {
      this.ribbons.push(val);
      this.selectRibbon(val);
    });
  }

  remove() {
    this.dialog.open(BoConfirmationComponent, {
      width: '500px',
      disableClose: true,
      data: { message: 'Are you sure you want to remove this ribbon?' }
    }).afterClosed()
      .pipe(
        filter(isConfirmed => isConfirmed),
        tap(() => {
          const ribbonToRemove = this.selectedRibbon;
          this.ribbons = this.ribbons.filter(( el: LobbyMenuItemRibbon ) =>
            JSON.stringify(el) !== JSON.stringify(ribbonToRemove));
          this.selectRibbon(undefined);
        }),
        takeUntil(this.destroyed$)
      ).subscribe();
  }

  private selectRibbon( val: LobbyMenuItemRibbon ): void {
    this.changeRibbon(val);
    this._onChange(val);
  }

  private changeRibbon( val: LobbyMenuItemRibbon ): void {
    this.selectedRibbon = val;

    this.options = val
      ? this.ribbons.filter(el => JSON.stringify(el) !== JSON.stringify(val))
      : this.ribbons;

    setTimeout(() => {
      if (this.ribbonLabelRef) {
        const label = this.ribbonLabelRef.nativeElement as HTMLElement;
        label.setAttribute('style', `background: ${val ? val.bg : ''}; color: ${val ? val.color : ''}`);
      }
    });
  }
}

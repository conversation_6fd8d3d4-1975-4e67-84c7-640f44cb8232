import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AVAILABLE_LANGUAGES, LobbyMenuItem, LobbyMenuItemSettings } from '../../../../lobby.model';
import slugify from 'slugify';


@Component({
    selector: 'lobby-menu-items-general',
    templateUrl: './lobby-menu-items-general.component.html',
    standalone: false
})

export class LobbyMenuItemsGeneralComponent implements OnInit, OnDestroy {
  @Output() settingsChange = new EventEmitter<LobbyMenuItemSettings>();
  @Output() validStatusChange = new EventEmitter<boolean>();

  @Input() menuItemIndex?: number;
  @Input() isChild = false;
  @Input() isRibbonsEnabled = false;
  @Input() themeKey?: string;

  @Input('submitted')
  set setSubmitted( val: boolean ) {
    this.submitted = !!val;
    this.form.markAllAsTouched();
  }

  @Input('menuItem')
  set setMenuItem( value: LobbyMenuItem | undefined ) {
    this.menuItem = value;
    if (value) {
      const { slug, translations, title, description, icon } = value;
      this.hasSlug = slug !== undefined;
      this.form.patchValue({
        slug,
        translations: {
          en: {
            title: title ?? '',
            description: description ?? '',
            icon: icon ?? ''
          },
          ...(translations || {})
        }
      }, { emitEvent: false });
    }
  }

  readonly availableLanguages = AVAILABLE_LANGUAGES;
  readonly form: FormGroup;

  menuItem?: LobbyMenuItem;
  submitted?: boolean;

  private hasSlug = false;

  private readonly destroyed$ = new Subject<void>();

  constructor() {
    this.form = new FormGroup({
      slug: new FormControl(),
      translations: new FormControl()
    });
  }

  ngOnInit(): void {
    this.form.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      const value = this.form.getRawValue() ?? {};
      const translations = value.translations ?? {};
      if (!this.hasSlug && !this.isChild && this.menuItemIndex !== undefined && Boolean(translations?.en?.title)) {
        if (!this.form.get('slug').dirty) {
          const slug = slugify(translations?.en?.title, { replacement: '_', lower: true }) + '_' + this.menuItemIndex;
          this.form.patchValue({ slug }, { emitEvent: false });
          value.slug = slug;
        }
      }
      this.settingsChange.emit({
        slug: value.slug,
        title: '',
        ...(translations.en ?? {}),
        translations
      });
    });

    this.form.statusChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(result => {
      this.validStatusChange.emit(result === 'VALID');
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get slugControl(): FormControl {
    return this.form.get('slug') as FormControl;
  }

  get translationsControl(): FormControl {
    return this.form.get('translations') as FormControl;
  }
}

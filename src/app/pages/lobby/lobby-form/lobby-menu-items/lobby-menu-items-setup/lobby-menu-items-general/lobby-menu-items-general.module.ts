import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiTranslationsManagerModule } from '@skywind-group/lib-swui';
import { TrimInputValueModule } from '../../../../../../common/directives/trim-input-value/trim-input-value.module';
import { PipesModule } from '../../../../../../common/pipes/pipes.module';
import { LobbyMenuItemsRibbonsModule } from '../../lobby-menu-items-ribbons/lobby-menu-items-ribbons.module';

import { LobbyMenuItemsGeneralComponent } from './lobby-menu-items-general.component';
import { LobbyMenuItemsInfoComponent } from './lobby-menu-items-info/lobby-menu-items-info.component';


@NgModule({
  declarations: [
    LobbyMenuItemsGeneralComponent,
    LobbyMenuItemsInfoComponent,
  ],
  exports: [LobbyMenuItemsGeneralComponent],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        TranslateModule,
        SwuiControlMessagesModule,
        PipesModule,
        LobbyMenuItemsRibbonsModule,
        SwuiTranslationsManagerModule,
        MatInputModule,
        MatFormFieldModule,
        MatButtonModule,
        MatIconModule,
        LayoutModule,
        TrimInputValueModule,
    ],
})
export class LobbyMenuItemsGeneralModule {
}

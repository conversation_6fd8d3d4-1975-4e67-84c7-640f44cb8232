import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Observable } from 'rxjs';
import { ErrorMessage } from '../../../../../../../common/components/mat-user-editor/user-form.component';
import { LobbyMenuItemRibbon } from '../../../../../lobby.model';


@Component({
    selector: 'lobby-menu-items-info',
    templateUrl: './lobby-menu-items-info.component.html',
    styleUrls: ['./lobby-menu-items-info.component.scss'],
    standalone: false
})
export class LobbyMenuItemsInfoComponent {
  @Input() submitted?: boolean;
  @Input() isIconEnabled = false;
  @Input() isRibbonsEnabled = false;

  readonly form: FormGroup;
  defaultRibbons: LobbyMenuItemRibbon[] = [
    {
      text: 'Hot',
      bg: '#EC2033',
      color: '#FFFFFF'
    },
    {
      text: 'New',
      bg: '#FFCF00',
      color: '#000000'
    },
    {
      text: 'Exclusive',
      bg: '#1B76EE',
      color: '#FFFFFF'
    }
  ];

  messageErrors: ErrorMessage = {
    required: 'VALIDATION.required'
  };

  constructor( private fb: FormBuilder,
               private readonly cdr: ChangeDetectorRef ) {
    this.form = this.initForm();
  }

  initForm(val?: any): FormGroup {
    const group = this.fb.group({
      id: [''],
      title: ['', Validators.required],
      description: [''],
      icon: [''],
      ribbon: []
    });

    if (val) {
      group.patchValue(val);
    }

    return group;
  }

  setValue(val: any) {
    if (val) {
      this.form.patchValue(val);
    }
  }

  get valueChanges(): Observable<any> {
    return this.form.valueChanges;
  }

  get statusChanges(): Observable<any> {
    return this.form.statusChanges;
  }

  get invalid(): boolean {
    return this.form.invalid;
  }

  patchValue( value: { [name: string]: any }, options?: { emitEvent: boolean } ): void {
    this.form.patchValue(value, options);
    this.form.markAsPristine();
    this.form.markAsUntouched();
  }

  isRemoveIconAvailable(): boolean {
    return this.iconControl.value && this.iconControl.value !== ' ';
  }

  get titleControl(): FormControl {
    return this.form.get('title') as FormControl;
  }

  get descriptionControl(): FormControl {
    return this.form.get('description') as FormControl;
  }

  get iconControl(): FormControl {
    return this.form.get('icon') as FormControl;
  }

  get ribbonControl(): FormControl {
    return this.form.get('ribbon') as FormControl;
  }

  removeIcon( event: MouseEvent ) {
    event.preventDefault();
    event.stopPropagation();
    this.iconControl.reset();
    const iconInput = document.getElementById('icon-control') as HTMLInputElement;
    iconInput.value = '';
  }

  onIconFileChange( event: any ) {
    const reader = new FileReader();
    const target = event.target as any;

    if (target.files && target.files.length) {
      const [file] = target.files;
      if (file.type === 'image/svg+xml') {
        reader.readAsDataURL(file);
      } else {
        this.iconControl.setErrors({ requiredFileType: true });
      }

      reader.onload = () => {
        this.iconControl.patchValue(reader.result);
        this.cdr.detectChanges();
      };
    }
  }
}

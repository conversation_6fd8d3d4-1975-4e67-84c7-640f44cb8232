import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { CdnService } from '../../../../../../../common/services/cdn.service';
import { ValidationService } from '../../../../../../../common/services/validation.service';
import { GameInfo } from '../../../../../../../common/typings';
import { LobbyMenuItemRibbon } from '../../../../../lobby.model';
import { LobbyMenuItemPreviewService } from '../lobby-menu-item-preview.service';


@Component({
    selector: 'lobby-menu-items-preview-form',
    templateUrl: './lobby-menu-items-preview-from.component.html',
    styleUrls: ['./lobby-menu-items-preview-form.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class LobbyMenuItemsPreviewFormComponent implements OnInit, OnDestroy {
  @Input() gameCategory?: string;
  images: { [key: string]: string; } = {};
  defaultRibbons: LobbyMenuItemRibbon[] = [
    {
      text: 'Hot',
      bg: 'linear-gradient(270deg, #EC203300 0%, #EC2033 50%, #EC2033 100%)',
      color: '#FFFFFF'
    },
    {
      text: 'New',
      bg: 'linear-gradient(270deg, #FECB0100 0%, #FECB01 51%, #FECB01 100%)',
      color: '#000000'
    },
    {
      text: 'Exclusive',
      bg: 'linear-gradient(270deg, #1B76EE00 0%, #1B76EE 51%, #1B76EE 100%)',
      color: '#FFFFFF'
    }
  ];
  games: GameInfo[] = [];
  readonly form: FormGroup;
  readonly errorMessages = {
    urlIsNotCorrect: 'VALIDATION.urlIsNotCorrect',
  };

  private readonly destroyed$ = new Subject();

  constructor(private fb: FormBuilder,
    private lobbyMenuItemPreviewService: LobbyMenuItemPreviewService,
    private readonly cdnService: CdnService,
    private readonly cdr: ChangeDetectorRef
  ) {
    this.lobbyMenuItemPreviewService.getGames()
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe((val: GameInfo[]) => {
        this.games = val;
        this.cdr.markForCheck();
      });

    this.form = this.initForm();
  }

  ngOnInit() {
    this.form.valueChanges
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe(() => {
        this.cdr.detectChanges();
      });

    this.cdnService.gameImages.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(images => {
      this.images = images;
      this.cdr.detectChanges();
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  initForm(val?: any): FormGroup {
    const group = this.fb.group({
      id: [''],
    }) as FormGroup<any>;

    this.games.forEach(({ code }) => {
      group.setControl(code, new FormGroup({
        ribbon: new FormControl(),
        overlayUrl: new FormControl(null, ValidationService.urlValidation)
      }));
    });

    if (val) {
      group.patchValue(val);
    }

    return group;
  }

  setValue(val: any) {
    if (val) {
      this.form.patchValue(val);
    }
  }

  getOverlayUrl(game: GameInfo): string {
    return this.getOverlayUrlControl(game.code).value || game?.defaultInfo?.images?.overlay;
  }

  get valueChanges(): Observable<any> {
    return this.form.valueChanges;
  }

  get statusChanges(): Observable<any> {
    return this.form.statusChanges;
  }

  get invalid(): boolean {
    return this.form.invalid;
  }

  getRibbonControl(name: string): FormControl {
    return this.form.get(name).get('ribbon') as FormControl;
  }

  getOverlayUrlControl(name: string): FormControl {
    return this.form.get(name).get('overlayUrl') as FormControl;
  }

  patchValue(values: { [name: string]: any; }, options?: { emitEvent: boolean; }): void {
    Object.entries(values).forEach(([code, value]) => {
      if (code in this.form.controls) {
        this.form.get(code).patchValue(value, options);
      } else {
        this.form.setControl(code, new FormControl(value));
      }
    });
    this.form.markAsPristine();
    this.form.markAsUntouched();
  }

  getGameImageUrl(gameId: string): string {
    return this.images[gameId];
  }
}

import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { SwHubEntityService, SwuiSelectOption } from '@skywind-group/lib-swui';
import { BehaviorSubject, of, Subject } from 'rxjs';
import { map, pluck, switchMap, takeUntil, tap } from 'rxjs/operators';

import { GameCategoryService } from '../../../../../../common/services/game-categories.service';
import { GameInfo } from '../../../../../../common/typings';
import { GameCategory } from '../../../../../games-categories-management/game-category.model';
import {
  AVAILABLE_LANGUAGES,
  LobbyMenuItem,
  LobbyMenuItemGameOverlayTranslations,
  LobbyMenuItemGameRibbonTranslations,
  LobbyMenuItemRibbon
} from '../../../../lobby.model';
import { LobbyMenuItemPreviewService } from './lobby-menu-item-preview.service';

export interface LobbyMenuItemGameTranslations {
  [lang: string]: {
    [key: string]: {
      ribbon: LobbyMenuItemRibbon,
      overlayUrl: string
    }
  };
}

@Component({
    selector: 'lobby-menu-items-preview',
    templateUrl: './lobby-menu-items-preview.component.html',
    styleUrls: ['./lobby-menu-items-preview.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class LobbyMenuItemsPreviewComponent implements OnInit, OnDestroy {
  @Output() translationsChange = new EventEmitter<LobbyMenuItemGameTranslations>();
  @Output() validStatusChange = new EventEmitter<boolean>();

  readonly form: FormGroup;
  availableLanguages: SwuiSelectOption[] = AVAILABLE_LANGUAGES;
  isLoading = false;
  gameCategory?: GameCategory;
  games: GameInfo[] = [];
  private _gameRibbons?: LobbyMenuItemGameRibbonTranslations;
  private _overlayUrls?: LobbyMenuItemGameOverlayTranslations;
  private _menuItem$ = new BehaviorSubject<LobbyMenuItem>(null);
  private _submitted = false;
  private _entityPath: string = '';
  private readonly destroyed$ = new Subject<void>();

  @Input()
  set submitted( val: boolean ) {
    this._submitted = !!val;
    this.form.markAllAsTouched();
  }

  get submitted(): boolean {
    return this._submitted;
  }

  @Input()
  set menuItem( val: LobbyMenuItem | undefined ) {
    this._menuItem$.next(val);
  }

  get menuItem(): LobbyMenuItem | undefined {
    return this._menuItem$.value;
  }

  constructor( private cdr: ChangeDetectorRef,
               private entityService: SwHubEntityService,
               private lobbyMenuItemPreviewService: LobbyMenuItemPreviewService,
               private readonly gameService: GameCategoryService) {
    this.entityService.entitySelected$.pipe(
      pluck('path'),
      takeUntil(this.destroyed$),
    ).subscribe(( path: string ) => {
      this._entityPath = path;
    });

    this.form = new FormGroup({
      translations: new FormControl({})
    });
  }

  ngOnInit(): void {

    this.lobbyMenuItemPreviewService.getGames()
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe((val: GameInfo[]) => {
        this.games = val;
      });

    this._menuItem$.pipe(
      tap( (val: LobbyMenuItem) => {
        this.isLoading = true;
        if (val) {
          this._gameRibbons = val.gameRibbons;
          this._overlayUrls = val.overlayUrls;
        }
      }),
      map(( { gameCategoryId } ) => gameCategoryId),
      switchMap(id => id ? this.gameService.getItem(id, this._entityPath) : of<GameCategory>(undefined)),
      tap(gameCategory => {
        this.gameCategory = gameCategory;
      }),
      switchMap(gameCategory => gameCategory ? this.gameService.getCategoryGames(gameCategory.id, this._entityPath) : of<GameInfo[]>([])),
      takeUntil(this.destroyed$)
    ).subscribe(games => {
      this.isLoading = false;
      games.filter(( { code } ) => !(code in this.form.controls));
      this.lobbyMenuItemPreviewService.setGames(games);
      const translations = this.mergeTranslations(this._gameRibbons, this._overlayUrls);
      this.translationsControl.patchValue(translations, { emitEvent: false });
      this.cdr.detectChanges();
    });

    this.form.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      const value = (this.form.getRawValue() || {}).translations || {};
      this.translationsChange.emit(value);
    });

    this.form.statusChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(result => {
      this.validStatusChange.emit(result === 'VALID');
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get translationsControl(): FormControl {
    return this.form.get('translations') as FormControl;
  }

  private mergeTranslations(ribbons: LobbyMenuItemGameRibbonTranslations = {en: {}},
                            overlayUrls: LobbyMenuItemGameOverlayTranslations = {en: {}}) {
    return Object.keys(ribbons).reduce((res, lang) => {
      const ribbonItems = ribbons[lang];
      const overlayItems = overlayUrls[lang] || {};
      res[lang] = {};

      Object.keys(ribbonItems).forEach(key => {
        if (typeof ribbonItems[key] === 'object') {
          res[lang][key] = {ribbon: ribbonItems[key]};
        }
      });
      Object.keys(overlayItems).forEach(key => {
        res[lang][key] = {...(res[lang][key] || {}), overlayUrl: overlayItems[key]};
      });

      return res;
    }, {});
  }
}

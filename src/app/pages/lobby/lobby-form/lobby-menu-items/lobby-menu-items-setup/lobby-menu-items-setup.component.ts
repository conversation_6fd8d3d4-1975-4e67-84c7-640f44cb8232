import { AfterViewChecked, Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';

import {
  isParentMenuItem,
  LobbyMenuItem,
  LobbyMenuItemGameOverlayTranslations,
  LobbyMenuItemGameRibbonTranslations,
  LobbyMenuItemOptions,
  LobbyMenuItemSettings,
  LobbyMenuItemWidget,
  LobbyMenuItemWidgets
} from '../../../lobby.model';
import { LobbyWidget } from '../../../../../common/services/lobby-widgets.service';
import { LobbyMenuItemGameTranslations } from './lobby-menu-items-preview/lobby-menu-items-preview.component';


@Component({
    selector: 'lobby-menu-items-setup',
    templateUrl: './lobby-menu-items-setup.component.html',
    styleUrls: ['./lobby-menu-items-setup.component.scss'],
    standalone: false
})
export class LobbyMenuItemsSetupComponent implements AfterViewChecked {
  @Input() themeKey: string;
  @Input() widgets: LobbyWidget[] = [];
  @Input() submitted: boolean;

  @Output() menuItemChanged = new EventEmitter<void>();
  @Output() validStatusChanged = new EventEmitter<boolean>();

  @ViewChild('tabs') tabsRef: MatTabGroup;

  menuItem?: LobbyMenuItem;
  menuItemIndex?: number;
  isChild = false;
  selectedTabIndex = 0;

  private readonly statuses = {
    general: true,
    options: true,
    widgets: true,
    widget: true,
    preview: true
  };

  @Input()
  set activeMenuItem( val: { item: LobbyMenuItem; index: number; isChild: boolean } | undefined ) {
    this.menuItem = val?.item;
    this.menuItemIndex = val?.index;
    this.isChild = val?.isChild ?? false;
    this.selectedTabIndex = 0;
  }

  ngAfterViewChecked(): void {
    if (this.tabsRef) {
      this.tabsRef.realignInkBar();
    }
  }

  onSelectedIndexChange( index: number ) {
    this.selectedTabIndex = index;
  }

  onGeneralChanged( { slug, title, description, icon, translations }: LobbyMenuItemSettings ) {
    this.menuItem.slug = slug;
    this.menuItem.title = title;
    this.menuItem.description = description;
    this.menuItem.icon = icon;
    this.menuItem.translations = translations;
    this.menuItemChanged.emit();
  }

  onOptionsChanged( { isCommissionFilter, isGridLayout }: LobbyMenuItemOptions ) {
    this.menuItem.options = {
      ...this.menuItem.options,
      isCommissionFilter,
      isGridLayout
    };
    this.menuItemChanged.emit();
  }

  onWidgetsChanged( items: LobbyMenuItemWidgets ) {
    this.menuItem.options = {
      ...this.menuItem.options,
      widgets: JSON.parse(JSON.stringify(items))
    };
    this.menuItemChanged.emit();
  }

  onWidgetChanged( widget: LobbyMenuItemWidget ) {
    this.menuItem.widget = widget;
    this.menuItemChanged.emit();
  }

  onGameTranslationsChanged( translations: LobbyMenuItemGameTranslations ) {
    const gameRibbons: LobbyMenuItemGameRibbonTranslations = {};
    const overlayUrls: LobbyMenuItemGameOverlayTranslations = {};

    Object.keys(translations).forEach(lang => {
      gameRibbons[lang] = {};
      overlayUrls[lang] = {};
      Object.keys(translations[lang]).forEach(code => {
        if (translations[lang][code].ribbon) {
          gameRibbons[lang][code] = translations[lang][code].ribbon;
        }

        if (translations[lang][code].overlayUrl) {
          overlayUrls[lang][code] = translations[lang][code].overlayUrl;
        }
      });
    });

    this.menuItem.gameRibbons = gameRibbons;
    this.menuItem.overlayUrls = overlayUrls;
    this.menuItemChanged.emit();
  }

  onValidStatusChanged( name: string, value: boolean ) {
    this.statuses[name] = value;
    this.validStatusChanged.emit(Object.values(this.statuses).every(status => status));
  }

  get widgetPlacements(): string[] {
    if (isParentMenuItem(this.menuItem)) {
      return ['header', 'footer'];
    }
    if (this.isChild) {
      return ['grid'];
    }
    return ['header', 'footer', 'grid'];
  }
}

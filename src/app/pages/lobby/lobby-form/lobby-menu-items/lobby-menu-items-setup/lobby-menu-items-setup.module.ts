import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { MatDynamicFormModule, SwuiControlMessagesModule, SwuiSelectModule, SwuiTranslationsManagerModule } from '@skywind-group/lib-swui';

import { CdnService } from '../../../../../common/services/cdn.service';
import { LobbyMenuItemsRibbonsModule } from '../lobby-menu-items-ribbons/lobby-menu-items-ribbons.module';
import { LobbyMenuItemsGeneralModule } from './lobby-menu-items-general/lobby-menu-items-general.module';
import { LobbyMenuItemsOptionsComponent } from './lobby-menu-items-options/lobby-menu-items-options.component';
import { LobbyMenuItemPreviewService } from './lobby-menu-items-preview/lobby-menu-item-preview.service';
import { LobbyMenuItemsPreviewFormComponent } from './lobby-menu-items-preview/lobby-menu-items-preview-form/lobby-menu-items-preview-form.component';
import { LobbyMenuItemsPreviewComponent } from './lobby-menu-items-preview/lobby-menu-items-preview.component';
import { LobbyMenuItemsSetupComponent } from './lobby-menu-items-setup.component';
import { LobbyMenuItemsWidgetComponent } from './lobby-menu-items-widget/lobby-menu-items-widget.component';
import { LobbyMenuItemsWidgetsComponent } from './lobby-menu-items-widgets/lobby-menu-items-widgets.component';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatIconModule } from '@angular/material/icon';
import { PlacementPositionModule } from './lobby-menu-items-widgets/placement/placement-position/placement-position.module';
import { PlacementComponent } from './lobby-menu-items-widgets/placement/placement.component';
import { MatSelectModule } from '@angular/material/select';
import { WidgetOptionsDialogModule } from './lobby-menu-items-widgets/widget-options-dialog/widget-options-dialog.module';


@NgModule({
  declarations: [
    LobbyMenuItemsSetupComponent,
    LobbyMenuItemsPreviewComponent,
    LobbyMenuItemsPreviewFormComponent,
    LobbyMenuItemsOptionsComponent,
    LobbyMenuItemsWidgetsComponent,
    LobbyMenuItemsWidgetComponent,
    PlacementComponent,
  ],
  exports: [LobbyMenuItemsSetupComponent],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        LobbyMenuItemsGeneralModule,
        TranslateModule,
        MatSlideToggleModule,
        LobbyMenuItemsRibbonsModule,
        SwuiTranslationsManagerModule,
        MatProgressSpinnerModule,
        MatTabsModule,
        SwuiSelectModule,
        MatFormFieldModule,
        MatInputModule,
        SwuiControlMessagesModule,
        LayoutModule,
        MatButtonModule,
        MatMenuModule,
        MatIconModule,
        MatSelectModule,
        MatTooltipModule,
        PlacementPositionModule,
        WidgetOptionsDialogModule,
        MatDynamicFormModule
    ],
  providers: [
    CdnService,
    LobbyMenuItemPreviewService,
  ]
})
export class LobbyMenuItemsSetupModule {
}

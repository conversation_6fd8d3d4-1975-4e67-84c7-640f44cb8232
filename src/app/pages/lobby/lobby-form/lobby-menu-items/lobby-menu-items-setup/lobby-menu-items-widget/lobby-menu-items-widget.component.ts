import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormGroup, ValidationErrors } from '@angular/forms';
import { Subject } from 'rxjs';
import { distinctUntilChanged, map, takeUntil } from 'rxjs/operators';
import { ErrorMessage } from '../../../../../../common/components/mat-user-editor/user-form.component';
import { LobbyMenuItemWidget } from '../../../../lobby.model';
import { LobbyWidget, removeObsoleteWidgetOptions } from '../../../../../../common/services/lobby-widgets.service';
import { DynamicFormOptionData } from '@skywind-group/lib-swui/swui-dynamic-form/dynamic-form.model';
import { isEqual } from 'lodash';

function toJson( value: string | undefined ): { [name: string]: any } {
  try {
    return JSON.parse(value);
  } catch (_) {
    return {};
  }
}

function jsonReplacer( k, v: any ) {
  if (k === '') {
    return v;
  }
  return v === null ? undefined : v;
}

function jsonStringify( value: any ): string {
  return JSON.stringify(value, jsonReplacer);
}

@Component({
    selector: 'lobby-menu-items-widget',
    templateUrl: './lobby-menu-items-widget.component.html',
    styleUrls: ['./lobby-menu-items-widget.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class LobbyMenuItemsWidgetComponent implements OnInit, OnDestroy {
  @Input() submitted: boolean | undefined;

  @Input('values') set setWidget( value: LobbyMenuItemWidget | undefined ) {
    if (!isEqual(this.value, value)) {
      this.value = value;
      this.setOptions();
    }
  }

  @Input('widgets') set setWidgets( value: LobbyWidget[] | undefined ) {
    this.widgets = (value ?? []).reduce(( result, w ) => ({ ...result, [w.tag]: w }), {});
    this.setOptions();
  }

  @Output() valuesChanged = new EventEmitter<LobbyMenuItemWidget>();
  @Output() validStatusChange = new EventEmitter<boolean>();

  options: DynamicFormOptionData | undefined;

  readonly messageErrors: ErrorMessage = {
    jsonInvalid: 'JSON invalid'
  };
  readonly form = new FormGroup({}, {
    validators: ( control: FormGroup ): ValidationErrors | null => {
      const value = control.getRawValue();
      if (value) {
        try {
          jsonStringify(value);
        } catch {
          return { jsonInvalid: true };
        }
      }
      return null;
    }
  });

  private value?: LobbyMenuItemWidget;
  private widgets?: { [tag: string]: LobbyWidget } = {};

  private readonly destroyed$ = new Subject<void>();

  ngOnInit(): void {
    this.form.valueChanges.pipe(
      distinctUntilChanged((prev, curr) => isEqual(prev, curr)),
      map(value => this.value ? {
        tag: this.value.tag,
        src: this.value.src,
        path: this.value.path,
        options: {
          ...removeObsoleteWidgetOptions(this.value.options, Object.keys(this.widget.properties ?? {})),
          ...(value ? toJson(jsonStringify(value)) : {})
        }
      } : undefined),
      takeUntil(this.destroyed$)
    ).subscribe(val => {
      if (this.form.valid) {
        this.value = val;
        this.valuesChanged.emit(val);
      }
    });

    this.form.statusChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(result => {
      this.validStatusChange.emit(result === 'VALID');
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get widget(): LobbyWidget | undefined {
    if (this.value && this.widgets) {
      return this.widgets[this.value.tag];
    }
  }

  private setOptions() {
    if (this.value && this.widgets) {
      const { tag, options } = this.value;
      const widget = this.widgets[tag];
      if (widget) {
        this.options = widget?.properties && Object.entries(widget.properties).reduce(( result, [key, prop] ) => ({
          ...result,
          [key]: {
            ...prop,
            value: options && options[key]
          }
        }), {});
      }
    }
  }
}

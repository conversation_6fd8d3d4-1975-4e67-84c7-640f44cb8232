import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { AbstractControl, FormArray, FormControl, FormGroup } from '@angular/forms';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { filter, map, take, takeUntil } from 'rxjs/operators';
import { ErrorMessage } from '../../../../../../common/components/mat-user-editor/user-form.component';
import { LobbyWidget, removeObsoleteWidgetOptions } from '../../../../../../common/services/lobby-widgets.service';
import { LobbyService } from '../../../../../../common/services/lobby.service';
import { LobbyMenuItemWidgetOptions, LobbyMenuItemWidgets } from '../../../../lobby.model';
import { buildPlacementForm, PlacementFormValues } from './placement/placement.component';
import { MatDialog } from '@angular/material/dialog';
import { WidgetOptionsDialogComponent, WidgetOptionsDialogData } from './widget-options-dialog/widget-options-dialog.component';

interface FormValues {
  widgets: {
    options: string;
    placement: PlacementFormValues;
    widget: LobbyWidget;
  }[];
}

function fromJson( value: { [name: string]: any } ): string | undefined {
  try {
    return JSON.stringify(value);
  } catch (_) {
    return '{}';
  }
}

function toJson( value: string | undefined ): { [name: string]: any } {
  try {
    return JSON.parse(value);
  } catch (_) {
    return {};
  }
}

function isRequired(property: any): boolean {
  let requiredKeys = [];

  JSON.stringify(property ?? {}, ( key, value ) => {
    if (key === 'required' && value === true) {
      requiredKeys.push(value);
    }
    return value;
  });
  return !!requiredKeys.length;
}

function newWidgetForm( widget: LobbyWidget, key?: string, values?: LobbyMenuItemWidgetOptions ): FormGroup {
  const { desktop, mobile, portrait, ...data } = values ?? {};
  const widgetValues = removeObsoleteWidgetOptions(data, Object.keys(widget.properties ?? {}));
  const optionsControl = new FormControl(fromJson(widgetValues));
  if (isRequired(widget.properties)) {
    optionsControl.setValidators((control: AbstractControl) => {
      return !control.value || control.value === '{}' ? { 'empty': {valid: false} } : null;
    });
    optionsControl.updateValueAndValidity();
  }
  return new FormGroup({
    widget: new FormControl(widget),
    widgetOptions: new FormControl(widgetValues),
    options: optionsControl,
    placement: buildPlacementForm(key, { desktop, mobile, portrait })
  });
}

@Component({
    selector: 'lobby-menu-items-widgets',
    templateUrl: './lobby-menu-items-widgets.component.html',
    styleUrls: ['./lobby-menu-items-widgets.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class LobbyMenuItemsWidgetsComponent implements OnInit, OnDestroy {
  readonly messageErrors: ErrorMessage = {
    jsonInvalid: 'JSON invalid'
  };
  readonly form: FormGroup;

  @Input('widgets')
  set setWidgets( widgets: LobbyWidget[] | undefined ) {
    this.widgets$.next((widgets ?? []).filter(( { targets } ) => targets?.includes('bo-lobby-fullpage') === false));
  }

  @Input('values')
  set setValues( value: LobbyMenuItemWidgets | undefined ) {
    this.values$.next(value);
  }

  @Input() placements?: string[];

  @Output() valuesChanged = new EventEmitter<LobbyMenuItemWidgets>();
  @Output() validStatusChange = new EventEmitter<boolean>();

  submitted = false;
  private setControl = false;

  private readonly widgets$ = new BehaviorSubject<LobbyWidget[]>([]);
  private readonly values$ = new BehaviorSubject<LobbyMenuItemWidgets | undefined>(undefined);
  private readonly destroyed$ = new Subject<void>();

  constructor( private readonly cd: ChangeDetectorRef,
               private readonly dialog: MatDialog,
               private readonly service: LobbyService ) {
    this.form = new FormGroup({
      widgets: new FormArray([])
    });
  }

  ngOnInit(): void {
    this.service.formSubmitted$
      .pipe(
        filter(val => !!val),
        takeUntil(this.destroyed$)
      )
      .subscribe(() => {
        this.submitted = true;
        this.form.markAllAsTouched();
        LobbyMenuItemsWidgetsComponent.scrollToInvalidOptions();
        this.cd.detectChanges();
      });

    combineLatest([
      this.widgets$,
      this.values$
    ]).pipe(
      map(( [widgets, values] ) => {
        const control = new FormArray([]);
        Object.entries(values ?? {})
          .sort(( [, { order: a }], [, { order: b }] ) => a - b)
          .map(( [key, { tag, options }] ) => {
            const w = widgets.find(widget => tag === widget.tag);
            if (w) {
              return newWidgetForm(w, key, options);
            }
          }).forEach(item => {
          if (item) {
            control.push(item);
          }
        });
        return control;
      })
    ).subscribe(control => {
      this.setControl = true;
      this.form.setControl('widgets', control);
      this.cd.markForCheck();
    });

    this.form.valueChanges.pipe(
      map(() => {
        const value = this.form.getRawValue() as FormValues;
        return value?.widgets.reduce<LobbyMenuItemWidgets>(( result, { placement, options, widget }, index ) => {
          const { mobile, desktop, portrait, section } = placement;
          return {
            ...result,
            ...(section ? {
              [section === 'grid' ? `grid_${index}` : section]: {
                order: index,
                tag: widget.tag,
                src: widget.src,
                path: widget.path,
                options: {
                  ...removeObsoleteWidgetOptions(widget.options, Object.keys(widget.properties ?? {})),
                  ...toJson(options),
                  ...(section === 'grid' ? { desktop, mobile, portrait } : {})
                }
              }
            } : {})
          };
        }, {});
      }),
      takeUntil(this.destroyed$)
    ).subscribe(( value: LobbyMenuItemWidgets ) => {
      if (this.setControl) {
        this.setControl = false;
      } else if (this.form.valid) {
        this.valuesChanged.emit(value);
      }
    });

    this.form.statusChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(result => {
      this.validStatusChange.emit(result === 'VALID');
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get widgets(): LobbyWidget[] {
    return this.widgets$.value;
  }

  get widgetsControl(): FormArray {
    return this.form.get('widgets') as FormArray;
  }

  onAddClick( event: MouseEvent, widget: LobbyWidget ) {
    event.preventDefault();
    this.widgetsControl.push(newWidgetForm(widget));
  }

  onEditClick( event: MouseEvent, control: AbstractControl, widget: LobbyWidget, options?: LobbyMenuItemWidgetOptions ) {
    event.preventDefault();
    this.dialog.open<any, WidgetOptionsDialogData>(WidgetOptionsDialogComponent, {
      width: '700px',
      data: {
        widget,
        options
      },
      disableClose: true
    }).afterClosed()
      .pipe(take(1))
      .subscribe(value => {
        if (value) {
          control.setValue(value);
        }
      });
  }

  onRemoveClick( event: MouseEvent, index: number ) {
    event.preventDefault();
    this.widgetsControl.removeAt(index);
  }

  private static scrollToInvalidOptions() {
    setTimeout(() => {
      const scrollableContainer = document.getElementById('main-content-wrap');
      const invalidOptions: HTMLCollectionOf<Element> = document.getElementsByClassName('invalid-widget');
      if (scrollableContainer && invalidOptions.item(0)) {
        const elementToScroll = invalidOptions.item(0) as HTMLElement;
        scrollableContainer.scroll({top: elementToScroll.offsetTop + 230, behavior: 'smooth'});
      }
    });
  }
}

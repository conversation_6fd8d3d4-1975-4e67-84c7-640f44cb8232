import { Component, Input, OnChanges } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, Validators } from '@angular/forms';
import { ErrorMessage } from '../../../../../../../../common/components/mat-user-editor/user-form.component';
import { ValidationService } from '../../../../../../../../common/services/validation.service';
import { LobbyMenuItemWidgetGridOptionsPositions } from '../../../../../../lobby.model';
import { MatSelectChange } from '@angular/material/select';


type GridType = 'desktop' | 'mobile' | 'portrait';

export function buildPositionForm(gridType: GridType, options?: LobbyMenuItemWidgetGridOptionsPositions ): FormGroup {
  const group = new FormGroup({
    fixed: new FormControl(options?.fixed),
    x: new FormControl(options?.x, { updateOn: 'blur' }),
    y: new FormControl(options?.y, { validators: Validators.compose([
        Validators.required,
        Validators.min(1),
        Validators.max(500),
        ValidationService.digitsOnlyValidator
      ]), updateOn: 'blur'}),
    width: new FormControl(gridType === 'mobile' || gridType === 'portrait' ? 1 : options?.width, { validators: Validators.compose([
      Validators.required,
      Validators.min(1),
      Validators.max(7),
      ValidationService.digitsOnlyValidator
    ]), updateOn: 'blur' }),
    height: new FormControl(gridType === 'mobile' || gridType === 'portrait' ? 1 : options?.height, { validators: Validators.compose([
      Validators.required,
      Validators.min(1),
      ValidationService.digitsOnlyValidator
    ]), updateOn: 'blur'}),
  });

  const xControl = group.get('x') as FormControl;
  const fixedControl = group.get('fixed') as FormControl;

  if (fixedControl.value === 'fixed') {
    xControl.setValidators(Validators.compose([
      Validators.required,
      Validators.min(1),
      Validators.max(7),
      ValidationService.digitsOnlyValidator
    ]));
  } else {
    xControl.clearValidators();
    xControl.setValue(null);
  }

  return group;
}

@Component({
    selector: 'placement-position',
    templateUrl: './placement-position.component.html',
    styleUrls: ['./placement-position.component.scss'],
    standalone: false
})
export class PlacementPositionComponent implements OnChanges {
  @Input() gridType?: GridType;
  @Input() control?: AbstractControl | null;

  messageErrors: ErrorMessage = {
    max: `VALIDATION.max`,
    min: `VALIDATION.min`,
    required: 'VALIDATION.required',
    invalidDigitsOnly: 'VALIDATION.invalidDigitsOnly'
  };

  ngOnChanges(): void {
    if (this.gridType === undefined) {
      this.fixedControl?.disable({ emitEvent: false });
      this.xControl?.disable({ emitEvent: false });
      this.yControl?.disable({ emitEvent: false });
      this.widthControl?.disable({ emitEvent: false });
      this.heightControl?.disable({ emitEvent: false });
    } else if (this.gridType === 'portrait') {
      this.fixedControl?.disable({ emitEvent: false });
      this.xControl?.disable({ emitEvent: false });
      this.widthControl?.disable({ emitEvent: false });
      this.heightControl?.disable({ emitEvent: false });
    } else if (this.gridType === 'mobile') {
      this.widthControl?.disable({ emitEvent: false });
      this.heightControl?.disable({ emitEvent: false });
    }
  }

  fixedChanged( { value }: MatSelectChange ) {
    if (value === 'fixed') {
      this.xControl.setValidators(Validators.compose([
        Validators.required,
        Validators.min(1),
        Validators.max(7),
        ValidationService.digitsOnlyValidator
      ]));
    } else {
      this.xControl.clearValidators();
      this.xControl.setValue(null, { emitEvent: null });
    }
    this.xControl.updateValueAndValidity();
  }

  get form(): FormGroup | null | undefined {
    return this.control as FormGroup;
  }

  get fixedControl(): FormControl | undefined {
    return this.form?.get('fixed') as FormControl;
  }

  get xControl(): FormControl | undefined {
    return this.form?.get('x') as FormControl;
  }

  get yControl(): FormControl | undefined {
    return this.form?.get('y') as FormControl;
  }

  get widthControl(): FormControl | undefined {
    return this.form?.get('width') as FormControl;
  }

  get heightControl(): FormControl | undefined {
    return this.form?.get('height') as FormControl;
  }
}

import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { LobbyMenuItemWidgetGridOptionsPositions } from '../../../../../lobby.model';
import { buildPositionForm } from './placement-position/placement-position.component';
import { MatSelectChange } from '@angular/material/select';

type SectionControlValues = 'header' | 'footer' | 'grid' | null;

type FixedControlValues = 'left' | 'right' | 'center' | 'fixed' | null;

export interface PositionFormValues {
  fixed: FixedControlValues;
  x: number | null;
  y: number | null;
  width: number | null;
  height: number | null;
}

export interface PlacementFormValues {
  section: SectionControlValues;
  desktop: PositionFormValues;
  mobile: PositionFormValues;
  portrait: PositionFormValues;
}

export interface PlacementFormOptions {
  desktop?: LobbyMenuItemWidgetGridOptionsPositions;
  mobile?: LobbyMenuItemWidgetGridOptionsPositions;
  portrait?: LobbyMenuItemWidgetGridOptionsPositions;
}

export function buildPlacementForm( key: string | undefined, { desktop, mobile, portrait }: PlacementFormOptions ): FormGroup {
  const desktopForm = buildPositionForm('desktop', desktop);
  const mobileForm = buildPositionForm('mobile', mobile);
  const portraitForm = buildPositionForm('portrait', portrait);
  const section = key ? ['header', 'footer'].includes(key) ? key : 'grid' : undefined;
  if (section !== 'grid') {
    desktopForm.disable();
    mobileForm.disable();
    portraitForm.disable();
  }
  return new FormGroup({
    section: new FormControl(section, Validators.required),
    desktop: desktopForm,
    mobile: mobileForm,
    portrait: portraitForm,
  });
}

@Component({
    selector: 'lobby-menu-items-options-widget-placement',
    templateUrl: './placement.component.html',
    styleUrls: ['./placement.component.scss'],
    standalone: false
})
export class PlacementComponent implements OnInit, OnDestroy {
  @Input() control?: AbstractControl | null;
  @Input() placements?: string[];

  private _destroyed$ = new Subject<void>();

  ngOnInit() {
    this.setupAutofill();
  }

  ngOnDestroy(): void {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  sectionChanged( { value }: MatSelectChange ) {
    if (value === 'grid') {
      this.desktopControl?.enable();
      this.mobileControl?.enable();
      this.portraitControl?.enable();
    } else {
      this.desktopControl?.disable();
      this.mobileControl?.disable();
      this.portraitControl?.disable();
    }
  }

  get desktopFixedControl(): FormControl | undefined {
    return this.desktopControl?.get('fixed') as FormControl;
  }

  get desktopXControl(): FormControl | undefined {
    return this.desktopControl?.get('x') as FormControl;
  }

  get desktopYControl(): FormControl | undefined {
    return this.desktopControl?.get('y') as FormControl;
  }

  get mobileFixedControl(): FormControl | undefined {
    return this.mobileControl?.get('fixed') as FormControl;
  }

  get mobileXControl(): FormControl | undefined {
    return this.mobileControl?.get('x') as FormControl;
  }

  get mobileYControl(): FormControl | undefined {
    return this.mobileControl?.get('y') as FormControl;
  }

  get portraitYControl(): FormControl | undefined {
    return this.portraitControl?.get('y') as FormControl;
  }

  get sectionControl(): FormControl | undefined {
    return this.form?.get('section') as FormControl;
  }

  get desktopControl(): FormControl | undefined {
    return this.form?.get('desktop') as FormControl;
  }

  get mobileControl(): FormControl | undefined {
    return this.form?.get('mobile') as FormControl;
  }

  get portraitControl(): FormControl | undefined {
    return this.form?.get('portrait') as FormControl;
  }

  get form(): FormGroup | null | undefined {
    return this.control as FormGroup;
  }

  private setupAutofill() {
    this.desktopFixedControl.valueChanges
      .pipe(
        filter(val => !!val && !this.mobileFixedControl.value),
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: FixedControlValues ) => {
        this.mobileFixedControl.patchValue(val);
      });

    this.desktopXControl.valueChanges
      .pipe(
        filter(val => !!val && !this.mobileXControl.value && this.mobileFixedControl.value === 'fixed'),
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: number ) => {
        this.mobileXControl.patchValue(val);
      });

    this.desktopYControl.valueChanges
      .pipe(
        filter( val => !!val),
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: number ) => {
        if (!this.mobileYControl.value) {
          this.mobileYControl.patchValue(val);
        }

        if (!this.portraitYControl.value) {
          this.portraitYControl.patchValue(val);
        }
      });

    this.mobileFixedControl.valueChanges
      .pipe(
        filter( val => !!val && !this.desktopFixedControl.value),
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: FixedControlValues ) => {
        this.desktopFixedControl.patchValue(val);
      });

    this.mobileXControl.valueChanges
      .pipe(
        filter(val => !!val && !this.desktopXControl.value && this.desktopFixedControl.value === 'fixed'),
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: number ) => {
        this.desktopXControl.patchValue(val);
      });

    this.mobileYControl.valueChanges
      .pipe(
        filter( val => !!val),
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: number ) => {
        if (!this.desktopYControl.value) {
          this.desktopYControl.patchValue(val);
        }

        if (!this.portraitYControl.value) {
          this.portraitYControl.patchValue(val);
        }
      });

    this.portraitYControl.valueChanges
      .pipe(
        filter( val => !!val),
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: number ) => {
        if (!this.desktopYControl.value) {
          this.desktopYControl.patchValue(val);
        }

        if (!this.mobileYControl.value) {
          this.mobileYControl.patchValue(val);
        }
      });
  }
}

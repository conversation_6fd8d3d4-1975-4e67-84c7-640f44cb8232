import { ChangeDetectionStrategy, ChangeDetectorRef, Component, forwardRef, Input, OnDestroy, OnInit } from '@angular/core';
import { ControlValueAccessor, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors, Validator } from '@angular/forms';
import { SwHubEntityService, SwHubShortEntity } from '@skywind-group/lib-swui';
import { BehaviorSubject, iif, of, Subject, zip } from 'rxjs';
import { filter, map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { GameCategoryService } from '../../../../common/services/game-categories.service';
import { LobbyWidget } from '../../../../common/services/lobby-widgets.service';
import { LobbyMenuItem } from '../../lobby.model';


@Component({
    selector: 'lobby-menu-items',
    templateUrl: './lobby-menu-items.component.html',
    styleUrls: ['./lobby-menu-items.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => LobbyMenuItemsComponent),
            multi: true
        },
        {
            provide: NG_VALIDATORS,
            useExisting: forwardRef(() => LobbyMenuItemsComponent),
            multi: true
        }
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class LobbyMenuItemsComponent implements ControlValueAccessor, Validator, OnInit, OnDestroy {
  @Input() submitted: boolean;
  @Input() themeKey: string;

  @Input('widgets') set setWidgets( widgets: LobbyWidget[] | undefined ) {
    this.widgets$.next((widgets || []).filter(( { targets } ) => targets?.includes('bo-lobby')));
  }

  activeMenuItem: { item: LobbyMenuItem; index: number; isChild: boolean } | undefined;
  widgets: LobbyWidget[] = [];
  isValid = true;
  disabled = false;

  onChange: ( _: any ) => void = (() => {
  });

  onTouched: any = (() => {
  });

  private entityPath: string = '';
  private readonly widgets$ = new BehaviorSubject<LobbyWidget[]>([]);
  private readonly menuItems$ = new BehaviorSubject<LobbyMenuItem[]>([]);
  private readonly destroyed$ = new Subject();

  constructor( private readonly categoryService: GameCategoryService,
               private readonly entityService: SwHubEntityService,
               private readonly cd: ChangeDetectorRef ) {
    this.entityService.entitySelected$.pipe(
      filter(entity => !!entity),
      tap(( entity: SwHubShortEntity ) => this.entityPath = entity.path),
      takeUntil(this.destroyed$)
    ).subscribe();
  }

  ngOnInit() {
    this.widgets$.pipe(
      switchMap(widgets =>
        iif(() => widgets.length === 0,
          of([]),
          this.categoryService.getList('', this.entityPath).pipe(
            map(categories => categories.map(( { id } ) => id)),
            switchMap(ids => zip(...ids.map(id => id
              ? this.categoryService.getCategoryGames(id, this.entityPath)
              : of([])))),
            map(value => value.reduce(( result, item ) => [...result, ...item], [])),
            map(games => games.map(( { code } ) => code)),
            map(gameCodes => widgets.map(widget => ({
              ...widget,
              disabled: widget.options?.gameCode ? !gameCodes.includes(widget.options.gameCode) : false
            })))
          )
        ))
    ).subscribe(widgets => {
      this.widgets = widgets;
      this.cd.markForCheck();
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get menuItems(): LobbyMenuItem[] {
    return this.menuItems$.value;
  }

  writeValue( value: LobbyMenuItem[] ) {
    if (!value) {
      return;
    }
    this.menuItems$.next(value);
  }

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( disabled: boolean ) {
    this.disabled = disabled;
  }

  validate(): ValidationErrors | null {
    return this.isValid || this.menuItems.length === 0 ? null : { invalidMenuItems: { valid: false } };
  }

  onMenuItemsChange( menuItems: LobbyMenuItem[] ) {
    this.menuItems$.next(menuItems);
    this.onChange(menuItems);
  }

  activateItem( data: { item: LobbyMenuItem; isChild: boolean } | undefined ) {
    this.activeMenuItem = data && {
      ...data,
      index: data.isChild ? undefined : this.menuItems.findIndex(item => item === data.item)
    };
  }

  onMenuItemChanged() {
    setTimeout(() => {
      this.onChange(this.menuItems);
    }, 0);
  }

  onMenuItemStatusChanged( isValid: boolean ) {
    this.isValid = isValid;
    this.onChange(this.menuItems);
  }
}

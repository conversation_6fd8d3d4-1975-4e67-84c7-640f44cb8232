import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { MatCardModule } from '@angular/material/card';
import { LobbyMenuItemsListModule } from './lobby-menu-items-list/lobby-menu-items-list.module';
import { LobbyMenuItemsSetupModule } from './lobby-menu-items-setup/lobby-menu-items-setup.module';

import { LobbyMenuItemsComponent } from './lobby-menu-items.component';


@NgModule({
  declarations: [LobbyMenuItemsComponent],
  exports: [LobbyMenuItemsComponent],
  imports: [
    CommonModule,
    LobbyMenuItemsListModule,
    LobbyMenuItemsSetupModule,
    LayoutModule,
    MatCardModule,
  ]
})
export class LobbyMenuItemsModule { }

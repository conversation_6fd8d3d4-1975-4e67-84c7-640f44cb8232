import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { DynamicFormOptionData, SwHubEntityService } from '@skywind-group/lib-swui';
import { LobbyThemeOption } from '../../lobby.model';
import { OPTIONS } from '../../theme.model';
import { merge, of, Subject } from 'rxjs';
import { delay, distinctUntilChanged, map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { cloneDeep } from 'lodash';
import { CollectionOptionData } from '@skywind-group/lib-swui/swui-dynamic-form/dynamic-form.model';
import { GameService } from '../../../../common/services/game.service';
import { SelectOption } from '../../../../common/components/swConditions/conditions-element.component';

@Component({
    selector: 'lobby-settings',
    templateUrl: './lobby-settings.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class LobbySettingsComponent implements OnChanges, OnInit, OnDestroy {
  @Input() form?: FormGroup;
  @Input() name?: string;
  @Input() values?: LobbyThemeOption[];
  @Input() submitted = false;

  data: DynamicFormOptionData = {};
  gamesOptions: SelectOption[] = [];

  private readonly destroyed$ = new Subject<void>();

  constructor(private readonly cd: ChangeDetectorRef,
              private readonly gameService: GameService,
              private readonly swHubEntityService: SwHubEntityService) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.hasOwnProperty('name') || changes.hasOwnProperty('values')) {
      this.setData(this.values.reduce((result, {key, value}) => ({
        ...result,
        [key]: value
      }), {}));
    }
  }

  ngOnInit(): void {
    merge(
      this.form?.valueChanges.pipe(
        map(value => value?.[this.name]?.gameLaunchMode?.pwaDesktop),
        distinctUntilChanged()
      ),
      this.form?.valueChanges.pipe(
        map(value => value?.[this.name]?.gameLaunchMode?.pwaMobile),
        distinctUntilChanged()
      )
    ).pipe(
      switchMap(() => this.swHubEntityService.entitySelected$),
      switchMap(entity => this.gameService.getAllGames(entity.path)),
      tap(games => {
        this.gamesOptions = games
          .filter(({type}) => type === 'live')
          .map((game) => ({value: game.code, ...game}));
        this.setData(this.form?.value?.[this.name], true);
        this.cd.detectChanges();
      }),
      switchMap(() => this.form.valueChanges),
      distinctUntilChanged((prev, curr) => prev.settings.numberOfPlayers_show === curr.settings.numberOfPlayers_show),
      takeUntil(this.destroyed$)
    ).subscribe(data => {
      if (data.settings.numberOfPlayers_show) {
        this.form.get('settings').get('numberOfPlayers_games').enable();
      } else {
        this.form.get('settings').get('numberOfPlayers_games').setValue([]);
        this.form.get('settings').get('numberOfPlayers_games').disable();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  private setData(formValues: Record<string, any>, loaded?: boolean) {
    const schema = cloneDeep(OPTIONS);
    if (formValues?.gameLaunchMode?.pwaDesktop === 'modal') {
      delete (schema.gameLaunchMode as CollectionOptionData).inputs.pwaDesktopSize;
    }

    if (schema.numberOfPlayers_games?.type === 'select-table') {
      schema.numberOfPlayers_games.data = this.gamesOptions;
      if (loaded) {
        schema.numberOfPlayers_games.loading = false;
      }
    }

    of(null)
      .pipe(delay(1))
      .subscribe(() => {
        if (!this.form?.value.settings.numberOfPlayers_show) {
          this.form?.get('settings')?.get('numberOfPlayers_games')?.disable();
        }
      });

    this.data = Object.entries(schema).reduce((result, [key, value]) => ({
      ...result,
      [key]: {...value, value: formValues[key]}
    }), {});
  }
}

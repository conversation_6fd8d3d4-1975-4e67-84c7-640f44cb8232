import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Input,
  OnChanges,
  SimpleChanges,
  ViewChild
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { ControlOptionData, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { BehaviorSubject, merge, Observable, of, Subject } from 'rxjs';
import { filter, map, mergeMap, takeUntil } from 'rxjs/operators';

import { LobbyThemeOption } from '../../lobby.model';
import { ThemeOptionsData } from '../../theme.model';


function setValue( data: ControlOptionData, values: LobbyThemeOption[], name: string ): ControlOptionData {
  const option = values.find(( { key } ) => key === name);
  return option ? { ...data, value: option.value } : data;
}

function buildFn( values$: Observable<{ options: ThemeOptionsData; values: any; }>, name: string ): Observable<string> {
  return values$.pipe(
    map(( { values, options } ) => Object.entries(values)
      .filter(( [key] ) => key in options && name in options[key])
      .map(( [key, value] ) => ({ value, fn: options[key][name] }))
      .map(( { value, fn } ) => fn(value as any))
      .join(''))
  );
}

function buildDownloadData( themeTitle: string, value: any ): { href: string; title: string } {
  const title = themeTitle.replace(/\s+/g, '_').toLowerCase();
  const data = encodeURIComponent(JSON.stringify(value));
  return {
    href: `data:application/json;charset=utf-8,${data}`,
    title: `${title ? title + '_' : ''}theme-settings.json`
  };
}

@Component({
    selector: '[template-settings]',
    templateUrl: './template-settings.component.html',
    styleUrls: ['./template-settings.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class TemplateSettingsComponent implements OnChanges {
  @Input() themeTitle = '';
  @Input('template-settings') form: FormGroup;
  @Input() name: string;
  @Input() values: LobbyThemeOption[];
  @Input() options: ThemeOptionsData;
  @Input() submitted: boolean;

  @ViewChild('fileInput', { static: true }) fileInputRef: ElementRef;

  data: ThemeOptionsData;
  download?: { href: string; title: string };

  readonly values$: Observable<{ options: ThemeOptionsData; values: any; }>;
  readonly css$: Observable<string>;
  readonly meta$: Observable<string>;
  private readonly themeInputs$ = new BehaviorSubject<{
    name: string;
    form: FormGroup;
    options: ThemeOptionsData
  }>(null);
  private readonly destroyed$ = new Subject<void>();

  constructor( private readonly cdr: ChangeDetectorRef,
               private readonly notifications: SwuiNotificationsService,
               private readonly translate: TranslateService
  ) {
    this.values$ = this.themeInputs$.pipe(
      filter(( { name, form, options } ) => !!name && !!form && !!options),
      mergeMap(( { name, form, options } ) => form.valueChanges.pipe(
        map(values => ({
          options,
          values: name in values ? values[name] : {}
        })),
      ))
    );
    this.css$ = buildFn(this.values$, 'cssFn');
    this.meta$ = buildFn(this.values$, 'metaFn');
  }

  ngOnInit(): void {
    merge(of({ values: {} }), this.values$).pipe(
      takeUntil(this.destroyed$)
    ).subscribe(val => {
      this.download = buildDownloadData(this.themeTitle, val.values);
      this.cdr.markForCheck();
    });
  }

  ngOnChanges( changes: SimpleChanges ): void {
    if (changes.options) {
      this.data = Object.entries((this.options || {})).reduce(( result, [key, value] ) => ({
        ...result,
        [key]: setValue(value, this.values || [], key)
      }), {});
    }
    if (changes.name || changes.form || changes.options) {
      this.themeInputs$.next({
        name: this.name,
        form: this.form,
        options: this.options
      });
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  onImportFileChange( event: any ) {
    const reader = new FileReader();
    reader.onload = () => {
      const fileData = reader.result as string;
      try {
        const values = Object.entries(JSON.parse(fileData)).map(( [key, value] ) => ({ key, value }));
        this.data = Object.entries((this.options || {})).reduce(( result, [key, value] ) => ({
          ...result,
          [key]: setValue(value, values, key)
        }), {});
        this.cdr.detectChanges();
        if (values.length === 0) {
          this.notifications.success(this.translate.instant('LOBBY.THEMES.jsonEmpty'));
        } else {
          this.notifications.success(this.translate.instant('LOBBY.THEMES.jsonLoaded'));
        }
      } catch (error) {
        if (error instanceof SyntaxError) {
          error.message = this.translate.instant('LOBBY.THEMES.syntaxJsonError');
        }
        this.notifications.error(error.message);
      }
    };
    reader.onloadend = () => {
      if (this.fileInputRef) {
        const fileInput = this.fileInputRef.nativeElement as HTMLInputElement;
        fileInput.value = '';
      }
    };

    if (event.target.files && event.target.files.length) {
      const [file] = event.target.files;
      if (file.type === 'application/json') {
        reader.readAsText(file);
      }
    }
  }
}

import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { Theme, Themes } from '../../theme.model';

@Component({
    selector: '[templates-list]',
    templateUrl: './templates-list.component.html',
    styleUrls: ['templates-list.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class TemplatesListComponent {

  @Input('templates-list') list: Themes;
  @Output() onActivate = new EventEmitter<any>();

  private _active: { key: string } = { key: 'playtech' };
  get active(): { key: string } {
    return this._active;
  }

  @Input()
  set active( value: { key: string } ) {
    this._active = value;
  }

  isActive( theme: any ): boolean {
    return this._active.key === theme.key;
  }

  get themes(): Theme[] {
    return Object.keys(this.list).map(key => ({ key, ...this.list[key] }));
  }

  public activate( theme: any ): void {
    this._active = theme;
    this.onActivate.emit(theme);
  }
}

import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSidenavModule } from '@angular/material/sidenav';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiSidebarModule } from '@skywind-group/lib-swui';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { LiveChatModule } from '../common/components/livechat/live-chat.module';
import { BaThemeSpinner } from '../common/services/baThemeSpinner/baThemeSpinner.service';
import { CalendarService } from '../common/services/calendar.service';
import { CsvService } from '../common/services/csv.service';
import { MenuSidebarService } from '../common/services/menu-sidebar.service';

import { CountryService } from '../common/services/country.service';
import { CurrencyService } from '../common/services/currency.service';
import { GameProviderService } from '../common/services/game-provider.service';
import { LanguagesService } from '../common/services/languages.service';
import { BriefResolver } from '../common/services/resolvers/brief.resolver';
import { CurrenciesResolver } from '../common/services/resolvers/currencies.resolver';
import { ShortStructureResolver } from '../common/services/resolvers/short-structure.resolver';
import { PagesComponent } from './pages.component';
import { PagesRoutingModule } from './pages.routing';

export const pagesMatModules = [
  MatSidenavModule,
];

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    BsDropdownModule.forRoot(),
    TabsModule.forRoot(),
    PagesRoutingModule,
    TranslateModule,
    LiveChatModule,
    SwuiSidebarModule,
    ...pagesMatModules,
  ],
  declarations: [
    PagesComponent,
  ],
  providers: [
    BriefResolver,
    CalendarService,
    MenuSidebarService,
    CsvService,
    ShortStructureResolver,
    CurrencyService,
    CurrenciesResolver,
    CountryService,
    LanguagesService,
    GameProviderService
  ]
})
export class PagesModule {
  constructor(private readonly _spinner: BaThemeSpinner) {
    this._spinner.show();
  }
}

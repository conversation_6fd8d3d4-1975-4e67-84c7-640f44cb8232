import { Component, ViewChild } from '@angular/core';
import { SwuiGridComponent, SwuiGridDataService, SwuiGridField } from '@skywind-group/lib-swui';

import { DepositInfo } from '../../../../common/typings';
import { DepositsService } from './deposits.service';
import { SCHEMA_LIST } from './schema';

@Component({
    selector: 'deposits-list',
    templateUrl: './deposits.component.html',
    providers: [
        DepositsService,
        { provide: SwuiGridDataService, useExisting: DepositsService },
    ],
    standalone: false
})
export class DepositsComponent {

  @ViewChild('grid', { static: true }) public grid: SwuiGridComponent<DepositInfo>;

  public schema: SwuiGridField[] = SCHEMA_LIST;

  constructor() {
  }
}

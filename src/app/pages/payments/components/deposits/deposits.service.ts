import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import moment from 'moment';
import { PaymentsService } from '../../../../common/services/payments.service';

import { DepositInfo } from '../../../../common/typings';

@Injectable()
export class DepositsService extends PaymentsService<DepositInfo> {
  constructor(
    readonly route: ActivatedRoute,
    readonly http: HttpClient,
  ) {
    super(route, http, 'transfer_in');
  }

  public processRecord( record: DepositInfo ): DepositInfo {
    record._meta = {
      startDate: record.startDate && moment.utc(record.startDate),
      endDate: record.endDate && moment.utc(record.endDate),
    };
    record.agentDomain = record.agentDomain || '';
    record.operator = 'DEMO';

    return record;
  }
}

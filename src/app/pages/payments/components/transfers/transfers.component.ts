import { Component, ViewChild } from '@angular/core';
import { SwHubEntityService, SwuiGridComponent, SwuiGridDataService, SwuiTopFilterDataService } from '@skywind-group/lib-swui';
import { Subject, throwError } from 'rxjs';
import { catchError, map, takeUntil } from 'rxjs/operators';
import { CsvService } from '../../../../common/services/csv.service';
import { EntityDataSourceService } from '../../../../common/services/entity-data-source.service';
import { TransfersService } from '../../../../common/services/transfers.service';
import { Payment } from '../../../../common/typings';
import { TRANSFERS_SCHEMA } from './schema';


const COMPONENT_NAME = 'transfers-list';

@Component({
    selector: 'transfers-list',
    templateUrl: './transfers.component.html',
    providers: [
        CsvService,
        TransfersService,
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: TransfersService }
    ],
    standalone: false
})
export class TransfersComponent {
  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<Payment>;

  readonly componentName = COMPONENT_NAME;
  readonly schemaFilter = TRANSFERS_SCHEMA.filter(el => el.isFilterable);
  readonly schema = TRANSFERS_SCHEMA.filter(el => el.isList);

  loading: boolean = false;
  isEntity = false;

  private destroyed$ = new Subject<void>();

  constructor( private readonly entityDataSourceService: EntityDataSourceService,
               private readonly service: TransfersService,
               hubEntityService: SwHubEntityService,
  ) {
    hubEntityService.items$.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( data ) => {
      this.schema.find(( item ) => item.field === 'playerCode').td.data = data;
    });

    hubEntityService.entitySelected$.pipe(
      map(entity => entity?.type === 'entity'),
      takeUntil(this.destroyed$),
    ).subscribe(isEntity => {
      this.isEntity = isEntity;
    });
  }

  ngOnInit(): void {
    this.entityDataSourceService.show();
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
    this.entityDataSourceService.hide();
  }

  downloadCsv() {
    this.loading = true;
    this.service.downloadCsv()
      .pipe(
        catchError(( err ) => {
          this.loading = false;
          return throwError(err);
        }),
        takeUntil(this.destroyed$)
      ).subscribe(() => this.loading = false);
  }

  exportPage() {
    this.service.exportPage(this.grid.dataSource.data, this.grid.displayedColumns, this.grid.paginator.pageIndex + 1);
  }
}

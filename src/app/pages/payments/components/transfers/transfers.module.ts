import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';
import { DownloadCsvModule } from '../../../../common/components/download-csv/download-csv.module';
import { HintsModule } from '../../../../common/components/hints/hints.module';
import { TransfersService } from '../../../../common/services/transfers.service';
import { TransfersComponent } from './transfers.component';


@NgModule({
  imports: [
    CommonModule,
    SwuiPagePanelModule,
    SwuiGridModule,
    SwuiNotificationsModule.forRoot(),
    TranslateModule,
    LayoutModule,
    MatTooltipModule,
    SwuiSchemaTopFilterModule,
    DownloadCsvModule,
    HintsModule
  ],
  exports: [],
  declarations: [
    TransfersComponent,
  ],
  providers: [
    TransfersService,
  ]
})
export class TransfersModule {
}

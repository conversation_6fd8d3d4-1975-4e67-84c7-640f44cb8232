import { Component, ViewChild } from '@angular/core';
import { SwuiGridComponent, SwuiGridDataService } from '@skywind-group/lib-swui';

import { DepositInfo } from '../../../../common/typings';
import { SCHEMA_LIST } from './schema';
import { WithdrawalsService } from './withdrawals.service';

@Component({
    selector: 'withdrawals-list',
    templateUrl: './withdrawals.html',
    providers: [
        WithdrawalsService,
        { provide: SwuiGridDataService, useExisting: WithdrawalsService },
    ],
    standalone: false
})
export class WithdrawalsComponent {

  @ViewChild('grid', { static: true }) public grid: SwuiGridComponent<DepositInfo>;

  public schema = SCHEMA_LIST;
}

import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import moment from 'moment';

import { PaymentsService } from '../../../../common/services/payments.service';
import { WithdrawalInfo } from '../../../../common/typings';

@Injectable()
export class WithdrawalsService extends PaymentsService<WithdrawalInfo> {
  constructor(
    readonly route: ActivatedRoute,
    readonly http: HttpClient,
  ) {
    super(route, http, 'transfer_out');
  }

  public processRecord( record: WithdrawalInfo ): WithdrawalInfo {
    record._meta = {
      orderDate: record.orderDate && moment.utc(record.orderDate),
      startDate: record.startDate && moment.utc(record.startDate),
      endDate: record.endDate && moment.utc(record.endDate),
    };
    record.agentDomain = record.agentDomain || '';
    record.operator = 'DEMO';

    return record;
  }
}

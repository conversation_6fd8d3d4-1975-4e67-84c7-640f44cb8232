import { Component, Inject } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ErrorMessage } from '../../../../../../common/components/mat-user-editor/user-form.component';

import { Player } from '../../../../../../common/typings';
import { multiplyAmount } from '../../../../../../common/core/currecy-transform';

@Component({
    selector: 'customer-balance-dialog',
    styleUrls: ['customer-balance-dialog.component.scss'],
    templateUrl: 'customer-balance-dialog.component.html',
    standalone: false
})

export class CustomerBalanceDialogComponent {
  direction: 'in' | 'out';
  player: Player;
  control: FormControl = new FormControl(0, Validators.min(0.01));
  messageErrors: ErrorMessage = {
    min: 'VALIDATION.min'
  };

  constructor(private dialogRef: MatDialogRef<CustomerBalanceDialogComponent>,
              @Inject(MAT_DIALOG_DATA) public data: { direction: 'in' | 'out', player: Player }) {
    this.direction = data.direction;
    this.player = data.player;

    this.control.setValidators(Validators.required);
  }

  submit() {
    this.control.markAsTouched();
    if (this.control.valid) {
      this.dialogRef.close(multiplyAmount(this.control.value, this.player.currency));
    }
  }

  onNoClick() {
    this.dialogRef.close();
  }
}

import { Component, Input } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, finalize, map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { Player } from 'src/app/common/typings';

import { CustomerData, TransferData } from '../../../../../common/models/customer-balance.model';
import { PlayerService } from '../../../../../common/services/player.service';
import { CustomerBalanceDialogComponent } from './customer-balance-dialog/customer-balance-dialog.component';
import { getCurrencyLabel } from '../../../../../common/core/currecy-transform';


@Component({
    selector: 'customer-balance',
    styleUrls: ['customer-balance.component.scss'],
    templateUrl: 'customer-balance.component.html',
    standalone: false
})

export class CustomerBalanceComponent {
  public direction: 'in' | 'out';
  public loadingPlayer = false;
  public currencyLabel = '';

  private _player: Player;
  private playerData: CustomerData;
  private destroyed$ = new Subject<void>();

  @Input()
  set player( player: Player ) {
    this._player = player;
    this.currencyLabel = getCurrencyLabel(player.currency);
  }

  get player(): Player {
    return this._player;
  }

  constructor( route: ActivatedRoute,
               private readonly playerService: PlayerService,
               private readonly translate: TranslateService,
               private readonly notifications: SwuiNotificationsService,
               private readonly dialog: MatDialog ) {
    this.playerData = {
      path: route.snapshot.params['path'],
      id: route.snapshot.params['id']
    };
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  public openTransferBalanceModal( direction: 'in' | 'out' ) {
    this.loadingPlayer = true;

    this.playerService.getItem(this.playerData, true)
      .pipe(
        finalize(() => this.loadingPlayer = false),
        tap(( player: Player ) => this._player = player),
        takeUntil(this.destroyed$)
      )
      .subscribe(( player: Player ) => {
        direction === 'out' && player.isOnline ?
          this.notifications.error(this.translate.instant('CUSTOMERS.playerIsOnline')) : this.openDialog(direction);
      });
  }

  private openDialog( direction: 'in' | 'out' ) {

    const dialogRef = this.dialog.open(CustomerBalanceDialogComponent, {
      data: {
        direction,
        player: this.player
      },
      width: '300px',
      disableClose: true
    });

    dialogRef.afterClosed()
      .pipe(
        filter(( amount: number ) => !!amount),
        map(( amount: number ) => {
          return {
            path: this.playerData.path,
            player: this._player,
            direction: direction,
            amount: amount.toString()
          };
        }),
        switchMap(( transferData: TransferData ) => this.playerService.transfer(transferData)),
        tap(() => this.notifications.success(this.translate.instant('CUSTOMERS.NOTIFICATIONS.transfer_success'))),
        switchMap(() => this.playerService.getItem(this.playerData, true)),
        takeUntil(this.destroyed$)
      )
      .subscribe();
  }
}

import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiCurrencySymbolModule, SwuiNotificationsModule } from '@skywind-group/lib-swui';
import { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';
import { BaIfAllowedModule } from '../../../../../common/directives/baIfAllowed/baIfAllowed.module';

import { CustomerBalanceDialogComponent } from './customer-balance-dialog/customer-balance-dialog.component';
import { CustomerBalanceComponent } from './customer-balance.component';
import { PipesModule } from '../../../../../common/pipes/pipes.module';


@NgModule({
  declarations: [
    CustomerBalanceComponent,
    CustomerBalanceDialogComponent,
  ],
  exports: [CustomerBalanceComponent],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ControlMessagesModule,
        BaIfAllowedModule,
        TranslateModule,
        SwuiCurrencySymbolModule,
        SwuiNotificationsModule.forRoot(),
        MatButtonModule,
        MatIconModule,
        MatFormFieldModule,
        MatInputModule,
        MatSnackBarModule,
        LayoutModule,
        PipesModule,

    ],
})

export class CustomerBalanceModule {
}

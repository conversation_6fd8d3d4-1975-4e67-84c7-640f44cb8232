import { Component, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { SwuiGridComponent, SwuiGridDataService, SwuiGridField } from '@skywind-group/lib-swui';
import { Subject, throwError } from 'rxjs';
import { catchError, takeUntil } from 'rxjs/operators';
import { GameInfo } from '../../../../../../common/typings';
import { GameHistoryExternalService } from '../../../../../gamehistory/components/game-history-external/game-history-external.service';

import { CustomerExternalGameHistoryService } from './customer-external-game-history.service';
import { SCHEMA_LIST } from './schema';


@Component({
    selector: 'customer-external-game-history',
    templateUrl: './customer-external-game-history.component.html',
    providers: [
        GameHistoryExternalService,
        CustomerExternalGameHistoryService,
        { provide: SwuiGridDataService, useExisting: CustomerExternalGameHistoryService }
    ],
    standalone: false
})

export class CustomerExternalGameHistoryComponent implements OnDestroy {
  readonly schema: SwuiGridField[] = SCHEMA_LIST;
  @ViewChild(SwuiGridComponent) grid: SwuiGridComponent<GameInfo>;
  loading: boolean = false;

  private readonly destroyed$ = new Subject<void>();

  constructor( private readonly service: GameHistoryExternalService,
               private readonly customerExternalGameHistoryService: CustomerExternalGameHistoryService
  ) {
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  downloadCsv() {
    const { path, id } = this.customerExternalGameHistoryService;

    this.loading = true;
    this.service.downloadCsv({ playerCode: id }, path)
      .pipe(
        catchError(( err ) => {
          this.loading = false;
          return throwError(err);
        }),
        takeUntil(this.destroyed$)
      ).subscribe(() => this.loading = false);
  }

  exportPage() {
    this.service.exportPage(this.grid.dataSource.data, this.grid.displayedColumns, this.grid.paginator.pageIndex + 1);
  }
}

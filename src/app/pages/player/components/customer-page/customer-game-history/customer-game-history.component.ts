import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { HISTORY_TYPE_MAP } from '../../../../../app.constants';

import { SelectOptionModel } from '../../../../../common/models/select-option.model';
import { Player } from '../../../../../common/typings';

@Component({
    selector: 'customer-game-history',
    templateUrl: 'customer-game-history.component.html',
    standalone: false
})

export class CustomerGameHistoryComponent implements OnInit, OnDestroy {
  public historyTypes: SelectOptionModel[] = [];
  public activeType = 'general';
  historyTypeControl: FormControl = new FormControl();

  @Input() player: Player;

  private readonly destroyed = new Subject<void>();

  constructor() {
    this.historyTypes = HISTORY_TYPE_MAP;
    this.historyTypeControl.setValue(this.activeType);
  }

  ngOnInit(): void {
    this.historyTypeControl.valueChanges
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(item => this.activeType = item);
  }

  ngOnDestroy(): void {
    this.destroyed.next();
    this.destroyed.complete();
  }
}

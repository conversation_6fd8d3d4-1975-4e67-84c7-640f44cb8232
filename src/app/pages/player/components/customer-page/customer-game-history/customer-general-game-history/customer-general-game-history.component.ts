import { ComponentType } from '@angular/cdk/overlay';
import { <PERSON>mponent, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { RowAction, SwHubAuthService, SwuiGridComponent, SwuiGridDataService, SwuiGridField } from '@skywind-group/lib-swui';
import { combineLatest, of, Subject, throwError } from 'rxjs';
import { catchError, map, take, takeUntil } from 'rxjs/operators';
import { Entity } from '../../../../../../common/models/entity.model';
import { EntityService } from '../../../../../../common/services/entity.service';
import { GameService } from '../../../../../../common/services/game.service';
import { Game } from '../../../../../../common/typings';

import { GameHistory } from '../../../../../../common/typings/reports/game_history';
import { GameHistoryGeneralService } from '../../../../../gamehistory/components/game-history-general/game-history-general.service';
import {
  IframeViewModalComponent
} from '../../../../../gamehistory/components/internal-game-history/modals/iframe-view-modal/iframe-view-modal.component';
import {
  RoundInfoViewModalComponent
} from '../../../../../gamehistory/components/internal-game-history/modals/round-info-view-modal.component';
import { CustomerGeneralGameHistoryService } from './customer-general-game-history.service';

import { SCHEMA_LIST } from './schema';


@Component({
    selector: 'customer-general-game-history',
    templateUrl: './customer-general-game-history.component.html',
    providers: [
        GameHistoryGeneralService,
        CustomerGeneralGameHistoryService,
        { provide: SwuiGridDataService, useExisting: CustomerGeneralGameHistoryService }
    ],
    standalone: false
})
export class CustomerGeneralGameHistoryComponent implements OnInit, OnDestroy {
  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<GameHistory>;
  readonly schema: SwuiGridField[] = SCHEMA_LIST;

  actions: RowAction[] = [];
  loading: boolean = false;

  private readonly destroyed$ = new Subject<void>();

  constructor(
    private readonly gameService: GameService,
    private readonly dialog: MatDialog,
    private readonly route: ActivatedRoute,
    private readonly authService: SwHubAuthService,
    private readonly entityService: EntityService<Entity>,
    private readonly historyGeneralService: GameHistoryGeneralService,
    private readonly customerGeneralGameHistoryService: CustomerGeneralGameHistoryService,
  ) {
    this.gameService.getAllGames(this.customerGeneralGameHistoryService.path, false, true)
      .pipe(
        map(( games: Game[] ) => {
          this.historyGeneralService.games = games.reduce(( acc: Record<string, Game>, cur: Game ) => {
            acc[cur.code] = cur;
            return acc;
          }, {});
        }),
        take(1),
      )
      .subscribe();
  }

  ngOnInit() {
    this.actions.push(
      new RowAction({
        icon: 'history',
        inMenu: false,
        canActivateFn: ( info ) => info?.totalEvents > 0,
        fn: ( info: GameHistory ) => {
          const { path } = this.route.snapshot.params;
          const entityInfo = this.authService.isSuperAdmin ? this.entityService.getItem(path) : of(null);

          combineLatest([
            entityInfo,
            this.gameService.getEntityGame(info.gameCode, path)
          ]).pipe(
            take(1),
          ).subscribe(
            ( [entity, game] ) => {
              let component: ComponentType<IframeViewModalComponent | RoundInfoViewModalComponent> = game.historyRenderType === 3 ?
                IframeViewModalComponent :
                RoundInfoViewModalComponent;
              info._meta = {
                ...info._meta,
                name: entity?.name
              };

              this.dialog.open(component, {
                width: '1000px',
                data: {
                  roundInfo: info
                },
                disableClose: true
              });
            }
          );
        },
      })
    );
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  downloadCsv() {
    this.loading = true;
    this.historyGeneralService
      .downloadCsv({ playerCode: this.customerGeneralGameHistoryService.id }, this.customerGeneralGameHistoryService.path)
      .pipe(
        catchError(( err ) => {
          this.loading = false;
          return throwError(err);
        }),
        takeUntil(this.destroyed$)
      ).subscribe(() => this.loading = false);
  }

  exportPage() {
    this.historyGeneralService.exportPage(this.grid.dataSource.data, this.grid.displayedColumns, this.grid.paginator.pageIndex + 1);
  }
}

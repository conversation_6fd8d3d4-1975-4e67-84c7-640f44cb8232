import { ComponentType } from '@angular/cdk/overlay';
import { Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  RowAction,
  SwHubAuthService, SwHubEntityItem, SwHubEntityService,
  SwuiGridComponent,
  SwuiGridDataService,
  SwuiGridField,
  SwuiNotificationsService
} from '@skywind-group/lib-swui';
import { combineLatest, of, Subject, throwError } from 'rxjs';
import { catchError, filter, finalize, switchMap, take, takeUntil } from 'rxjs/operators';

import { BoConfirmationComponent } from '../../../../../../common/components/bo-confirmation/bo-confirmation.component';
import { Entity } from '../../../../../../common/models/entity.model';
import { EntityService } from '../../../../../../common/services/entity.service';
import { GameService } from '../../../../../../common/services/game.service';
import { GameInfo } from '../../../../../../common/typings';
import { GameHistory } from '../../../../../../common/typings/reports/game_history';
import { GameHistoryBrokenService } from '../../../../../gamehistory/components/game-history-broken/game-history-broken.service';
import {
  IframeViewModalComponent
} from '../../../../../gamehistory/components/internal-game-history/modals/iframe-view-modal/iframe-view-modal.component';
import {
  RoundInfoViewModalComponent
} from '../../../../../gamehistory/components/internal-game-history/modals/round-info-view-modal.component';
import { CustomerUnfinishedGameHistoryService } from './customer-unfinished-game-history.service';


import { SCHEMA_LIST } from './schema';


@Component({
    selector: 'customer-unfinished-game-history',
    templateUrl: './customer-unfinished-game-history.component.html',
    providers: [
        GameHistoryBrokenService,
        CustomerUnfinishedGameHistoryService,
        { provide: SwuiGridDataService, useExisting: CustomerUnfinishedGameHistoryService }
    ],
    standalone: false
})
export class CustomerUnfinishedGameHistoryComponent implements OnInit, OnDestroy {
  @ViewChild(SwuiGridComponent) grid: SwuiGridComponent<GameInfo>;

  readonly schema: SwuiGridField[] = SCHEMA_LIST;

  actions: RowAction[] = [];
  loading: boolean = false;
  idPathMap: SwHubEntityItem[];

  private readonly destroyed$ = new Subject<void>();

  constructor(
    readonly notifications: SwuiNotificationsService,
    readonly translate: TranslateService,
    readonly gameService: GameService,
    readonly route: ActivatedRoute,
    public dialog: MatDialog,
    private entityService: EntityService<Entity>,
    private readonly gameHistoryService: CustomerUnfinishedGameHistoryService,
    private readonly gameHistoryBrokenService: GameHistoryBrokenService,
    private authService: SwHubAuthService,
    hubEntityService: SwHubEntityService
  ) {
    hubEntityService.items$.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( idPathMap ) => {
      this.idPathMap = idPathMap;
    });
  }

  ngOnInit() {
    this.actions.push(
      new RowAction({
        icon: 'history',
        inMenu: false,
        canActivateFn: ( info ) => info?.totalEvents > 0,
        fn: ( info: GameHistory ) => {
          const { path } = this.route.snapshot.params;
          const entityInfo = this.authService.isSuperAdmin ? this.entityService.getItem(path) : of(null);

          combineLatest([
            entityInfo,
            this.gameService.getEntityGame(info.gameCode, path)
          ]).pipe(
            take(1),
          )
            .subscribe(
              ( [entity, game] ) => {
                let component: ComponentType<IframeViewModalComponent | RoundInfoViewModalComponent> = game.historyRenderType === 3 ?
                  IframeViewModalComponent :
                  RoundInfoViewModalComponent;

                info._meta = {
                  ...info._meta,
                  name: entity?.name
                };

                this.dialog.open(component, {
                  width: '1000px',
                  data: {
                    roundInfo: info
                  },
                  disableClose: true
                });
              }
            );
        },
      })
    );
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  statusClick( { row, payload } ): void {
    let message = this.getPopupMessage(row, payload);
    this.dialog.open(BoConfirmationComponent, {
      width: '500px',
      data: { message: message },
      disableClose: true
    }).afterClosed().pipe(
      finalize(payload.onCompleteFn()),
      filter(result => result),
      switchMap(() => {
        return this.gameHistoryService.changeStatus(row, payload)
          .pipe(
            catchError(err => {
              if (err.error.code === 503 && this.authService.isSuperAdmin) {
                return this.dialog.open(BoConfirmationComponent, {
                  width: '500px',
                  data: { message: 'GAMEHISTORY.INTERNAL.ignoreMerchantParams' },
                  disableClose: true
                }).afterClosed()
                  .pipe(
                    filter(data => data),
                    switchMap(() => {
                      return this.gameHistoryService.changeStatus(row, payload, true);
                    }),
                  );
              }
              return throwError(err);
            }),
          );
      }),
      switchMap(data => {
        switch (data['result']) {
          case 'force-finished':
            return this.translate.get([
              'GAMEHISTORY.INTERNAL.NOTIFICATIONS.roundFinished',
              'GAMEHISTORY.INTERNAL.NOTIFICATIONS.noForceFinish',
              'GAMEHISTORY.INTERNAL.NOTIFICATIONS.note'
            ]);
          case 'finalized':
            return this.translate.get(['GAMEHISTORY.INTERNAL.NOTIFICATIONS.roundFinalized']);
          default:
            return this.translate.get(['GAMEHISTORY.INTERNAL.NOTIFICATIONS.roundClosed']);
        }
      }))
      .subscribe(( mes ) => {
        const messages = Object.values(mes)
          .reduce<string>(( res: string, item: string ) => {
            if (res) {
              res = `${res}. ${item}`;
            } else {
              res = item;
            }

            return res;
          }, '');

        this.notifications.success(messages, '');
        this.grid.dataSource.loadData();
      });
  }

  downloadCsv() {
    this.loading = true;
    const { path, id } = this.gameHistoryService;
    this.gameHistoryBrokenService.downloadCsv({ playerCode: id }, path)
      .pipe(
        catchError(( err ) => {
          this.loading = false;
          return throwError(err);
        }),
        takeUntil(this.destroyed$)
      ).subscribe(() => this.loading = false);
  }

  exportPage() {
    this.gameHistoryBrokenService.exportPage(this.grid.dataSource.data, this.grid.displayedColumns, this.grid.paginator.pageIndex + 1);
  }

  private getPopupMessage( row, payload ): string {
    switch (payload.status) {
      case 'forceFinish':
        if (row.status === 'broken') {
          return 'GAMEHISTORY.INTERNAL.messageForceFinishBroken';
        } else {
          return 'GAMEHISTORY.INTERNAL.messageForceFinish';
        }
      case 'revert':
        if (row.status === 'broken') {
          return 'GAMEHISTORY.INTERNAL.messageRevertBroken';
        } else {
          return 'GAMEHISTORY.INTERNAL.messageRevert';
        }
      case 'finalize':
        return 'GAMEHISTORY.INTERNAL.messageFinalize';
      case 'retryPending':
        return 'GAMEHISTORY.INTERNAL.messageRetry';
      case 'requireTransferOut':
        return 'GAMEHISTORY.INTERNAL.messageTransferOut';
      default:
        break;
    }
  }
}

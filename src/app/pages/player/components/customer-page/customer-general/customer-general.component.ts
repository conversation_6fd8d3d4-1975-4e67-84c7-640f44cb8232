import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import moment from 'moment-timezone';
import { of, Subject } from 'rxjs';
import { catchError, distinctUntilChanged, filter, finalize, map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { GameGroup } from '../../../../../common/models/game-group.model';
import { SelectOptionModel } from '../../../../../common/models/select-option.model';
import { CountryService } from '../../../../../common/services/country.service';
import { PlayerService } from '../../../../../common/services/player.service';
import { ValidationService } from '../../../../../common/services/validation.service';

import { Country, Player } from '../../../../../common/typings';


@Component({
  selector: 'customer-general',
  templateUrl: 'customer-general.component.html',
  standalone: false
})
export class CustomerGeneralComponent implements OnInit, OnDestroy {
  @Input() isStorePlayerInfo: boolean;

  public countries: Country[];
  public form: FormGroup;
  public gameGroupForm: FormGroup;
  public submitted: boolean;

  public gameGroups: GameGroup[];
  public gameGroupsSelectOptions: SelectOptionModel[];
  public path: string;
  public isChecked: boolean;
  public minDate = moment();
  isAllowedEdit: boolean;

  private _player: Player;
  private playerCode: string;
  private formData: Player;
  private firstGameGroupValue = '';
  private readonly destroyed = new Subject<void>();

  @Input()
  set player(player: Player) {
    if (!player) return;
    this._player = player;
    this.playerCode = player.code;
    this.populateForm(this._player);
  }

  get player(): Player {
    return this._player;
  }


  get gameGroupControl() {
    return this.gameGroupForm.get('gameGroup') as FormControl;
  }

  constructor(
    private readonly fb: FormBuilder,
    private readonly playerService: PlayerService,
    private readonly authService: SwHubAuthService,
    private readonly countriesService: CountryService<Country>,
    private readonly notificationService: SwuiNotificationsService,
    private readonly translate: TranslateService,
    private readonly route: ActivatedRoute,
  ) {
    const { path } = route.snapshot.params;
    this.path = path;

    this.initForm();
    this.buildGameGroupsSelectOptions();
    this.initGameGroupForm();
  }

  ngOnInit() {
    if (this.player) {
      this.player.status !== 'normal' ? this.gameGroupControl.disable() : this.gameGroupControl.enable();
      this.isChecked = !!this.player.deactivatedAt;
    }

    this.playerService.item.subscribe((player: Player) =>
      player.status !== 'normal' ? this.gameGroupControl.disable() : this.gameGroupControl.enable());

    this.isAllowedEdit = this.authService.allowedTo(['keyentity:player:edit']) && this.isStorePlayerInfo;
    this.isAllowedEdit ? this.form.enable() : this.form.disable();

    this.countriesService.getList()
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(data => {
        this.countries = data;
      });

    this.gameGroupControl.valueChanges.pipe(
      distinctUntilChanged(),
      takeUntil(this.destroyed),
      filter((value) => value !== this.firstGameGroupValue),
      map((gameGroup: string) => gameGroup || null),
      switchMap(value => this.playerService.updatePlayer({ gameGroup: value }, this.playerCode, this.path)
        .pipe(
          catchError(() => {
            this.gameGroupControl.patchValue(this.firstGameGroupValue, { emitEvent: false });
            return of(null);
          })
        )),
      filter((value) => !!value),
      tap(value => this.firstGameGroupValue = value)
    ).subscribe();
  }

  ngOnDestroy() {
    this.destroyed.next();
    this.destroyed.complete();
  }

  public buildGameGroupsSelectOptions() {
    this.gameGroupsSelectOptions = [];
    this.gameGroups = this.route.snapshot.data.gameGroups;

    if (this.gameGroups) {
      this.gameGroups.map((gameGroup: any) => this.gameGroupsSelectOptions.push(
        new GameGroup(gameGroup).toSelectOption())
      );
    }
  }

  public onFormSubmit() {
    this.submitted = true;
    if (this.form.valid) {
      this.formData = Object.assign({}, this.form.value);

      if (this.formData.email === '') {
        this.formData.email = null;
      }

      this.formData.deactivatedAt = this.formData.deactivatedAt ? moment(this.formData.deactivatedAt).format() : null;

      if (!this.isStorePlayerInfo) {
        delete this.formData.email;
        delete this.formData.firstName;
        delete this.formData.lastName;
      }

      this.playerService.saveItem(this.playerCode, this.formData)
        .pipe(
          switchMap(() => this.playerService.getItem({ id: this.player.code }, true)),
          finalize(() => {
            this.submitted = false;
          })
        )
        .subscribe(() => {
          this.notificationService.success(this.translate.instant('CUSTOMERS.NOTIFICATIONS.edit_success', {
            code: this.player.id
          }));
        });
    }
  }

  public getDefaultGameGroupLabel(): string {
    return this.player.defaultGameGroup ?
      this.translate.instant('CUSTOMERS.defaultGameGroupLabel', { defaultGameGroup: this.player.defaultGameGroup }) :
      this.translate.instant('CUSTOMERS.defaultLimitsLabel');
  }

  clearControl(event: MouseEvent, control: FormControl) {
    event.preventDefault();
    event.stopPropagation();
    control.reset();
  }

  private initForm() {
    this.form = this.fb.group({
      firstName: [''],
      lastName: [''],
      email: ['', ValidationService.emailValidator],
      country: [''],
      deactivatedAt: [''],
    });
  }

  private initGameGroupForm() {
    this.gameGroupForm = this.fb.group({
      gameGroup: [''],
    });
  }

  private populateForm(player): void {
    const commonPlayerInfo = { ...player };
    delete commonPlayerInfo['customData'];

    this.form.patchValue(commonPlayerInfo as any);

    if (player.gameGroup) {
      this.gameGroupControl.patchValue(player.gameGroup, { emitEvent: false });
      this.firstGameGroupValue = player.gameGroup;
    }
  }

}

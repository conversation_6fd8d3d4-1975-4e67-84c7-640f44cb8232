import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiDatePickerModule } from '@skywind-group/lib-swui';
import { CalendarModule } from '../../../../../common/components/calendar/calendar.module';
import { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';
import { TrimInputValueModule } from '../../../../../common/directives/trim-input-value/trim-input-value.module';
import { GameGroupService } from '../../../../../common/services/game-group.service';

import { CustomerGeneralComponent } from './customer-general.component';


@NgModule({
  declarations: [
    CustomerGeneralComponent
  ],
  exports: [CustomerGeneralComponent],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ControlMessagesModule,
        TranslateModule,
        CalendarModule,

        MatFormFieldModule,
        MatSelectModule,
        LayoutModule,
        MatInputModule,
        MatButtonModule,
        MatDividerModule,
        MatIconModule,
        MatCardModule,
        SwuiDatePickerModule,
        TrimInputValueModule,
    ],
  providers: [
    GameGroupService,
  ]
})
export class CustomerGeneralModule {

}

import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { PERMISSIONS_NAMES, SettingsService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import moment from 'moment';
import { Observable, Subject } from 'rxjs';
import { filter, switchMap, take, takeUntil, tap } from 'rxjs/operators';
import { PlayerService } from '../../../../../common/services/player.service';
import { ValidationService } from '../../../../../common/services/validation.service';
import { Player } from '../../../../../common/typings';
import { transformCurrencyValue } from '../../../../../common/core/currecy-transform';


@Component({
    selector: 'customer-info-widget',
    styleUrls: ['customer-info-widget.component.scss'],
    templateUrl: 'customer-info-widget.component.html',
    standalone: false
})
export class CustomerInfoWidgetComponent implements OnInit, OnDestroy {
  @Input()
  set player( player: Player | undefined ) {
    if (!player) {
      return;
    }
    this._player = {...player, balance: transformCurrencyValue(player.balance, player.currency)};
    this.nicknameControl.patchValue(player.nickname, { emitEvent: false });
  }

  get player(): Player | undefined {
    return this._player;
  }

  readonly passwordControl: FormControl;
  readonly nicknameControl: FormControl;
  readonly resetAttemptsPermission = [PERMISSIONS_NAMES.PLAYER, PERMISSIONS_NAMES.PLAYER_RESET_CHANGE_NICKNAME];

  dateFormat: string;

  private _player?: Player;
  private readonly path?: string;
  private readonly destroyed$ = new Subject<void>();

  constructor(
    route: ActivatedRoute,
    private readonly playerService: PlayerService,
    private readonly notificationService: SwuiNotificationsService,
    private readonly translate: TranslateService,
    private readonly settingsService: SettingsService,
  ) {
    this.path = route.snapshot.params.path;
    this.passwordControl = new FormControl('', [
      Validators.required,
      ValidationService.passwordConditions(),
    ]);
    this.nicknameControl = new FormControl('', [
      Validators.maxLength(15)
    ]);
  }

  ngOnInit() {
    this.playerService.item.pipe(
      takeUntil(this.destroyed$),
    ).subscribe(player => {
      this.player = player;
    });

    this.settingsService.appSettings$.pipe(takeUntil(this.destroyed$))
      .subscribe(appSettings => {
        this.dateFormat = `${appSettings.dateFormat} ${appSettings.timeFormat}`;
      });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  setPlayerStatus( status, event: MouseEvent ): void {
    event.preventDefault();
    this.playerService.setStatus(this.player, status, this.path)
      .pipe(
        switchMap<Player, Observable<string>>(player => player.status === 'suspended' ?
          this.translate.get('CUSTOMERS.NOTIFICATIONS.status_inactive') :
          this.translate.get('CUSTOMERS.NOTIFICATIONS.status_active')),
        tap<string>(( message ) => this.notificationService.success(message, '')),
        takeUntil(this.destroyed$)
      ).subscribe();
  }

  setPlayerType( isTest, event: MouseEvent ): void {
    event.preventDefault();
    const balance = this._player?.balance;
    this.playerService.saveType(this._player, isTest, this.path)
      .pipe(
        switchMap<Player, Observable<string>>(player => {
          if (!player['balance']) {
            player['balance'] = balance;
          }

          return player.isTest === false ?
            this.translate.get('CUSTOMERS.NOTIFICATIONS.type_real') :
            this.translate.get('CUSTOMERS.NOTIFICATIONS.type_test');
        }),
        tap<string>(( message ) => this.notificationService.success(message, '')),
        takeUntil(this.destroyed$)
      ).subscribe();
  }

  unblockPlayer( event: MouseEvent ): void {
    event.preventDefault();
    this.playerService.unblockPlayer(this.player, this.path).pipe(
      filter(data => !!data),
      tap(( player: Player ) => this.player.isBlocked = player.isBlocked),
      switchMap<Player, Observable<string>>(player =>
        this.translate.get('CUSTOMERS.NOTIFICATIONS.unblock', { code: player.code })),
      tap<string>(( message ) => this.notificationService.success(message, '')),
      takeUntil(this.destroyed$)
    ).subscribe();
  }

  changePassword( event: MouseEvent ): void {
    event.preventDefault();
    if (this.passwordControl.valid) {
      const newPassword = this.passwordControl.value;
      this.playerService.changePassword(this.player.code, { newPassword, confirmPassword: newPassword }, this.path).pipe(
        switchMap(() => this.translate.get('PASSWORD_CHANGE.passwordChangedSuccessfully')),
        tap<string>(message => this.notificationService.success(message, '')),
        takeUntil(this.destroyed$)
      ).subscribe();
    }
  }

  randomNickname( event: MouseEvent ) {
    event.preventDefault();
    const random = () => Math.floor(Math.random() * 9) + 1;
    const nickname = ['Player', random(), random(), random(), random()].join('');
    this.nicknameControl.setValue(nickname);
  }

  changeNickname( event: MouseEvent ) {
    event.preventDefault();
    if (this.nicknameControl.valid) {
      const nickname = this.nicknameControl.value;
      this.playerService.updatePlayer({ nickname: nickname === '' ? null : nickname }, this.player.code, this.path).pipe(
        takeUntil(this.destroyed$)
      ).subscribe();
    }
  }

  resetNicknameChangeAttempts( event: MouseEvent ) {
    if (!this.player.brandId) {
      return;
    }

    event.preventDefault();
    this.playerService.resetNicknameChangeAttempts(this.player.brandId, this.player.code)
      .pipe(
        switchMap(() => this.playerService.getItem({path: this.path, id: this.player.code}, true)),
        take(1)
      ).subscribe(player => {
        this.player = player;
    });
  }

  getDate( date: string ): string {
    if (!date) {
      return '-';
    }
    return moment(date).format(this.dateFormat);
  }
}

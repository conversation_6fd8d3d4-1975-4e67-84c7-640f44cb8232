import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SwBrowserTitleService } from '@skywind-group/lib-swui';
import { Player } from '../../../../common/typings';
import { GlobalState } from '../../../../global.state';
import { ResponsibleGamingStatusResolver } from './responsible-gaming-status-resolver.service';


@Component({
    selector: 'customer-page',
    styleUrls: ['customer-page.component.scss'],
    templateUrl: 'customer-page.component.html',
    providers: [ResponsibleGamingStatusResolver],
    standalone: false
})

export class CustomerPageComponent implements OnInit {

  public player: Player;

  public activeViewState: string;
  public isResponsibleGamingEnabled: boolean;
  public isStorePlayerInfo: boolean = true;

  private _subscriptions: any[] = [];

  constructor( private route: ActivatedRoute,
               private globalState: GlobalState,
               private browserTitleService: SwBrowserTitleService,
  ) {
    const { snapshot: { data: { responsibleGamingStatus: rsp } } } = this.route;
    const { snapshot: { data: { playerInfo: pli } } } = this.route;
    this.player = pli;
    this.isResponsibleGamingEnabled = rsp;

    if (this.route.snapshot.data.brief['settings']) {
      const { snapshot: { data: { brief: { settings: {storePlayerInfo: isPlayerInfo } } } } } = this.route;
      this.isStorePlayerInfo = isPlayerInfo;
    }

    const additionalTitle = this.player ? `Edit player ${this.player.code } - ` : '';
    this.browserTitleService.setupTitles('Casino', additionalTitle);
  }

  ngOnInit() {
    this.globalState.subscribe('customer-page.activeViewState', ( activeViewState ) => {
      this.activeViewState = activeViewState;
    });
  }

  ngOnDestroy() {
    this._subscriptions.forEach(subscription => subscription.unsubscribe());
    this.browserTitleService.setupTitles('Casino', '');
  }

}

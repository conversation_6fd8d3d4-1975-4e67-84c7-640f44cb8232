import { Component, Input } from '@angular/core';
import { SwuiGridDataService, SwuiGridField } from '@skywind-group/lib-swui';

import { CustomerPaymentsDepositsService } from './customer-payments-deposits.service';
import { SCHEMA_LIST } from './schema';


@Component({
    selector: 'sw-customer-payments-deposits',
    templateUrl: './customer-payments-deposits.component.html',
    providers: [
        { provide: SwuiGridDataService, useExisting: CustomerPaymentsDepositsService }
    ],
    standalone: false
})
export class CustomerPaymentsDepositsComponent {

  schema: SwuiGridField[] = SCHEMA_LIST;
  @Input() set path(val: string) {
    this.paymentsService.path = val;
  }
  @Input() set id(val: string) {
    this.paymentsService.id = val;
  }

  constructor( private paymentsService: CustomerPaymentsDepositsService) {
  }
}

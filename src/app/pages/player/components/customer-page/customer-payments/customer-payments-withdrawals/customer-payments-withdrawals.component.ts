import { Component, Input } from '@angular/core';
import { SwuiGridDataService, SwuiGridField } from '@skywind-group/lib-swui';

import { CustomerPaymentsWithdrawalsService } from './customer-payments-withdrawals.service';
import { SCHEMA_LIST } from './schema';


@Component({
    selector: 'sw-customer-payments-withdrawals',
    templateUrl: './customer-payments-withdrawals.component.html',
    providers: [
        { provide: SwuiGridDataService, useExisting: CustomerPaymentsWithdrawalsService }
    ],
    standalone: false
})
export class CustomerPaymentsWithdrawalsComponent {

  schema: SwuiGridField[] = SCHEMA_LIST;
  @Input() set path(val: string) {
    this.paymentsService.path = val;
  }
  @Input() set id(val: string) {
    this.paymentsService.id = val;
  }

  constructor( private paymentsService: CustomerPaymentsWithdrawalsService) {
  }
}

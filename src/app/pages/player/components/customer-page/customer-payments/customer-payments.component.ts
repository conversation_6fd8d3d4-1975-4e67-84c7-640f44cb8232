import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';


@Component({
    selector: 'customer-payments',
    templateUrl: 'customer-payments.component.html',
    standalone: false
})

export class CustomerPaymentsComponent {
  path: string;
  id: string;

  constructor( private route: ActivatedRoute ) {
    const { snapshot: { params: { path: path, id: id } } } = this.route;
    this.path = path;
    this.id = id;
  }
}

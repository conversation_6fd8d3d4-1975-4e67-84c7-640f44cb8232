import { Component, Input } from '@angular/core';
import { Player } from '../../../../../common/typings';


@Component({
    selector: 'customer-report',
    templateUrl: 'customer-report.component.html',
    standalone: false
})
export class CustomerReportComponent {
  @Input() report: string;

  public _player: Player;

  @Input()
  set player( player: Player ) {
    if (!player || !this.report) return;
    this._player = player;
  }
}

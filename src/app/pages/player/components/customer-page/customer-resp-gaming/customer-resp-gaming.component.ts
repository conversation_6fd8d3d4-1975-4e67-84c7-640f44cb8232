import { Component, Input } from '@angular/core';
import { ResponsibleGamingService } from '../../../../../common/services/responsible-gaming.service';

import { Player } from '../../../../../common/typings';
import { ResponsibleGaming } from '../../../../../common/typings/responsible-gaming';


@Component({
    selector: 'customer-resp-gaming',
    styleUrls: ['customer-resp-gaming.component.scss'],
    templateUrl: 'customer-resp-gaming.component.html',
    standalone: false
})

export class CustomerRespGamingComponent {

  private _player: Player;

  @Input()
  set player(player: Player){
    if (!player) return;
    this._player = player;
    this.respGamingService.getItem({ id: player.code });
  }

  get player(): Player {
    return this._player;
  }

  constructor(private respGamingService: ResponsibleGamingService<ResponsibleGaming>) {}

}

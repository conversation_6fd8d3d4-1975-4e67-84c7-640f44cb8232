import { Component, EventEmitter, Input, Output } from '@angular/core';

import { NoticeSettings } from './notice-settings';


@Component({
    selector: 'notice',
    styleUrls: ['notice.component.scss'],
    templateUrl: 'notice.component.html',
    standalone: false
})

export class NoticeComponent {

  @Input() settings: NoticeSettings;
  @Output() buttonClick: EventEmitter<MouseEvent> = new EventEmitter();

  constructor() {}

  onButtonClick($event: MouseEvent) {
    this.buttonClick.emit($event);
  }

}

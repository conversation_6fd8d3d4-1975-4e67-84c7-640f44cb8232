import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil, tap } from 'rxjs/operators';


import { ResponsibleGamingService } from '../../../../../../../common/services/responsible-gaming.service';
import { Player } from '../../../../../../../common/typings';
import { ResponsibleGaming } from '../../../../../../../common/typings/responsible-gaming';
import { NoticeSettings } from '../../notice/notice-settings';
import { TIMEFRAMES, TimeframeStringItem } from '../../timeframe.model';
import { RgDepositLimitDialogComponent } from '../rg-deposit-limit-dialog/rg-deposit-limit-dialog.component';


@Component({
    selector: 'rg-deposit-limit-casino',
    styleUrls: ['../../customer-resp-gaming.component.scss'],
    templateUrl: 'rg-deposit-limit-casino.component.html',
    standalone: false
})

export class RgDepositLimitCasinoComponent implements OnInit, OnDestroy {

  @Input() player: Player;

  public timeframes: TimeframeStringItem[] = TIMEFRAMES;
  public form: FormGroup;
  public noticeSettings: NoticeSettings;
  public isEditAllowed: boolean;
  public isLimitPending: boolean;
  public isLimitSet: boolean;
  public formTimeframeValue: string;
  public formTimeframeTitle: string;
  public submitted: boolean = false;
  public respGaming: ResponsibleGaming;

  private destroyed$ = new Subject<void>();

  constructor( private responsibleGamingService: ResponsibleGamingService<ResponsibleGaming>,
               private fb: FormBuilder,
               private notificationService: SwuiNotificationsService,
               private translate: TranslateService,
               private dialog: MatDialog,
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    this.responsibleGamingService.item
      .pipe(
        filter(item => !!item),
        takeUntil(this.destroyed$)
      )
      .subscribe(( item: ResponsibleGaming ) => {
        this.form.patchValue(item);
        this.respGaming = item;
        this.isEditAllowed = this.responsibleGamingService.isEditEnabled(item.settings.casino.selfExclusionTillDate);
        this.isEditAllowed ? this.form.enable() : this.form.disable();
        this.checkLimitState();
      });

    this.depositLimitTimeframeControl.valueChanges
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe(newLimitTimeframe => {
        this.formTimeframeValue = newLimitTimeframe;
        this.formTimeframeTitle = this.responsibleGamingService.getTitleByValue(this.timeframes, newLimitTimeframe);
        this.responsibleGamingService.checkNewTimeframe(this.depositLimitControl, newLimitTimeframe);
      });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get depositLimitTimeframeControl(): FormControl {
    return this.form.get('settings.casino.depositLimitTimeframe') as FormControl;
  }

  get depositLimitControl(): FormControl {
    return this.form.get('settings.casino.depositLimit') as FormControl;
  }

  public onFormSubmit(): void {
    this.submitted = true;
    if (this.form.valid) {
      const dialogRef = this.dialog.open(RgDepositLimitDialogComponent, {
        width: '600px',
        data: {
          isRestrictive: this.checkRestrictive(),
          formTimeframeTitle: this.formTimeframeTitle,
          currency: this.player.currency,
          depositLimit: this.depositLimitControl.value,
        },
        disableClose: true
      });

      dialogRef.afterClosed()
        .pipe(
          filter(val => !!val),
          tap(() => {
            if (this.formTimeframeValue === '') {
              this.depositLimitControl.setValue(null);
            }
          }),
          switchMap(() => this.responsibleGamingService.saveItem(this.player.code, this.form.get('settings').value)),
          switchMap(() => this.responsibleGamingService.getItem({ id: this.player.code })),
          takeUntil(this.destroyed$)
        )
        .subscribe(() => {
          this.notificationService.success(this.translate.instant('CUSTOMERS.RG.LOSSLIMIT.notification_success'));
        });
    }
  }

  public handleNoticeClick(): void {
    if (this.isLimitPending === true) {
      const pendingData = { casino: { depositLimit: this.respGaming.settings.casino.depositLimit } };
      this.responsibleGamingService.deletePending(this.player.code, pendingData)
        .pipe(
          filter(val => !!val),
          takeUntil(this.destroyed$)
        )
        .subscribe(value => {
          this.respGaming = value;
          this.checkLimitState();
        });
    } else {
      this.isLimitSet = false;
    }
  }

  public checkRestrictive(): boolean {
    const currentLimit = this.respGaming.settings.casino.depositLimit;
    const currentTimeframe = this.respGaming.settings.casino.depositLimitTimeframe;
    const newTimeframe = this.formTimeframeValue;
    const newLimit = this.depositLimitControl.value;

    return this.responsibleGamingService
      .newSettingRequiresCoolingOff(currentLimit, currentTimeframe, newLimit, newTimeframe);
  }

  private initForm(): void {
    this.form = this.fb.group({
      settings: this.fb.group({
        casino: this.fb.group({
          depositLimit: [''],
          depositLimitTimeframe: [''],
        })
      }),
    });
  }

  private checkLimitState(): void {
    const limit = this.respGaming.settings.casino.depositLimit;
    const pendingDate = this.respGaming.settings.casino.depositLimitPendingDate;

    this.isLimitSet = this.responsibleGamingService.isLimitSet(limit);
    this.isLimitPending = this.responsibleGamingService.isPending(pendingDate);

    this.noticeSettings = this.responsibleGamingService.setLimitNotice(limit, pendingDate, this.isEditAllowed);
  }
}

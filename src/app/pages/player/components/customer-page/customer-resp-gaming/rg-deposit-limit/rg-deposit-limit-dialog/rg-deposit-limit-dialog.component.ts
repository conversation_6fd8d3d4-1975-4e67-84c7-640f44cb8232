import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
    selector: 'rg-deposit-limit-dialog',
    templateUrl: 'rg-deposit-limit-dialog.component.html',
    standalone: false
})

export class RgDepositLimitDialogComponent {
  isRestrictive = false;
  currency = '';
  formTimeframeTitle = '';
  depositLimit = '';

  constructor( private dialogRef: MatDialogRef<RgDepositLimitDialogComponent>,
               @Inject(MAT_DIALOG_DATA) public data: {
                 isRestrictive: boolean,
                 currency: string,
                 formTimeframeTitle: string,
                 depositLimit: string,
               } ) {
    this.isRestrictive = data.isRestrictive;
    this.currency = data.currency;
    this.formTimeframeTitle = data.formTimeframeTitle;
    this.depositLimit = data.depositLimit;
  }

  submit() {
    this.dialogRef.close(true);
  }

  onNoClick() {
    this.dialogRef.close();
  }
}

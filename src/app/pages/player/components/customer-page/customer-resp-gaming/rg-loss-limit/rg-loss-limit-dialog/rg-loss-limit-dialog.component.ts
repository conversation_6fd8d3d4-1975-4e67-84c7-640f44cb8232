import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
    selector: 'rg-loss-limit-dialog',
    templateUrl: 'rg-loss-limit-dialog.component.html',
    standalone: false
})

export class RgLossLimitDialogComponent {

  isRestrictive = false;
  currency = '';
  formTimeframeTitle = '';
  lossLimit = '';

  constructor( private dialogRef: MatDialogRef<RgLossLimitDialogComponent>,
               @Inject(MAT_DIALOG_DATA) public data: {
                 isRestrictive: boolean,
                 currency: string,
                 formTimeframeTitle: string,
                 lossLimit: string,
               } ) {
    this.isRestrictive = data.isRestrictive;
    this.currency = data.currency;
    this.formTimeframeTitle = data.formTimeframeTitle;
    this.lossLimit = data.lossLimit;
  }

  submit() {
    this.dialogRef.close(true);
  }

  onNoClick() {
    this.dialogRef.close();
  }
}

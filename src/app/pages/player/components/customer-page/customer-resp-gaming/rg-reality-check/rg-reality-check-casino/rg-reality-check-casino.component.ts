import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil } from 'rxjs/operators';

import { ResponsibleGamingService } from '../../../../../../../common/services/responsible-gaming.service';
import { Player } from '../../../../../../../common/typings';
import { ResponsibleGaming } from '../../../../../../../common/typings/responsible-gaming';
import { REAILTY_CHECK, TimeframeItem } from '../../timeframe.model';
import { RgRealityCheckDialogComponent } from '../rg-reality-check-dialog/rg-reality-check-dialog.component';


@Component({
    selector: 'rg-reality-check-casino',
    styleUrls: ['./../../customer-resp-gaming.component.scss'],
    templateUrl: 'rg-reality-check-casino.component.html',
    standalone: false
})

export class RgRealityCheckCasinoComponent implements OnInit, OnDestroy {
  @Input() player: Player;

  public realityCheckValues: TimeframeItem[] = REAILTY_CHECK;
  public form: FormGroup;
  public isEditAllowed: boolean;
  public realityCheckTitle: string;

  private destroyed$ = new Subject<void>();

  constructor( private responsibleGamingService: ResponsibleGamingService<ResponsibleGaming>,
               private fb: FormBuilder,
               private notificationService: SwuiNotificationsService,
               private translate: TranslateService,
               private dialog: MatDialog ) {
    this.initForm();
  }

  ngOnInit(): void {
    this.form.get('settings.casino.realityCheck').valueChanges
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe(value => {
        this.realityCheckTitle = value !== null ?
          this.responsibleGamingService.getTitleByValue(this.realityCheckValues, value) : 'off';
      });

    this.responsibleGamingService.item
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe(( item: ResponsibleGaming ) => {
        this.isEditAllowed = this.responsibleGamingService.isEditEnabled(item.settings.casino.selfExclusionTillDate);
        this.form.patchValue(item);
        this.isEditAllowed ? this.form.enable() : this.form.disable();
      });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  public onFormSubmit() {
    if (this.form.valid) {
      const dialogRef = this.dialog.open(RgRealityCheckDialogComponent, {
        width: '300px',
        data: {
          title: this.realityCheckTitle,
        },
        disableClose: true
      });

      dialogRef.afterClosed()
        .pipe(
          filter(val => !!val),
          switchMap(() => this.responsibleGamingService.saveItem(this.player.code, this.form.get('settings').value)),
          switchMap(() => this.responsibleGamingService.getItem({ id: this.player.code })),
          takeUntil(this.destroyed$)
        )
        .subscribe(() => {
          this.notificationService.success(this.translate.instant('CUSTOMERS.RG.REALITYCHECK.notification_success'));
        });
    }
  }

  private initForm() {
    this.form = this.fb.group({
      settings: this.fb.group({
        casino: this.fb.group({
          realityCheck: [''],
        })
      }),
    });
  }
}

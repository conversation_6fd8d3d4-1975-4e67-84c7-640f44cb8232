import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
    selector: 'rg-reality-check-dialog',
    templateUrl: 'rg-reality-check-dialog.component.html',
    standalone: false
})

export class RgRealityCheckDialogComponent {
  title = '';

  constructor( private dialogRef: MatDialogRef<RgRealityCheckDialogComponent>,
               @Inject(MAT_DIALOG_DATA) public data: { title: string } ) {
    this.title = data.title;
  }

  submit() {
    this.dialogRef.close(true);
  }

  onNoClick() {
    this.dialogRef.close();
  }
}

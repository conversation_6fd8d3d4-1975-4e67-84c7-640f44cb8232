import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil } from 'rxjs/operators';
import { ResponsibleGamingService } from '../../../../../../../common/services/responsible-gaming.service';

import { Player } from '../../../../../../../common/typings';
import { ResponsibleGaming } from '../../../../../../../common/typings/responsible-gaming';
import { NoticeSettings } from '../../notice/notice-settings';
import { TimeframeItem } from '../../timeframe.model';
import { RgSelfExclusionDialogComponent } from '../rg-self-exclusion-dialog/rg-self-exclusion-dialog.component';


@Component({
    selector: 'rg-self-exclusion-casino',
    styleUrls: ['../../customer-resp-gaming.component.scss'],
    templateUrl: 'rg-self-exclusion-casino.component.html',
    standalone: false
})

export class RgSelfExclusionCasinoComponent implements OnInit, OnDestroy {

  @Input() player: Player;

  public selfExclusionTimes: TimeframeItem[] = [];
  public noticeSettings: NoticeSettings;
  public isEditAllowed: boolean;
  public daysTimeOut: number;
  public daysTimeOutTitle: string;
  public isSetTimeOut: boolean;
  public longDateTime: string;
  selfExclusionControl: FormControl = new FormControl();

  private destroyed$ = new Subject<void>();

  constructor( private responsibleGamingService: ResponsibleGamingService<ResponsibleGaming>,
               private notificationService: SwuiNotificationsService,
               private translate: TranslateService,
               private dialog: MatDialog,
  ) {
    this.selfExclusionTimes = [
      { value: null, title: 'Not set' },
      { value: this.exactDaysTillDateByMonth(6), title: '6 months' },
      { value: this.exactDaysTillDateByYears(1), title: '1 year' },
      { value: this.exactDaysTillDateByYears(3), title: '3 years' },
      { value: this.exactDaysTillDateByYears(5), title: '5 years' },
    ];
    this.noticeSettings = this.responsibleGamingService.setNotice(false);
  }

  ngOnInit(): void {
    this.responsibleGamingService.item
      .pipe(
        takeUntil(this.destroyed$),
        filter(item => !!item),
      )
      .subscribe(( item: ResponsibleGaming ) => {
        const selfExclusion = item.settings.casino.selfExclusionTillDate;
        this.isEditAllowed = this.responsibleGamingService.isEditEnabled(selfExclusion);
        this.longDateTime = this.responsibleGamingService.formatDateDMYHM(selfExclusion);
        this.responsibleGamingService.daysFromDateTillNow(selfExclusion) != null ?
          this.isSetTimeOut = true : this.isSetTimeOut = false;
        if (!this.isSetTimeOut) {
          this.selfExclusionControl.patchValue(null);
        }

        this.isEditAllowed ? this.selfExclusionControl.enable() : this.selfExclusionControl.disable();
      });

    this.selfExclusionControl.valueChanges
      .pipe(
        takeUntil(this.destroyed$),
      )
      .subscribe(newTimeOut => {
        this.daysTimeOut = newTimeOut;
        this.daysTimeOutTitle = this.responsibleGamingService.getTitleByValue(this.selfExclusionTimes, newTimeOut);
      });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  public onFormSubmit() {
    if (this.selfExclusionControl.value !== null) {
      const dialogRef = this.dialog.open(RgSelfExclusionDialogComponent, {
        width: '600px',
        data: {
          daysTimeOutTitle: this.daysTimeOutTitle,
        },
        disableClose: true
      });

      dialogRef.afterClosed()
        .pipe(
          filter(val => !!val),
          switchMap(() => {
            const data = {
              casino: {
                selfExclusionTillDate: this.responsibleGamingService.dateFromNowByDays(this.daysTimeOut)
              }
            };
            return this.responsibleGamingService.saveItem(this.player.code, data);
          }),
          switchMap(() => this.responsibleGamingService.getItem({ id: this.player.code })),
          takeUntil(this.destroyed$)
        )
        .subscribe(() => {
          this.notificationService.success(this.translate.instant('CUSTOMERS.RG.SELFEXCLUSION.notification_success'));
        });
    }
  }

  private exactDaysTillDateByMonth( months: number ): number {
    const date = this.responsibleGamingService.dateFromNowByMonths(months);
    return this.responsibleGamingService.daysFromDateTillNow(date);
  }

  private exactDaysTillDateByYears( years: number ): number {
    const date = this.responsibleGamingService.dateFromNowByYears(years);
    return this.responsibleGamingService.daysFromDateTillNow(date);
  }

}

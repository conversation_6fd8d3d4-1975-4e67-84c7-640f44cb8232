import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
    selector: 'rg-self-exclusion-dialog',
    templateUrl: 'rg-self-exclusion-dialog.component.html',
    standalone: false
})

export class RgSelfExclusionDialogComponent {
  daysTimeOutTitle = '';

  constructor( private dialogRef: MatDialogRef<RgSelfExclusionDialogComponent>,
               @Inject(MAT_DIALOG_DATA) public data: { daysTimeOutTitle: string } ) {
    this.daysTimeOutTitle = data.daysTimeOutTitle;
  }

  submit() {
    this.dialogRef.close(true);
  }

  onNoClick() {
    this.dialogRef.close();
  }
}

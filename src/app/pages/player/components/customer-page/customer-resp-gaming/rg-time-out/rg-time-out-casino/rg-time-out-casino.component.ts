import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil } from 'rxjs/operators';
import { ResponsibleGamingService } from '../../../../../../../common/services/responsible-gaming.service';

import { Player } from '../../../../../../../common/typings';
import { ResponsibleGaming } from '../../../../../../../common/typings/responsible-gaming';
import { NoticeSettings } from '../../notice/notice-settings';
import { TimeframeItem, TIMEOUTS } from '../../timeframe.model';
import { RgTimeOutDialogComponent } from '../rg-time-out-dialog/rg-time-out-dialog.component';


@Component({
    selector: 'rg-time-out-casino',
    styleUrls: ['../../customer-resp-gaming.component.scss'],
    templateUrl: 'rg-time-out-casino.component.html',
    standalone: false
})

export class RgTimeOutCasinoComponent implements OnInit, OnDestroy {

  @Input() player: Player;

  public timeOuts: TimeframeItem[] = TIMEOUTS;
  public noticeSettings: NoticeSettings;
  public isEditAllowed: boolean;
  public timeOutTillDate: string;
  public daysTimeOut: number;
  public daysTimeOutTitle: string;
  public isSetTimeOut: boolean;
  public longDateTime: string;
  timeOutControl: FormControl = new FormControl();

  private destroyed$ = new Subject<void>();

  constructor( private responsibleGamingService: ResponsibleGamingService<ResponsibleGaming>,
               private notificationService: SwuiNotificationsService,
               private translate: TranslateService,
               private dialog: MatDialog ) {
    this.noticeSettings = this.responsibleGamingService.setNotice(false);
  }

  ngOnInit(): void {
    this.responsibleGamingService.item
      .pipe(
        takeUntil(this.destroyed$),
      )
      .subscribe(( item: ResponsibleGaming ) => {
        this.isEditAllowed = this.responsibleGamingService.isEditEnabled(item.settings.casino.selfExclusionTillDate);
        const timeOut = item.settings.casino.casinoTimeoutTillDate;
        this.longDateTime = this.responsibleGamingService.formatDateDMYHM(timeOut);
        this.responsibleGamingService.daysFromDateTillNow(timeOut) != null ?
          this.isSetTimeOut = true : this.isSetTimeOut = false;
        if (!this.isSetTimeOut) {
          this.timeOutControl.patchValue(null);
        }

        this.isEditAllowed ? this.timeOutControl.enable() : this.timeOutControl.disable();
      });

    this.timeOutControl.valueChanges
      .pipe(
        takeUntil(this.destroyed$),
      )
      .subscribe(newTimeOut => {
        this.daysTimeOut = newTimeOut;
        this.daysTimeOutTitle = this.responsibleGamingService.getTitleByValue(this.timeOuts, newTimeOut);
        const timeOut = this.responsibleGamingService.dateFromNowByDays(this.daysTimeOut);
        this.timeOutTillDate = this.responsibleGamingService.formatDateDMY(timeOut);
      });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  public onFormSubmit(): void {
    if (this.timeOutControl.value !== null) {
      const dialogRef = this.dialog.open(RgTimeOutDialogComponent, {
        width: '600px',
        data: {
          longDateTime: this.longDateTime,
          daysTimeOutTitle: this.daysTimeOutTitle,
          timeOutTillDate: this.timeOutTillDate
        },
        disableClose: true
      });

      dialogRef.afterClosed()
        .pipe(
          filter(val => !!val),
          switchMap(() => {
            const data = {
              casino: {
                casinoTimeoutTillDate: this.responsibleGamingService.dateFromNowByDays(this.daysTimeOut)
              }
            };

            return this.responsibleGamingService.saveItem(this.player.code, data);
          }),
          switchMap(() => this.responsibleGamingService.getItem({ id: this.player.code })),
          takeUntil(this.destroyed$)
        )
        .subscribe(() => {
          this.notificationService.success(this.translate.instant('CUSTOMERS.RG.TIMEOUT.notification_success'));
        });
    }
  }

}

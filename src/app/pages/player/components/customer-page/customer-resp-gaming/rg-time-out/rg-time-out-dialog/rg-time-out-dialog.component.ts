import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
    selector: 'rg-time-out-dialog',
    templateUrl: 'rg-time-out-dialog.component.html',
    standalone: false
})

export class RgTimeOutDialogComponent {
  longDateTime = '';
  daysTimeOutTitle = '';
  timeOutTillDate = '';

  constructor( private dialogRef: MatDialogRef<RgTimeOutDialogComponent>,
               @Inject(MAT_DIALOG_DATA) public data: {
                 longDateTime: string,
                 daysTimeOutTitle: string,
                 timeOutTillDate: string
               } ) {
    this.longDateTime = data.longDateTime;
    this.daysTimeOutTitle = data.daysTimeOutTitle;
    this.timeOutTillDate = data.timeOutTillDate;
  }

  submit() {
    this.dialogRef.close(true);
  }

  onNoClick() {
    this.dialogRef.close();
  }
}

import { Component } from '@angular/core';
import { SwuiSidebarService } from '@skywind-group/lib-swui';
import { GlobalState } from '../../../../../global.state';


@Component({
    selector: 'customer-view-control',
    styleUrls: ['customer-view-control.component.scss'],
    templateUrl: 'customer-view-control.component.html',
    standalone: false
})

export class CustomerViewControlComponent {

  public activeViewState: string;
  public viewStates: any[] = [
    {
      value: 'normal',
      title: 'Normal',
      iconClass: 'icon-sw-view-normal'
    },
    {
      value: 'expanded',
      title: 'Expanded',
      iconClass: 'icon-sw-view-expanded'
    },
    {
      value: 'fullscreen',
      title: 'Full screen',
      iconClass: 'icon-sw-view-full'
    },
  ];

  constructor( private globalState: GlobalState,
               private sidebarService: SwuiSidebarService ) {
    this.activeViewState = globalState.getDataEvent('customer-page.activeViewState') || 'normal';
  }

  onViewControlClick(value){
    this.activeViewState = value;
    this.sidebarService.isCollapsed.next(this.activeViewState === 'fullscreen');
    this.globalState.notifyDataChanged('customer-page.activeViewState', this.activeViewState);
  }
}

import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SwHubEntityService, SwHubShortEntity } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { finalize, map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { promoTypes } from '../../../../../common/models/promotion.model';
import { SimplePromoFreebet } from '../../../../../common/models/simple-promo.model';
import { SimplePromoService } from '../../../../../common/services/simple-promo.service';
import { Player } from '../../../../../common/typings';

interface ApplySimpleFreebetDialogData {
  rows: Player[];
}

@Component({
    selector: 'apply-simple-freebet-dialog',
    templateUrl: 'apply-simple-freebet-dialog.component.html',
    standalone: false
})
export class ApplySimpleFreebetDialogComponent implements OnInit {
  rows: Player[] = [];
  options: any;
  form: FormGroup;

  loading: { [key: string]: boolean } = {
    promoList: false,
    applyPromotion: false,
  };
  results: { code: string; result: string }[];
  promotions: SimplePromoFreebet[];

  private readonly destroyed$ = new Subject<void>();

  constructor(
    public dialogRef: MatDialogRef<ApplySimpleFreebetDialogComponent>,
    @Inject(MAT_DIALOG_DATA) data: ApplySimpleFreebetDialogData,
    private service: SimplePromoService<SimplePromoFreebet>,
    private fb: FormBuilder,
    readonly hubEntityService: SwHubEntityService
  ) {
    this.rows = data.rows;
    this.initForm();
  }

  ngOnInit() {
    this.service.items.pipe(takeUntil(this.destroyed$)).subscribe(
      promotions => this.promotions = <SimplePromoFreebet[]>promotions
    );

    this.hubEntityService.entitySelected$.pipe(
      tap(() => this.loading['promoList'] = true),
      switchMap(( entity: SwHubShortEntity ) => this.service.getList({
        values: {
          type__in: [promoTypes.freebetSimple, promoTypes.freebet]
        }
      }, entity.path)),
      takeUntil(this.destroyed$)
    ).subscribe(() => this.loading['promoList'] = false);
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  initForm() {
    this.form = this.fb.group({
      selectedPromotion: ['', Validators.required]
    });
  }

  applyPromotion() {
    const playerCodes = this.rows.map(item => item.code);
    const { selectedPromotion: promoId } = this.form.value;
    this.loading['applyPromotion'] = true;
    this.results = null;

    this.hubEntityService.entitySelected$.pipe(
      switchMap(( entity: SwHubShortEntity ) =>
        this.service.applyPromotionByFilter(promoId, { values: { code__in: playerCodes } }, entity.path)),
      map(data => Object.keys(data).map(k => ({ code: k, result: data[k] }))),
      finalize(() => this.loading['applyPromotion'] = false),
      takeUntil(this.destroyed$)
    ).subscribe(results => this.results = results);
  }

  isPromoActive( promotion: SimplePromoFreebet ) {
    return promotion.status !== 'inactive' && promotion.state !== 'finished' && promotion.state !== 'expired';
  }
}

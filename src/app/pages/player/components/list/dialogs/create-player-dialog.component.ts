import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Player } from '../../../../../common/typings';


interface CreatePlayerDialogData {
  player: Player;
  prefix?: string;
}

@Component({
    selector: 'create-player-dialog',
    templateUrl: 'create-player-dialog.component.html',
    standalone: false
})

export class CreatePlayerDialogComponent implements OnInit {

  player: Player;
  prefix?: string;

  constructor(
    public dialogRef: MatDialogRef<CreatePlayerDialogComponent, Player>,
    @Inject(MAT_DIALOG_DATA) data: CreatePlayerDialogData,
  ) {
    this.player = data.player;
    this.prefix = data.prefix;
  }

  ngOnInit() {
  }

  submitDialog( player: Player ) {
    this.dialogRef.close(player);
  }
}

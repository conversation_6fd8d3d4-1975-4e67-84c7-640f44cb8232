import { Component, ViewChild, ViewEncapsulation } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  ActionModel,
  BulkAction,
  PanelAction,
  SwHubAuthService,
  SwHubEntityService,
  SwuiGridComponent,
  SwuiGridDataService,
  SwuiGridField,
  SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import { Subject, throwError } from 'rxjs';
import { catchError, filter, mergeMap, take, takeUntil } from 'rxjs/operators';

import { PERMISSIONS_NAMES } from '../../../../app.constants';
import { Entity as EntityModel } from '../../../../common/models/entity.model';
import { CsvService } from '../../../../common/services/csv.service';
import { EntityDataSourceService } from '../../../../common/services/entity-data-source.service';
import { PlayerService } from '../../../../common/services/player.service';

import { Player } from '../../../../common/typings';
import { ServerConfig } from '../../../../common/typings/server-config';

import { SCHEMA_FILTER, SCHEMA_LIST } from '../../schema';
import { ApplySimpleFreebetDialogComponent } from './dialogs/apply-simple-freebet-dialog.component';
import { CreatePlayerDialogComponent } from './dialogs/create-player-dialog.component';

const COMPONENT_NAME: string = 'player-list';

@Component({
    selector: COMPONENT_NAME,
    encapsulation: ViewEncapsulation.None,
    templateUrl: './player-list.html',
    styleUrls: ['./player-list.scss'],
    providers: [
        CsvService,
        PlayerService,
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: PlayerService },
    ],
    standalone: false
})
export class PlayerListComponent {

  public items: Player[] = [];
  public schema: SwuiGridField[] = [];
  public filterSchema: SwuiGridField[] = SCHEMA_FILTER;
  isMaster: boolean = false;
  loading: boolean = false;
  public componentName = COMPONENT_NAME;

  @ViewChild('grid', { static: true }) public playersGrid: SwuiGridComponent<Player>;

  public bulkActions: BulkAction[] = [];
  public panelActions: PanelAction[] = [];

  readonly config: ServerConfig;
  private readonly playerPrefix: string;
  private readonly isBrand: boolean;

  private destroyed$ = new Subject<void>();

  constructor( private readonly service: PlayerService,
               private readonly translate: TranslateService,
               { snapshot: { data: { brief, config } } }: ActivatedRoute,
               private readonly authService: SwHubAuthService,
               private readonly dialog: MatDialog,
               private readonly entityDataSourceService: EntityDataSourceService,
               private readonly hubEntityService: SwHubEntityService,
  ) {
    const briefEntity = new EntityModel(brief);
    this.isBrand = briefEntity.isOperator() || briefEntity.isMerchant;
    this.config = config;
    this.playerPrefix = briefEntity.settings.playerPrefix;

    this.setBulkActions();

    hubEntityService.items$.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( idPathMap ) => {
      const schema = SCHEMA_LIST.slice();
      schema.find(( item ) => item.field === 'code').td.data = idPathMap;
      this.schema = schema;
    });
  }

  ngOnInit() {
    this.entityDataSourceService.show();
    this.setPanelActions();
    this.hubEntityService.entitySelected$.pipe(takeUntil(this.destroyed$)).subscribe(entity => {
      this.isMaster = entity.path === ':' && this.authService.isSuperAdmin;
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
    this.entityDataSourceService.hide();
  }

  changePlayerStatus( isNormal: boolean ): void {
    let ids = this.playersGrid.selection.selected.map(r => r.id);
    let status = isNormal ? 'normal' : 'suspended';

    this.service.setStatuses(ids, status)
      .pipe(
        take(1),
      )
      .subscribe(
        () => {
          this.playersGrid.refreshData();
          this.playersGrid.selection.clear();
        },
      );
  }

  public onCreateItemAction() {
    this.dialog.open(CreatePlayerDialogComponent, {
      width: '600px',
      data: {
        player: {},
        prefix: this.playerPrefix
      },
      disableClose: true,
    }).afterClosed().pipe(
      filter(data => !!data),
      mergeMap(( player ) => this.service.register(player)),
      take(1),
    ).subscribe(() => this.playersGrid.dataSource.loadData());
  }

  downloadCsv( withBalance?: boolean ) {
    this.loading = true;
    const limit = withBalance ? this.config.csvMaxPlayersWithBalance : this.config.csvMaxPlayersWithoutBalance;
    this.service.downloadCsv(limit, withBalance).pipe(
      catchError(( err ) => {
        this.loading = false;
        return throwError(err);
      }),
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      this.loading = false;
    });
  }

  exportPage() {
    this.service.exportPage(this.playersGrid.dataSource.data, this.playersGrid.displayedColumns, this.playersGrid.paginator.pageIndex + 1);
  }

  private setBulkActions() {
    this.translate.get('CUSTOMERS.setActive').subscribe(
      ( title ) => {
        const setActive = new ActionModel({
          title: title,
          fn: this.changePlayerStatus.bind(this, true),
        });
        this.bulkActions.push(setActive);
      }
    );

    this.translate.get('CUSTOMERS.setInactive').subscribe(
      ( title ) => {
        const setActive = new ActionModel({
          title: title,
          fn: this.changePlayerStatus.bind(this, false),
        });
        this.bulkActions.push(setActive);
      }
    );

    if (this.authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_PLAYER_PROMOTION])) {
      this.setPromoActions();
    }
  }

  private setPromoActions() {
    this.translate.get('PROMO.CUSTOMER_BULK_ACTIONS.applyFreeBets').subscribe(
      ( title ) => {
        const freeBetAction = new ActionModel({
          title,
          dialog: {
            componentRef: ApplySimpleFreebetDialogComponent,
            config: {
              width: '600px',
            }
          }
        });
        this.bulkActions.push(freeBetAction);
      }
    );
  }

  private setPanelActions() {
    this.panelActions.push({
      title: 'CUSTOMERS.GRID.createPlayer',
      color: 'primary',
      icon: 'person_add',
      actionFn: () => this.onCreateItemAction(),
      availableFn: () => this.isBrand && this.authService.allowedTo(['keyentity:player:create']),
    });
  }
}

import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { forkJoin } from 'rxjs';
import { CountryService } from '../../../../common/services/country.service';
import { CurrencyService } from '../../../../common/services/currency.service';
import { LanguagesService } from '../../../../common/services/languages.service';
import { ValidationService } from '../../../../common/services/validation.service';
import { Country, Currency, Language, Player } from '../../../../common/typings';
import moment from 'moment';

@Component({
    selector: '[player-form]',
    templateUrl: './player-form.component.html',
    styleUrls: [
        './player-form.component.scss',
    ],
    standalone: false
})
export class PlayerFormComponent implements OnInit {

  @Output() onFormSubmitted: EventEmitter<Player> = new EventEmitter();

  public playerForm: FormGroup;
  public currencies: { id: string, text: string }[];
  public countries: { id: string, text: string }[];
  public languages: { id: string, text: string }[];
  public submitted: boolean = false;
  public minCalendarDate?: string;

  @Input('player-form')
  set player( value: any ) {
    if (!value) return;
    this.playerForm.patchValue(value);
  }

  @Input() prefix: string;


  constructor( private fb: FormBuilder,
               private currenciesService: CurrencyService<Currency>,
               private countriesService: CountryService<Country>,
               private languagesService: LanguagesService<Language>,
  ) {
    this.playerForm = this.fb.group({
      code: [
        '', Validators.compose([
          Validators.required,
          ValidationService.customerIdValidator
        ])
      ],
      firstName: [''],
      lastName: [''],
      email: ['', ValidationService.emailValidator],
      password: [
        '', Validators.compose([
          Validators.required,
          ValidationService.passwordValidator
        ])
      ],
      country: [''],
      language: [''],
      currency: [''],
      status: ['normal'],
      deactivatedAt: [''],
    });

    this.minCalendarDate = moment().toISOString();
  }

  ngOnInit() {
    this.updateFormDictionaries('')
      .subscribe(( [countriesData, currenciesData, languagesData] ) => {
          this.countries = countriesData.map(( { code: id, displayName: text } ) => ({ id, text }));
          this.currencies = currenciesData.map(( { code: id, displayName: text } ) => ({ id, text }));
          this.languages = languagesData.map(( { code: id, name: text } ) => ({ id, text }));
        }
      );

  }

  public onFormSubmitFn() {
    this.submitted = true;
    if (this.playerForm.valid) {
      const player: Player = Object.assign({}, this.playerForm.getRawValue());
      if (player.email === '') {
        player.email = null;
      }
      player.deactivatedAt = player?.deactivatedAt !== '' ? player?.deactivatedAt : null;
      this.onFormSubmitted.emit(player);
    }
  }


  private updateFormDictionaries( path?: string ) {
    return forkJoin([
      this.countriesService.getList({}, path),
      this.currenciesService.getList({}, path),
      this.languagesService.getList({}, path)
    ]);
  }

}

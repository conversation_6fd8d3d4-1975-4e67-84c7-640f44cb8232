import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiDatePickerModule } from '@skywind-group/lib-swui';

import { ControlMessagesModule } from '../../../../common/components/control-messages/control-messages.module';
import { CountryService } from '../../../../common/services/country.service';
import { CurrencyService } from '../../../../common/services/currency.service';
import { LanguagesService } from '../../../../common/services/languages.service';
import { ValidationService } from '../../../../common/services/validation.service';
import { PlayerFormComponent } from './player-form.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ControlMessagesModule,
    TranslateModule,
    MatFormFieldModule,
    MatInputModule,
    LayoutModule,
    MatSelectModule,
    SwuiDatePickerModule,
    MatIconModule,
    MatButtonModule,
  ],
  exports: [PlayerFormComponent],
  declarations: [PlayerFormComponent],
  providers: [
    CurrencyService,
    CountryService,
    LanguagesService,
    ValidationService,
  ],
})

export class MatPlayerFormModule {
}

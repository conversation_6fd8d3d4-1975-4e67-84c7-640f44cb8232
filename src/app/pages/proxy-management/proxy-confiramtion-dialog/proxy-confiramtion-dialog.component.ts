import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

import { Proxy } from '../../../common/models/proxy.model';
import { ProxyItemDialogComponent } from '../proxy-item-dialog/proxy-item-dialog.component';

@Component({
    selector: 'proxy-confiramtion-dialog',
    templateUrl: './proxy-confiramtion-dialog.component.html',
    standalone: false
})
export class ProxyConfiramtionDialogComponent {

  constructor( private dialogRef: MatDialogRef<ProxyItemDialogComponent>,
               @Inject(MAT_DIALOG_DATA) public proxy: Proxy ) {
  }

  onNoClick() {
    this.dialogRef.close(false);
  }

  onConfirmClick() {
    this.dialogRef.close(true);
  }
}

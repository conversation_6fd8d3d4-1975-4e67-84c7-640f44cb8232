import { Component, Inject, OnInit } from '@angular/core';
import { <PERSON><PERSON>uilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Proxy } from '../../../common/models/proxy.model';

@Component({
    selector: 'proxy-item-dialog',
    templateUrl: 'proxy-item-dialog.component.html',
    standalone: false
})
export class ProxyItemDialogComponent implements OnInit {
  form: FormGroup;

  constructor( private dialogRef: MatDialogRef<ProxyItemDialogComponent>,
               private fb: FormBuilder,
               @Inject(MAT_DIALOG_DATA) public proxy: Proxy ) {

    this.form = this.fb.group({
      url: ['', Validators.required],
      description: ['', Validators.required]
    });

  }

  ngOnInit() {
    if (this.proxy) {
      this.form.patchValue(this.proxy);
    }
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  get urlControl(): FormControl {
    return this.form.get('url') as FormControl;
  }

  get descriptionControl(): FormControl {
    return this.form.get('description') as FormControl;
  }

  submit() {
    this.form.markAllAsTouched();
    if (this.form.valid) {
      this.dialogRef.close(this.form.value);
    }
  }
}

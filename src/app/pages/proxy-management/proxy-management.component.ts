import { Component, Inject, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import {
  PanelAction, SwuiTopFilterDataService, SwuiGridComponent, SwuiGridDataService, SwuiGridField, SwuiNotificationsService
} from '@skywind-group/lib-swui';
import { RowAction } from '@skywind-group/lib-swui/swui-schema-grid/row-actions/row-actions.component';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil } from 'rxjs/operators';

import { Proxy } from '../../common/models/proxy.model';
import { ProxyConfiramtionDialogComponent } from './proxy-confiramtion-dialog/proxy-confiramtion-dialog.component';
import { ProxyItemDialogComponent } from './proxy-item-dialog/proxy-item-dialog.component';
import { ProxyManagementService } from './proxy-management.service';
import { SCHEMA_LIST } from './schema';

@Component({
    selector: 'proxy-management',
    templateUrl: './proxy-management.component.html',
    styleUrls: ['./proxy-management.component.scss'],
    providers: [
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useClass: ProxyManagementService }
    ],
    standalone: false
})
export class ProxyManagementComponent implements OnInit, OnDestroy {
  schema: SwuiGridField[] = SCHEMA_LIST;
  panelActions: PanelAction[] = [];
  rowActions: RowAction[] = [];

  @ViewChild('grid', { static: true }) public grid: SwuiGridComponent<Proxy>;

  private destroyed$ = new Subject<void>();

  constructor( private dialog: MatDialog,
               private notifications: SwuiNotificationsService,
               private translate: TranslateService,
               @Inject(SwuiGridDataService) public service: ProxyManagementService ) {
  }

  ngOnInit() {
    this.setPanelActions();
    this.setRowActions();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  private setPanelActions() {
    this.panelActions.push({
      title: 'PROXY.addProxy',
      icon: 'add',
      color: 'primary',
      actionFn: () => {
        const dialogRef = this.dialog.open(ProxyItemDialogComponent, {
          width: '500px',
          data: {},
          disableClose: true
        });

        dialogRef.afterClosed()
          .pipe(
            filter(data => !!data),
            switchMap(( proxy: Proxy ) => this.service.create(proxy)),
            switchMap(( proxy: Proxy ) => this.translate.get('PROXY.notificationCreated', { proxy: proxy.url })),
            takeUntil(this.destroyed$)
          )
          .subscribe(message => {
            this.notifications.success(message);
            this.grid.dataSource.loadData();
          });

      },
    });
  }

  private setRowActions() {
    this.rowActions = [
      {
        title: 'PROXY.editProxy',
        icon: 'edit',
        fn: ( item: Proxy ) => {
          const dialogRef = this.dialog.open(ProxyItemDialogComponent, {
            width: '500px',
            data: item,
            disableClose: true
          });

          dialogRef.afterClosed()
            .pipe(
              filter(data => !!data),
              switchMap(( proxy: Proxy ) => this.service.update(item.id, proxy)),
              switchMap(( proxy: Proxy ) => this.translate.get('PROXY.notificationModified', { proxy: proxy.url })),
              takeUntil(this.destroyed$)
            )
            .subscribe(message => {
              this.notifications.success(message);
              this.grid.dataSource.loadData();
            });
        },
        canActivateFn: () => true,
      },
      {
        title: 'PROXY.removeProxy',
        icon: 'delete',
        fn: ( item: Proxy ) => {
          const dialogRef = this.dialog.open(ProxyConfiramtionDialogComponent, {
            width: '500px',
            data: item,
            disableClose: true
          });

          dialogRef.afterClosed()
            .pipe(
              filter(data => !!data),
              switchMap(() => this.service.delete(item.id)),
              switchMap(() => this.translate.get('PROXY.notificationRemoved', { proxy: item.url })),
              takeUntil(this.destroyed$)
            )
            .subscribe(message => {
              this.notifications.success(message);
              this.grid.dataSource.loadData();
            });
        },
        canActivateFn: () => true,
      },
    ];
  }


}

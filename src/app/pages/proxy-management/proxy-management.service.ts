import { HttpClient, HttpErrorResponse, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridDataService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import moment from 'moment';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { API_ENDPOINT, ZERO_TIME } from '../../app.constants';
import { Proxy } from '../../common/models/proxy.model';


const URL_PROXY = `${API_ENDPOINT}/proxy/`;
const DATE_PARAMS: string[] = ['createdAt', 'updatedAt'];

@Injectable()
export class ProxyManagementService implements GridDataService<Proxy> {

  constructor( private http: HttpClient,
               private notifications: SwuiNotificationsService
  ) {
  }

  getGridData( params: HttpParams ): Observable<HttpResponse<Proxy[]>> {
    return this.http.get<Proxy[]>(URL_PROXY, {
      params,
      observe: 'response'
    })
      .pipe(
        map(response => {
          response.body.map(( proxy: Proxy ) => this.processRecord(proxy));
          response.body.sort(( a: Proxy, b: Proxy ) => a.createdAt > b.createdAt ? -1 : 1);
          return response;
        }),
        catchError(( error: HttpErrorResponse ) => this.handleErrors(error))
      );
  }

  delete( id: string ): Observable<Object> {
    const url = `${URL_PROXY}${id}`;
    return this.http.delete(url)
      .pipe(
        catchError(( error: HttpErrorResponse ) => this.handleErrors(error))
      );
  }

  create( data: Proxy ): Observable<Proxy> {
    if ('_meta' in data) delete data['_meta'];

    let body = JSON.stringify(data);
    const url = `${URL_PROXY}`;
    return this.http.post<Proxy>(url, body)
      .pipe(
        map(response => this.processRecord(response)),
        catchError(( error: HttpErrorResponse ) => this.handleErrors(error))
      );
  }

  update( id: string, data: Proxy ): Observable<Proxy> {
    if ('_meta' in data) delete data['_meta'];

    let body = JSON.stringify(data);
    const url = `${URL_PROXY}${id}`;

    return this.http.patch<Proxy>(url, body)
      .pipe(
        map(response => this.processRecord(response)),
        catchError(( error: HttpErrorResponse ) => this.handleErrors(error))
      );
  }

  private processRecord( record: Proxy ): Proxy {
    if (!record) return record;

    DATE_PARAMS.forEach(property => {
      if (record[property] === ZERO_TIME) {
        record[property] = null;
      }
    });

    record._meta = {
      createdAt: record.createdAt && moment(record.createdAt),
      updatedAt: record.updatedAt && moment(record.updatedAt),
    };

    return record;
  }

  private handleErrors( httpErrorResponse ): Observable<never> {
    const errorBody = httpErrorResponse.error;
    if (errorBody) {
      this.notifications.error(errorBody.message);
    } else {
      this.notifications.error(httpErrorResponse.statusText, `Status: ${httpErrorResponse.status}`);
    }

    return throwError(httpErrorResponse);
  }

}

import { Component, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { UserService } from '../../../../common/services/user.service';
import { User } from '../../../../common/typings';


@Component({
    selector: 'report-audit-log',
    encapsulation: ViewEncapsulation.None,
    templateUrl: './audit-log.html',
    standalone: false
})
export class ReportAuditLogComponent {
  query: string = '';


  private itemsSubscription;


  constructor( public router: Router,
               public notifications: SwuiNotificationsService,
               protected service: UserService<User>
  ) {
    // TODO: GridFilter to be here
    // this.service.getList(0, 100);
    this.service.getList();
  }


  ngOnInit() {
    /* this.itemsSubscription = this.service.items.subscribe(
      ( data ) => this.gridOptions.api.setRowData(data),
      this.processError
    ); */
  }

  ngOnDestroy() {
    this.itemsSubscription.unsubscribe();
  }


}

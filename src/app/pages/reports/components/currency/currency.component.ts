import { Component, ViewChild } from '@angular/core';
import { Params } from '@angular/router';
import {
  SwHubAuthService,
  SwHubEntityService, SwuiGridComponent, SwuiGridDataService, SwuiGridField, SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import moment from 'moment';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CsvService } from '../../../../common/services/csv.service';
import { EntityDataSourceService } from '../../../../common/services/entity-data-source.service';
import { ReportCurrencyService } from '../../../../common/services/reports/currency.service';
import { CurrencyReportItem } from '../../../../common/typings';
import { CurrencyReportSchema } from './schema';

const COMPONENT_NAME: string = 'report-currency';
const REQUIRED_FILTERS = ['ts__gte', 'ts__lt'];

@Component({
    selector: COMPONENT_NAME,
    templateUrl: './currency.component.html',
    providers: [
        CsvService,
        ReportCurrencyService,
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: ReportCurrencyService }
    ],
    standalone: false
})
export class ReportCurrencyComponent {
  isEntity: boolean = true;
  readonly componentName = COMPONENT_NAME;
  readonly schema: SwuiGridField[];
  readonly schemaFilter: SwuiGridField[];

  notifyVisibility: boolean;

  initialFilterState = {
    ts__gte: moment().startOf('month').set({minute: 0, second: 0, millisecond: 0}),
    ts__lt: moment().add(1, 'day').startOf('day')
  };

  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<CurrencyReportItem>;

  private readonly destroyed$ = new Subject<void>();

  constructor( private readonly service: ReportCurrencyService,
               private readonly entityDataSourceService: EntityDataSourceService,
               readonly hubEntityService: SwHubEntityService,
               readonly swHubAuthService: SwHubAuthService,
               private readonly filterDataService: SwuiTopFilterDataService,
  ) {
    const schema = new CurrencyReportSchema(swHubAuthService);
    this.schema = schema.schemaList;
    this.schemaFilter = schema.schemaFilter;
  }

  ngOnInit() {
    this.entityDataSourceService.show();
    this.hubEntityService.entitySelected$.pipe(
      takeUntil(this.destroyed$),
    ).subscribe(( entity ) => {
      this.isEntity = entity.type === 'entity';
    });

    this.filterDataService.appliedFilter
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe(params => {
        this.updateOnParamsChange(params);
      });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
    this.entityDataSourceService.hide();
  }

  printPage() {
    window.print();
  }

  exportPage() {
    this.service.exportPage(this.grid.dataSource.data, this.grid.displayedColumns, (this.grid.paginator?.pageIndex + 1) || 1);
  }

  private updateOnParamsChange( params: Params ) {
    this.notifyVisibility = !REQUIRED_FILTERS.every(field => !!params[field]);

    if (this.notifyVisibility) {
      this.service.blockData();
    } else {
      this.service.unblockData();
    }
  }
}

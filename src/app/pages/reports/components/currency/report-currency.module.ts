import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';
import { HintsModule } from '../../../../common/components/hints/hints.module';
import { GameNotifyModule } from '../../../gamehistory/components/game-notify/game-notify.module';
import { ReportCurrencyComponent } from './currency.component';

@NgModule({
  imports: [
    CommonModule,
    SwuiGridModule,
    SwuiPagePanelModule,
    SwuiNotificationsModule.forRoot(),
    TranslateModule,
    LayoutModule,
    MatTooltipModule,
    SwuiSchemaTopFilterModule,
    HintsModule,
    GameNotifyModule,
  ],
  declarations: [
    ReportCurrencyComponent
  ]
})
export class ReportCurrencyModule {
}

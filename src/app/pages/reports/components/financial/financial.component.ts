import { Component, ViewChild } from '@angular/core';
import { SwuiGridComponent, SwuiGridDataService, SwuiGridField, SwuiTopFilterDataService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { CsvService } from '../../../../common/services/csv.service';
import { EntityDataSourceService } from '../../../../common/services/entity-data-source.service';
import { ReportFinancialService } from '../../../../common/services/reports/financial.service';
import { CurrencyReportItem } from '../../../../common/typings';

import { SCHEMA_FILTER, SCHEMA_LIST } from './schema';


const COMPONENT_NAME: string = 'report-financial';

@Component({
    selector: COMPONENT_NAME,
    templateUrl: './financial.component.html',
    providers: [
        CsvService,
        ReportFinancialService,
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: ReportFinancialService }
    ],
    standalone: false
})
export class ReportFinancialComponent {
  readonly componentName = COMPONENT_NAME;
  readonly schema: SwuiGridField[] = SCHEMA_LIST;
  readonly schemaFilter: SwuiGridField[] = SCHEMA_FILTER;

  @ViewChild(SwuiGridComponent, {static: true}) grid: SwuiGridComponent<CurrencyReportItem>;

  private readonly destroyed$ = new Subject<void>();

  constructor( private readonly service: ReportFinancialService,
               private readonly entityDataSourceService: EntityDataSourceService,
  ) {
  }

  ngOnInit() {
    this.entityDataSourceService.show();
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
    this.entityDataSourceService.hide();
  }

  printPage() {
    window.print();
  }

  exportPage() {
    this.service.exportPage(this.grid.dataSource.data, this.grid.displayedColumns, this.grid.paginator.pageIndex + 1);
  }
}

import { Component } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { BiService } from '../../../../common/services/bi.service';

@Component({
    selector: 'kpi-report',
    styles: [],
    templateUrl: './kpi.html',
    standalone: false
})
export class ReportKpiComponent {
  isServiceAvailable = true;
  location = null;

  constructor( protected biService: BiService,
               protected sanitizer: DomSanitizer
  ) {

    biService.getIframeUrl('kpi')
      .then(( data ) => {
        this.location = this.sanitizer.bypassSecurityTrustResourceUrl(data['location']);
      })
      .catch(( ) => {
        this.isServiceAvailable = false;
      });
  }

}

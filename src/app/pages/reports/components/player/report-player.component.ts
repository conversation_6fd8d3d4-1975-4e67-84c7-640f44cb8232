import { Component, ViewChild } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import {
  PERMISSIONS_NAMES,
  SchemaTopFilterField,
  SwHubAuthService,
  SwHubEntityService,
  SwuiGridComponent,
  SwuiGridDataService,
  SwuiGridField,
  SwuiSelectOption,
  SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import moment from 'moment';
import { Subject, throwError } from 'rxjs';
import { catchError, takeUntil } from 'rxjs/operators';
import { CsvService } from '../../../../common/services/csv.service';
import { EntityDataSourceService } from '../../../../common/services/entity-data-source.service';
import { ReportPlayerService } from '../../../../common/services/reports/report-player.service';
import { PlayerReportItem } from '../../../../common/typings';
import { PlayersReportSchema } from './schema';

const COMPONENT_NAME: string = 'report-player';
const REQUIRED_FILTERS = ['paymentDateHour__gte', 'paymentDateHour__lt'];

@Component({
    selector: COMPONENT_NAME,
    templateUrl: './report-player.component.html',
    providers: [
        CsvService,
        ReportPlayerService,
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: ReportPlayerService }
    ],
    standalone: false
})
export class ReportPlayerComponent {
  loading: boolean = false;
  isEntity: boolean = true;
  schema: SwuiGridField[] = [];
  schemaFilter: SchemaTopFilterField[] = [];
  notifyVisibility: boolean;
  initialFilterState = {
    paymentDateHour__gte: moment().startOf('month').set({ minute: 0, second: 0, millisecond: 0 }),
    paymentDateHour__lt: moment().add(1, 'day').startOf('day')
  };
  readonly componentName = COMPONENT_NAME;

  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<PlayerReportItem>;

  private readonly destroyed$ = new Subject<void>();

  constructor( { snapshot: { data: { gamesShortInfo } } }: ActivatedRoute,
               private readonly service: ReportPlayerService,
               private readonly authService: SwHubAuthService,
               private readonly entityDataSourceService: EntityDataSourceService,
               private readonly hubEntityService: SwHubEntityService,
               private readonly filterDataService: SwuiTopFilterDataService,
  ) {
    this.removeDebitsCreditsColumnsIfNotAllowed();

    const { schemaFilter, schemaList } = new PlayersReportSchema(authService);
    this.schema = schemaList;
    this.schemaFilter = schemaFilter.map(( item ) => {
      if (item.field === 'gameCode' && item.type === 'select') {
        item.data = ((gamesShortInfo as any[]) || []).map<SwuiSelectOption>(( { id, text } ) => ({ id, text: `${text} (${id})` }));
      }
      return item;
    });
  }

  ngOnInit() {
    this.entityDataSourceService.show();
    this.hubEntityService.entitySelected$.pipe(
      takeUntil(this.destroyed$),
    ).subscribe(( entity ) => {
      this.isEntity = entity.type === 'entity';
    });
    this.filterDataService.appliedFilter
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe(params => {
        this.updateOnParamsChange(params);
      });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
    this.entityDataSourceService.hide();
  }

  downloadCsv() {
    this.loading = true;
    this.service.downloadCsv()
      .pipe(
        catchError(( err ) => {
          this.loading = false;
          return throwError(err);
        }),
        takeUntil(this.destroyed$)
      ).subscribe(() => this.loading = false);
  }

  printPage() {
    window.print();
  }

  exportPage() {
    this.service.exportPage(this.grid.dataSource.data, this.grid.displayedColumns, this.grid.paginator.pageIndex + 1);
  }

  private removeDebitsCreditsColumnsIfNotAllowed() {
    if (!this.authService.allowedTo([
      PERMISSIONS_NAMES.KEYENTITY_BI_REPORT_PLAYER_SHOW_HIDE_COLUMN_DEBITS_CREDITS,
      PERMISSIONS_NAMES.BI_REPORT_PLAYER_SHOW_HIDE_COLUMN_DEBITS_CREDITS,
    ])) {
      ['debits', 'credits'].forEach(( fieldName ) => {
        const idx = this.schema.findIndex(( { field } ) => field === fieldName);
        if (idx > -1) {
          this.schema.splice(idx, 1);
        }
      });
    }
  }

  private updateOnParamsChange( params: Params ) {
    this.notifyVisibility = !REQUIRED_FILTERS.every(field => !!params[field]);

    if (this.notifyVisibility) {
      this.service.blockData();
    } else {
      this.service.unblockData();
    }
  }
}

import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';
import { DownloadCsvModule } from '../../../../common/components/download-csv/download-csv.module';
import { HintsModule } from '../../../../common/components/hints/hints.module';
import { ReportPlayerComponent } from './report-player.component';
import { GameNotifyModule } from '../../../gamehistory/components/game-notify/game-notify.module';

@NgModule({
    imports: [
        CommonModule,
        SwuiGridModule,
        SwuiPagePanelModule,
        SwuiNotificationsModule.forRoot(),
        TranslateModule,
        LayoutModule,
        MatTooltipModule,
        SwuiSchemaTopFilterModule,
        DownloadCsvModule,
        HintsModule,
        GameNotifyModule
    ],
  declarations: [
    ReportPlayerComponent
  ],
})
export class ReportPlayerModule {
}

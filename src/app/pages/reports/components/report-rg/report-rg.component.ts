import { Component, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SwuiGridComponent, SwuiGridDataService, SwuiTopFilterDataService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { map } from 'rxjs/operators';

import { Entity as EntityModel } from '../../../../common/models/entity.model';
import { CsvService } from '../../../../common/services/csv.service';
import { entitiesStructureToSelectOptions, EntityService } from '../../../../common/services/entity.service';
import { ReportRgService } from '../../../../common/services/reports/report-rg.service';
import { Entity } from '../../../../common/typings';
import { RgReportItem } from '../../../../common/typings/reports/responsible-gaming';
import { SCHEMA_FILTER, SCHEMA_LIST } from './schema';


const COMPONENT_NAME: string = 'report-rg';

@Component({
    selector: COMPONENT_NAME,
    templateUrl: './report-rg.component.html',
    styleUrls: ['./report-rg.component.scss'],
    providers: [
        CsvService,
        ReportRgService,
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: ReportRgService }
    ],
    standalone: false
})

export class ReportRgComponent {

  readonly componentName = COMPONENT_NAME;
  readonly schema = SCHEMA_LIST;
  readonly schemaFilter = SCHEMA_FILTER;

  @ViewChild(SwuiGridComponent) grid: SwuiGridComponent<RgReportItem>;

  private readonly destroyed$ = new Subject<void>();

  constructor({ snapshot: { data: { brief } } }: ActivatedRoute,
    private readonly entityService: EntityService<Entity>,
    private readonly service: ReportRgService,
  ) {
    if (brief && brief.type === EntityModel.TYPE_ENTITY) {
      this.schemaFilter = SCHEMA_FILTER.map(item => {
        if (item.field === 'path' && item.type === 'select') {
          item.data = this.entityService.getShortStructure().pipe(
            map(structure => entitiesStructureToSelectOptions(structure, 0, [], false))
          );
        }
        return item;
      });
    } else {
      this.schemaFilter = SCHEMA_FILTER.filter(({ field }) => field !== 'path');
    }
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  refreshGrid() {
    this.grid.dataSource.loadData();
  }

  printPage() {
    window.print();
  }

  exportPage() {
    this.service.exportPage(this.grid.dataSource.data, this.grid.displayedColumns, this.grid.paginator.pageIndex + 1);
  }
}

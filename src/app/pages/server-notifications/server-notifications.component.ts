import { Component } from '@angular/core';
import { ServerNotification } from '../../common/typings';
import { ServerNotificationService } from '../../common/services/server-notification.service';

@Component({
    selector: 'server-notifications-page',
    styles: [],
    templateUrl: './server-notifications.html',
    standalone: false
})
export class ServerNotificationsComponent {

  public messages: Array<any>;

  constructor( private service: ServerNotificationService<ServerNotification> ) {
    // TODO: GridFilter to be here
    // this.service.getList(0, 100);
    this.service.getList();

    this.service.items.subscribe(data => {
      this.messages = data;
    });
  }

}

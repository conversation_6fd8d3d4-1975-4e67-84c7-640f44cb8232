import { Component, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import {
  RowAction,
  SelectInputOptionData,
  SwuiGridComponent,
  SwuiGridDataService,
  SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import { Subject, throwError } from 'rxjs';
import { catchError, map, takeUntil } from 'rxjs/operators';
import { CsvService } from '../../../../common/services/csv.service';
import { EntityDataSourceService } from '../../../../common/services/entity-data-source.service';

import { Audit } from '../../../../common/typings';
import { ActivityLogService } from './activityLog.service';
import { ViewHistoryComponent } from './modals/view-history.component';
import { SCHEMA } from './schema';


const COMPONENT_NAME: string = 'activity-log';

@Component({
    selector: COMPONENT_NAME,
    templateUrl: './activityLog.component.html',
    providers: [
        CsvService,
        ActivityLogService,
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: ActivityLogService }
    ],
    standalone: false
})
export class ActivityLogComponent {
  readonly componentName = COMPONENT_NAME;
  readonly schema = SCHEMA.filter(( { isList } ) => isList);
  readonly schemaFilter = SCHEMA.filter(( { isFilterable } ) => isFilterable);
  readonly rowActions: RowAction[];

  loading: boolean = false;

  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<Audit>;
  private readonly destroyed$ = new Subject<void>();

  constructor( private readonly dialog: MatDialog,
               private readonly service: ActivityLogService,
               private readonly entityDataSourceService: EntityDataSourceService,
  ) {
    this.rowActions = this.setRowActions();
    const includeActivity = this.schemaFilter.find(item => item.field === 'auditsSummaryId') as any as SelectInputOptionData;
    const excludedActivity = this.schemaFilter.find(item => item.field === 'auditsSummaryId__in!') as any as SelectInputOptionData;
    includeActivity.data = excludedActivity.data = this.service.getAuditsSummaryList().pipe(
      map(items => items.map(( { id, summary } ) => ({ id, text: summary })))
    );
  }

  ngOnInit() {
    this.entityDataSourceService.show();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
    this.entityDataSourceService.hide();
  }

  downloadCsv() {
    this.loading = true;
    this.service.downloadCsv()
      .pipe(
        catchError(( err ) => {
          this.loading = false;
          return throwError(err);
        }),
        takeUntil(this.destroyed$)
      ).subscribe(() => this.loading = false);
  }

  printPage() {
    window.print();
  }

  exportPage() {
    this.service.exportPage(this.grid.dataSource.data, this.grid.displayedColumns, this.grid.paginator.pageIndex + 1);
  }

  private setRowActions(): RowAction[] {
    return [
      new RowAction({
        icon: 'remove_red_eye',
        title: 'ACTIVITY_LOG.GRID.showHistoryTitle',
        inMenu: false,
        fn: ( item: Audit ) => {
          this.dialog.open(ViewHistoryComponent, {
            width: '500px',
            data: item,
            disableClose: true
          });
        }
      }),
    ];
  }
}

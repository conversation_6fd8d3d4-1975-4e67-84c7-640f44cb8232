import { LayoutModule } from '@angular/cdk/layout';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';
import { DownloadCsvModule } from '../../../../common/components/download-csv/download-csv.module';
import { ActivityLogComponent } from './activityLog.component';
import { ViewHistoryModule } from './modals/view-history.module';

@NgModule({
  declarations: [
    ActivityLogComponent,
  ],
  exports: [
    ActivityLogComponent
  ],
  imports: [
    CommonModule,
    LayoutModule,
    MatIconModule,
    MatButtonModule,
    SwuiGridModule,
    TranslateModule,
    MatDialogModule,
    ViewHistoryModule,
    MatTooltipModule,
    SwuiSchemaTopFilterModule,
    SwuiPagePanelModule,
    DownloadCsvModule
  ],
  providers: []
})
export class ActivityLogModule {
}

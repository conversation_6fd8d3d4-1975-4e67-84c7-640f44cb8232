import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridRequestData, SettingsService, SwuiGridDataService } from '@skywind-group/lib-swui';
import moment from 'moment';
import { Observable } from 'rxjs';
import { share, tap } from 'rxjs/operators';

import { API_ENDPOINT, FORMAT_DATETIME } from '../../../../app.constants';
import { CsvSchema, CsvService, overrideString, transformDate } from '../../../../common/services/csv.service';
import { Audit } from '../../../../common/typings';

const parser = require('ua-parser-js');

const csvSchema = ( timezoneName: string, format: string ): CsvSchema[] => [
  {
    name: 'ts',
    title: 'ACTIVITY_LOG.CSV.date-time',
    transform: transformDate(timezoneName, format),
  },
  {
    name: 'auditsSummary',
    title: 'ACTIVITY_LOG.GRID.eventName',
    transform( value: string ) {
      return overrideString<Audit['auditsSummary']>(value)?.eventName ?? '';
    }
  },
  {
    name: 'auditsSummary',
    title: 'ACTIVITY_LOG.GRID.activity',
    transform( value: string ) {
      return overrideString<Audit['auditsSummary']>(value)?.summary ?? '';
    }
  },
  {
    name: 'auditsSummary',
    title: 'ACTIVITY_LOG.GRID.path',
    transform( value: string ) {
      return overrideString<Audit['auditsSummary']>(value)?.path ?? '';
    }
  },
  {
    name: 'history',
    title: 'ACTIVITY_LOG.GRID.operation',
    transform( value: string ) {
      return overrideString<Audit['history']>(value)?.operation ?? '';
    }
  },
  {
    name: 'auditsSummary',
    title: 'ACTIVITY_LOG.GRID.method',
    transform( value: string ) {
      return overrideString<Audit['auditsSummary']>(value)?.method ?? '';
    }
  },
  {
    name: 'history',
    title: 'ACTIVITY_LOG.GRID.statusCode',
    transform( value: string ) {
      return overrideString<Audit['history']>(value)?.statusCode ?? '';
    }
  },
  {
    name: 'history',
    title: 'ACTIVITY_LOG.CSV.history',
    transform( value: string ) {
      const data = overrideString<Audit['history']>(value);
      const result = {
        result: data?.result ?? '',
        parameters: data?.parameters ?? ''
      };
      return `"${JSON.stringify(result)
        .replace(new RegExp('"', 'g'), '\'')
        .replace(new RegExp('},\'', 'g'), '},\n \'')}"`;
    }
  },
  {
    name: 'initiatorType',
    title: 'ACTIVITY_LOG.CSV.initiatorType',
  },
  {
    name: 'initiatorName',
    title: 'ACTIVITY_LOG.CSV.initiatorName',
  },
  {
    name: 'initiatorServiceName',
    title: 'ACTIVITY_LOG.CSV.initiatorServiceName',
  },
  {
    name: 'ip',
    title: 'ACTIVITY_LOG.CSV.ip',
  },
  {
    name: 'userAgent',
    title: 'ACTIVITY_LOG.CSV.userAgent',
    transform( data: string ) {
      const ua = parser(data);
      const { os: { name, version } } = ua;
      return `${name || ''} ${version || ''}`;
    }
  },
];

const currentPageSchema = ( timezoneName: string, format: string ): CsvSchema[] => [
  {
    name: 'ts',
    title: 'ACTIVITY_LOG.CSV.date-time',
    transform: transformDate(timezoneName, format),
  },
  {
    name: 'eventName',
    title: 'ACTIVITY_LOG.CSV.eventName',
  },
  {
    name: 'summary',
    title: 'ACTIVITY_LOG.GRID.activity',
  },
  {
    name: 'path',
    title: 'ACTIVITY_LOG.CSV.path',
  },
  {
    name: 'operation',
    title: 'ACTIVITY_LOG.GRID.operation',
  },
  {
    name: 'method',
    title: 'ACTIVITY_LOG.CSV.method',
  },
  {
    name: 'statusCode',
    title: 'ACTIVITY_LOG.GRID.statusCode',
    transform( _: string, row ): string {
      return row.history?.statusCode ?? '';
    }
  },
  {
    name: 'initiatorType',
    title: 'ACTIVITY_LOG.CSV.initiatorType',
  },
  {
    name: 'initiatorName',
    title: 'ACTIVITY_LOG.CSV.login',
  },
  {
    name: 'initiatorServiceName',
    title: 'ACTIVITY_LOG.CSV.initiatorServiceName',
  },
  {
    name: 'ip',
    title: 'ACTIVITY_LOG.CSV.ip',
  },
  {
    name: 'system',
    title: 'ACTIVITY_LOG.CSV.system',
  },
];

function getUrl( path?: string, urlList: string = '/audits' ): string {
  return `${API_ENDPOINT}${path ? '/entities/' + path : ''}${urlList}`;
}

@Injectable()
export class ActivityLogService implements SwuiGridDataService<Audit> {

  private readonly urlAuditsSummaryList: string = '/audits-summary';

  constructor(
    private readonly http: HttpClient,
    private readonly csvService: CsvService,
    private readonly setting: SettingsService
  ) {
  }

  getGridData( params: HttpParams, requestData?: GridRequestData ): Observable<HttpResponse<Audit[]>> {
    const path = requestData?.path || '';
    return this.http.get<Audit[]>(getUrl(path), {
      params,
      observe: 'response'
    }).pipe(
      tap(response => {
        response.body.forEach(item => ActivityLogService.processRecord(item));
      }),
      share(),
    );
  }

  getAuditsSummaryList(): Observable<any[]> {
    const url = getUrl(null, this.urlAuditsSummaryList);
    const params = new HttpParams({
      fromObject: {
        limit: 10000,
        offset: 0
      }
    });
    return this.http.get<any[]>(url, { params }).pipe(share());
  }

  public downloadCsv(): Observable<any> {
    const { appSettings: { dateFormat, timeFormat, timezoneName } } = this.setting;
    const datetimeFormat = dateFormat && timeFormat ? `${dateFormat} ${timeFormat}` : FORMAT_DATETIME;
    const fileName = `Export Activity log ${moment().format('YYYY-MM-DD HH:MM')}`;
    return this.csvService.download(getUrl, csvSchema(timezoneName, datetimeFormat), fileName);
  }

  public exportPage( data: Record<string, any>[], columns: string[], page: number ) {
    const { appSettings: { dateFormat, timeFormat, timezoneName } } = this.setting;
    const datetimeFormat = dateFormat && timeFormat ? `${dateFormat} ${timeFormat}` : FORMAT_DATETIME;
    const fileName = `Export Activity log ${moment().format('YYYY-MM-DD HH:MM')} (page ${page})`;
    this.csvService.exportToCsv(currentPageSchema(timezoneName, datetimeFormat), data, fileName, columns);
  }

  private static processRecord( record: any ) {
    record._meta = {};

    const ua = parser(record.userAgent);
    const { os: { name, version } } = ua;

    record.system = `${name || ''} ${version || ''}`;

    if (record.auditsSummary) {
      record.eventName = record.auditsSummary.eventName;
      record.summary = record.auditsSummary.summary;
      record.path = record.auditsSummary.path;
      record.method = record.auditsSummary.method;
    }

    if (record.history) {
      record.operation = record.history.operation;
    }

    record._meta.ts = moment(record.ts);

    return record;
  }
}

import { Component, Inject } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';

import { Audit } from '../../../../../common/typings';


@Component({
    selector: 'view-history',
    templateUrl: './view-history.component.html',
    standalone: false
})
export class ViewHistoryComponent {
  form: FormGroup;

  constructor( @Inject(MAT_DIALOG_DATA) public data: Audit,
               public dialogRef: MatDialogRef<ViewHistoryComponent>,
               private fb: FormBuilder,
               private translate: TranslateService,
               private notificationsService: SwuiNotificationsService,
               ) {
    this.form = this.fb.group({
      parameters: [''],
      result: ['']
    });

    this.form.get('parameters').disable();
    this.form.get('result').disable();
  }

  ngOnInit(){
    const { parameters, result } = this.data.history;
    const value = {
      parameters: JSON.stringify( parameters, null, 4 ),
      result: JSON.stringify( result, null, 4 ),
    };

    this.form.setValue(<any>value);
  }

  getCopyContentFn( formControlName: string ) {
    return () => this.form.get(formControlName).value;
  }

  copySuccess() {
    this.translate.get('INTEGRATIONS.notificationCopy')
      .subscribe(message => this.notificationsService.success(message, ''));
  }
}

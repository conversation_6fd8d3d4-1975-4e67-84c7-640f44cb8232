import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  PanelAction,
  RowAction,
  SwHubAuthService,
  SwHubEntityService,
  SwuiGridComponent,
  SwuiGridDataService,
  SwuiNotificationsService,
  SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import { Subject, throwError } from 'rxjs';
import { catchError, filter, switchMap, take, takeUntil, tap } from 'rxjs/operators';
import { PERMISSIONS_NAMES } from '../../../../app.constants';
import {
  UserEditorDialogComponent,
  UserEditorDialogData
} from '../../../../common/components/mat-user-editor/user-editor-dialog.component';
import { <PERSON>tity } from '../../../../common/models/entity.model';
import { EntityDataSourceService } from '../../../../common/services/entity-data-source.service';
import { UserActionsService } from '../../../../common/services/user-actions.service';
import { UserService } from '../../../../common/services/user.service';
import { DeleteUserDialogComponent } from '../../../business-management/components/entities/tab-users/dialogs/delete-user-dialog.component';
import {
  TwofaResetDialogComponent,
  TwofaResetDialogResult
} from '../../../business-management/components/entities/tab-users/dialogs/twofa-reset-dialog.component';
import {
  UnblockUserDialogComponent
} from '../../../business-management/components/entities/tab-users/dialogs/unblock-user-dialog.component';
import { SCHEMA_FILTER, SCHEMA_LIST } from '../../schema';
import { User } from '../../user.model';

const COMPONENT_NAME: string = 'user-list';

@Component({
    selector: COMPONENT_NAME,
    encapsulation: ViewEncapsulation.None,
    templateUrl: './users-list.component.html',
    styleUrls: ['users-list.component.scss'],
    providers: [
        UserService,
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: UserService },
    ],
    standalone: false
})
export class UsersListComponent implements OnInit, OnDestroy {
  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<User>;

  schema = SCHEMA_LIST;
  filterSchema = SCHEMA_FILTER;

  items: User[] = [];
  readonly actions: RowAction[] = [];
  readonly panelActions: PanelAction[] = [];
  readonly entity?: Entity;
  componentName = COMPONENT_NAME;
  loading: boolean = false;

  private selectedEntity?: Entity;
  private readonly destroyed$ = new Subject<void>();

  constructor(private service: UserService<User>,
    private notifications: SwuiNotificationsService,
    private translate: TranslateService,
    { snapshot: { data: { brief } } }: ActivatedRoute,
    private authService: SwHubAuthService,
    private userActionsService: UserActionsService,
    private dialog: MatDialog,
    private readonly entityDataSourceService: EntityDataSourceService,
    private readonly hubEntityService: SwHubEntityService,
  ) {
    this.entity = brief ? new Entity(brief) : undefined;
    this.selectedEntity = this.entity;
    this.setActions();
  }

  ngOnInit() {
    this.entityDataSourceService.show();

    this.hubEntityService.entitySelected$.pipe(
      filter(Boolean),
      takeUntil(this.destroyed$),
    ).subscribe(entity => {
      this.selectedEntity = new Entity(entity);
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
    this.entityDataSourceService.hide();
  }

  downloadCsv() {
    this.loading = true;
    this.service.downloadCsv(this.entity?.type, this.selectedEntity?.path)
      .pipe(
        catchError((err) => {
          this.loading = false;
          return throwError(err);
        }),
        takeUntil(this.destroyed$)
      ).subscribe(() => {
        this.loading = false;
      });
  }

  printPage() {
    window.print();
  }

  exportPage() {
    const { dataSource: { data }, displayedColumns, paginator: { pageIndex } } = this.grid;
    this.service.exportPage(this.entity?.type, this.selectedEntity?.path, data, displayedColumns, pageIndex + 1);
  }

  private showModifyUserDialog(user = new User()) {
    this.dialog.open<any, UserEditorDialogData>(UserEditorDialogComponent, {
      width: '700px',
      data: {
        user,
        entity: this.selectedEntity,
        brief: this.entity
      },
      disableClose: true
    }).afterClosed()
      .pipe(take(1))
      .subscribe(() => {
        this.grid.dataSource.loadData();
      });
  }

  private showUnblockUserConfirmDialog(user: User) {
    let dialogUser = { ...user };
    this.service.getUserProfile(dialogUser)
      .pipe(
        switchMap((userProfile) =>
          this.dialog.open(UnblockUserDialogComponent, {
            width: '700px',
            data: { user: { ...dialogUser, ...userProfile } },
            disableClose: true
          }).afterClosed()),
        filter((data) => !!data),
        switchMap((data) => this.userActionsService.onUnblockUserAction(data, this.entity)),
        tap(() => this.notifications.success(
          this.translate.instant('ENTITY_SETUP.USERS.userUnblocked', user))),
        takeUntil(this.destroyed$)
      ).subscribe(() => {
        this.grid.dataSource.loadData();
      });
  }

  private showResetTwoAuthDialog(user: User) {
    const dialogUser = { ...user };
    this.dialog.open(TwofaResetDialogComponent, {
      width: '600px',
      data: {
        entity: this.entity,
        user: dialogUser
      },
      disableClose: true
    }).afterClosed().pipe(
      filter((data) => !!data),
      switchMap((data: TwofaResetDialogResult) => this.userActionsService.onTwofaResetAction(data)),
      tap(() => this.notifications.success(this.translate.instant('ENTITY_SETUP.USERS.twofaResetSuccessfull', user))),
      takeUntil(this.destroyed$)
    ).subscribe(() => this.grid.dataSource.loadData());
  }

  private showDeleteConfirmDialog(user: User) {
    const dialogUser = { ...user };
    this.dialog.open(DeleteUserDialogComponent, {
      width: '600px',
      data: { user: dialogUser },
      disableClose: true
    }).afterClosed()
      .pipe(
        filter((data) => !!data),
        switchMap(() => this.userActionsService.onDeleteUserAction(user, this.entity)),
        tap(() => this.notifications.success(this.translate.instant('ENTITY_SETUP.USERS.userRemoved', user))),
        catchError((error) => {
          this.notifications.error(error.statusText, `Status: ${error.status}`);
          return throwError(error);
        }),
        takeUntil(this.destroyed$)
      )
      .subscribe(() => this.grid.dataSource.loadData());
  }

  private setActions() {
    if (this.userActionsService.editUserEnabled()) {
      this.actions.push(new RowAction({
        title: 'ENTITY_SETUP.USERS.editUserAction',
        icon: 'edit',
        fn: this.showModifyUserDialog.bind(this)
      }));
    }

    if (this.userActionsService.unblockUserGranted(this.entity)) {
      this.actions.push(new RowAction({
        title: 'ENTITY_SETUP.USERS.MODALS.unblockUserModalTitle',
        icon: 'lock_open',
        fn: this.showUnblockUserConfirmDialog.bind(this),
        canActivateFn: (row: User) => {
          return row.status === 'locked_by_auth';
        }
      }));
    }

    if (this.userActionsService.twofaResetEnabled()) {
      this.actions.push(new RowAction({
        title: 'ENTITY_SETUP.USERS.reset2faAction',
        icon: 'security',
        fn: this.showResetTwoAuthDialog.bind(this),
        canActivateFn: (row: User) => !!row.twoFAInfo
          && (('defaultAuthType' in row.twoFAInfo) || ('authTypes' in row.twoFAInfo && row.twoFAInfo.authTypes.length))
      }));
    }

    if (this.userActionsService.deleteUserGranted(this.entity)) {
      this.actions.push(new RowAction({
        title: 'ENTITY_SETUP.USERS.deleteUserAction',
        icon: 'delete',
        fn: this.showDeleteConfirmDialog.bind(this),
        canActivateFn: (row: User) => row.username !== this.authService.username
      }));
    }

    this.panelActions.push(
      {
        title: 'USERS.createUser',
        color: 'primary',
        icon: 'person_add',
        actionFn: () => this.showModifyUserDialog(),
        availableFn: () => this.authService.allowedTo([
          PERMISSIONS_NAMES.USER,
          PERMISSIONS_NAMES.USER_CREATE,
          PERMISSIONS_NAMES.KEYENTITY_USER,
          PERMISSIONS_NAMES.KEYENTITY_USER_CREATE
        ]),
      }
    );
  }
}

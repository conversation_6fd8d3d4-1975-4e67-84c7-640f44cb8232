import { Component, EventEmitter, Inject, Output } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

export interface ConfirmDeleteRoleDialogData{
  showForceMessage?: boolean;
}

@Component({
    selector: 'confirm-delete-dialog',
    templateUrl: './confirm-delete-dialog.component.html',
    standalone: false
})
export class ConfirmDeleteDialogComponent {

  showForceMessage = false;
  isForceDelete = false;

  @Output() onFormSubmitted: EventEmitter<boolean> = new EventEmitter();

  constructor(
    public dialogRef: MatDialogRef<ConfirmDeleteDialogComponent, boolean>,
    @Inject(MAT_DIALOG_DATA) data: ConfirmDeleteRoleDialogData,
  ) {
    if ('showForceMessage' in data) {
      this.showForceMessage = data.showForceMessage;
    }
  }

  confirmDelete() {
    this.dialogRef.close(this.isForceDelete);
  }
}

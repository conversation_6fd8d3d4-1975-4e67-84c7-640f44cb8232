import { Component, Inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { Permission, PermissionGroup } from '../../../../../common/typings/permission';
import { Role } from '../role.model';

export interface RolesDialogData {
  dialogTitle: string;
  currentRole: Role;
  permissionGroups: PermissionGroup[];
  disabled: boolean;
}

@Component({
    selector: 'roles-dialog',
    templateUrl: './roles-dialog.component.html',
    standalone: false
})
export class RolesDialogComponent {

  dialogTitle: string;
  currentRole: Role;
  permissionGroups: PermissionGroup[];
  disabled: boolean;
  form: FormGroup;
  readonly isSuperadmin: boolean;

  constructor(
    public dialogRef: MatDialogRef<RolesDialogComponent, Role>,
    @Inject(MAT_DIALOG_DATA) data: RolesDialogData,
    private fb: FormBuilder,
    auth: SwHubAuthService
  ) {
    this.isSuperadmin = auth.isSuperAdmin;
    this.setDialogData(data);
    this.initForm();
    this.populateForm();
  }

  isPermissionGroupChecked( permissionGroup: PermissionGroup ): boolean {
    return permissionGroup.permissions.every(permission => this.isPermissionChecked(permission));
  }

  isPermissionChecked( permission: Permission ): boolean {
    return !!this.currentRole && this.currentRole.permissions.includes(permission.code);
  }

  removePermissionGroupFromRole( permissionGroup: PermissionGroup ): void {
    permissionGroup.permissions.forEach(permission => this.removePermissionFromRole(permission));
  }

  removePermissionFromRole( permission: Permission ): void {
    this.currentRole.permissions = this.currentRole.permissions.filter(item => item !== permission.code);
  }

  addPermissionGroupToRole( permissionGroup: PermissionGroup ): void {
    permissionGroup.permissions.forEach(permission => this.addPermissionToRole(permission));
  }

  addPermissionToRole( permission: Permission ): void {
    this.currentRole.permissions.push(permission.code);
  }

  submit() {
    let result;
    if (this.form.valid) {
      this.currentRole.title = this.form.get('title').value;
      this.currentRole.description = this.form.get('description').value;
      this.currentRole.isShared = this.form.get('isShared').value;
      result = this.currentRole;
    }
    this.dialogRef.close(result);
  }

  private setDialogData( data: RolesDialogData ) {
    this.dialogTitle = data.dialogTitle;
    this.currentRole = data.currentRole;
    this.permissionGroups = data.permissionGroups;
    this.disabled = data.disabled;
  }

  private initForm() {
    this.form = this.fb.group({
      title: [{ value: '', disabled: this.disabled }, Validators.required],
      description: [{ value: '', disabled: this.disabled }],
      isShared: [{ value: false, disabled: this.disabled }],
    });
  }

  private populateForm() {
    this.form.patchValue(this.currentRole);
  }
}

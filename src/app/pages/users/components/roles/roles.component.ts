import { Component, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import {
  PanelAction,
  PERMISSIONS_NAMES,
  RowAction,
  SwHubAuthService,
  SwuiGridComponent,
  SwuiGridDataService,
  SwuiGridField,
  SwuiNotificationsService
} from '@skywind-group/lib-swui';
import { Subject, throwError } from 'rxjs';
import { catchError, filter, switchMap, take, tap } from 'rxjs/operators';
import { RoleService } from '../../../../common/services/role.service';
import { UserService } from '../../../../common/services/user.service';
import { User } from '../../../../common/typings';
import { Permission, PermissionGroup } from '../../../../common/typings/permission';
import { ConfirmDeleteDialogComponent, ConfirmDeleteRoleDialogData } from './dialogs/confirm-delete-dialog.component';
import { RolesDialogComponent, RolesDialogData } from './dialogs/roles-dialog.component';
import { Role, RoleSimple } from './role.model';
import { buildSchema } from './roles.schema';


const EDIT_ROLE_STR = 'USERS.ROLES.edit';
const ADD_ROLE_STR = 'USERS.ROLES.add';
const VIEW_ROLE_STR = 'USERS.ROLES.view';

@Component({
    selector: 'roles',
    styleUrls: [
        './roles.component.scss',
    ],
    templateUrl: './roles.component.html',
    providers: [
        { provide: SwuiGridDataService, useExisting: RoleService }
    ],
    standalone: false
})
export class RolesComponent {
  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<Role>;

  schema: SwuiGridField[];
  permissionGroups: PermissionGroup[];
  panelActions: PanelAction[] = [];
  rowActions: RowAction[] = [];

  protected permissions: Permission[];
  private readonly destroyed$ = new Subject<void>();

  constructor( protected userService: UserService<User>,
               protected roleService: RoleService,
               private dialog: MatDialog,
               private notifications: SwuiNotificationsService,
               private translate: TranslateService,
               private authService: SwHubAuthService
  ) {
    this.schema = buildSchema(authService.isSuperAdmin);
    this.setRowActions();
  }

  ngOnInit() {
    this.setPanelActions();
    this.getPermissions();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  showAddDialog(): void {
    this.dialog.open(RolesDialogComponent, {
      width: '1000px',
      data: <RolesDialogData>{
        dialogTitle: ADD_ROLE_STR,
        currentRole: <Role>{
          title: '',
          description: '',
          isShared: false,
          permissions: [],
        },
        permissionGroups: this.permissionGroups,
        disabled: false
      },
      disableClose: true
    }).afterClosed()
      .pipe(
        filter(result => !!result),
        switchMap(( role ) => this.roleService.addRole(role)),
        tap(() => this.notifications.success(this.translate.instant('USERS.ROLES.notificationAdded')
        )),
        take(1),
      ).subscribe(() => this.grid.dataSource.loadData());
  }

  deleteRole( role, forceDelete ): void {
    if (!role.owned) {
      return;
    }

    this.roleService.deleteRole(role.id, forceDelete).pipe(
      tap(() => this.notifications.success(this.translate.instant('USERS.ROLES.notificationRemoved'))),
      catchError(( err ) => {
        if (err.error.code === 624) {
          this.prepareToDelete(role, true);
        }
        return throwError(err);
      }),
      take(1)
    ).subscribe(() => this.grid.dataSource.loadData());
  }

  showEditDialog( simpleRole: RoleSimple, disabled: boolean = false ): void {
    this.roleService.getRole(simpleRole)
      .pipe(take(1))
      .subscribe(( role ) => {
        this.dialog.open(RolesDialogComponent, {
          width: '1000px',
          data: <RolesDialogData>{
            dialogTitle: disabled ? VIEW_ROLE_STR : EDIT_ROLE_STR,
            currentRole: role,
            permissionGroups: this.permissionGroups,
            disabled,
          },
          disableClose: true
        }).afterClosed()
          .pipe(
            filter(result => !!result),
            switchMap(( currentRole ) => this.roleService.editRole(currentRole)),
            tap(() => this.notifications.success(this.translate.instant('USERS.ROLES.notificationEdited'))),
            take(1),
          ).subscribe(() => this.grid.dataSource.loadData());
      });
  }

  prepareToDelete( role: Role, showForceMessage? ) {
    this.dialog.open(ConfirmDeleteDialogComponent, {
      width: '700px',
      data: <ConfirmDeleteRoleDialogData>{
        showForceMessage
      },
      disableClose: true
    }).afterClosed().pipe(
      filter(result => typeof result === 'boolean'),
      take(1)
    ).subscribe(( forceDelete ) => this.deleteRole(role, forceDelete));
  }

  private getPermissions(): void {
    this.userService.getPermissions()
      .pipe(take(1))
      .subscribe(permissionGroups => this.permissionGroups = permissionGroups);
  }

  private setPanelActions() {
    this.panelActions.push({
      title: 'USERS.ROLES.btnCreate',
      availableFn: () => this.authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_ROLE_CREATE]),
      actionFn: () => this.showAddDialog(),
      icon: 'add',
      color: 'primary'
    });
  }

  private setRowActions() {
    this.rowActions.push(
      <RowAction>{
        title: 'USERS.ROLES.view',
        icon: 'view_list',
        inMenu: false,
        availableFn: () => {
          return false;
          // will be used diff permission
          // return !role.owned && this.authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_ROLE_VIEW]);
        },
        fn: ( role ) => this.showEditDialog(role, true)
      },
      <RowAction>{
        title: 'USERS.ROLES.edit',
        icon: 'edit',
        inMenu: false,
        availableFn: ( role ) => {
          return role.owned && this.authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_ROLE_EDIT]);
        },
        fn: ( role ) => this.showEditDialog(role)
      },
      <RowAction>{
        title: 'USERS.ROLES.delete',
        icon: 'delete',
        inMenu: false,
        availableFn: ( role ) => {
          return role.owned && this.authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_ROLE_DELETE]);
        },
        fn: ( role ) => this.prepareToDelete(role)
      });
  }
}

import { LayoutModule } from '@angular/cdk/layout';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiNotificationsModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { ControlMessagesModule } from '../../../../common/components/control-messages/control-messages.module';
import { RoleService } from '../../../../common/services/role.service';
import { UserService } from '../../../../common/services/user.service';
import { ConfirmDeleteDialogComponent } from './dialogs/confirm-delete-dialog.component';
import { RolesDialogComponent } from './dialogs/roles-dialog.component';

import { RolesComponent } from './roles.component';
import { RolesRoutingModule } from './roles.routing';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    RolesRoutingModule,
    SwuiPagePanelModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    SwuiGridModule,
    SwuiNotificationsModule.forRoot(),
    MatCheckboxModule,
    ControlMessagesModule,
    LayoutModule,
    MatInputModule,
    MatCheckboxModule,
    MatDividerModule,
  ],
  exports: [],
  declarations: [
    RolesComponent,
    RolesDialogComponent,
    ConfirmDeleteDialogComponent,
  ],
  providers: [
    RoleService,
    UserService,
  ],
})
export class RolesModule {
}
